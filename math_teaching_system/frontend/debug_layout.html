<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局调试 - 逗逗专用高中智能数学教学系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #f0f4ff;
            color: #1e293b;
            min-height: 100vh;
            display: flex;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        .sidebar {
            width: 280px;
            background: linear-gradient(135deg, #3a0ca3 0%, #4361ee 100%);
            color: white;
            padding: 20px;
            height: 100vh;
            flex-shrink: 0;
            border: 3px solid red; /* 调试边框 */
        }

        .main-content {
            flex: 1;
            padding: 20px;
            background: white;
            border: 3px solid blue; /* 调试边框 */
        }

        .debug-info {
            background: #ffeb3b;
            color: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <h1>侧边栏</h1>
        <div class="debug-info">
            侧边栏宽度: 280px<br>
            背景: 蓝色渐变<br>
            边框: 红色（调试用）
        </div>
        <p>如果你能看到这个红色边框的侧边栏，说明布局正常。</p>
    </div>

    <div class="main-content">
        <h1>主内容区域</h1>
        <div class="debug-info">
            主内容区域: flex: 1<br>
            背景: 白色<br>
            边框: 蓝色（调试用）
        </div>
        <p>如果你能看到这个蓝色边框的主内容区域，说明布局正常。</p>
        
        <h2>布局测试结果</h2>
        <ul>
            <li>✅ 如果左侧有红色边框的蓝色侧边栏 = 侧边栏显示正常</li>
            <li>✅ 如果右侧有蓝色边框的白色主内容区 = 主内容区显示正常</li>
            <li>✅ 如果两个区域并排显示 = Flexbox布局正常</li>
        </ul>

        <h2>问题诊断</h2>
        <p>如果侧边栏没有显示，可能的原因：</p>
        <ol>
            <li>CSS没有正确加载</li>
            <li>HTML结构有问题</li>
            <li>其他CSS规则覆盖了侧边栏样式</li>
            <li>JavaScript错误导致页面渲染问题</li>
        </ol>

        <button onclick="checkLayout()" style="padding: 10px 20px; margin: 10px 0; background: #4361ee; color: white; border: none; border-radius: 5px; cursor: pointer;">检查布局</button>
        
        <div id="layout-check-result"></div>
    </div>

    <script>
        function checkLayout() {
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');
            const body = document.body;
            
            const result = document.getElementById('layout-check-result');
            
            let report = '<h3>布局检查报告</h3>';
            
            // 检查body样式
            const bodyStyles = window.getComputedStyle(body);
            report += `<p><strong>Body Display:</strong> ${bodyStyles.display}</p>`;
            report += `<p><strong>Body Flex Direction:</strong> ${bodyStyles.flexDirection}</p>`;
            
            // 检查侧边栏
            if (sidebar) {
                const sidebarStyles = window.getComputedStyle(sidebar);
                report += `<p><strong>侧边栏存在:</strong> ✅</p>`;
                report += `<p><strong>侧边栏宽度:</strong> ${sidebarStyles.width}</p>`;
                report += `<p><strong>侧边栏显示:</strong> ${sidebarStyles.display}</p>`;
                report += `<p><strong>侧边栏可见性:</strong> ${sidebarStyles.visibility}</p>`;
                report += `<p><strong>侧边栏位置:</strong> left: ${sidebar.offsetLeft}px, top: ${sidebar.offsetTop}px</p>`;
            } else {
                report += `<p><strong>侧边栏存在:</strong> ❌ 未找到</p>`;
            }
            
            // 检查主内容区
            if (mainContent) {
                const mainStyles = window.getComputedStyle(mainContent);
                report += `<p><strong>主内容区存在:</strong> ✅</p>`;
                report += `<p><strong>主内容区Flex:</strong> ${mainStyles.flex}</p>`;
                report += `<p><strong>主内容区宽度:</strong> ${mainStyles.width}</p>`;
            } else {
                report += `<p><strong>主内容区存在:</strong> ❌ 未找到</p>`;
            }
            
            result.innerHTML = report;
        }

        // 页面加载时自动检查
        window.onload = function() {
            checkLayout();
        };
    </script>
</body>
</html>
