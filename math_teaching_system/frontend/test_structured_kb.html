<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>结构化知识库测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .selector {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .selector select {
            margin: 5px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .content-area {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            min-height: 400px;
        }
        .section-content {
            border: 1px solid #eee;
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .module-content {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #f5c6cb;
        }
        .btn {
            padding: 6px 12px;
            margin: 3px;
            border: 1px solid #007bff;
            background: #007bff;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-outline {
            background: white;
            color: #007bff;
        }
        .btn-outline:hover {
            background: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎓 逗逗专用高中智能数学教学系统 - 结构化知识库测试</h1>
        
        <div class="selector">
            <h3>📚 选择教材和章节</h3>
            <select id="textbookSelect" onchange="loadChapters()">
                <option value="必修第一册">必修第一册</option>
                <option value="必修第二册">必修第二册</option>
                <option value="选择性必修第一册">选择性必修第一册</option>
                <option value="选择性必修第二册">选择性必修第二册</option>
                <option value="选择性必修第三册">选择性必修第三册</option>
            </select>
            
            <select id="chapterSelect" onchange="loadChapterContent()">
                <option value="">请选择章节</option>
            </select>
            
            <select id="sectionSelect" onchange="loadSectionContent()">
                <option value="">请选择小节</option>
            </select>
            
            <select id="moduleSelect" onchange="loadModuleContent()">
                <option value="concept_learning">概念学习</option>
                <option value="core_skills">核心技巧</option>
                <option value="problem_types">题型高手</option>
                <option value="practice_exercises">动手练习</option>
            </select>
        </div>

        <div class="content-area" id="contentArea">
            <div class="loading">请选择教材、章节和模块开始浏览内容...</div>
        </div>
    </div>

    <script>
        let currentData = null;

        // 教材章节映射
        const textbookChapters = {
            '必修第一册': [
                { id: '第01章_集合与常用逻辑用语', title: '第一章 集合与常用逻辑用语', file: '第01章_集合与常用逻辑用语.json' },
                { id: '第02章_一元二次函数方程和不等式', title: '第二章 一元二次函数、方程和不等式', file: '第02章_一元二次函数方程和不等式.json' },
                { id: '第03章_函数概念与性质', title: '第三章 函数概念与性质', file: '第03章_函数概念与性质.json' },
                { id: '第04章_指数函数与对数函数', title: '第四章 指数函数与对数函数', file: '第04章_指数函数与对数函数.json' },
                { id: '第05章_三角函数', title: '第五章 三角函数', file: '第05章_三角函数.json' }
            ],
            '必修第二册': [
                { id: '第01章_空间向量与立体几何', title: '第一章 空间向量与立体几何', file: '第01章_空间向量与立体几何.json' },
                { id: '第02章_平面向量', title: '第六章 平面向量', file: '第02章_平面向量.json' }
            ]
        };

        // 加载章节选项
        function loadChapters() {
            const textbook = document.getElementById('textbookSelect').value;
            const chapterSelect = document.getElementById('chapterSelect');
            const sectionSelect = document.getElementById('sectionSelect');
            
            chapterSelect.innerHTML = '<option value="">请选择章节</option>';
            sectionSelect.innerHTML = '<option value="">请选择小节</option>';
            
            const chapters = textbookChapters[textbook] || [];
            chapters.forEach(chapter => {
                const option = document.createElement('option');
                option.value = chapter.file;
                option.textContent = chapter.title;
                chapterSelect.appendChild(option);
            });
        }

        // 加载章节内容
        async function loadChapterContent() {
            const textbook = document.getElementById('textbookSelect').value;
            const chapterFile = document.getElementById('chapterSelect').value;
            const sectionSelect = document.getElementById('sectionSelect');
            
            if (!chapterFile) return;
            
            try {
                const response = await fetch(`/knowledge_base_structured/${textbook}/${chapterFile}`);
                const data = await response.json();
                currentData = data;
                
                // 加载小节选项
                sectionSelect.innerHTML = '<option value="">请选择小节</option>';
                if (data.sections) {
                    Object.keys(data.sections).forEach(sectionKey => {
                        const option = document.createElement('option');
                        option.value = sectionKey;
                        option.textContent = sectionKey;
                        sectionSelect.appendChild(option);
                    });
                }
                
                // 显示章节信息
                displayChapterInfo(data);
                
            } catch (error) {
                document.getElementById('contentArea').innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 显示章节信息
        function displayChapterInfo(data) {
            const contentArea = document.getElementById('contentArea');
            const chapterInfo = data.chapter_info || {};
            
            contentArea.innerHTML = `
                <h2>📖 ${chapterInfo.title || '未知章节'}</h2>
                <p><strong>教材:</strong> ${chapterInfo.textbook || '未知'}</p>
                <p><strong>预计学时:</strong> ${chapterInfo.estimated_time || 0} 分钟</p>
                <p><strong>难度:</strong> ${chapterInfo.difficulty || '未知'}</p>
                <p><strong>重要性:</strong> ${chapterInfo.importance || '未知'}</p>
                <p><strong>包含小节:</strong></p>
                <ul>
                    ${(chapterInfo.sections || []).map(section => `<li>${section}</li>`).join('')}
                </ul>
                <hr>
                <p>请选择具体的小节和学习模块查看详细内容。</p>
            `;
        }

        // 加载小节内容
        function loadSectionContent() {
            loadModuleContent();
        }

        // 加载模块内容
        function loadModuleContent() {
            const sectionKey = document.getElementById('sectionSelect').value;
            const moduleKey = document.getElementById('moduleSelect').value;
            
            if (!currentData || !sectionKey || !moduleKey) return;
            
            const sectionData = currentData.sections[sectionKey];
            if (!sectionData) {
                document.getElementById('contentArea').innerHTML = '<div class="error">未找到小节数据</div>';
                return;
            }
            
            const moduleData = sectionData[moduleKey];
            if (!moduleData) {
                document.getElementById('contentArea').innerHTML = '<div class="error">未找到模块数据</div>';
                return;
            }
            
            displayModuleContent(sectionKey, moduleKey, moduleData);
        }

        // 显示模块内容
        function displayModuleContent(sectionKey, moduleKey, moduleData) {
            const contentArea = document.getElementById('contentArea');
            
            let html = `
                <h2>📚 ${sectionKey}</h2>
                <h3>🎯 ${moduleData.title || moduleKey}</h3>
                <hr>
            `;
            
            // 根据不同模块类型显示内容
            switch (moduleKey) {
                case 'concept_learning':
                    html += displayConceptContent(moduleData);
                    break;
                case 'core_skills':
                    html += displaySkillsContent(moduleData);
                    break;
                case 'problem_types':
                    html += displayProblemTypesContent(moduleData);
                    break;
                case 'practice_exercises':
                    html += displayExercisesContent(moduleData);
                    break;
                default:
                    html += '<p>未知模块类型</p>';
            }
            
            contentArea.innerHTML = html;
        }

        // 显示概念学习内容
        function displayConceptContent(data) {
            const content = data.content || {};
            let html = `
                <div class="module-content">
                    <h4>📝 定义</h4>
                    <p>${content.definition || '暂无定义'}</p>
                    
                    <h4>🔑 关键点</h4>
                    <ul>
                        ${(content.key_points || []).map(point => `<li>${point}</li>`).join('')}
                    </ul>
                    
                    <h4>💡 示例</h4>
                    ${(content.examples || []).map(example => `
                        <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                            <strong>${example.title}:</strong> ${example.content}<br>
                            <em>解释: ${example.explanation}</em>
                        </div>
                    `).join('')}
                    
                    <h4>🎧 功能按钮</h4>
                    <div>
                        <button class="btn" onclick="playVoice()">🎵 听讲解</button>
                        <button class="btn" onclick="playVideo()">📹 视频精讲</button>
                        <button class="btn" onclick="playAnimation()">🎬 看动画</button>
                        <button class="btn" onclick="showImages()">🖼️ 概念图片</button>
                        <button class="btn" onclick="showVisualization()">🔍 概念可视化</button>
                    </div>
                </div>
            `;
            return html;
        }

        // 显示技巧内容
        function displaySkillsContent(data) {
            const skills = data.skills || [];
            let html = '<div class="module-content">';
            
            skills.forEach(skill => {
                html += `
                    <h4>🛠️ ${skill.name}</h4>
                    <div style="margin: 10px 0;">
                        ${Object.entries(skill.content || {}).map(([key, value]) => `
                            <div style="margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                                <strong>${key}:</strong> ${value.description || value}<br>
                                ${value.voice_script ? `<button class="btn btn-outline" onclick="playVoice('${value.voice_script}')">🎵 听讲解</button>` : ''}
                            </div>
                        `).join('')}
                    </div>
                `;
            });
            
            html += '</div>';
            return html;
        }

        // 显示题型内容
        function displayProblemTypesContent(data) {
            const types = data.types || [];
            let html = '<div class="module-content">';
            
            types.forEach(type => {
                html += `
                    <h4>📊 ${type.name}</h4>
                    <p>${type.description}</p>
                    <button class="btn btn-outline" onclick="playVoice('${type.voice_script || ''}')">🎵 听讲解</button>
                    <div style="margin: 10px 0;">
                        ${(type.examples || []).map(example => `
                            <div style="margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                                <strong>问题:</strong> ${example.problem}<br>
                                <strong>解答:</strong> ${example.solution}<br>
                                <em>说明: ${example.explanation}</em>
                            </div>
                        `).join('')}
                    </div>
                `;
            });
            
            html += '</div>';
            return html;
        }

        // 显示练习内容
        function displayExercisesContent(data) {
            const exercises = data.exercises || [];
            let html = '<div class="module-content">';
            
            exercises.forEach((exercise, index) => {
                html += `
                    <h4>📝 练习 ${index + 1} (${exercise.difficulty})</h4>
                    <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                        <p><strong>题目:</strong> ${exercise.question}</p>
                        ${exercise.options ? `
                            <div>
                                ${exercise.options.map(option => `<p>${option}</p>`).join('')}
                            </div>
                        ` : ''}
                        <p><strong>答案:</strong> ${exercise.answer}</p>
                        <p><strong>解释:</strong> ${exercise.explanation}</p>
                        <button class="btn btn-outline" onclick="playVoice('${exercise.voice_script || ''}')">🎵 听讲解</button>
                        <span style="margin-left: 10px; color: #666;">预计用时: ${exercise.estimated_time || 0} 分钟</span>
                    </div>
                `;
            });
            
            html += '</div>';
            return html;
        }

        // 功能函数（模拟）
        function playVoice(text) {
            alert('播放语音: ' + (text || '语音内容'));
        }

        function playVideo() {
            alert('播放视频精讲');
        }

        function playAnimation() {
            alert('播放动画');
        }

        function showImages() {
            alert('显示概念图片');
        }

        function showVisualization() {
            alert('显示概念可视化');
        }

        // 页面加载时初始化
        window.onload = function() {
            loadChapters();
        };
    </script>
</body>
</html>
