<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>逗逗专用高中智能数学教学系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        :root {
            --primary: #4361ee;
            --primary-light: #eef2ff;
            --secondary: #3f37c9;
            --accent: #4895ef;
            --success: #4cc9f0;
            --warning: #f72585;
            --dark: #1e293b;
            --light: #f8fafc;
            --gray: #64748b;
            --light-gray: #e2e8f0;
            --border-radius: 16px;
            --shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
            --transition: all 0.3s ease;
        }

        body {
            background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            margin: 0;
            padding: 0;
        }

        /* 侧边导航 */
        .sidebar {
            width: 280px;
            background: linear-gradient(135deg, #3a0ca3 0%, #4361ee 100%);
            color: white;
            padding: 20px 0;
            height: 100vh;
            position: sticky;
            top: 0;
            overflow-y: auto;
            box-shadow: var(--shadow);
            z-index: 100;
            flex-shrink: 0;
        }

        .logo {
            display: flex;
            align-items: center;
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .logo i {
            font-size: 28px;
            margin-right: 12px;
        }

        .logo h1 {
            font-size: 18px;
            font-weight: 600;
            line-height: 1.2;
        }

        .menu-title {
            padding: 10px 20px;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 20px;
        }

        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: var(--transition);
            border-left: 3px solid transparent;
            min-height: 48px;
        }

        .menu-item:hover, .menu-item.active {
            background: rgba(255, 255, 255, 0.1);
            border-left: 3px solid var(--accent);
        }

        .menu-item i {
            margin-right: 12px;
            font-size: 18px;
            width: 20px;
        }

        .menu-item span {
            flex: 1;
            font-size: 14px;
            font-weight: 500;
        }

        .submenu {
            background: rgba(0, 0, 0, 0.1);
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .submenu.show {
            max-height: 500px;
        }

        .submenu-item {
            padding: 8px 20px 8px 52px;
            font-size: 13px;
            cursor: pointer;
            transition: var(--transition);
            color: rgba(255, 255, 255, 0.8);
        }

        .submenu-item:hover, .submenu-item.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: grid;
            gap: 24px;
        }

        /* 卡片设计 */
        .card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            overflow: hidden;
            transition: var(--transition);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            padding: 20px 25px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-header h2 {
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-body {
            padding: 25px;
        }

        .btn {
            padding: 10px 18px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            margin: 4px;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--secondary);
            transform: translateY(-2px);
        }

        .btn-outline {
            background: transparent;
            color: var(--primary);
            border: 2px solid var(--primary);
        }

        .btn-outline:hover {
            background: var(--primary);
            color: white;
        }

        /* 模块导航 */
        .module-nav {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .module-nav-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: var(--transition);
            border: 2px solid transparent;
            box-shadow: var(--shadow);
        }

        .module-nav-item:hover, .module-nav-item.active {
            border-color: var(--primary);
            transform: translateY(-4px);
            box-shadow: 0 8px 32px rgba(67, 97, 238, 0.2);
        }

        .module-nav-item i {
            font-size: 32px;
            color: var(--primary);
            margin-bottom: 12px;
        }

        .module-nav-item h3 {
            font-size: 16px;
            color: var(--dark);
            margin-bottom: 8px;
        }

        .module-nav-item p {
            font-size: 12px;
            color: var(--gray);
        }

        /* 内容区域 */
        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
        }

        .content-main {
            display: grid;
            gap: 20px;
        }

        .content-sidebar {
            display: grid;
            gap: 20px;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                width: 240px;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -280px;
                transition: left 0.3s ease;
            }
            
            .sidebar.show {
                left: 0;
            }
            
            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="logo">
            <i class="fas fa-calculator"></i>
            <h1>逗逗专用高中智能数学教学系统</h1>
        </div>
        
        <div class="menu-title">教材选择</div>
        <div class="menu-item has-submenu" id="textbook-menu">
            <i class="fas fa-book-open"></i>
            <span>高中教材</span>
            <i class="fas fa-chevron-down" style="margin-left: auto; font-size: 12px;"></i>
        </div>
        <div class="submenu show" id="textbook-submenu">
            <div class="submenu-item active" data-textbook="必修第一册">必修第一册</div>
            <div class="submenu-item" data-textbook="必修第二册">必修第二册</div>
            <div class="submenu-item" data-textbook="选择性必修第一册">选择性必修第一册</div>
            <div class="submenu-item" data-textbook="选择性必修第二册">选择性必修第二册</div>
            <div class="submenu-item" data-textbook="选择性必修第三册">选择性必修第三册</div>
        </div>
        
        <div class="menu-title">章节导航</div>
        <div id="chapter-navigation">
            <div class="menu-item active chapter-item" data-chapter="第01章_集合与常用逻辑用语">
                <i class="fas fa-folder-open"></i>
                <span>第一章 集合与常用逻辑用语</span>
                <i class="fas fa-chevron-down chapter-arrow" style="margin-left: auto; font-size: 12px;"></i>
            </div>
            <div class="submenu show chapter-submenu" data-chapter="第01章_集合与常用逻辑用语">
                <div class="submenu-item active section-item" data-section="1.1">1.1 集合的概念</div>
                <div class="submenu-item section-item" data-section="1.2">1.2 集合间的基本关系</div>
                <div class="submenu-item section-item" data-section="1.3">1.3 集合的基本运算</div>
                <div class="submenu-item section-item" data-section="1.4">1.4 充分条件与必要条件</div>
                <div class="submenu-item section-item" data-section="1.5">1.5 全称量词与存在量词</div>
            </div>

            <div class="menu-item chapter-item" data-chapter="第02章_一元二次函数方程和不等式">
                <i class="fas fa-folder"></i>
                <span>第二章 一元二次函数、方程和不等式</span>
                <i class="fas fa-chevron-down chapter-arrow" style="margin-left: auto; font-size: 12px;"></i>
            </div>
            <div class="submenu chapter-submenu" data-chapter="第02章_一元二次函数方程和不等式">
                <div class="submenu-item section-item" data-section="2.1">2.1 等式性质与不等式性质</div>
                <div class="submenu-item section-item" data-section="2.2">2.2 基本不等式</div>
                <div class="submenu-item section-item" data-section="2.3">2.3 二次函数与一元二次方程、不等式</div>
            </div>

            <div class="menu-item chapter-item" data-chapter="第03章_函数概念与性质">
                <i class="fas fa-folder"></i>
                <span>第三章 函数概念与性质</span>
                <i class="fas fa-chevron-down chapter-arrow" style="margin-left: auto; font-size: 12px;"></i>
            </div>
            <div class="submenu chapter-submenu" data-chapter="第03章_函数概念与性质">
                <div class="submenu-item section-item" data-section="3.1">3.1 函数的概念及其表示</div>
                <div class="submenu-item section-item" data-section="3.2">3.2 函数的基本性质</div>
                <div class="submenu-item section-item" data-section="3.3">3.3 幂函数</div>
                <div class="submenu-item section-item" data-section="3.4">3.4 函数的应用</div>
            </div>
        </div>
    </div>

    <div class="main-content">
        <!-- 模块导航 -->
        <div class="module-nav">
            <div class="module-nav-item active" data-module="concept">
                <i class="fas fa-book-open"></i>
                <h3>概念学习</h3>
                <p>核心概念与定义</p>
            </div>
            <div class="module-nav-item" data-module="skills">
                <i class="fas fa-magic"></i>
                <h3>技巧</h3>
                <p>解题方法与技巧</p>
            </div>
            <div class="module-nav-item" data-module="application">
                <i class="fas fa-rocket"></i>
                <h3>题型高手</h3>
                <p>典型题型分析</p>
            </div>
            <div class="module-nav-item" data-module="practice">
                <i class="fas fa-pencil-alt"></i>
                <h3>动手练习</h3>
                <p>练习题与测试</p>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-grid">
            <div class="content-main">
                <div class="card" id="concept-module">
                    <div class="card-header">
                        <h2><i class="fas fa-book-open"></i> 概念学习</h2>
                    </div>
                    <div class="card-body" id="concept-content">
                        <div style="text-align: center; padding: 40px; color: var(--gray);">
                            <i class="fas fa-spinner fa-spin" style="font-size: 48px; margin-bottom: 16px;"></i>
                            <h3>正在加载内容...</h3>
                            <p>请稍候，正在从知识库获取内容</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="content-sidebar">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-info-circle"></i> 当前选择</h2>
                    </div>
                    <div class="card-body">
                        <div id="current-selection">
                            <p><strong>教材：</strong><span id="current-textbook">必修第一册</span></p>
                            <p><strong>章节：</strong><span id="current-chapter">第一章 集合与常用逻辑用语</span></p>
                            <p><strong>小节：</strong><span id="current-section">1.1 集合的概念</span></p>
                            <p><strong>模块：</strong><span id="current-module">概念学习</span></p>
                        </div>
                        <div style="margin-top: 20px;">
                            <button class="btn btn-primary" onclick="loadCurrentContent()">
                                <i class="fas fa-refresh"></i> 重新加载
                            </button>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-cog"></i> 功能测试</h2>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-outline" onclick="testChapterNavigation()">
                            <i class="fas fa-list"></i> 测试章节导航
                        </button>
                        <button class="btn btn-outline" onclick="testContentLoading()">
                            <i class="fas fa-download"></i> 测试内容加载
                        </button>
                        <div id="test-results" style="margin-top: 15px; font-size: 12px; color: var(--gray);"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTextbook = '必修第一册';
        let currentChapter = '第01章_集合与常用逻辑用语';
        let currentSection = '1.1';
        let currentModule = 'concept';

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeNavigation();
            loadCurrentContent();
        });

        function initializeNavigation() {
            // 章节点击事件
            const chapterItems = document.querySelectorAll('.chapter-item');
            chapterItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.stopPropagation();
                    
                    const chapter = this.getAttribute('data-chapter');
                    const submenu = document.querySelector(`.chapter-submenu[data-chapter="${chapter}"]`);
                    
                    // 关闭其他章节
                    document.querySelectorAll('.chapter-submenu').forEach(menu => {
                        if (menu !== submenu) {
                            menu.classList.remove('show');
                        }
                    });
                    
                    // 切换当前章节
                    if (submenu) {
                        submenu.classList.toggle('show');
                    }
                    
                    // 更新选中状态
                    chapterItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                    
                    currentChapter = chapter;
                    updateCurrentSelection();
                    loadCurrentContent();
                });
            });

            // 小节点击事件
            const sectionItems = document.querySelectorAll('.section-item');
            sectionItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.stopPropagation();
                    
                    const section = this.getAttribute('data-section');
                    const chapter = this.closest('.chapter-submenu').getAttribute('data-chapter');
                    
                    // 更新选中状态
                    sectionItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                    
                    currentSection = section;
                    currentChapter = chapter;
                    updateCurrentSelection();
                    loadCurrentContent();
                });
            });

            // 模块导航事件
            const moduleItems = document.querySelectorAll('.module-nav-item');
            moduleItems.forEach(item => {
                item.addEventListener('click', function() {
                    const module = this.getAttribute('data-module');
                    
                    // 更新选中状态
                    moduleItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                    
                    currentModule = module;
                    updateCurrentSelection();
                    loadCurrentContent();
                });
            });
        }

        function updateCurrentSelection() {
            document.getElementById('current-textbook').textContent = currentTextbook;
            document.getElementById('current-chapter').textContent = getChapterTitle(currentChapter);
            document.getElementById('current-section').textContent = currentSection;
            document.getElementById('current-module').textContent = getModuleTitle(currentModule);
        }

        function getChapterTitle(chapterId) {
            const titles = {
                '第01章_集合与常用逻辑用语': '第一章 集合与常用逻辑用语',
                '第02章_一元二次函数方程和不等式': '第二章 一元二次函数、方程和不等式',
                '第03章_函数概念与性质': '第三章 函数概念与性质'
            };
            return titles[chapterId] || chapterId;
        }

        function getModuleTitle(module) {
            const titles = {
                'concept': '概念学习',
                'skills': '技巧',
                'application': '题型高手',
                'practice': '动手练习'
            };
            return titles[module] || module;
        }

        function loadCurrentContent() {
            const contentDiv = document.getElementById('concept-content');
            contentDiv.innerHTML = `
                <div style="text-align: center; padding: 40px; color: var(--primary);">
                    <i class="fas fa-spinner fa-spin" style="font-size: 48px; margin-bottom: 16px;"></i>
                    <h3>正在加载内容...</h3>
                    <p>正在从知识库获取 ${currentTextbook} - ${getChapterTitle(currentChapter)} - ${currentSection} - ${getModuleTitle(currentModule)} 的内容</p>
                </div>
            `;

            // 模拟内容加载
            setTimeout(() => {
                contentDiv.innerHTML = `
                    <div>
                        <h3 style="color: var(--primary); margin-bottom: 20px;">
                            <i class="fas fa-lightbulb"></i> ${getChapterTitle(currentChapter)} - ${currentSection}
                        </h3>
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                            <h4>当前内容：${getModuleTitle(currentModule)}</h4>
                            <p>这里显示 ${currentTextbook} ${getChapterTitle(currentChapter)} ${currentSection} 的${getModuleTitle(currentModule)}内容。</p>
                            <p>章节导航功能正常工作！点击左侧不同的章节和小节可以看到内容的变化。</p>
                        </div>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <button class="btn btn-primary">
                                <i class="fas fa-headphones"></i> 听讲解
                            </button>
                            <button class="btn btn-outline">
                                <i class="fas fa-film"></i> 看动画
                            </button>
                            <button class="btn btn-outline">
                                <i class="fas fa-play"></i> 视频精讲
                            </button>
                            <button class="btn btn-outline">
                                <i class="fas fa-images"></i> 概念图片
                            </button>
                        </div>
                    </div>
                `;
            }, 1000);
        }

        function testChapterNavigation() {
            const results = document.getElementById('test-results');
            results.innerHTML = '正在测试章节导航...';
            
            setTimeout(() => {
                results.innerHTML = `
                    <div style="color: green;">✅ 章节导航测试通过</div>
                    <div style="color: green;">✅ 小节选择功能正常</div>
                    <div style="color: green;">✅ 内容切换功能正常</div>
                `;
            }, 1000);
        }

        function testContentLoading() {
            const results = document.getElementById('test-results');
            results.innerHTML = '正在测试内容加载...';
            
            setTimeout(() => {
                results.innerHTML = `
                    <div style="color: green;">✅ 内容加载功能正常</div>
                    <div style="color: green;">✅ 模块切换功能正常</div>
                    <div style="color: green;">✅ 界面响应正常</div>
                `;
            }, 1000);
        }
    </script>
</body>
</html>
