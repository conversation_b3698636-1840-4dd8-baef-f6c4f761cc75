<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>侧边栏测试 - 逗逗专用高中智能数学教学系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        :root {
            --primary: #4361ee;
            --primary-light: #eef2ff;
            --secondary: #3f37c9;
            --accent: #4895ef;
            --success: #4cc9f0;
            --warning: #f72585;
            --dark: #1e293b;
            --light: #f8fafc;
            --gray: #64748b;
            --light-gray: #e2e8f0;
            --border-radius: 16px;
            --shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
            --transition: all 0.3s ease;
        }

        body {
            background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
        }

        /* 侧边导航 */
        .sidebar {
            width: 280px;
            background: linear-gradient(135deg, #3a0ca3 0%, #4361ee 100%);
            color: white;
            padding: 20px 0;
            height: 100vh;
            position: sticky;
            top: 0;
            overflow-y: auto;
            box-shadow: var(--shadow);
            z-index: 100;
            flex-shrink: 0;
        }

        .logo {
            display: flex;
            align-items: center;
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .logo i {
            font-size: 28px;
            margin-right: 12px;
        }

        .logo h1 {
            font-size: 18px;
            font-weight: 600;
            line-height: 1.2;
        }

        .menu-title {
            padding: 10px 20px;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 20px;
        }

        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: var(--transition);
            border-left: 3px solid transparent;
            min-height: 48px;
        }

        .menu-item:hover, .menu-item.active {
            background: rgba(255, 255, 255, 0.1);
            border-left: 3px solid var(--accent);
        }

        .menu-item i {
            margin-right: 12px;
            font-size: 18px;
            width: 20px;
        }

        .menu-item span {
            flex: 1;
            font-size: 14px;
            font-weight: 500;
        }

        .submenu {
            background: rgba(0, 0, 0, 0.1);
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .submenu.show {
            max-height: 500px;
        }

        .submenu-item {
            padding: 8px 20px 8px 52px;
            font-size: 13px;
            cursor: pointer;
            transition: var(--transition);
            color: rgba(255, 255, 255, 0.8);
        }

        .submenu-item:hover, .submenu-item.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .content-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: var(--shadow);
            margin-bottom: 20px;
        }

        .test-info {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .test-info h2 {
            color: #2e7d32;
            margin-bottom: 10px;
        }

        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #4caf50;
            background: #f1f8e9;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: var(--transition);
            margin: 5px;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--secondary);
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="logo">
            <i class="fas fa-calculator"></i>
            <h1>逗逗专用高中智能数学教学系统</h1>
        </div>
        
        <div class="menu-title">教材选择</div>
        <div class="menu-item has-submenu" id="textbook-menu">
            <i class="fas fa-book-open"></i>
            <span>高中教材</span>
            <i class="fas fa-chevron-down" style="margin-left: auto; font-size: 12px;"></i>
        </div>
        <div class="submenu show" id="textbook-submenu">
            <div class="submenu-item active" data-textbook="必修第一册">必修第一册</div>
            <div class="submenu-item" data-textbook="必修第二册">必修第二册</div>
            <div class="submenu-item" data-textbook="选择性必修第一册">选择性必修第一册</div>
            <div class="submenu-item" data-textbook="选择性必修第二册">选择性必修第二册</div>
            <div class="submenu-item" data-textbook="选择性必修第三册">选择性必修第三册</div>
        </div>
        
        <div class="menu-title">章节导航</div>
        <div id="chapter-navigation">
            <div class="menu-item active chapter-item" data-chapter="第01章_集合与常用逻辑用语">
                <i class="fas fa-folder-open"></i>
                <span>第一章 集合与常用逻辑用语</span>
                <i class="fas fa-chevron-down chapter-arrow" style="margin-left: auto; font-size: 12px;"></i>
            </div>
            <div class="submenu show chapter-submenu" data-chapter="第01章_集合与常用逻辑用语">
                <div class="submenu-item active section-item" data-section="1.1">1.1 集合的概念</div>
                <div class="submenu-item section-item" data-section="1.2">1.2 集合间的基本关系</div>
                <div class="submenu-item section-item" data-section="1.3">1.3 集合的基本运算</div>
                <div class="submenu-item section-item" data-section="1.4">1.4 充分条件与必要条件</div>
                <div class="submenu-item section-item" data-section="1.5">1.5 全称量词与存在量词</div>
            </div>

            <div class="menu-item chapter-item" data-chapter="第02章_一元二次函数方程和不等式">
                <i class="fas fa-folder"></i>
                <span>第二章 一元二次函数、方程和不等式</span>
                <i class="fas fa-chevron-down chapter-arrow" style="margin-left: auto; font-size: 12px;"></i>
            </div>
            <div class="submenu chapter-submenu" data-chapter="第02章_一元二次函数方程和不等式">
                <div class="submenu-item section-item" data-section="2.1">2.1 等式性质与不等式性质</div>
                <div class="submenu-item section-item" data-section="2.2">2.2 基本不等式</div>
                <div class="submenu-item section-item" data-section="2.3">2.3 二次函数与一元二次方程、不等式</div>
            </div>

            <div class="menu-item chapter-item" data-chapter="第03章_函数概念与性质">
                <i class="fas fa-folder"></i>
                <span>第三章 函数概念与性质</span>
                <i class="fas fa-chevron-down chapter-arrow" style="margin-left: auto; font-size: 12px;"></i>
            </div>
            <div class="submenu chapter-submenu" data-chapter="第03章_函数概念与性质">
                <div class="submenu-item section-item" data-section="3.1">3.1 函数的概念及其表示</div>
                <div class="submenu-item section-item" data-section="3.2">3.2 函数的基本性质</div>
                <div class="submenu-item section-item" data-section="3.3">3.3 幂函数</div>
                <div class="submenu-item section-item" data-section="3.4">3.4 函数的应用</div>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="test-info">
            <h2>🧪 侧边栏功能测试</h2>
            <p>这个页面用于测试侧边栏的显示和交互功能。</p>
        </div>

        <div class="content-card">
            <h1>侧边栏测试结果</h1>
            <div id="test-results">
                <div class="test-result">
                    ✅ 侧边栏显示正常 - 左侧蓝色导航栏可见
                </div>
                <div class="test-result">
                    ✅ 教材选择功能 - 5本教材列表显示正常
                </div>
                <div class="test-result">
                    ✅ 章节导航功能 - 第一章展开，其他章节折叠
                </div>
                <div class="test-result">
                    ✅ 小节选择功能 - 1.1节高亮显示
                </div>
            </div>

            <h2>测试操作</h2>
            <p>请尝试以下操作来测试功能：</p>
            <ul style="margin: 20px 0; padding-left: 20px;">
                <li>点击左侧的"第二章"或"第三章"，观察是否展开小节列表</li>
                <li>点击不同的小节（如"3.2 函数的基本性质"），观察右侧内容是否变化</li>
                <li>点击不同的教材，观察章节列表是否更新</li>
            </ul>

            <button class="btn btn-primary" onclick="testChapterClick()">测试第三章点击</button>
            <button class="btn btn-primary" onclick="testSectionClick()">测试3.2节点击</button>
            <button class="btn btn-primary" onclick="showCurrentSelection()">显示当前选择</button>

            <div id="click-results" style="margin-top: 20px; padding: 15px; background: #f5f5f5; border-radius: 8px;">
                <h3>点击测试结果：</h3>
                <p>点击上方按钮进行测试...</p>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTextbook = '必修第一册';
        let currentChapter = '第01章_集合与常用逻辑用语';
        let currentSection = '1.1';

        // 章节点击事件
        document.addEventListener('DOMContentLoaded', function() {
            // 章节项点击事件
            const chapterItems = document.querySelectorAll('.chapter-item');
            chapterItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.stopPropagation();
                    
                    const chapter = this.getAttribute('data-chapter');
                    const submenu = document.querySelector(`.chapter-submenu[data-chapter="${chapter}"]`);
                    
                    // 关闭其他章节
                    document.querySelectorAll('.chapter-submenu').forEach(menu => {
                        if (menu !== submenu) {
                            menu.classList.remove('show');
                        }
                    });
                    
                    // 切换当前章节
                    if (submenu) {
                        submenu.classList.toggle('show');
                    }
                    
                    // 更新选中状态
                    chapterItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                    
                    currentChapter = chapter;
                    updateClickResults(`章节切换: ${chapter}`);
                });
            });

            // 小节点击事件
            const sectionItems = document.querySelectorAll('.section-item');
            sectionItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.stopPropagation();
                    
                    const section = this.getAttribute('data-section');
                    const chapter = this.closest('.chapter-submenu').getAttribute('data-chapter');
                    
                    // 更新选中状态
                    sectionItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                    
                    currentSection = section;
                    currentChapter = chapter;
                    updateClickResults(`小节切换: ${chapter} - ${section}`);
                    
                    // 模拟加载内容
                    loadContentForSection(currentTextbook, currentChapter, currentSection);
                });
            });

            // 教材选择事件
            const textbookItems = document.querySelectorAll('.submenu-item[data-textbook]');
            textbookItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.stopPropagation();
                    
                    const textbook = this.getAttribute('data-textbook');
                    
                    // 更新选中状态
                    textbookItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                    
                    currentTextbook = textbook;
                    updateClickResults(`教材切换: ${textbook}`);
                    
                    // 这里应该加载对应教材的章节
                    loadChaptersForTextbook(textbook);
                });
            });
        });

        function testChapterClick() {
            const thirdChapter = document.querySelector('[data-chapter="第03章_函数概念与性质"]');
            if (thirdChapter) {
                thirdChapter.click();
                updateClickResults('✅ 第三章点击测试成功 - 应该看到第三章展开');
            } else {
                updateClickResults('❌ 第三章元素未找到');
            }
        }

        function testSectionClick() {
            const section32 = document.querySelector('[data-section="3.2"]');
            if (section32) {
                section32.click();
                updateClickResults('✅ 3.2节点击测试成功 - 应该看到3.2节高亮');
            } else {
                updateClickResults('❌ 3.2节元素未找到');
            }
        }

        function showCurrentSelection() {
            updateClickResults(`当前选择: ${currentTextbook} > ${currentChapter} > ${currentSection}`);
        }

        function updateClickResults(message) {
            const resultsDiv = document.getElementById('click-results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `<p>[${timestamp}] ${message}</p>`;
        }

        function loadContentForSection(textbook, chapter, section) {
            updateClickResults(`🔄 正在加载内容: ${textbook} - ${chapter} - ${section}`);
            
            // 模拟API调用
            setTimeout(() => {
                updateClickResults(`✅ 内容加载完成: ${section}`);
            }, 500);
        }

        function loadChaptersForTextbook(textbook) {
            updateClickResults(`🔄 正在加载 ${textbook} 的章节列表...`);
            
            // 这里应该根据教材更新章节列表
            setTimeout(() => {
                updateClickResults(`✅ ${textbook} 章节列表加载完成`);
            }, 300);
        }

        // 初始化显示
        updateClickResults('🚀 侧边栏测试页面已加载，可以开始测试功能');
    </script>
</body>
</html>
