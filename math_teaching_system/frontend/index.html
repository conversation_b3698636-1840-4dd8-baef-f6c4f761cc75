<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>逗逗专用高中智能数学教学系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background: #f5f5f5;
            display: flex;
            height: 100vh;
            overflow: hidden;
        }

        /* 左侧导航栏 */
        .sidebar {
            width: 200px;
            background: white;
            border-right: 1px solid #ddd;
            overflow-y: auto;
        }

        /* 系统标题 */
        .system-title {
            background: #007AFF;
            color: white;
            padding: 15px 10px;
            font-size: 14px;
            font-weight: 600;
            text-align: center;
            line-height: 1.3;
        }

        /* 章节导航 */
        .chapter-nav {
            padding: 0;
        }

        .chapter-item {
            border-bottom: 1px solid #eee;
        }

        .chapter-title {
            background: #f8f9fa;
            padding: 12px 15px;
            font-size: 13px;
            font-weight: 600;
            color: #333;
            cursor: pointer;
            border: none;
            width: 100%;
            text-align: left;
            transition: background-color 0.2s;
        }

        .chapter-title:hover {
            background: #e9ecef;
        }

        .chapter-title.active {
            background: #007AFF;
            color: white;
        }

        /* 小节列表 */
        .section-list {
            background: white;
        }

        .section-item {
            padding: 8px 25px;
            font-size: 12px;
            color: #666;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.2s;
        }

        .section-item:hover {
            background: #f8f9fa;
            color: #333;
        }

        .section-item.active {
            background: #e3f2fd;
            color: #007AFF;
            font-weight: 500;
        }

        /* 右侧内容区域 */
        .main-content {
            flex: 1;
            background: white;
            display: flex;
            flex-direction: column;
        }

        /* 内容头部 */
        .content-header {
            padding: 20px 30px;
            border-bottom: 1px solid #eee;
            background: white;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .content-subtitle {
            font-size: 14px;
            color: #666;
        }

        /* 内容区域 */
        .content-area {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
            background: white;
        }

        /* 模块导航 */
        .module-nav {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            padding: 20px 0;
            border-bottom: 1px solid #eee;
        }

        .module-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            color: #666;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }

        .module-btn:hover {
            border-color: #007AFF;
            color: #007AFF;
        }

        .module-btn.active {
            background: #007AFF;
            color: white;
            border-color: #007AFF;
        }

        /* 内容卡片 */
        .content-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .content-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .content-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin: 5px 5px 5px 0;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #007AFF;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-outline {
            background: white;
            color: #007AFF;
            border: 1px solid #007AFF;
        }

        .btn-outline:hover {
            background: #007AFF;
            color: white;
        }

        /* 空状态 */
        .empty-state {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            color: #999;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 18px;
            margin-bottom: 10px;
            font-weight: 500;
        }

        .empty-state p {
            font-size: 14px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <!-- 系统标题 -->
        <div class="system-title">
            逗逗专用高中智能数学教学系统
        </div>

        <!-- 章节导航 -->
        <div class="chapter-nav" id="chapter-nav">
            <!-- 章节内容将通过JavaScript动态生成 -->
        </div>
    </div>

    <div class="main-content">
        <!-- 内容头部 -->
        <div class="content-header">
            <div class="content-title" id="current-chapter">请选择章节</div>
            <div class="content-subtitle" id="current-section">请选择小节</div>
        </div>

        <!-- 模块导航 -->
        <div class="module-nav">
            <button class="module-btn active" data-module="concept_learning">概念学习</button>
            <button class="module-btn" data-module="core_skills">核心技巧</button>
            <button class="module-btn" data-module="problem_types">题型高手</button>
            <button class="module-btn" data-module="practice_exercises">动手练习</button>
        </div>

        <!-- 内容区域 -->
        <div class="content-area" id="content-area">
            <div class="empty-state">
                <i>📚</i>
                <h3>欢迎使用逗逗专用高中智能数学教学系统</h3>
                <p>请从左侧选择章节和小节开始学习</p>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTextbook = '必修第一册';
        let currentChapter = '';
        let currentSection = '';
        let currentModule = 'concept_learning';

        // 教材章节数据
        const textbookData = {
            '必修第一册': [
                {
                    id: '第01章_集合与常用逻辑用语',
                    title: '第一章 集合与常用逻辑用语',
                    sections: [
                        { id: '1.1', name: '集合的概念' },
                        { id: '1.2', name: '集合间的基本关系' },
                        { id: '1.3', name: '集合的基本运算' },
                        { id: '1.4', name: '充分条件与必要条件' },
                        { id: '1.5', name: '全称量词与存在量词' }
                    ]
                },
                {
                    id: '第02章_一元二次函数方程和不等式',
                    title: '第二章 一元二次函数、方程和不等式',
                    sections: [
                        { id: '2.1', name: '等式性质与不等式性质' },
                        { id: '2.2', name: '基本不等式' },
                        { id: '2.3', name: '二次函数与一元二次方程、不等式' }
                    ]
                },
                {
                    id: '第03章_函数概念与性质',
                    title: '第三章 函数概念与性质',
                    sections: [
                        { id: '3.1', name: '函数的概念及其表示' },
                        { id: '3.2', name: '函数的基本性质' },
                        { id: '3.3', name: '幂函数' },
                        { id: '3.4', name: '函数的应用' }
                    ]
                }
            ]
        };

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeNavigation();
            initializeModuleButtons();
        });

        // 初始化导航
        function initializeNavigation() {
            const chapterNav = document.getElementById('chapter-nav');
            const chapters = textbookData[currentTextbook];
            
            let navHtml = '';
            chapters.forEach((chapter, index) => {
                const isFirst = index === 0;
                navHtml += `
                    <div class="chapter-item">
                        <button class="chapter-title ${isFirst ? 'active' : ''}" onclick="toggleChapter('${chapter.id}', this)">
                            ${chapter.title}
                        </button>
                        <div class="section-list" id="${chapter.id}-sections" style="display: ${isFirst ? 'block' : 'none'};">
                            ${chapter.sections.map((section, sIndex) => `
                                <div class="section-item ${isFirst && sIndex === 0 ? 'active' : ''}" 
                                     onclick="selectSection('${chapter.id}', '${section.id}', '${section.name}', this)">
                                    ${section.id} ${section.name}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            });
            
            chapterNav.innerHTML = navHtml;
            
            // 设置默认选择
            if (chapters.length > 0) {
                currentChapter = chapters[0].id;
                currentSection = chapters[0].sections[0].id;
                updateContentHeader(chapters[0].title, chapters[0].sections[0].name);
                loadContent();
            }
        }

        // 切换章节
        function toggleChapter(chapterId, element) {
            // 隐藏所有章节的小节
            document.querySelectorAll('.section-list').forEach(list => {
                list.style.display = 'none';
            });

            // 移除所有章节标题的active状态
            document.querySelectorAll('.chapter-title').forEach(title => {
                title.classList.remove('active');
            });

            // 显示当前章节的小节
            const sectionList = document.getElementById(chapterId + '-sections');
            if (sectionList) {
                sectionList.style.display = 'block';
            }

            // 激活当前章节标题
            element.classList.add('active');
            currentChapter = chapterId;

            // 更新标题
            const chapter = textbookData[currentTextbook].find(ch => ch.id === chapterId);
            if (chapter) {
                updateContentHeader(chapter.title, '请选择小节');
            }
        }

        // 选择小节
        function selectSection(chapterId, sectionId, sectionName, element) {
            // 移除所有小节的active状态
            document.querySelectorAll('.section-item').forEach(item => {
                item.classList.remove('active');
            });

            // 激活当前小节
            element.classList.add('active');
            currentChapter = chapterId;
            currentSection = sectionId;

            // 更新标题
            const chapter = textbookData[currentTextbook].find(ch => ch.id === chapterId);
            updateContentHeader(chapter.title, `${sectionId} ${sectionName}`);

            // 加载内容
            loadContent();
        }

        // 更新内容头部
        function updateContentHeader(chapterTitle, sectionTitle) {
            document.getElementById('current-chapter').textContent = chapterTitle;
            document.getElementById('current-section').textContent = sectionTitle;
        }

        // 初始化模块按钮
        function initializeModuleButtons() {
            const moduleButtons = document.querySelectorAll('.module-btn');
            moduleButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有按钮的active状态
                    moduleButtons.forEach(b => b.classList.remove('active'));
                    
                    // 激活当前按钮
                    this.classList.add('active');
                    currentModule = this.dataset.module;
                    
                    // 重新加载内容
                    loadContent();
                });
            });
        }

        // 加载内容
        async function loadContent() {
            if (!currentChapter || !currentSection) {
                return;
            }

            const contentArea = document.getElementById('content-area');
            contentArea.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #666;">
                    <div style="font-size: 24px; margin-bottom: 20px;">⏳</div>
                    <h3>正在加载内容...</h3>
                    <p>正在从知识库获取内容，请稍候...</p>
                </div>
            `;

            try {
                // 构建文件路径
                const sectionName = getSectionName(currentSection);
                const filePath = `/media/dp/software/mathtech1.0/3.0/math_teaching_system/knowledge_base_structured/${currentTextbook}/${currentChapter}/${currentSection}_${sectionName}.json`;
                
                console.log('Loading:', filePath);
                
                const response = await fetch(filePath);
                if (response.ok) {
                    const data = await response.json();
                    renderContent(data);
                } else {
                    renderFallbackContent();
                }
            } catch (error) {
                console.error('加载失败:', error);
                renderFallbackContent();
            }
        }

        // 获取小节名称
        function getSectionName(sectionId) {
            const chapter = textbookData[currentTextbook].find(ch => ch.id === currentChapter);
            if (chapter) {
                const section = chapter.sections.find(s => s.id === sectionId);
                return section ? section.name : '未知小节';
            }
            return '未知小节';
        }

        // 渲染内容
        function renderContent(data) {
            const contentArea = document.getElementById('content-area');
            const moduleContent = data.content[currentModule];
            
            if (moduleContent) {
                let html = `
                    <div class="content-card">
                        <h3>${data.section_info.full_title} - ${getModuleName(currentModule)}</h3>
                `;
                
                // 根据模块类型渲染内容
                if (currentModule === 'concept_learning' && moduleContent.content) {
                    html += `
                        <p><strong>定义：</strong>${moduleContent.content.definition || '暂无定义'}</p>
                        ${moduleContent.content.key_points ? `
                            <div style="margin: 15px 0;">
                                <strong>要点：</strong>
                                <ul style="margin: 10px 0; padding-left: 20px;">
                                    ${moduleContent.content.key_points.map(point => `<li>${point}</li>`).join('')}
                                </ul>
                            </div>
                        ` : ''}
                    `;
                } else {
                    html += `<p>${moduleContent.title || '内容加载中...'}</p>`;
                }
                
                html += `
                        <div style="margin-top: 20px;">
                            <button class="btn btn-primary">🎧 听讲解</button>
                            <button class="btn btn-outline">🎬 看动画</button>
                            <button class="btn btn-outline">📹 视频精讲</button>
                            <button class="btn btn-outline">🖼️ 概念图片</button>
                        </div>
                    </div>
                `;
                
                contentArea.innerHTML = html;
            } else {
                renderFallbackContent();
            }
        }

        // 渲染备用内容
        function renderFallbackContent() {
            const contentArea = document.getElementById('content-area');
            contentArea.innerHTML = `
                <div class="content-card">
                    <h3>${currentChapter} - ${currentSection} - ${getModuleName(currentModule)}</h3>
                    <p>✅ 章节导航功能正常工作！</p>
                    <p>✅ 知识库已重构为细粒度小节文件！</p>
                    <p>📁 文件路径：${currentTextbook}/${currentChapter}/${currentSection}_${getSectionName(currentSection)}.json</p>
                    <div style="margin-top: 20px;">
                        <button class="btn btn-primary">🎧 听讲解</button>
                        <button class="btn btn-outline">🎬 看动画</button>
                        <button class="btn btn-outline">📹 视频精讲</button>
                        <button class="btn btn-outline">🖼️ 概念图片</button>
                    </div>
                </div>
            `;
        }

        // 获取模块名称
        function getModuleName(module) {
            const names = {
                'concept_learning': '概念学习',
                'core_skills': '核心技巧',
                'problem_types': '题型高手',
                'practice_exercises': '动手练习'
            };
            return names[module] || module;
        }
    </script>
</body>
</html>
