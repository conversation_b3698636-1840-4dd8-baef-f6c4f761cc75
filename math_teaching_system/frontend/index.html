<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>逗逗专用高中智能数学教学系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background: #f5f5f5;
            display: flex;
            height: 100vh;
            overflow: hidden;
        }

        /* 左侧导航栏 */
        .sidebar {
            width: 200px;
            background: white;
            border-right: 1px solid #ddd;
            overflow-y: auto;
        }

        /* 系统标题 */
        .system-title {
            background: #007AFF;
            color: white;
            padding: 15px 10px;
            font-size: 14px;
            font-weight: 600;
            text-align: center;
            line-height: 1.3;
        }

        /* 教材选择 */
        .textbook-selector {
            padding: 15px;
            border-bottom: 1px solid #eee;
        }

        .textbook-selector label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
            display: block;
        }

        .textbook-selector select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
        }

        /* 章节导航 */
        .chapter-nav {
            padding: 0;
        }

        .chapter-item {
            border-bottom: 1px solid #eee;
        }

        .chapter-title {
            background: #f8f9fa;
            padding: 12px 15px;
            font-size: 13px;
            font-weight: 600;
            color: #333;
            cursor: pointer;
            border: none;
            width: 100%;
            text-align: left;
            transition: background-color 0.2s;
        }

        .chapter-title:hover {
            background: #e9ecef;
        }

        .chapter-title.active {
            background: #007AFF;
            color: white;
        }

        /* 右侧内容区域 */
        .main-content {
            flex: 1;
            background: white;
            display: flex;
            flex-direction: column;
        }

        /* 内容头部 */
        .content-header {
            padding: 20px 30px;
            border-bottom: 1px solid #eee;
            background: white;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .content-subtitle {
            font-size: 14px;
            color: #666;
        }

        /* 内容区域 */
        .content-area {
            flex: 1;
            padding: 0;
            overflow: hidden;
            background: white;
        }

        /* 章节页面iframe */
        .chapter-frame {
            width: 100%;
            height: 100%;
            border: none;
            display: none;
        }

        .chapter-frame.active {
            display: block;
        }

        /* 空状态 */
        .empty-state {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            color: #999;
            padding: 40px;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 18px;
            margin-bottom: 10px;
            font-weight: 500;
        }

        .empty-state p {
            font-size: 14px;
            line-height: 1.5;
            text-align: center;
        }

        /* 加载状态 */
        .loading-state {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            color: #666;
        }

        .loading-state i {
            font-size: 24px;
            margin-bottom: 15px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <!-- 系统标题 -->
        <div class="system-title">
            逗逗专用高中智能数学教学系统
        </div>

        <!-- 教材选择 -->
        <div class="textbook-selector">
            <label for="textbook-select">选择教材：</label>
            <select id="textbook-select" onchange="changeTextbook()">
                <option value="必修第一册">必修第一册</option>
                <option value="必修第二册">必修第二册</option>
                <option value="选择性必修第一册">选择性必修第一册</option>
                <option value="选择性必修第二册">选择性必修第二册</option>
                <option value="选择性必修第三册">选择性必修第三册</option>
            </select>
        </div>

        <!-- 章节导航 -->
        <div class="chapter-nav" id="chapter-nav">
            <!-- 章节内容将通过JavaScript动态生成 -->
        </div>
    </div>

    <div class="main-content">
        <!-- 内容头部 -->
        <div class="content-header">
            <div class="content-title" id="current-chapter">请选择章节</div>
            <div class="content-subtitle" id="current-textbook">必修第一册</div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area" id="content-area">
            <div class="empty-state">
                <div style="font-size: 48px; margin-bottom: 20px;">📚</div>
                <h3>欢迎使用逗逗专用高中智能数学教学系统</h3>
                <p>请从左侧选择教材和章节开始学习<br>每个章节都有独立的学习页面，包含完整的四大学习模块</p>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTextbook = '必修第一册';
        let currentChapter = '';

        // 教材章节数据
        const textbookData = {
            '必修第一册': [
                { id: '第1章_集合与常用逻辑用语', title: '第1章 集合与常用逻辑用语' },
                { id: '第2章_一元二次函数、方程和不等式', title: '第2章 一元二次函数、方程和不等式' },
                { id: '第3章_函数的概念与性质', title: '第3章 函数的概念与性质' },
                { id: '第4章_指数函数与对数函数', title: '第4章 指数函数与对数函数' },
                { id: '第5章_三角函数', title: '第5章 三角函数' }
            ],
            '必修第二册': [
                { id: '第6章_平面向量及其应用', title: '第6章 平面向量及其应用' },
                { id: '第7章_复数', title: '第7章 复数' },
                { id: '第8章_立体几何初步', title: '第8章 立体几何初步' },
                { id: '第9章_统计', title: '第9章 统计' },
                { id: '第10章_概率', title: '第10章 概率' }
            ],
            '选择性必修第一册': [
                { id: '第1章_空间向量与立体几何', title: '第1章 空间向量与立体几何' },
                { id: '第2章_直线和圆的方程', title: '第2章 直线和圆的方程' },
                { id: '第3章_圆锥曲线的方程', title: '第3章 圆锥曲线的方程' }
            ],
            '选择性必修第二册': [
                { id: '第4章_数列', title: '第4章 数列' },
                { id: '第5章_一元函数的导数及其应用', title: '第5章 一元函数的导数及其应用' }
            ],
            '选择性必修第三册': [
                { id: '第6章_计数原理', title: '第6章 计数原理' },
                { id: '第7章_随机变量及其分布', title: '第7章 随机变量及其分布' },
                { id: '第8章_成对数据的统计分析', title: '第8章 成对数据的统计分析' }
            ]
        };

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeNavigation();
        });

        // 初始化导航
        function initializeNavigation() {
            updateChapterNavigation();
        }

        // 更新章节导航
        function updateChapterNavigation() {
            const chapterNav = document.getElementById('chapter-nav');
            const chapters = textbookData[currentTextbook];
            
            let navHtml = '';
            chapters.forEach((chapter, index) => {
                navHtml += `
                    <div class="chapter-item">
                        <button class="chapter-title" onclick="selectChapter('${chapter.id}', '${chapter.title}', this)">
                            ${chapter.title}
                        </button>
                    </div>
                `;
            });
            
            chapterNav.innerHTML = navHtml;
        }

        // 切换教材
        function changeTextbook() {
            const select = document.getElementById('textbook-select');
            currentTextbook = select.value;
            currentChapter = '';
            
            // 更新显示
            document.getElementById('current-textbook').textContent = currentTextbook;
            document.getElementById('current-chapter').textContent = '请选择章节';
            
            // 更新导航
            updateChapterNavigation();
            
            // 清空内容区域
            showEmptyState();
        }

        // 选择章节
        function selectChapter(chapterId, chapterTitle, element) {
            // 移除所有章节的active状态
            document.querySelectorAll('.chapter-title').forEach(title => {
                title.classList.remove('active');
            });

            // 激活当前章节
            element.classList.add('active');
            currentChapter = chapterId;

            // 更新标题
            document.getElementById('current-chapter').textContent = chapterTitle;

            // 加载章节页面
            loadChapterPage(chapterId);
        }

        // 加载章节页面
        function loadChapterPage(chapterId) {
            const contentArea = document.getElementById('content-area');
            
            // 显示加载状态
            contentArea.innerHTML = `
                <div class="loading-state">
                    <i class="fas fa-spinner"></i>
                    <h3>正在加载章节页面...</h3>
                    <p>正在加载 ${currentTextbook} - ${chapterId} 的学习内容</p>
                </div>
            `;

            // 构建章节页面URL
            const chapterUrl = `chapters/${currentTextbook}/${chapterId}.html`;
            
            // 创建iframe加载章节页面
            setTimeout(() => {
                contentArea.innerHTML = `
                    <iframe src="${chapterUrl}" 
                            class="chapter-frame active" 
                            onload="onChapterLoaded()"
                            onerror="onChapterError()">
                    </iframe>
                `;
            }, 500);
        }

        // 章节页面加载完成
        function onChapterLoaded() {
            console.log('章节页面加载完成');
        }

        // 章节页面加载错误
        function onChapterError() {
            const contentArea = document.getElementById('content-area');
            contentArea.innerHTML = `
                <div class="empty-state">
                    <div style="font-size: 48px; margin-bottom: 20px; color: #f72585;">⚠️</div>
                    <h3>章节页面加载失败</h3>
                    <p>无法加载 ${currentTextbook} - ${currentChapter} 的页面<br>请检查文件是否存在或稍后重试</p>
                </div>
            `;
        }

        // 显示空状态
        function showEmptyState() {
            const contentArea = document.getElementById('content-area');
            contentArea.innerHTML = `
                <div class="empty-state">
                    <div style="font-size: 48px; margin-bottom: 20px;">📚</div>
                    <h3>欢迎使用逗逗专用高中智能数学教学系统</h3>
                    <p>请从左侧选择教材和章节开始学习<br>每个章节都有独立的学习页面，包含完整的四大学习模块</p>
                </div>
            `;
        }
    </script>
</body>
</html>
