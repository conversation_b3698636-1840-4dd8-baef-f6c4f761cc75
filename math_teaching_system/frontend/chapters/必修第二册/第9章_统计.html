<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第9章 统计 - 逗逗专用高中智能数学教学系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/d3@7"></script>
    <script src="https://cdn.jsdelivr.net/npm/markmap-lib@0.15.3/dist/browser/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/markmap-view@0.15.3/dist/browser/index.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        :root {
            --primary: #4361ee;
            --primary-light: #eef2ff;
            --secondary: #3f37c9;
            --accent: #4895ef;
            --success: #4cc9f0;
            --warning: #f72585;
            --dark: #1e293b;
            --light: #f8fafc;
            --gray: #64748b;
            --light-gray: #e2e8f0;
            --border-radius: 16px;
            --shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
            --transition: all 0.3s ease;
        }

        body {
            background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
        }

        /* 侧边导航 */
        .sidebar {
            width: 280px;
            background: linear-gradient(135deg, #3a0ca3 0%, #4361ee 100%);
            color: white;
            padding: 20px 0;
            height: 100vh;
            position: sticky;
            top: 0;
            overflow-y: auto;
            box-shadow: var(--shadow);
            z-index: 100;
        }

        .logo {
            display: flex;
            align-items: center;
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .logo i {
            font-size: 28px;
            margin-right: 12px;
        }

        .logo h1 {
            font-size: 20px;
            font-weight: 600;
        }

        .menu-title {
            padding: 10px 20px;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 20px;
        }

        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: var(--transition);
            border-left: 3px solid transparent;
            min-height: 48px;
        }

        .menu-item:hover, .menu-item.active {
            background: rgba(255, 255, 255, 0.1);
            border-left: 3px solid var(--accent);
        }

        .menu-item i {
            margin-right: 12px;
            font-size: 18px;
            width: 24px;
            text-align: center;
        }

        .menu-item span {
            font-size: 16px;
        }

        /* 语音设置样式 */
        .voice-settings-toggle {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            padding: 12px 20px;
            min-height: 48px;
            cursor: pointer;
        }

        .voice-settings-toggle .voice-toggle-left {
            display: flex;
            align-items: center;
            flex: 1;
        }
        
        /* 修正图标对齐问题 */
        .voice-settings-toggle .voice-toggle-left i {
            margin-right: 12px;
            font-size: 18px;
            width: 24px;
            text-align: center;
        }

        .voice-settings-toggle .voice-toggle-left span {
            font-size: 16px;
            line-height: 1.2;
        }

        .voice-settings-toggle .voice-arrow {
            font-size: 14px;
            transition: transform 0.3s ease;
        }

        .voice-settings.expanded .voice-toggle-arrow {
            transform: rotate(180deg);
        }
        
        .voice-arrow.rotated {
            transform: rotate(180deg);
        }

        .voice-settings-panel {
            background: rgba(255, 255, 255, 0.05);
            border-left: 3px solid var(--accent);
            padding: 0;
            margin: 0;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease, padding 0.3s ease;
            position: relative;
            z-index: 10;
        }

        .voice-settings-panel.show {
            max-height: 400px;
            padding: 15px 20px;
        }

        .voice-option {
            margin-bottom: 15px;
        }

        .voice-option:last-child {
            margin-bottom: 0;
        }

        .voice-label {
            display: block;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 6px;
            font-weight: 500;
        }

        .voice-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 13px;
            cursor: pointer;
            transition: var(--transition);
        }

        .voice-select:hover {
            border-color: var(--accent);
            background: rgba(255, 255, 255, 0.15);
        }

        .voice-select:focus {
            outline: none;
            border-color: var(--accent);
            box-shadow: 0 0 0 2px rgba(76, 201, 240, 0.2);
        }

        .voice-select option {
            background: var(--primary);
            color: white;
            padding: 8px;
        }

        .voice-note {
            margin-top: 8px;
            padding: 8px 12px;
            background: rgba(255, 152, 0, 0.1);
            border: 1px solid rgba(255, 152, 0, 0.3);
            border-radius: 4px;
            font-size: 11px;
            color: #ff9800;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .voice-note i {
            font-size: 12px;
        }

        .slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .voice-slider {
            flex: 1;
            height: 4px;
            border-radius: 2px;
            background: rgba(255, 255, 255, 0.2);
            outline: none;
            cursor: pointer;
            appearance: none;
        }

        .voice-slider::-webkit-slider-thumb {
            appearance: none;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: var(--accent);
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .voice-slider::-moz-range-thumb {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: var(--accent);
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .slider-value {
            font-size: 12px;
            color: var(--accent);
            min-width: 40px;
            text-align: center;
            font-weight: bold;
        }

        .test-voice-btn {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--accent);
            border-radius: 6px;
            background: rgba(76, 201, 240, 0.1);
            color: var(--accent);
            font-size: 13px;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .test-voice-btn:hover {
            background: var(--accent);
            color: white;
        }

        .test-voice-btn:active {
            transform: scale(0.98);
        }

        .submenu {
            padding-left: 55px;
            max-height: 0;
            overflow: hidden;
            transition: var(--transition);
        }

        .submenu.show {
            max-height: 500px;
        }

        .submenu-item {
            padding: 8px 0;
            font-size: 14px;
            opacity: 0.9;
            position: relative;
            cursor: pointer;
        }

        .submenu-item:hover, .submenu-item.active {
            opacity: 1;
            color: var(--accent);
        }

        .submenu-item.active:before {
            content: "";
            position: absolute;
            left: -15px;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 6px;
            background: var(--accent);
            border-radius: 50%;
        }

        .container {
            flex: 1;
            padding: 20px;
        }

        /* 学习路径导航 */
        .learning-path-nav {
            display: flex;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            margin-bottom: 24px;
            overflow: hidden;
        }

        .path-step {
            flex: 1;
            text-align: center;
            padding: 18px 10px;
            position: relative;
            cursor: pointer;
            transition: var(--transition);
            font-weight: 500;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .path-step:not(:last-child):after {
            content: "";
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 1px;
            height: 40%;
            background: var(--light-gray);
        }

        .path-step.active {
            background: var(--primary-light);
            color: var(--primary);
        }

        .path-step.active .step-icon {
            background: var(--primary);
            color: white;
        }

        .step-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--light-gray);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: var(--transition);
        }

        /* 内容网格 */
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 24px;
        }

        /* 主内容区 */
        .main-content {
            display: grid;
            gap: 24px;
        }

        /* 卡片设计 */
        .card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            overflow: hidden;
            transition: var(--transition);
        }

        .card-header {
            padding: 20px 25px;
            border-bottom: 1px solid var(--light-gray);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header h2 {
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .card-header h2 i {
            color: var(--primary);
        }

        .card-body {
            padding: 25px;
        }

        .btn {
            padding: 10px 18px;
            border-radius: 50px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: var(--transition);
            font-size: 15px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
        }

        .btn-outline {
            background: transparent;
            border: 2px solid var(--primary);
            color: var(--primary);
        }

        .btn-sm {
            padding: 8px 16px;
            font-size: 14px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(67, 97, 238, 0.25);
        }
        
        /* 概念学习区 */
        .concept-section h3 {
            font-size: 22px;
            color: var(--secondary);
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 2px solid var(--light-gray);
        }

        .definition-box {
            background: var(--primary-light);
            border-left: 5px solid var(--primary);
            padding: 24px;
            border-radius: 0 var(--border-radius) var(--border-radius) 0;
            margin-bottom: 30px;
            position: relative;
        }

        .definition-box:before {
            content: "核心概念";
            position: absolute;
            top: -12px;
            left: 20px;
            background: var(--primary);
            color: white;
            padding: 4px 12px;
            border-radius: 50px;
            font-size: 14px;
            font-weight: 500;
        }

        .definition-box p {
            line-height: 1.8;
            margin-bottom: 15px;
            font-size: 17px;
        }

        .definition-box strong {
            color: var(--primary);
        }

        .characteristics {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 30px 0;
        }

        .characteristic-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            border: 1px solid var(--light-gray);
            transition: var(--transition);
            display: flex;
            flex-direction: column;
        }

        .characteristic-card:hover {
            transform: translateY(-5px);
            border-color: var(--accent);
            box-shadow: 0 8px 20px rgba(67, 97, 238, 0.15);
        }
        
        .characteristic-card .card-content {
            flex-grow: 1;
        }

        .characteristic-card h4 {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            color: var(--secondary);
        }

        .characteristic-card h4 i {
            width: 36px;
            height: 36px;
            background: var(--primary-light);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary);
        }

        /* 表示法对比 */
        .methods-comparison {
            display: flex;
            gap: 24px;
            margin: 30px 0;
        }

        .method-card {
            flex: 1;
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            background: white;
            display: flex;
            flex-direction: column;
        }
        
        .method-card .card-content {
            flex-grow: 1;
        }

        .method-card h4 {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
            color: var(--secondary);
        }

        .method-card h4 i {
            font-size: 24px;
            color: var(--primary);
        }

        .examples {
            background: var(--light);
            border-radius: 12px;
            padding: 18px;
            margin-top: 15px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        /* 示例翻页 */
        .example-page {
            display: none;
        }

        .example-page.active {
            display: block;
            animation: fadeInExample 0.4s ease;
        }

        @keyframes fadeInExample {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .example-nav {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: 15px;
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px solid var(--light-gray);
            font-size: 14px;
            color: var(--gray);
        }

        .example-nav i {
            cursor: pointer;
            color: var(--primary);
            transition: var(--transition);
            width: 28px;
            height: 28px;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
        }

        .example-nav i:hover {
            background-color: var(--primary-light);
        }
        
        .example-nav i.disabled {
            color: #ccc;
            cursor: not-allowed;
            background-color: transparent;
        }

        /* 技巧卡片 */
        .skill-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            border: 1px solid var(--light-gray);
            margin: 0 0 30px 0;
        }

        .skill-card h4 {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
            color: var(--secondary);
            font-size: 20px;
        }

        .skill-card h4 i {
            font-size: 24px;
            color: var(--primary);
        }

        .skill-item {
            padding: 15px;
            background: var(--light);
            border-radius: var(--border-radius);
            margin-bottom: 15px;
            border-left: 4px solid var(--accent);
            position: relative;
        }

        .skill-item:last-child {
            margin-bottom: 0;
        }

        .skill-item h5 {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            color: var(--primary);
        }

        .audio-btn-small {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(67, 97, 238, 0.1);
            border: 1px solid var(--primary);
            color: var(--primary);
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 13px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: var(--transition);
        }

        .audio-btn-small:hover {
            background: var(--primary);
            color: white;
        }

        /* 动画演示区 */
        .animation-container {
            background: linear-gradient(135deg, #f5f7ff 0%, #e6eeff 100%);
            border-radius: var(--border-radius);
            height: 300px;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
            margin: 20px 0;
        }

        .animation-controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 15px;
        }

        /* 集合元素动画 */
        .set-element {
            position: absolute;
            width: 60px;
            height: 60px;
            background: var(--primary);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
            box-shadow: 0 6px 15px rgba(67, 97, 238, 0.4);
            animation: float 6s infinite ease-in-out;
            cursor: pointer;
            transition: var(--transition);
        }

        .set-element:hover {
            transform: scale(1.1);
            z-index: 10;
        }

        .set-element:nth-child(1) { top: 50px; left: 150px; background: var(--primary); animation-delay: 0s; }
        .set-element:nth-child(2) { top: 180px; left: 250px; background: var(--accent); animation-delay: 0.5s; }
        .set-element:nth-child(3) { top: 100px; left: 350px; background: var(--secondary); animation-delay: 1s; }
        .set-element:nth-child(4) { top: 200px; left: 400px; background: #560bad; animation-delay: 1.5s; }
        .set-element:nth-child(5) { top: 120px; left: 500px; background: #7209b7; animation-delay: 2s; }

        .set-circle {
            position: absolute;
            width: 400px;
            height: 250px;
            border: 4px dashed var(--primary);
            border-radius: 50%;
            opacity: 0.7;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-20px); }
        }

        /* 练习区 */
        #practice-module .card-body {
            display: flex;
            flex-direction: column;
        }
        #practice-pages-container {
            flex-grow: 1;
        }

        .exercise {
            margin-bottom: 25px;
            border-left: 4px solid var(--success);
            padding-left: 20px;
        }

        .exercise:last-child {
            margin-bottom: 0;
        }

        .exercise h4 {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 18px;
            color: var(--dark);
            font-size: 18px;
        }

        .exercise h4 i { color: var(--success); }

        .exercise-content {
            background: var(--light);
            border-radius: var(--border-radius);
            padding: 20px;
        }

        .options { margin: 15px 0; }

        .option {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            margin-bottom: 10px;
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            border: 2px solid transparent;
        }

        .option:hover { background: rgba(67, 97, 238, 0.05); }

        .option.selected {
            background: rgba(76, 201, 240, 0.15);
            border-color: var(--success);
        }

        .option input {
            margin-right: 12px;
            width: 18px;
            height: 18px;
        }

        .answer-input {
            width: 100%;
            padding: 14px 18px;
            border: 2px solid var(--light-gray);
            border-radius: 12px;
            font-size: 16px;
            margin-top: 10px;
            transition: var(--transition);
        }

        .answer-input:focus {
            border-color: var(--primary);
            outline: none;
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
        }

        .submit-btn {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 500;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: var(--transition);
            font-size: 15px;
            margin-top: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(67, 97, 238, 0.35);
        }

        .audio-btn {
            background: linear-gradient(135deg, #f72585, #b5179e);
            color: white;
            border: none;
            padding: 14px 28px;
            border-radius: 12px;
            font-weight: 500;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: var(--transition);
            font-size: 16px;
        }

        .audio-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(247, 37, 133, 0.35);
        }
        
        /* 反馈区 */
        .feedback-box {
            background: rgba(76, 201, 240, 0.1);
            border: 2px solid var(--success);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-top: 20px;
            display: none;
        }

        .feedback-box.show { display: block; }

        .feedback-box h4 {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            color: var(--secondary);
        }

        /* 侧边栏 */
        .sidebar-content { display: grid; gap: 24px; }

        /* 学习目标卡片 */
        .objective-list { list-style: none; }
        .objective-list li {
            padding: 16px 0;
            display: flex;
            align-items: flex-start;
            gap: 15px;
            border-bottom: 1px solid var(--light-gray);
        }
        .objective-list li:last-child { border-bottom: none; }
        .objective-icon {
            width: 30px;
            height: 30px;
            background: var(--primary-light);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary);
            flex-shrink: 0;
        }

        /* 学习工具 */
        .tools-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; }
        .tool-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: var(--transition);
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            border: 1px solid var(--light-gray);
        }
        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(67, 97, 238, 0.15);
            border-color: var(--accent);
        }
        .tool-card i { font-size: 36px; color: var(--primary); margin-bottom: 15px; }
        .tool-card h4 { margin-bottom: 8px; }

        /* 进度跟踪 */
        .progress-container { background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); border-radius: var(--border-radius); padding: 25px; text-align: center; }
        .progress-title { display: flex; align-items: center; justify-content: center; gap: 10px; margin-bottom: 20px; color: var(--secondary); }
        .progress-circle { width: 180px; height: 180px; margin: 0 auto 20px; position: relative; }
        .progress-bg { fill: none; stroke: var(--light-gray); stroke-width: 12; }
        .progress-bar { fill: none; stroke: var(--primary); stroke-width: 12; stroke-linecap: round; transform: rotate(-90deg); transform-origin: 50% 50%; transition: stroke-dashoffset 1s ease; }
        .progress-value { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 32px; font-weight: 700; color: var(--primary); }

        /* 知识应用区 */
        .application-section { padding: 25px; }
        .application-section h3 { font-size: 22px; color: var(--secondary); margin-bottom: 20px; padding-bottom: 12px; border-bottom: 2px solid var(--light-gray); }
        .application-card { background: white; border-radius: var(--border-radius); padding: 25px; box-shadow: 0 4px 12px rgba(0,0,0,0.05); margin: 20px 0; border-left: 4px solid var(--warning); }
        .application-card h4 { display: flex; align-items: center; gap: 12px; margin-bottom: 15px; color: var(--primary); }

        /* 难度选择 */
        .difficulty-selector { display: flex; gap: 15px; margin-bottom: 20px; }
        .difficulty-btn { padding: 8px 16px; border-radius: 20px; background: var(--light-gray); border: 2px solid transparent; cursor: pointer; transition: var(--transition); font-weight: 500; }
        .difficulty-btn.active { background: var(--primary-light); border-color: var(--primary); color: var(--primary); }

        /* 上传区域 */
        .upload-area { border: 2px dashed var(--light-gray); border-radius: var(--border-radius); padding: 25px; text-align: center; margin: 20px 0; cursor: pointer; transition: var(--transition); }
        .upload-area:hover { border-color: var(--accent); background: rgba(67, 97, 238, 0.05); }
        .upload-area i { font-size: 48px; color: var(--gray); margin-bottom: 15px; }

        /* 分页控件 */
        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 25px;
            justify-content: center;
        }
        
        .page-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--light-gray);
            color: var(--dark);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .page-btn.active {
            background: var(--primary);
            color: white;
        }
        
        .page-btn:hover:not(.active) {
            background: var(--primary-light);
        }
        
        .pagination-arrow {
            background: white;
            border: 2px solid var(--light-gray);
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .pagination-arrow:hover:not(.disabled) {
            border-color: var(--primary);
            background: var(--primary-light);
            color: var(--primary);
        }

        .pagination-arrow.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .page-indicator {
            font-weight: 600;
            color: var(--primary);
            margin: 0 10px;
        }
        
        .application-page, .skill-page, .practice-page {
            display: none;
        }
        
        .application-page.active, .skill-page.active, .practice-page.active {
            display: block;
            animation: fadeInExample 0.5s;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .content-grid { grid-template-columns: 1fr; }
            .characteristics { grid-template-columns: repeat(2, 1fr); }
        }
        @media (max-width: 992px) {
            .sidebar { width: 80px; }
            .sidebar .logo h1, .sidebar .menu-title, .sidebar .menu-item span, .submenu { display: none; }
            .menu-item { justify-content: center; padding: 15px 0; }
            .menu-item i { margin-right: 0; font-size: 20px; }
        }
        @media (max-width: 768px) {
            .learning-path-nav { flex-direction: column; }
            .path-step:not(:last-child):after { display: none; }
            .characteristics { grid-template-columns: 1fr; }
            .methods-comparison { flex-direction: column; }
            .animation-container { height: 250px; }
            .pagination-controls { flex-wrap: wrap; }
        }
        
        /* 内容模块 */
        .content-module { display: none; }
        .content-module.active { display: block; }

        /* 脑图弹窗样式 */
        .mindmap-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .mindmap-modal.show {
            display: flex;
        }

        .mindmap-modal-content {
            background: white;
            border-radius: var(--border-radius);
            width: 90%;
            height: 85%;
            max-width: 1200px;
            position: relative;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .mindmap-modal-header {
            padding: 20px 25px;
            border-bottom: 1px solid var(--light-gray);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: var(--primary-light);
        }

        .mindmap-modal-header h2 {
            color: var(--primary);
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .mindmap-close-btn {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--gray);
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: var(--transition);
        }

        .mindmap-close-btn:hover {
            background: rgba(0, 0, 0, 0.1);
            color: var(--dark);
        }

        .mindmap-modal-body {
            padding: 25px;
            height: calc(100% - 80px);
            overflow-y: auto;
        }

        .mindmap-container {
            height: 100%;
            border: 1px solid var(--light-gray);
            border-radius: 8px;
            background: #f8f9fa;
        }

        /* 语音控制面板样式 */
        .voice-control-panel {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 320px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            border: 1px solid var(--light-gray);
            z-index: 10000;
            animation: slideInUp 0.3s ease-out;
        }

        @keyframes slideInUp {
            from {
                transform: translateY(100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .voice-control-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 1px solid var(--light-gray);
            background: linear-gradient(135deg, var(--primary), #4a90e2);
            color: white;
            border-radius: 12px 12px 0 0;
        }

        .voice-control-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            font-size: 14px;
        }

        .voice-control-title i {
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.6; }
        }

        .voice-control-close {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .voice-control-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .voice-control-body {
            padding: 16px;
        }

        .voice-control-buttons {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }

        .voice-control-btn {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 12px 8px;
            background: var(--light-gray);
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 12px;
            color: var(--text);
        }

        .voice-control-btn:hover {
            background: var(--primary);
            color: white;
            transform: translateY(-1px);
        }

        .voice-control-btn i {
            font-size: 16px;
        }

        .voice-progress-container {
            margin-top: 12px;
        }

        .voice-progress-bar {
            width: 100%;
            height: 4px;
            background: var(--light-gray);
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .voice-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary), #4a90e2);
            width: 0%;
            transition: width 0.1s linear;
        }

        .voice-time {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: var(--gray);
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="logo">
            <i class="fas fa-calculator"></i>
            <h1>逗逗专用高中智能数学教学系统</h1>
        </div>
        
        <div class="menu-title">教材选择</div>
        <div class="menu-item has-submenu">
            <i class="fas fa-book-open"></i>
            <span>高中教材</span>
        </div>
        <div class="submenu">
            <div class="submenu-item">必修第一册</div>
            <div class="submenu-item">必修第二册</div>
            <div class="submenu-item">选择性必修一</div>
            <div class="submenu-item">选择性必修二</div>
            <div class="submenu-item">选择性必修三</div>
        </div>
        
        <div class="menu-title">章节导航</div>
        <div class="menu-item active">
            <i class="fas fa-folder-open"></i>
            <span>第一章 集合与常用逻辑用语</span>
        </div>
        <div class="submenu show">
            <div class="submenu-item active">1.1 集合的概念</div>
            <div class="submenu-item">1.2 集合间的基本关系</div>
            <div class="submenu-item">1.3 集合的基本运算</div>
            <div class="submenu-item">1.4 充分条件与必要条件</div>
            <div class="submenu-item">1.5 全称量词与存在量词</div>
        </div>
        
        <div class="menu-item">
            <i class="fas fa-folder"></i>
            <span>第二章 一元二次函数、方程和不等式</span>
        </div>
        <div class="menu-item">
            <i class="fas fa-folder"></i>
            <span>第三章 函数的概念与性质</span>
        </div>
        <div class="menu-item">
            <i class="fas fa-folder"></i>
            <span>第四章 指数函数与对数函数</span>
        </div>
        <div class="menu-item">
            <i class="fas fa-folder"></i>
            <span>第五章 三角函数</span>
        </div>
        
        <div class="menu-title">学习工具</div>
        <div class="menu-item voice-settings-toggle" onclick="toggleVoiceSettings()">
            <div class="voice-toggle-left">
                <i class="fas fa-language"></i>
                <span>语音设置</span>
            </div>
            <i class="fas fa-chevron-down voice-arrow" id="voiceArrow"></i>
        </div>
        <div class="voice-settings-panel" id="voiceSettingsPanel">
            <div class="voice-option">
                <label class="voice-label">语音类型</label>
                <select id="voiceSelect" class="voice-select">
                    <option value="zh-female-teacher">中文女教师 (ChatTTS数学优化版)</option>
                    <option value="zh-male-teacher">中文男教师 (ChatTTS)</option>
                    <option value="zh-female-sweet">中文女声(甜美) (ChatTTS)</option>
                    <option value="zh-male-deep">中文男声(深沉) (ChatTTS)</option>
                    <option value="zh-female-clear">中文女声(清晰) (ChatTTS)</option>
                    <option value="zh-male-warm">中文男声(温和) (ChatTTS)</option>
                </select>
                <div class="voice-note">
                    <i class="fas fa-microphone-alt"></i>
                    <span>当前使用ChatTTS引擎，数学教学专用优化版</span>
                </div>
            </div>
            <div class="voice-option">
                <label for="speedRange" class="voice-label">语音速度</label>
                <div class="slider-container">
                    <input type="range" id="speedRange" class="voice-slider" min="0.5" max="2.0" step="0.1" value="1.0">
                    <span id="speedValue" class="slider-value">1.0x</span>
                </div>
            </div>
            <div class="voice-option">
                <label for="volumeRange" class="voice-label">音量大小</label>
                <div class="slider-container">
                    <input type="range" id="volumeRange" class="voice-slider" min="0" max="1" step="0.1" value="0.8">
                    <span id="volumeValue" class="slider-value">80%</span>
                </div>
            </div>
            <button id="testVoiceBtn" class="test-voice-btn" onclick="testCurrentVoice()">
                <i class="fas fa-play"></i> 测试语音
            </button>
        </div>
        <div class="menu-item">
            <i class="fas fa-history"></i>
            <span>学习进度</span>
        </div>
        <div class="menu-item">
            <i class="fas fa-bookmark"></i>
            <span>我的笔记</span>
        </div>
    </div>
    
    <div class="container">
        <div class="learning-path-nav">
            <div class="path-step active" data-target="concept">
                <div class="step-icon">
                    <i class="fas fa-lightbulb"></i>
                </div>
                <span>概念学习</span>
            </div>
            <div class="path-step" data-target="skills">
                <div class="step-icon">
                    <i class="fas fa-magic"></i>
                </div>
                <span>技巧</span>
            </div>
            <div class="path-step" data-target="application">
                <div class="step-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <span>题型高手</span>
            </div>
            <div class="path-step" data-target="practice">
                <div class="step-icon">
                    <i class="fas fa-pencil-alt"></i>
                </div>
                <span>动手练习</span>
            </div>
        </div>
        
        <div class="content-grid">
            <div class="main-content">
                <div class="content-module active" id="concept-module">
                    <div class="card">
                        <div class="card-header">
                            <h2><i class="fas fa-book-open"></i> 集合的核心概念</h2>
                            <div style="display: flex; gap: 10px;">
                                <button class="btn btn-primary" onclick="playVoice('集合是数学中最基本的概念之一，指具有某种特定性质的、确定的、互异的对象的全体。这些对象称为集合的元素。集合通常用大写字母表示，如A、B、C，元素用小写字母表示，如a、b、c。集合具有三大特性：确定性、互异性和无序性。确定性是指任何一个对象要么属于该集合，要么不属于该集合，二者必居其一。互异性是指集合中的元素互不相同，没有重复元素。无序性是指集合中的元素没有顺序关系。集合有两种主要表示方法：列举法和描述法。列举法是将集合的元素一一列举出来，用大括号括起来。描述法是用集合所含元素的共同特征表示集合。')">
                                    <i class="fas fa-headphones"></i> 听概念讲解
                                </button>
                                <button class="btn btn-outline">
                                    <i class="fas fa-video"></i> 视频精讲
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="concept-section">
                                <div class="definition-box">
                                    <p><strong>集合</strong>是数学中最基本的概念之一，指具有某种特定性质的、确定的、互异的对象的全体。这些对象称为集合的<strong>元素</strong>。</p>
                                    <p>集合通常用大写字母表示（如 A, B, C），元素用小写字母表示（如 a, b, c）。</p>
                                </div>
                                
                                <h3>集合的三大特性</h3>
                                <div class="characteristics">
                                    <div class="characteristic-card">
                                        <div class="card-content">
                                            <h4><i class="fas fa-check-circle"></i> 确定性</h4>
                                            <p>任何一个对象要么属于该集合，要么不属于该集合，二者必居其一。</p>
                                        </div>
                                        <div class="examples">
                                            <div class="example-page">
                                                <p><strong>正确：</strong>"身高超过180cm的学生"</p>
                                                <p><strong>错误：</strong>"高个子学生"（标准模糊）</p>
                                            </div>
                                            <div class="example-page">
                                                <p><strong>正确：</strong>"方程 x-1=0 的解"</p>
                                                <p><strong>错误：</strong>"著名的数学家"（标准模糊）</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="characteristic-card">
                                        <div class="card-content">
                                            <h4><i class="fas fa-ban"></i> 互异性</h4>
                                            <p>集合中的元素互不相同，没有重复元素。</p>
                                        </div>
                                        <div class="examples">
                                            <div class="example-page">
                                                <p><strong>正确：</strong>{1,2,3}</p>
                                                <p><strong>错误：</strong>{1,1,2}（自动变为{1,2}）</p>
                                            </div>
                                            <div class="example-page">
                                                <p><strong>正确：</strong>单词"apple"中字母的集合 {a,p,l,e}</p>
                                                <p><strong>错误：</strong>{a,p,p,l,e}（应去重）</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="characteristic-card">
                                        <div class="card-content">
                                            <h4><i class="fas fa-random"></i> 无序性</h4>
                                            <p>集合中的元素没有顺序关系。</p>
                                        </div>
                                        <div class="examples">
                                            <div class="example-page">
                                                <p><strong>正确：</strong>{a,b,c} = {b,c,a}</p>
                                                <p><strong>错误：</strong>认为{a,b,c}与{b,c,a}不同</p>
                                            </div>
                                            <div class="example-page">
                                                <p><strong>正确：</strong>{1, 3, 5} = {5, 1, 3}</p>
                                                <p><strong>错误：</strong>认为元素的顺序很重要</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <h3>集合的表示方法</h3>
                                <div class="methods-comparison">
                                    <div class="method-card">
                                        <div class="card-content">
                                            <h4><i class="fas fa-list-ol"></i> 列举法</h4>
                                            <p>将集合的元素一一列举出来，用大括号{}括起来。</p>
                                        </div>
                                        <div class="examples">
                                            <div class="example-page">
                                                <p><strong>示例1：</strong>小于5的自然数集合：{0,1,2,3,4}</p>
                                                <p><strong>示例2：</strong>英文字母元音集合：{a,e,i,o,u}</p>
                                            </div>
                                            <div class="example-page">
                                                <p><strong>示例3：</strong>方程 x²-9=0 的解集：{-3, 3}</p>
                                                <p><strong>示例4：</strong>1到10之间的质数集合：{2, 3, 5, 7}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="method-card">
                                        <div class="card-content">
                                            <h4><i class="fas fa-pencil-alt"></i> 描述法</h4>
                                            <p>用集合所含元素的共同特征表示集合。</p>
                                        </div>
                                        <div class="examples">
                                            <div class="example-page">
                                                <p><strong>示例1：</strong>偶数集合：{x | x是整数, x能被2整除}</p>
                                                <p><strong>示例2：</strong>正奇数集合：{x | x=2k-1, k∈N*}</p>
                                            </div>
                                            <div class="example-page">
                                                <p><strong>示例3：</strong>有理数集合Q：{p/q | p,q∈Z, q≠0}</p>
                                                <p><strong>示例4：</strong>第一象限的点集：{(x,y) | x>0, y>0}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h2><i class="fas fa-eye"></i> 集合元素可视化</h2>
                            <div>
                                <button class="btn btn-outline">
                                    <i class="fas fa-sync-alt"></i> 重置演示
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="manim-animation-container">
                                <video id="set-animation-video"
                                       width="100%"
                                       height="400"
                                       controls
                                       loop
                                       muted
                                       autoplay
                                       preload="auto"
                                       style="border-radius: 12px; background: #F5F7FA; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                                    <source src="SetVisualization.mp4" type="video/mp4">
                                    您的浏览器不支持视频播放。请检查视频文件是否存在。
                                </video>

                                <div style="margin-top: 16px; padding: 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid var(--primary);">
                                    <p style="margin: 0; color: #666; font-size: 14px;">
                                        <i class="fas fa-magic" style="color: var(--primary); margin-right: 8px;"></i>
                                        使用Manim专业数学动画库制作，演示集合的三大特性（确定性、互异性、无序性）和元素关系（∈、∉）
                                    </p>
                                </div>
                            </div>

                            <div class="animation-controls">
                                <button class="btn btn-primary" onclick="playManimAnimation()">
                                    <i class="fas fa-play"></i> 播放动画
                                </button>
                                <button class="btn btn-outline" onclick="pauseManimAnimation()">
                                    <i class="fas fa-pause"></i> 暂停
                                </button>
                                <button class="btn btn-outline" onclick="fullscreenManimAnimation()">
                                    <i class="fas fa-expand"></i> 全屏查看
                                </button>
                                <button class="btn btn-outline" onclick="restartManimAnimation()">
                                    <i class="fas fa-redo"></i> 重新播放
                                </button>
                            </div>

                            <div class="interactive-hint">
                                <p><i class="fas fa-graduation-cap"></i> 专业数学动画演示：观看完整的集合概念教学动画</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="content-module" id="skills-module">
                    <div class="card">
                        <div class="card-header">
                            <h2><i class="fas fa-magic"></i> 集合解题技巧</h2>
                        </div>
                        <div class="card-body">
                            <div id="skills-pages-container">
                                <div class="skill-page active">
                                    <div class="skill-card">
                                        <h4><i class="fas fa-lightbulb"></i> 集合三要素验证技巧</h4>
                                        <div class="skill-item">
                                            <h5><i class="fas fa-check-circle"></i> 确定性验证</h5>
                                            <p>判断标准是否明确：如"班上数学成绩90分以上的同学"是确定的，"学习好的同学"是不确定的。</p>
                                            <button class="audio-btn-small" onclick="playVoice('确定性是集合的基本性质。判断标准必须明确，如班上数学成绩90分以上的同学是确定的，而学习好的同学是不确定的。')">
                                                <i class="fas fa-volume-up"></i> 听讲解
                                            </button>
                                        </div>
                                        <div class="skill-item">
                                            <h5><i class="fas fa-check-circle"></i> 互异性处理</h5>
                                            <p>当集合中出现重复元素时，需自动去重：如{1,2,2,3}应简化为{1,2,3}。</p>
                                            <button class="audio-btn-small" onclick="playVoice('互异性是指集合中的任意两个元素都是不同的。当集合中出现重复元素时，需要自动去重，如{1,2,2,3}应简化为{1,2,3}。')">
                                                <i class="fas fa-volume-up"></i> 听讲解
                                            </button>
                                        </div>
                                        <div class="skill-item">
                                            <h5><i class="fas fa-check-circle"></i> 无序性应用</h5>
                                            <p>比较集合时，元素顺序不影响集合相等性：如{1,2,3}和{3,2,1}表示同一集合。</p>
                                            <button class="audio-btn-small" onclick="playVoice('无序性是指集合中元素的排列顺序不重要。比较集合时，元素顺序不影响集合相等性，如{1,2,3}和{3,2,1}表示同一集合。')">
                                                <i class="fas fa-volume-up"></i> 听讲解
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="skill-page">
                                    <div class="skill-card">
                                        <h4><i class="fas fa-pencil-alt"></i> 集合表示法转换技巧</h4>
                                        <div class="skill-item">
                                            <h5><i class="fas fa-arrow-right"></i> 列举法转描述法</h5>
                                            <p>找出元素的共同特征：如{2,4,6,8} → {x | x是偶数, 0<x<10}</p>
                                            <button class="audio-btn-small" onclick="playVoice('列举法转描述法的关键是找出元素的共同特征。比如集合{2,4,6,8}，我们观察发现这些都是偶数，且都小于10，所以可以用描述法表示为{x | x是偶数, 0<x<10}。')">
                                                <i class="fas fa-volume-up"></i> 听讲解
                                            </button>
                                        </div>
                                        <div class="skill-item">
                                            <h5><i class="fas fa-arrow-left"></i> 描述法转列举法</h5>
                                            <p>根据条件列举所有元素：如{x | x²<10, x∈Z} → {-3,-2,-1,0,1,2,3}</p>
                                            <button class="audio-btn-small" onclick="playVoice('描述法转列举法需要根据条件找出所有满足条件的元素。比如{x | x²<10, x∈Z}，我们需要找出所有平方小于10的整数。因为3²=9<10，4²=16>10，所以x的范围是-3到3，即{-3,-2,-1,0,1,2,3}。')">
                                                <i class="fas fa-volume-up"></i> 听讲解
                                            </button>
                                        </div>
                                        <div class="skill-item">
                                            <h5><i class="fas fa-exchange-alt"></i> 特殊集合表示</h5>
                                            <p>空集：∅ 或 {}；实数集：R；整数集：Z；自然数集：N；有理数集：Q</p>
                                            <button class="audio-btn-small" onclick="playVoice('特殊集合的表示方法需要记住。空集用∅或{}表示，表示不包含任何元素的集合。实数集用R表示，整数集用Z表示，自然数集用N表示，有理数集用Q表示。这些都是数学中的标准记号。')">
                                                <i class="fas fa-volume-up"></i> 听讲解
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="skill-page">
                                    <div class="skill-card">
                                        <h4><i class="fas fa-users"></i> 元素与集合关系判断</h4>
                                        <div class="skill-item">
                                            <h5><i class="fas fa-check"></i> 属于关系 (∈)</h5>
                                            <p>若元素a在集合A中，则a∈A：如3∈{1,2,3,4}</p>
                                            <button class="audio-btn-small" onclick="playVoice('属于关系用符号∈表示。如果元素a在集合A中，我们就说a属于A，记作a∈A。比如3∈{1,2,3,4}，表示3属于集合{1,2,3,4}。')">
                                                <i class="fas fa-volume-up"></i> 听讲解
                                            </button>
                                        </div>
                                        <div class="skill-item">
                                            <h5><i class="fas fa-times"></i> 不属于关系 (∉)</h5>
                                            <p>若元素a不在集合A中，则a∉A：如5∉{1,2,3,4}</p>
                                            <button class="audio-btn-small" onclick="playVoice('不属于关系用符号∉表示。如果元素a不在集合A中，我们就说a不属于A，记作a∉A。比如5∉{1,2,3,4}，表示5不属于集合{1,2,3,4}。')">
                                                <i class="fas fa-volume-up"></i> 听讲解
                                            </button>
                                        </div>
                                        <div class="skill-item">
                                            <h5><i class="fas fa-exclamation-triangle"></i> 常见错误</h5>
                                            <p>混淆元素与集合：如a∈{a}正确，{a}∈{{a}}正确，但a∈{{a}}错误</p>
                                            <button class="audio-btn-small" onclick="playVoice('在使用属于关系时，要注意区分元素和集合。a∈{a}是正确的，表示元素a属于集合{a}。{a}∈{{a}}也是正确的，表示集合{a}属于集合{{a}}。但是a∈{{a}}是错误的，因为a是元素，而{{a}}的元素是集合{a}，不是元素a。')">
                                                <i class="fas fa-volume-up"></i> 听讲解
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="pagination-controls" id="skills-pagination"></div>
                        </div>
                    </div>
                </div>
                
                <div class="content-module" id="application-module">
                    <div class="card">
                        <div class="card-header">
                            <h2><i class="fas fa-rocket"></i> 题型高手</h2>
                            <div class="page-indicator">
                                第 <span id="current-app-page">1</span> / <span id="total-app-pages">4</span> 页
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="application-page active" id="app-page-1">
                                <div class="application-section">
                                    <h3>集合在概率问题中的应用</h3>
                                    
                                    <div class="application-card">
                                        <h4><i class="fas fa-dice"></i> 概率问题求解</h4>
                                        <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                                           <button class="btn btn-outline btn-sm" onclick="playVoice('在概率问题中，集合常用于表示事件。集合运算对应事件的运算，如并集表示事件的或运算，交集表示事件的且运算。比如掷骰子，事件A是得到偶数，事件B是得到大于3的数，那么A∪B表示得到偶数或大于3的数。')"><i class="fas fa-headphones"></i> 听讲解</button>
                                           <button class="btn btn-outline btn-sm"><i class="fas fa-film"></i> 动画分析</button>
                                        </div>
                                        <p>在概率问题中，集合常用于表示事件，集合运算对应事件的运算：</p>
                                        <div class="examples">
                                            <p><strong>问题：</strong>某班有50名学生，其中30人喜欢数学，25人喜欢物理，10人既喜欢数学又喜欢物理。随机抽取一名学生：</p>
                                            <p>1. 求该生喜欢数学或物理的概率</p>
                                            <p>2. 求该生只喜欢数学的概率</p>
                                            
                                            <p><strong>解答：</strong></p>
                                            <p>设A={喜欢数学的学生}, B={喜欢物理的学生}</p>
                                            <p>|A|=30, |B|=25, |A∩B|=10</p>
                                            <p>1. P(A∪B) = |A∪B|/50 = (30+25-10)/50 = 45/50 = 0.9</p>
                                            <p>2. P(只喜欢数学) = |A - B|/50 = (30-10)/50 = 20/50 = 0.4</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="application-section">
                                    <h3>集合在统计中的应用</h3>
                                    
                                    <div class="application-card">
                                        <h4><i class="fas fa-chart-pie"></i> 调查数据分析</h4>
                                        <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                                           <button class="btn btn-outline btn-sm" onclick="playVoice('在调查问卷分析中，集合可以表示不同特征的群体。比如调查学生的兴趣爱好，可以用集合A表示喜欢数学的学生，集合B表示喜欢物理的学生。通过集合运算可以分析数据，如A∩B表示既喜欢数学又喜欢物理的学生。')"><i class="fas fa-headphones"></i> 听讲解</button>
                                           <button class="btn btn-outline btn-sm"><i class="fas fa-film"></i> 动画分析</button>
                                        </div>
                                        <p>在调查问卷分析中，集合可表示不同特征的群体：</p>
                                        <div class="examples">
                                            <p><strong>问题：</strong>某学校对500名学生进行调查，发现：</p>
                                            <ul style="padding-left: 20px; margin: 10px 0;">
                                                <li>320人参加数学竞赛</li>
                                                <li>280人参加物理竞赛</li>
                                                <li>150人同时参加两项竞赛</li>
                                            </ul>
                                            <p>求只参加一项竞赛的学生人数</p>
                                            
                                            <p><strong>解答：</strong></p>
                                            <p>设M={参加数学竞赛}, P={参加物理竞赛}</p>
                                            <p>|M|=320, |P|=280, |M∩P|=150</p>
                                            <p>只参加数学: |M - P| = 320-150 = 170</p>
                                            <p>只参加物理: |P - M| = 280-150 = 130</p>
                                            <p>只参加一项: 170+130 = 300人</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="application-page" id="app-page-2">
                                <div class="application-section">
                                    <h3>集合在生活中的应用</h3>
                                    
                                    <div class="application-card">
                                        <h4><i class="fas fa-users"></i> 班级学生管理</h4>
                                        <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                                           <button class="btn btn-outline btn-sm" onclick="playVoice('班主任可以用集合来管理学生信息。比如用集合A表示成绩优秀的学生，集合B表示积极参加活动的学生，集合C表示需要重点关注的学生。通过集合运算可以更好地了解学生情况，制定针对性的教育方案。')"><i class="fas fa-headphones"></i> 听讲解</button>
                                           <button class="btn btn-outline btn-sm"><i class="fas fa-film"></i> 动画分析</button>
                                        </div>
                                        <p>班主任可以用集合管理学生信息：</p>
                                        <div class="examples">
                                            <p>设高一(1)班全体学生为全集U</p>
                                            <p>A = {男生}, B = {团员}, C = {数学课代表}</p>
                                            
                                            <p>常见查询：</p>
                                            <ul style="padding-left: 20px; margin: 10px 0;">
                                                <li>非团员男生：A ∩ B'</li>
                                                <li>女生团员：A' ∩ B</li>
                                                <li>数学课代表中的男生：A ∩ C</li>
                                            </ul>
                                        </div>
                                    </div>
                                    
                                    <div class="application-card">
                                        <h4><i class="fas fa-book"></i> 图书馆书籍分类</h4>
                                        <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                                           <button class="btn btn-outline btn-sm" onclick="playVoice('图书馆使用集合对书籍进行分类管理。比如用集合A表示文学类书籍，集合B表示科技类书籍，集合C表示教育类书籍。通过集合运算可以快速查找和统计不同类别的书籍，提高管理效率。')"><i class="fas fa-headphones"></i> 听讲解</button>
                                           <button class="btn btn-outline btn-sm"><i class="fas fa-film"></i> 动画分析</button>
                                        </div>
                                        <p>图书馆使用集合对书籍进行分类管理：</p>
                                        <div class="examples">
                                            <p>设全集U为图书馆所有书籍</p>
                                            <p>A = {数学类}, B = {文学类}, C = {2020年后出版}</p>
                                            
                                            <p>常见查询：</p>
                                            <ul style="padding-left: 20px; margin: 10px 0;">
                                                <li>数学类新书：A ∩ C</li>
                                                <li>非文学类书籍：B'</li>
                                                <li>2020年前出版的文学书：B ∩ C'</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="application-page" id="app-page-3">
                                <div class="application-section">
                                    <h3>集合在逻辑推理中的应用</h3>
                                    
                                    <div class="application-card">
                                        <h4><i class="fas fa-brain"></i> 逻辑推理问题</h4>
                                        <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                                           <button class="btn btn-outline btn-sm" onclick="playVoice('使用集合可以解决复杂的逻辑推理问题。通过将条件转化为集合，利用集合的交集、并集、补集等运算，可以清晰地分析逻辑关系，得出正确的结论。这种方法在解决复杂推理题时特别有效。')"><i class="fas fa-headphones"></i> 听讲解</button>
                                           <button class="btn btn-outline btn-sm"><i class="fas fa-film"></i> 动画分析</button>
                                        </div>
                                        <p>使用集合解决复杂的逻辑推理问题：</p>
                                        <div class="examples">
                                            <p><strong>问题：</strong>某次考试有数学、物理、化学三门科目。参加考试的学生中：</p>
                                            <ul style="padding-left: 20px; margin: 10px 0;">
                                                <li>90人至少通过一门科目</li>
                                                <li>通过数学的45人</li>
                                                <li>通过物理的40人</li>
                                                <li>通过化学的35人</li>
                                                <li>同时通过数学和物理的15人</li>
                                                <li>同时通过数学和化学的12人</li>
                                                <li>同时通过物理和化学的10人</li>
                                                <li>三门都通过的5人</li>
                                            </ul>
                                            <p>求只通过一门科目的学生人数。</p>
                                            
                                            <p><strong>解答：</strong></p>
                                            <p>使用容斥原理计算：</p>
                                            <p>设M={通过数学}, P={通过物理}, C={通过化学}</p>
                                            <p>|M∪P∪C| = |M| + |P| + |C| - |M∩P| - |M∩C| - |P∩C| + |M∩P∩C|</p>
                                            <p>90 = 45 + 40 + 35 - 15 - 12 - 10 + 5</p>
                                            <p>只通过一门 = (|M| - |M∩P| - |M∩C| + |M∩P∩C|) + 
                                               (|P| - |M∩P| - |P∩C| + |M∩P∩C|) + 
                                               (|C| - |M∩C| - |P∩C| + |M∩P∩C|)</p>
                                            <p>计算得：只通过一门 = (45-15-12+5) + (40-15-10+5) + (35-12-10+5) = 23 + 20 + 18 = 61人</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="application-page" id="app-page-4">
                                <div class="application-section">
                                    <h3>集合在实际问题中的综合应用</h3>
                                    
                                    <div class="application-card">
                                        <h4><i class="fas fa-shopping-cart"></i> 购物决策问题</h4>
                                        <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                                           <button class="btn btn-outline btn-sm" onclick="playVoice('使用集合可以优化购物决策。比如用集合A表示需要购买的商品，集合B表示打折的商品，集合C表示库存充足的商品。通过A∩B∩C可以找到既需要又打折且有库存的商品，帮助做出最优的购物选择。')"><i class="fas fa-headphones"></i> 听讲解</button>
                                           <button class="btn btn-outline btn-sm"><i class="fas fa-film"></i> 动画分析</button>
                                        </div>
                                        <p>使用集合优化购物决策：</p>
                                        <div class="examples">
                                            <p><strong>问题：</strong>某超市商品促销，顾客需从以下商品中选择：</p>
                                            <ul style="padding-left: 20px; margin: 10px 0;">
                                                <li>A类商品：牛奶、面包、鸡蛋</li>
                                                <li>B类商品：苹果、香蕉、橙子</li>
                                                <li>C类商品：牛肉、鸡肉、鱼肉</li>
                                            </ul>
                                            <p>促销规则：</p>
                                            <ul style="padding-left: 20px; margin: 10px 0;">
                                                <li>购买任意2类商品可享9折</li>
                                                <li>购买全部3类商品可享8折</li>
                                                <li>每类商品至少选一种</li>
                                            </ul>
                                            <p>若顾客想购买牛奶、苹果、牛肉三种商品，应如何组合购买最优惠？</p>
                                            
                                            <p><strong>解答：</strong></p>
                                            <p>设购买方案为集合：</p>
                                            <p>方案1：{牛奶, 苹果} - 属于A和B类</p>
                                            <p>方案2：{牛奶, 牛肉} - 属于A和C类</p>
                                            <p>方案3：{苹果, 牛肉} - 属于B和C类</p>
                                            <p>方案4：{牛奶, 苹果, 牛肉} - 属于A、B和C类</p>
                                            <p>根据规则，方案4享受8折优惠，是最优惠的选择。</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="pagination-controls" id="application-pagination">
                                <div class="pagination-arrow prev-btn">
                                    <i class="fas fa-chevron-left"></i>
                                </div>
                                <div class="page-btn active" data-page="1">1</div>
                                <div class="page-btn" data-page="2">2</div>
                                <div class="page-btn" data-page="3">3</div>
                                <div class="page-btn" data-page="4">4</div>
                                <div class="pagination-arrow next-btn">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="content-module" id="practice-module">
                    <div class="card">
                        <div class="card-header">
                            <div style="display: flex; align-items: center; gap: 20px;">
                                <h2><i class="fas fa-pencil-alt"></i> 动手练习</h2>
                                <span class="exercise-progress" style="color: var(--gray); font-size: 16px; font-weight: 500;">
                                    进度: <span id="completed-count">0</span> / <span id="total-count">100</span>
                                </span>
                            </div>
                            <div>
                                <button class="btn btn-outline">
                                    <i class="fas fa-redo"></i> 换一组题目
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="difficulty-selector">
                                <div class="difficulty-btn active">基础</div>
                                <div class="difficulty-btn">中等</div>
                                <div class="difficulty-btn">困难</div>
                            </div>
                            
                            <div id="practice-pages-container">
                                <div class="practice-page active">
                                    <div class="exercise">
                                        <h4><i class="fas fa-question-circle"></i> 选择题 1</h4>
                                        <div class="exercise-content">
                                            <p>下列各组对象能构成集合的是？</p>
                                            <div class="options">
                                                <div class="option">
                                                    <input type="radio" name="q1" id="q1a">
                                                    <label for="q1a">A. 好看的手机</label>
                                                </div>
                                                <div class="option">
                                                    <input type="radio" name="q1" id="q1b">
                                                    <label for="q1b">B. 小于5的自然数</label>
                                                </div>
                                                <div class="option">
                                                    <input type="radio" name="q1" id="q1c">
                                                    <label for="q1c">C. 数学成绩好的同学</label>
                                                </div>
                                                <div class="option">
                                                    <input type="radio" name="q1" id="q1d">
                                                    <label for="q1d">D. 跑得快的人</label>
                                                </div>
                                            </div>
                                            <button class="submit-btn" style="margin: 20px 0 0 0;">检查答案</button>
                                            <div class="feedback-box">
                                                <h4><i class="fas fa-comment-dots"></i> 解析与反馈</h4>
                                                <p><strong>正确答案：B</strong>。只有B选项满足集合的确定性要求。"小于5的自然数"是明确的，而其他选项的标准模糊。</p>
                                                <div style="display: flex; gap: 10px; margin-top: 15px;">
                                                   <button class="btn btn-outline btn-sm" onclick="playVoice('正确答案是B。只有B选项小于5的自然数满足集合的确定性要求。小于5的自然数是明确的，包括0、1、2、3、4这五个数。而其他选项如好看的手机、跑得快的人等标准都比较模糊，不符合集合元素的确定性要求。')"><i class="fas fa-headphones"></i> 听讲解</button>
                                                   <button class="btn btn-outline btn-sm"><i class="fas fa-film"></i> 动画分析</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="exercise">
                                        <h4><i class="fas fa-question-circle"></i> 选择题 2</h4>
                                        <div class="exercise-content">
                                            <p>用列举法表示集合 {x | x²-4=0}，正确的是？</p>
                                            <div class="options">
                                                <div class="option">
                                                    <input type="radio" name="q2" id="q2a">
                                                    <label for="q2a">A. {2}</label>
                                                </div>
                                                <div class="option">
                                                    <input type="radio" name="q2" id="q2b">
                                                    <label for="q2b">B. {-2}</label>
                                                </div>
                                                <div class="option">
                                                    <input type="radio" name="q2" id="q2c">
                                                    <label for="q2c">C. {2, -2}</label>
                                                </div>
                                                <div class="option">
                                                    <input type="radio" name="q2" id="q2d">
                                                    <label for="q2d">D. {x=2, x=-2}</label>
                                                </div>
                                            </div>
                                            <button class="submit-btn" style="margin: 20px 0 0 0;">检查答案</button>
                                            <div class="feedback-box">
                                                <h4><i class="fas fa-comment-dots"></i> 解析与反馈</h4>
                                                <p><strong>正确答案：C</strong>。方程 x²-4=0 的解为 x=2 或 x=-2。用列举法表示包含这两个元素的集合应为 {2, -2}。</p>
                                                <div style="display: flex; gap: 10px; margin-top: 15px;">
                                                   <button class="btn btn-outline btn-sm" onclick="playVoice('正确答案是C。首先我们来解方程x²-4=0。这个方程可以因式分解为(x-2)(x+2)=0，所以x=2或x=-2。用列举法表示这个方程的解集，就是把所有的解都列出来，应该是{2, -2}。注意集合中元素的顺序不重要，所以{2, -2}和{-2, 2}是同一个集合。')"><i class="fas fa-headphones"></i> 听讲解</button>
                                                   <button class="btn btn-outline btn-sm"><i class="fas fa-film"></i> 动画分析</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="practice-page">
                                    <div class="exercise">
                                        <h4><i class="fas fa-question-circle"></i> 填空题 1</h4>
                                        <div class="exercise-content">
                                            <p>用描述法表示小于10的正奇数集合：</p>
                                            <input type="text" class="answer-input" placeholder="{x | ...}">
                                            <button class="submit-btn" style="margin: 20px 0 0 0;">检查答案</button>
                                            <div class="feedback-box">
                                                <h4><i class="fas fa-comment-dots"></i> 解析与反馈</h4>
                                                <p><strong>参考答案：</strong>{x | x = 2k-1, k∈N*, 且k≤5} 或 {x | x为正奇数, 且x<10}。</p>
                                                <div style="display: flex; gap: 10px; margin-top: 15px;">
                                                   <button class="btn btn-outline btn-sm" onclick="playVoice('小于10的正奇数有1、3、5、7、9这五个数。用描述法表示时，我们需要找出这些数的共同特征。第一种表示方法是{x | x = 2k-1, k∈N*, 且k≤5}，这里用了奇数的通项公式2k-1。第二种更直观的表示方法是{x | x为正奇数, 且x<10}。两种表示方法都是正确的。')"><i class="fas fa-headphones"></i> 听讲解</button>
                                                   <button class="btn btn-outline btn-sm"><i class="fas fa-film"></i> 动画分析</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                     <div class="exercise">
                                        <h4><i class="fas fa-question-circle"></i> 填空题 2</h4>
                                        <div class="exercise-content">
                                            <p>集合 {a, b, c} 的子集个数为：</p>
                                            <input type="text" class="answer-input" placeholder="输入数字">
                                            <button class="submit-btn" style="margin: 20px 0 0 0;">检查答案</button>
                                            <div class="feedback-box">
                                                <h4><i class="fas fa-comment-dots"></i> 解析与反馈</h4>
                                                <p><strong>正确答案：8</strong>。对于含有n个元素的有限集，其子集的个数为 2ⁿ。此集合有3个元素，所以子集个数为 2³=8。</p>
                                                <div style="display: flex; gap: 10px; margin-top: 15px;">
                                                   <button class="btn btn-outline btn-sm" onclick="playVoice('正确答案是8。这里用到一个重要公式：对于含有n个元素的有限集，其子集的个数为2的n次方。集合{a, b, c}有3个元素，所以子集个数为2³=8。这8个子集分别是：空集、{a}、{b}、{c}、{a,b}、{a,c}、{b,c}、{a,b,c}。')"><i class="fas fa-headphones"></i> 听讲解</button>
                                                   <button class="btn btn-outline btn-sm"><i class="fas fa-film"></i> 动画分析</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="practice-page">
                                    <div class="exercise">
                                        <h4><i class="fas fa-star"></i> 证明题</h4>
                                        <div class="exercise-content">
                                            <p>证明：集合 A = {x | x = 2k, k∈Z}（偶数集）和集合 B = {x | x = 2k+1, k∈Z}（奇数集）的交集为空集。</p>
                                            <textarea class="answer-input" placeholder="写下你的证明过程..." rows="4"></textarea>
                                            
                                            <div class="upload-area">
                                                <i class="fas fa-cloud-upload-alt"></i>
                                                <h4>上传手写证明</h4>
                                                <p>拖放文件到此处或点击上传</p>
                                                <p class="small">支持格式：JPG, PNG, PDF（最大10MB）</p>
                                                <button class="btn btn-outline" style="margin-top: 15px;">
                                                    <i class="fas fa-file-upload"></i> 选择文件
                                                </button>
                                            </div>
                                            <button class="submit-btn" style="margin: 20px 0 0 0;">检查答案</button>
                                            <div class="feedback-box">
                                                <h4><i class="fas fa-comment-dots"></i> 解析与反馈</h4>
                                                <p><strong>证明思路：</strong>使用反证法。假设存在元素c同时属于A和B，那么c既是偶数又是奇数。可得 c=2m 且 c=2n+1（m,n为整数），导出 2m=2n+1，即 2(m-n)=1。由于m,n为整数，m-n也为整数，2(m-n)必为偶数，而1是奇数，偶数=奇数产生矛盾。故假设不成立，A∩B=∅。</p>
                                                <div style="display: flex; gap: 10px; margin-top: 15px;">
                                                   <button class="btn btn-outline btn-sm" onclick="playVoice('这道题要用反证法来证明。我们假设存在一个元素c，它既属于集合A又属于集合B。那么根据集合A的定义，c是偶数，可以写成c=2m的形式，其中m是整数。同时根据集合B的定义，c是奇数，可以写成c=2n+1的形式，其中n是整数。这样我们就得到了2m=2n+1，整理后得到2(m-n)=1。但是左边2(m-n)是偶数，右边1是奇数，偶数不可能等于奇数，这就产生了矛盾。因此我们的假设不成立，所以A∩B=∅。')"><i class="fas fa-headphones"></i> 听讲解</button>
                                                   <button class="btn btn-outline btn-sm"><i class="fas fa-film"></i> 动画分析</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="pagination-controls" id="practice-pagination"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="sidebar-content">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-bullseye"></i> 本节学习目标</h2>
                    </div>
                    <div class="card-body">
                        <ul class="objective-list">
                            <li>
                                <div class="objective-icon">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div>
                                    <strong>理解集合三要素</strong>
                                    <p>掌握确定性、互异性、无序性</p>
                                </div>
                            </li>
                            <li>
                                <div class="objective-icon">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div>
                                    <strong>掌握两种表示法</strong>
                                    <p>熟练使用列举法和描述法</p>
                                </div>
                            </li>
                            <li>
                                <div class="objective-icon">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div>
                                    <strong>辨析元素与集合关系</strong>
                                    <p>正确使用 ∈ 和 ∉ 符号</p>
                                </div>
                            </li>
                            <li>
                                <div class="objective-icon">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div>
                                    <strong>应用集合概念解题</strong>
                                    <p>解决相关数学问题</p>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-tools"></i> 学习工具箱</h2>
                    </div>
                    <div class="card-body">
                        <div class="tools-grid">
                            <div class="tool-card" onclick="openMindmapModal()">
                                <i class="fas fa-sitemap"></i>
                                <h4>概念图谱</h4>
                                <p>可视化知识关联</p>
                            </div>
                            <div class="tool-card">
                                <i class="fas fa-book"></i>
                                <h4>错题本</h4>
                                <p>自动收集练习错题</p>
                            </div>
                            <div class="tool-card">
                                <i class="fas fa-calculator"></i>
                                <h4>公式手册</h4>
                                <p>常用公式查询</p>
                            </div>
                            <div class="tool-card">
                                <i class="fas fa-video"></i>
                                <h4>视频精讲</h4>
                                <p>名师知识点讲解</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-chart-line"></i> 学习进度跟踪</h2>
                    </div>
                    <div class="card-body">
                        <div class="progress-container">
                            <div class="progress-title">
                                <i class="fas fa-tachometer-alt"></i>
                                <h3>当前章节掌握度</h3>
                            </div>
                            
                            <div class="progress-circle">
                                <svg viewBox="0 0 100 100">
                                    <circle class="progress-bg" cx="50" cy="50" r="45"></circle>
                                    <circle class="progress-bar" cx="50" cy="50" r="45" 
                                            stroke-dasharray="283" stroke-dashoffset="113.2"></circle>
                                </svg>
                                <div class="progress-value">60%</div>
                            </div>
                            
                            <p>继续完成本节练习可提高掌握度</p>
                            <button class="btn btn-primary" style="margin-top: 15px;">
                                <i class="fas fa-forward"></i> 继续学习
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 学习路径导航交互
        const pathSteps = document.querySelectorAll('.path-step');
        
        pathSteps.forEach(step => {
            step.addEventListener('click', function() {
                pathSteps.forEach(s => s.classList.remove('active'));
                this.classList.add('active');
                document.querySelectorAll('.content-module').forEach(m => {
                    m.classList.remove('active');
                });
                const target = this.getAttribute('data-target');
                document.getElementById(`${target}-module`).classList.add('active');
            });
        });
        
        // 选择题交互
        const options = document.querySelectorAll('.option');
        options.forEach(option => {
            option.addEventListener('click', function() {
                const radio = option.querySelector('input[type="radio"]');
                const questionName = radio.getAttribute('name');
                
                // Unselect other options in the same question
                document.querySelectorAll(`input[name="${questionName}"]`).forEach(r => {
                    r.closest('.option').classList.remove('selected');
                });
                
                this.classList.add('selected');
                radio.checked = true;
            });
        });
        
        // 练习提交按钮交互
        document.querySelectorAll('#practice-module .submit-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const feedbackBox = this.closest('.exercise-content').querySelector('.feedback-box');
                if (feedbackBox) {
                    const isShown = feedbackBox.classList.toggle('show');
                    this.textContent = isShown ? '隐藏答案' : '检查答案';
                }
            });
        });
        
        // 集合元素交互
        const setElements = document.querySelectorAll('.set-element');
        setElements.forEach(element => {
            element.addEventListener('click', function() {
                alert(`元素 ${this.textContent} 属于集合\n属于关系: ${this.textContent} ∈ A`);
            });
        });
        
        // 难度选择交互
        const difficultyBtns = document.querySelectorAll('.difficulty-btn');
        difficultyBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                difficultyBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 教材选择下拉菜单交互
        const textbookMenu = document.querySelector('.menu-item.has-submenu');
        if (textbookMenu) {
            textbookMenu.addEventListener('click', function() {
                this.classList.toggle('active');
                const submenu = this.nextElementSibling;
                if (submenu && submenu.classList.contains('submenu')) {
                    submenu.classList.toggle('show');
                }
            });
        }
        
        // 通用分页功能函数
        function setupPagination(containerId, pageClass, paginationControlsId) {
            const container = document.getElementById(containerId);
            if (!container) return;
            
            const pages = Array.from(container.querySelectorAll(`.${pageClass}`));
            if (pages.length <= 1) return;

            const controlsContainer = document.getElementById(paginationControlsId);
            if (!controlsContainer) return;

            let paginationHTML = `
                <div class="pagination-arrow prev-btn disabled">
                    <i class="fas fa-chevron-left"></i>
                </div>
            `;
            for (let i = 1; i <= pages.length; i++) {
                paginationHTML += `<div class="page-btn ${i === 1 ? 'active' : ''}" data-page="${i}">${i}</div>`;
            }
            paginationHTML += `
                <div class="pagination-arrow next-btn">
                    <i class="fas fa-chevron-right"></i>
                </div>
            `;
            controlsContainer.innerHTML = paginationHTML;

            const pageBtns = controlsContainer.querySelectorAll('.page-btn');
            const prevBtn = controlsContainer.querySelector('.prev-btn');
            const nextBtn = controlsContainer.querySelector('.next-btn');
            let currentPage = 1;

            function updateDisplay() {
                pages.forEach((page, index) => {
                    page.style.display = (index + 1) === currentPage ? 'block' : 'none';
                    if ((index + 1) === currentPage) {
                        page.classList.add('active');
                    } else {
                        page.classList.remove('active');
                    }
                });
                pageBtns.forEach(btn => {
                    btn.classList.toggle('active', parseInt(btn.dataset.page) === currentPage);
                });
                
                prevBtn.classList.toggle('disabled', currentPage === 1);
                nextBtn.classList.toggle('disabled', currentPage === pages.length);
            }
            
            pageBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    currentPage = parseInt(btn.dataset.page);
                    updateDisplay();
                });
            });

            prevBtn.addEventListener('click', () => {
                if (currentPage > 1) {
                    currentPage--;
                    updateDisplay();
                }
            });

            nextBtn.addEventListener('click', () => {
                if (currentPage < pages.length) {
                    currentPage++;
                    updateDisplay();
                }
            });
            
            updateDisplay(); // 初始化
        }
        
        // 题型高手分页功能
        function setupApplicationPagination() {
            const appPages = document.querySelectorAll('.application-page');
            const pageBtns = document.querySelectorAll('#application-pagination .page-btn');
            const prevBtn = document.querySelector('#application-pagination .prev-btn');
            const nextBtn = document.querySelector('#application-pagination .next-btn');
            const currentPageEl = document.getElementById('current-app-page');
            const totalPages = appPages.length;
            
            let currentPage = 1;
        
            function updatePageDisplay() {
                appPages.forEach(page => page.classList.remove('active'));
                document.getElementById(`app-page-${currentPage}`).classList.add('active');
                
                pageBtns.forEach(btn => btn.classList.remove('active'));
                document.querySelector(`#application-pagination .page-btn[data-page="${currentPage}"]`).classList.add('active');
                
                if(currentPageEl) currentPageEl.textContent = currentPage;
                
                prevBtn.classList.toggle('disabled', currentPage === 1);
                nextBtn.classList.toggle('disabled', currentPage === totalPages);
            }
        
            pageBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    currentPage = parseInt(this.getAttribute('data-page'));
                    updatePageDisplay();
                });
            });
            
            prevBtn.addEventListener('click', function() {
                if (currentPage > 1) {
                    currentPage--;
                    updatePageDisplay();
                }
            });
            
            nextBtn.addEventListener('click', function() {
                if (currentPage < totalPages) {
                    currentPage++;
                    updatePageDisplay();
                }
            });
        }

        // 示例翻页功能
        function setupExamplePagination() {
            const exampleContainers = document.querySelectorAll('.examples');
            exampleContainers.forEach(container => {
                const pages = container.querySelectorAll('.example-page');
                
                if (pages.length <= 1) {
                    if (pages.length === 1) {
                        pages[0].classList.add('active');
                    }
                    return;
                }

                const nav = document.createElement('div');
                nav.className = 'example-nav';
                nav.innerHTML = `
                    <i class="fas fa-chevron-left prev-example-btn"></i>
                    <span class="page-info">1 / ${pages.length}</span>
                    <i class="fas fa-chevron-right next-example-btn"></i>
                `;
                container.appendChild(nav);
                
                let currentPage = 0;
                const pageInfo = nav.querySelector('.page-info');
                const prevBtn = nav.querySelector('.prev-example-btn');
                const nextBtn = nav.querySelector('.next-example-btn');

                const updateDisplay = () => {
                    pages.forEach((page, idx) => {
                        page.classList.toggle('active', idx === currentPage);
                    });
                    pageInfo.textContent = `${currentPage + 1} / ${pages.length}`;
                    prevBtn.classList.toggle('disabled', currentPage === 0);
                    nextBtn.classList.toggle('disabled', currentPage === pages.length - 1);
                };

                prevBtn.addEventListener('click', () => {
                    if (currentPage > 0) {
                        currentPage--;
                        updateDisplay();
                    }
                });

                nextBtn.addEventListener('click', () => {
                    if (currentPage < pages.length - 1) {
                        currentPage++;
                        updateDisplay();
                    }
                });
                
                updateDisplay();
            });
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            const progressBar = document.querySelector('.progress-bar');
            progressBar.style.strokeDashoffset = "113.2"; // 60%
            
            document.querySelector('.path-step[data-target="concept"]').classList.add('active');
            document.getElementById('concept-module').classList.add('active');
            
            // 初始化各种分页
            setupApplicationPagination();
            setupExamplePagination();
            setupPagination('skills-pages-container', 'skill-page', 'skills-pagination');
            setupPagination('practice-pages-container', 'practice-page', 'practice-pagination');
        });

        // 脑图弹窗功能
        function openMindmapModal() {
            const modal = document.getElementById('mindmap-modal');
            modal.classList.add('show');

            // 生成脑图
            generateMindmapInModal();
        }

        function closeMindmapModal() {
            const modal = document.getElementById('mindmap-modal');
            modal.classList.remove('show');
        }

        async function generateMindmapInModal() {
            const container = document.getElementById('mindmap-display');

            // 显示加载状态
            container.innerHTML = `
                <div style="display: flex; justify-content: center; align-items: center; height: 100%; color: var(--primary);">
                    <div style="text-align: center;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 48px; margin-bottom: 16px;"></i>
                        <h3>正在生成概念脑图...</h3>
                        <p>正在为您创建专业的思维导图</p>
                    </div>
                </div>
            `;

            try {
                // 使用markmap直接生成脑图
                setTimeout(() => {
                    renderMarkmapInModal();
                }, 1000); // 模拟加载时间
            } catch (error) {
                console.error('脑图生成失败:', error);
                container.innerHTML = `
                    <div style="display: flex; justify-content: center; align-items: center; height: 100%; color: var(--warning);">
                        <div style="text-align: center;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 16px;"></i>
                            <h3>脑图生成失败</h3>
                            <p>请稍后重试</p>
                            <button class="btn btn-primary" onclick="generateMindmapInModal()" style="margin-top: 16px;">
                                <i class="fas fa-redo"></i> 重新生成
                            </button>
                        </div>
                    </div>
                `;
            }
        }

        function renderMarkmapInModal() {
            const container = document.getElementById('mindmap-display');
            container.innerHTML = '';

            // 创建markmap容器
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.style.width = '100%';
            svg.style.height = '100%';
            svg.style.background = '#ffffff';
            container.appendChild(svg);

            // 集合概念的markdown数据
            const markdownData = `# 集合的概念

## 基本定义
- **集合**：具有某种特定性质的、确定的、互异的对象的全体
- **元素**：集合中的每个对象
- **表示方法**：集合用大写字母，元素用小写字母

## 三大特性

### 确定性
- 任何对象要么属于该集合，要么不属于该集合
- **正确示例**：身高超过180cm的学生
- **错误示例**：高个子学生（标准模糊）

### 互异性
- 集合中的元素互不相同
- **正确示例**：{1,2,3}
- **错误示例**：{1,1,2}（自动变为{1,2}）

### 无序性
- 集合中元素没有顺序关系
- **示例**：{a,b,c} = {b,c,a}

## 表示方法

### 列举法
- 将元素一一列举，用大括号{}括起来
- **示例1**：小于5的自然数 {0,1,2,3,4}
- **示例2**：英文字母元音 {a,e,i,o,u}

### 描述法
- 用元素的共同特征表示集合
- **示例1**：偶数集合 {x | x是整数, x能被2整除}
- **示例2**：正奇数集合 {x | x=2k-1, k∈N*}

## 元素与集合关系

### 属于关系 (∈)
- 若元素a在集合A中，则a∈A
- **示例**：3∈{1,2,3,4}

### 不属于关系 (∉)
- 若元素a不在集合A中，则a∉A
- **示例**：5∉{1,2,3,4}

## 常见错误
- 混淆元素与集合
- **正确**：a∈{a}，{a}∈{{a}}
- **错误**：a∈{{a}}`;

            try {
                // 等待markmap库加载
                if (typeof window.markmap === 'undefined') {
                    // 如果markmap未加载，使用简化的树形图
                    renderSimpleTreeMap(container, markdownData);
                    return;
                }

                const { Transformer } = window.markmap;
                const { Markmap, loadCSS, loadJS } = window.markmap;

                // 创建transformer
                const transformer = new Transformer();
                const { root, features } = transformer.transform(markdownData);

                // 加载必要的资源
                const { styles, scripts } = transformer.getUsedAssets(features);
                if (styles) loadCSS(styles);
                if (scripts) loadJS(scripts);

                // 创建markmap实例
                const mm = Markmap.create(svg, {
                    color: (node) => {
                        const colors = ['#4361ee', '#4895ef', '#4cc9f0', '#7209b7', '#560bad'];
                        return colors[node.depth % colors.length];
                    },
                    duration: 500,
                    maxWidth: 300,
                    spacingVertical: 8,
                    spacingHorizontal: 80,
                    paddingX: 8,
                    paddingY: 4,
                    fontSize: 14,
                    fontFamily: 'Microsoft YaHei, sans-serif'
                });

                mm.setData(root);
                mm.fit();
            } catch (error) {
                console.error('Markmap渲染失败:', error);
                container.innerHTML = `
                    <div style="display: flex; justify-content: center; align-items: center; height: 100%; color: var(--warning);">
                        <div style="text-align: center;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 16px;"></i>
                            <h3>脑图渲染失败</h3>
                            <p>Markmap库加载失败，请刷新页面重试</p>
                            <button class="btn btn-primary" onclick="generateMindmapInModal()" style="margin-top: 16px;">
                                <i class="fas fa-redo"></i> 重新生成
                            </button>
                        </div>
                    </div>
                `;
            }
        }

        // 简化的树形图渲染（备用方案）
        function renderSimpleTreeMap(container, markdownData) {
            container.innerHTML = `
                <div style="padding: 20px; height: 100%; overflow-y: auto; background: #ffffff;">
                    <div style="text-align: center; margin-bottom: 24px;">
                        <h3 style="color: #4361ee; margin: 0; font-size: 20px; font-weight: 600;">
                            <i class="fas fa-sitemap" style="margin-right: 8px;"></i>
                            集合的概念 - 知识脑图
                        </h3>
                        <p style="color: #666; font-size: 14px; margin-top: 8px;">简化版思维导图</p>
                    </div>

                    <div style="max-width: 800px; margin: 0 auto;">
                        <div style="text-align: center; margin-bottom: 30px;">
                            <div style="background: #4361ee; color: white; padding: 16px 24px; border-radius: 12px; font-size: 18px; font-weight: 600; display: inline-block; box-shadow: 0 4px 12px rgba(67,97,238,0.3);">
                                集合的概念
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; margin-bottom: 30px;">
                            <div style="text-align: center;">
                                <div style="background: #4895ef; color: white; padding: 12px 20px; border-radius: 8px; font-weight: 500; margin-bottom: 15px;">
                                    基本定义
                                </div>
                                <div style="background: #f8f9fa; border-radius: 8px; padding: 15px; text-align: left;">
                                    <div style="margin-bottom: 10px;"><strong>集合：</strong>具有某种特定性质的、确定的、互异的对象的全体</div>
                                    <div style="margin-bottom: 10px;"><strong>元素：</strong>集合中的每个对象</div>
                                    <div><strong>表示：</strong>集合用大写字母，元素用小写字母</div>
                                </div>
                            </div>

                            <div style="text-align: center;">
                                <div style="background: #4cc9f0; color: white; padding: 12px 20px; border-radius: 8px; font-weight: 500; margin-bottom: 15px;">
                                    三大特性
                                </div>
                                <div style="background: #f8f9fa; border-radius: 8px; padding: 15px; text-align: left;">
                                    <div style="margin-bottom: 10px;"><strong>确定性：</strong>要么属于，要么不属于</div>
                                    <div style="margin-bottom: 10px;"><strong>互异性：</strong>元素互不相同</div>
                                    <div><strong>无序性：</strong>元素没有顺序关系</div>
                                </div>
                            </div>

                            <div style="text-align: center;">
                                <div style="background: #7209b7; color: white; padding: 12px 20px; border-radius: 8px; font-weight: 500; margin-bottom: 15px;">
                                    表示方法
                                </div>
                                <div style="background: #f8f9fa; border-radius: 8px; padding: 15px; text-align: left;">
                                    <div style="margin-bottom: 10px;"><strong>列举法：</strong>用大括号{}列举元素</div>
                                    <div><strong>描述法：</strong>用元素的共同特征表示</div>
                                </div>
                            </div>
                        </div>

                        <div style="background: #f0f4ff; border-radius: 12px; padding: 20px; margin-top: 20px;">
                            <h4 style="color: #4361ee; margin-bottom: 15px; text-align: center;">
                                <i class="fas fa-lightbulb"></i> 典型示例
                            </h4>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                                <div style="background: white; border-radius: 8px; padding: 15px;">
                                    <strong style="color: #4361ee;">列举法示例：</strong><br>
                                    小于5的自然数：{0,1,2,3,4}<br>
                                    英文字母元音：{a,e,i,o,u}
                                </div>
                                <div style="background: white; border-radius: 8px; padding: 15px;">
                                    <strong style="color: #4361ee;">描述法示例：</strong><br>
                                    偶数集合：{x | x是整数, x能被2整除}<br>
                                    正奇数集合：{x | x=2k-1, k∈N*}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Manim动画控制函数
        function playManimAnimation() {
            const video = document.getElementById('set-animation-video');
            console.log('Playing video:', video);
            video.play().catch(e => console.error('Play failed:', e));
        }

        function pauseManimAnimation() {
            const video = document.getElementById('set-animation-video');
            video.pause();
        }

        function restartManimAnimation() {
            const video = document.getElementById('set-animation-video');
            video.currentTime = 0;
            video.play().catch(e => console.error('Restart failed:', e));
        }

        function fullscreenManimAnimation() {
            const video = document.getElementById('set-animation-video');
            if (video.requestFullscreen) {
                video.requestFullscreen();
            } else if (video.webkitRequestFullscreen) {
                video.webkitRequestFullscreen();
            } else if (video.msRequestFullscreen) {
                video.msRequestFullscreen();
            }
        }

        // 视频加载调试
        document.addEventListener('DOMContentLoaded', function() {
            const video = document.getElementById('set-animation-video');
            if (video) {
                video.addEventListener('loadstart', () => console.log('Video load started'));
                video.addEventListener('loadeddata', () => console.log('Video data loaded'));
                video.addEventListener('canplay', () => console.log('Video can play'));
                video.addEventListener('error', (e) => console.error('Video error:', e));
                console.log('Video element found:', video.src);
            }
        });

        // 点击弹窗外部关闭
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('mindmap-modal');
            if (e.target === modal) {
                closeMindmapModal();
            }
        });

        // ==================== MOSS-TTSD语音功能 ====================

        // ChatTTS语音配置
        let voiceConfig = {
            apiUrl: 'http://localhost:5001/api/voice/generate',
            voice: 'zh-female-teacher',
            speed: 1.0,
            volume: 0.8,
            content_type: 'concept',
            difficulty: 'medium'
        };

        // ChatTTS语音类型配置
        const voiceTypes = {
            'zh-female-teacher': '中文女教师 (ChatTTS优化)',
            'zh-male-teacher': '中文男教师 (ChatTTS)',
            'zh-female-sweet': '中文女声(甜美) (ChatTTS)',
            'zh-male-deep': '中文男声(深沉) (ChatTTS)',
            'zh-female-clear': '中文女声(清晰) (ChatTTS)',
            'zh-male-warm': '中文男声(温和) (ChatTTS)'
        };

        // ChatTTS语音类型说明（数学教学专用优化）
        const voiceTypeNotes = {
            'zh-female-teacher': 'ChatTTS数学教学专用优化版，150字/分钟教学语速，+2Hz音调强化重点',
            'zh-male-teacher': 'ChatTTS男教师语音，数学教学优化',
            'zh-female-sweet': 'ChatTTS甜美女声，适合轻松学习',
            'zh-male-deep': 'ChatTTS深沉男声，适合严肃讲解',
            'zh-female-clear': 'ChatTTS清晰女声，适合概念解释',
            'zh-male-warm': 'ChatTTS温和男声，适合耐心教学'
        };

        // 语音角色个性化介绍
        const voiceCharacterIntros = {
            'zh-female-teacher': '逗逗你好，我是你的数学老师小美老师！我专门为高中数学教学进行了优化，语速适中，发音清晰。我会用温和耐心的声音为你讲解每一个数学概念，让学习变得更轻松愉快。',
            'zh-male-teacher': '逗逗你好，我是张老师！作为一名经验丰富的数学教师，我会用沉稳专业的声音为你讲解数学知识。我的教学风格严谨而富有逻辑性，帮助你建立扎实的数学基础。',
            'zh-female-sweet': '逗逗你好呀，我是小雨！我是你的数学学习小助手，声音甜美活泼。我特别擅长用轻松有趣的方式讲解基础概念，让数学学习不再枯燥，而是充满乐趣！',
            'zh-male-deep': '逗逗你好，我是李教授。我拥有深厚的数学功底和丰富的教学经验，声音深沉而富有磁性。我专门负责讲解复杂的数学定理和高难度题目，帮助你攻克数学难关。',
            'zh-female-clear': '逗逗你好，我是小晴！我的声音清晰标准，发音准确。我特别适合为你讲解练习题和解题步骤，确保每一个细节都能被清楚地理解和掌握。',
            'zh-male-warm': '逗逗你好，我是王老师！我有着温和友善的声音，善于耐心细致地为你答疑解惑。无论你遇到什么数学问题，我都会用最温暖的声音为你提供帮助和指导。'
        };

        // 当前播放的音频对象
        let currentAudio = null;

        // 音频上下文初始化标志
        let audioContextInitialized = false;

        /**
         * 初始化音频上下文（需要用户交互）
         */
        function initializeAudioContext() {
            if (!audioContextInitialized) {
                // 创建一个静音音频来初始化音频上下文
                const silentAudio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                silentAudio.volume = 0;
                silentAudio.play().then(() => {
                    audioContextInitialized = true;
                    console.log('🔊 音频上下文已初始化');
                }).catch(() => {
                    // 静默处理初始化失败
                });
            }
        }

        // 页面加载时尝试初始化音频上下文
        document.addEventListener('DOMContentLoaded', () => {
            // 添加点击监听器来初始化音频
            document.addEventListener('click', initializeAudioContext, { once: true });
        });

        /**
         * 播放语音
         * @param {string} text - 要播放的文本
         * @param {Object} options - 语音选项
         */
        async function playVoice(text, options = {}) {
            try {
                // 停止当前播放的音频
                if (currentAudio) {
                    currentAudio.pause();
                    currentAudio = null;
                }

                console.log('🎤 开始语音合成:', text);

                // 合并配置
                const config = {
                    ...voiceConfig,
                    ...options
                };

                // 调用ChatTTS语音API
                const response = await fetch(config.apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: text,
                        voice: config.voice,
                        speed: config.speed,
                        volume: config.volume,  // 添加音量参数
                        content_type: config.content_type || 'concept',
                        difficulty: config.difficulty || 'medium',
                        use_smart_voice: false  // 明确禁用智能语音匹配，使用用户选择的语音类型
                    })
                });

                if (!response.ok) {
                    throw new Error(`语音API请求失败: ${response.status}`);
                }

                const data = await response.json();

                if (data.success && data.audio_url) {
                    // 创建音频对象并播放
                    const fullAudioUrl = `http://localhost:5001${data.audio_url}`;
                    currentAudio = new Audio(fullAudioUrl);
                    currentAudio.volume = config.volume;

                    // 显示语音控制面板
                    showVoiceControlPanel(text);

                    // 播放完成后清理
                    currentAudio.addEventListener('ended', () => {
                        currentAudio = null;
                        hideVoiceControlPanel();
                    });

                    // 添加时间更新监听器
                    currentAudio.addEventListener('timeupdate', updateVoiceProgress);
                    currentAudio.addEventListener('loadedmetadata', updateVoiceDuration);

                    // 播放音频
                    try {
                        await currentAudio.play();
                        console.log('🔊 语音播放开始');
                    } catch (playError) {
                        console.error('❌ 音频播放被阻止:', playError);

                        // 如果是用户交互问题，显示提示
                        if (playError.name === 'NotAllowedError') {
                            // 显示音频启用提示
                            showAudioEnablePrompt();

                            // 添加一次性点击监听器来启用音频
                            const enableAudio = () => {
                                currentAudio.play().then(() => {
                                    console.log('🔊 音频播放已启用');
                                    hideAudioEnablePrompt();
                                }).catch(e => console.error('音频播放失败:', e));
                            };
                            document.addEventListener('click', enableAudio, { once: true });
                        } else {
                            throw playError;
                        }
                    }
                } else {
                    throw new Error(data.error || '语音生成失败');
                }

            } catch (error) {
                console.error('❌ 语音播放错误:', error);

                // 显示用户友好的错误信息
                const errorMessage = error.message.includes('语音API')
                    ? '语音服务暂时不可用，请稍后再试'
                    : '语音播放失败，请检查网络连接';

                // 可以选择显示一个小提示而不是alert
                showVoiceError(errorMessage);
            }
        }

        /**
         * 停止当前语音播放
         */
        function stopVoice() {
            if (currentAudio) {
                currentAudio.pause();
                currentAudio = null;
                console.log('⏹️ 语音播放已停止');
            }
            hideVoiceControlPanel();
        }

        /**
         * 暂停/恢复语音播放
         */
        function pauseVoice() {
            if (currentAudio) {
                if (currentAudio.paused) {
                    currentAudio.play();
                    updatePauseButton(false);
                    console.log('▶️ 语音播放已恢复');
                } else {
                    currentAudio.pause();
                    updatePauseButton(true);
                    console.log('⏸️ 语音播放已暂停');
                }
            }
        }

        /**
         * 显示语音控制面板
         */
        function showVoiceControlPanel(text) {
            const panel = document.getElementById('voice-control-panel');

            if (panel) {
                panel.style.display = 'block';

                // 重置进度条
                updateVoiceProgress();
                updatePauseButton(false);
            }
        }

        /**
         * 隐藏语音控制面板
         */
        function hideVoiceControlPanel() {
            const panel = document.getElementById('voice-control-panel');
            if (panel) {
                panel.style.display = 'none';
            }
        }

        /**
         * 更新暂停按钮状态
         */
        function updatePauseButton(isPaused) {
            const pauseBtn = document.getElementById('voice-pause-btn');
            if (pauseBtn) {
                const icon = pauseBtn.querySelector('i');

                if (isPaused) {
                    icon.className = 'fas fa-play';
                } else {
                    icon.className = 'fas fa-pause';
                }
            }
        }

        /**
         * 更新语音播放进度
         */
        function updateVoiceProgress() {
            if (currentAudio) {
                const progress = document.getElementById('voice-progress');
                const currentTimeElement = document.getElementById('voice-current-time');

                if (progress && currentTimeElement) {
                    const percentage = (currentAudio.currentTime / currentAudio.duration) * 100;
                    progress.style.width = percentage + '%';
                    currentTimeElement.textContent = formatTime(currentAudio.currentTime);
                }
            }
        }

        /**
         * 更新语音总时长
         */
        function updateVoiceDuration() {
            if (currentAudio) {
                const totalTimeElement = document.getElementById('voice-total-time');
                if (totalTimeElement) {
                    totalTimeElement.textContent = formatTime(currentAudio.duration);
                }
            }
        }

        /**
         * 格式化时间显示
         */
        function formatTime(seconds) {
            if (isNaN(seconds)) return '0:00';
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = Math.floor(seconds % 60);
            return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        }

        /**
         * 显示语音错误提示
         * @param {string} message - 错误信息
         */
        function showVoiceError(message) {
            // 创建一个临时的错误提示
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #ff6b6b;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                animation: slideIn 0.3s ease-out;
            `;
            errorDiv.textContent = message;

            document.body.appendChild(errorDiv);

            // 3秒后自动移除
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.style.animation = 'slideOut 0.3s ease-in';
                    setTimeout(() => {
                        document.body.removeChild(errorDiv);
                    }, 300);
                }
            }, 3000);
        }

        /**
         * 显示音频启用提示
         */
        function showAudioEnablePrompt() {
            // 创建提示元素
            const prompt = document.createElement('div');
            prompt.id = 'audio-enable-prompt';
            prompt.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.9);
                color: white;
                padding: 20px 30px;
                border-radius: 10px;
                z-index: 10000;
                text-align: center;
                font-size: 16px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                cursor: pointer;
                animation: fadeIn 0.3s ease-out;
            `;
            prompt.innerHTML = `
                <div style="margin-bottom: 10px; font-size: 24px;">🔊</div>
                <div style="margin-bottom: 10px; font-weight: bold;">点击此处启用音频播放</div>
                <div style="font-size: 12px; opacity: 0.8;">浏览器需要用户交互才能播放音频</div>
            `;

            // 点击提示启用音频
            prompt.addEventListener('click', () => {
                hideAudioEnablePrompt();
                initializeAudioContext();
            });

            document.body.appendChild(prompt);
        }

        /**
         * 隐藏音频启用提示
         */
        function hideAudioEnablePrompt() {
            const prompt = document.getElementById('audio-enable-prompt');
            if (prompt) {
                prompt.style.animation = 'fadeOut 0.3s ease-out';
                setTimeout(() => {
                    if (prompt.parentNode) {
                        prompt.parentNode.removeChild(prompt);
                    }
                }, 300);
            }
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
            @keyframes fadeIn {
                from { opacity: 0; transform: translate(-50%, -50%) scale(0.9); }
                to { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            }
            @keyframes fadeOut {
                from { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                to { opacity: 0; transform: translate(-50%, -50%) scale(0.9); }
            }
        `;
        document.head.appendChild(style);

        // ==================== 语音设置功能 ====================

        /**
         * 切换语音设置面板显示/隐藏
         */
        function toggleVoiceSettings() {
            const panel = document.getElementById('voiceSettingsPanel');
            const arrow = document.getElementById('voiceArrow');

            if (panel && arrow) {
                panel.classList.toggle('show');
                arrow.classList.toggle('rotated');

                // 保存展开状态
                const isExpanded = panel.classList.contains('show');
                localStorage.setItem('voiceSettingsExpanded', isExpanded.toString());
            }
        }

        /**
         * 更新当前语音类型显示
         * @param {string} voiceType - 语音类型
         */
        function updateCurrentVoiceDisplay(voiceType) {
            const display = document.getElementById('currentVoiceDisplay');
            if (display) {
                display.textContent = voiceTypes[voiceType] || '中文女教师';
            }
        }

        /**
         * 初始化语音设置
         */
        function initVoiceSettings() {
            const voiceSelect = document.getElementById('voiceSelect');
            const speedRange = document.getElementById('speedRange');
            const speedValue = document.getElementById('speedValue');

            if (voiceSelect) {
                // 从本地存储加载语音设置
                const savedVoice = localStorage.getItem('selectedVoice');
                if (savedVoice && voiceTypes[savedVoice]) {
                    voiceConfig.voice = savedVoice;
                    voiceSelect.value = savedVoice;
                    updateCurrentVoiceDisplay(savedVoice);
                } else {
                    updateCurrentVoiceDisplay('zh-female-teacher');
                }

                // 监听语音类型变化
                voiceSelect.addEventListener('change', function() {
                    const selectedVoice = this.value;

                    // 停止当前播放的语音
                    stopVoice();

                    voiceConfig.voice = selectedVoice;

                    // 更新当前语音显示
                    updateCurrentVoiceDisplay(selectedVoice);

                    // 保存到本地存储
                    localStorage.setItem('selectedVoice', selectedVoice);

                    // 显示设置成功提示
                    showVoiceSettingSuccess(voiceTypes[selectedVoice]);

                    console.log('🎤 语音类型已切换为:', voiceTypes[selectedVoice]);
                });
            }

            if (speedRange && speedValue) {
                // 从本地存储加载语音速度设置
                const savedSpeed = localStorage.getItem('voiceSpeed');
                if (savedSpeed) {
                    voiceConfig.speed = parseFloat(savedSpeed);
                    speedRange.value = savedSpeed;
                    speedValue.textContent = savedSpeed + 'x';
                }

                // 监听语音速度变化
                speedRange.addEventListener('input', function() {
                    const speed = parseFloat(this.value);
                    voiceConfig.speed = speed;
                    speedValue.textContent = speed + 'x';

                    // 保存到本地存储
                    localStorage.setItem('voiceSpeed', speed.toString());

                    console.log('🎤 语音速度已调整为:', speed + 'x');
                });
            }

            const volumeRange = document.getElementById('volumeRange');
            const volumeValue = document.getElementById('volumeValue');

            if (volumeRange && volumeValue) {
                // 从本地存储加载音量设置
                const savedVolume = localStorage.getItem('voiceVolume');
                if (savedVolume) {
                    voiceConfig.volume = parseFloat(savedVolume);
                    volumeRange.value = savedVolume;
                    volumeValue.textContent = Math.round(savedVolume * 100) + '%';
                }

                // 监听音量变化
                volumeRange.addEventListener('input', function() {
                    const volume = parseFloat(this.value);
                    voiceConfig.volume = volume;
                    volumeValue.textContent = Math.round(volume * 100) + '%';

                    // 保存到本地存储
                    localStorage.setItem('voiceVolume', volume.toString());

                    console.log('🔊 音量已调整为:', Math.round(volume * 100) + '%');
                });
            }
        }

        /**
         * 显示语音设置成功提示
         * @param {string} voiceName - 语音类型名称
         */
        function showVoiceSettingSuccess(voiceName) {
            const successDiv = document.createElement('div');
            successDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #4CAF50;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                animation: slideIn 0.3s ease-out;
            `;
            successDiv.innerHTML = `<i class="fas fa-check-circle"></i> 语音已切换为: ${voiceName}`;

            document.body.appendChild(successDiv);

            // 3秒后自动移除
            setTimeout(() => {
                if (successDiv.parentNode) {
                    successDiv.style.animation = 'slideOut 0.3s ease-in';
                    setTimeout(() => {
                        document.body.removeChild(successDiv);
                    }, 300);
                }
            }, 3000);
        }

        /**
         * 测试当前选择的语音
         */
        function testCurrentVoice() {
            const currentVoiceName = voiceTypes[voiceConfig.voice];
            const currentNote = voiceTypeNotes[voiceConfig.voice];
            const characterIntro = voiceCharacterIntros[voiceConfig.voice];

            // 显示语音类型说明
            showVoiceTypeNote(currentNote);

            // 使用个性化的角色介绍
            const testText = characterIntro + ` 当前语音速度为${voiceConfig.speed}倍，音量为${Math.round(voiceConfig.volume * 100)}%。让我们一起开始愉快的数学学习之旅吧！`;

            // 使用测试专用配置
            playVoice(testText, {
                content_type: 'concept',
                difficulty: 'easy'
            });
        }

        /**
         * 显示语音类型说明
         * @param {string} note - 说明文本
         */
        function showVoiceTypeNote(note) {
            const noteDiv = document.createElement('div');
            noteDiv.style.cssText = `
                position: fixed;
                top: 70px;
                right: 20px;
                background: #ff9800;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 13px;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                animation: slideIn 0.3s ease-out;
                max-width: 300px;
            `;
            noteDiv.innerHTML = `<i class="fas fa-info-circle"></i> ${note}`;

            document.body.appendChild(noteDiv);

            // 5秒后自动移除
            setTimeout(() => {
                if (noteDiv.parentNode) {
                    noteDiv.style.animation = 'slideOut 0.3s ease-in';
                    setTimeout(() => {
                        document.body.removeChild(noteDiv);
                    }, 300);
                }
            }, 5000);
        }

        // 页面加载完成后初始化语音设置
        document.addEventListener('DOMContentLoaded', function() {
            initVoiceSettings();

            // 恢复语音设置面板的展开状态
            const isExpanded = localStorage.getItem('voiceSettingsExpanded') === 'true';
            if (isExpanded) {
                const panel = document.getElementById('voiceSettingsPanel');
                const arrow = document.getElementById('voiceArrow');
                if (panel && arrow) {
                    panel.classList.add('show');
                    arrow.classList.add('rotated');
                }
            }
        });

        // 页面卸载时停止音频
        window.addEventListener('beforeunload', () => {
            stopVoice();
        });

    </script>

    <div id="mindmap-modal" class="mindmap-modal">
        <div class="mindmap-modal-content">
            <div class="mindmap-modal-header">
                <h2><i class="fas fa-sitemap"></i> 概念图谱</h2>
                <button class="mindmap-close-btn" onclick="closeMindmapModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="mindmap-modal-body">
                <div id="mindmap-display" class="mindmap-container">
                    <div style="display: flex; justify-content: center; align-items: center; height: 100%; color: var(--gray);">
                        <div style="text-align: center;">
                            <i class="fas fa-sitemap" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
                            <h3>概念图谱</h3>
                            <p>点击右侧"概念图谱"查看知识结构</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="voice-control-panel" class="voice-control-panel" style="display: none;">
        <div class="voice-control-header">
            <div class="voice-control-title">
                <i class="fas fa-volume-up"></i>
            </div>
            <button class="voice-control-close" onclick="stopVoice()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="voice-control-body">
            <div class="voice-control-buttons">
                <button id="voice-pause-btn" class="voice-control-btn" onclick="pauseVoice()">
                    <i class="fas fa-pause"></i>
                </button>
                <button id="voice-stop-btn" class="voice-control-btn" onclick="stopVoice()">
                    <i class="fas fa-stop"></i>
                </button>
            </div>
            <div class="voice-progress-container">
                <div class="voice-progress-bar">
                    <div id="voice-progress" class="voice-progress-fill"></div>
                </div>
                <div class="voice-time">
                    <span id="voice-current-time">0:00</span> / <span id="voice-total-time">0:00</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 章节配置
        const CHAPTER_CONFIG = {
            textbook: '必修第二册',
            chapterId: '第9章_统计',
            chapterTitle: '第9章 统计',
            sections: [{"id": "9.1", "name": "随机抽样", "filename": "9.1_随机抽样"}, {"id": "9.2", "name": "用样本估计总体", "filename": "9.2_用样本估计总体"}, {"id": "9.3", "name": "统计案例", "filename": "9.3_统计案例"}],
            knowledgeBasePath: '/media/dp/software/mathtech1.0/3.0/math_teaching_system/knowledge_base_structured'
        };
        
        // 当前选择状态
        let currentSection = '9.1';
        let currentModule = 'concept_learning';
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeChapterPage();
            if (currentSection) {
                loadSectionContent(currentSection);
            }
        });
        
        // 初始化章节页面
        function initializeChapterPage() {
            // 更新页面标题
            updatePageTitle();
            
            // 初始化侧边栏导航
            initializeSidebarNavigation();
            
            // 初始化模块按钮
            initializeModuleButtons();
        }
        
        // 更新页面标题
        function updatePageTitle() {
            const titleElement = document.querySelector('.content-header h1');
            if (titleElement) {
                titleElement.textContent = CHAPTER_CONFIG.chapterTitle;
            }
        }
        
        // 初始化侧边栏导航
        function initializeSidebarNavigation() {
            const sidebarContent = document.querySelector('.sidebar-content');
            if (!sidebarContent) return;
            
            let navHtml = `
                <div class="chapter-nav">
                    <h3>${CHAPTER_CONFIG.chapterTitle}</h3>
                    <div class="section-list">
                        ${CHAPTER_CONFIG.sections.map(section => `
                            <div class="section-item ${section.id === currentSection ? 'active' : ''}" 
                                 onclick="selectSection('${section.id}', '${section.name}')">
                                ${section.id} ${section.name}
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
            
            sidebarContent.innerHTML = navHtml;
        }
        
        // 选择小节
        function selectSection(sectionId, sectionName) {
            // 更新当前选择
            currentSection = sectionId;
            
            // 更新导航状态
            document.querySelectorAll('.section-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 加载内容
            loadSectionContent(sectionId);
        }
        
        // 加载小节内容
        async function loadSectionContent(sectionId) {
            if (!sectionId) return;
            
            const contentArea = document.querySelector('.content-area');
            if (!contentArea) return;
            
            // 显示加载状态
            contentArea.innerHTML = `
                <div class="loading-state">
                    <i class="fas fa-spinner fa-spin"></i>
                    <h3>正在加载内容...</h3>
                    <p>正在从知识库获取 ${sectionId} 的内容</p>
                </div>
            `;
            
            try {
                // 构建文件路径
                const sectionName = getSectionName(sectionId);
                const filePath = `${CHAPTER_CONFIG.knowledgeBasePath}/${CHAPTER_CONFIG.textbook}/${CHAPTER_CONFIG.chapterId}/${sectionId}_${sectionName}.json`;
                
                const response = await fetch(filePath);
                if (response.ok) {
                    const data = await response.json();
                    renderSectionContent(data);
                } else {
                    renderFallbackContent(sectionId);
                }
            } catch (error) {
                console.error('加载失败:', error);
                renderFallbackContent(sectionId);
            }
        }
        
        // 获取小节名称
        function getSectionName(sectionId) {
            const section = CHAPTER_CONFIG.sections.find(s => s.id === sectionId);
            return section ? section.name : '未知小节';
        }
        
        // 渲染小节内容
        function renderSectionContent(data) {
            const contentArea = document.querySelector('.content-area');
            const moduleContent = data.content[currentModule];
            
            if (moduleContent) {
                let html = `
                    <div class="section-content">
                        <h2>${data.section_info.full_title} - ${getModuleName(currentModule)}</h2>
                `;
                
                // 根据模块类型渲染内容
                if (currentModule === 'concept_learning' && moduleContent.content) {
                    html += renderConceptContent(moduleContent);
                } else {
                    html += `<p>${moduleContent.title || '内容加载中...'}</p>`;
                }
                
                html += `
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="playVoice('${moduleContent.voice_script || ''}')">
                                🎧 听讲解
                            </button>
                            <button class="btn btn-outline" onclick="showAnimation('${moduleContent.animation_id || ''}')">
                                🎬 看动画
                            </button>
                            <button class="btn btn-outline" onclick="showVideo('${moduleContent.video_url || ''}')">
                                📹 视频精讲
                            </button>
                        </div>
                    </div>
                `;
                
                contentArea.innerHTML = html;
            } else {
                renderFallbackContent(currentSection);
            }
        }
        
        // 渲染概念学习内容
        function renderConceptContent(moduleContent) {
            let html = '';
            
            if (moduleContent.content.definition) {
                html += `<div class="definition"><strong>定义：</strong>${moduleContent.content.definition}</div>`;
            }
            
            if (moduleContent.content.key_points) {
                html += `
                    <div class="key-points">
                        <strong>要点：</strong>
                        <ul>
                            ${moduleContent.content.key_points.map(point => `<li>${point}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }
            
            if (moduleContent.content.examples) {
                html += `
                    <div class="examples">
                        <strong>示例：</strong>
                        ${moduleContent.content.examples.map(example => `
                            <div class="example">
                                <h4>${example.title}</h4>
                                <p>${example.content}</p>
                                <em>${example.explanation}</em>
                            </div>
                        `).join('')}
                    </div>
                `;
            }
            
            return html;
        }
        
        // 渲染备用内容
        function renderFallbackContent(sectionId) {
            const contentArea = document.querySelector('.content-area');
            contentArea.innerHTML = `
                <div class="fallback-content">
                    <h2>${CHAPTER_CONFIG.chapterTitle} - ${sectionId} - ${getModuleName(currentModule)}</h2>
                    <p>✅ 章节页面功能正常工作！</p>
                    <p>✅ 基于框架文件生成的独立章节页面！</p>
                    <p>📁 数据路径：${CHAPTER_CONFIG.textbook}/${CHAPTER_CONFIG.chapterId}/${sectionId}_${getSectionName(sectionId)}.json</p>
                    <div class="action-buttons">
                        <button class="btn btn-primary">🎧 听讲解</button>
                        <button class="btn btn-outline">🎬 看动画</button>
                        <button class="btn btn-outline">📹 视频精讲</button>
                    </div>
                </div>
            `;
        }
        
        // 初始化模块按钮
        function initializeModuleButtons() {
            const moduleButtons = document.querySelectorAll('.module-btn');
            moduleButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    moduleButtons.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentModule = this.dataset.module;
                    if (currentSection) {
                        loadSectionContent(currentSection);
                    }
                });
            });
        }
        
        // 获取模块名称
        function getModuleName(module) {
            const names = {
                'concept_learning': '概念学习',
                'core_skills': '核心技巧',
                'problem_types': '题型高手',
                'practice_exercises': '动手练习'
            };
            return names[module] || module;
        }
        
        // 多媒体功能
        function playVoice(text) {
            console.log('播放语音:', text);
            alert('🔊 语音播放功能：' + (text || '暂无语音内容'));
        }
        
        function showAnimation(animationId) {
            console.log('播放动画:', animationId);
            alert('🎬 动画播放功能：' + (animationId || '暂无动画'));
        }
        
        function showVideo(videoUrl) {
            console.log('播放视频:', videoUrl);
            alert('📹 视频播放功能：' + (videoUrl || '暂无视频'));
        }
    </script>
    
</body>
</html>