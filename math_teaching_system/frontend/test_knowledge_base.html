<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .module-selector {
            margin-bottom: 20px;
        }
        .module-selector button {
            margin: 5px;
            padding: 10px 20px;
            border: none;
            background: #007bff;
            color: white;
            border-radius: 5px;
            cursor: pointer;
        }
        .module-selector button:hover {
            background: #0056b3;
        }
        .module-selector button.active {
            background: #28a745;
        }
        .content-area {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            min-height: 400px;
        }
        .content-item {
            border: 1px solid #eee;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .content-header {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .content-meta {
            color: #666;
            font-size: 12px;
            margin-bottom: 10px;
        }
        .content-preview {
            color: #555;
            line-height: 1.6;
        }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #f5c6cb;
        }
        .stats {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎓 逗逗专用高中智能数学教学系统 - 知识库测试</h1>
        
        <div class="stats" id="stats">
            <h3>📊 知识库统计</h3>
            <div id="statsContent">正在加载统计信息...</div>
        </div>

        <div class="module-selector">
            <h3>📚 选择学习模块</h3>
            <button onclick="loadModule('concept_learning')" id="btn-concept">概念学习</button>
            <button onclick="loadModule('core_skills')" id="btn-skills">核心技巧</button>
            <button onclick="loadModule('problem_types')" id="btn-types">题型高手</button>
            <button onclick="loadModule('practice_exercises')" id="btn-practice">动手练习</button>
        </div>

        <div class="content-area" id="contentArea">
            <div class="loading">请选择一个学习模块开始浏览内容...</div>
        </div>
    </div>

    <script>
        let currentModule = null;

        // 页面加载时获取统计信息
        window.onload = function() {
            loadStats();
        };

        // 加载统计信息
        async function loadStats() {
            try {
                const response = await fetch('/knowledge_base_final/metadata/processing_stats.json');
                const stats = await response.json();
                
                document.getElementById('statsContent').innerHTML = `
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <div><strong>总文件数:</strong> ${stats.total_files}</div>
                        <div><strong>处理成功:</strong> ${stats.processed_files}</div>
                        <div><strong>成功率:</strong> ${stats.success_rate}%</div>
                        <div><strong>练习题总数:</strong> ${stats.total_exercises}</div>
                        <div><strong>处理时间:</strong> ${stats.processing_time}秒</div>
                        <div><strong>生成时间:</strong> ${new Date(stats.generated_date).toLocaleString()}</div>
                    </div>
                `;
            } catch (error) {
                document.getElementById('statsContent').innerHTML = `<div class="error">统计信息加载失败: ${error.message}</div>`;
            }
        }

        // 加载模块内容
        async function loadModule(moduleKey) {
            // 更新按钮状态
            document.querySelectorAll('.module-selector button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.getElementById(`btn-${moduleKey.split('_')[0]}`).classList.add('active');

            currentModule = moduleKey;
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = '<div class="loading">正在加载内容...</div>';

            try {
                const response = await fetch(`/knowledge_base_final/modules/${moduleKey}.json`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                displayModuleContent(data, moduleKey);
            } catch (error) {
                contentArea.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 显示模块内容
        function displayModuleContent(data, moduleKey) {
            const contentArea = document.getElementById('contentArea');
            const moduleInfo = data.module_info || {};
            const content = data.content || [];

            let html = `
                <h2>📖 ${moduleInfo.name || moduleKey}</h2>
                <p><strong>内容数量:</strong> ${moduleInfo.file_count || content.length} 个</p>
                <p><strong>生成时间:</strong> ${moduleInfo.generated_date ? new Date(moduleInfo.generated_date).toLocaleString() : '未知'}</p>
                <hr>
            `;

            if (content.length === 0) {
                html += '<div class="loading">该模块暂无内容</div>';
            } else {
                // 显示前20个内容项
                const displayItems = content.slice(0, 20);
                
                displayItems.forEach((item, index) => {
                    const metadata = item.metadata || {};
                    const itemContent = item.content || {};
                    
                    // 生成预览内容
                    let preview = '';
                    if (itemContent.raw_content) {
                        preview = itemContent.raw_content.substring(0, 300) + '...';
                    } else if (itemContent.total_exercises > 0) {
                        preview = `包含 ${itemContent.total_exercises} 道练习题`;
                        if (itemContent.difficulty_distribution) {
                            const dist = itemContent.difficulty_distribution;
                            preview += `\n难度分布: 基础 ${dist['基础'] || 0} 题, 中等 ${dist['中等'] || 0} 题, 困难 ${dist['困难'] || 0} 题`;
                        }
                    } else if (typeof itemContent === 'string') {
                        preview = itemContent.substring(0, 300) + '...';
                    } else {
                        preview = '暂无预览';
                    }

                    html += `
                        <div class="content-item">
                            <div class="content-header">${index + 1}. ${metadata.title || '未命名'}</div>
                            <div class="content-meta">
                                类型: ${metadata.content_type || '未知'} | 
                                难度: ${metadata.difficulty || '未知'} | 
                                章节: ${metadata.chapter || '未分类'} | 
                                节: ${metadata.section || '未知'}
                            </div>
                            <div class="content-preview">${preview}</div>
                        </div>
                    `;
                });

                if (content.length > 20) {
                    html += `<div class="loading">还有 ${content.length - 20} 个内容项未显示...</div>`;
                }
            }

            contentArea.innerHTML = html;
        }
    </script>
</body>
</html>
