{"section_info": {"id": "2.2 直线的方程", "number": "2.2", "title": "直线的方程", "full_title": "2.2 直线的方程", "chapter_id": "第02章_直线和圆的方程", "chapter_title": "第二章 直线和圆的方程", "textbook": "选择性必修第一册", "estimated_time": 30, "difficulty": "中等", "importance": "高"}, "content": {"concept_learning": {"title": "直线的方程", "content": {"definition": "直线方程是表示直线上所有点坐标满足的关系式，有多种表示形式。", "key_points": ["点斜式：y-y₀ = k(x-x₀)", "斜截式：y = kx + b", "两点式：(y-y₁)/(y₂-y₁) = (x-x₁)/(x₂-x₁)", "截距式：x/a + y/b = 1", "一般式：Ax + By + C = 0"], "examples": [{"title": "点斜式应用", "content": "过点(2,3)且斜率为2的直线方程：y-3 = 2(x-2)，即y = 2x-1", "explanation": "已知点和斜率时用点斜式最方便"}, {"title": "截距式应用", "content": "在x轴上截距为3，在y轴上截距为4的直线方程：x/3 + y/4 = 1", "explanation": "已知两个截距时用截距式"}]}, "voice_script": "直线方程有多种形式，每种形式都有其适用的情况。点斜式适用于已知点和斜率的情况，斜截式是最常用的形式，两点式适用于已知两点的情况，截距式适用于已知截距的情况，一般式是最通用的形式。", "video_url": "/videos/concept/直线的方程.mp4", "animation_id": "line_equation_animation", "concept_images": ["/images/concepts/直线方程形式图.png", "/images/concepts/点斜式图.png", "/images/concepts/截距式图.png"]}, "core_skills": {"title": "直线方程求解技巧", "skills": [{"name": "方程形式选择", "content": {"条件分析": {"description": "根据已知条件选择合适的方程形式", "voice_script": "选择直线方程形式要根据已知条件，如已知点和斜率用点斜式，已知两点用两点式。"}, "形式转换": {"description": "不同形式间的相互转换", "voice_script": "要熟练掌握各种形式间的转换，特别是化为一般式。"}}}]}, "problem_types": {"title": "直线方程的典型题型", "types": [{"name": "求直线方程", "description": "根据给定条件求直线方程", "voice_script": "求直线方程要根据条件选择合适的方法和形式。", "examples": [{"problem": "求过点(1,2)且与直线2x+y-1=0垂直的直线方程", "solution": "x-2y+3=0", "explanation": "垂直直线斜率乘积为-1，原直线斜率为-2，所求直线斜率为1/2"}]}]}, "practice_exercises": {"title": "直线方程练习题", "exercises": [{"id": "exercise_2_2_1", "type": "选择题", "difficulty": "中等", "question": "过点(2,1)且斜率为-1的直线方程为（）", "options": ["A. x+y-3=0", "B. x-y-1=0", "C. x+y+1=0", "D. x-y+3=0"], "answer": "A", "explanation": "用点斜式：y-1 = -1(x-2)，整理得x+y-3=0。", "voice_script": "正确答案是A。利用点斜式方程：y-y₀ = k(x-x₀)，代入点(2,1)和斜率k=-1，得到y-1 = -1(x-2)，展开得y-1 = -x+2，整理得x+y-3=0。", "estimated_time": 3}]}}}