{"chapter_info": {"id": "第02章_随机变量及其分布", "title": "第二章 随机变量及其分布", "textbook": "选择性必修第三册", "sections": ["2.1 条件概率", "2.2 事件的相互独立性", "2.3 离散型随机变量及其分布列", "2.4 二项分布", "2.5 连续型随机变量的概率密度函数", "2.6 正态分布"], "estimated_time": 120, "difficulty": "困难", "importance": "高"}, "sections": {"2.1 条件概率": {"concept_learning": {"title": "条件概率", "content": {"definition": "条件概率是指在事件B发生的条件下，事件A发生的概率，记作P(A|B)。", "key_points": ["条件概率定义：P(A|B) = P(AB)/P(B)，其中P(B) > 0", "乘法公式：P(AB) = P(A|B)·P(B) = P(B|A)·P(A)", "全概率公式：P(A) = Σ P(A|Bᵢ)·P(Bᵢ)", "贝叶斯公式：P(Bᵢ|A) = P(A|Bᵢ)·P(Bᵢ)/P(A)", "条件概率的性质：满足概率的基本性质"], "examples": [{"title": "条件概率计算", "content": "掷两次骰子，已知第一次得到偶数，求两次点数之和为8的概率", "explanation": "利用条件概率公式计算"}, {"title": "全概率公式应用", "content": "某工厂有三条生产线，产品次品率分别为2%、3%、1%，求随机抽取一件产品是次品的概率", "explanation": "利用全概率公式求解"}]}, "voice_script": "条件概率是概率论中的重要概念，它描述了在某个条件下事件发生的概率。条件概率的引入使我们能够处理更复杂的概率问题，全概率公式和贝叶斯公式是条件概率的重要应用。", "video_url": "/videos/concept/条件概率.mp4", "animation_id": "conditional_probability_animation", "concept_images": ["/images/concepts/条件概率图.png", "/images/concepts/全概率公式图.png", "/images/concepts/贝叶斯公式图.png"]}, "core_skills": {"title": "条件概率计算技巧", "skills": [{"name": "公式应用", "content": {"基本公式": {"description": "熟练运用条件概率的定义公式", "voice_script": "条件概率的计算要准确理解条件的含义，正确应用定义公式。"}, "复合公式": {"description": "灵活运用全概率公式和贝叶斯公式", "voice_script": "复杂的条件概率问题往往需要全概率公式和贝叶斯公式。"}}}]}, "problem_types": {"title": "条件概率的典型题型", "types": [{"name": "条件概率计算", "description": "根据给定条件计算条件概率", "voice_script": "条件概率计算要明确条件事件和目标事件。", "examples": [{"problem": "一副牌中抽到红桃的概率是1/4，已知抽到红色牌，求是红桃的概率", "solution": "1/2", "explanation": "P(红桃|红色) = P(红桃)/P(红色) = (1/4)/(1/2) = 1/2"}]}]}, "practice_exercises": {"title": "条件概率练习题", "exercises": [{"id": "exercise_2_1_1", "type": "选择题", "difficulty": "中等", "question": "掷一枚骰子，已知点数大于3，求点数为偶数的概率", "options": ["A. 1/3", "B. 1/2", "C. 2/3", "D. 3/4"], "answer": "C", "explanation": "点数大于3的有{4,5,6}，其中偶数有{4,6}，所以概率为2/3。", "voice_script": "正确答案是C。已知点数大于3，即样本空间缩小为{4,5,6}，共3个等可能结果。其中偶数有4和6，共2个。所以在点数大于3的条件下，点数为偶数的概率为2/3。", "estimated_time": 3}]}}, "2.3 离散型随机变量及其分布列": {"concept_learning": {"title": "离散型随机变量及其分布列", "content": {"definition": "离散型随机变量是取值为有限个或可列无限个的随机变量，分布列描述了随机变量各个取值的概率。", "key_points": ["离散型随机变量：取值可以一一列举", "分布列：P(X = xᵢ) = pᵢ，i = 1,2,...", "分布列性质：pᵢ ≥ 0，Σpᵢ = 1", "数学期望：E(X) = Σxᵢpᵢ", "方差：D(X) = E(X²) - [E(X)]² = Σ(xᵢ - E(X))²pᵢ"], "examples": [{"title": "分布列", "content": "掷骰子的点数X的分布列：P(X=k) = 1/6，k=1,2,3,4,5,6", "explanation": "每个点数出现的概率都是1/6"}, {"title": "期望和方差", "content": "上述骰子点数的期望E(X) = 3.5，方差D(X) = 35/12", "explanation": "利用期望和方差的计算公式"}]}, "voice_script": "离散型随机变量是概率论中的基本概念。分布列完整描述了随机变量的概率分布，数学期望反映了随机变量的平均水平，方差反映了随机变量的离散程度。", "video_url": "/videos/concept/离散型随机变量.mp4", "animation_id": "discrete_random_variable_animation", "concept_images": ["/images/concepts/分布列图.png", "/images/concepts/数学期望图.png", "/images/concepts/方差图.png"]}, "core_skills": {"title": "随机变量分析技巧", "skills": [{"name": "分布列构造", "content": {"概率计算": {"description": "根据问题背景计算各取值的概率", "voice_script": "构造分布列要准确计算随机变量各个取值的概率。"}, "性质验证": {"description": "验证分布列是否满足基本性质", "voice_script": "分布列必须满足概率的基本性质：非负性和归一性。"}}}]}, "problem_types": {"title": "随机变量的典型题型", "types": [{"name": "期望方差计算", "description": "计算离散型随机变量的数学期望和方差", "voice_script": "期望方差的计算要熟练运用相关公式。", "examples": [{"problem": "随机变量X的分布列为P(X=0)=0.3, P(X=1)=0.7，求E(X)和D(X)", "solution": "E(X)=0.7, D(X)=0.21", "explanation": "利用期望和方差的定义公式计算"}]}]}, "practice_exercises": {"title": "随机变量练习题", "exercises": [{"id": "exercise_2_3_1", "type": "选择题", "difficulty": "中等", "question": "随机变量X的分布列为P(X=1)=0.2, P(X=2)=0.3, P(X=3)=0.5，则E(X)=（）", "options": ["A. 2.0", "B. 2.3", "C. 2.5", "D. 3.0"], "answer": "B", "explanation": "E(X) = 1×0.2 + 2×0.3 + 3×0.5 = 0.2 + 0.6 + 1.5 = 2.3。", "voice_script": "正确答案是B。数学期望的计算公式是E(X) = Σxᵢpᵢ。代入各个取值和对应的概率：E(X) = 1×0.2 + 2×0.3 + 3×0.5 = 0.2 + 0.6 + 1.5 = 2.3。", "estimated_time": 3}]}}}}