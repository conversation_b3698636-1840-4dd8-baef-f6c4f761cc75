{"chapter_info": {"id": "第01章_计数原理", "title": "第一章 计数原理", "textbook": "选择性必修第三册", "sections": ["1.1 分类加法计数原理和分步乘法计数原理", "1.2 排列与组合", "1.3 二项式定理"], "estimated_time": 80, "difficulty": "中等", "importance": "高"}, "sections": {"1.1 分类加法计数原理和分步乘法计数原理": {"concept_learning": {"title": "分类加法计数原理和分步乘法计数原理", "content": {"definition": "计数原理是解决计数问题的基本方法，包括分类加法计数原理和分步乘法计数原理。", "key_points": ["分类加法计数原理：完成一件事有n类办法，各类办法的方法数分别为m₁, m₂, ..., mₙ，则总方法数为m₁ + m₂ + ... + mₙ", "分步乘法计数原理：完成一件事需要n个步骤，各步骤的方法数分别为m₁, m₂, ..., mₙ，则总方法数为m₁ × m₂ × ... × mₙ", "关键区别：分类是'或'的关系，分步是'且'的关系", "应用条件：各类方法相互独立，各步骤连续进行"], "examples": [{"title": "分类加法原理", "content": "从北京到上海，可以坐飞机（5种航班）或火车（3种车次），共有5+3=8种方法", "explanation": "不同的交通方式是分类关系，用加法"}, {"title": "分步乘法原理", "content": "从A地到C地要经过B地，A到B有2条路，B到C有3条路，共有2×3=6种路线", "explanation": "必须分步进行，用乘法"}]}, "voice_script": "计数原理是组合数学的基础。分类加法原理用于'或'的情况，分步乘法原理用于'且'的情况。正确区分这两种情况是解决计数问题的关键。", "video_url": "/videos/concept/计数原理.mp4", "animation_id": "counting_principle_animation", "concept_images": ["/images/concepts/分类加法原理图.png", "/images/concepts/分步乘法原理图.png", "/images/concepts/计数原理对比图.png"]}, "core_skills": {"title": "计数原理应用技巧", "skills": [{"name": "原理选择", "content": {"问题分析": {"description": "分析问题结构，选择合适的计数原理", "voice_script": "要仔细分析问题是分类还是分步的结构，这决定了用加法还是乘法。"}, "复杂问题": {"description": "对于复杂问题，可能需要两个原理结合使用", "voice_script": "复杂的计数问题往往需要分类和分步原理的综合运用。"}}}]}, "problem_types": {"title": "计数原理的典型题型", "types": [{"name": "基本计数问题", "description": "直接应用两个计数原理解决问题", "voice_script": "基本计数问题要准确识别是分类还是分步的结构。", "examples": [{"problem": "书架上有5本数学书，3本物理书，任选一本，有多少种选法？", "solution": "8种", "explanation": "这是分类问题，用加法原理：5+3=8"}]}]}, "practice_exercises": {"title": "计数原理练习题", "exercises": [{"id": "exercise_1_1_1", "type": "选择题", "difficulty": "基础", "question": "某人有3件上衣，2条裤子，要搭配一套衣服，有多少种搭配方法？", "options": ["A. 5", "B. 6", "C. 8", "D. 9"], "answer": "B", "explanation": "这是分步问题，先选上衣再选裤子，用乘法原理：3×2=6。", "voice_script": "正确答案是B。搭配衣服需要分两步：第一步选上衣有3种选择，第二步选裤子有2种选择。由于这两步必须都完成才能搭配成一套衣服，所以用分步乘法计数原理，总的搭配方法数为3×2=6种。", "estimated_time": 2}]}}, "1.2 排列与组合": {"concept_learning": {"title": "排列与组合", "content": {"definition": "排列是从n个不同元素中取出m个元素的有序排列，组合是从n个不同元素中取出m个元素的无序组合。", "key_points": ["排列数公式：A(n,m) = n!/(n-m)! = n(n-1)(n-2)...(n-m+1)", "组合数公式：C(n,m) = n!/[m!(n-m)!] = A(n,m)/m!", "排列与组合的区别：排列考虑顺序，组合不考虑顺序", "组合数性质：C(n,m) = C(n,n-m), C(n,0) = C(n,n) = 1", "杨辉三角：C(n,m) = C(n-1,m-1) + C(n-1,m)"], "examples": [{"title": "排列问题", "content": "从5个人中选3个人排成一排，有A(5,3) = 5×4×3 = 60种排法", "explanation": "考虑顺序的问题用排列"}, {"title": "组合问题", "content": "从5个人中选3个人组成小组，有C(5,3) = 10种选法", "explanation": "不考虑顺序的问题用组合"}]}, "voice_script": "排列与组合是计数问题中的重要概念。关键是要区分问题是否考虑顺序：考虑顺序用排列，不考虑顺序用组合。掌握排列数和组合数的计算公式是解题的基础。", "video_url": "/videos/concept/排列与组合.mp4", "animation_id": "permutation_combination_animation", "concept_images": ["/images/concepts/排列组合对比图.png", "/images/concepts/排列数公式图.png", "/images/concepts/组合数公式图.png"]}, "core_skills": {"title": "排列组合技巧", "skills": [{"name": "问题识别", "content": {"顺序判断": {"description": "判断问题是否考虑顺序", "voice_script": "判断是排列还是组合的关键是看问题是否考虑元素的顺序。"}, "公式应用": {"description": "熟练运用排列数和组合数公式", "voice_script": "要熟记排列数和组合数的计算公式，并能灵活运用。"}}}]}, "problem_types": {"title": "排列组合的典型题型", "types": [{"name": "排列组合应用", "description": "实际问题中的排列组合应用", "voice_script": "解决排列组合问题要先分析问题的结构，确定是排列还是组合。", "examples": [{"problem": "从10个人中选5个人参加比赛，有多少种选法？", "solution": "C(10,5) = 252", "explanation": "选人不考虑顺序，用组合"}]}]}, "practice_exercises": {"title": "排列组合练习题", "exercises": [{"id": "exercise_1_2_1", "type": "选择题", "difficulty": "中等", "question": "从6个不同的球中取3个球排成一排，有多少种排法？", "options": ["A. 20", "B. 60", "C. 120", "D. 216"], "answer": "C", "explanation": "这是排列问题，A(6,3) = 6×5×4 = 120。", "voice_script": "正确答案是C。这是一个排列问题，因为要把球排成一排，所以顺序是重要的。从6个不同的球中取3个球的排列数为A(6,3) = 6!/(6-3)! = 6!/3! = 6×5×4 = 120种。", "estimated_time": 3}]}}}}