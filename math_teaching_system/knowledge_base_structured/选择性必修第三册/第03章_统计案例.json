{"chapter_info": {"id": "第03章_统计案例", "title": "第三章 统计案例", "textbook": "选择性必修第三册", "sections": ["3.1 回归分析", "3.2 独立性检验"], "estimated_time": 60, "difficulty": "中等", "importance": "中等"}, "sections": {"3.1 回归分析": {"concept_learning": {"title": "回归分析", "content": {"definition": "回归分析是研究变量间相关关系的统计方法，通过建立回归方程来描述变量间的数量关系。", "key_points": ["线性回归方程：ŷ = bx + a", "回归系数：b = Σ(xᵢ-x̄)(yᵢ-ȳ)/Σ(xᵢ-x̄)²", "截距：a = ȳ - bx̄", "相关系数：r = Σ(xᵢ-x̄)(yᵢ-ȳ)/√[Σ(xᵢ-x̄)²Σ(yᵢ-ȳ)²]", "决定系数：R² = 1 - Σ(yᵢ-ŷᵢ)²/Σ(yᵢ-ȳ)²"], "examples": [{"title": "线性回归", "content": "研究身高与体重的关系，建立回归方程ŷ = 0.8x - 80", "explanation": "通过最小二乘法建立线性回归方程"}, {"title": "相关性分析", "content": "计算相关系数r = 0.85，说明身高与体重有较强的正相关关系", "explanation": "相关系数的绝对值越接近1，相关性越强"}]}, "voice_script": "回归分析是统计学中重要的分析方法，用于研究变量间的相关关系。线性回归是最基本的回归分析方法，通过建立线性方程来描述两个变量间的关系。相关系数用来衡量变量间线性相关的强弱。", "video_url": "/videos/concept/回归分析.mp4", "animation_id": "regression_analysis_animation", "concept_images": ["/images/concepts/散点图.png", "/images/concepts/回归直线图.png", "/images/concepts/相关系数图.png"]}, "core_skills": {"title": "回归分析技巧", "skills": [{"name": "回归方程建立", "content": {"最小二乘法": {"description": "利用最小二乘法求回归方程", "voice_script": "最小二乘法是建立回归方程的标准方法，要熟练掌握计算步骤。"}, "相关性判断": {"description": "通过相关系数判断变量间的相关性", "voice_script": "相关系数的大小和符号反映了变量间相关关系的强弱和方向。"}}}]}, "problem_types": {"title": "回归分析的典型题型", "types": [{"name": "回归方程求解", "description": "根据数据建立回归方程并进行预测", "voice_script": "回归方程的建立要按照最小二乘法的步骤进行计算。", "examples": [{"problem": "根据5组数据建立y关于x的回归方程", "solution": "ŷ = 2x + 1", "explanation": "利用最小二乘法公式计算回归系数"}]}]}, "practice_exercises": {"title": "回归分析练习题", "exercises": [{"id": "exercise_3_1_1", "type": "应用题", "difficulty": "中等", "question": "某商店统计了5天的气温x(℃)和冷饮销量y(杯)的数据，建立回归方程并预测气温30℃时的销量。数据：(20,50), (22,60), (25,75), (28,85), (30,95)", "answer": "ŷ = 3x - 10，预测销量80杯", "explanation": "利用最小二乘法建立回归方程，然后代入x=30进行预测。", "voice_script": "这是一个典型的回归分析问题。首先计算各项统计量：x̄=25，ȳ=73，然后利用最小二乘法公式计算回归系数b和截距a，得到回归方程ŷ = 3x - 10。当x=30时，预测销量为3×30-10=80杯。", "estimated_time": 8}]}}, "3.2 独立性检验": {"concept_learning": {"title": "独立性检验", "content": {"definition": "独立性检验是用来判断两个分类变量是否独立的统计方法，常用卡方检验。", "key_points": ["卡方统计量：χ² = n(ad-bc)²/[(a+b)(c+d)(a+c)(b+d)]", "2×2列联表：用于整理两个分类变量的数据", "临界值：根据显著性水平确定临界值", "判断准则：χ² > 临界值时拒绝独立性假设", "显著性水平：常用0.05, 0.01等"], "examples": [{"title": "独立性检验", "content": "检验性别与专业选择是否独立，计算χ²统计量并与临界值比较", "explanation": "利用卡方检验判断两个分类变量的独立性"}, {"title": "列联表分析", "content": "整理数据到2×2列联表中，便于计算和分析", "explanation": "列联表是独立性检验的基础工具"}]}, "voice_script": "独立性检验是统计推断的重要方法，用于判断两个分类变量是否存在关联。卡方检验是最常用的独立性检验方法，通过比较卡方统计量与临界值来做出统计推断。", "video_url": "/videos/concept/独立性检验.mp4", "animation_id": "independence_test_animation", "concept_images": ["/images/concepts/列联表图.png", "/images/concepts/卡方分布图.png", "/images/concepts/独立性检验流程图.png"]}, "core_skills": {"title": "独立性检验技巧", "skills": [{"name": "卡方检验", "content": {"统计量计算": {"description": "正确计算卡方统计量", "voice_script": "卡方统计量的计算要按照公式准确进行，注意各项数据的对应关系。"}, "结论判断": {"description": "根据统计量与临界值的比较得出结论", "voice_script": "独立性检验的结论要基于统计量与临界值的比较，并结合显著性水平。"}}}]}, "problem_types": {"title": "独立性检验的典型题型", "types": [{"name": "卡方检验应用", "description": "利用卡方检验判断两个分类变量的独立性", "voice_script": "独立性检验问题要正确建立列联表，计算卡方统计量。", "examples": [{"problem": "检验吸烟与患肺癌是否独立", "solution": "χ² = 6.25 > 3.841，拒绝独立性假设", "explanation": "计算卡方统计量并与临界值比较"}]}]}, "practice_exercises": {"title": "独立性检验练习题", "exercises": [{"id": "exercise_3_2_1", "type": "应用题", "difficulty": "中等", "question": "调查100名学生的性别与是否喜欢数学的关系。男生50人，其中30人喜欢数学；女生50人，其中20人喜欢数学。在α=0.05的显著性水平下检验性别与是否喜欢数学是否独立。", "answer": "χ² = 2.0 < 3.841，接受独立性假设", "explanation": "计算卡方统计量，与临界值3.841比较。", "voice_script": "这是一个典型的独立性检验问题。首先建立2×2列联表，然后计算卡方统计量χ² = 100×(30×30-20×20)²/(50×50×50×50) = 2.0。在α=0.05的显著性水平下，临界值为3.841。由于2.0 < 3.841，所以接受独立性假设，认为性别与是否喜欢数学独立。", "estimated_time": 6}]}}}}