{"chapter_info": {"id": "第09章_统计", "title": "第九章 统计", "textbook": "必修第二册", "sections": ["9.1 随机抽样", "9.2 用样本估计总体", "9.3 统计案例"], "estimated_time": 80, "difficulty": "中等", "importance": "中等"}, "sections": {"9.1 随机抽样": {"concept_learning": {"title": "随机抽样", "content": {"definition": "随机抽样是从总体中随机选取样本的方法，确保样本的代表性和无偏性。", "key_points": ["简单随机抽样：抽签法、随机数表法", "系统抽样：等距抽样", "分层抽样：按比例分层", "抽样的基本要求：随机性、代表性", "样本容量的确定"], "examples": [{"title": "简单随机抽样", "content": "从1000名学生中随机抽取50名进行调查", "explanation": "每个个体被抽到的概率相等"}, {"title": "分层抽样", "content": "某校有男生600人，女生400人，抽取100人的样本，应抽男生60人，女生40人", "explanation": "按男女比例进行抽样"}]}, "voice_script": "随机抽样是统计学的基础。简单随机抽样保证每个个体被抽到的概率相等，系统抽样适用于总体较大的情况，分层抽样适用于总体差异较大的情况。选择合适的抽样方法对获得代表性样本很重要。", "video_url": "/videos/concept/随机抽样.mp4", "animation_id": "random_sampling_animation", "concept_images": ["/images/concepts/简单随机抽样图.png", "/images/concepts/系统抽样图.png", "/images/concepts/分层抽样图.png"]}, "core_skills": {"title": "抽样方法选择技巧", "skills": [{"name": "抽样方法选择", "content": {"总体特点分析": {"description": "根据总体的大小和特点选择合适的抽样方法", "voice_script": "选择抽样方法要考虑总体的大小、个体间的差异程度等因素。"}, "样本容量确定": {"description": "根据精度要求和总体大小确定样本容量", "voice_script": "样本容量的确定要平衡精度要求和调查成本。"}}}]}, "problem_types": {"title": "随机抽样的典型题型", "types": [{"name": "抽样方案设计", "description": "根据实际情况设计合适的抽样方案", "voice_script": "抽样方案设计要考虑总体特点、样本容量、抽样方法等因素。", "examples": [{"problem": "某工厂有3个车间，人数分别为200、300、500，要抽取100人调查，如何抽样？", "solution": "用分层抽样，分别抽取20、30、50人", "explanation": "按各车间人数比例进行分层抽样"}]}]}, "practice_exercises": {"title": "随机抽样练习题", "exercises": [{"id": "exercise_9_1_1", "type": "选择题", "difficulty": "基础", "question": "下列抽样方法中，最适合从1000名学生中抽取50名的是（）", "options": ["<PERSON><PERSON> 抽签法", "B. 随机数表法", "C. 系统抽样", "D. 分层抽样"], "answer": "C", "explanation": "当总体容量较大时，系统抽样比较方便实用。", "voice_script": "正确答案是C。当总体容量较大（如1000人）而样本容量相对较小（如50人）时，系统抽样是最实用的方法。抽签法和随机数表法在总体容量大时操作不便，分层抽样需要总体有明显的层次结构。", "estimated_time": 2}]}}, "9.2 用样本估计总体": {"concept_learning": {"title": "用样本估计总体", "content": {"definition": "通过分析样本数据来推断总体的特征，包括用样本的数字特征估计总体的数字特征。", "key_points": ["频率分布直方图", "样本均值估计总体均值", "样本方差估计总体方差", "样本标准差估计总体标准差", "正态分布的特征"], "examples": [{"title": "频率分布直方图", "content": "将样本数据分组，画出频率分布直方图，观察数据分布特征", "explanation": "直方图能直观显示数据的分布情况"}, {"title": "样本均值", "content": "样本均值x̄ = (x₁ + x₂ + ... + xₙ)/n", "explanation": "样本均值是总体均值的无偏估计"}]}, "voice_script": "用样本估计总体是统计推断的核心内容。频率分布直方图能够直观地显示数据的分布特征，样本的数字特征如均值、方差等可以用来估计总体的相应特征。理解这些估计方法对数据分析很重要。", "video_url": "/videos/concept/样本估计总体.mp4", "animation_id": "sample_estimation_animation", "concept_images": ["/images/concepts/频率分布直方图.png", "/images/concepts/样本统计量.png", "/images/concepts/正态分布图.png"]}, "core_skills": {"title": "统计估计技巧", "skills": [{"name": "直方图分析", "content": {"分组方法": {"description": "合理确定组距和组数", "voice_script": "分组时要考虑数据的范围和样本容量，一般分5-12组比较合适。"}, "分布特征": {"description": "从直方图判断分布的形状、中心、离散程度", "voice_script": "观察直方图的形状可以判断数据是否服从正态分布等。"}}}]}, "problem_types": {"title": "统计估计的典型题型", "types": [{"name": "数字特征计算", "description": "计算样本的均值、方差、标准差等", "voice_script": "数字特征计算要熟练掌握各种公式和计算方法。", "examples": [{"problem": "样本数据：2, 4, 6, 8, 10，求样本均值和方差", "solution": "均值6，方差8", "explanation": "x̄ = 30/5 = 6，s² = [(2-6)² + ... + (10-6)²]/4 = 8"}]}]}, "practice_exercises": {"title": "统计估计练习题", "exercises": [{"id": "exercise_9_2_1", "type": "选择题", "difficulty": "中等", "question": "样本数据1, 3, 5, 7, 9的方差为（）", "options": ["A. 5", "B. 8", "C. 10", "D. 25"], "answer": "C", "explanation": "均值为5，方差为[(1-5)² + (3-5)² + (5-5)² + (7-5)² + (9-5)²]/4 = 40/4 = 10。", "voice_script": "正确答案是C。首先计算样本均值：(1+3+5+7+9)/5 = 25/5 = 5。然后计算方差：s² = [(1-5)² + (3-5)² + (5-5)² + (7-5)² + (9-5)²]/(5-1) = [16+4+0+4+16]/4 = 40/4 = 10。", "estimated_time": 4}]}}, "9.3 统计案例": {"concept_learning": {"title": "统计案例", "content": {"definition": "通过具体的统计案例，学习如何运用统计方法解决实际问题。", "key_points": ["回归分析：研究变量间的关系", "相关系数：衡量线性相关程度", "回归直线方程", "独立性检验", "统计决策"], "examples": [{"title": "线性回归", "content": "研究身高与体重的关系，建立回归直线方程", "explanation": "通过散点图和回归分析找出变量间的关系"}, {"title": "独立性检验", "content": "检验性别与专业选择是否独立", "explanation": "用卡方检验判断两个分类变量是否独立"}]}, "voice_script": "统计案例展示了统计方法在实际问题中的应用。回归分析用于研究变量间的关系，独立性检验用于判断分类变量间是否独立。这些方法在社会科学、自然科学等领域都有广泛应用。", "video_url": "/videos/concept/统计案例.mp4", "animation_id": "statistical_cases_animation", "concept_images": ["/images/concepts/回归分析图.png", "/images/concepts/相关系数图.png", "/images/concepts/独立性检验图.png"]}, "core_skills": {"title": "统计分析技巧", "skills": [{"name": "回归分析", "content": {"散点图": {"description": "画散点图观察变量间的关系", "voice_script": "散点图是进行回归分析的第一步，能直观显示变量间的关系。"}, "回归方程": {"description": "利用最小二乘法求回归直线方程", "voice_script": "回归直线方程的系数可以用公式计算，也可以用计算器求得。"}}}]}, "problem_types": {"title": "统计案例的典型题型", "types": [{"name": "回归分析题", "description": "根据数据建立回归方程并进行预测", "voice_script": "回归分析题要会画散点图、求回归方程、进行预测。", "examples": [{"problem": "根据5组数据求回归直线方程", "solution": "ŷ = bx + a", "explanation": "利用最小二乘法公式计算回归系数"}]}]}, "practice_exercises": {"title": "统计案例练习题", "exercises": [{"id": "exercise_9_3_1", "type": "应用题", "difficulty": "中等", "question": "某商店统计了5天的气温x(℃)和冷饮销量y(杯)的数据，求回归直线方程。数据：(1,5), (2,7), (3,8), (4,10), (5,12)", "answer": "ŷ = 1.7x + 2.4", "explanation": "利用最小二乘法公式计算得到回归直线方程。", "voice_script": "这是一个典型的回归分析问题。首先计算x和y的均值，然后利用最小二乘法公式计算回归系数b和a。通过计算可得回归直线方程为ŷ = 1.7x + 2.4，表示气温每升高1℃，冷饮销量平均增加1.7杯。", "estimated_time": 6}]}}}}