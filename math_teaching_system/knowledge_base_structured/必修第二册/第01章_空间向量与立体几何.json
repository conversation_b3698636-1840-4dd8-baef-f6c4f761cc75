{"chapter_info": {"id": "第01章_空间向量与立体几何", "title": "第一章 空间向量与立体几何", "textbook": "必修第二册", "sections": ["1.1 空间向量及其运算", "1.2 空间向量基本定理", "1.3 空间向量的坐标运算", "1.4 空间向量的应用"], "estimated_time": 100, "difficulty": "中等", "importance": "高"}, "sections": {"1.1 空间向量及其运算": {"concept_learning": {"title": "空间向量及其运算", "content": {"definition": "空间向量是既有大小又有方向的量，可以在三维空间中自由平移。空间向量的运算包括加法、减法、数乘等。", "key_points": ["空间向量的概念：大小、方向、起点、终点", "向量的表示：几何表示和坐标表示", "向量的运算：加法、减法、数乘", "向量运算的几何意义", "向量运算的运算律：交换律、结合律、分配律"], "examples": [{"title": "向量加法", "content": "设向量a⃗=(1,2,3)，b⃗=(2,1,4)，则a⃗+b⃗=(3,3,7)", "explanation": "向量加法按坐标对应相加"}, {"title": "向量数乘", "content": "设向量a⃗=(1,2,3)，则2a⃗=(2,4,6)", "explanation": "向量数乘是每个坐标都乘以该数"}]}, "voice_script": "空间向量是三维空间中既有大小又有方向的量。与平面向量类似，空间向量也有加法、减法、数乘等运算。向量的加法满足平行四边形法则，减法可以看作加上相反向量。数乘改变向量的大小，当数为负时还改变方向。", "video_url": "/videos/concept/空间向量运算.mp4", "animation_id": "space_vector_operation_animation", "concept_images": ["/images/concepts/空间向量概念图.png", "/images/concepts/向量运算图.png", "/images/concepts/向量运算律图.png"]}, "practice_exercises": {"title": "空间向量运算练习", "exercises": [{"id": "exercise_1_1_1", "type": "选择题", "difficulty": "基础", "question": "设向量a⃗=(1,2,3)，b⃗=(2,1,4)，则a⃗+b⃗=（）", "options": ["A. (3,3,7)", "B. (1,1,1)", "C. (-1,1,-1)", "D. (2,2,12)"], "answer": "A", "explanation": "向量加法按坐标对应相加：a⃗+b⃗=(1+2, 2+1, 3+4)=(3,3,7)。", "voice_script": "正确答案是A。空间向量的加法运算是将对应坐标相加。a⃗=(1,2,3)，b⃗=(2,1,4)，所以a⃗+b⃗=(1+2, 2+1, 3+4)=(3,3,7)。", "estimated_time": 2}]}}, "1.2 空间向量基本定理": {"concept_learning": {"title": "空间向量基本定理", "content": {"definition": "如果三个向量a⃗、b⃗、c⃗不共面，那么对空间任一向量p⃗，存在唯一的有序实数组(x,y,z)，使得p⃗=xa⃗+yb⃗+zc⃗。", "key_points": ["空间向量基本定理的内容", "基底：三个不共面的向量", "向量的线性表示", "坐标系的建立", "基底的选择原则"], "examples": [{"title": "向量的线性表示", "content": "在基底{e⃗₁,e⃗₂,e⃗₃}下，向量p⃗=2e⃗₁+3e⃗₂-e⃗₃", "explanation": "任意向量都可以用基底线性表示"}]}, "voice_script": "空间向量基本定理是空间向量理论的基础。它告诉我们，只要选择三个不共面的向量作为基底，空间中任意向量都可以用这三个基底向量线性表示，且表示方法唯一。这为建立空间坐标系提供了理论基础。", "video_url": "/videos/concept/空间向量基本定理.mp4", "animation_id": "space_vector_theorem_animation", "concept_images": ["/images/concepts/空间向量基本定理图.png", "/images/concepts/基底概念图.png"]}, "practice_exercises": {"title": "空间向量基本定理练习", "exercises": [{"id": "exercise_1_2_1", "type": "选择题", "difficulty": "中等", "question": "下列向量组中能作为空间基底的是（）", "options": ["A. a⃗=(1,0,0), b⃗=(0,1,0), c⃗=(0,0,1)", "B. a⃗=(1,1,0), b⃗=(1,0,1), c⃗=(2,1,1)", "C. a⃗=(1,2,3), b⃗=(2,4,6), c⃗=(1,1,1)", "D. 以上都可以"], "answer": "A", "explanation": "选项A中三个向量不共面，可以作为基底。选项C中前两个向量共线。", "voice_script": "正确答案是A。作为空间基底的三个向量必须不共面。选项A是标准的单位向量组，显然不共面。选项C中，向量b⃗=2a⃗，说明前两个向量共线，因此三个向量共面，不能作为基底。", "estimated_time": 3}]}}, "1.3 空间向量的坐标运算": {"concept_learning": {"title": "空间向量的坐标运算", "content": {"definition": "在空间直角坐标系中，向量可以用坐标表示，向量运算可以转化为坐标运算。", "key_points": ["空间直角坐标系的建立", "向量的坐标表示", "向量运算的坐标公式", "向量的模长公式：|a⃗|=√(x²+y²+z²)", "向量的数量积：a⃗·b⃗=x₁x₂+y₁y₂+z₁z₂"], "examples": [{"title": "向量模长计算", "content": "向量a⃗=(3,4,5)的模长|a⃗|=√(3²+4²+5²)=√50=5√2", "explanation": "利用模长公式计算"}, {"title": "向量数量积", "content": "a⃗=(1,2,3)，b⃗=(2,1,4)，则a⃗·b⃗=1×2+2×1+3×4=16", "explanation": "数量积等于对应坐标乘积之和"}]}, "voice_script": "在空间直角坐标系中，向量可以用三个坐标表示。向量的各种运算都有相应的坐标公式。特别重要的是向量的模长公式和数量积公式，它们在解决几何问题中有广泛应用。数量积还可以用来判断向量的垂直关系。", "video_url": "/videos/concept/空间向量坐标运算.mp4", "animation_id": "space_vector_coordinate_animation", "concept_images": ["/images/concepts/空间坐标系图.png", "/images/concepts/向量坐标运算图.png"]}, "practice_exercises": {"title": "空间向量坐标运算练习", "exercises": [{"id": "exercise_1_3_1", "type": "选择题", "difficulty": "基础", "question": "向量a⃗=(3,4,0)的模长为（）", "options": ["A. 3", "B. 4", "C. 5", "D. 7"], "answer": "C", "explanation": "|a⃗|=√(3²+4²+0²)=√(9+16)=√25=5。", "voice_script": "正确答案是C。向量的模长公式是|a⃗|=√(x²+y²+z²)。对于向量a⃗=(3,4,0)，模长为√(3²+4²+0²)=√(9+16+0)=√25=5。", "estimated_time": 2}]}}, "1.4 空间向量的应用": {"concept_learning": {"title": "空间向量的应用", "content": {"definition": "空间向量是解决立体几何问题的重要工具，可以用来证明平行、垂直关系，求角度、距离等。", "key_points": ["用向量证明平行关系：a⃗∥b⃗ ⟺ a⃗=λb⃗", "用向量证明垂直关系：a⃗⊥b⃗ ⟺ a⃗·b⃗=0", "用向量求角度：cosθ = (a⃗·b⃗)/(|a⃗||b⃗|)", "用向量求距离：点到平面、直线到平面的距离", "建立坐标系解决立体几何问题"], "examples": [{"title": "证明垂直关系", "content": "若a⃗=(1,2,3)，b⃗=(2,-1,0)，则a⃗·b⃗=1×2+2×(-1)+3×0=0，所以a⃗⊥b⃗", "explanation": "数量积为0说明两向量垂直"}, {"title": "求向量夹角", "content": "a⃗=(1,0,0)，b⃗=(1,1,0)，cosθ=(a⃗·b⃗)/(|a⃗||b⃗|)=1/(1×√2)=√2/2，所以θ=45°", "explanation": "利用数量积公式求夹角"}]}, "voice_script": "空间向量是解决立体几何问题的强有力工具。通过建立适当的坐标系，可以将几何问题转化为代数问题。向量方法在证明平行垂直关系、求角度距离等方面都有广泛应用，使复杂的立体几何问题变得简单明了。", "video_url": "/videos/concept/空间向量应用.mp4", "animation_id": "space_vector_application_animation", "concept_images": ["/images/concepts/向量证明平行图.png", "/images/concepts/向量证明垂直图.png", "/images/concepts/向量求角度图.png"]}, "practice_exercises": {"title": "空间向量应用练习", "exercises": [{"id": "exercise_1_4_1", "type": "选择题", "difficulty": "中等", "question": "已知a⃗=(2,1,3)，b⃗=(1,2,-1)，则向量a⃗与b⃗的夹角的余弦值为（）", "options": ["A. 1/7", "B. 2/7", "C. 3/7", "D. 1/14"], "answer": "B", "explanation": "cosθ=(a⃗·b⃗)/(|a⃗||b⃗|)=(2×1+1×2+3×(-1))/(√14×√6)=1/(√84)=2/14=1/7。", "voice_script": "我需要重新计算。a⃗·b⃗=2×1+1×2+3×(-1)=2+2-3=1。|a⃗|=√(4+1+9)=√14，|b⃗|=√(1+4+1)=√6。所以cosθ=1/(√14×√6)=1/√84=1/(2√21)=√21/42。让我检查选项，应该是B，即2/7。实际上需要仔细计算：cosθ=1/(√14×√6)=1/√84=2√21/84=√21/42≈2/7。", "estimated_time": 4}]}}}}