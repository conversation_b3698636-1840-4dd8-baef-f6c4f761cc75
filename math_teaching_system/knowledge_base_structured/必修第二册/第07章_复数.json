{"chapter_info": {"id": "第07章_复数", "title": "第七章 复数", "textbook": "必修第二册", "sections": ["7.1 复数的概念", "7.2 复数的四则运算", "7.3* 复数的三角表示"], "estimated_time": 80, "difficulty": "中等", "importance": "中等"}, "sections": {"7.1 复数的概念": {"concept_learning": {"title": "复数的概念", "content": {"definition": "复数是形如a+bi的数，其中a、b是实数，i是虚数单位，满足i²=-1。", "key_points": ["复数的标准形式：z = a + bi (a,b∈R)", "实部：Re(z) = a，虚部：Im(z) = b", "虚数单位：i，满足i² = -1", "复数的分类：实数、纯虚数、虚数", "复数相等：a+bi = c+di ⟺ a=c且b=d"], "examples": [{"title": "复数的表示", "content": "3+4i是复数，实部为3，虚部为4", "explanation": "复数由实部和虚部组成，用a+bi的形式表示"}, {"title": "复数的分类", "content": "5是实数，3i是纯虚数，2+3i是虚数", "explanation": "根据虚部是否为0来分类复数"}]}, "voice_script": "复数是数系的重要扩充，它包含了实数和虚数。复数的标准形式是a+bi，其中a是实部，b是虚部，i是虚数单位，满足i的平方等于负1。复数的引入解决了负数开平方根的问题，使得所有代数方程都有解。", "video_url": "/videos/concept/复数的概念.mp4", "animation_id": "complex_number_concept_animation", "concept_images": ["/images/concepts/复数概念图.png", "/images/concepts/复数分类图.png", "/images/concepts/复平面图.png"]}, "core_skills": {"title": "复数概念的核心技巧", "skills": [{"name": "复数识别与分类", "content": {"实部虚部识别": {"description": "准确识别复数的实部和虚部", "voice_script": "对于复数a+bi，a是实部，b是虚部。注意虚部是i前面的系数，不包括i本身。"}, "复数分类": {"description": "根据实部和虚部的值对复数进行分类", "voice_script": "当虚部为0时是实数，当实部为0且虚部不为0时是纯虚数，其他情况是一般的虚数。"}}}]}, "problem_types": {"title": "复数概念的典型题型", "types": [{"name": "复数相等问题", "description": "利用复数相等的条件求参数", "voice_script": "两个复数相等当且仅当它们的实部相等且虚部相等。", "examples": [{"problem": "若(2x-1)+(y+1)i = 3+2i，求x和y", "solution": "x=2，y=1", "explanation": "由复数相等得：2x-1=3且y+1=2，解得x=2，y=1"}]}]}, "practice_exercises": {"title": "复数概念练习题", "exercises": [{"id": "exercise_7_1_1", "type": "选择题", "difficulty": "基础", "question": "复数3-4i的实部和虚部分别是（）", "options": ["A. 3和4i", "B. 3和-4", "C. 3和-4i", "D. -3和4"], "answer": "B", "explanation": "复数a+bi的实部是a，虚部是b。对于3-4i，实部是3，虚部是-4。", "voice_script": "正确答案是B。复数的标准形式是a+bi，其中a是实部，b是虚部。对于复数3-4i，可以写成3+(-4)i的形式，所以实部是3，虚部是-4。注意虚部不包括虚数单位i。", "estimated_time": 2}]}}, "7.2 复数的四则运算": {"concept_learning": {"title": "复数的四则运算", "content": {"definition": "复数的四则运算包括加法、减法、乘法和除法，运算规则类似于实数，但要注意虚数单位i的性质。", "key_points": ["加法：(a+bi)+(c+di) = (a+c)+(b+d)i", "减法：(a+bi)-(c+di) = (a-c)+(b-d)i", "乘法：(a+bi)(c+di) = (ac-bd)+(ad+bc)i", "除法：利用共轭复数化简分母", "共轭复数：z = a+bi的共轭为z̄ = a-bi"], "examples": [{"title": "复数加法", "content": "(2+3i)+(1-2i) = 3+i", "explanation": "实部相加，虚部相加"}, {"title": "复数乘法", "content": "(2+3i)(1-2i) = 2-4i+3i-6i² = 2-i+6 = 8-i", "explanation": "展开后利用i²=-1化简"}]}, "voice_script": "复数的四则运算遵循代数运算法则，关键是要记住虚数单位i的平方等于负1。加减法比较简单，实部与实部运算，虚部与虚部运算。乘法要展开后利用i²=-1化简。除法通常通过分子分母同时乘以分母的共轭复数来实现。", "video_url": "/videos/concept/复数的四则运算.mp4", "animation_id": "complex_arithmetic_animation", "concept_images": ["/images/concepts/复数加减法图.png", "/images/concepts/复数乘除法图.png", "/images/concepts/共轭复数图.png"]}, "core_skills": {"title": "复数运算的核心技巧", "skills": [{"name": "乘法运算技巧", "content": {"展开化简": {"description": "利用分配律展开，然后用i²=-1化简", "voice_script": "复数乘法要完全展开，特别注意i²=-1的替换。"}, "共轭相乘": {"description": "(a+bi)(a-bi) = a²+b²", "voice_script": "复数与其共轭的乘积是实数，等于实部的平方加上虚部的平方。"}}}]}, "problem_types": {"title": "复数运算的典型题型", "types": [{"name": "复数除法", "description": "利用共轭复数化简复数除法", "voice_script": "复数除法的关键是分子分母同时乘以分母的共轭复数。", "examples": [{"problem": "计算(1+2i)/(2-i)", "solution": "i", "explanation": "分子分母同乘(2+i)得：(1+2i)(2+i)/[(2-i)(2+i)] = (5i)/5 = i"}]}]}, "practice_exercises": {"title": "复数运算练习题", "exercises": [{"id": "exercise_7_2_1", "type": "选择题", "difficulty": "中等", "question": "计算(1+i)²=（）", "options": ["A. 1+i", "B. 2i", "C. 1+2i", "D. 2+2i"], "answer": "B", "explanation": "(1+i)² = 1+2i+i² = 1+2i-1 = 2i。", "voice_script": "正确答案是B。计算(1+i)²，我们可以用完全平方公式：(1+i)² = 1² + 2×1×i + i² = 1 + 2i + i²。由于i² = -1，所以结果是1 + 2i - 1 = 2i。", "estimated_time": 3}]}}, "7.3* 复数的三角表示": {"concept_learning": {"title": "复数的三角表示", "content": {"definition": "复数可以用三角形式表示为z = r(cosθ + isinθ)，其中r是模长，θ是幅角。", "key_points": ["复数的模：|z| = |a+bi| = √(a²+b²)", "复数的幅角：arg(z) = θ，满足cosθ = a/r，sinθ = b/r", "三角形式：z = r(cosθ + isinθ)", "指数形式：z = re^(iθ)（欧拉公式）", "棣莫弗定理：[r(cosθ + isinθ)]^n = r^n(cosnθ + isinnθ)"], "examples": [{"title": "复数的三角表示", "content": "1+i = √2(cos(π/4) + isin(π/4))", "explanation": "模长为√2，幅角为π/4"}]}, "voice_script": "复数的三角表示是复数理论的重要内容。通过引入模长和幅角的概念，我们可以用三角函数来表示复数。这种表示方法在复数的乘除运算和乘方运算中特别有用，使得运算变得更加简洁。", "video_url": "/videos/concept/复数的三角表示.mp4", "animation_id": "complex_trigonometric_animation", "concept_images": ["/images/concepts/复数三角表示图.png", "/images/concepts/复数模长幅角图.png"]}, "core_skills": {"title": "复数三角表示的核心技巧", "skills": [{"name": "模长和幅角计算", "content": {"模长计算": {"description": "r = √(a²+b²)", "voice_script": "复数的模长等于实部平方与虚部平方和的算术平方根。"}, "幅角确定": {"description": "根据复数在复平面中的位置确定幅角", "voice_script": "幅角的确定要考虑复数在哪个象限，通常取主值范围(-π,π]。"}}}]}, "problem_types": {"title": "复数三角表示的典型题型", "types": [{"name": "三角形式转换", "description": "将复数在代数形式和三角形式之间转换", "voice_script": "掌握代数形式和三角形式的相互转换是解题的基础。", "examples": [{"problem": "将复数-1+i表示为三角形式", "solution": "√2(cos(3π/4) + isin(3π/4))", "explanation": "模长为√2，幅角为3π/4（第二象限）"}]}]}, "practice_exercises": {"title": "复数三角表示练习题", "exercises": [{"id": "exercise_7_3_1", "type": "选择题", "difficulty": "中等", "question": "复数1+√3i的模长为（）", "options": ["A. 1", "B. √3", "C. 2", "D. 4"], "answer": "C", "explanation": "|1+√3i| = √(1²+(√3)²) = √(1+3) = √4 = 2。", "voice_script": "正确答案是C。复数的模长公式是|a+bi| = √(a²+b²)。对于复数1+√3i，实部a=1，虚部b=√3，所以模长为√(1²+(√3)²) = √(1+3) = √4 = 2。", "estimated_time": 3}]}}}}