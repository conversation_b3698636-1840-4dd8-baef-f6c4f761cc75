{"section_info": {"id": "4.4 对数函数", "number": "4.4", "title": "对数函数", "full_title": "4.4 对数函数", "chapter_id": "第04章_指数函数与对数函数", "chapter_title": "第四章 指数函数与对数函数", "textbook": "必修第一册", "estimated_time": 30, "difficulty": "中等", "importance": "高"}, "content": {"concept_learning": {"title": "对数函数", "content": {"definition": "形如y = log_a x (a>0且a≠1)的函数叫做对数函数，其中x是自变量。", "key_points": ["对数函数的定义：y = log_a x (a>0且a≠1)", "定义域：(0,+∞)，值域：R", "当a>1时，函数单调递增；当0<a<1时，函数单调递减", "图象都过点(1,0)", "对数函数与指数函数互为反函数"], "examples": [{"title": "对数函数性质", "content": "y = log_2 x 在(0,+∞)上单调递增", "explanation": "底数大于1的对数函数单调递增"}]}, "voice_script": "对数函数是形如y = log_a x的函数。对数函数的定义域是(0,+∞)，值域是R。对数函数与指数函数互为反函数，它们的图象关于直线y=x对称。对数函数在科学计算、地震强度测量等方面有重要应用。", "video_url": "/videos/concept/对数函数.mp4", "animation_id": "logarithmic_function_animation", "concept_images": ["/images/concepts/对数函数图像.png", "/images/concepts/对数函数性质表.png"]}, "core_skills": {"title": "对数函数 - 核心技巧", "content": "这里是对数函数的核心技巧内容，正在整理中...", "voice_script": "欢迎学习对数函数的核心技巧内容。", "estimated_time": 10}, "problem_types": {"title": "对数函数 - 题型高手", "content": "这里是对数函数的题型高手内容，正在整理中...", "voice_script": "欢迎学习对数函数的题型高手内容。", "estimated_time": 10}, "practice_exercises": {"title": "对数函数练习", "exercises": [{"id": "exercise_4_4_1", "type": "选择题", "difficulty": "基础", "question": "函数y = log_2 x的定义域为（）", "options": ["<PERSON><PERSON> R", "B. (0,+∞)", "C. [0,+∞)", "D. (1,+∞)"], "answer": "B", "explanation": "对数函数的真数必须大于0，所以定义域为(0,+∞)。", "voice_script": "正确答案是B。对数函数y = log_a x的定义域是(0,+∞)，这是因为对数的真数必须大于0。当x≤0时，log_a x没有意义。所以函数y = log_2 x的定义域是(0,+∞)。", "estimated_time": 2}]}}}