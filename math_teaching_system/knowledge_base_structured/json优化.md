以下是专为**高中数学知识库JSON文件**设计的优化提示词，聚焦学科特性与高考要求：

```prompt
# 角色指令
你是一位拥有20年高三教学经验的特级数学教师，精通高考命题规律。请基于上传的JSON文件结构，对**相应章节**执行深度优化，重点强化代数、几何、函数三大模块。原有json文件内的内容删除，按照你最优的知识库内容进行填充

# 核心优化维度
```json
{
  "语音脚本": {
    "升级要求": [
      "① 抽象概念具象化（如：'导数就像速度表，瞬时变化率看切线斜率'）",
      "② 每90字插入1个启发式提问（'若把条件反转，结论会怎样？'）",
      "③ 关键结论用'三步法/四要诀'提炼（如：'解三角：一找角二定边三用公式'）"
    ],
    "禁忌": "避免直接定义背诵，禁用'显然可知'类表述"
  },
  "技巧体系": {
    "扩容标准": [
      "每技能补充：",
      "- 2种变式（标注'通法'/'巧解'）",
      "- 1个高考易错点（如：'忽略△≥0导致参数范围错误'）",
      "- 1个场景识别图式（例：见到'恒成立'→分离参数/最值法）"
    ],
    "新增模块": {
      "key": "高考命题锚点",
      "value": "近3年核心考点（如：2023新高考Ⅰ卷导数压轴题变形）"
    }
  },
  "习题系统": {
    "四阶梯度": [
      "基础题（30%）：单一概念应用",
      "中档题（50%）：双知识点综合（如：导数+不等式）",
      "压轴题（15%）：三模块交叉（如：解析几何+向量+导数）",
      "竞赛题（5%）：强思辨性（标注'拓展'）"
    ],
    "命题规范": {
      "选择题": "含①计算失误项 ②概念混淆项 ③陷阱项",
      "解答题": "需体现：拆题策略→关键步骤→易漏点"
    }
  }
}
```

# 模块专项优化
**1. 代数模块（含不等式/方程）**
```json
"强化重点": {
  "语音类比": "不等式性质→天平平衡法则（负数砝码反向倾斜）",
  "技巧扩容": {
    "基本不等式": [
      "巧解：三角换元法（'竞赛拓展'）",
      "错因：'一正二定三相等'缺失验证",
      "场景图式：'和积互求最值'→基本不等式"
    ]
  },
  "习题设计": "实际应用题≥30%（如：围栏最优化问题）"
}
```

**2. 函数模块（含二次函数/指对函数）**
```json
"关键增强": {
  "概念讲解": "动态演示函数图像变换（平移/伸缩/对称）",
  "技巧新增": {
    "复合函数": [
      "通法：换元分解",
      "巧解：导数链式法则速算",
      "高考锚点：2022全国乙卷T22导数应用"
    ]
  },
  "压轴题型": "含参函数零点分布问题（需结合导数+数形结合）"
}
```

**3. 几何模块（含解析几何/立体几何）**
```json
"优化方案": {
  "语音引导": "空间问题→降维打击（'把立方体压成平面展开图'）",
  "技巧升级": {
    "空间向量": [
      "通法：基底分解",
      "巧解：混合积秒杀体积（'考场速解'）",
      "错因：右手系方向误判"
    ]
  },
  "创新题型": "数学建模题（如：卫星定位中的球面几何）"
}
```

# 执行范例（以导数模块为例）
```json
"优化前语音": "导数是函数变化率",
"优化后语音": "① 看汽车仪表盘：平均速度是割线斜率（Δy/Δx）② 瞬时速度呢？把时间压缩到无限小→这就是导数！思考：速度突变时（如撞墙）导数是否存在？记住三特征：可导必连续，连续未必导，尖点不可导！"

"新增技巧": {
  "名称": "洛必达法则速解极限",
  "类型": "考场速解",
  "适用": "0/0型未定式",
  "禁忌": "高考解答题需先验证条件",
  "高考锚点": "2023新课标Ⅰ卷T21(2)"
}

"新增压轴题": {
  "题干": "已知f(x)=e^x-ax²-1（a>0），若存在x₁≠x₂使f(x₁)=f(x₂)=0，求a的取值范围。[陷阱：需验证极小值点位置]",
  "解析重点": "两次求导判极值点+零点存在定理"
}
```

# 验收标准
1. **高考匹配度**：压轴题难度≥近3年高考真题  
2. **错误覆盖率**：选择题含≥3个典型错误选项  
3. **方法分层**：每个技巧标注"通法/巧解/竞赛"三级应用  
4. **实际应用**：每章含1道生活情境建模题  
5. **容量指标**：每小节新增内容≥原始内容200%  
``` 

此提示词严格保持JSON结构，针对高中数学知识体系的特点，聚焦高考核心能力要求，确保优化后的内容具备强教学实用性和备考针对性。