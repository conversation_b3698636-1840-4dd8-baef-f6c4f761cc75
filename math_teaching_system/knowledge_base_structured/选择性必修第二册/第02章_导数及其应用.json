{"chapter_info": {"id": "第02章_导数及其应用", "title": "第二章 导数及其应用", "textbook": "选择性必修第二册", "sections": ["2.1 导数的概念及其几何意义", "2.2 导数的运算", "2.3 导数在研究函数中的应用", "2.4 生活中的优化问题举例"], "estimated_time": 100, "difficulty": "困难", "importance": "高"}, "sections": {"2.1 导数的概念及其几何意义": {"concept_learning": {"title": "导数的概念及其几何意义", "content": {"definition": "函数y=f(x)在点x₀处的导数是函数在该点处切线的斜率，反映了函数在该点的瞬时变化率。", "key_points": ["导数的定义：f'(x₀) = lim[h→0] [f(x₀+h)-f(x₀)]/h", "几何意义：函数图象在某点处的切线斜率", "物理意义：瞬时变化率", "导函数：f'(x) = lim[h→0] [f(x+h)-f(x)]/h", "可导与连续的关系：可导必连续，连续不一定可导"], "examples": [{"title": "导数的几何意义", "content": "函数f(x)=x²在点(1,1)处的导数f'(1)=2，表示切线斜率为2", "explanation": "导数的几何意义是切线斜率"}, {"title": "瞬时速度", "content": "位移函数s(t)=5t²，在t=2时的瞬时速度v=s'(2)=20m/s", "explanation": "导数的物理意义是瞬时变化率"}]}, "voice_script": "导数是微积分的核心概念之一。它描述了函数在某一点的瞬时变化率，几何上表示为函数图象在该点的切线斜率。导数概念的建立为研究函数的性质提供了强有力的工具。", "video_url": "/videos/concept/导数的概念.mp4", "animation_id": "derivative_concept_animation", "concept_images": ["/images/concepts/导数定义图.png", "/images/concepts/导数几何意义图.png", "/images/concepts/切线斜率图.png"]}, "core_skills": {"title": "导数概念技巧", "skills": [{"name": "导数定义应用", "content": {"极限计算": {"description": "利用导数定义计算导数值", "voice_script": "用导数定义计算导数需要熟练掌握极限的计算方法。"}, "几何应用": {"description": "利用导数的几何意义求切线方程", "voice_script": "导数的几何意义是求切线方程的关键。"}}}]}, "problem_types": {"title": "导数概念的典型题型", "types": [{"name": "切线方程", "description": "利用导数求函数图象的切线方程", "voice_script": "求切线方程需要先求出切点处的导数值，即切线斜率。", "examples": [{"problem": "求函数f(x)=x³在点(1,1)处的切线方程", "solution": "y = 3x - 2", "explanation": "f'(1) = 3，切线方程为y-1 = 3(x-1)"}]}]}, "practice_exercises": {"title": "导数概念练习题", "exercises": [{"id": "exercise_2_1_1", "type": "选择题", "difficulty": "中等", "question": "函数f(x)=2x²在x=1处的导数为（）", "options": ["A. 2", "B. 4", "C. 6", "D. 8"], "answer": "B", "explanation": "f'(x) = 4x，所以f'(1) = 4。", "voice_script": "正确答案是B。对于函数f(x)=2x²，利用幂函数的导数公式，f'(x) = 2×2x = 4x。因此在x=1处的导数f'(1) = 4×1 = 4。", "estimated_time": 3}]}}, "2.2 导数的运算": {"concept_learning": {"title": "导数的运算", "content": {"definition": "导数的运算包括基本函数的导数公式和导数的运算法则。", "key_points": ["基本导数公式：(xⁿ)' = nxⁿ⁻¹, (eˣ)' = eˣ, (ln x)' = 1/x", "四则运算法则：(u±v)' = u'±v', (uv)' = u'v + uv', (u/v)' = (u'v-uv')/v²", "复合函数求导：[f(g(x))]' = f'(g(x))·g'(x)", "反函数求导：若y=f(x)，则dx/dy = 1/(dy/dx)", "隐函数求导：对方程两边同时求导"], "examples": [{"title": "复合函数求导", "content": "y = (2x+1)³，y' = 3(2x+1)²·2 = 6(2x+1)²", "explanation": "利用复合函数求导法则"}, {"title": "乘积法则", "content": "y = x²·eˣ，y' = 2x·eˣ + x²·eˣ = eˣ(2x + x²)", "explanation": "利用乘积法则求导"}]}, "voice_script": "导数的运算是微积分的基础技能。要熟练掌握基本函数的导数公式和各种运算法则，特别是复合函数的求导法则。这些是解决复杂导数问题的基础。", "video_url": "/videos/concept/导数的运算.mp4", "animation_id": "derivative_calculation_animation", "concept_images": ["/images/concepts/导数公式表.png", "/images/concepts/复合函数求导图.png", "/images/concepts/导数运算法则图.png"]}, "core_skills": {"title": "导数运算技巧", "skills": [{"name": "复合函数求导", "content": {"链式法则": {"description": "掌握复合函数的求导方法", "voice_script": "复合函数求导要从外到内逐层求导，然后相乘。"}, "技巧应用": {"description": "灵活运用各种求导技巧", "voice_script": "不同类型的函数要选择合适的求导方法。"}}}]}, "problem_types": {"title": "导数运算的典型题型", "types": [{"name": "复杂函数求导", "description": "对复杂的复合函数、隐函数等求导", "voice_script": "复杂函数求导要分析函数结构，选择合适的求导法则。", "examples": [{"problem": "求y = ln(x² + 1)的导数", "solution": "y' = 2x/(x² + 1)", "explanation": "利用复合函数求导法则"}]}]}, "practice_exercises": {"title": "导数运算练习题", "exercises": [{"id": "exercise_2_2_1", "type": "选择题", "difficulty": "中等", "question": "函数y = x·ln x的导数为（）", "options": ["A. ln x", "B. 1 + ln x", "C. x + ln x", "D. x·ln x"], "answer": "B", "explanation": "利用乘积法则：y' = 1·ln x + x·(1/x) = ln x + 1。", "voice_script": "正确答案是B。这是一个乘积函数，需要用乘积法则求导。设u = x，v = ln x，则u' = 1，v' = 1/x。根据乘积法则(uv)' = u'v + uv'，得到y' = 1·ln x + x·(1/x) = ln x + 1。", "estimated_time": 4}]}}}}