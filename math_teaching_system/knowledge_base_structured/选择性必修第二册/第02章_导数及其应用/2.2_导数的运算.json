{"section_info": {"id": "2.2 导数的运算", "number": "2.2", "title": "导数的运算", "full_title": "2.2 导数的运算", "chapter_id": "第02章_导数及其应用", "chapter_title": "第二章 导数及其应用", "textbook": "选择性必修第二册", "estimated_time": 30, "difficulty": "困难", "importance": "高"}, "content": {"concept_learning": {"title": "导数的运算", "content": {"definition": "导数的运算包括基本函数的导数公式和导数的运算法则。", "key_points": ["基本导数公式：(xⁿ)' = nxⁿ⁻¹, (eˣ)' = eˣ, (ln x)' = 1/x", "四则运算法则：(u±v)' = u'±v', (uv)' = u'v + uv', (u/v)' = (u'v-uv')/v²", "复合函数求导：[f(g(x))]' = f'(g(x))·g'(x)", "反函数求导：若y=f(x)，则dx/dy = 1/(dy/dx)", "隐函数求导：对方程两边同时求导"], "examples": [{"title": "复合函数求导", "content": "y = (2x+1)³，y' = 3(2x+1)²·2 = 6(2x+1)²", "explanation": "利用复合函数求导法则"}, {"title": "乘积法则", "content": "y = x²·eˣ，y' = 2x·eˣ + x²·eˣ = eˣ(2x + x²)", "explanation": "利用乘积法则求导"}]}, "voice_script": "导数的运算是微积分的基础技能。要熟练掌握基本函数的导数公式和各种运算法则，特别是复合函数的求导法则。这些是解决复杂导数问题的基础。", "video_url": "/videos/concept/导数的运算.mp4", "animation_id": "derivative_calculation_animation", "concept_images": ["/images/concepts/导数公式表.png", "/images/concepts/复合函数求导图.png", "/images/concepts/导数运算法则图.png"]}, "core_skills": {"title": "导数运算技巧", "skills": [{"name": "复合函数求导", "content": {"链式法则": {"description": "掌握复合函数的求导方法", "voice_script": "复合函数求导要从外到内逐层求导，然后相乘。"}, "技巧应用": {"description": "灵活运用各种求导技巧", "voice_script": "不同类型的函数要选择合适的求导方法。"}}}]}, "problem_types": {"title": "导数运算的典型题型", "types": [{"name": "复杂函数求导", "description": "对复杂的复合函数、隐函数等求导", "voice_script": "复杂函数求导要分析函数结构，选择合适的求导法则。", "examples": [{"problem": "求y = ln(x² + 1)的导数", "solution": "y' = 2x/(x² + 1)", "explanation": "利用复合函数求导法则"}]}]}, "practice_exercises": {"title": "导数运算练习题", "exercises": [{"id": "exercise_2_2_1", "type": "选择题", "difficulty": "中等", "question": "函数y = x·ln x的导数为（）", "options": ["A. ln x", "B. 1 + ln x", "C. x + ln x", "D. x·ln x"], "answer": "B", "explanation": "利用乘积法则：y' = 1·ln x + x·(1/x) = ln x + 1。", "voice_script": "正确答案是B。这是一个乘积函数，需要用乘积法则求导。设u = x，v = ln x，则u' = 1，v' = 1/x。根据乘积法则(uv)' = u'v + uv'，得到y' = 1·ln x + x·(1/x) = ln x + 1。", "estimated_time": 4}]}}}