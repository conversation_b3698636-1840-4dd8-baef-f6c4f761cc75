{"chapter_info": {"id": "第01章_数列", "title": "第一章 数列", "textbook": "选择性必修第二册", "sections": ["1.1 数列的概念与简单表示法", "1.2 等差数列", "1.3 等差数列的前n项和", "1.4 等比数列", "1.5 等比数列的前n项和"], "estimated_time": 120, "difficulty": "中等", "importance": "高"}, "sections": {"1.1 数列的概念与简单表示法": {"concept_learning": {"title": "数列的概念与简单表示法", "content": {"definition": "数列是按照一定顺序排列的一列数，数列中的每一个数叫做这个数列的项。", "key_points": ["数列的定义：按一定顺序排列的一列数", "数列的项：a₁, a₂, a₃, ..., aₙ, ...", "数列的通项公式：aₙ = f(n)", "数列的表示方法：列举法、通项公式法、递推公式法", "数列的分类：有穷数列、无穷数列"], "examples": [{"title": "数列的表示", "content": "数列1, 3, 5, 7, 9, ...的通项公式为aₙ = 2n-1", "explanation": "这是一个奇数数列，第n项等于2n-1"}, {"title": "递推公式", "content": "a₁ = 1, aₙ₊₁ = aₙ + 2，得到数列1, 3, 5, 7, ...", "explanation": "通过递推关系确定数列的各项"}]}, "voice_script": "数列是数学中重要的概念，它是按照一定顺序排列的一列数。数列的表示方法有多种，包括列举法、通项公式法和递推公式法。掌握数列的基本概念对学习等差数列和等比数列很重要。", "video_url": "/videos/concept/数列的概念.mp4", "animation_id": "sequence_concept_animation", "concept_images": ["/images/concepts/数列概念图.png", "/images/concepts/数列表示法图.png"]}, "core_skills": {"title": "数列表示技巧", "skills": [{"name": "通项公式求解", "content": {"观察规律": {"description": "通过观察数列的规律找出通项公式", "voice_script": "观察数列各项之间的关系，寻找规律是求通项公式的基本方法。"}, "递推关系": {"description": "利用递推关系建立数列", "voice_script": "递推公式给出了相邻项之间的关系，是定义数列的重要方法。"}}}]}, "problem_types": {"title": "数列概念的典型题型", "types": [{"name": "求通项公式", "description": "根据数列的前几项求出通项公式", "voice_script": "求通项公式要观察数列的规律，找出项数n与项值aₙ的关系。", "examples": [{"problem": "数列2, 5, 8, 11, 14, ...的通项公式", "solution": "aₙ = 3n - 1", "explanation": "观察可知这是公差为3的等差数列，首项为2"}]}]}, "practice_exercises": {"title": "数列概念练习题", "exercises": [{"id": "exercise_1_1_1", "type": "选择题", "difficulty": "基础", "question": "数列1, 4, 7, 10, 13, ...的第10项是（）", "options": ["A. 28", "B. 29", "C. 30", "D. 31"], "answer": "A", "explanation": "这是首项为1，公差为3的等差数列，a₁₀ = 1 + (10-1)×3 = 28。", "voice_script": "正确答案是A。这个数列的通项公式是aₙ = 3n - 2。我们可以验证：a₁ = 3×1 - 2 = 1，a₂ = 3×2 - 2 = 4，依此类推。所以第10项a₁₀ = 3×10 - 2 = 28。", "estimated_time": 3}]}}, "1.2 等差数列": {"concept_learning": {"title": "等差数列", "content": {"definition": "如果一个数列从第二项起，每一项与它的前一项的差等于同一个常数，那么这个数列就叫做等差数列。", "key_points": ["等差数列的定义：aₙ₊₁ - aₙ = d（常数）", "公差d：相邻两项的差", "通项公式：aₙ = a₁ + (n-1)d", "等差中项：若a, A, b成等差数列，则A = (a+b)/2", "等差数列的性质：若m+n = p+q，则aₘ + aₙ = aₚ + aᵩ"], "examples": [{"title": "等差数列的通项", "content": "数列3, 7, 11, 15, ...是等差数列，a₁=3，d=4，aₙ=3+4(n-1)=4n-1", "explanation": "利用等差数列通项公式求解"}]}, "voice_script": "等差数列是最基本的数列类型之一。它的特点是相邻两项的差为常数，这个常数叫做公差。掌握等差数列的通项公式和性质对解决相关问题很重要。", "video_url": "/videos/concept/等差数列.mp4", "animation_id": "arithmetic_sequence_animation", "concept_images": ["/images/concepts/等差数列图.png", "/images/concepts/等差数列性质图.png"]}, "core_skills": {"title": "等差数列技巧", "skills": [{"name": "通项公式应用", "content": {"基本公式": {"description": "aₙ = a₁ + (n-1)d", "voice_script": "等差数列的通项公式是解决等差数列问题的基础工具。"}, "性质应用": {"description": "利用等差数列的性质简化计算", "voice_script": "等差数列有很多重要性质，合理运用可以简化计算过程。"}}}]}, "problem_types": {"title": "等差数列的典型题型", "types": [{"name": "求项和公差", "description": "已知等差数列的某些条件，求指定项或公差", "voice_script": "这类问题通常利用等差数列的通项公式建立方程求解。", "examples": [{"problem": "等差数列{aₙ}中，a₃=7，a₇=15，求a₁和d", "solution": "a₁=1，d=2", "explanation": "利用通项公式建立方程组求解"}]}]}, "practice_exercises": {"title": "等差数列练习题", "exercises": [{"id": "exercise_1_2_1", "type": "选择题", "difficulty": "中等", "question": "等差数列{aₙ}中，a₁=2，d=3，则a₁₀=（）", "options": ["A. 29", "B. 30", "C. 31", "D. 32"], "answer": "A", "explanation": "a₁₀ = a₁ + 9d = 2 + 9×3 = 29。", "voice_script": "正确答案是A。利用等差数列通项公式aₙ = a₁ + (n-1)d，代入a₁=2，d=3，n=10，得到a₁₀ = 2 + (10-1)×3 = 2 + 27 = 29。", "estimated_time": 3}]}}}}