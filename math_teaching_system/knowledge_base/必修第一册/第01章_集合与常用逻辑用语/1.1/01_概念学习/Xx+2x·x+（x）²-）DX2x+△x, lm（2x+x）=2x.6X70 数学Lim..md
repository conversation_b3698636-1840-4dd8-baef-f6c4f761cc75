---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 120
source_file: 普通高中教科书·数学（A版）选择性必修 第二册.md
title: 普通高中教科书·数学（A版）选择性必修 第二册
type: concept
---

Xx+2x·x+（x）²-）DX2x+△x, lm（2x+x）=2x.6X70 数学Lim.  
[fom±g）=fx）±9x）;[fmgm)]=fxgx）+fgm;f9）-f9）（9[9m]² 选择性必修fo]'yx=y·u. g() 第二册  
y↑ y=f(x2VpPox)XooA版

#

选择性必修

# 第二册

人民教育出版社课程教材研究所编著中学数学课程教材研究开发中心

# A版

主编：章建跃李增沪副主编：李勇李海东李龙才本册主编：李龙才周远方

编写人员：李龙才宋莉莉张艳娇周远方桂思铭郭慧清

责任编辑：张艳娇美术编辑：王俊宏

?

# 本册导引

本书根据《普通高中数学课程标准（2017年版）》编写，包括“数列”“一元函数的导数及其应用”两章内容.

数列是一类特殊的函数，是数学重要的研究对象，是研究其他函数的基本工具，在日常生活中也有着广泛的应用．在“数列”中，同学们将学习数列的概念和表示方法；研究两类特殊的数列一—等差数列和等比数列，探索它们的取值规律，建立它们的通项公式、前 $n$ 项和公式；在运用等差数列、等比数列解决实际问题和数学问题的过程中，体会数学模型的现实意义与应用；通过建立等差数列与一次函数、等比数列与指数函数的联系，感受数列与函数的共性和差异，体会数学的整体性．同学们还将学习一种特殊的证明方法一—数学归纳法，并用它证明与数列相关的一些简单命题.

导数是微积分的核心内容之一，是现代数学的基本概念，蕴含着微积分的基本思想；导数定量地刻画了函数的局部变化，是研究函数性质的基本工具．在“一元函数的导数及其应用”中，同学们将通过丰富的实际背景，经历由平均变化率过渡到瞬时变化率的过程，学习导数的概念及其几何意义，导数的运算法则；并在运用导数研究函数的性质、解决简单的实际问题的过程中，体会导数的内涵与思想，感受导数的作用.

祝愿同学们通过本册书的学习，不但学到更多的数学知识，而且在数学能力、数学核心素养等方面都有较大的提高，并培养起更高的数学学习兴趣，形成对数学的更加全面的认识.

# 目录

![](images/8262888e76a2ca9d9f30c85e5cfdd8c76f31a73e61e8bfebf36698bf2dfb7992.jpg)

# 第四章数列

4.1数列的概念 2  
阅读与思考斐波那契数列 10  
4.2等差数列 12  
4.3等比数列 27  
阅读与思考中国古代数学家求数列和的方法42  
$4 . 4 ^ { \times }$ 数学归纳法 44  
小结… 53  
复习参考题4 54

![](images/60c8af10c2142f1d13a526a18635a61d4b6bc3f555ad55381339538168fbfa38.jpg)

# 第五章一元函数的导数及其应用 58

5.1 导数的概念及其意义 59  
5.2 导数的运算 72探究与发现牛顿法—用导数方法求方程的近似解82

# 5.3导数在研究函数中的应用 .. 84

信息技术应用 图形技术与函数性质 100  
文献阅读与数学写作＊微积分的创立与发展 101  
小结 102  
复习参考题5 103

部分中英文词汇索……… .…105

# 第四章数列

对数列的研究源于现实生产、生活的需要．例如，一棵树在某一时刻的高度是 $2 \textrm { m }$ ，如果在每年的同一时刻都记录下这棵树的高度，并按先后顺序排列起来，就得到一列数，人们常用这样的一列数有序地表达一类事物，或者记录一个过程．像这样按照确定的顺序排列的一列数称为数列．如果用正整数表示事物发展过程的先后顺序，并且把这样的正整数看作自变量的取值，把事物的对应数值看作相应的函数值，那么数列就是定义在正整数集（或正整数集的有限子集）上的一类离散函数．数列无论在理论研究还是在实际应用中都非常重要.

本章我们将学习数列的概念和表示方法，并研究两类特殊的数列一 -等差数列和等比数列，探索它们的取值规律，建立它们的通项公式、前 $n$ 项和公式，并应用它们解决一些问题，我们将把数列看成一类特殊的函数，并用函数的思想方法研究数列．我们还将学习数学归纳法，这是一种证明与正整数有关的数学命题的特殊方法．在本章的学习中，我们可以体验通过数学抽象获得一个数学对象，并通过数学运算、逻辑推理等进行研究的过程和方法；通过建立数学模型刻画具有递推规律的事物，提高解决实际问题的能力.

![](images/2dd6ddebbc646bbf531afac668463e04931f8f49a066ed60b961202f40682b7e.jpg)

# 4.1数列的概念

在现实生活和数学学习中，我们经常需要根据问题的意义，通过对一些数据按特定顺序排列的方法来刻画研究对象．例如：

1.王芳从1岁到17岁，每年生日那天测量身高，将这些身高数据（单位：cm）依次排成一列数：

75，87，96，103，110，116，120，128，138,  
145，153，158，160，162，163，165，168.  
6500万 1168 163  
160

记王芳第 $_ i$ 岁时的身高为 $h _ { i }$ ，那么 $h _ { 1 } = 7 5$ ， $h _ { 2 } = 8 7$ ，…， $h _ { 1 7 } = 1 6 8$ 我们发现， $h _ { i }$ 中的 $i$ 反映了身高按岁数从1到17的顺序排列时的确定位置，即 $h _ { 1 } = 7 5$ 是排在第1位的数， $h _ { 2 } = 8 7$ 是排在第2位的数 $\cdots h _ { 1 7 } =$ 168是排在第17位的数，它们之间不能交换位置，所以， $\textcircled{1}$ 是具有确定顺序的一列数.

2.在两河流域发掘的一块泥版（编号K90，约产生于公元前7世纪）上，有一列依次表示15天中从第1天到第15天每天月亮可见部分的数：

5，10，20，40，80，96，112，128,  
144，160，176，192，208，224，240.

记第 $_ i$ 天月亮可见部分的数为 $s _ { i }$ ，那么$s _ { 1 } = 5$ ， $s _ { 2 } = 1 0$ ，…， $s _ { 1 5 } = 2 4 0$ 这里， $s _ { i }$ 中的 $i$ 反映了月亮可见部分的数按日期从1到15的顺序排列时的确定位置，即 $s _ { 1 } = 5$ 是排在第1位的数， $s _ { 2 } = 1 0$ 是排在第2位的数 $s _ { 1 5 } = 2 4 0$ 是排在第15位的数，它们之间不能交换位置．所以，$\textcircled{2}$ 也是具有确定顺序的一列数.

BBP 14 15

#

①把满月分成240份，则从初一到十五每天月亮的可见部分可用一个代表份数的数来表示。

3. $- \frac { 1 } { 2 }$ 的 $_ n$ 次幂按1次幂、2次幂、3次幂、4次幂…依次排成一列数：

$$
- { \frac { 1 } { 2 } } , \ { \frac { 1 } { 4 } } , \ - { \frac { 1 } { 8 } } , \ { \frac { 1 } { 1 6 } } , \ \cdots .
$$

# 思考

你能仿照上面的叙述，说明 $\textcircled{3}$ 也是具有确定顺序的一列数吗？

# 归纳

上述例子的共同特征是什么？

一般地，我们把按照确定的顺序排列的一列数称为数列(sequenceofnumber)，数列中的每一个数叫做这个数列的项．数列的第一个位置上的数叫做这个数列的第1项，常用符号 $\boldsymbol { a } _ { 1 }$ 表示，第二个位置上的数叫做这个数列的第2项，用 $a _ { 2 }$ 表示…第 $n$ 个位置上的数叫做这个数列的第 $n$ 项，用 $\boldsymbol { a } _ { n }$ 表示：其中第1项也叫做首项。 $\textcircled{1}$ 是按年龄从小到大的顺序排列的， $\textcircled{2}$ 是按每月的日期从小到大的顺序排列的，$\textcircled{3}$ 是按幂指数从小到大的顺序排列的，它们都是从第1项开始的.

# d

项数有限的数列叫做 有穷数列，项数无限的数 列叫做无穷数列.

数列的一般形式是

$$
a _ { 1 } , ~ a _ { 2 } , ~ \cdots , ~ a _ { n } , ~ \cdots ,
$$

简记为 $\left\{ a _ { n } \right\}$

由于数列 $\left\{ a _ { n } \right\}$ 中的每一项 $\boldsymbol { a } _ { n }$ 与它的序号 $n$ 有下面的对应关系：

![](images/58ecbfbcf941531e2bd91b8251ea2109c7df159dce145e706c9eedf91a29e3f5.jpg)

所以数列 $\left\{ a _ { n } \right\}$ 是从正整数集 $\mathbf { N } ^ { \ast }$ （或它的有限子集{1，$2 , \cdots , n \}$ ）到实数集 $\mathbf { R }$ 的函数，其自变量是序号 $n$ ，对应的函数值是数列的第 $n$ 项 $a _ { n }$ ，记为 $a _ { n } = f ( n )$ ，也就是说，当自变量从1开始，按照从小到大的顺序依次取值时，对应的一列函数值 $f ( 1 )$ ， $f ( 2 )$ ，…， $f ( n )$ ，…就是数列 $\{ a _ { n } \}$ 另一方面，对于函数 $y { = } f ( x )$ ，如果 $f ( n ) \quad ( n \in \mathbf { N } ^ { * }$ ）有意义，那么

以前我们学过的函数的自变量通常是连续变化的，而数列是自变量为离散的数的函数。

$$
f ( 1 ) , \ f ( 2 ) , \ \cdots , \ f ( n ) , \ \cdots
$$

构成了一个数列 $\{ f ( n ) \}$

与其他函数一样，数列也可以用表格和图象来表示．例如，数列 $\textcircled{1}$ 可以表示为表4.1-1.

表4.1-1  

<html><body><table><tr><td></td><td>n123</td><td></td><td></td><td>4567891011121314151617</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>a</td><td></td><td></td><td>758796103110116120128138145153158160162163165</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td>168</td></tr></table></body></html>

#

![](images/1592f7abc6a3746c5fb8c31e17e88c3ce7f2e8bd653968bc518953b3abfe9c3c.jpg)  
图4.1-1

从表4.1-1和图4.1-1中，你能发现数列 $\textcircled{1}$ 中的项随序号的变化呈现出的特点吗？

与函数类似，我们可以定义数列的单调性．从第2项起，每一项都大于它的前一项的数列叫做递增数列；从第2项起，每一项都小于它的前一项的数列叫做递减数列．特别地，各项都相等的数列叫做常数列.

如果数列 $\left\{ a _ { n } \right\}$ 的第 $n$ 项 $\boldsymbol { a } _ { n }$ 与它的序号 $n$ 之间的对应关系可以用一个式子来表示，那么这个式子叫做这个数列的通项公式，例如，数列 $\textcircled{3}$ 的通项公式为 $a _ { n } = \left( - { \frac { 1 } { 2 } } \right) ^ { n }$ ，显然，通项公式就是数列的函数解析式，根据通项公式可以写出数列的各项.

例1根据下列数列 $\{ a _ { n } \}$ 的通项公式，写出数列的前5项，并画出它们的图象.

$$
a _ { n } = { \frac { n ^ { 2 } + n } { 2 } } ; \qquad ( 2 ) a _ { n } = \cos { \frac { ( n - 1 ) \pi } { 2 } } .
$$

解：（1）当通项公式中的 $n = 1$ ，2，3，4，5时，数列 $\left\{ a _ { n } \right\}$ 的前5项依次为1,3，6，10，15.

图象如图4.1-2（1）所示.

![](images/4601b7f02ae83a5f16acd9bf27853483c769ec0dcd6f36c67049d8f4f59ba756.jpg)  
图4.1-2

（2）当通项公式中的 $n { = } 1$ ，2，3，4，5时，数列 $\{ a _ { n } \}$ 的前5项依次为

图象如图4.1-2（2）所示.

例2根据下列数列的前4项，写出数列的一个通项公式：

① $1 , \ - { \frac { 1 } { 2 } } , \ { \frac { 1 } { 3 } } , \ - { \frac { 1 } { 4 } } , \ \cdots ;$ (2）2，0,2,0，….

解：（1）这个数列的前4项的绝对值都是序号的倒数，并且奇数项为正，偶数项为负，所以它的一个通项公式为

$$
a _ { n } { = } \frac { ( - 1 ) ^ { n + 1 } } { n } { \bf 0 } .
$$

（2）这个数列前4项的奇数项是2，偶数项是0，所以它的一个通项公式为

1 $( - 1 ) ^ { n }$ 或 $( - 1 ) ^ { n + 1 }$ 常常用来表示正负相间的变化规律.

$$
a _ { n } = ( - 1 ) ^ { n + 1 } + 1 .
$$

# 练习

1.写出下列数列的前10项，并作出它们的图象：

（1）所有正整数的倒数按从大到小的顺序排列成的数列；  
(2）当自变量 $_ { \mathcal { X } }$ 依次取1，2，3，时，函数 $f ( x ) \ = 2 x + 1$ 的值构成的数列；$\alpha _ { n } = { \binom { 2 , } { n + 1 } }$ $n$ 为奇数，  
（3）数列的通项公式为， $_ n$ 为偶数.

2.根据数列 $\{ a _ { n } \}$ 的通项公式填表：

<html><body><table><tr><td>n</td><td>1</td><td>2</td><td></td><td>5</td><td>：</td><td></td><td></td><td></td><td>：</td><td>n</td></tr><tr><td>an</td><td></td><td></td><td>…</td><td></td><td>…</td><td>153</td><td>…</td><td>273</td><td>…</td><td>3(3+4n)</td></tr></table></body></html>

3.除数函数（divisorfunction） $\boldsymbol { y } = \boldsymbol { d } \left( n \right) \left( \boldsymbol { n } \in \mathbf { N } ^ { * } \right)$ 的函数值等于 $_ n$ 的正因数的个数，例如， $d ( 1 ) = 1$ $d ( 4 ) = 3$ 写出数列 $d ( 1 )$ ， $d ( 2 )$ ，…, $d ( n )$ ，…的前10项.

4.根据下列数列的前5项，写出数列的一个通项公式：

(1） $1 , \ { \frac { 1 } { 3 } } , \ { \frac { 1 } { 5 } } , \ { \frac { 1 } { 7 } } , \ { \frac { 1 } { 9 } } , \ \cdots ;$ (2)1 ${ \mathrm { , ~ } } { \frac { \sqrt { 2 } } { 2 } } , { \mathrm { ~ } } { \frac { 1 } { 2 } } , { \frac { \sqrt { 2 } } { 4 } } , { \frac { 1 } { 4 } } , { \cdots } .$

$$
--- - \_ { n - 1 } = \_ { 1 } = \_ { 2 } = \_ { 1 } = \_ { 2 } = \_ { 1 } = \_ { 2 } = \_ { 2 } = \_ { 1 } = \_ { 2 } = \_ { 2 } = \_ { 2 }
$$

例3如果数列 $\{ a _ { n } \}$ 的通项公式为 $a _ { n } = n ^ { 2 } + 2 n$ ，那么120是不是这个数列的项？如果是，是第几项？

分析：要判断120是不是数列 $\left\{ a _ { n } \right\}$ 中的项，就是要回答是否存在正整数 $_ n$ ，使得$n ^ { 2 } + 2 n { = } 1 2 0$ 也就是判断上述关于 $n$ 的方程是否有正整数解。

解：令

$$
n ^ { 2 } + 2 n { = } 1 2 0 ,
$$

解这个关于 $n$ 的方程，得

所以，120是数列 $\left\{ a _ { n } \right\}$ 的项，是第10项.

例4图4.1-3中的一系列三角形图案称为谢尔宾斯基三角形．在图中4个大三角形中，着色的三角形的个数依次构成一个数列的前4项，写出这个数列的一个通项公式.

![](images/836125c7468ca3c6a70834ee991bacce256f36dd464341c381532798ec2e66bc.jpg)  
图4.1-3

解：在图4.1-3（1）（2）（3）（4）中，着色三角形的个数依次为

即所求数列的前4项都是3的指数幂，指数为序号减1.

因此，这个数列的一个通项公式是

$$
a _ { n } = 3 ^ { n - 1 } .
$$

换个角度观察图4.1-3中的4个图形.可以发现，$a _ { 1 } = 1$ ，且每个图形中的着色三角形都在下一个图形中分裂为3个着色小三角形和1个无色小三角形．于是从第2个图形开始，每个图形中着色三角形的个数都是前一个图形中着色三角形个数的3倍.这样，例4中的数列的前4项满足$a _ { 1 } = 1 , ~ a _ { 2 } = 3 a _ { 1 } , ~ a _ { 3 } = 3 a _ { 2 } , ~ a _ { 4 } = 3 a _ { 3 }$ ，由此猜测这个数列满足公式 $a _ { n } = { \left\{ \begin{array} { l l } { 1 , } & { n = 1 , } \\ { } & { } \\ { 3 a _ { n - 1 } , } & { n \geq 2 . } \end{array} \right. }$

#

当不能明显看出数列的项的取值规律时，可以尝试通过运算来寻找规律.如依次取出数列的某一项，减去或除以它的前一项，再对差或商加以观察.

像 $a _ { n } { = } 3 a _ { n - 1 } ( n { \geqslant } 2 )$ 这样，如果一个数列的相邻两项或多项之间的关系可以用一个式子来表示，那么这个式子叫做这个数列的递推公式，知道了首项或前几项，以及递推公式，就能求出数列的每一项了.

例5已知数列 $\{ a _ { n } \}$ 的首项为 $a _ { 1 } = 1$ ，递推公式为 $a _ { n } = 1 + \frac { 1 } { a _ { n - 1 } } \ ( n \geq 2 )$ ，写出这个数列的前5项.

解：由题意可知

$$
\begin{array} { l } { \displaystyle a _ { 1 } = 1 , } \\ { \displaystyle a _ { 2 } = 1 + \frac { 1 } { a _ { 1 } } = 1 + \frac { 1 } { 1 } = 2 , } \\ { \displaystyle a _ { 3 } = 1 + \frac { 1 } { a _ { 2 } } = 1 + \frac { 1 } { 2 } = \frac { 3 } { 2 } , } \\ { \displaystyle a _ { 4 } = 1 + \frac { 1 } { a _ { 3 } } = 1 + \frac { 2 } { 3 } = \frac { 5 } { 3 } , } \\ { \displaystyle a _ { 5 } = 1 + \frac { 1 } { a _ { 4 } } = 1 + \frac { 3 } { 5 } = \frac { 8 } { 5 } . } \end{array}
$$

在对数列的研究中，求数列某些项的和是主要问题之一．我们把数列 $\left\{ a _ { n } \right\}$ 从第1项起到第 $_ n$ 项止的各项之和，称为数列 $\left\{ a _ { n } \right\}$ 的前 $_ n$ 项和，记作 $S _ { n }$ ，即

$$
S _ { n } { = } a _ { 1 } { + } a _ { 2 } { + } \cdots { + } a _ { n } .
$$

探索数列的求和公式，曾是古代算学家非常感兴趣的问题.

如果数列 $\{ a _ { n } \}$ 的前 $_ n$ 项和 $S _ { n }$ 与它的序号 $n$ 之间的对应关系可以用一个式子来表示，那么这个式子叫做这个数列的前 $n$ 项和公式.

显然 $S _ { 1 } { = } a _ { 1 }$ ，而 $S _ { n - 1 } = a _ { 1 } + a _ { 2 } + \cdots + a _ { n - 1 } ( n \geq 2 )$ 于是我们有

$$
a _ { n } = \left\{ \begin{array} { l l } { { S _ { 1 } , } } & { { n = 1 , } } \\ { { } } & { { } } \\ { { S _ { n } - S _ { n - 1 } , } } & { { n \geq 2 . } } \end{array} \right.
$$

# 思考

已知数列 $\{ a _ { n } \}$ 的前 $n$ 项和公式为 $S _ { n } { = } n ^ { 2 } + n$ ，你能求出 $\{ a _ { n } \}$ 的通项公式吗？

因为

$$
\begin{array} { c } { { a _ { n } = S _ { n } - S _ { n - 1 } } } \\ { { \ } } \\ { { \ = n ^ { 2 } + n - \left[ ( n - 1 ) ^ { 2 } + ( n - 1 ) \right] } } \\ { { \ \Rightarrow } } \\ { { \ = 2 n ~ ( n \geqslant 2 ) , } } \end{array}
$$

并且当 $n { = } 1$ 时， $a _ { 1 } = 2 \times 1 = 2$ 依然成立.

所以 $\{ a _ { n } \}$ 的通项公式是 $a _ { n } = 2 n$

1.根据下面的图形及相应的点数，写出点数构成的数列的一个通项公式，并在横线上和括号中分别填上第5项的图形和点数.

![](images/2a70261c4a212cf69d9dc3c45719f9a7b30f4d8c5089f0b9d4b48e3e7f16afa7.jpg)  
（第1题）

2.根据下列条件，写出数列 $\{ a _ { n } \}$ 的前5项：

(1) $a _ { 1 } = 1 , \ a _ { n } = a _ { n - 1 } + 2 ^ { n - 1 } \ ( n \geq 2 ) ;$ (2） $a _ { 1 } = 3 , \ a _ { n } = { \frac { 2 } { 3 } } a _ { n - 1 } + 1 \ ( n \geq 2 ) .$

3.已知数列 $\left\{ a _ { n } \right\}$ 满足 $a _ { 1 } = 2 , \ a _ { n } = 2 - \frac { 1 } { a _ { n - 1 } } \ ( n \geq 2 )$ ，写出它的前5项，并猜想它的通项公式.

4.已知数列 $\{ a _ { n } \}$ 的前 $_ n$ 项和公式为 $S _ { n } = - 2 n ^ { 2 }$ ，求 $\left\{ a _ { n } \right\}$ 的通项公式.

# 习题4.1

# 复习巩固

1.写出下列数列的前10项，并作出它们的图象：

（1）素数按从小到大的顺序排列成的数列；  
（2）欧拉函数 $\varphi ( n )$ （ $\boldsymbol { n } \in \mathbf { N } ^ { \ast }$ ）的函数值按自变量从小到大的顺序排列成的数列.

2.根据下列条件，写出数列 $\left\{ a _ { n } \right\}$ 的前5项：

欧拉函数 $\varphi ( n )$ $\textstyle ( n \in \mathbf { N } ^ { * }$ ）的函数值等于所有不超过正整数 $_ n$ ，且与 $_ n$ 互素的正整数的个数，例如， $\varphi ( 1 ) = 1$ $\varphi ( 4 ) = 2$

（1） $\scriptstyle a _ { n } = { \frac { 1 } { n ^ { 2 } } } ;$ (2） $a _ { n } = ( - 1 ) ^ { n + 1 } ( n ^ { 2 } + 1 ) ;$ （3） $a _ { 1 } = \frac { 1 } { 2 } , \ \alpha _ { n } = 4 a _ { n - 1 } + 1 \ ( n \geqslant 2 ) , \qquad ( 4 ) \ a _ { 1 } = - \frac { 1 } { 4 } , \ a _ { n } = 1 - \frac { 1 } { a _ { n - 1 } } \ ( n \geqslant 2 ) .$

观察下列数列的特点，用适当的数填空，并写出数列的一个通项公式：

（1)（），-4，9，（），25，（），49；

$$
\begin{array} { r l } & { \mathrm { 1 , ~ \frac { 1 } { 3 ^ { 2 } } , ~ ( } \mathrm { ~  ~ \gamma ~ } \mathrm { ~ 2 } , \frac { 1 } { 7 ^ { 2 } } , \frac { 1 } { 9 ^ { 2 } } , \mathrm { ~ \frac { 1 } { 9 } ~ ( ~  ~ \gamma ~ } \mathrm { ~ 2 } , \frac { 1 } { 1 3 ^ { 2 } } ; } \\ & { \mathrm { 1 , ~ \sqrt { 2 } , ~ \cos ~ \gamma ~ } \mathrm { ~ ) , ~ 2 } , \sqrt { 5 } , \mathrm { ~ \cos ~ \gamma ~ } \mathrm { ~ ) , ~ \sqrt { 7 } , ~ } } \\ & { \frac { 1 } { 2 } , \frac { 1 } { 6 } , \mathrm { ~ \cos ~ \gamma ~ } \mathrm { ~ 2 } 0 , \frac { 1 } { 3 0 } , \mathrm { ~ \cos ~ \gamma ~ } \mathrm { ~ 2 } . } \end{array}
$$

# 综合运用

1.已知数列 $\left\{ a _ { n } \right\}$ 的第1项是1，第2项是2，以后各项由 $a _ { n } = a _ { n - 1 } + a _ { n - 2 }$ $n { > } 2$ 给出.

（1）写出这个数列的前5项；

（2）利用数列 $\{ a _ { n } \}$ ，通过公式 $b _ { n } = \frac { a _ { n + 1 } } { a _ { n } }$ 构造一个新的数列$\left\{ b _ { n } \right\}$ ，试写出数列 $\left\{ b _ { n } \right\}$ 的前5项.

5.传说古希腊毕达哥拉斯学派的数学家用沙粒和小石子来研究数.他们根据沙粒或小石子所排列的形状把数分成许多类，如图中第一行的1，3，6，10称为三角形数，第二行的1，4，9，16称为正方形数，第三行的1，5，12，22称为五边形数．请你分别写出三角形数、正方形数和五边形数所构成的数列的第5项和第6项.

![](images/d662e39ad1a8191d5c099bf82a2a199e387aae93ac01ec1d587d34d07603ba29.jpg)  
（第5题）

6.假设某银行的活期存款年利率为 $0 . 3 5 \%$ ，某人存入10万元后，既不加进存款也不取款，每年到期利息连同本金自动转存，如果不考虑利息税及利率的变化，用 $\boldsymbol { a } _ { n }$ 表示第 $n$ 年到期时的存款余额，求 $\boldsymbol { a } _ { 1 }$ ， $\boldsymbol { a } _ { 2 }$ ， $\boldsymbol { a } _ { 3 }$ 及 $a _ { n }$

# 拓广探索

7.已知函数 $f ( x ) \ = { \frac { 2 ^ { x } - 1 } { 2 ^ { x } } } \ ( x \in \mathbf { R } )$ ，设数列 $\left\{ a _ { n } \right\}$ 的通项公式为 $a _ { n } = f ( n )$ $\mathbf { \Phi } _ { n } \in \mathbf { N } ^ { * }$ ）

（1）求证a≥(2） $\{ a _ { n } \}$ 是递增数列还是递减数列？为什么？

# 斐波那契数列

1202年，意大利数学家斐波那契（LeonardoFibonacci，约1170—约1250）出版了他的《算盘书》（LiberAbaci），他在书中收录了一些有意思的问题，其中有一个关于兔子繁殖的问题：

如果1对兔子每月能生1对小兔子（一雄一雌)，而每 $1$ 对小兔子在它出生后的第3个月里，又能生1对小兔子，假定在不发生死亡的情况下，由1对初生的小兔子开始，50个月后会有多少对兔子？

在第1个月时，只有1对小兔子，过了1个月，那对兔子成熟了，在第3个月时便生下1对小兔子，这时有2对兔子，再过1个月，成熟的兔子再生1对小兔子，而另1对小兔子长大，有3对兔子，如此推算下去，我们可以得到一个表格：

<html><body><table><tr><td>时间/月</td><td>初生兔子/对</td><td>成熟兔子/对</td><td>兔子总数/对</td></tr><tr><td>1</td><td>1</td><td>0</td><td>1</td></tr><tr><td>2</td><td>0</td><td>1</td><td>1</td></tr><tr><td>3</td><td>1</td><td>1</td><td>2</td></tr><tr><td>4</td><td>1</td><td>2</td><td>3</td></tr><tr><td>5</td><td>2</td><td>3</td><td>5</td></tr><tr><td>6</td><td>3</td><td>5</td><td>8</td></tr><tr><td>7</td><td>5</td><td>8</td><td>13</td></tr><tr><td>8</td><td>8</td><td>13</td><td>21</td></tr><tr><td>9</td><td>13</td><td>21</td><td>34</td></tr><tr><td>10</td><td>21</td><td>34</td><td>55</td></tr><tr><td>11</td><td>34</td><td>55</td><td>89</td></tr><tr><td>12</td><td>55</td><td>89</td><td>144</td></tr><tr><td>…</td><td>…</td><td>…</td><td>…</td></tr></table></body></html>

由此可知，从第1个月开始，每月末的兔子总对数是

1,1,2,3,5,8,13,21，34，55,89,144，.

你发现这个数列的规律了吗？

如果用 $F _ { n }$ 表示第 $_ n$ 个月的兔子的总对数，可以看出，

$$
F _ { n } = F _ { n - 1 } + F _ { n - 2 } ( n > 2 ) .
$$

这是一个由递推公式给出的数列，称为斐波那契数列。

斐波那契数列有很多有趣的性质。例如，斐波那契数列满足等式 $F _ { 1 } ^ { 2 } + F _ { 2 } ^ { 2 } +$ $\cdots + F _ { n } ^ { 2 } = F _ { n } F _ { n + 1 }$ ，我们可以用图形（图1）来表示这个等式，图1中小正方形的边长分别为斐波那契数 $F _ { 1 } = 1$ $F _ { 2 } = 1$ ， $F _ { 3 } = 2$ ， $F _ { 4 } = 3$ ，…，面积分别为 $F _ { 1 } ^ { 2 }$ ， $F _ { 2 } ^ { 2 }$ ， $F _ { 3 } ^ { 2 }$ ， $F _ { 4 } ^ { 2 }$ ，…前$n$ $n = 2$ ，3，4，…）个小正方形拼成的长方形的面积依次是两个斐波那

![](images/9ceb4662b606a11b5f7aa40675862c68242da029ca8d452a307de1cd4fd18e36.jpg)  
图1

契数的乘积 $F _ { 2 } F _ { 3 }$ ， $F _ { 3 } F _ { 4 }$ ， $F _ { 4 } F _ { 5 }$ ，…，如图1所示，从内到外依次连接通过小正方形的四分之一圆弧，就得到了一条被称为“斐波那契螺旋”的弧线．如果我们在图1上不断增加边长是斐波那契数的正方形，那么“斐波那契螺旋”也将不断向外延伸，而且它的形状将越来越接近“黄金比例螺旋”

更加有趣的是，人们在自然界中发现了许多斐波那契数列，例如，一棵树在第一年长出一条新枝，新枝成长一年后变为老枝，老枝每年都长出一条新枝。每一条树枝都按照这个规律成长，则每年的树枝总数正好构成了斐波那契数列，又如，图2中向日葵的管状小花排列成两组交错的螺旋，从内往外看，逆时针方向的螺旋有13条，顺时针方向的有21条，恰为斐波那契数列的相邻两项。菠萝和松球的鳞片的排列也呈现出类似的规律. D

![](images/bfb751add0740f4201e05ff08bdacffc7ae8900f584da5a31efbeb33ae8b7160.jpg)  
图2

由于斐波那契数列的广泛应用性，美国成立了斐波那契协会，并于1963年创办《斐波那契季刊》，专门发表关于这个数列的研究论文.

有兴趣的同学可以通过浏览互联网或查阅相关书籍搜集资料，进一步了解和研究斐波那契数列.

# 4.2 等差数列

我们知道，数列是一种特殊的函数．在函数的研究中，我们在理解了函数的一般概念，了解了函数变化规律的研究内容（如单调性、奇偶性等）后，通过研究基本初等函数，不仅加深了对函数的理解，而且掌握了幂函数、指数函数、对数函数、三角函数等非常有用的函数模型，类似地，在了解了数列的一般概念后，我们要研究一些具有特殊变化规律的数列，建立它们的通项公式和前 $n$ 项和公式，并运用它们解决实际问题和数学问题，从中感受数学模型的现实意义与应用．下面，我们从一类取值规律比较简单的数列入手.

# 4.2.1等差数列的概念

请看下面几个问题中的数列.

1.北京天坛圜丘坛的地面由石板铺成，最中间是圆形的天心石，围绕天心石的是9圈扇环形的石板，从内到外各圈的石板数依次为

9，18，27，36，45，54，63，72，81.

2.S,M,L，XL，XXL，XXXL型号的女装 上衣对应的尺码分别是

3.测量某地垂直地面方向上海拔 $5 0 0 ~ \mathrm { m }$ 以下的大气温度，得到从距离地面 $2 0 \textrm { m }$ 起每升高 $1 0 0 \textrm { m }$ 处的大气温度（单位： $\mathrm { { } ^ { \circ } C }$ ）依次为 n4

25.0,24.4，23.8，23.2，22.6.

4.某人向银行贷款 $a$ 万元，贷款时间为 $n$ 年.如果个人贷款月利率为 $r$ ，那么按照等额本金方式还款，他从某月开始，每月应还本金 $b \left( = \frac { a } { 1 2 n } \right)$ 万元，每月支付给银行的利息（单位：万元）依次为

ar，ar-br, $a r - 2 b r$ ，ar-3br，.….

![](images/41396e9cce9ff94f6d23d430a6146f28e729bc46b98669406d183699f7fb0609.jpg)

?

如果按月还款，等额本金还款方式的计算公式是

每月归还本金 $=$ 贷款 总额÷贷款期总月数，

利息部分 $=$ （贷款总额一已归还本金累计额） $\times$ 月利率

# 思考

在代数的学习中，我们常常通过运算来发现规律，例如，在指数函数的学习中，我们通过运算发现了A，B两地旅游人数的变化规律．类似地，你能通过运算发现以上数列的取值规律吗？

对于 $\textcircled{1}$ ，我们发现

$$
1 8 = 9 + 9 , 2 7 = 1 8 + 9 , \cdots , 8 1 = 7 2 + 9 ,
$$

换一种写法，就是

$$
1 8 - 9 = 9 , \ 2 7 - 1 8 = 9 , \ \cdots , \ 8 1 - 7 2 = 9 .
$$

改变表达方式使数列的取值规律更突出了.

如果用 $\left\{ a _ { n } \right\}$ 表示数列 $\textcircled{1}$ ，那么有

$$
a _ { 2 } - a _ { 1 } = 9 , \enspace a _ { 3 } - a _ { 2 } = 9 , \enspace \cdots , \enspace a _ { 9 } - a _ { 8 } = 9 .
$$

这表明，数列 $\textcircled{1}$ 有这样的取值规律：从第2项起，每一项与它的前一项的差都等于同一个常数.数列 $\textcircled{2} \sim \textcircled{4}$ 也有这样的取值规律.

一般地，如果一个数列从第2项起，每一项与它的前一项的差都等于同一个常数，那么这个数列就叫做等差数列（arithmeticprogression），这个常数叫做等差数列的公差(commondifference)，公差通常用字母 $d$ 表示，例如，数列 $\textcircled{1}$ 的公差 $d = 9$

由三个数 $^ a$ ， $A$ ， $^ { b }$ 组成的等差数列可以看成是最简单的等差数列．这时， $A$ 叫做 $^ a$ 与 $^ { b }$ 的等差中项（arithmeticmean)．根据等差数列的定义可以知道， $2 A = a + b$ ·

在日常生活中，人们常常用到等差数列．例如，在给各种产品的尺寸划分级别时，当其中的最大尺寸与最小尺寸相差不大时，常按等差数列进行分级（如前面例子中的上衣尺码)．你能举出一些例子吗？

# 探究

你能根据等差数列的定义推导它的通项公式吗？

设一个等差数列 $\{ a _ { n } \}$ 的首项为 $a _ { 1 }$ ，公差为 $d$ 根据等差数列的定义，可得

$$
a _ { n + 1 } - a _ { n } = d ,
$$

$a _ { n + 1 } - a _ { n } = d$ 就是等差数列 $\{ a _ { n } \}$ 的递推公式。

所以

$$
a _ { 2 } - a _ { 1 } = d , \ a _ { 3 } - a _ { 2 } = d , \ a _ { 4 } - a _ { 3 } = d , \ \cdots .
$$

于是

$$
\begin{array} { r l } & { a _ { 2 } = a _ { 1 } + d , } \\ & { a _ { 3 } = a _ { 2 } + d = ( a _ { 1 } + d ) + d = a _ { 1 } + 2 d , } \\ & { a _ { 4 } = a _ { 3 } + d = ( a _ { 1 } + 2 d ) + d = a _ { 1 } + 3 d , } \end{array}
$$

归纳可得

$$
a _ { n } = a _ { 1 } + ( n - 1 ) d ( n \geq 2 ) .
$$

当 $n { = } 1$ 时，上式为 $a _ { 1 } = a _ { 1 } + ( 1 - 1 ) d = a _ { 1 }$ ，这就是说，上式当 $n { = } 1$ 时也成立.因此，首项为 $\boldsymbol { a } _ { 1 }$ ，公差为 $d$ 的等差数列 $\left\{ a _ { n } \right\}$ 的通项公式为

$$
a _ { n } = a _ { 1 } + ( n - 1 ) d .
$$

# 思考

观察等差数列的通项公式，你认为它与我们熟悉的哪一类函数有关？

由于 $a _ { n } = a _ { 1 } + ( n - 1 ) d = d n + ( a _ { 1 } - d )$ ，所以当 $d \neq 0$ 时，等差数列 $\left\{ a _ { n } \right\}$ 的第 $_ n$ 项 $a _ { n }$ 是一次函数 $f ( x ) = d x +$ $( a _ { 1 } - d ) ( x \in \mathbf { R } )$ 当 $x = n$ 时的函数值，即 $a _ { n } = f ( n )$

如图4.2-1，在平面直角坐标系中画出函数 $f ( x ) =$ $d x + ( a _ { 1 } - d$ ）的图象，就得到一条斜率为 $d$ ，截距为$a _ { 1 } - d$ 的直线．在这条直线上描出点（1， $f ( 1 )$ ），（2，$f ( 2 ) )$ ，…， $( n$ ， $f ( n ) )$ ，…，就得到了等差数列 $\left\{ a _ { n } \right\}$ 的图象．事实上，公差 $d \neq 0$ 的等差数列 $\left\{ a _ { n } \right\}$ 的图象是点$( n , \ a _ { n } )$ 组成的集合，这些点均匀分布在直线 $f ( x ) =$ $d x + ( a _ { 1 } - d )$ 上

![](images/ace51ab99e84dd6ae7d48adb54e5842b03c883c90bae4bb45c79e36438cc4c31.jpg)  
图4.2-1

反之，任给一次函数 $f ( x ) = k x + b$ （k， $^ { b }$ 为常数)，则 $f ( 1 ) = k + b$ ， $f ( 2 ) = 2 k + b$ ，…， $f ( n ) = n k + b$ ，…构成一个等差数列 $\{ n k + b \}$ ，其首项为 $( k + b )$ ，公差为 $k$

下面，我们利用通项公式解决等差数列的一些问题.

例1（1）已知等差数列 $\{ a _ { n } \}$ 的通项公式为 $a _ { n } = 5 - 2 n$ ，求 $\{ a _ { n } \}$ 的公差和首项；

（2）求等差数列8，5，2，…的第20项.

分析：（1）已知等差数列的通项公式，只要根据等差数列的定义，由 $a _ { n } - a _ { n - 1 } = d$ 即可求出公差 $d$ ；（2）可以先根据数列的两个已知项求出通项公式，再利用通项公式求数列的第20项.

解：（1）当 $n \geq 2$ 时，由 $\{ a _ { n } \}$ 的通项公式 $a _ { n } = 5 - 2 n$ ，可得

$$
a _ { n - 1 } = 5 - 2 ( n - 1 ) = 7 - 2 n .
$$

于是

$$
d = a _ { n } - a _ { n - 1 } = ( 5 - 2 n ) - ( 7 - 2 n ) = - 2 .
$$

把 $n { = } 1$ 代入通项公式 $a _ { n } = 5 - 2 n$ ，得

$$
a _ { 1 } = 5 - 2 \times 1 { = } 3 .
$$

所以， $\{ a _ { n } \}$ 的公差为一2，首项为3.

（2）由已知条件，得

$$
d = 5 - 8 = - 3 .
$$

把 $a _ { 1 } = 8$ ， $d = - 3$ 代人 $a _ { n } = a _ { 1 } + ( n - 1 ) d$ ，得

$$
a _ { n } = 8 - 3 ( n - 1 ) = 1 1 - 3 n .
$$

把 $n { = } 2 0$ 代人上式，得

$$
a _ { 2 0 } = 1 1 - 3 \times 2 0 = - 4 9 .
$$

所以，这个数列的第20项是一49.

例2－401是不是等差数列—5，－9， $- 1 3$ ，…的项？如果是，是第几项？

分析：先求出数列的通项公式，它是一个关于 $n$ 的方程，再看一401是否能使这个方程有正整数解.

解：由 $a _ { 1 } = - 5$ ， $d = - 9 - ( - 5 ) = - 4$ ，得这个数列的通项公式为

$$
a _ { n } = - 5 - 4 ( n - 1 ) = - 4 n - 1 .
$$

令

$$
- 4 n - 1 = - 4 0 1 ,
$$

解这个关于 $n$ 的方程，得

$$
n { = } 1 0 0 .
$$

所以，－401是这个数列的项，是第100项.

# 练习

1.判断下列数列是不是等差数列，如果是，写出它的公差.

（1）95，82，69，56，43，30；  
(2）1，1.1，1.11，1.111，1.1111，1.11111;  
（3）1，-2，3，-4，5， $- 6$ $1 , \ { \frac { 1 1 } { 1 2 } } , \ { \frac { 5 } { 6 } } , \ { \frac { 3 } { 4 } } , \ { \frac { 2 } { 3 } } , \ { \frac { 7 } { 1 2 } } , \ { \frac { 1 } { 2 } } .$

2.求下列各组数的等差中项：

（1）647和895； $- 1 2 { \frac { 1 } { 3 } }$ $2 4 \frac { 3 } { 5 }$

3.已知 $\{ a _ { n } \}$ 是一个等差数列，请在下表中的空格处填入适当的数.

<html><body><table><tr><td>a1</td><td>a3</td><td>a5</td><td>a7</td><td>d</td></tr><tr><td>-7</td><td></td><td>8</td><td></td><td></td></tr><tr><td></td><td>2</td><td></td><td></td><td>-6.5</td></tr></table></body></html>

4.已知在等差数列 $\{ a _ { n } \}$ 中， $a _ { 4 } + a _ { 8 } = 2 0$ ， $a _ { 7 } = 1 2$ 求 $\boldsymbol { a } _ { 4 }$

5.在7和21中插入3个数，使这5个数成等差数列.

例3某公司购置了一台价值为220万元的设备，随着设备在使用过程中老化，其价值会逐年减少，经验表明，每经过一年其价值就会减少 $d ( d$ 为正常数）万元．已知这台设备的使用年限为10年，超过10年，它的价值将低于购进价值的 $5 \%$ ，设备将报废.请确定 $d$ 的取值范围.

分析：这台设备使用 $n$ 年后的价值构成一个数列 $\left\{ a _ { n } \right\}$ ．由题意可知，10年之内（含10年），这台设备的价值应不小于（ $2 2 0 \times 5 \% =$ ）11万元；而10年后，这台设备的价值应小于11万元，可以利用 $\left\{ a _ { n } \right\}$ 的通项公式列不等式求解。

解：设使用 $_ n$ 年后，这台设备的价值为 $a _ { n }$ 万元，则可得数列 $\{ a _ { n } \}$ ，由已知条件，得

$$
a _ { n } = a _ { n - 1 } - d ( n \geq 2 ) .
$$

由于 $d$ 是与 $n$ 无关的常数，所以数列 $\{ a _ { n } \}$ 是一个公差为一 $d$ 的等差数列．因为购进设备的价值为220万元，所以 $a _ { 1 } = 2 2 0 - d$ ，于是

$$
a _ { n } = a _ { 1 } + ( n - 1 ) ( - d ) = 2 2 0 - n d .
$$

根据题意，得

$$
\begin{array} { r } { \left\{ \begin{array} { l l } { a _ { 1 0 } \geq 1 1 , } \\ { a _ { 1 1 } < 1 1 , } \end{array} \right. } \end{array}
$$

即

解这个不等式组，得

所以， $d$ 的取值范围为 $1 9 < d { \leqslant } 2 0 . 9$

例4已知等差数列 $\{ a _ { n } \}$ 的首项 $a _ { 1 } = 2$ ，公差 $d { = } 8$ ，在 $\{ a _ { n } \}$ 中每相邻两项之间都插入3个数，使它们和原数列的数一起构成一个新的等差数列 $\left\{ b _ { n } \right\}$ ?

（1）求数列 $\left\{ b _ { n } \right\}$ 的通项公式.  
（2） $b _ { 2 9 }$ 是不是数列 $\{ a _ { n } \}$ 的项？若是，它是 $\left\{ a _ { n } \right\}$ 的第几项？若不是，说明理由.

分析：（1） $\left\{ a _ { n } \right\}$ 是一个确定的数列，只要把 $a _ { 1 }$ ， $a _ { 2 }$ 表示为 $\left\{ b _ { n } \right\}$ 中的项，就可以利用等差数列的定义得出 $\left\{ b _ { n } \right\}$ 的通项公式；（2）设 $\{ a _ { n } \}$ 中的第 $_ n$ 项是 $\left\{ b _ { n } \right\}$ 中的第 $c _ { n }$ 项，根据条件可以求出 $n$ 与 $c _ { n }$ 的关系式，由此即可判断 $b _ { 2 9 }$ 是不是 $\left\{ a _ { n } \right\}$ 的项.

解：（1）设数列 $\left\{ b _ { n } \right\}$ 的公差为 $d ^ { \prime }$

由题意可知， $b _ { 1 } = a _ { 1 } , ~ b _ { 5 } = a _ { 2 }$ ，于是

# D

$$
b _ { 5 } - b _ { 1 } = a _ { 2 } - a _ { 1 } = 8 .
$$

因为 $b _ { 5 } - b _ { 1 } = 4 d ^ { \prime }$ ，所以 $4 d ^ { \prime } = 8$ ，所以 $d ^ { \prime } = 2$

所以

如果插入 $k \left( k \in \mathbf { N } ^ { * } \right.$ ）个数，那么 $\{ b _ { n } \}$ 的公差是多少？

$$
b _ { n } = 2 + ( n - 1 ) \times 2 = 2 n .
$$

所以，数列 $\left\{ b _ { n } \right\}$ 的通项公式是

$$
b _ { n } = 2 n .
$$

（2）数列 $\{ a _ { n } \}$ 的各项依次是数列 $\left\{ b _ { n } \right\}$ 的第1，5，9，13，项，这些下标构成一个首项为1，公差为4的等差数列 $\left\{ c _ { n } \right\}$ ，则 $\scriptstyle c _ { n } = 4 n - 3$

#

令 $4 n - 3 = 2 9$ ，解得

对于第（2）小题，你还有其他解决方法吗？

$$
n { = } 8 .
$$

所以， $b _ { 2 9 }$ 是数列 $\{ a _ { n } \}$ 的第8项.

例5 已知数列 $\{ a _ { n } \}$ 是等差数列， $\boldsymbol { \phi }$ ， $q$ ， $s$ ， $\boldsymbol { t } \in \mathbf { N } ^ { \ast }$ ，且 $\ p + q = s + t$ 求证 $a _ { \phi } + a _ { q } =$ $a _ { s } + a _ { t }$

分析：只要根据等差数列的定义写出 $a _ { \scriptscriptstyle  { p } }$ ， $\boldsymbol { a } _ { \boldsymbol { q } }$ ， $\boldsymbol { a } _ { s }$ ， $\boldsymbol { a } _ { t }$ ，再利用已知条件即可得证.

证明：设数列 $\left\{ a _ { n } \right\}$ 的公差为 $d$ ，则

$$
\begin{array} { r l } & { a _ { \hbar } = a _ { 1 } + ( p - 1 ) d , } \\ & { a _ { q } = a _ { 1 } + ( q - 1 ) d , } \\ & { a _ { s } = a _ { 1 } + ( s - 1 ) d , } \\ & { a _ { t } = a _ { 1 } + ( t - 1 ) d . } \end{array}
$$

所以

$$
\begin{array} { c } { { a _ { \flat } + a _ { q } = 2 a _ { 1 } + ( \hbar + q - 2 ) d , } } \\ { { a _ { s } + a _ { t } = 2 a _ { 1 } + ( s + t - 2 ) d . } } \end{array}
$$

因为 $\ p + q = s + t$ ，所以

$$
a _ { \scriptscriptstyle \phi } + a _ { \scriptscriptstyle q } = a _ { \scriptscriptstyle s } + a _ { \scriptscriptstyle t } .
$$

# 思考

例5是等差数列的一条性质，图4.2-2是它的一种情形，你能从几何角度解释等差数列的这一性质吗？

![](images/f8bc28e6e29224d4301de99ab2761544dad509cbb6b58aa024a62eec92752333.jpg)  
图4.2-2

3.在等差数列 $\{ a _ { n } \}$ 中， $a _ { n } = m$ ， $a _ { m } = n$ ，且 $n \neq m$ ，求 $a _ { m + n }$

1.已知数列 $\{ a _ { n } \}$ ， $\left\{ b _ { n } \right\}$ 都是等差数列，公差分别为 $d _ { 1 }$ ， $d _ { 2 }$ ，数列 $\left\{ c _ { n } \right\}$ 满足 $c _ { n } = a _ { n } + 2 b _ { n }$

（1）数列 $\left\{ c _ { n } \right\}$ 是不是等差数列？若是，证明你的结论；若不是，请说明理由.  
（2）若 $\{ a _ { n } \}$ ， $\{ b _ { n } \}$ 的公差都等于2， $a _ { 1 } = b _ { 1 } = 1$ ，求数列 $\left\{ c _ { n } \right\}$ 的通项公式.

5.已知一个无穷等差数列 $\{ a _ { n } \}$ 的首项为 $a _ { 1 }$ ，公差为 $d$

（1）将数列中的前 $_ m$ 项去掉，其余各项组成一个新的数列，这个新数列是等差数列吗？如果是，它的首项和公差分别是多少？  
（2）依次取出数列中的所有奇数项，组成一个新的数列，这个新数列是等差数列吗？如果是，它的首项和公差分别是多少？  
（3）依次取出数列中所有序号为7的倍数的项，组成一个新的数列，它是等差数列吗？你能根据得到的结论作出一个猜想吗？

$$
\yen 123,456
$$

# 4.2.2等差数列的前 $n$ 项和公式

前面我们学习了等差数列的概念和通项公式，下面我们将利用这些知识解决等差数列的求和问题.

![](images/3686e040d67fb988b451944145dacd71b0399552ba1859ac439915be7a7ff42b.jpg)

据说，二百多年前，高斯的算术老师提出了下面的问题：

$$
1 + 2 + 3 + \cdots + 1 0 0 = ?
$$

当其他同学忙于把100个数逐项相加时，10岁的高斯却用下面的方法迅速算出了正确答案：

$$
( 1 + 1 0 0 ) + ( 2 + 9 9 ) + \cdots + ( 5 0 + 5 1 ) = 1 0 1 \times 5 0 = 5 0 5 0 .
$$

高斯的算法实际上解决了求等差数列

$$
1 , \ 2 , \ 3 , \ \cdots , \ n , \ \cdots
$$

高斯（Gauss,1777-1855)，德国数学家，近代数学的奠基者之一，他在天文学、大地测量学、磁学、光学等领域都作出了杰出贡献。

前100项的和的问题.

# 思考

你能说说高斯在求和过程中利用了数列 $\textcircled{1}$ 的什么性质吗？你能从中得到求数列 $\textcircled{1}$ 的前 $_ n$ 项和的方法吗？

对于数列 $\textcircled{1}$ ，设 $a _ { n } = n$ ，那么高斯的计算方法可以表示为

$$
( a _ { 1 } + a _ { 1 0 0 } ) + ( a _ { 2 } + a _ { 9 9 } ) + \cdots + ( a _ { 5 0 } + a _ { 5 1 } ) = 1 0 1 \times 5 0 = 5 0 5 0 .
$$

可以发现，高斯在计算中利用了

$$
a _ { 1 } + a _ { 1 0 0 } = a _ { 2 } + a _ { 9 9 } = \cdots = a _ { 5 0 } + a _ { 5 1 }
$$

#

这一特殊关系，这就是上一小节例5中性质的应用，它使不同数的求和问题转化成了相同数（即101）的求和，从而简

你能用高斯的方法求$1 { + } 2 { + } { \cdots } { + } 1 0 0 { + } 1 0 1$ 吗？

化了运算.

将上述方法推广到一般，可以得到：

当 $n$ 是偶数时，有

$$
a _ { 1 } + a _ { n } = a _ { 2 } + a _ { n - 1 } = \cdots = a _ { \frac { n } { 2 } } + a _ { \frac { n } { 2 } + 1 } ,
$$

于是有

$$
\begin{array} { r l } & { S _ { n } = 1 + 2 + 3 + \cdots + n } \\ & { \quad = ( 1 + n ) + [ 2 + ( n - 1 ) ] + \cdots + \left[ \frac { n } { 2 } + \left( \frac { n } { 2 } + 1 \right) \right] } \\ & { \quad = \underbrace { ( 1 + n ) + ( 1 + n ) + \cdots + ( 1 + n ) } _ { \frac { n } { 2 } \mathrm { \normalfont \cdot } } } \\ & { \quad = \frac { n ( 1 + n ) } { 2 } . } \end{array}
$$

当 $n$ 为奇数时，有

$$
\begin{array} { r l } & { S _ { n } = 1 + 2 + 3 + \cdots + n } \\ & { \quad = ( 1 + n ) + [ 2 + ( n - 1 ) ] + \cdots + \left[ \left( \frac { n + 1 } { 2 } - 1 \right) + \left( \frac { n + 1 } { 2 } + 1 \right) \right] + \frac { n + 1 } { 2 } } \\ & { \quad = \underbrace { ( 1 + n ) + ( 1 + n ) + \cdots + ( 1 + n ) } _ { \frac { n - 1 } { 2 } \star } + \frac { n + 1 } { 2 } } \\ & { \quad = \frac { n - 1 } { 2 } \cdot ( 1 + n ) + \frac { n + 1 } { 2 } } \\ & { \quad = \frac { n ( 1 + n ) } { 2 } . } \end{array}
$$

所以，对任意正整数 $n$ ，都有

$$
S _ { n } = 1 + 2 + 3 + \cdots + n = \frac { n \left( 1 + n \right) } { 2 } .
$$

# 思考

我们发现，在求前 $n$ 个正整数的和时，要对 $n$ 分奇数、偶数进行讨论，比较麻烦．能否设法避免分类讨论？

如果对公式 $S _ { n } = 1 + 2 + 3 + \cdots + n = { \frac { n \left( n + 1 \right) } { 2 } }$ 作变形，可得

$$
2 S _ { n } = 2 ( 1 + 2 + 3 + \cdots + n ) = n \left( n + 1 \right) ,
$$

它相当于两个 $S _ { n }$ 相加，而结果变成 $n$ 个 $( n + 1 )$ 相加.

受此启发，我们得到下面的方法：

$$
\begin{array} { l c r } { { S _ { n } = 1 + \begin{array} { c c c } { { 2 } } & { { + } } & { { 3 } } \end{array} + \dots + n , } } \\ { { S _ { n } = n + ( n - 1 ) + ( n - 2 ) + \dots + 1 , } } \end{array}
$$

将上述两式相加，可得

$$
\begin{array} { r l } & { 2 S _ { n } = ( n + 1 ) + [ ( n - 1 ) + 2 ] + [ ( n - 2 ) + 3 ] + \dots + ( 1 + n ) } \\ & { \qquad = \underbrace { ( 1 + n ) + ( 1 + n ) + \dots + ( 1 + n ) } _ { n \uparrow } } \\ & { \qquad = n \left( n + 1 \right) , } \end{array}
$$

所以

$$
S _ { n } = 1 + 2 + 3 + \cdots + n = \frac { n \left( n + 1 \right) } { 2 } .
$$

# 探究

上述方法的妙处在哪里？这种方法能够推广到求等差数列 $\{ a _ { n } \}$ 的前 $n$ 项和吗？

可以发现，上述方法的妙处在于将 $1 + 2 + 3 + \cdots + n$ “倒序”为 $n + ( n - 1 ) + ( n -$ $2 ) + \cdots + 1$ ，再将两式相加，得到 $n$ 个相同的数（即 $n { + 1 }$ ）相加，从而把不同数的求和转化为 $n$ 个相同的数求和.

对于等差数列 $\left\{ a _ { n } \right\}$ ，因为 $a _ { 1 } + a _ { n } = a _ { 2 } + a _ { n - 1 } = \cdots = a _ { n } + a _ { 1 }$ ，由上述方法得到启示，我们用两种方式表示 $S _ { n }$

$$
\begin{array} { l l } { S _ { n } = a _ { 1 } + a _ { 2 } \ + \dots + a _ { n } , } \\ { S _ { n } = a _ { n } + a _ { n - 1 } + \dots + a _ { 1 } . } \end{array}
$$

$\textcircled{2} + \textcircled { 3 }$ ，得

$$
\begin{array} { r l } & { 2 S _ { n } = ( a _ { 1 } + a _ { n } ) + ( a _ { 2 } + a _ { n - 1 } ) + \dots + ( a _ { n } + a _ { 1 } ) } \\ & { \quad = \underbrace { ( a _ { 1 } + a _ { n } ) + ( a _ { 1 } + a _ { n } ) + \dots + ( a _ { 1 } + a _ { n } ) } _ { n \uparrow } } \\ & { \quad = n ( a _ { 1 } + a _ { n } ) . } \end{array}
$$

由此得到等差数列 $\{ a _ { n } \}$ 的前 $n$ 项和公式

$$
\boxed { \begin{array} { c } { S _ { n } = \frac { n \left( a _ { 1 } + a _ { n } \right) } { 2 } . } \end{array} }
$$

对于等差数列 $\{ a _ { n } \}$ ，利用公式（1），只要已知等差数列 $\{ a _ { n } \}$ 的首项 $a _ { 1 }$ 和末项 $a _ { n }$ ，就可以求得前 $_ n$ 项和 $S _ { n }$ 另外，如果已知首项 $a _ { 1 }$ 和公差 $d$ ，那么这个等差数列就完全确定了，所以我们也可以用 $a _ { 1 }$ 和 $d$ 来表示 $S _ { n }$

把等差数列的通项公式 $a _ { n } = a _ { 1 } + ( n - 1 ) d$ 代人公式(1)，可得

$$
\boxed { \begin{array} { c } { S _ { n } = n a _ { 1 } + \frac { n \left( n - 1 \right) } { 2 } d . } \end{array} }
$$

将（1）变形可得${ \frac { a _ { 1 } + a _ { n } } { 2 } } = { \frac { a _ { 1 } + a _ { 2 } + \cdots + a _ { n } } { n } } ,$ 所以 $\frac { a _ { 1 } + a _ { n } } { 2 }$ 就是等差数列$\{ a _ { n } \}$ 前 $n$ 项的平均数，实际上，我们就是利用等差数列的这一重要特性来推导它的前 $_ n$ 项和的，你还能发现这一特性的一些应用吗？

# 思考

不从公式（1）出发，你能用其他方法得到公式（2）吗？

例6已知数列 $\{ a _ { n } \}$ 是等差数列.

（1）若 $a _ { 1 } = 7$ ， $a _ { 5 0 } = 1 0 1$ ，求 $S _ { 5 0 }$ （2）若 $a _ { 1 } = 2$ $a _ { 2 } = \frac { 5 } { 2 }$ 求 $S _ { 1 0 }$

3若 $a _ { 1 } = { \frac { 1 } { 2 } } , ~ d = - { \frac { 1 } { 6 } } , ~ S _ { n }$ $S _ { n } = - 5$ 求 $n$

分析：对于（1)，可以直接利用公式 $S _ { n } { = } \frac { n \left( a _ { 1 } { + } a _ { n } \right) } { 2 }$ 求和；在（2）中，可以先利用 $a _ { 1 }$ 和 $a _ { 2 }$ 的值求出 $d$ ，再利用公式 $S _ { n } { = } n a _ { 1 } { + } \frac { n \left( n { - } 1 \right) } { 2 } d$ 求和；（3）已知公式 $S _ { n } = n a _ { 1 } +$ ${ \frac { n ( n - 1 ) } { 2 } } d$ 中的 $\boldsymbol { a } _ { 1 }$ ， $d$ 和 $S _ { n }$ ，解方程即可求得 $n$

#

对于等差数列 $\{ a _ { n } \}$ 的相关量 $a _ { 1 }$ ， $a _ { n }$ ， $d$ ， $n$ ，$S _ { n }$ ，已知几个量就可以确定其他量？

解：（1）因为 $a _ { 1 } = 7$ ， $a _ { 5 0 } = 1 0 1$ ，根据公式 $S _ { n } { = } \frac { n ^ { ( a _ { 1 } + a _ { n } ) } } { 2 }$ 可得

$$
S _ { 5 0 } = \frac { 5 0 \times ( 7 + 1 0 1 ) } { 2 } = 2 7 0 0 .
$$

（2）因为 $a _ { 1 } = 2 , a _ { 2 } = \frac { 5 } { 2 }$ ，所以 $d { = } \frac { 1 } { 2 }$ （204 根据公式 $S _ { n } { = } n a _ { 1 } { + } \frac { n ^ { ( n - 1 ) } } { 2 } d$ ，可得

$$
S _ { 1 0 } = 1 0 \times 2 + \frac { 1 0 \times ( 1 0 - 1 ) } { 2 } \times \frac { 1 } { 2 } = \frac { 8 5 } { 2 } .
$$

（3）把 $a _ { 1 } = \frac { 1 } { 2 } , ~ d = - \frac { 1 } { 6 } , ~ S _ { n } = -$ 代人 $S _ { n } { = } ~ n a _ { 1 } { + } \frac { n \left( n { - } 1 \right) } 2 d$ ，得

$$
- 5 = { \frac { 1 } { 2 } } n + { \frac { n \left( n - 1 \right) } { 2 } } \times \left( - { \frac { 1 } { 6 } } \right) .
$$

整理，得

$$
n ^ { 2 } - 7 n - 6 0 = 0 .
$$

解得

所以

$$
n { = } 1 2 .
$$

例7已知一个等差数列 $\left\{ a _ { n } \right\}$ 前10项的和是310，前20项的和是1220．由这些条件能确定这个等差数列的首项和公差吗？

分析：把已知条件代入等差数列前 $n$ 项和的公式（2）后，可得到两个关于 $a _ { 1 }$ 与 $d$ 的二元一次方程，解这两个二元一次方程所组成的方程组，就可以求得 $a _ { 1 }$ 和 $d$

解：由题意，知

$$
\begin{array} { r } { S _ { 1 0 } = 3 1 0 , \ S _ { 2 0 } = 1 2 2 0 . } \end{array}
$$

把它们代人公式

$$
S _ { n } = n a _ { 1 } + \frac { n \left( n - 1 \right) } { 2 } d ,
$$

得

$$
\left\{ { \begin{array} { l } { { 1 0 a _ { 1 } + 4 5 d = 3 1 0 , } } \\ { { 2 0 a _ { 1 } + 1 9 0 d = 1 2 2 0 . } } \end{array} } \right.
$$

解方程组，得

$$
\left\{ \begin{array} { l l } { a _ { 1 } = 4 , } \\ { ~ } \\ { d = 6 . } \end{array} \right.
$$

所以，由所给的条件可以确定等差数列的首项和公差。

一般地，对于等差数列，只要给定两个相互独立的条件，这个数列就完全确定.

# 探究

已知数列 $\{ a _ { n } \}$ 的前 $n$ 项和为 $S _ { n } = p n ^ { 2 } + q n + r$ ，其中 $\boldsymbol { \phi }$ ， $q$ ， $r$ 为常数，且 $ { p } \neq 0$ 任取若干组 $\boldsymbol { \phi }$ ， $q$ ， $r$ ，在电子表格中计算 $a _ { 1 }$ ， $a _ { 2 }$ ， $a _ { 3 }$ ， $a _ { 4 }$ ， $a _ { 5 }$ 的值（图4.2-3给出$\scriptstyle { p = 1 }$ ， $q = 2$ ， $r = 0$ 的情况），观察数列 $\{ a _ { n } \}$ 的特点，研究它是一个怎样的数列，并证明你的结论。

![](images/8aa1563aa3ef2b59d35f0ae0bfbfd8b3096173c9572595335cd27c762d08f9a1.jpg)  
图4.2-3

图4.2-3中的电子表格A列中A1，A2，A3分别表示 $\phi$ ， $q$ ， $r$ 的值，B列、C列中分别是相应的 $S _ { n }$ 和 $\boldsymbol { a } _ { n }$ 的值.

# 练习

1.根据下列各题中的条件，求相应等差数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和 $S _ { n }$

（1） $a _ { 1 } = 5 , a _ { n } = 9 5 , n = 1 0 ;$ (2） $a _ { 1 } = 1 0 0 , \ d = - 2 , \ n = 5 0 ;$ （3） $a _ { 1 } = - 4 , \ a _ { 8 } = - 1 8 , \ n = 1 0 ; \qquad ( 4 ) \ a _ { 1 } = 1 4 . 5 , \ d = 0 . 7 , \ a _ { n } = 3 2 .$

2.等差数列-1， $- 3$ ， $- 5$ ，…的前多少项的和是一100？

3.在等差数列 $\{ a _ { n } \}$ 中， $S _ { n }$ 为其前 $_ n$ 项的和，若 $S _ { 4 } { = } 6$ ， $S _ { 8 } = 2 0$ ，求 $S _ { 1 6 }$

4.在等差数列 $\{ a _ { n } \}$ 中，若 $S _ { 1 5 } = 5 ( a _ { 2 } + a _ { 6 } + a _ { k } )$ ，求 $k$

5.已知一个等差数列的项数为奇数，其中所有奇数项的和为290，所有偶数项的和为261.求此数列中间一项的值以及项数.

例8某校新建一个报告厅，要求容纳800个座位，报告厅共有20排座位，从第2排起后一排都比前一排多2个座位，问第1排应安排多少个座位.

分析：将第1排到第20排的座位数依次排成一列，构成数列 $\{ a _ { n } \}$ ，设数列 $\{ a _ { n } \}$ 的前$n$ 项和为 $S _ { n }$ ，由题意可知， $\{ a _ { n } \}$ 是等差数列，且公差及前20项的和已知，所以可利用等差数列的前 $_ n$ 项和公式求首项.

解：设报告厅的座位从第1排到第20排，各排的座位数依次排成一列，构成数列$\{ a _ { n } \}$ ，其前 $_ n$ 项和为 $S _ { n }$ ．根据题意，数列 $\left\{ a _ { n } \right\}$ 是一个公差为2的等差数列，且 $S _ { 2 0 } { = } 8 0 0$

由 $S _ { 2 0 } = 2 0 a _ { 1 } + \frac { 2 0 \times ( 2 0 - 1 ) } { 2 } \times 2 = 8 0 0$ ，可得

$$
a _ { 1 } = 2 1 .
$$

因此，第1排应安排21个座位.

例9已知等差数列 $\{ a _ { n } \}$ 的前 $n$ 项和为 $S _ { n }$ ，若 $a _ { 1 } = 1 0$ ，公差 $d = - 2$ ，则 $S _ { n }$ 是否存在最大值？若存在，求 $S _ { n }$ 的最大值及取得最大值时 $n$ 的值；若不存在，请说明理由.

分析：由 $a _ { 1 } > 0$ 和 $d { < } 0$ ，可以证明 $\{ a _ { n } \}$ 是递减数列，且存在正整数 $k$ ，使得当 $n { \geqslant } k$ 时， $a _ { n } < 0$ ， $S _ { n }$ 递减，这样，就把求 $S _ { n }$ 的最大值转化为求 $\{ a _ { n } \}$ 的所有正数项的和.

另一方面，等差数列的前 $n$ 项和公式可写成 $S _ { n } = \frac { d } { 2 } n ^ { 2 } +$ ${ \Big ( } a _ { 1 } - { \frac { d } { 2 } } { \Big ) } n$ ，所以当 $d \neq 0$ 时 $S _ { n }$ 可以看成二次函数 ${ { y } = \frac { d } { 2 } { { x } ^ { 2 } } + }$ $\Bigl ( a _ { 1 } - \frac { d } { 2 } \Bigr ) x$ $x \in \mathbf { R } )$ 電当 $x = n$ 时的函数值，如图4.244，当 $d < 0$ 时， $S _ { n }$ 关于 $_ n$ 的图象是一条开口向下的抛物线上的一些点.因此，可以利用二次函数求相应的 $n$ ， $S _ { n }$ 的值.

解法1：由 $a _ { n + 1 } - a _ { n } = - 2 < 0$ ，得 $a _ { n + 1 } < a _ { n }$ ，所以 $\{ a _ { n } \}$ 是递减数列.

![](images/48180ec10b5d6bb25d64ed674ed2a1683a1c12306b9fc1c4e04a76837afec260.jpg)  
图4.2-4

又由 $\ a _ { n } = 1 0 + ( n - 1 ) \times ( - 2 ) = - 2 n + 1 2$ ，可知：

当 $n { < } 6$ 时， $a _ { n } > 0$ 当 $n = 6$ 时， ${ { a } _ { n } } = 0$ 当 $n > 6$ 时， $a _ { n } < 0$

所以

$$
S _ { 1 } { < } S _ { 2 } { < } \cdots { < } S _ { 5 } { = } S _ { 6 } { > } S _ { 7 } { > } \cdots .
$$

也就是说，当 $n = 5$ 或6时， $S _ { n }$ 最大.

因为 $S _ { 5 } = \frac { 5 } { 2 } \times [ 2 \times 1 0 + ( 5 - 1 ) \times ( - 2 ) ] = 3 0$ ，所以 $S _ { n }$ 的最大值为30.

解法2：因为 $\begin{array} { l } { { S _ { n } = \displaystyle \frac { d } { 2 } n ^ { 2 } + \bigl ( a _ { 1 } { - } \frac { d } { 2 } \bigr ) n } } \\ { { \mathrm { ~ } = - n ^ { 2 } + 1 1 n } } \\ { { \mathrm { ~ } = - \bigl ( n { - } \frac { 1 1 } { 2 } \bigr ) ^ { 2 } + \frac { 1 2 1 } { 4 } , } } \end{array}$

#

想一想，这是为什么？

所以，当 $_ n$ 取与 $\frac { 1 1 } { 2 }$ 最接近的整数即5或6时， $S _ { n }$ 最大，最大值为30.

# 思考

在例9中，当 $d = - 3 . 5$ 时， $S _ { n }$ 有最大值吗？结合例9考虑更一般的等差数列前$n$ 项和的最大值问题.

# 练习

1.某市一家商场的新年最高促销奖设立了两种领奖方式：第一种，获奖者可以选择2000元的奖金；第二种，从12月20日到第二年的1月1日，每天到该商场领取奖品，第1天领取的奖品价值为100元，第2天为110元，以后逐天增加10元．你认为哪种领奖方式获奖者受益更多？

2.已知数列 $\{ a _ { n } \}$ 的前 $_ n$ 项和 $S _ { n } = { \frac { 1 } { 4 } } n ^ { 2 } + { \frac { 2 } { 3 } } n + 3 .$ 求这个数列的通项公式.

3.已知等差数列一4.2，-3.7，-3.2，的前 $_ n$ 项和为 $S _ { n }$ ， $S _ { n }$ 是否存在最大（小）值？如果存在，求出取得最值时 $_ n$ 的值.4.求集合 $M = \{ m \ | \ m = 2 n - 1$ ， $\boldsymbol { n } \in \mathbf { N } ^ { * }$ ，且 $m { < } 6 0 \}$ 中元素的个数，并求这些元素的和.

$^ { * } 5 .$ 已知数列 $\{ a _ { n } \}$ 的通项公式为 $a _ { n } = \frac { n - 2 } { 2 n - 1 5 }$ 前 $n$ 项和为 $S _ { n }$ 求 $S _ { n }$ 取得最小值时 $n$ 的值.

# 习题4.2

# 复习巩固

1.根据下列等差数列 $\left\{ a _ { n } \right\}$ 中的已知量，求相应的未知量：

(1） $a _ { 1 } = 2 0$ ， $a _ { n } = 5 4$ ， $S _ { n } { = } 9 9 9$ ，求 $d$ 及 $n$ ; (2) ，n=37，S=629，求a及a；（3）a1= $a _ { 1 } = \frac { 5 } { 6 } , \ d = - \frac { 1 } { 6 } , \ S _ { n } = - 5$ 1，S=-5，求n及a；（4)d=2，n=15,a=10，求a及S

2.已知 $\{ a _ { n } \}$ 为等差数列， $a _ { 1 } + a _ { 3 } + a _ { 5 } = 1 0 5$ ， $a _ { 2 } + a _ { 4 } + a _ { 6 } = 9 9$ 求 $a _ { 2 0 }$

3.（1）求从小到大排列的前 $_ n$ 个正偶数的和.（2）求从小到大排列的前 $n$ 个正奇数的和.（3）在三位正整数的集合中有多少个数是5的倍数？求这些数的和.（4）在小于100的正整数中，有多少个数被7除余2？这些数的和是多少？

4.1682年，英国天文学家哈雷发现一颗大彗星的运行曲线和1531年、1607年的彗星惊人地相似.他大胆断定，这是同一天体的三次出现，并预言它将于76年后再度回归.这就是著名的哈雷彗星，它的回归周期大约是76年，请你查找资料，列出哈雷彗星的回归时间表，并预测它在本世纪回归的年份.

![](images/ba98a9d7c5f09264b02e667d5e7992c0ce5acc3e9baeb070645c59affd9f2545.jpg)

# 综合运用

5.已知一个多边形的周长等于 $1 5 8 ~ \mathrm { c m }$ ，所有各边的长成等差数列，最大的边长为 $4 4 \ \mathrm { c m }$ ，公差为$3 \ \mathrm { c m } .$ 求这个多边形的边数.

6.数列 $\{ a _ { n } \}$ ， $\left\{ b _ { n } \right\}$ 都是等差数列，且 $a _ { 1 } = 5$ $b _ { 1 } = 1 5$ ， $a _ { 1 0 0 } + b _ { 1 0 0 } = 1 0 0$ ，求数列 $\{ a _ { n } + b _ { n } \}$ 的前100项的和.

7.已知 $S _ { n }$ 是等差数列 $\left\{ a _ { n } \right\}$ 的前 $_ n$ 项和.

(1）证明 $\left\{ { \frac { S _ { n } } { n } } \right\}$ 是等差数列； （2）设 $T _ { n }$ 为数列 $\left\{ { \frac { S _ { n } } { n } } \right\}$ 的前 $_ n$ 项和，若 $S _ { 4 } = 1 2$ ， $S _ { 8 } = 4 0$ ，求 $T _ { n }$

8.已知两个等差数列2，6，10，，190及2，8，14，，200，将这两个等差数列的公共项按从小到大的顺序组成一个新数列，求这个新数列的各项之和.

）.一支车队有15辆车，某天下午依次出发执行运输任务．第一辆车于14时出发，以后每间隔$1 0 \ \mathrm { m i n }$ 发出一辆车，假设所有的司机都连续开车，并都在18时停下来休息.

（1）截止到18时，最后一辆车行驶了多长时间？（2）如果每辆车行驶的速度都是 $6 0 ~ \mathrm { k m / h }$ ，这个车队当天一共行驶了多少千米？

# 拓广探索

10.已知等差数列 $\left\{ a _ { n } \right\}$ 的公差为 $d$ ，求证 ${ \frac { a _ { m } - a _ { n } } { m - n } } = d$ ，你能从直线的斜率角度来解释这个结果吗？

11.虎甲虫以爬行速度快闻名，下表记录了一只虎甲虫连续爬行 $n$ s $n { = } 1$ ，2，…，10）时爬行的距离.

<html><body><table><tr><td>时间/s</td><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td><td>6</td><td>7</td><td>8</td><td>9</td><td>10</td></tr><tr><td>距离/m</td><td>2.50</td><td>5.03</td><td>7.55</td><td>10.05</td><td>12.45</td><td>15.01</td><td>17.2819.90</td><td></td><td>22.48</td><td>25.07</td></tr></table></body></html>

（1）你能建立一个数列模型，近似地表示这只虎甲虫连续爬行的距离与时间之间的关系吗？

（2）利用建立的模型计算，这只虎甲虫连续爬行 $1 ~ \mathrm { m i n }$ 能爬多远（精确到 $0 . 0 1 \mathrm { ~ m ~ }$ ？它连续爬行 $1 0 ~ \mathrm { m }$ 需要多长时间（精确到0.1s）？

12.如图的形状出现在南宋数学家杨辉所著的《详解九章算法·商功》中，后人称为“三角垛”“三角垛”的最上层有1个球，第二层有3个球，第三层有6个球…设各层球数构成一个数列 $\left\{ a _ { n } \right\}$

（1）写出数列 $\{ a _ { n } \}$ 的一个递推公式；\*（2）根据（1）中的递推公式，写出数列 $\left\{ a _ { n } \right\}$ 的一个通项公式。

![](images/69ae603c523c44d1898145105cca996ee99ec647d38661e8c8001387a4ec1b4a.jpg)  
（第12题）

# 4.3 等比数列

我们知道，等差数列的特征是“从第2项起，每一项与它的前一项的差都等于同一个常数”，类比等差数列的研究思路和方法，从运算的角度出发，你觉得还有怎样的数列是值得研究的？

# 4.3.1等比数列的概念

请看下面几个问题中的数列.

1.两河流域发掘的古巴比伦时期的泥版上记录了下面的数列：

$$
1 0 0 , \ 1 0 0 ^ { 2 } , \ 1 0 0 ^ { 3 } , \ \cdots , \ 1 0 0 ^ { 1 0 } ;
$$

$$
5 , 5 ^ { 2 } , 5 ^ { 3 } , \cdots , 5 ^ { 1 0 } .
$$

2.《庄子·天下》中提到：“一尺之棰，日取其半，万世不竭.”如果把“一尺之棰”的长度看成单位“1”，那么从第1天开始，各天得到的“棰”的长度依次是

①古巴比伦人用六十 进制记数，这里转化为十 进制.

$$
{ \frac { 1 } { 2 } } , { \frac { 1 } { 4 } } , { \frac { 1 } { 8 } } , { \frac { 1 } { 1 6 } } , { \frac { 1 } { 3 2 } } , \cdots .
$$

3.在营养和生存空间没有限制的情况下，某种细菌每$2 0 ~ \mathrm { m i n }$ 就通过分裂繁殖一代，那么一个这种细菌从第1次分裂开始，各次分裂产生的后代个数依次是

4.某人存人银行 $a$ 元，存期为5年，年利率为 $r$ ，那么按照复利，他5年内每年末得到的本利和分别是

$$
a ( 1 + r ) , a ( 1 + r ) ^ { 2 } , a ( 1 + r ) ^ { 3 } , a ( 1 + r ) ^ { 4 } , a ( 1 + r ) ^ { 5 }
$$

![](images/791347e9af5673f61def1443ca4cc4f193fb8fa647c9922640997f6361eaf2cf.jpg)

②复利是指把前一期的利息和本金加在一起算作本金，再计算下一期的利息.

# 探究

类比等差数列的研究，你认为可以通过怎样的运算发现以上数列的取值规律？你发现了什么规律？

我们可以通过除法运算探究以上数列的取值规律.

如果用 $\{ a _ { n } \}$ 表示数列 $\textcircled{1}$ ，那么有

$$
\frac { a _ { 2 } } { a _ { 1 } } = 9 , \ \frac { a _ { 3 } } { a _ { 2 } } = 9 , \ \cdots , \ \frac { a _ { 1 0 } } { a _ { 9 } } = 9 .
$$

这表明，数列 $\textcircled{1}$ 有这样的取值规律：从第2项起，每一项与它的前一项的比都等于9.

其余几个数列也有这样的取值规律，请你写出相应的规律.

# 思考

类比等差数列的概念，从上述几个数列的规律中，你能抽象出等比数列的概念吗？

一般地，如果一个数列从第2项起，每一项与它的前一项的比都等于同一个常数，那么这个数列叫做等比数列（geometricprogression），这个常数叫做等比数列的公比（com-monratio），公比通常用字母 $q$ 表示（显然 $q \neq 0$ ).例如，数列 $\textcircled{1} \sim \textcircled { 6 }$ 的公比依次是9，100，5， $\frac { 1 } { 2 }$ ，2, $1 + r$

与等差中项类似，如果在 $^ a$ 与 $^ { b }$ 中间插人一个数 $G$ ，使 $a$ ， $G$ ， $^ { b }$ 成等比数列，那么$G$ 叫做 $^ a$ 与 $^ { b }$ 的等比中项（geometricmean)．此时， ${ G } ^ { 2 } { = } a b$

# 探究

你能根据等比数列的定义推导它的通项公式吗？

设一个等比数列 $\left\{ a _ { n } \right\}$ 的公比为 $q$ ．根据等比数列的定义，可得

$$
a _ { n + 1 } = a _ { n } \cdot q .
$$

所以

$$
a _ { 2 } = a _ { 1 } q \ ,
$$

$$
a _ { 3 } = a _ { 2 } q = ( a _ { 1 } q ) q = a _ { 1 } q ^ { 2 } ,
$$

$$
a _ { 4 } = a _ { 3 } q = ( a _ { 1 } q ^ { 2 } ) q = a _ { 1 } q ^ { 3 } ,
$$

由此可得

$$
a _ { n } { = } a _ { 1 } q ^ { n - 1 } ~ ( n { \geq } 2 ) .
$$

又 $a _ { 1 } = a _ { 1 } q ^ { 0 } = a _ { 1 } q ^ { 1 - 1 }$ ，这就是说，当 $n { = } 1$ 时上式也成立.因此，首项为 $a _ { 1 }$ ，公比为 $q$ 的等比数列 $\{ a _ { n } \}$ 的通项公式为

$$
a _ { n } = a _ { 1 } q ^ { n - 1 } .
$$

类似于等差数列与一次函数的关系，由 $a _ { n } = { \frac { a _ { 1 } } { q } } \cdot q ^ { n }$ 可知，当 $q > 0$ 且 $q \neq 1$ 时，等比数列 $\left\{ a _ { n } \right\}$ 的第 $n$ 项 $a _ { n }$ 是函数$f ( x ) { \stackrel { } { = } } { \frac { a _ { 1 } } { q } } \cdot q ^ { x } \left( x \in \mathbf { R } \right)$ 当 $x = n$ 时的函数值，即 $a _ { n } = f ( n )$ （如图4.3-1所示）

#

类比指数函数的性质，说说公比 $q > 0$ 的等比数列的单调性。

![](images/93d3ed46b23396c6197bba70d66ec9c6cc1d04810c4a14a450bb4937bdaabca9.jpg)  
图4.3-1

#

公比 $q > 0$ 且 $q \neq 1$ 的等比数列 $\{ a _ { n } \}$ 的图象有什么特点？

反之，任给函数 $f ( x ) = k a ^ { x } ( k , a$ 为常数， $k \neq 0$ ， $a > 0$ ，且 $a \neq 1 ,$ ，则 $f ( 1 ) = k a$ ，$f ( 2 ) = k a ^ { 2 }$ ，…， $f ( n ) = k a ^ { n }$ ，…构成一个等比数列 $\{ k a ^ { n } \}$ ，其首项为 $k a$ ，公比为 $a$

下面，我们利用通项公式解决等比数列的一些问题.

例1若等比数列 $\left\{ a _ { n } \right\}$ 的第4项和第6项分别为48和12，求 $\{ a _ { n } \}$ 的第5项.

分析：等比数列 $\{ a _ { n } \}$ 由 $a _ { 1 }$ ， $q$ 唯一确定，可利用条件列出关于 $\boldsymbol { a } _ { 1 }$ ， $q$ 的方程（组），进行求解. Y R

解法1：由 $a _ { 4 } = 4 8$ ， ${ a _ { 6 } = } 1 2$ ，得

$$
\left\{ { \begin{array} { l } { a _ { 1 } q ^ { 3 } = 4 8 , } \\ { a _ { 1 } q ^ { 5 } = 1 2 . } \end{array} } \right.
$$

$\textcircled{2}$ 的两边分别除以 $\textcircled{1}$ 的两边，得

$$
q ^ { 2 } { = } \frac { 1 } { 4 } .
$$

解得

$$
q = \frac { 1 } { 2 } \nexists \bigl \langle \bigl - \frac { 1 } { 2 } .
$$

把 $q { = } \frac { 1 } { 2 }$ 代人 $\textcircled{1}$ ，得

$$
a _ { 1 } = 3 8 4 .
$$

此时

$$
a _ { 5 } = a _ { 1 } q ^ { 4 } = 3 8 4 \times \left( \frac { 1 } { 2 } \right) ^ { 4 } = 2 4 .
$$

把 $q = - \frac { 1 } { 2 }$ 代 $\textcircled{1}$ ，得

$$
a _ { 1 } = - 3 8 4 .
$$

此时

$$
a _ { 5 } = a _ { 1 } q ^ { 4 } = - 3 8 4 \times ( - \frac { 1 } { 2 } ) ^ { 4 } = - 2 4 .
$$

因此， $\{ a _ { n } \}$ 的第5项是24或-24.

解法2：因为 $a _ { 5 }$ 是 $a _ { 4 }$ 与 $a _ { 6 }$ 的等比中项，所以

$$
a _ { 5 } ^ { 2 } = a _ { 4 } a _ { 6 } = 4 8 \times 1 2 = 5 7 6 .
$$

所以

$$
a _ { 5 } = \pm { \sqrt { 5 7 6 } } = \pm 2 4 .
$$

因此， $\left\{ a _ { n } \right\}$ 的第5项是24或-24.

例2已知等比数列 $\left\{ a _ { n } \right\}$ 的公比为 $q$ ，试用 $\left\{ a _ { n } \right\}$ 的第 $m$ 项 $a _ { m }$ 表示 $\boldsymbol { a } _ { n }$ 解：由题意，得

$$
\begin{array} { l } { { a _ { m } = a _ { 1 } q ^ { m - 1 } , } } \\ { { { } } } \\ { { a _ { n } = a _ { 1 } q ^ { n - 1 } . } } \end{array}
$$

$\textcircled{2}$ 的两边分别除以 $\textcircled{1}$ 的两边，得

$$
{ \frac { a _ { n } } { a _ { m } } } { = } q ^ { n - m } ,
$$

等比数列的任意一项都可以由该数列的某一项和公比表示。

所以

$$
a _ { n } = a _ { m } q ^ { n - m } .
$$

例3数列 $\left\{ a _ { n } \right\}$ 共有5项，前三项成等比数列，后三项成等差数列，第3项等于80，第2项与第4项的和等于136，第1项与第5项的和等于132.求这个数列.

分析：先利用已知条件表示出数列的各项，再进一步根据条件列方程组求解.

解：设前三项的公比为 $q$ ，后三项的公差为 $d$ ，则数列的各项依次为 $\frac { 8 0 } { q ^ { 2 } }$ ， $\frac { 8 0 } { q }$ （ 80，$8 0 + d$ ， $8 0 + 2 d$ 于是得

$$
\left\{ \begin{array} { l l } { \displaystyle { \frac { 8 0 } { q } } + ( 8 0 + d ) = 1 3 6 , } \\ { \quad } \\ { \displaystyle { \frac { 8 0 } { q ^ { 2 } } } + ( 8 0 + 2 d ) = 1 3 2 . } \end{array} \right.
$$

解方程组，得

$$
\stackrel { \scriptscriptstyle } { \stackrel { \scriptstyle } { \scriptscriptstyle { d = 1 6 } } } , \quad \stackrel { \scriptscriptstyle \# } { \scriptscriptstyle { d = - 6 4 } } ,
$$

所以这个数列是20，40，80，96，112，或180，120，80，16，-48.

# 练习

1.判断下列数列是不是等比数列，如果是，写出它的公比.

(1）3，9，15,21，27，33； (2）1，1.1，1.21，1.331，1.4641;  
（3) ${ \begin{array} { l } { { \frac { 1 } { 3 } } , { \frac { 1 } { 6 } } , { \frac { 1 } { 9 } } , { \frac { 1 } { 1 2 } } , { \frac { 1 } { 1 5 } } , { \frac { 1 } { 1 8 } } ; } \end{array} }$ (4）4，-8，16，-32，64，-128.

2.已知 $\left\{ a _ { n } \right\}$ 是一个公比为 $q$ 的等比数列，在下表中填上适当的数.

<html><body><table><tr><td>a1</td><td>a3</td><td>a5</td><td>a7</td><td>q</td></tr><tr><td>2</td><td></td><td>8</td><td></td><td></td></tr><tr><td></td><td>2</td><td></td><td></td><td>0.2</td></tr></table></body></html>

3.在等比数列 $\left\{ a _ { n } \right\}$ 中， $a _ { 1 } a _ { 3 } = 3 6$ ， $a _ { 2 } + a _ { 4 } = 6 0$ 求 $a _ { 1 }$ 和公比 $q$

4.对于数列 $\left\{ a _ { n } \right\}$ ，若点 $( n , \ a _ { n } )$ $\boldsymbol { n } \in \mathbf { N } ^ { \ast }$ ）都在函数 $y = c q ^ { x }$ 的图象上，其中 $c$ ， $q$ 为常数，且 $c \neq 0$ $q \not = 0 , q \not = 1$ ，试判断数列 $\left\{ a _ { n } \right\}$ 是不是等比数列，并证明你的结论.

5.已知数列 $\{ a _ { n } \}$ 是等比数列.

(1） $\boldsymbol { a } _ { 3 }$ ， $a _ { 5 }$ ， $a _ { 7 }$ 是否构成等比数列？为什么？ $a _ { 1 }$ ， $a _ { 5 }$ ， $a _ { 9 }$ 呢？(2）当 $n { > } 1$ 时， $\boldsymbol { a } _ { n - 1 }$ $\ldots , a _ { n } \ldots a$ $\boldsymbol a _ { n + 1 }$ 是否构成等比数列？为什么？当 $n { > } k { > } 0$ 时， $\boldsymbol { a } _ { n - k }$ ， $a _ { n }$ ， $a _ { n + k }$ 是否构成等比数列？

例4用10000元购买某个理财产品一年.

（1）若以月利率 $0 . 4 0 0 \%$ 的复利计息，12个月能获得多少利息（精确到0.01元)？

（2）若以季度复利计息，存4个季度，则当每季度利率为多少时，按季结算的利息不少于按月结算的利息（精确到 $1 0 ^ { - 5 }$ ）？ 1D

分析：复利是指把前一期的利息与本金之和算作本金，再计算下一期的利息，所以若原始本金为 $a$ 元，每期的利率为 $r$ ，则从第一期开始，各期的本利和 $a$ ， $\boldsymbol { a } \left( 1 + r \right)$ $a ( 1 + r ) ^ { 2 }$ ，…构成等比数列。

解：（1）设这笔钱存 $n$ 个月以后的本利和组成一个数列 $\{ a _ { n } \}$ ，则 $\{ a _ { n } \}$ 是等比数列，首项 $a _ { 1 } { = } 1 0 ^ { 4 } ( 1 { + } 0 . 4 0 0 \% )$ ，公比 $q = 1 + 0 . 4 0 0 \%$ ，所以

$$
a _ { 1 2 } = 1 0 ^ { 4 } ( 1 \small { + } 0 . 4 0 0 \% ) ^ { 1 2 } \approx 1 0 ~ 4 9 0 . 7 0 2 .
$$

所以，12个月后的利息为 $1 0 ~ 4 9 0 . 7 0 2 - 1 0 ^ { 4 } \approx 4 9 0 . 7 0$ （元）.

（2）设季度利率为 $r$ ，这笔钱存 $_ n$ 个季度以后的本利和组成一个数列 $\left\{ b _ { n } \right\}$ ，则 $\{ b _ { n } \}$ 也是一个等比数列，首项 $b _ { 1 } { = } 1 0 ^ { 4 } ( 1 { + } r )$ ，公比为 $1 + r$ ，于是

$$
b _ { 4 } { = } 1 0 ^ { 4 } ( 1 { + } r ) ^ { 4 } .
$$

因此，以季度复利计息，存4个季度后的利息为

$$
\left[ 1 0 ^ { 4 } ( 1 + r ) ^ { 4 } - 1 0 ^ { 4 } \right] \overline { { \mathcal { D } } } .
$$

解不等式 $1 0 ^ { 4 } ( 1 + r ) ^ { 4 } - 1 0 ^ { 4 } \geq 4 9 0 . 7 0$ ，得

$$
r { \geq } 1 . 2 0 5 \% .
$$

所以，当季度利率不小于 $1 . 2 0 5 \%$ 时，按季结算的利息不少于按月结算的利息.

例5已知数列 $\{ a _ { n } \}$ 的首项 $a _ { 1 } = 3$

（1）若 $\left\{ a _ { n } \right\}$ 为等差数列，公差 $d = 2$ ，证明数列 $\{ 3 ^ { a _ { n } } \}$ 为等比数列；

(2）若 $\left\{ a _ { n } \right\}$ 为等比数列，公比 $q = \frac { 1 } { 9 }$ ，证明数列 $\left\{ \log _ { 3 } a _ { n } \right\}$ 为等差数列.

分析：根据题意，需要从等差数列、等比数列的定义出发，利用指数、对数的知识进行证明.

证明：（1）由 $a _ { 1 } = 3$ ， $d = 2$ ，得 $\left\{ a _ { n } \right\}$ 的通项公式为

$$
a _ { n } = 2 n + 1 .
$$

设 $b _ { n } = 3 ^ { a _ { n } }$ ，则

$$
\frac { b _ { n + 1 } } { b _ { n } } { = } \frac { 3 ^ { 2 ( n + 1 ) + 1 } } { 3 ^ { 2 n + 1 } } { = } 9 .
$$

又

$$
b _ { 1 } = 3 ^ { 3 } = 2 7 ,
$$

所以， $\{ 3 ^ { a _ { n } } \}$ 是以27为首项，9为公比的等比数列.

(2）由 $a _ { 1 } = 3 , q = \frac { 1 } { 9 }$ ，得

$$
a _ { n } = 3 \times \left( { \frac { 1 } { 9 } } \right) ^ { n - 1 } = 3 ^ { 3 - 2 n } .
$$

两边取以3为底的对数，得

$$
\log _ { 3 } a _ { n } = \log _ { 3 } 3 ^ { 3 - 2 n } = 3 - 2 n .
$$

所以

$$
\log _ { 3 } a _ { n + 1 } - \log _ { 3 } a _ { n } = [ 3 - 2 ( n + 1 ) ] - ( 3 - 2 n ) = - 2 .
$$

又

$$
\log _ { 3 } a _ { 1 } = \log _ { 3 } 3 = 1 ,
$$

所以， $\left\{ \log _ { 3 } a _ { n } \right\}$ 是首项为1，公差为一2的等差数列.

# 思考

已知 $b > 0$ 且 $b \neq 1$ ，如果数列 $\left\{ a _ { n } \right\}$ 是等差数列，那么数列 $\{ b ^ { a _ { n } } \}$ 是否一定是等比数列？如果数列 $\left\{ a _ { n } \right\}$ 是各项均为正的等比数列，那么数列 $\left\{ \log _ { b } a _ { n } \right\}$ 是否一定是等差数列？

例6某工厂去年12月试产1050个高新电子产品，产品合格率为 $90 \%$ 从今年1月开始，工厂在接下来的两年中将生产这款产品．1月按去年12月的产量和产品合格率生产，以后每月的产量都在前一个月的基础上提高 $5 \%$ ，产品合格率比前一个月增加 $0 . 4 \%$ .那么生产该产品一年后，月不合格品的数量能否控制在100个以内？

分析：设从今年1月起，各月的产量及不合格率分别构成数列 $\{ a _ { n } \}$ ， $\left\{ b _ { n } \right\}$ ，则各月不合格品的数量构成数列 $\{ a _ { n } b _ { n } \}$ ，由题意可知，数列 $\left\{ a _ { n } \right\}$ 是等比数列， $\left\{ b _ { n } \right\}$ 是等差数列．由于数列 $\{ a _ { n } b _ { n } \}$ 既非等差数列又非等比数列，所以可以先列表观察规律，再寻求问题的解决方法.

解：设从今年1月起，各月的产量及不合格率分别构成数列 $\left\{ a _ { n } \right\}$ ， $\left\{ b _ { n } \right\}$

由题意，知

$a _ { n } = 1 0 5 0 \times 1 . 0 5 ^ { n - 1 }$ $b _ { n } = 1 - [ 9 0 \% + 0 . 4 \% ( n - 1 ) ] = 0 . 1 0 4 - 0 . 0 0 4 n$ ，其中 $n = 1$ ，2，…，24,则从今年1月起，各月不合格产品的数量是

$$
\begin{array} { c } { { a , b , _ { n } = 1 0 5 0 \times 1 . \ 0 5 ^ { n - 1 } \times \left( 0 . 1 0 4 - 0 . 0 0 4 n \right) } } \\ { { { } } } \\ { { = 1 . \ 0 5 ^ { n } \times \left( 1 0 4 - 4 n \right) . } } \end{array}
$$

由计算工具计算（精确到0.1)，并列表（表4.3-1).

表4.3-1  

<html><body><table><tr><td>n</td><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td><td>6</td><td>7</td></tr><tr><td>anbn</td><td>105.0</td><td>105.8</td><td>106.5</td><td>107.0</td><td>107.2</td><td>107.2</td><td>106.9</td></tr><tr><td>n</td><td>8</td><td>9</td><td>10</td><td>11</td><td>12</td><td>13</td><td>14</td></tr><tr><td>anbn</td><td>106.4</td><td>105.5</td><td>104.2</td><td>102.6</td><td>100.6</td><td>98.1</td><td>95.0</td></tr></table></body></html>

观察发现，数列 $\{ a _ { n } b _ { n } \}$ 先递增，在第6项以后递减，所以只要设法证明当 $n \geqslant 6$ 时，$\{ a _ { n } b _ { n } \}$ 递减，且 $a _ { 1 3 } b _ { 1 3 } { < } 1 0 0$ 即可 R

由

$$
\frac { a _ { n + 1 } b _ { n + 1 } } { a _ { n } b _ { n } } { = } \frac { 1 . 0 5 ^ { n + 1 } \times \left[ 1 0 4 - 4 ( n + 1 ) \right] } { 1 . 0 5 ^ { n } \times ( 1 0 4 - 4 n ) } { < } 1 ,
$$

得

$$
n > 5 .
$$

所以，当 $n \geqslant 6$ 时， $\{ a _ { n } b _ { n } \}$ 递减.

又

$$
a _ { 1 3 } b _ { 1 3 } \approx 9 8 < 1 0 0 ,
$$

所以，当 $1 3 { \leqslant } n { \leqslant } 2 4$ 时， $a _ { n } b _ { n } { \leqslant } a _ { 1 3 } b _ { 1 3 } { \leqslant } 1 0 0 .$

![](images/e6f6cf400b08f2989dfc12b0ee5b13a257fd2f58082294699e400f57773c4cad.jpg)

所以，生产该产品一年后，月不合格品的数量能控制在100个以内.

1.求满足下列条件的数：

（1）在9与243中间插入2个数，使这4个数成等比数列；  
（2）在160与一5中间插入4个数，使这6个数成等比数列.

2.设数列 $\{ a _ { n } \}$ ， $\left\{ b _ { n } \right\}$ 都是等比数列，分别研究下列数列是不是等比数列．若是，证明结论；若不是，请说明理由.

（1）数列 $\left\{ c _ { n } \right\}$ ，其中 $\scriptstyle { c _ { n } = a _ { n } b _ { n } }$ （2）数列 $\{ d _ { n } \}$ ，其中 $d _ { n } = \frac { a _ { n } } { b _ { n } }$

3.某汽车集团计划大力发展新能源汽车，2017年全年生产新能源汽车5000辆．如果在后续的几年中，后一年新能源汽车的产量都是前一年的 $1 5 0 \%$ ，那么2025年全年约生产新能源汽车多少辆（精确到1）？

4.某城市今年空气质量为“优”“良”的天数为105，力争2年后使空气质量为“优”“良”的天数达到240.这个城市空气质量为“优”“良”的天数的年平均增长率应达到多少（精确到0.01)？

5.已知数列 $\left\{ a _ { n } \right\}$ 的通项公式为 $a _ { n } = { \frac { n ^ { 3 } } { 3 ^ { n } } }$ 求使 $a _ { n }$ 取得最大值时 $_ n$ 的值.

$$
\yen 123,456,7
$$

# 4.3.2等比数列的前 $n$ 项和公式

![](images/a76682b5bb42eeb90a6abd6ee171d7dbe19200b7bb8f0fc62d5f22dd35a6f2a4.jpg)

国际象棋起源于古印度．相传国王要奖赏国际象棋的发明者，问他想要什么．发明者说：“请在棋盘的第1个格子里放上1颗麦粒，第2个格子里放上2颗麦粒，第3个格子里放上4颗麦粒…依此类推，每个格子里放的麦粒数都是前一个格子里放的麦粒数的2倍，直到第64个格子．请给我足够的麦粒以实现上述要求.”国王觉得这个要求不高，就欣然同意了．已知1000颗麦粒的质量约为 $4 0 \ \mathrm { g }$ ，据查，2016—2017年度世界小麦产量约为7.5亿吨，根据以上数据，判断国王是否能实现他的诺言.

让我们一起来分析一下．如果把各格所放的麦粒数看成一个数列，我们可以得到一个等比数列，它的首项是1，公比是2，求第1个格子到第64个格子各格所放的麦粒数总和就是求这个等比数列前64项的和.

一般地，如何求一个等比数列的前 $n$ 项和呢？

设等比数列 $\left\{ a _ { n } \right\}$ 的首项为 $a _ { 1 }$ ，公比为 $q$ ，则 $\{ a _ { n } \}$ 的前 $n$ 项和是

$$
S _ { n } = a _ { 1 } + a _ { 2 } + a _ { 3 } + \cdots + a _ { n } .
$$

根据等比数列的通项公式，上式可写成

$$
S _ { n } { = } a _ { 1 } { + } a _ { 1 } q { + } a _ { 1 } q ^ { 2 } { + } { \cdots } { + } a _ { 1 } q ^ { n - 1 } .
$$

我们发现，如果用公比 $q$ 乘 $\textcircled{1}$ 的两边，可得

$$
q S _ { n } { = } a _ { 1 } q { + } a _ { 1 } q ^ { 2 } { + } { \cdots } { + } a _ { 1 } q ^ { n - 1 } { + } a _ { 1 } q ^ { n } .
$$

$\textcircled{1} \textcircled{2}$ 两式的右边有很多相同的项，用 $\textcircled{1}$ 的两边分别减去 $\textcircled{2}$ 的两边，就可以消去这些相同的项，可得 $S _ { n } - q S _ { n } = a _ { 1 } - a _ { 1 } q ^ { n }$ ，即

$$
( 1 - q ) S _ { n } = a _ { 1 } ( 1 - q ^ { n } ) .
$$

因此，当 $q \neq 1$ 时，我们就得到了等比数列的前 $n$ 项和公式

$$
S _ { n } { \mathop { = } } { \frac { a _ { 1 } ( 1 { \mathop { - } } q ^ { n } ) } { 1 { \mathop { - } } q } } \ ( q { \mathop { \neq } } 1 ) .
$$

因为 $a _ { n } = a _ { 1 } q ^ { n - 1 }$ ，所以公式（1）还可以写成

$$
S _ { n } { = } \frac { a _ { 1 } { - } a _ { n } q } { 1 { - } q } ~ ( q { \neq } 1 ) .
$$

当 $q = 1$ 时，等比数列的前 $_ n$ 项和 $S _ { n }$ 等于多少？

有了上述公式，就可以解决本小节开头提出的问题了。

由 $a _ { 1 } = 1 , \ q = 2$ ， $n = 6 4$ ，可得

$$
S _ { 6 4 } = \frac { 1 \times ( 1 - 2 ^ { 6 4 } ) } { 1 - 2 } = 2 ^ { 6 4 } - 1 .
$$

$2 ^ { 6 4 } - 1$ 这个数很大，超过了 $1 . 8 4 \times 1 0 ^ { 1 9 }$ ，如果一千颗麦粒的质量约为 $4 0 \ \mathrm { g }$ ，那么以上这些麦粒的总质量超过了7000亿吨，约是2016—2017年度世界小麦产量的981倍．因此，国王根本不可能实现他的诺言.

例7已知数列 $\{ a _ { n } \}$ 是等比数列.

(1）若 $a _ { 1 } = { \frac { 1 } { 2 } } , \ q = { \frac { 1 } { 2 } }$ 求 $S _ { 8 }$ (2)若 $a _ { 1 } = 2 7$ ， $a _ { 9 } = \frac { 1 } { 2 4 3 }$ ， $q < 0$ ，求 $S _ { 8 }$ $a _ { 1 } = 8 , q = \frac { 1 } { 2 } , S _ { n } = \frac { 3 1 } { 2 }$ 求 $n$ ：

#

对于等比数列的相关量 $a _ { 1 }$ ， $a _ { n } , \textit { q }$ ， $n$ ， $S _ { n }$ ，已知几个量就可以确定其他量？

解：（1）因为 $\displaystyle a _ { 1 } = \frac { 1 } { 2 } , \ d = \frac { 1 } { 2 }$ ，所以

$$
S _ { 8 } { = } \frac { \displaystyle \frac { 1 } { 2 } { \times } \left[ 1 { - } \left( \frac { 1 } { 2 } \right) ^ { 8 } \right] } { 1 { - } \displaystyle \frac { 1 } { 2 } } { = } \frac { 2 5 5 } { 2 5 6 } .
$$

(2）由 $a _ { 1 } = 2 7$ ， $a _ { 9 } = \frac { 1 } { 2 4 3 }$ ，可得

$$
2 7 \times q ^ { 8 } = \frac { 1 } { 2 4 3 } ,
$$

即

$$
q ^ { 8 } = { \left( \frac { 1 } { 3 } \right) } ^ { 8 } .
$$

又由 $q < 0$ ，得

$$
\scriptstyle q = - { \frac { 1 } { 3 } } ,
$$

所以

$$
S _ { 8 } = { \frac { 2 7 \times \left[ 1 - \left( - { \frac { 1 } { 3 } } \right) ^ { 8 } \right] } { 1 - \left( - { \frac { 1 } { 3 } } \right) } } { \frac { 1 6 4 0 } { 8 1 } } .
$$

（3）把 $a _ { 1 } = 8 , q = \frac { 1 } { 2 } , S _ { n } = \frac { 3 1 } { 2 } ,$ $S _ { n } { = } \frac { a _ { 1 } ( 1 { - } q ^ { n } ) } { 1 { - } q }$ ，得

$$
\frac { 8 \times \left[ 1 - \left( { \frac { 1 } { 2 } } \right) ^ { n } \right] } { 1 - { \frac { 1 } { 2 } } } { = } { \frac { 3 1 } { 2 } } .
$$

整理，得

$$
\left( { \frac { 1 } { 2 } } \right) ^ { n } = { \frac { 1 } { 3 2 } } .
$$

解得

$$
n = 5 .
$$

例8已知等比数列 $\left\{ a _ { n } \right\}$ 的首项为－1，前 $n$ 项和为 $S _ { n }$ .若 $\frac { S _ { 1 0 } } { S _ { 5 } } { = } \frac { 3 1 } { 3 2 }$ 32，求公比q

解：若 $q = 1$ ，则

$$
\frac { S _ { 1 0 } } { S _ { 5 } } = \frac { 1 0 a _ { 1 } } { 5 a _ { 1 } } = 2 \neq \frac { 3 1 } { 3 2 } ,
$$

所以

$$
q \neq 1 .
$$

当 $q \neq 1$ 时，由 $\frac { S _ { 1 0 } } { S _ { 5 } } { = } \frac { 3 1 } { 3 2 }$ ，得

$$
\frac { ( - 1 ) ( 1 { - } q ^ { 1 0 } ) } { 1 { - } q } \bullet \frac { 1 { - } q } { ( - 1 ) ( 1 { - } q ^ { 5 } ) } { = } \frac { 3 1 } { 3 2 } \bullet
$$

整理，得

$$
1 + q ^ { 5 } = { \frac { 3 1 } { 3 2 } } ,
$$

即

$$
q ^ { 5 } = - { \frac { 1 } { 3 2 } } .
$$

所以

$$
q = - { \frac { 1 } { 2 } } .
$$

例9已知等比数列 $\{ a _ { n } \}$ 的公比 $q \neq - 1$ ，前 $n$ 项和为 $S _ { n }$ ．证明 $S _ { n }$ ， $S _ { 2 n } - S _ { n }$ ， $S _ { 3 n } -$ $S _ { 2 n }$ 成等比数列，并求这个数列的公比.

证明：当 $q = 1$ 时，

$$
\begin{array} { r c l } { S _ { n } = n a _ { 1 } , } \\ { } & { } & { } \\ { } & { } & { } \\ { } & { } & { } & { S _ { 2 n } - S _ { n } = 2 n a _ { 1 } - n a _ { 1 } = n a _ { 1 } , } \\ { } & { } & { } \\ { } & { } & { } & { S _ { 3 n } - S _ { 2 n } = 3 n a _ { 1 } - 2 n a _ { 1 } = n a _ { 1 } , } \end{array}
$$

所以 $S _ { n }$ ， $S _ { 2 n } - S _ { n }$ ， $S _ { 3 n } - S _ { 2 n }$ 成等比数列，公比为1.

当 $q \neq 1$ 时，

想一想，不用分类讨论的方式能否证明该结论？

$$
\begin{array} { l } { \displaystyle { S _ { n } = \frac { a _ { 1 } ( 1 - q ^ { n } ) } { 1 - q } , } } \\ { \displaystyle { S _ { 2 n } - S _ { n } = \frac { a _ { 1 } ( 1 - q ^ { 2 n } ) } { 1 - q } - \frac { a _ { 1 } ( 1 - q ^ { n } ) } { 1 - q } = \frac { a _ { 1 } q ^ { n } ( 1 - q ^ { n } ) } { 1 - q } = q ^ { n } S _ { n } , } } \\ { \displaystyle { S _ { 3 n } - S _ { 2 n } = \frac { a _ { 1 } ( 1 - q ^ { 3 n } ) } { 1 - q } - \frac { a _ { 1 } ( 1 - q ^ { 2 n } ) } { 1 - q } = \frac { a _ { 1 } q ^ { 2 n } ( 1 - q ^ { n } ) } { 1 - q } = q ^ { n } ( S _ { 2 n } - S _ { n } ) , } } \end{array}
$$

所以

$$
{ \frac { S _ { 2 n } - S _ { n } } { S _ { n } } } { = } { \frac { S _ { 3 n } - S _ { 2 n } } { S _ { 2 n } - S _ { n } } } { = } q ^ { n } .
$$

因为 $q ^ { n }$ 为常数，所以 $S _ { n }$ ， $S _ { 2 n } - S _ { n }$ ， $S _ { 3 n } - S _ { 2 n }$ 成等比数列，公比为 $q ^ { n }$

# 练习

1.已知数列 $\{ a _ { n } \}$ 是等比数列.

（1）若 $a _ { 1 } = 3 , \ q = 2 , \ n = 6$ ，求 $S _ { n }$ （2）若 $a _ { 1 } = - 2 . 7 , q = - \frac { 1 } { 3 } , a _ { n } = \frac { 1 } { 9 0 }$ ，求S； （3）若 $a _ { 3 } = \frac { 3 } { 2 } , \ S _ { 3 } = \frac { 9 } { 2 }$ ，求 $\boldsymbol { a } _ { 1 }$ 与 $q$

2.已知 $a \neq b$ ，且 $a b \neq 0$ 对于 $n \in \mathbf { N } ^ { \ast }$ ，证明：

$$
a ^ { n } + a ^ { n - 1 } b + a ^ { n - 2 } b ^ { 2 } + \cdots + a b ^ { n - 1 } + b ^ { n } = { \frac { a ^ { n + 1 } - b ^ { n + 1 } } { a - b } } .
$$

3.设等比数列 $\{ a _ { n } \}$ 的前 $n$ 项和为 $S _ { n }$ ，已知 $a _ { 2 } = 6$ ， $6 a _ { 1 } + a _ { 3 } = 3 0$ 求 $\alpha _ { n }$ 和 $S _ { n }$

4.已知三个数成等比数列，它们的和等于14，积等于64．求这个等比数列的首项和公比.

5.如果一个等比数列前5项的和等于10，前10项的和等于50，那么这个数列的公比等于多少？

例10如图4.3-2，正方形ABCD的边长为 $5 ~ \mathrm { c m }$ ，取正方形ABCD各边的中点 $E$ ， $F$ ， $G$ ， $H$ ，作第2个正方形EFGH，然后再取正方形EFGH各边的中点 $I$ ， $J$ ， $K$ ， $L$ ，作第3个正方形$I J K L$ ，依此方法一直继续下去.

（1）求从正方形ABCD开始，连续10个正方形的面积之和；

（2）如果这个作图过程可以一直继续下去，那么所有这些正方形的面积之和将趋近于多少？

![](images/d48405325c1cbcbb2a1bdda65fb87a98e5fb3eefe515e2d95cf5c257555910f8.jpg)  
图4.3-2

分析：可以利用数列表示各正方形的面积，根据条件可知，这是一个等比数列.

解：设正方形ABCD的面积为 $a _ { 1 }$ ，后继各正方形的面积依次为 $a _ { 2 }$ ， $a _ { 3 }$ ，…， $a _ { n }$ ，…，则

$$
a _ { 1 } = 2 5 .
$$

由于第 $k + 1$ 个正方形的顶点分别是第 $k$ 个正方形各边的中点，所以

$$
\boldsymbol { a } _ { k + 1 } = \frac { 1 } { 2 } \boldsymbol { a } _ { k } . ^ { \bullet }
$$

因此， $\left\{ a _ { n } \right\}$ 是以25为首项， $\frac { 1 } { 2 }$ 为公比的等比数列.

①你能说明理由吗？

设 $\{ a _ { n } \}$ 的前 $n$ 项和为 $S _ { n }$

③ $S _ { 1 0 } = \frac { 2 5 \times { \left[ 1 - { \left( \frac { 1 } { 2 } \right) } ^ { 1 0 } \right] } } { 1 - { \frac { 1 } { 2 } } } = 5 0 \times { \left[ 1 - { \left( \frac { 1 } { 2 } \right) } ^ { 1 0 } \right] } = \frac { 2 5 ~ 5 7 5 } { 5 1 2 } .$ 所以，前10个正方形的面积之和为 ${ \frac { 2 5 \ 5 7 5 } { 5 1 2 } } \ \mathrm { c m } ^ { 2 } .$

(2）当 $n$ 无限增大时， $S _ { n }$ 无限趋近于所有正方形的面积和 $a _ { 1 } + a _ { 2 } + a _ { 3 } + \cdots + a _ { n } + \cdots$ 而

$$
S _ { n } { = } \frac { 2 5 { \times } \left[ 1 { - } \left( \frac { 1 } { 2 } \right) ^ { n } \right] } { 1 { - } \displaystyle \frac { 1 } { 2 } } { = } 5 0 \Big [ 1 { - } \left( \frac { 1 } { 2 } \right) ^ { n } \Big ] ,
$$

随着 $n$ 的无限增大， $\left( { \frac { 1 } { 2 } } \right)$ 2 将趋近于0， $S _ { n }$ 将趋近于50.

所以，所有这些正方形的面积之和将趋近于50.

例11去年某地产生的生活垃圾为20万吨，其中14万吨垃圾以填埋方式处理，6万吨垃圾以环保方式处理．预计每年生活垃圾的总量递增 $5 \%$ ，同时，通过环保方式处理的垃圾量每年增加1.5万吨．为了确定处理生活垃圾的预算，请写出从今年起 $n$ 年内通过填埋方式处理的垃圾总量的计算公式，并计算从今年起5年内通过填埋方式处理的垃圾总量（精确到0.1万吨）.

分析：由题意可知，每年生活垃圾的总量构成等比数列，而每年以环保方式处理的垃圾量构成等差数列，因此，可以利用等差数列、等比数列的知识进行计算.

解：设从今年起每年生活垃圾的总量（单位：万吨）构成数列 $\{ a _ { n } \}$ ，每年以环保方式处理的垃圾量（单位：万吨）构成数列 $\left\{ b _ { n } \right\}$ ， $n$ 年内通过填埋方式处理的垃圾总量为 $S _ { n }$ （单位：万吨），则

$$
a _ { n } = 2 0 ( 1 + 5 \% ) ^ { n } ,
$$

$$
\displaystyle b _ { n } = 6 + 1 . 5 n ,
$$

$$
\begin{array} { r l } & { S _ { n } = ( a _ { 1 } - b _ { 1 } ) + ( a _ { 2 } - b _ { 2 } ) + \dots + ( a _ { n } - b _ { n } ) } \\ & { \quad = ( a _ { 1 } + a _ { 2 } + \dots + a _ { n } ) - ( b _ { 1 } + b _ { 2 } + \dots + b _ { n } ) } \\ & { \quad = ( 2 0 \times 1 . 0 5 + 2 0 \times 1 . 0 5 ^ { 2 } + \dots + 2 0 \times 1 . 0 5 ^ { n } ) - ( 7 . 5 + 9 + \dots + 6 + 1 . 5 n ) } \\ & { \quad = \frac { ( 2 0 \times 1 . 0 5 ) \times ( 1 - 1 . 0 5 ^ { n } ) } { 1 - 1 . 0 5 } - \frac { n } { 2 } ( 7 . 5 + 6 + 1 . 5 n ) } \\ & { \quad = 4 2 0 \times 1 . 0 5 ^ { n } - \frac { 3 } { 4 } n ^ { 2 } - \frac { 2 7 } { 4 } n - 4 2 0 . } \end{array}
$$

当 $n = 5$ 时， $S _ { 5 } { \approx } 6 3 . 5$

所以，从今年起5年内，通过填埋方式处理的垃圾总量约为63.5万吨.

例12某牧场今年初牛的存栏数为1200，预计以后每年存栏数的增长率为 $8 \%$ ，且在每年年底卖出100头牛．设牧场从今年起每年年初的计划存栏数依次为 $c _ { 1 }$ ， $c _ { 2 }$ ，$c _ { 3 }$ ，….

（1）写出一个递推公式，表示 $c _ { n + 1 }$ 与 $c _ { n }$ 之间的关系；  
（2）将（1）中的递推公式表示成 $\displaystyle { c _ { n + 1 } - k = r ( c _ { n } - k ) }$ 的形式，其中 $k$ ， $r$ 为常数；  
（3）求 $S _ { 1 0 } { = } c _ { 1 } { + } c _ { 2 } { + } c _ { 3 } { + } { \cdots + } c _ { 1 0 }$ 的值（精确到1).

分析：（1）可以利用“每年存栏数的增长率为 $8 \% ^ { 9 9 }$ 和“每年年底卖出100头”建立$c _ { n + 1 }$ 与 $c _ { n }$ 的关系；（2）这是待定系数法的应用，可以将它还原为（1）中的递推公式的形式，通过比较系数，得到方程组；（3）利用（2）的结论可得出解答。

解：（1）由题意，得 $c _ { 1 } { = } 1 2 0 0$ ，并且

$$
c _ { n + 1 } = 1 . 0 8 c _ { n } - 1 0 0 .
$$

(2）将 $\scriptstyle { c _ { n + 1 } - k = r ( c _ { n } - k ) }$ 化成

$$
\displaystyle c _ { n + 1 } = r c _ { n } - r k + k .
$$

比较 $\textcircled{1} \textcircled{2}$ 的系数，可得

$$
\left\{ { \begin{array} { l } { r = 1 . 0 8 , } \\ { k - r k = - 1 0 0 . } \end{array} } \right.
$$

解这个方程组，得

$$
\begin{array} { r } { f ^ { r = 1 , 0 8 , } } \\ { k = 1 2 5 0 . } \end{array}
$$

所以，（1）中的递推公式可以化为

$$
c _ { n + 1 } - 1 2 5 0 { = } 1 . 0 8 ( c _ { n } { - } 1 2 5 0 ) .
$$

#

$( c _ { 1 } - 1 2 5 0 ) + ( c _ { 2 } - 1 2 5 0 ) + ( c _ { 3 } - 1 2 5 0 ) + \cdots + ( c _ { 1 0 } - 1 2 5 0 )$ $= { \frac { - 5 0 \times ( 1 - 1 . 0 8 ^ { 1 0 } ) } { 1 - 1 . 0 8 } } { \approx } - 7 2 4 . 3 .$

利用递推公式，借助于电子表格的计算，能快捷地求得（3）的结果.你可以试一试。

所以

$$
S _ { 1 0 } { = } c _ { 1 } + c _ { 2 } + c _ { 3 } + \cdots + c _ { 1 0 } { \approx } 1 2 5 0 { \times } 1 0 { - } 7 2 4 . 3 { = } 1 1 \ 7 7 5 . 7 { \approx } 1 1 \ 7 7 6 .
$$

# 练习

1.一个乒乓球从 $1 \textrm { m }$ 高的高度自由落下，每次落下后反弹的高度都是原来高度的0.61倍.

（1）当它第6次着地时，经过的总路程是多少（精确到 $1 \ \mathrm { c m }$ ？（2）至少在第几次着地后，它经过的总路程能达到 $4 0 0 ~ \mathrm { c m ? }$

2.某牛奶厂2015年初有资金1000万元，由于引进了先进生产设备，资金年平均增长率可达到 $50 \%$ 每年年底扣除下一年的消费基金后，剩余资金投入再生产，这家牛奶厂每年应扣除多少消费基金才能实现经过5年资金达到2000万元的目标（精确到1万元）？

3.已知数列 $\{ a _ { n } \}$ 的前 $_ n$ 项和为 $S _ { n }$ ，若 $S _ { n } { = } 2 a _ { n } { + } 1$ ，求 $S _ { n }$

# 习题4.3

# 复习巩固

1.已知数列 $\{ a _ { n } \}$ 是等比数列.

（1）若 $a _ { 1 } = - 1$ ， $a _ { 4 } = 6 4$ ，求 $q$ 与 $S _ { 4 }$ (2）若 $a _ { 5 } - a _ { 1 } = 1 5$ ， $a _ { 4 } - a _ { 2 } = 6$ ，求 $\boldsymbol { a } _ { 3 }$

2.已知 $\{ a _ { n } \}$ 是一个无穷等比数列，公比为 $q$

（1）将数列 $\{ a _ { n } \}$ 中的前 $k$ 项去掉，剩余项组成一个新数列，这个新数列是等比数列吗？如果是，它的首项与公比分别是多少？  
（2）取出数列 $\{ a _ { n } \}$ 中的所有奇数项，组成一个新数列，这个新数列是等比数列吗？如果是，它的首项与公比分别是多少？  
（3）在数列 $\{ a _ { n } \}$ 中，从第1项起，每隔10项取出一项，组成一个新数列，这个新数列是等比数列吗？如果是，它的公比是多少？你能根据得到的结论作出关于等比数列的一个猜想吗？

3.求和：

（1） $( 2 - 3 \times 5 ^ { - 1 } ) + ( 4 - 3 \times 5 ^ { - 2 } ) + \cdots + ( 2 n - 3 \times 5 ^ { - n } ) ;$ (2） $1 + 2 x + 3 x ^ { 2 } + \cdots + n x ^ { n - 1 }$

4.放射性元素在 $t = 0$ 时的原子核总数为 $N _ { \mathrm { 0 } }$ ，经过一年原子核总数衰变为 $N _ { \circ } q$ ，常数 $1 - q$ 称为年衰变率，考古学中常利用死亡的生物体中碳14元素稳定持续衰变的现象测定遗址的年代.已知碳14的半衰期为5730年.

（1）碳14的年衰变率为多少（精确到 $1 0 ^ { - 6 }$ ）？  
（2）某动物标本中碳14含量为正常大气中碳14含量的 $60 \%$ （即衰变了 $40 \%$ ，该动物的死亡时间大约距今多少年（精确到1年）？

# 综合运用

.已知 $S _ { n }$ 是等比数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和， $S _ { 3 }$ ， $S _ { 9 }$ ， $S _ { 6 }$ 成等差数列.求证： $a _ { 2 }$ ， $a _ { 8 }$ ， $a _ { 5 }$ 成等差数列.

6.求下列数列的一个通项公式和一个前 $n$ 项和公式：

1,11，111,1 111，11 111，.

7.已知数列 $\{ a _ { n } \}$ 的首项 $a _ { 1 } = 1$ ，且满足 $a _ { n + 1 } + a _ { n } = 3 \times 2 ^ { n }$ （1）求证： $\{ a _ { n } - 2 ^ { n } \}$ 是等比数列. （2）求数列 $\{ a _ { n } \}$ 的前 $n$ 项和 $S _ { n }$

8.若数列 $\{ a _ { n } \}$ 的首项 $a _ { 1 } = 1$ ，且满足 $a _ { n + 1 } = 2 a _ { n } + 1$ ，求数列 $\{ a _ { n } \}$ 的通项公式及前10项的和.

9.在流行病学中，基本传染数 $R _ { 0 }$ 是指在没有外力介入，同时所有人都没有免疫力的情况下，一个感染者平均传染的人数.$R _ { 0 }$ 一般由疾病的感染周期、感染者与其他人的接触频率、每次接触过程中传染的概率决定，假设某种传染病的基本传染数 $R _ { \mathrm { 0 } } = 3 . 8$ ，平均感染周期为7天，那么感染人数由1个初始感染者增加到1000人大约需要几轮传染？需要多少天？（初

对于 $R _ { 0 } > 1$ ，而且死亡率较高的传染病，一般要隔离感染者，以控制传染源，切断传播途径.

始感染者传染 $R _ { 0 }$ 个人为第一轮传染，这 $R _ { 0 }$ 个人每人再传染 $R _ { 0 }$ 个人为第二轮传染

# 拓广探索

10.已知数列 $\{ a _ { n } \}$ 为等比数列， $a _ { 1 } = 1 0 2 4$ ，公比 $q { = } \frac { 1 } { 2 }$ 若 $T _ { n }$ 是数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项积，求 $T _ { n }$ 的最大值.

11.已知数列 $\{ a _ { n } \}$ 的首项 $a _ { 1 } = \frac { 3 } { 5 }$ 且满足an+1 $a _ { n + 1 } = { \frac { 3 a _ { n } } { 2 a _ { n } + 1 } }$

（1）求证：数列 $\left\{ \frac { 1 } { a _ { n } } - 1 \right\}$ 为等比数列.（2）若 $\frac { 1 } { a _ { 1 } } + \frac { 1 } { a _ { 2 } } + \frac { 1 } { a _ { 3 } } + \cdots + \frac { 1 } { a _ { n } } < 1 0 0$ ，求满足条件的最大整数 $_ n$

12.已知数列 $\{ a _ { n } \}$ 为等差数列， $a _ { 1 } = 1$ ， $a _ { 3 } = 2 { \sqrt { 2 } } + 1$ ，前 $_ n$ 项和为 $S _ { n }$ ，数列 $\left\{ b _ { n } \right\}$ 满足 $b _ { n } = { \frac { S _ { n } } { n } }$ 求证：

（1）数列 $\left\{ b _ { n } \right\}$ 为等差数列；  
（2）数列 $\{ a _ { n } \}$ 中的任意三项均不能构成等比数列.

# 中国古代数学家求数列和的方法

前面，我们用巧妙的“倒序相加法”和“错位相减法”推导出了等差数列和等比数列的前 $_ n$ 项和公式，人们对数列求和问题的兴趣由来已久，大约在公元前1800年，古埃及的“加罕纸草书”上就记载了等差数列的求和问题，同样地，等比数列的求和也是人们很早就感兴趣的问题，后来，人们开始探索求正整数的平方、立方、4次幂以至 $_ n$ 次幂之和的公式，17世纪代数符号普及之后，数列求和问题经历了由有限到无限、由数项到函数项的发展过程，逐渐形成了现代分析学的一个分支——级数理论.

中国古代许多著名的数学家对推导高阶等差数列的求和公式很感兴趣，创造并发展了名为“垛积术”的算法，展现了聪明才智．下面，我们介绍中国数学家求数列和的方法。

刘徽是我国魏晋时期的数学家，他在为《九章算术》所做的注文中给出了等差数列的求和公式（2）.《九章算术》“盈不足”章的第19问是一个等差数列问题：“今有良马与弩马发长安至齐，齐去长安三千里（里是我国市制长度单位，1里 $= 5 0 0 \ \mathrm { m }$ )．良马初日行一百九十三里，日增十三里，弩马初日行九十七里，日减半里，良马先至齐，复还迎弩马．问：几何日相逢及各行几何？”刘徽用“平行数士中平里”来计算良马和弩马15日所行里数，其中“平行数”“以二马初日所行里乘十五日”得到，良马的“中平里”的计算公式是 $( 1 + 1 4 ) \times \frac { 1 4 } { 2 } \times 1 3$ ，驾马的“中平里”的计算公式是 $( 1 + 1 4 ) \times \frac { 1 4 } { 2 } \times \frac { 1 } { 2 }$ 这样良马15日所行里数的和为 $S _ { \vec { \mathbb { R } } } =$ $1 9 3 \times 1 5 + ( 1 + 1 4 ) \times \frac { 1 4 } { 2 } \times 1 3$ ，这相当于使用了等差数列的求和公式(2)．刘徽是怎样发现这个公式的呢？史学家认为，他可能是在把良马15日内每日所行里数逐项相加的过程中发现的，具体如下：

![](images/8a4a6e346fd21044b9a239e688a238d4d425384dd51a2a6408a53fac0bec36be.jpg)  
刘徽

$1 9 3 + ( 1 9 3 + 1 3 ) + ( 1 9 3 + 1 3 \times 2 ) + ( 1 9 3 + 1 3 \times 3 ) + \cdots + ( 1 9 3 + 1 3 \times 1 4 )$ $= 1 9 3 \times 1 5 + ( 1 + 2 + 3 + \cdots + 1 4 ) \times 1 3$ $= 2 8 9 5 + ( 1 + 1 4 ) \times \frac { 1 4 } { 2 } \times 1 3 .$

北宋的数学家沈括博学多才、善于观察．据说有一天，他走进一家酒馆，看见一层层垒起的酒坛，不禁想到：“怎么求这些酒坛的总数呢？”沈括“用刍童（长方台）法求之，常失于数少”，他想堆积的酒坛、棋子等虽然看起来像实体，但中间是有空隙的，应该把它们看成离散的量，经过反复尝试，沈括提出对于上底有ab个，下底有 $c d$ 个，共 $n$ 层的堆积物（图1)，可以用公式 $S = \frac { n } { 6 } [ ( 2 b + d )$ ·$a + ( b + 2 d ) c ] + \frac { n } { 6 } ( c - a )$ 求出物体的总数，这就是所谓的“隙积术”，相当于求数列 $a b$ ， $\scriptstyle ( + 1 ) ( b + 1 ) , ( a + 2 ) ( b + 2 ) , \ \cdots , ( a + n - 1 ) ( b + n - 1 ) = c d$ 的和，然而，“隙积术”的意义不仅在于提出了二阶等差数列的一个求和公式，而且在于发展了自《九章算术》以来对等差数列问题的研究，开创了我国“垛积术”的研究.

![](images/79faa74802f1f0e4766f1aa39fd412ec29f4e1f65ddc5677e8b644eae5e4f73a.jpg)  
图1

![](images/b30af0e39cb90083d4c830a96113e8981e8aa99534db1edc763a0de21c982140.jpg)  
图2

南宋的数学家杨辉“善于把已知形状、大小的几何图形的求面积、体积的连续量问题转化为求离散量的垛积问题”，例如，求图2“圭垛”中的格点个数总和，杨辉认为虽然圭垛的形状与三角形相似，但要用梯形的面积公式计算，即$S _ { 7 } = \frac { ( 1 + 7 ) \times 7 } { 2 } = 2 8 .$ 在他的专著《详解九章算法·商功》中，杨辉将堆垛与相应立体图形作类比，推导出了三角垛、方垛、刍甍垛、刍童垛等的公式，例如三角垛指的是顶层放1个，第二层放3个，第三层放6个…第 $n$ 层放 $\frac { 1 } { 2 } n ( n + 1 )$ 个物体堆成的堆垛，类比图3中的立体图形，杨辉推出了它的求和公式 $S = 1 + 3 + 6 + \cdots + { \frac { 1 } { 2 } } n ( n + 1 ) =$ ${ \frac { 1 } { 6 } } n ( n + 1 ) ( n + 2 ) .$

![](images/fd8e4508b32a45004979014905cc8ba89d470cfd94e496938cabe8109334923c.jpg)  
图3

# 4.4 数学归纳法

在数列的学习过程中，我们已经用归纳的方法得出了一些结论，例如等差数列 $\{ a _ { n } \}$ 的通项公式 $a _ { n } = a _ { 1 } + ( n - 1 ) d$ 等，但并没有给出严格的数学证明．那么，对于这类与正整数 $n$ 有关的命题，我们怎样证明它对每一个正整数 $n$ 都成立呢？本节我们就来介绍一种重要的证明方法一 数学归纳法.

# 探究

已知数列{an}满足a1=1，an+1 $a _ { n + 1 } { = } \frac { 1 } { 2 { - } a _ { n } } \left( n { \in } \mathbf { N } ^ { * } \right)$ ，计算 $a _ { 2 }$ ， $a _ { 3 }$ ， $a _ { 4 }$ ，猜想其通项公式，并证明你的猜想.

计算可得 $a _ { 2 } = 1$ ， $a _ { 3 } = 1$ ， $a _ { 4 } = 1$ ，再结合 $a _ { 1 } = 1$ ，由此猜想： $a _ { n } = 1$ $( n \in \mathbf { N } ^ { * } )$

如何证明这个猜想呢？我们自然会想到从 $n = 5$ 开始一个个往下验证，一般来说，与正整数 $n$ 有关的命题，当 $n$ 比较小时可以逐个验证，但当 $n$ 较大时，验证起来会很麻烦．特别是证明 $n$ 取所有正整数都成立的命题时，逐一验证是不可能的．因此，我们需要另辟蹊径，寻求一种方法：

通过有限个步骤的推理，证明 $_ n$ 取所有正整数时命题都成立.

我们先从多米诺骨牌游戏说起．码放骨牌时，要保证任意相邻的两块骨牌，若前一块骨牌倒下，则一定导致后一块骨牌倒下．这样，只要推倒第1块骨牌，就可导致第2块骨牌倒下；而第2块骨牌倒下，就可导致第3块骨牌倒下；…，总之，不论有多少块骨牌，都能全部倒下.

![](images/c5c1da6aca4f49ddfaab273e601ab60301d33db08d80db2124778cea8c8e495f.jpg)

# 思考

在这个游戏中，能使所有多米诺骨牌全部倒下的条件是什么？

可以看出，使所有骨牌都能倒下的条件有两个：

（1）第一块骨牌倒下；

（2）任意相邻的两块骨牌，前一块倒下一定导致后一块倒下.

# 思考

你认为条件（2)的作用是什么？如何用数学语言描述它？

可以看出，条件(2)实际上是给出了一个递推关系：第$k$ 块骨牌倒下 $\Rightarrow$ 第 $k + 1$ 块骨牌倒下.

这样，只要第1块骨牌倒下，其他所有的骨牌就能够相继倒下．事实上，无论有多少块骨牌，只要保证（1）（2）成立，那么所有的骨牌一定可以全部倒下.

假设有无限多块多米诺骨牌，我们可以想象前一块推倒后一块的动作将永远进行下去。

# 思考

你认为证明前面的猜想“数列的通项公式是 $a _ { n } = 1$ $\boldsymbol { n } \in \mathbf { N } ^ { \ast }$ ）”与上述多米诺骨牌游戏有相似性吗？你能类比多米诺骨牌游戏解决这个问题吗？

显然，如果能得到一个类似于“第 $k$ 块骨牌倒下 $\Rightarrow$ 第 $k + 1$ 块骨牌倒下”的递推关系，那么猜想的正确性也就得到证明了．为此，我们先回顾一下猜想的获得过程：

由 $a _ { 1 } = 1$ ，利用递推关系，推出 $a _ { 2 } = 1$ 由 $a _ { 2 } = 1$ ，利用递推关系，推出 $a _ { 3 } = 1$ 由 $a _ { 3 } = 1$ ，利用递推关系，推出 $a _ { 4 } = 1$

# 思考

归纳上述过程的共性，你能得出推理的一般结构吗？

我们发现，上述过程蕴含着一个与多米诺骨牌游戏的条件（2）类似的递推结构： h u

以 $a _ { k } = 1$ 成立为条件，推出 $a _ { k + 1 } = 1$ 也成立.

它相当于命题：

当 $n { = } k$ 时猜想成立，则 $n { = } k + 1$ 时猜想也成立.

只要能够证明这个命题，我们就可以在 $a _ { 1 } = 1$ 的条件下，由这个命题得到：对任意正整数 $_ n$ ， $a _ { n } = 1$ 成立.

事实上，如果 $n { = } k$ 时猜想成立，即 $a _ { k } = 1$ ，那么

这里k是任意的，所有能使猜想成立的正整数都可以作为k，并且这样的k也是存在的，因为数“1”就是一个例子。

$$
a _ { k + 1 } = { \frac { 1 } { 2 - a _ { k } } } = { \frac { 1 } { 2 - 1 } } = 1 ,
$$

即当 $n { = } k + 1$ 时，猜想也成立.

这样，对于猜想“ ${ \dot { a } } _ { n } = 1 ^ { \mathfrak { p } }$ ，由 $n { = } 1$ 成立，就有 $n = 2$ 成立；由 $n = 2$ 成立，就有 $n = 3$ 成立；….．所以，对于任意正整数 $n$ ，猜想都成立，即数列 $\{ a _ { n } \}$ 的通项公式是 ${ a _ { n } } = 1$ $( n \in \mathbf { N } ^ { \ast } )$

一般地，证明一个与正整数 $_ n$ 有关的命题，可按下列步骤进行：

（1）（归纳奠基）证明当 $n = n _ { 0 }$ （ $\mathbf { \Phi } _ { n _ { 0 } } \in \mathbf { N } ^ { \times }$ ）时命题成立；

（2）（归纳递推）以“当 $n = k$ $k \in \mathbf { N } ^ { \ast }$ ， $k \geqslant n _ { 0 }$ ）时命题成立”为条件，推出“当$n { = } k + 1$ 时命题也成立”

只要完成这两个步骤，就可以断定命题对从 $n _ { \mathrm { 0 } }$ 开始的所有正整数 $_ n$ 都成立，这种证明方法称为数学归纳法（mathematicalinduction）.

# 思考

数学归纳法中的两个步骤之间有什么关系？

记 $P ( n )$ 是一个关于正整数 $n$ 的命题，我们可以把用数学归纳法证明的形式改写如下：

条件：（1） $P ( n _ { 0 } )$ 为真；（2）若 $P ( k )$ $k \in \mathbf { N } ^ { \ast }$ ， $k \geqslant n _ { 0 }$ ）为真，则 $P ( k + 1 )$ 也为真.   
结论： $P ( n )$ 为真.

在数学归纳法的两步中，第一步验证（或证明）了当 $n = n _ { 0 }$ 时结论成立，即命题$P ( n _ { 0 } )$ 为真；第二步是证明一种递推关系，实际上是要证明一个新命题：

若 $P ( k )$ 为真，则 $P ( k + 1 )$ 也为真.

完成这两步，就有 $P \left( n _ { 0 } \right)$ 真， $P \left( n _ { 0 } + 1 \right)$ 真… $P \left( k \right)$ 真， $P \left( k + 1 \right)$ 真….。从而完成证明.

例1用数学归纳法证明：如果 $\left\{ a _ { n } \right\}$ 是一个公差为 $d$ 的等差数列，那么

$$
a _ { n } = a _ { 1 } + ( n - 1 ) d
$$

对任何 $n \in \mathbf { N } ^ { * }$ 都成立·

分析：因为等差数列的通项公式涉及全体正整数，所以用数学归纳法证明的第一步应证明当 $n { = } 1$ 时命题成立，第二步要明确证明的目标，即要证明一个新命题：如果当 $n { = } k$ 时 $\textcircled{1}$ 式是正确的，那么当 $n { = } k + 1$ 时 $\textcircled{1}$ 式也是正确的。

证明：（1）当 $n { = } 1$ 时，左边 $= a _ { 1 }$ ，右边 $= a _ { 1 } + 0 \times d = a _ { 1 }$ ， $\textcircled{1}$ 式成立.

（2）假设当 $n = k$ $k \in \mathbf { N } ^ { \ast }$ ）时， $\textcircled{1}$ 式成立，即

$$
a _ { k } = a _ { 1 } + ( k - 1 ) d ,
$$

根据等差数列的定义，有

$$
a _ { k + 1 } - a _ { k } = d ,
$$

于是

$$
\begin{array} { r } { a _ { k + 1 } = a _ { k } + d } \\ { = [ a _ { 1 } + ( k - 1 ) d ] + d } \\ { = a _ { 1 } + [ ( k - 1 ) + 1 ] d } \\ { = a _ { 1 } + [ ( k + 1 ) - 1 ] d , } \end{array}
$$

#

在证明递推步骤时，必须使用归纳假设，并把“证明的目标”牢记在心。

即当 $n { = } k + 1$ 时， $\textcircled{1}$ 式也成立.

由（1）（2）可知， $\textcircled{1}$ 式对任何 $n \in \mathbf { N } ^ { \ast }$ 都成立.

# 练习

1.下列各题在应用数学归纳法证明的过程中，有没有错误？如果有错误，错在哪里？

（1）求证：当 $\boldsymbol { n } \in \mathbf { N } ^ { \ast }$ 时， $n { = } n { + } 1$ 证明：假设当 $n { = } k$ $k \in \mathbf { N } ^ { * }$ ）时，等式成立，即 $k = k + 1$   
则当 $n { = } k + 1$ 时，左边 $= k + 1 = ( k + 1 ) + 1 =$ 右边.所以当 $n = k + 1$ 时，等式也成立.由此得出，对任何 $n \in \mathbf { N } ^ { * }$ ，等式 $n { = } n { + } 1$ 都成立.

（2）用数学归纳法证明等差数列的前 $n$ 项和公式是 $S _ { n } { = } \frac { n \left( a _ { 1 } { + } a _ { n } \right) } { 2 }$ 证明： $\textcircled{1}$ 当 $n { = } 1$ 时，左边 ${ } = { S } _ { 1 } { = } a _ { 1 }$ ，右边 $= a _ { 1 }$ ，等式成立.$\textcircled{2}$ 假设当 $n { = } k$ （ $\boldsymbol { k } \in \mathbf { N } ^ { \ast }$ ）时，等式成立，即 $S _ { k } = \frac { k \left( a _ { 1 } + a _ { k } \right) } { 2 }$ 则当 $n { = } k + 1$ 时，

$$
\begin{array} { r } { S _ { k + 1 } = a _ { 1 } + a _ { 2 } + a _ { 3 } + \cdots + a _ { k } + a _ { k + 1 } , } \\ { \quad S _ { k + 1 } = a _ { k + 1 } + a _ { k } + a _ { k - 1 } + \cdots + a _ { 2 } + a _ { 1 } . } \end{array}
$$

上面两式相加并除以2，可得

$$
S _ { k + 1 } = \frac { ( k + 1 ) ( a _ { 1 } + a _ { k + 1 } ) } { 2 } ,
$$

即当 $n { = } k + 1$ 时，等式也成立.

由 $\textcircled{1} \textcircled{2}$ 可知，等差数列的前 $n$ 项和公式是 $S _ { n } { = } \frac { n \left( a _ { 1 } { + } a _ { n } \right) } { 2 }$

2.用数学归纳法证明：首项为 $a _ { 1 }$ ，公比为 $q$ 的等比数列的通项公式是 $a _ { n } = a _ { 1 } q ^ { n - 1 }$ ，前 $_ n$ 项和公式是$S _ { n } { \mathop { = } } { \frac { a _ { 1 } ( 1 { \mathop { - } } q ^ { n } ) } { 1 { \mathop { - } } q } } \ ( q { \mathop { \neq } } 1 ) .$

例2用数学归纳法证明：

$$
1 ^ { 2 } + 2 ^ { 2 } + \cdots + n ^ { 2 } = { \frac { 1 } { 6 } } n ( n + 1 ) ( 2 n + 1 ) ( n \in \mathbf { N } ^ { * } ) .
$$

分析：用数学归纳法证明时，第二步要证明的是一个以“当 $n { = } k$ 时， $\textcircled{1}$ 式成立”为条件，得出“当 $n { = } k + 1$ 时， $\textcircled{1}$ 式也成立”的命题，证明时必须用上上述条件。

证明：（1）当 $n { = } 1$ 时， $\textcircled{1}$ 式的左边 $= 1 ^ { 2 } = 1$ ，

$$
\scriptstyle ! = { \frac { 1 } { 6 } } \times 1 \times ( 1 + 1 ) \times ( 2 \times 1 + 1 ) = { \frac { 1 \times 2 \times 3 } { 6 } } = 1 ,
$$

所以 $\textcircled{1}$ 式成立.

（2）假设当 $n = k$ $k \in \mathbf { N } ^ { \ast }$ ）时， $\textcircled{1}$ 式成立，即

$$
1 ^ { 2 } + 2 ^ { 2 } + \cdots + k ^ { 2 } = { \frac { 1 } { 6 } } k \left( k + 1 \right) \left( 2 k + 1 \right) ,
$$

在上式两边同时加上 $( k + 1 ) ^ { 2 }$ ，有

$$
\begin{array} { r l } & { \quad 1 ^ { 2 } + 2 ^ { 2 } + \cdots + k ^ { 2 } + ( k + 1 ) ^ { 2 } } \\ & { = \frac { 1 } { 6 } k ( k + 1 ) ( 2 k + 1 ) + ( k + 1 ) ^ { 2 } } \\ & { = \frac { k ( k + 1 ) ( 2 k + 1 ) + 6 ( k + 1 ) ^ { 2 } } { 6 } } \\ & { = \frac { ( k + 1 ) ( 2 k ^ { 2 } + 7 k + 6 ) } { 6 } } \\ & { = \frac { ( k + 1 ) ( k + 2 ) ( 2 k + 3 ) } { 6 } } \\ & { = \frac { 1 } { 6 } ( k + 1 ) [ ( k + 1 ) + 1 ] [ 2 ( k + 1 ) + 1 ] , } \end{array}
$$

即当 $n { = } k + 1$ 时， $\textcircled{1}$ 式也成立.

由（1）（2）可知， $\textcircled{1}$ 式对任何 $\boldsymbol { n } \in \mathbf { N } ^ { * }$ 都成立.

例3已知数列 $\{ a _ { n } \}$ 满足 $a _ { 1 } = 0$ ， $2 a _ { n + 1 } - a _ { n } a _ { n + 1 } = 1$ $\mathbf { \Phi } _ { n } \in \mathbf { N } ^ { \times \mathbf { \Phi } } ,$ ，试猜想数列 $\left\{ a _ { n } \right\}$ 的通项公式，并用数学归纳法加以证明.

分析：先将数列 $\{ a _ { n } \}$ 的递推关系 $2 a _ { n + 1 } - a _ { n } a _ { n + 1 } = 1$ 化为 $a _ { n + 1 } { = } \frac { 1 } { 2 { - } a _ { n } } \left( n { \in } \mathbf { N } ^ { * } \right)$ ，通过计算 $a _ { 2 }$ ， $\boldsymbol { a } _ { 3 }$ ， $a _ { 4 }$ ， $a _ { 5 }$ 的值，归纳共性并作出猜想，再应用数学归纳法证明猜想.

解：由 $2 a _ { n + 1 } - a _ { n } a _ { n + 1 } = 1$ ，可得

$$
a _ { n + 1 } { = } \frac { 1 } { 2 { - } a _ { n } } \left( n \in \mathbf { N } ^ { * } \right) .
$$

由

$$
a _ { 1 } = 0 ,
$$

可得

$$
a _ { 2 } = { \frac { 1 } { 2 - 0 } } = { \frac { 1 } { 2 } } .
$$

同理可得

$$
a _ { 3 } = \frac { 1 } { 2 - \frac { 1 } { 2 } } = \frac { 2 } { 3 } , a _ { 4 } = \frac { 1 } { 2 - \frac { 2 } { 3 } } = \frac { 3 } { 4 } , a _ { 5 } = \frac { 1 } { 2 - \frac { 3 } { 4 } } = \frac { 4 } { 5 } .
$$

归纳上述结果，猜想

$$
a _ { n } = { \frac { n - 1 } { n } } ( n \in \mathbf { N } ^ { * } ) .
$$

下面用数学归纳法证明这个猜想.

(1）当 $n { = } 1$ 时， $\textcircled{1}$ 式左边 $= a _ { 1 } = 0$ ，右边 $= \frac { 1 - 1 } { 1 } = 0$ ，猜想成立.

（2）假设当 $n { = } k$ （ $k \in \mathbf { N } ^ { \ast }$ ）时， $\textcircled{1}$ 式成立，即

$$
a _ { k } = \frac { k - 1 } { k } ,
$$

那么

$$
a _ { k + 1 } = \displaystyle { \frac { 1 } { 2 - a _ { k } } } = \displaystyle { \frac { 1 } { 2 - \displaystyle { \frac { k - 1 } { k } } } } = \displaystyle { \frac { k } { k + 1 } } = \displaystyle { \frac { ( k + 1 ) - 1 } { k + 1 } } ,
$$

即当 $n { = } k + 1$ 时，猜想也成立.

由（1）（2）可知，猜想对任何 $\boldsymbol { n } \in \mathbf { N } ^ { * }$ 都成立.

例4设 $_ { \mathcal { X } }$ 为实数，且 $x { > } - 1$ $x \neq 0$ ， $n$ 为大于1的正整数，记数列

$$
x , \ x ( 1 { + } x ) , \ x ( 1 { + } x ) ^ { 2 } , \ \cdots , \ x ( 1 { + } x ) ^ { n - 1 } , \ \cdots
$$

的前 $n$ 项和为 $S _ { n }$ ，试比较 $S _ { n }$ 与 $_ { n x }$ 的大小，并用数学归纳法证明你的结论.

分析：该问题中涉及两个字母， $_ { \mathcal { X } }$ 是大于一1且不等于零的实数， $_ n$ 是大于1的正整数，一种思路是不求和，而直接通过 $n$ 取特殊值比较 $S _ { n }$ 与 $_ { n x }$ 的大小关系，并作出猜想；另一种思路是先由等比数列的求和公式求出 $S _ { n }$ ，再通过 $n$ 取特殊值比较 $S _ { n }$ 与 $_ { n x }$ 的大小关系后作出猜想，两种做法都必须用数学归纳法证明得到的猜想.

解法1：由已知可得

$$
S _ { n } = x + x ( 1 + x ) + x ( 1 + x ) ^ { 2 } + \cdots + x ( 1 + x ) ^ { n - 1 } .
$$

当 $n = 2$ 时， $S _ { 2 } = x + x \left( 1 + x \right) = x ^ { 2 } + 2 x$ ，由 $x \neq 0$ ，知 $x ^ { 2 } > 0$ ，可得

$$
S _ { 2 } > 2 x
$$

当 $n = 3$ 时， $S _ { 3 } = x + x ( 1 + x ) + x ( 1 + x ) ^ { 2 } = x ^ { 2 } \left( x + 3 \right) + 3 x$ ，由 $x { > } { - } 1$ 且 $x \neq 0$ ，知$x ^ { 2 } ( x + 3 ) > 0$ ，可得

$$
S _ { 3 } > 3 x .
$$

由此，我们猜想，当 $x { > } { - } 1$ 且 $x \neq 0$ ， $\boldsymbol { n } \in \mathbf { N } ^ { \ast }$ 且 $n { \stackrel { } { > } } 1$ 时， $S _ { n } > n x$ 下面用数学归纳法证明这个猜想.

（1）当 $n = 2$ 时，由上述过程知，猜想成立.

（2）假设当 $n { = } k$ $k \in \mathbf { N } ^ { \ast }$ ，且 $k \geqslant 2$ ）时，不等式成立，即

$$
S _ { k } > k x ,
$$

则

$$
\begin{array} { r } { S _ { k + 1 } = S _ { k } + x ( 1 + x ) ^ { k } } \\ { > k x + x ( 1 + x ) ^ { k } . } \end{array}
$$

$\textcircled{1}$ 当 $x > 0$ 时，因为 $k > 1$ ，所以 $( 1 + x ) ^ { k } > 1$ ，所以

$$
x ( 1 + x ) ^ { k } > x .
$$

$\textcircled{2}$ 当一 $1 { < x } { < 0 }$ 时， $\scriptstyle 0 < 1 + x < 1$ ，且 $x ^ { 2 } > 0$ 又因为 $k > 1$ ，所以 $( 1 + x ) ^ { k } < 1 + x$ ，可得

$$
x ( 1 + x ) ^ { k } > x ( 1 + x ) = x + x ^ { 2 } > x .
$$

综合 $\textcircled{1} \textcircled{2}$ 可得，当 $x > - 1$ 且 $x \neq 0$ 时，

$$
\begin{array} { c } { { S _ { k + 1 } > k x + x ( 1 + x ) ^ { k } } } \\ { { > k x + x } } \\ { { = ( k + 1 ) x , } } \end{array}
$$

所以，当 $n { = } k + 1$ 时，猜想也成立.

由（1)(2）可知，不等式 $S _ { n } > n x$ 对任何大于1的正整数 $_ n$ 都成立.

解法2：因为 $x > - 1 , \ x \neq 0$ ，所以所给数列是等比数列，公比为 $1 + x$ ，于是

$$
{ \begin{array} { r l } & { S _ { n } = x + x ( 1 + x ) + x ( 1 + x ) ^ { 2 } + \dots + x ( 1 + x ) ^ { n - 1 } } \\ & { \quad = { \frac { x [ 1 - ( 1 + x ) ^ { n } ] } { 1 - ( 1 + x ) } } } \\ & { \quad = ( 1 + x ) ^ { n } - 1 . } \end{array} }
$$

当 $n = 2$ 时， $S _ { 2 } = ( 1 + x ) ^ { 2 } - 1 = x ^ { 2 } + 2 x$ ，由 $x \neq 0$ ，知 $x ^ { 2 } > 0$ ，可得

$$
S _ { 2 } { > } 2 x ;
$$

当 $n = 3$ 时， $S _ { 3 } = ( 1 + x ) ^ { 3 } - 1 = x ^ { 2 } \left( x + 3 \right) + 3 x$ ，由 $x > - 1$ ， $x \neq 0$ ，得 $x ^ { 2 } ( x + 3 ) >$ 0，可得

$$
S _ { 3 } > 3 x .
$$

由此，我们猜想，当 $x { > } - 1$ 且 $x \neq 0$ ， $\boldsymbol { n } \in \mathbf { N } ^ { * }$ 且 $n { > } 1$ 时， $S _ { n } > n x$ 下面用数学归纳法证明. D

(1）当 $n = 2$ 时，由上述过程知，猜想成立.

(2）假设当 $n = k$ $k \in \mathbf { N } ^ { \ast }$ ，且 $k \geq 2$ 时，不等式 $S _ { k } > k x$ 成立，即

$$
( 1 + x ) ^ { k } - 1 { \ > } k x ,
$$

亦即

$$
( 1 + x ) ^ { k } > 1 + k x .
$$

由 $x > - 1$ ，得 $x + 1 > 0$ 又因为 $k > 1$ ， $x \neq 0$ ，所以 $k x ^ { 2 } > 0$ 于是

$$
\begin{array} { r l } & { S _ { k + 1 } = ( 1 + x ) ^ { k + 1 } - 1 } \\ & { \qquad = ( 1 + x ) ^ { k } ( 1 + x ) - 1 } \\ & { \qquad > ( 1 + k x ) ( 1 + x ) - 1 } \\ & { \qquad = k x ^ { 2 } + ( k + 1 ) x } \end{array}
$$

$$
> ( k + 1 ) x .
$$

所以，当 $n { = } k + 1$ 时，猜想也成立.

由（1)(2）可知，不等式 $S _ { n } > n x$ 对任何大于1的正整数 $n$ 都成立.

# 练习

1.用数学归纳法证明： $- 1 + 3 - 5 + \cdots + ( - 1 ) ^ { n } ( 2 n - 1 ) = ( - 1 ) ^ { n } n .$

2.若数列 $\frac { 1 } { 1 \times 2 }$ ， $\frac { 1 } { 2 \times 3 }$ $\frac { 1 } { 3 \times 4 }$ ， …， $\frac { 1 } { n ( n + 1 ) }$ ，…的前 $n$ 项和为 $S _ { n }$ ，计算 $S _ { 1 }$ ， $S _ { 2 }$ ， $S _ { 3 }$ ，由此推测计算$S _ { n }$ 的公式，并用数学归纳法进行证明.

3.观察下列两个数列 $\{ a _ { n } \}$ ， $\{ b _ { n } \}$ ：数列 $\left\{ a _ { n } \right\}$ ：1，4，9,16，25，36，49，64，81，；数列 $\left\{ b _ { n } \right\}$ ：2，4，8，16，32，64，128，256，512，.猜想从第几项起 $a _ { n }$ 小于 $b _ { n }$ ，并证明你的结论.

4.猜想满足 $a _ { 1 } = a$ ， $2 a _ { n + 1 } - a _ { n } a _ { n + 1 } = 1$ 的数列 $\left\{ a _ { n } \right\}$ 的通项公式，并用数学归纳法证明你的结论.

# 习题4.4

# 复习巩固

1.选择题

用数学归纳法证明下列等式：$\begin{array} { r } { - 1 + 3 - 5 + 7 + \cdots + ( - 1 ) ^ { n } ( 2 n - 1 ) + ( - 1 ) ^ { n + 1 } ( 2 n + 1 ) + ( - 1 ) ^ { n + 2 } ( 2 n + 3 ) = ( - 1 ) ^ { n + 2 } ( n + 2 ) . } \end{array}$   
要验证当 $n = 1$ 时等式成立，其左边的式子应为（）.

（A） $- 1$ (B）-1+3 (C）-1+3-5 (D)-1+3-5+7

2.用数学归纳法证明：

(1） $1 + 3 + 5 + \cdots + ( 2 n - 1 ) = n ^ { 2 } ;$ (2） $1 + 2 + 2 ^ { 2 } + \cdots + 2 ^ { n - 1 } = 2 ^ { n } - 1$ （3） $1 ^ { 3 } + 2 ^ { 3 } + 3 ^ { 3 } + \cdots + n ^ { 3 } = { \biggl [ } { \frac { 1 } { 2 } } n ( n + 1 ) { \biggr ] } ^ { 2 } ,$

3.已知数列 $\{ a _ { n } \}$ 满足 $a _ { 1 } = 1$ $, ~ 4 a _ { n + 1 } - a _ { n } a _ { n + 1 } + 2 a _ { n } = 9 ~ ( n \in \bf N ^ { * }$ .计算 $\boldsymbol { a } _ { 2 }$ ， $\boldsymbol { a } _ { 3 }$ ， $\alpha _ { 4 }$ ，由此猜想数列 $\{ a _ { n } \}$ 的通项公式，并用数学归纳法证明.

4.已知数列 $\frac { 1 } { 1 \times 4 }$ ， $\frac { 1 } { 4 \times 7 }$ ， $\frac { 1 } { 7 \times 1 0 }$ ，…， （3n-2）(3n+1），的前n项和为S，计算S，S，$S _ { 3 }$ ， $S _ { 4 }$ ，由此猜想 $S _ { n }$ 的表达式，并用数学归纳法证明.

# 综合运用

5.用数学归纳法证明： ${ \frac { 1 ^ { 2 } } { 1 \times 3 } } + { \frac { 2 ^ { 2 } } { 3 \times 5 } } + \cdots + { \frac { n ^ { 2 } } { ( 2 n - 1 ) ( 2 n + 1 ) } } = { \frac { n ( n + 1 ) } { 2 ( 2 n + 1 ) } } .$

6.已知数列 $\{ a _ { n } \}$ ， $\left\{ b _ { n } \right\}$ 的通项公式分别为 $a _ { n } = 2 ^ { n } , \ b _ { n } = n ^ { 4 }$ ，其中 $\boldsymbol { n } \in \mathbf { N } ^ { \ast }$ ．试推断 $a _ { n } > b _ { n }$ 对哪些正整数 $_ n$ 成立，证明你的结论.

7.已知数列 $\{ x _ { n } \}$ 满足 $x _ { 1 } = 1$ $\scriptstyle 1 , \ x _ { n } = x _ { n + 1 } + \ln ( 1 + x _ { n + 1 } )$ （ $\boldsymbol { n } \in \mathbf { N } ^ { * }$ )．试用数学归纳法证明 $x _ { n } > 0$ 并比较 $x _ { n }$ 与 $x _ { n + 1 }$ 的大小关系.

# 拓广探索

8.证明： $n ^ { 3 } + 5 n$ （ $\boldsymbol { n } \in \mathbf { N } ^ { \ast }$ ）能够被6整除.

9.一本旧教材上有一个关于正整数 $n$ 的恒等式

$$
1 \times 2 ^ { 2 } + 2 \times 3 ^ { 2 } + \cdots + n ( n + 1 ) ^ { 2 } = { \frac { 1 } { 1 2 } } n ( n + 1 ) ?
$$

其中问号处由于年代久远，只能看出它是关于 $_ n$ 的二次三项式，具体的系数已经看不清楚了。  
请你猜想这个恒等式的形式，并用数学归纳法证明.

10.已知命题：设 $\boldsymbol { a } _ { 1 }$ ， $\boldsymbol { a } _ { 2 }$ 为非负实数， $b _ { 1 }$ ， $b _ { 2 }$ 为正实数，若 $b _ { 1 } + b _ { 2 } = 1$ ，则

$$
a _ { 1 } ^ { b _ { 1 } } a _ { 2 } ^ { b _ { 2 } } \leqslant a _ { 1 } b _ { 1 } + a _ { 2 } b _ { 2 } .
$$

请将该命题推广到一般形式，并用数学归纳法证明你所推广的命题.

# 小结

# 一、本章知识结构

![](images/d4a3cf59d1bdc719b4db3c4270295e721213b4fcf734427912e7b955e9b1cfae.jpg)

# 二、回顾与思考

在本章中，我们学习了数列的定义，并以取值规律最简单的两类数列——等差数列和等比数列为例，在研究它们的性质的基础上，推导出了这两类数列的通项公式与前 $n$ 项和公式，还通过建立数列模型，解决了一些数学问题和实际问题。

数列的定义建立起了它的序号与项之间的对应关系，数列是一种特殊的函数，因此我们可以用函数的方法来研究数列，例如，用表格、图象和函数解析式（数列的通项公式）来表示数列，建立数列模型刻画具有递推规律的事物等.而从等差数列、等比数列的通项公式出发，我们发现了等差数列与一次函数、等比数列与指数函数之间的关系，在本章的学习中，我们还常常通过运算发现数列的取值规律，解决与数列有关的问题。

此外，我们还学习了数学归纳法，这种方法建立了一种无穷递推的机制，用有限的步骤证明了与无限多个正整数有关的命题，实现了从有限到无限的飞跃，它既是我们证明与正整数 $n$ 有关的命题的一种思想方法，又为我们提供了一种“观察一归纳一猜想一证明”的思维模式，需要注意的是，数学归纳法中的两个步骤是缺一不可的。

请你带着下面的问题，复习一下全章内容吧！

1.为什么说数列是一种特殊的函数？2.在什么情况下可以用通项公式表示数列，在什么情况下可以用递推公式表示数列？3.数列的前 $_ n$ 项和公式与它的通项公式有什么关系？4.等差数列和等比数列的通项公式分别是什么？你是如何推导出它们的？等差数列和等比数列的图象分别有什么特点？5.“等差中项”“等比中项”与“平均数”之间有什么内在联系？等差数列、等比数列有许多有趣的性质，你能列举一些吗？6.推导等差数列、等比数列的前 $n$ 项和公式时，各用了哪些巧妙的方法？\*7.为什么说数学归纳法的两个步骤（归纳奠基与归纳递推）缺一不可？你能说说两个步骤各自的作用吗？它们之间有怎样的关系？

# 复习参考题4

# 复习巩固

1.根据下列数列的通项公式，分别作出它们的图象：

（1）an= $a _ { n } = - { \frac { n } { 4 } } ; \qquad ( 2 ) b _ { n } = { \frac { 2 ^ { n } } { 3 } } ; \qquad ( 3 ) c _ { n } = { \frac { 2 n + 1 } { n } } ;$ （4）d(-1）

2.根据下列数列的前4项，写出数列的一个通项公式：

(1) ${ \frac { 1 } { 2 } } , { \frac { 3 } { 4 } } , { \frac { 5 } { 8 } } , { \frac { 7 } { 1 6 } } ;$ (2） $1 + \frac { 1 } { 2 ^ { 2 } } , \ 1 - \frac { 3 } { 4 ^ { 2 } } , \ 1 + \frac { 5 } { 6 ^ { 2 } } , \ 1 - \frac { 7 } { 8 ^ { 2 } } ;$ (3）0, $\sqrt { 2 }$ ，0, $\sqrt { 2 }$

3.选择题

（1）预测人口的变化趋势有多种方法，“直接推算法”使用的公式是 $P _ { n } = P _ { \mathrm { ~ o ~ } } ( 1 { + } k ) ^ { n }$ $( k > - 1 )$ ，其中 $P _ { n }$ 为预测期人口数， $P _ { 0 }$ 为初期人口数， $k$ 为预测期内人口年增长率， $n$ 为预测期间隔年数，如果在某一时期 $k \in ( - 1 , \ 0 )$ ，那么在这期间人口数（）.

（A）呈上升趋势 （B）呈下降趋势 （C）摆动变化 (D）不变（2）《莱因德纸草书》（RhindPapyrus）是世界上最古老的数学著作之一．书中有一道这样的题目，请给出答案：把100个面包分给5个人，使每人所得面包个数成等差数列，且使较大的三份之和的 $\frac { 1 } { 7 }$ 是较小的两份之和，则最小的一份为（）.

A $\frac { 5 } { 3 }$ (B) $\frac { 1 0 } { 3 }$ $\frac { 5 } { 6 }$ (D) $\frac { 1 1 } { 6 }$ （3）如图是瑞典数学家科赫在1904年构造的能够描述雪花形状的图案．图形的作法是：从一个正三角形开始，把每条边分成三等份，然后以各边的中间一段为底边分别向外作正三角形，再去掉底边，反复进行这一过程，就得到一条“雪花”状的曲线，设原正三角形（图 $\textcircled{1}$ 的边长为1，把图 $\textcircled{1}$ 、图 $\textcircled{2}$ 、图 $\textcircled{3}$ 、图 $\textcircled{4}$ 中图形的周长依次记为 $C _ { 1 }$ ， $C _ { 2 }$ ， $C _ { 3 }$ ， $C _ { 4 }$ ，则 $C _ { 4 } { = } ($ ）.

A $\frac { 1 2 8 } { 9 }$ (B) $\frac { 6 4 } { 9 }$ C 128

![](images/d7c0857033090fcf648235369a4b7ad042676667958fb69d62e4c4ae48645428.jpg)  
（第3（3）题）

4.填空题

（1）已知 $a = 5 + 2 { \sqrt { 6 } }$ ， $c = 5 - 2 { \sqrt { 6 } }$ 若 $^ a$ ， $^ { b }$ ， $c$ 三个数成等差数列，则 $b = \_$ ；若 $^ a$ ， $^ { b }$ ， $c$ 三个数成等比数列，则 $b = \_$

（2）我国古代数学名著《算法统宗》中有如下问题：“远望巍巍塔七层，红光点点倍加增，共灯三百八十一，请问尖头几盏灯.”意思是：一座7层塔共挂了381盏灯，且相邻两层中的下一层灯数是上一层灯数的2倍，则塔的顶层共有 盏灯.

5.某教育网站本月的用户为500人，网站改造后，预计平均每月的用户都比上一个月增加 $10 \%$ 那么从本月起，大约经过几个月可使用户达到1万人（精确到1）？

6.某中学的“希望工程”募捐小组暑假期间走上街头进行了一次募捐活动，共收到捐款1200元.他们第1天只得到10元，之后采取了积极措施，从第2天起，每一天收到的捐款都比前一天多10元．这次募捐活动一共进行了多少天？

7.某同学利用暑假时间到一家商场勤工俭学．该商场向他提供了三种付酬方案：第一种，每天支付38元；第二种，第1天付4元，从第2天起，每一天比前一天都多付4元；第三种，第1天付0.4元，以后每一天比前一天翻一番（即增加1倍)．他选择哪种方式领取报酬更划算？

# 综合运用

8.非零实数 $a$ ， $^ { b }$ ， $c$ 不全相等.

（1）若 $a$ ， $^ { b }$ ， $c$ 成等差数列， ${ \frac { 1 } { a } } , \ { \frac { 1 } { b } }$ 6 $\frac { 1 } { c }$ 能构成等差数列吗？你能用函数图象解释一下吗？

（2）若a，b，c成等比数列，1,1， $\frac { 1 } { c }$ 能构成等比数列吗？为什么？

9.小明的父母为了准备小明将来考人大学的学费，于2017年元旦在某银行存人10000元，并在后续每一年的元旦都在该银行存人1200元，直到2022年存人最后一笔钱为止，如果银行的存款年利率为 $2 . 7 5 \%$ ，且以复利计息，那么小明的父母在2023年元旦将存款连本带利全部取出时，能取到多少钱？  
10.任取一个正整数，若是奇数，就将该数乘3再加上1；若是偶数，就将该数除以2.反复进行上述两种运算，经过有限次步骤后，必进入循环圈 $1 {  } 4 {  } 2 {  } 1$ ，这就是数学史上著名的“冰雹猜想”（又称“角谷猜想”等)．如取正整数 $m = 6$ ，根据上述运算法则得出 $6 \to 3 \to 1 0 \to$ $5 \to 1 6 \to 8 \to 4 \to 2 \to 1$ ，共需经过8个步骤变成1（简称为8步“雹程”）.现给出冰雹猜想的递推关系如下：已知数列 $\{ a _ { n } \}$ 满足： $a _ { 1 } = m$ $\mathbf { \nabla } _ { m }$ 为正整数)， $a _ { n + 1 } = \left\{ { \frac { a _ { n } } { 2 } } \right.$ 当 $\boldsymbol { a } _ { n }$ 为偶数时，$\lfloor 3 a _ { n } + 1$ ，当 $\boldsymbol { a } _ { n }$ 为奇数时.（1）当 $m { = } 1 7$ 时，试确定使得 $a _ { n } = 1$ 需要多少步雹程；(2）若 $a _ { 8 } = 1$ ，求 $_ m$ 所有可能的取值集合 $M$

11.已知等差数列 $\{ a _ { n } \}$ 的前 $n$ 项和为 $S _ { n }$ ，且 $S _ { 4 } { = } 4 S _ { 2 } , \ a _ { 2 n } { = } 2 a _ { n } { + } 1 \ ( n { \in } \mathbf { N } ^ { \ast } ) .$

（1）求数列 $\left\{ a _ { n } \right\}$ 的通项公式；(2）若 $b _ { n } = 3 ^ { n - 1 }$ ，令 $\scriptstyle { C _ { n } = a _ { n } b _ { n } }$ ，求数列 $\left\{ c _ { n } \right\}$ 的前 $n$ 项和 $T _ { n }$

12.已知等比数列 $\{ a _ { n } \}$ 的前 $n$ 项和为 $S _ { n }$ ，且 $a _ { n + 1 } = 2 S _ { n } + 2 ( n \in \mathbf { N } ^ { * } ) .$

（1）求数列 $\{ a _ { n } \}$ 的通项公式  
(2）在 $\boldsymbol { a } _ { n }$ 与 $a _ { n + 1 }$ 之间插入 $n$ 个数，使这 $n + 2$ 个数组成一个公差为 $d _ { n }$ 的等差数列，在数列$\{ d _ { n } \}$ 中是否存在3项 $d _ { m }$ ， $d _ { k }$ ， $d _ { p }$ （其中 $_ m$ ， $k$ ， $\phi$ 成等差数列）成等比数列？若存在，求出这样的3项；若不存在，请说明理由.

13.类比等差数列和等比数列的定义、通项公式、常用性质等，发现它们具有如下的对偶关系：只要将等差数列的一个关系式中的运算“ $+ ^ { \dag }$ 改为“ $x '$ ，“一”改为“ $\div \ u ^ { * }$ ，正整数倍改为正整数指数幂，相应地就可得到等比数列中一个形式相同的关系式，反之也成立.

（1）根据上述说法，请你参照下表给出的信息推断出相关的对偶关系式；

<html><body><table><tr><td>名称</td><td>等差数列{an}</td><td>等比数列{6n}</td></tr><tr><td>定义</td><td>an+1-an=d</td><td></td></tr><tr><td>通项公式</td><td></td><td>b=b1q-1=bmq&quot;-m</td></tr><tr><td>常用性质</td><td>①a1+an=a+an-1=a3+an-2=… ②an-k+an+k=2an(n&gt;k） ③ ④a1+a+.+an 2（a1+an）</td><td>② ③若m+n=k+l（m，n，k，l∈N）， 则bnbm=bb</td></tr></table></body></html>

（2）在等差数列 $\{ a _ { n } \}$ 中，若 $a _ { 2 0 1 8 } = 0$ ，则有

$$
a _ { 1 } + a _ { 2 } + \cdots + a _ { n } = a _ { 1 } + a _ { 2 } + \cdots + a _ { 4 0 3 5 - n } ( n \in \bf N ^ { \ast } \ , \ n < 4 0 3 5 - \cdots + \cdots + a _ { 4 0 3 5 - n } = 0 )
$$

相应地，在等比数列 $\{ b _ { n } \}$ 中，若 $b _ { 2 0 1 9 } = 1$ ，请你类比推测出对偶的等式，并加以证明.

# 拓广探索

14.在2015年苏州世乒赛期间，某景点用乒乓球堆成若干堆“正三棱锥”形的装饰品，其中第1堆只有1层，就一个球；第2，3，4，堆最底层（第一层）分别按图中所示方式固定摆放，从第二层开始，每层的小球自然垒放在下一层之上，第 $n$ 堆第 $n$ 层就放一个乒乓球．记第 $n$ 堆的乒乓球总数为 $f ( n )$

![](images/a69353759d2d93be24b967df35a9ec033392a459fcb4fb290c471bca96bc285f.jpg)  
（第14题）

（1）求出 $f ( 3 )$   
（2）试归纳出 $f ( n { + } 1 )$ 与 $f ( n )$ 的关系式，并根据你得到的关系式探求 $f ( n$ ）的表达式.参考公式： $1 ^ { 2 } + 2 ^ { 2 } + \cdots + n ^ { 2 } = { \frac { 1 } { 6 } } n ( n + 1 ) ( 2 n + 1 ) .$

15.有理数都能表示成 $\frac { m } { n }$ （m， ${ \boldsymbol { n } } \in \mathbf { Z }$ ，且 $n \neq 0$ ， $m$ 与 $n$ 互质）的形式，进而有理数集 $Q =$ $\left\{ { \frac { m } { n } } \mid m , n \in \mathbf { Z } \right\}$ ，且 $n \neq 0$ ， $m$ 与 $_ n$ 互质 $\Bigg \}$ ，任何有理数 $\frac { m } { n }$ 都可以化为有限小数或无限循环小数.反之，任一有限小数也可以化为 $\frac { m } { n }$ 的形式，从而是有理数；那么无限循环小数是不是有理数？思考下列问题：

(1）1.2是有理数吗？请说明理由.  
(2）1.24是有理数吗？请说明理由.

16.平面上有 $_ n$ $\mathbf { \bar { \mu } } _ { n \in \mathbf { N } }$ $n { \geqslant } 3 ,$ ）个点，其中任何三点都不在同一条直线上.过这些点中任意两点作直线，这样的直线共有多少条？证明你的结论.

$^ { * } 1 7 .$ 数学归纳法还有其他变化形式，例如，将数学归纳法中的第（1）步保持不变，第（2）步改为“以‘当 $n _ { 0 } \leqslant n \leqslant k$ $k \in \mathbf { N } ^ { * }$ ， $k \geqslant n _ { 0 }$ ）时命题成立’为条件，推出‘当 $n = k + 1$ 时命题也成立，”也可以断定命题对从 $n _ { 0 }$ 开始的所有正整数 $_ n$ 都成立，这种证明方法称为第二数学归纳法：试用第二数学归纳法证明如下命题：

若数列 $\{ F _ { n } \}$ 满足 $. F _ { 1 } = 1 , \ F _ { 2 } = 1 , \ F _ { n } = F _ { n - 1 } + F _ { n - 2 } \ \ : ( n { \geqslant } 3 , \ n \in 1$ $\boldsymbol { n } \in \mathbf { N } ^ { * }$ ）（ $\{ F _ { n } \}$ 称为斐波那契数列），则其通项公式为 $F _ { n } = { \frac { 1 } { \sqrt { 5 } } } \left[ \left( { \frac { 1 + { \sqrt { 5 } } } { 2 } } \right) ^ { n } - \left( { \frac { 1 - { \sqrt { 5 } } } { 2 } } \right) ^ { n } \right]$

# 第五章一元函数的导数及其应用

为了描述现实世界中的运动、变化现象，在数学中引入了函数．刻画静态现象的数与刻画动态现象的函数都是数学中非常重要的概念，在对函数的深人研究中，数学家创立了微积分，这是具有划时代意义的伟大创造，被誉为数学史上的里程碑.

微积分的创立与处理四类科学问题直接相关．一是已知物体运动的路程作为时间的函数，求物体在任意时刻的速度与加速度，反之，已知物体的加速度作为时间的函数，求速度与路程；二是求曲线的切线；三是求函数的最大值与最小值；四是求长度、面积、体积和重心等．历史上科学家们对这些问题的兴趣和研究经久不衰，终于在17世纪中叶，牛顿和莱布尼茨在前人探索与研究的基础上，凭着他们敏锐的直觉和丰富的想象力，各自独立地创立了微积分.

导数是微积分的核心内容之一，是现代数学的基本概念，蕴含着微积分的基本思想；导数定量地刻画了函数的局部变化，是研究函数增减、变化快慢、最大（小）值等性质的基本工具，因而在解决诸如增长率、膨胀率、效率、密度、速度、加速度等实际问题中有着广泛应用。

在本章，我们将通过丰富的实际背景和具体实例，学习导数的概念和导数的基本运算，体会导数的内涵与思想，感悟极限的思想，通过具体实例感受导数在研究函数和解决实际问题中的作用，体会导数的意义.

# 5.1 导数的概念及其意义

在必修第一册中，我们研究了函数的单调性，并利用函数单调性等知识定性地研究了一次函数、指数函数、对数函数增长速度的差异，知道“对数增长”是越来越慢的，“指数爆炸”比“直线上升”快得多．进一步地，能否精确定量地刻画变化速度的快慢呢？下面我们就来研究这个问题.

# 5.1.1 变化率问题

问题 $^ 1$ 跳水运动员的速度

# 探究

在一次跳水运动中，某运动员在运动过程中的重心相对于水面的高度 $h$ （单位：m）与起跳后的时间 $t$ （单位：s）存在函数关系

$$
h \left( t \right) = - 4 . 9 t ^ { 2 } + 4 . 8 t + 1 1 .
$$

如何描述运动员从起跳到入水的过程中运动的快慢程度呢？

直觉告诉我们，运动员从起跳到入水的过程中，在上升阶段运动得越来越慢，在下降阶段运动得越来越快．我们可以把整个运动时间段分成许多小段，用运动员在每段时间内的平均速度 $\bar { \boldsymbol { v } }$ 近似地描述他的运动状态.

例如，在 $0 { \leqslant } t { \leqslant } 0 . 5$ 这段时间里，

$$
\displaystyle \overline { { v } } = \frac { h \left( 0 . 5 \right) - h \left( 0 \right) } { 0 . 5 - 0 } { = 2 . 3 5 \left( \mathrm { m / s } \right) } ;
$$

在 $1 { \leqslant } t { \leqslant } 2$ 这段时间里，

$$
\stackrel { - } { v } = \frac { h \left( 2 \right) - h \left( 1 \right) } { 2 - 1 } = - 9 . 9 ( \mathrm { m / s } ) .
$$

一般地，在 $t _ { 1 } { \leqslant } t { \leqslant } t _ { 2 }$ 这段时间里，

$$
\stackrel { - } { v } = \frac { h \left( t _ { 2 } \right) - h \left( t _ { 1 } \right) } { t _ { 2 } - t _ { 1 } } = - 4 . 9 ( t _ { 1 } + t _ { 2 } ) + 4 . 8 .
$$

# 思考

计算运动员在 $0 \leq t \leq \frac { 4 8 } { 4 9 }$ 这段时间里的平均速度，你发现了什么？你认为用平均速度描述运动员的运动状态有什么问题吗？

我们发现，运动员在 $0 { \leq } t { \leq } \frac { 4 8 } { 4 9 }$ 这段时间里的平均速度为0．显然，在这段时间内，运动员并不处于静止状态.因此，用平均速度不能准确反映运动员在这一时间段里的运动状态.

为了精确刻画运动员的运动状态，需要引入瞬时速度的概念，我们把物体在某一时刻的速度称为瞬时速度（instantaneousvelocity).

# 探究

瞬时速度与平均速度有什么关系？你能利用这种关系求运动员在 $t = 1$ s时的瞬时速度吗？

设运动员在 $t _ { 0 }$ 时刻附近某一时间段内的平均速度是 $\bar { \boldsymbol { v } }$ ，可以想象，如果不断缩短这一时间段的长度，那么 $\bar { \boldsymbol { v } }$ 将越来越趋近于运动员在 $t _ { 0 }$ 时刻的瞬时速度.

为了求运动员在 $t = 1$ 时的瞬时速度，我们在 $t = 1$ 之后或之前，任意取一个时刻 $1 + \Delta t$ ， $\Delta t$ 是时间改变量，可以是正值，也可以是负值，但不为0.当 $\Delta t > 0$ 时， $1 + \Delta t$ 在1之后；当 $\Delta t < 0$ 时， $1 + \Delta t$ 在1之前.当 $\Delta t > 0$ 时，把运动员在时间段[1， $1 + \Delta t ]$ 内近似看成做匀速直线运动，计算时间段[1， $1 + \Delta t$ 内的平均速度 $\bar { \boldsymbol { v } }$ ，用平均速度 $\bar { \cdot }$ 近似表示运动员在 $t = 1$ 时的瞬时速度，当 $\Delta t < 0$ 时，在时间段$[ 1 + \Delta t$ ，1内可作类似处理．为了提高近似表示的精确度，我们不断缩短时间间隔，得到如下表格（表5.1-1).

用运动变化的观点研究问题是微积分的重要思想.

表5.1-1  

<html><body><table><tr><td colspan="2">当△t&lt;0时，在时间段[1+△t，1]内</td><td colspan="3">当△t&gt;0时，在时间段[1，1+△t]内</td></tr><tr><td>△t</td><td>h(1)-h(1+△t) 1-(1+△t） 4.9（△t）²+5△t -△t =-4.9△t-5</td><td>△t</td><td>h(1+△t)-h(1） (1+△t)-1 -4.9（△t）²-5△t △t =-4.9△t-5</td></tr></table></body></html>

续表  

<html><body><table><tr><td colspan="2">当△t&lt;0时，在时间段[1+△t，1]内</td><td colspan="2">当△t&gt;0时，在时间段[1，1+△t]内</td></tr><tr><td>-0.01</td><td>-4.951</td><td>0.01</td><td>-5.049</td></tr><tr><td>-0.001</td><td>-4.9951</td><td>0.001</td><td>-5.0049</td></tr><tr><td>-0.0001</td><td>-4.999 51</td><td>0.0001</td><td>-5.00049</td></tr><tr><td>-0.00001</td><td>-4.999951</td><td>0.00001</td><td>-5.000049</td></tr><tr><td>-0.000001</td><td>-4.9999951</td><td>0.000001</td><td>-5.0000049</td></tr><tr><td colspan="2">.</td><td colspan="2"></td></tr></table></body></html>

# $\textcircled{6}$

# 观察

给出 $\Delta t$ 更多的值，利用计算工具计算对应的平均速度 $\bar { \boldsymbol { v } }$ 的值，当 $\Delta t$ 无限趋近于0时，平均速度 $\bar { \boldsymbol { v } }$ 有什么变化趋势？

我们发现，当 $\Delta t$ 无限趋近于0，即无论 $t$ 从小于1的一边，还是从大于 $1$ 的一边无限趋近于1时，平均速度 $\bar { \boldsymbol { v } }$ 都无限趋近于一5.

事实上，由 $\stackrel { - } { v } = \frac { h \left( 1 + \Delta t \right) - h \left( 1 \right) } { \left( 1 + \Delta t \right) - 1 } = - 4 . 9 \Delta t - 5$ 可以发现，当 $\Delta t$ 无限趋近于0时，$- 4 . 9 \Delta t$ 也无限趋近于0，所以 $\bar { \boldsymbol { v } }$ 无限趋近于一5.这与前面得到的结论一致.数学中，我们把-5叫做“当 $\Delta t$ 无限趋近于0时， $\overline { { v } } = \frac { h \left( 1 + \Delta t \right) - h \left( 1 \right) } { \Delta t }$ 的极限”，记为

$$
\operatorname* { l i m } _ { \Delta t  0 } \frac { h ( 1 + \Delta t ) - h ( 1 ) } { \Delta t } = - 5 .
$$

从物理的角度看，当时间间隔 $\mid \Delta t \mid$ 无限趋近于0时，平均速度 $\bar { \boldsymbol { v } }$ 就无限趋近于 $t = 1$ 时的瞬时速度，因此，运动员在 $t = 1$ s时的瞬时速度 $v ( 1 ) = - 5 ~ \mathrm { m / s } .$

# 思考

（1）求运动员在 $t = 2 \mathrm { ~ s ~ }$ 时的瞬时速度；（2）如何求运动员从起跳到入水过程中在某一时刻 $t _ { 0 }$ 的瞬时速度？

# 练习

1.求问题1中跳水运动员在 $t = 0 , 5$ s时的瞬时速度.

2.火箭发射 $t$ s后，其高度（单位：m）为 $h \left( t \right) = 0 . 9 t ^ { 2 }$ 求：

（1）在 $1 { \leqslant } t { \leqslant } 2$ 这段时间里，火箭爬高的平均速度；  
（2）发射后第10s时，火箭爬高的瞬时速度.

3.一个小球从 $5 ~ \mathrm { m }$ 的高处自由下落，其位移 $y$ （单位：m）与时间 $t$ （单位：s）之间的关系为 $y \left( t \right) =$ $- 4 . 9 t ^ { 2 }$ 求 $t = 1$ s时小球的瞬时速度.

问题2 抛物线的切线的斜率

我们知道，如果一条直线与一个圆只有一个公共点，那么这条直线与这个圆相切．对于一般的曲线 $C$ ，如何定义它的切线呢？下面我们以抛物线 $f ( x ) = x ^ { 2 }$ 为例进行研究.

# 探究

你认为应该如何定义抛物线 $f ( x ) = x ^ { 2 }$ 在点 $P _ { 0 } ( 1 , \ 1 )$ 处的切线？

与研究瞬时速度类似，为了研究抛物线 $f ( x ) = x ^ { 2 }$ 在点 $P _ { 0 } ( 1 , \ 1 )$ 处的切线，我们通常在点 $P _ { 0 }$ （1，1）的附近任取一点 $P ( x , \ x ^ { 2 } )$ ，考察抛物线 $f ( x ) = x ^ { 2 }$ 的割线 $P _ { 0 } P$ 的变化情况.

# 观察

如图5.1-1，当点 $P ( x , x ^ { 2 }$ ）沿着抛物线 $f ( x ) = x ^ { 2 }$ 趋近于点 $P _ { 0 } ( 1 , \ 1 )$ 时，割线 $P _ { 0 } P$ 有什么变化趋势？

![](images/939411672c197c5d813bce527499dbcebbdaea04c5cf92fc165641c0722f66fd.jpg)  
图5.1-1

利用信息技术工具，演示图5.1-1中 $P _ { \mathrm { 0 } } P$ 的动态变化趋势.

我们发现，当点 $P$ 无限趋近于点 $P _ { 0 }$ 时，割线 $P _ { \mathrm { 0 } } P$ 无限趋近于一个确定的位置，这个确定位置的直线 $P _ { \mathrm { ~ 0 ~ } } T$ 称为抛物线 $f ( x ) = x ^ { 2 }$ 在点 $P _ { 0 } ( 1 , \ 1 )$ 处的切线.

# 探究

我们知道，斜率是确定直线的一个要素，如何求抛物线 $f ( x ) = x ^ { 2 }$ 在点 $P _ { 0 } ( 1 , \ 1 )$ 处的切线 $P _ { 0 } T$ 的斜率 $k _ { 0 }$ 呢？

从上述切线的定义可见，抛物线 $f ( x ) = x ^ { 2 }$ 在点 $P _ { 0 } ( 1 , \ 1 )$ 处的切线 $P _ { 0 } T$ 的斜率与割线 $P _ { \mathrm { 0 } } P$ 的斜率有内在联系．记 $\Delta x =$ $x - 1 , \mathbf { \delta \bullet }$ 则点 $P$ 的坐标是（ $1 + \Delta x$ ， $( 1 + \Delta x ) ^ { 2 } )$ 于是，割线 $P _ { \mathrm { 0 } } P$ 的斜率

$\bullet$ $\Delta x$ 可以是正值，也可以是负值，但不为0.

$$
k = \frac { f ( x ) - f ( 1 ) } { x - 1 } = \frac { ( 1 + \Delta x ) ^ { 2 } - 1 } { ( 1 + \Delta x ) - 1 } = \Delta x + 2 .
$$

我们可以用割线 $P _ { \mathrm { 0 } } P$ 的斜率 $k$ 近似地表示切线 $P _ { 0 } T$ 的斜率 $k _ { 0 }$ ，并且可以通过不断缩短横坐标间隔 $\mid \Delta x \mid$ 来提高近似表示的精确度，得到如下表格（表5.1-2）.

表5.1-2  

<html><body><table><tr><td colspan="2">△x&lt;0</td><td colspan="2">△x&gt;0</td></tr><tr><td>△x</td><td>k=△x+2</td><td>△x</td><td>k=△x+2</td></tr><tr><td>-0.01</td><td>1.99</td><td>0.01</td><td>2.01</td></tr><tr><td>-0.001</td><td>1.999</td><td>0.001</td><td>2.001</td></tr><tr><td>-0.0001</td><td>1.9999</td><td>0.0001</td><td>2.0001</td></tr><tr><td>-0.00001</td><td>1.99999</td><td>0.00001</td><td>2.000 01</td></tr><tr><td>-0.000 001</td><td>1.999 999</td><td>0.000001</td><td>2.000 001</td></tr><tr><td colspan="2"></td><td colspan="2"></td></tr></table></body></html>

# $\textcircled{6}$

# 观察

利用计算工具计算更多割线 $P _ { \mathrm { 0 } } P$ 的斜率 $k$ 的值，当 $\Delta x$ 无限趋近于0时，割线$P _ { \mathrm { 0 } } P$ 的斜率 $k$ 有什么变化趋势？

我们发现，当 $\Delta x$ 无限趋近于0时，即无论 $_ { \mathcal { X } }$ 从小于1的一边，还是从大于1的一边无限趋近于1时，割线 $P _ { \mathrm { 0 } } P$ 的斜率 $k$ 都无限趋近于2.

事实上，由k=f(1+△x)-f(1) $k = \frac { f ( 1 + \Delta x ) - f ( 1 ) } { \Delta x } = \Delta x + 2$ 可以直接看出，当 $\Delta x$ 无限趋近于0时， $\Delta x { + 2 }$ 无限趋近于2.我们把2叫做“当△x无限趋近于0时，k=f(1+△x）-f(1) 的极限”，记为

$$
\operatorname* { l i m } _ { \Delta x \to 0 } { \frac { f ( 1 + \Delta x ) - f ( 1 ) } { \Delta x } } = 2 .
$$

从几何图形上看，当横坐标间隔 $| \Delta x |$ 无限变小时，点 $P$ 无限趋近于点 $P _ { 0 }$ ，于是割线$P _ { \mathrm { 0 } } P$ 无限趋近于点 $P _ { 0 }$ 处的切线 $P _ { \mathrm { ~ 0 ~ } } T$ 这时，割线 $P _ { \mathrm { 0 } } P$ 的斜率 $k$ 无限趋近于点 $P _ { 0 }$ 处的切线 $P _ { 0 } T$ 的斜率 $k _ { 0 }$ 因此，切线 $P _ { \mathrm { ~ 0 ~ } } T$ 的斜率 $k _ { 0 } = 2$

# 思考

观察问题1中的函数 $h \left( t \right) = - 4 . 9 t ^ { 2 } + 4 . 8 t + 1 1$ 的图象（图5.1-2)，平均速度

$$
\overline { { v } } = \frac { h \left( 1 + \Delta t \right) - h \left( 1 \right) } { \left( 1 + \Delta t \right) - 1 }
$$

的几何意义是什么？瞬时速度 $\upsilon ( 1 )$ 呢？

![](images/8da6d570dd17dc5977f01b3cd26daafeec326ea06c045fe6e4cd7f182212b5db.jpg)  
图5.1-2

# 练习

1.你认为应该怎样定义抛物线 $f ( x ) = x ^ { 2 }$ 在点 $( \boldsymbol { x } _ { 0 }$ ， ${ x _ { 0 } } ^ { 2 }$ ）处的切线？试求抛物线 $f ( x ) = x ^ { 2 }$ 在点（-1，1)处切线的斜率.

2.求抛物线 $f ( x ) = x ^ { 2 } + 1$ 在点（0，1）处的切线方程.

# 5.1.2导数的概念及其几何意义

前面我们研究了两类变化率问题：一类是物理学中的问题，涉及平均速度和瞬时速度；另一类是几何学中的问题，涉及割线斜率和切线斜率，这两类问题来自不同的学科领域，但在解决问题时，都采用了由“平均变化率”逼近“瞬时变化率”的思想方法；问题的答案也有一样的表示形式，下面我们用上述思想方法研究更一般的问题.

对于函数 $y { = } f ( x )$ ，设自变量 $_ { \mathcal { X } }$ 从 $x _ { 0 }$ 变化到 $x _ { 0 } + \Delta x$ ，相应地，函数值 $_ y$ 就从 $f ( x _ { 0 } )$ 变化到 $f ( x _ { 0 } + \Delta x )$ .这时， $_ { \mathcal { X } }$ 的变化量为 $\Delta x$ ， $y$ 的变化量为

$$
\Delta y = f ( x _ { 0 } + \Delta x ) - f ( x _ { 0 } ) .
$$

我们把比值 ，即

$$
\frac { \Delta y } { \Delta x } = \frac { f ( x _ { 0 } + \Delta x ) - f ( x _ { 0 } ) } { \Delta x }
$$

叫做函数 $y = f ( x )$ 从 $x _ { 0 }$ 到 $x _ { 0 } + \Delta x$ 的平均变化率.

如果当 $\Delta x {  } 0$ 时，平均变化率 $\frac { \Delta y } { \Delta x }$ 无限趋近于一个确定的值，即 $\frac { \Delta y } { \Delta x }$ 有极限，则称 $y =$ $f ( x )$ 在 ${ \boldsymbol { x } } = { \boldsymbol { x } } _ { 0 }$ 处可导，并把这个确定的值叫做 $y { = } f ( x )$ 在 ${ \boldsymbol { x } } = { \boldsymbol { x } } _ { 0 }$ 处的导数（derivative）(也称为瞬时变化率)，记作 $f ^ { \prime } ( x _ { 0 } )$ 或 $y ^ { \prime } \vert _ { x = x _ { 0 } }$ ，即

$$
f ^ { \prime } ( x _ { 0 } ) = \operatorname* { l i m } _ { \Delta x \to 0 } { \frac { \Delta y } { \Delta x } } { = } \operatorname* { l i m } _ { \Delta x \to 0 } { \frac { f ( x _ { 0 } + \Delta x ) - f ( x _ { 0 } ) } { \Delta x } } .
$$

由导数的定义可知，问题1中运动员在 $t = 1$ 时的瞬时速度 $\upsilon ( 1 )$ ，就是函数 $h \left( t \right) = - 4 . 9 t ^ { 2 } + 4 . 8 t + 1 1$ 在 $t = 1$ 处的导数 $h ^ { \prime } ( 1 )$ ；问题2中抛物线 $f ( x ) = x ^ { 2 }$ 在点 $P _ { 0 }$ (1，1）处的切线 $P _ { 0 } T$ 的斜率 $k _ { 0 }$ ，就是函数 $f ( x ) = x ^ { 2 }$ 在 $x = 1$ 处的导数 $f ^ { \prime } ( 1 )$ ，实际上，导数可以描述任何运动变化事物的瞬时变化率，如效率、交变电流、比热容等.

根据物体的路程关于时间的函数求速度与加速度和求已知曲线的切线这两类问题直接促使了导数的产生.

例1设 $f ( x ) = { \frac { 1 } { x } }$ ，求 $f ^ { \prime } ( 1 )$

$$
\begin{array} { l } { f ^ { \prime } ( 1 ) = \displaystyle \operatorname* { l i m } _ { \Delta x \to 0 } \frac { f ( 1 + \Delta x ) - f ( 1 ) } { \Delta x } = \displaystyle \operatorname* { l i m } _ { \Delta x \to 0 } \frac { \frac { 1 } { 1 + \Delta x } - 1 } { \Delta x } } \\ { = \displaystyle \operatorname* { l i m } _ { \Delta x \to 0 } \Bigl ( - \frac { 1 } { 1 + \Delta x } \Bigr ) = - 1 . } \end{array}
$$

例2将原油精炼为汽油、柴油、塑胶等各种不同产品，需要对原油进行冷却和加热.已知在第 $x \textrm { h }$ 时，原油的温度（单位： $\mathrm { ^ \circ C }$ ）为 $y = f ( x ) = x ^ { 2 } - 7 x + 1 5$ （ $\textstyle 0 \leqslant x \leqslant 8 )$ ．计算第 $2 \textup { h }$ 与第 $6 \mathrm { ~ h ~ }$ 时，原油温度的瞬时变化率，并说明它们的意义.

解：在第 $2 \textrm { h }$ 和第 $6 \textrm { h }$ 时，原油温度的瞬时变化率就是 $f ^ { \prime } ( 2 )$ 和 $f ^ { \prime } ( 6 )$ 根据导数的定义， D R

所以

$$
f ^ { \prime } ( 2 ) = \operatorname* { l i m } _ { \Delta x \to 0 } { \frac { \Delta y } { \Delta x } } = \operatorname* { l i m } _ { \Delta x \to 0 } ( \Delta x - 3 ) = - 3 .
$$

同理可得

$\textcircled{1}$ 请同学们自己完成具体运算过程

$$
f ^ { \prime } ( 6 ) = 5 . ^ { \bullet }
$$

在第 $2 \textrm { h }$ 与第 $6 \mathrm { ~ h ~ }$ 时，原油温度的瞬时变化率分别为一 $3 \ \mathrm { ^ { \circ } C / h }$ 与 $5 ~ { } ^ { \circ } \mathrm { C } / \mathrm { h } .$ 说明在第 $2 \textrm { h }$ 附近，原油温度大约以 $3 \ \mathrm { ^ { \circ } C / h }$ 的速率下降；在第 $6 \textrm { h }$ 附近，原油温度大约以 $5 \ \mathrm { ^ { \circ } C / h }$ 的速率上升.

一般地， $f ^ { \prime } ( x _ { 0 } )$ $\phantom { } 0 { \leqslant } x _ { 0 } { \leqslant } 8 )$ ）反映了原油温度在时刻 $x _ { 0 }$ 附近的变化情况.

例3一辆汽车在公路上沿直线变速行驶，假设 $t$ s时汽车的速度（单位： $\mathrm { m } / \mathrm { s }$ 为$y = v ( t ) = - t ^ { 2 } + 6 t + 6 0$ ，求汽车在第2s与第6s时的瞬时加速度，并说明它们的意义.

分析：瞬时加速度是速度关于时间的瞬时变化率，因此，在第 $2 \mathrm { ~ s ~ }$ 与第6s时，汽车的瞬时加速度分别为 $v ^ { \prime } ( 2 )$ ， $\upsilon ^ { \prime } ( 6 )$ ·

解：在第2s和第 $6 \mathrm { ~ s ~ }$ 时，汽车的瞬时加速度就是 $\upsilon ^ { \prime } ( 2 )$ 和 $\upsilon ^ { \prime } ( 6 )$

根据导数的定义，

$$
\begin{array} { r l } & { \frac { \Delta y } { \Delta t } = \frac { v ( 2 + \Delta t ) - v ( 2 ) } { \Delta t } } \\ & { \quad = \frac { - ( 2 + \Delta t ) ^ { 2 } + 6 ( 2 + \Delta t ) + 6 0 - ( - 2 ^ { 2 } + 6 \times 2 + 6 0 ) } { \Delta t } } \\ & { \quad = - \Delta t + 2 , } \end{array}
$$

所以

$$
v ^ { \prime } ( 2 ) = \operatorname* { l i m } _ { \Delta t  0 } \frac { \Delta y } { \Delta t } = \operatorname* { l i m } _ { \Delta t  0 } ( - \Delta t + 2 ) = 2 .
$$

同理可得

$$
v ^ { \prime } ( 6 ) = - 6 .
$$

在第2s与第6s时，汽车的瞬时加速度分别是 $2 ~ \mathrm { m } / \mathrm { s } ^ { 2 }$ 与一 $6 ~ \mathrm { m / s ^ { 2 } }$ ．说明在第2s附近，汽车的速度每秒大约增加 $2 ~ \mathrm { m / s }$ ；在第6s附近，汽车的速度每秒大约减少 $6 ~ \mathrm { m / s } .$

# 练习

1.在例2中，计算第 $3 \textup { h }$ 与第 $5 \textrm { h }$ 时，原油温度的瞬时变化率，并说明它们的意义.

2.设 $f ( x ) = x$ ，求 $f ^ { \prime } ( 1 )$

3.一质点 $A$ 沿直线运动，位移 $y$ （单位：m）与时间t（单位：s）之间的关系为 $y ( t ) = 2 t ^ { 2 } + 1$ ，求质点 $A$ 在 $t { = } 2 , 7$ s时的瞬时速度.

4.设函数 $f ( x ) = x ^ { 2 } - 1$ 求：

（1）当自变量 $_ { x }$ 由1变到1.1时，函数的平均变化率；  
（2）函数在 $x = 1$ 处的导数.

我们知道，导数 $f ^ { \prime } ( x _ { 0 } )$ 表示函数 $y { = } f ( x )$ 在 $\scriptstyle x = x _ { 0 }$ 处的瞬时变化率，反映了函数 $y =$ $f ( x )$ 在 ${ \boldsymbol { x } } = { \boldsymbol { x } } _ { 0 }$ 附近的变化情况，那么导数 $f ^ { \prime } ( x _ { 0 } )$ ）的几何意义是什么？

# 思考

观察函数 $y = f ( x )$ 的图象（图5.1-3），平均变化率

$$
\frac { \Delta y } { \Delta x } = \frac { f ( x _ { 0 } + \Delta x ) - f ( x _ { 0 } ) } { \Delta x }
$$

表示什么？瞬时化率

$$
f ^ { \prime } ( x _ { 0 } ) = \operatorname* { l i m } _ { \Delta x \to 0 } { \frac { \Delta y } { \Delta x } } { = } \operatorname* { l i m } _ { \Delta x \to 0 } { \frac { f ( x _ { 0 } + \Delta x ) - f ( x _ { 0 } ) } { \Delta x } }
$$

表示什么？

容易发现，平均变化率

$$
\frac { \Delta y } { \Delta x } = \frac { f ( x _ { 0 } + \Delta x ) - f ( x _ { 0 } ) } { \Delta x }
$$

![](images/841a84d0047ffcf59fb44a8699e0c4c31fa2e918d2a3614f4f2d9a37b32df8f7.jpg)  
图5.1-3

表示割线 $P _ { \mathrm { 0 } } P$ 的斜率.

#

如图5.1-4，在曲线 $y = f \left( x \right)$ 上任取一点 $P \ ( \boldsymbol { x }$ ，$f ( x ) )$ ，如果当点 $P ( x , \ f ( x ) )$ 沿着曲线 $y = f ( x )$ 无限趋近于点 $P _ { 0 } \left( x _ { 0 } \right.$ ， $f ( x _ { 0 } )$ 时，割线 $P _ { \mathrm { 0 } } P$ 无限趋近于一个确定的位置，这个确定位置的直线 $P _ { \mathrm { ~ 0 ~ } } T$ 称为曲线 $y = f ( x )$ 在点 $P _ { 0 }$ 处的切线（tangent line).

此处的切线定义与初中学过的圆的切线定义有什么不同？

![](images/657a8cefe8b22ef159c4cb84312313688a6138db765faa420672756f8e8bd056.jpg)  
图5.1-4

利用信息技术工具，演示图5.1-4中 $P _ { \mathrm { 0 } } P$ 的动态变化效果．做一做，看一看！

与问题2中抛物线的割线和切线之间的关系类似，容易知道，割线 $P _ { 0 } P$ 的斜率

$$
k = \frac { f ( x ) - f ( x _ { 0 } ) } { x - x _ { 0 } } .
$$

记 $\Delta \boldsymbol { x } = \boldsymbol { x } - \boldsymbol { x } _ { 0 }$ ，当点 $P$ 沿着曲线 $y { = } f ( x )$ 无限趋近于点 $P _ { 0 }$ 时，即当 $\Delta x  0$ 时， $k$ 无限趋

近于函数 $y { = } f ( x )$ 在 ${ \boldsymbol { x } } = { \boldsymbol { x } } _ { 0 }$ 处的导数．因此，函数 $y { = } f ( x )$ 在 ${ \boldsymbol { x } } = { \boldsymbol { x } } _ { 0 }$ 处的导数 $f ^ { \prime } ( x _ { 0 } )$ 就是切线 $P _ { 0 } T$ 的斜率 $k _ { 0 }$ ，即

$$
k _ { 0 } = \operatorname* { l i m } _ { \Delta x  0 } { \frac { f ( x _ { 0 } + \Delta x ) - f ( x _ { 0 } ) } { \Delta x } } = f ^ { \prime } ( x _ { 0 } ) .
$$

这就是导数的几何意义.

继续观察图5.1-4，可以发现点 $P _ { 0 }$ 处的切线 $P _ { \mathrm { ~ 0 ~ } } T$ 比任何一条割线更贴近点 $P _ { 0 }$ 附近的曲线．进一步地，利用信息技术工具将点 $P _ { 0 }$ 附近的曲线不断放大（图5.1-5），可以发现点 $P _ { 0 }$ 附近的曲线越来越接近于直线．因此，在点 $P _ { 0 }$ 附近，曲线 $y { = } f ( x )$ 可以用点 $P _ { 0 }$ 处的切线 $P _ { 0 } T$ 近似代替.

![](images/37664737739c8da354ed6cb2fb182f5510b1205d04d406fd1ce010046e5a0ecb.jpg)  
图5.1-5

#

①数学上常用简单的对象刻画复杂的对象，例如，用有理数3.1416近似代替无理数 $\pi _ { \epsilon }$ 这里，我们用曲线上某点处的切线近似代替这一点附近的曲线，这是微积分中重要的思想方法———以直代曲.

例4图5.1-6是跳水运动中某运动员的重心相对于水面的高度随时间变化的函数$h \left( t \right) = - 4 . 9 t ^ { 2 } + 4 . 8 t + 1 1$ 的图象．根据图象，请描述、比较曲线 $h \left( t \right)$ 在 $t = t _ { 0 }$ ， $t _ { 1 }$ ， $t _ { 2 }$ 附近的变化情况.

![](images/841a05e2cf36efd28c025a5df132c324ee318e3d7507bf024c5d51a3eaa852b3.jpg)  
图5.1-6

#

为使横轴中各点明确区分，本图坐标系中横、纵轴的单位长度选取不一致.

解：我们用曲线 $h \left( t \right)$ 在 $t = t _ { 0 }$ ， $t _ { 1 }$ ， $t _ { 2 }$ 处的切线斜率，刻画曲线 $h \left( t \right)$ 在上述三个时刻附近的变化情况.

（1）当 $t = t _ { 0 }$ 时，曲线 $h \left( t \right)$ 在 $t = t _ { 0 }$ 处的切线 $l _ { 0 }$ 平行于 $t$ 轴， $h ^ { ' } ( t _ { 0 } ) \mathop { = } 0$ 这时，在 $t =$ $t _ { 0 }$ 附近曲线比较平坦，几乎没有升降.

(2）当 $t = t _ { 1 }$ 时，曲线 $h \left( t \right)$ 在 $t = t _ { 1 }$ 处的切线 $l _ { 1 }$ 的斜率 $h ^ { \prime } ( t _ { 1 } ) { < } 0$ ，这时，在 $t = t _ { 1 }$ 附近曲线下降，即函数 $h \left( t \right)$ 在 $t = t _ { 1 }$ 附近单调递减.

（3）当 $t = t _ { 2 }$ 时，曲线 $h \left( t \right)$ 在 $t = t _ { 2 }$ 处的切线 $l _ { 2 }$ 的斜率 $h ^ { \prime } ( t _ { 2 } ) < 0 .$ ，这时，在 $t = t _ { 2 }$ 附近曲线下降，即函数 $h \left( t \right)$ 在 $t = t _ { 2 }$ 附近也单调递减.

从图5.1-6可以看出，直线 $l _ { 1 }$ 的倾斜程度小于直线 $l _ { 2 }$ 的倾斜程度，这说明曲线 $h \left( t \right)$ 在 $t = t _ { 1 }$ 附近比在 $t = t _ { 2 }$ 附近下降得缓慢.

例5图5.1-7是人体血管中药物浓度 $c = f \left( t \right)$ （单位： $\mathrm { m g / m L } ,$ ）随时间 $t$ （单位：min）变化的函数图象．根据图象，估计 $t = 0 , 2$ ，0.4，0.6， $0 . 8 \ \mathrm { m i n }$ 时，血管中药物浓度的瞬时变化率（精确到0.1）.

![](images/f21ceafbea34ae10a34378fdbafa4f28737d3affef142ea7b8e87bcfcc5b7025.jpg)  
图5.1-7

解：血管中某一时刻药物浓度的瞬时变化率，就是药物浓度 $f ( t )$ 在此时刻的导数，从图象上看，它表示曲线 $f ( t )$ 在此点处的切线的斜率.

如图5.1-7，画出曲线上某点处的切线，利用网格估计这条切线的斜率，可以得到此时刻药物浓度瞬时变化率的近似值.

作 $t = 0 , 8$ 处的切线，并在切线上取两点，如（0.7，0.91)，（1.0，0.48)，则该切线的斜率

$$
k = \frac { 0 . 4 8 - 0 . 9 1 } { 1 . 0 - 0 . 7 }
$$

所以

$$
f ^ { \prime } ( 0 . 8 ) \approx - 1 . 4 .
$$

表5.1-3给出了药物浓度的瞬时变化率的估计值.

表5.1-3  

<html><body><table><tr><td>t</td><td>0.2</td><td>0.4</td><td>0.6</td><td>0.8</td></tr><tr><td>药物浓度的瞬时变化率f&#x27;(t）</td><td>0.4</td><td>0</td><td>-0.7</td><td>-1.4</td></tr></table></body></html>

从求函数 $y = f ( x )$ 在 ${ \boldsymbol { x } } = { \boldsymbol { x } } _ { 0 }$ 处导数的过程可以看到，当 ${ \boldsymbol { x } } = { \boldsymbol { x } } _ { 0 }$ 时， $f ^ { \prime } ( x _ { 0 } )$ 是一个唯一确定的数．这样，当 $_ { \mathcal { X } }$ 变化时， $y = f ^ { \prime } ( x )$ 就是 $_ { \mathcal { X } }$ 的函数，我们称它为 $y = f ( x )$ 的导函数（derivedfunction）（简称导数）. $y = f ( x )$ 的导函数有时也记作 $y ^ { \prime }$ ，即

$$
f ^ { \prime } ( x ) = y ^ { \prime } { = } \operatorname* { l i m } _ { \Delta x \to 0 } { \frac { f ( x + \Delta x ) - f ( x ) } { \Delta x } } .
$$

# 练习

1.根据图5.1-6，描述曲线 $h \left( t \right)$ 在 $t { = } t _ { 3 }$ ， $t _ { 4 }$ 附近增（减）以及增（减）快慢的情况.

2.函数 $f ( x )$ 的图象如图所示，下列数值排序正确的是（）.

（A） $f ^ { \prime } ( 1 ) > f ^ { \prime } ( 2 ) > f ^ { \prime } ( 3 ) > 0 \qquad \mathrm { ( B ) } \ : f ^ { \prime } ( 1 ) < f ^ { \prime } ( 2 ) < f ^ { \prime } ( 3 ) < 0$ (C） $\scriptstyle 0 < f ^ { \prime } ( 1 ) < f ^ { \prime } ( 2 ) < f ^ { \prime } ( 3 ) \qquad \mathrm { ( D ) } \ f ^ { \prime } ( 1 ) > f ^ { \prime } ( 2 ) > 0 > f ^ { \prime } ( 3 )$

3.求曲线 $y = - 2 x ^ { 2 } + 1$ 在点（1， $- 1 )$ 处的切线方程.

4.吹气球时，气球的半径 $r$ （单位： $\mathrm { d m }$ 与体积 $V$ （单位：L）之间的函数关系是 $r ( V ) = \sqrt [ 3 ] { \frac { 3 V } { 4 \pi } }$ 利用信息技术工具，画出 $0 { \leqslant } V { \leqslant } 5$ 时函数的图象，并根据其图象估计 $V { = } 0 , 6$ ， $1 . 2 \mathrm { ~ L ~ }$ 时，气球的瞬时膨胀率.

![](images/464164ce9c56f9734ed48c9bf8b9c2a8072b0cb95da58cb670fc9844c90bdee2.jpg)  
（第2题）

# 习题5.1

# 复习巩固

1.一个物体从 $1 0 \mathrm { ~ m ~ }$ 高处做自由落体运动， $t$ s时该物体距离地面的高度（单位：m）为 $h \left( t \right) = \mathbf { \Gamma } _ { 1 } ^ { 1 }$ $- 4 . 9 t ^ { 2 } + 1 0$ 求该物体在 $t = 1$ 时的瞬时速度，并解释此时物体的运动状况.

2.圆的面积 $S$ （单位： $\mathrm { c m } ^ { 2 }$ ）与半径 $R$ （单位：cm）的关系为 $S { = } { \pi } R ^ { 2 }$ 求 $R = 5 ~ \mathrm { c m }$ 时面积关于半径的瞬时变化率.

3.某质点沿直线运动，位移 $_ y$ （单位：m）与时间 $t$ （单位：s）之间的关系为 $y \left( t \right) = 5 t ^ { 2 } + 6 .$ 求：(1） $2 { \leqslant } t { \leqslant } 3$ 这段时间内的平均速度；(2） $t = 2$ s时的瞬时速度.

4.已知车轮旋转的角度 $\theta$ （单位：rad）与时间 $t$ （单位：s）之间的关系为 $\theta ( t ) = \frac { 2 5 \pi } { 8 } t ^ { 2 }$ 求车轮转动开始后第3.2s时的瞬时角速度.

5.小明骑车上学，开始时匀速行驶，途中因交通堵塞停留了一段时间，后为了赶时间加快速度行驶．与以上事件吻合得最好的图象是（）.

![](images/af54f00dfe6ed7d22bba77444d59fa6fb3180321ef21f0c979f034c3109089de.jpg)

6.如图，试描述函数 $f ( x )$ 在 $x = - 5$ ， $- 4$ ， $- 2$ ，0，1附近的变化情况.

7.求曲线 $y = \frac { 1 } { 2 } x ^ { 2 } - 2$ 点 $\left( 1 , \ - { \frac { 3 } { 2 } } \right)$ 处的切线的倾斜角，

![](images/bd8480c44b29076aa244c2686de6a34e0375a389319bf01d4a310d7efe9ee24f.jpg)  
（第6题）

8.一个质量为 $m { = } 3 ~ \mathrm { k g }$ 的物体做直线运动，设位移 $y$ （单位： $\mathbf { m }$ 与时间 $t$ （单位：s）之间的关系为 $\boldsymbol { y } ( t ) = 1 + t ^ { 2 }$ ，并且物体的动能 $E _ { k } = \frac { 1 } { 2 } m v ^ { 2 }$ ．求物体开始运动后第5s时的动能.

9.根据下面的文字叙述，画出相应的路程关于时间的函数图象的大致形状.

（1）汽车在笔直的公路上匀速行驶；  
（2）汽车在笔直的公路上不断加速行驶；  
（3）汽车在笔直的公路上不断减速行驶.

10.如图，已知函数 $f ( x )$ 的图象，试画出其导函数 $f ^ { \prime } ( x )$ 图象的大致形状.

![](images/e75ab943f6d784da3e32850cbfc04f52437e916df2ccf170844bec4a123f4a84.jpg)  
（第10题）

# 拓广探索

11.在一次跳水运动中， $t$ s时运动员的重心相对于水面的高度（单位：m）是 $h \left( t \right) = - 4 . 9 t ^ { 2 } + \mathbf { \Omega } _ { 1 } ^ { }$ $4 . 8 t + 1 1$ 高度 $h$ 关于时间 $t$ 的导数是速度 $\boldsymbol { v }$ ，速度 $_ v$ 关于时间 $t$ 的导数 $\boldsymbol { v } ^ { \prime }$ 的物理意义是什么？试求 $_ { v }$ ， $\boldsymbol { v } ^ { \prime }$ 关于时间 $t$ 的函数解析式。

12.根据下列条件，分别画出函数 $y = f ( x )$ 的图象在这点附近的大致形状：

（1） $f ( 1 ) = - 5 , ~ f ^ { \prime } ( 1 ) = - 1 ;$ （2） $f ( 5 ) = 1 0 , \quad f ^ { \prime } ( 5 ) = 1 5 ;$ （3） $f ( 1 0 ) = 2 0 \quad$ ， $f ^ { \prime } ( 1 0 ) = 0$

# 5.2 导数的运算

由导函数的定义可知，一个函数的导数是唯一确定的，在必修第一册中我们学过基本初等函数，并且知道，很多复杂的函数都是通过对这些函数进行加、减、乘、除等运算得到的．由此自然想到，能否先求出基本初等函数的导数，然后研究出导数的“运算法则”，这样就可以利用导数的运算法则和基本初等函数的导数求出复杂函数的导数，本节我们就来研究这些问题.

# 5.2.1基本初等函数的导数

根据导数的定义，求函数 $y { = } f ( x )$ 的导数，就是求出当 $\Delta x  0$ 时， $\frac { \Delta y } { \Delta x }$ 无限趋近的那个定值，下面我们求几个常用函数的导数.

# 1.函数 $y = f ( x ) = c$ 的导数

因为

$$
\frac { \Delta y } { \Delta x } = \frac { f ( x + \Delta x ) - f ( x ) } { \Delta x } = \frac { c - c } { \Delta x } = 0 ,
$$

所以

$$
y ^ { \prime } = \operatorname* { l i m } _ { \Delta x \to 0 } { \frac { \Delta y } { \Delta x } } = \operatorname* { l i m } _ { \Delta x \to 0 } 0 = 0 .
$$

若 $y = c$ （图5.2-1）表示路程关于时间的函数，则 $y ^ { \prime } { = } 0$ 可以解释为某物体的瞬时速度始终为0，即一直处于静止状态.

# 2.函数 $y = f ( x ) = x$ 的导数

![](images/53f8972a5888027eb647c752aaec78527bb967787845ef0c7daedff150fd9e19.jpg)  
图5.2-1

因为

$$
\frac { \Delta y } { \Delta x } = \frac { f ( x + \Delta x ) - f ( x ) } { \Delta x } = \frac { ( x + \Delta x ) - x } { \Delta x } = 1 ,
$$

所以

$$
y ^ { \prime } = \operatorname* { l i m } _ { \Delta x \to 0 } { \frac { \Delta y } { \Delta x } } = \operatorname* { l i m } _ { \Delta x \to 0 } 1 { = } 1 .
$$

![](images/f3446640d7255a4f21d4198a7447228ae7c217c6b288e3e78b9100099965179d.jpg)  
图5.2-2

若 $y = x$ （图5.2-2）表示路程关于时间的函数，则 $y ^ { \prime } { = } 1$ 可以解释为某物体做瞬时速度为1的匀速直线运动.

3.函数 $y = f ( x ) = x ^ { 2 }$ 的导数

因为

$$
\begin{array} { c } { \frac { \Delta y } { \Delta x } { = } \frac { f ( x + \Delta x ) - f ( x ) } { \Delta x } { = } \frac { \left( x + \Delta x \right) ^ { 2 } - x ^ { 2 } } { \Delta x } } \\ { { = } \frac { x ^ { 2 } + 2 x \cdot \Delta x + \left( \Delta x \right) ^ { 2 } - x ^ { 2 } } { \Delta x } } \\ { { = } 2 x + \Delta x , } \end{array}
$$

所以

$$
y ^ { \prime } = \operatorname* { l i m } _ { \Delta x  0 } \frac { \Delta y } { \Delta x } { = } \operatorname* { l i m } _ { \Delta x  0 } ( 2 x + \Delta x ) { = } 2 x .
$$

$y ^ { \prime } { = } 2 x$ 表示函数 $y = x ^ { 2 }$ 的图象（图5.2-3）上点 $( x , y )$ 处切线的斜率为 $2 x$ ，说明随着 $_ { \mathcal { X } }$ 的变化，切线的斜率也在变化，另一方面，从导数作为函数在一点的瞬时变化率来看， $y ^ { \prime } { = } 2 x$ 表明：当$x { < } 0$ 时，随着 $_ { \mathcal { X } }$ 的增加， $\mid y ^ { \prime } \mid$ 越来越小， $y = x ^ { 2 }$ 减少得越来越慢；当 $x { > } 0$ 时，随着 $_ { \mathcal { X } }$ 的增加， $\mid { _ y } ^ { \prime }$ |越来越大， $y = x ^ { 2 }$ 增加得越来越快.若 $y = x ^ { 2 }$ 表示路程关于时间的函数，则 $y ^ { \prime } { = } 2 x$ 可以解释为某物体做变速运动，它在时刻 $x$ 的瞬时速度为 $2 x$

![](images/8b870a6da585e19a8d96139143b3a1c58e8f99aee52fad38c78ff6e148ec15e8.jpg)  
图5.2-3

# 4.函数 $y = f ( x ) = x ^ { 3 }$ 的导数

因为

$$
\begin{array} { c } { \displaystyle \frac { \Delta y } { \Delta x } = \frac { f ( x + \Delta x ) - f ( x ) } { \Delta x } = \frac { \left( x + \Delta x \right) ^ { 3 } - x ^ { 3 } } { \Delta x } } \\ { = \frac { x ^ { 3 } + 3 x ^ { 2 } \cdot \Delta x + 3 x \cdot ( \Delta x ) ^ { 2 } + ( \Delta x ) ^ { 3 } - x ^ { 3 } } { \Delta x } } \\ { = 3 x ^ { 2 } + 3 x \cdot \Delta x + ( \Delta x ) ^ { 2 } , } \end{array}
$$

所以

$$
y ^ { \prime } = \operatorname* { l i m } _ { \Delta x \to 0 } { \frac { \Delta y } { \Delta x } } = \operatorname* { l i m } _ { \Delta x \to 0 } [ 3 x ^ { 2 } + 3 x \cdot \Delta x + ( \Delta x ) ^ { 2 } ] = 3 x ^ { 2 } .
$$

$y ^ { \prime } { = } 3 x ^ { 2 }$ 表示函数 $y = x ^ { 3 }$ 的图象（图5.2-4）上点 $( x , \ y )$ 处切线的斜率为 $3 { x } ^ { 2 }$ ，这说明随着 $_ { \mathcal { X } }$ 的变化，切线的斜率也在变化，且恒为非负数.

![](images/0e00aa7775eafd80fd71285f2ea92151a1c4390bd1f888b410bd37e0e3b5222f.jpg)  
图5.2-4

5.函数 $y = f ( x ) = \frac { 1 } { x }$ 的导数

因为

$$
\frac { \Delta y } { \Delta x } = \frac { f ( x + \Delta x ) - f ( x ) } { \Delta x } = \frac { \frac { 1 } { x + \Delta x } - \frac { 1 } { x } } { \Delta x }
$$

$$
= \frac { x - ( x + \Delta x ) } { x ( x + \Delta x ) \Delta x } { = } - \frac { 1 } { x ^ { 2 } + x \cdot \Delta x } ,
$$

所以

$$
y ^ { \prime } = \operatorname* { l i m } _ { \Delta x \to 0 } { \frac { \Delta y } { \Delta x } } { = } \operatorname* { l i m } _ { \Delta x \to 0 } { \Bigl ( } { - { \frac { 1 } { x ^ { 2 } + x \cdot \Delta x } } } { \Bigr ) } { = } { - { \frac { 1 } { x ^ { 2 } } } } .
$$

# 探究

画出函数 $y = { \frac { 1 } { x } }$ 的图象，根据图象，描述它的变化情况，并求出曲线在点（1，1）处的切线方程.

# 6.函数 $y = f ( x ) = { \sqrt { x } }$ 的导数

因为

$$
\begin{array} { r l } & { \frac { \Delta y } { \Delta x } = \frac { f ( x + \Delta x ) - f ( x ) } { \Delta x } = \frac { \sqrt { x + \Delta x } - \sqrt { x } } { \Delta x } } \\ & { \quad = \frac { ( \sqrt { x + \Delta x } - \sqrt { x } ) ( \sqrt { x + \Delta x } + \sqrt { x } ) } { \Delta x ( \sqrt { x + \Delta x } + \sqrt { x } ) } } \\ & { \quad = \frac { 1 } { \sqrt { x + \Delta x } + \sqrt { x } } , } \end{array}
$$

所以

$$
y ^ { \prime } = \operatorname* { l i m } _ { \Delta x \to 0 } { \frac { \Delta y } { \Delta x } } { = } \operatorname* { l i m } _ { \Delta x \to 0 } { \frac { 1 } { \sqrt { x + \Delta x } + \sqrt { x } } } { = } \frac { 1 } { 2 \sqrt { x } } .
$$

前面我们根据导数的定义求出了一些常用函数的导数，一般地，有下面的基本初等函数的导数公式表（表5.2-1），这些公式可以直接使用.

表5.2-1  

<html><body><table><tr><td>基本初等函数的导数公式</td></tr><tr><td>1.若f(x）=c（c为常数），则f&#x27;（x）=0；</td></tr><tr><td>2.若f（x）=x°（a∈R，且a≠0），则f‘（x）=axa-1； 3.若f(x)=sinx，则f&#x27;(x）=cosx;</td></tr><tr><td>4.若f(x）=cosx，则f‘（x）=-sinx；</td></tr><tr><td>5.若f（x）=a²（a&gt;0，且a≠1），则f‘（x）=alna；</td></tr><tr><td>特别地，若f（x）=e，则f&#x27;（x）=e； 1</td></tr><tr><td>6.若f（x）=logax（a&gt;0，且a≠1），则f&#x27;（x）= 𝑥lna</td></tr></table></body></html>

例1求下列函数的导数：

(1）y=x²； $y = x ^ { \frac { 2 } { 3 } } ; \qquad ( 2 ) { \mathrm { ~ } } y = \log _ { 2 } x .$ 解：(1) $y ^ { \prime } { = } \left( x ^ { \frac { 2 } { 3 } } \right) ^ { \prime } { = } \frac { 2 } { 3 } x ^ { \frac { 2 } { 3 } - 1 } { = } \frac { 2 } { 3 } x ^ { - \frac { 1 } { 3 } }$ (2） $y ^ { \prime } { = } ( \log _ { 2 } x ) ^ { \prime } { = } \frac { 1 } { x \ln 2 } .$

例2假设某地在20年间的年均通货膨胀率为 $5 \%$ ，物价 $\boldsymbol { \phi }$ （单位：元）与时间 $t$ （单位：年）之间的关系为

$$
{ p } ( t ) = p _ { \mathrm { 0 } } ( 1 + 5 \% ) ^ { t } ,
$$

其中 $\scriptstyle { \mathcal { P } } _ { 0 }$ 为 $t = 0$ 时的物价，假定某种商品的 $\phi _ { 0 } = 1$ ，那么在第10个年头，这种商品的价格上涨的速度大约是多少（精确到0.01元/年）？

解：根据基本初等函数的导数公式表，有

$$
{ \boldsymbol { p } } ^ { \prime } ( t ) = 1 . 0 5 ^ { t } \ln 1 . 0 5 .
$$

所以

$$
p ^ { \prime } ( 1 0 ) = 1 . 0 5 ^ { 1 0 } \ln 1 . 0 5 \approx 0 . 0 8 .
$$

# D

如果某种商品的 $\mathbf { \nabla } \phi _ { 0 } =$ 5，那么在第10个年头，这种商品的价格上涨的速度大约是多少？

所以，在第10个年头，这种商品的价格约以0.08元/年的速度上涨.

# 练习

1.求下列函数的导数：

(1) $y = { \frac { 1 } { x ^ { 4 } } } ;$ (2) $y = { \sqrt [ 3 ] { x ^ { 4 } } } ;$ （3） $y = 3 ^ { x } \mathrm { ~ ; ~ }$ (4） $y = \left( { \frac { 1 } { 2 } } \right) ^ { x } ;$ （5） $y = \log _ { 4 } x $ （6） $y = \log _ { \frac { 1 } { 2 } } x$

2.求下列函数在给定点处的导数：

（1） $y = x ^ { 5 }$ 在 $x = 3$ 处的导数；  
(2） $y = \ln x$ 在 $x { = } \frac { 2 } { 3 }$ 处的导数；  
（3） $y = \sin { x }$ 在 $x = 2 \pi$ 处的导数；  
(4） $y = \mathrm { e } ^ { x }$ 在 $x = 0$ 处的导数.

3.求余弦曲线 $y = \cos { \ x }$ 在点 $\left( { \frac { \pi } { 2 } } , \mathbf { \Lambda } _ { 0 } \right)$ 处的切线方程.

4.求曲线 $y = x ^ { \frac { 1 } { 2 } }$ 在点（4，2）处的切线方程.

# 5.2.2导数的四则运算法则

在例2中，当 $ \phi _ { 0 } = 5$ 时， $p ( t ) = 5 \times 1 . 0 5 ^ { t }$ ，这时，求 $\boldsymbol { \mathscr { P } }$ 关于 $t$ 的导数可以看成求函数$f ( t ) = 5$ 与 $g ( t ) { = } 1 . 0 5 ^ { t }$ 乘积的导数．一般地，如何求两个函数的和、差、积、商的导数呢？

# 探究

设 $f ( x ) = x ^ { 2 }$ ， $g \left( x \right) = x$ ，计算 $[ f ( x ) + g ( x ) ]$ 与 $\left[ f ( x ) - g ( x ) \right] ^ { \prime }$ ，它们与$f ^ { \prime } ( x )$ 和 $g ^ { \prime } ( x )$ 有什么关系？再取几组函数试试，上述关系仍然成立吗？由此你能想到什么？

设 $y = f ( x ) + g \left( x \right) = x ^ { 2 } + x$ ，因为

$$
\begin{array} { c } { \frac { \Delta y } { \Delta x } { = } \frac { ( x + \Delta x ) ^ { 2 } + ( x + \Delta x ) - ( x ^ { 2 } + x ) } { \Delta x } } \\ { { = } \frac { ( \Delta x ) ^ { 2 } + 2 x \cdot \Delta x + \Delta x } { \Delta x } } \\ { { = } \Delta x + 2 x + 1 , } \end{array}
$$

所以

$$
{ \big [ } f ( x ) + g ( x ) { \big ] } ^ { \prime } = y ^ { \prime } = \operatorname* { l i m } _ { \Delta x \to 0 } { \frac { \Delta y } { \Delta x } } = \operatorname* { l i m } _ { \Delta x \to 0 } ( \Delta x + 2 x + 1 ) = 2 x + 1 .
$$

而

$$
f ^ { \prime } ( x ) { = } ( x ^ { 2 } ) ^ { \prime } { = } 2 x , g ^ { \prime } ( x ) { = } x ^ { \prime } { = } 1 ,
$$

所以

$$
[ f ( x ) + g ( x ) ] ^ { \prime } = f ^ { \prime } ( x ) + g ^ { \prime } ( x ) .
$$

同样地，对于上述函数， $[ f ( x ) - g ( x ) ] ^ { \prime } { = } f ^ { \prime } ( x ) { - } g ^ { \prime } ( x ) .$

一般地，对于两个函数 $f ( x )$ 和 $g \left( x \right)$ 的和（或差）的导数，我们有如下法则：

$$
[ \overbrace { { \cal { f } } ( x ) \pm g ( x ) } ] ^ { \prime } { = } f ^ { \prime } ( x ) { \pm } g ^ { \prime } ( x ) .
$$

例3求下列函数的导数：

$$
y = 2 ^ { x } + \cos x .
$$

$$
\begin{array} { c } { { = ( x ^ { 3 } ) ^ { \prime } - ( x ) ^ { \prime } + ( 3 ) ^ { \prime } } } \\ { { { } } } \\ { { = 3 x ^ { 2 } - 1 ; } } \\ { { { } y ^ { \prime } { = } ( 2 ^ { x } + \cos x ) ^ { \prime } } } \\ { { { } } } \\ { { = ( 2 ^ { x } ) ^ { \prime } + ( \cos x ) ^ { \prime } } } \\ { { { } } } \\ { { = 2 ^ { x } \ln 2 - \sin x . } } \end{array}
$$

# 思考

设 $f ( x ) = x ^ { 2 }$ ， $g \left( x \right) = x$ ，计算 $\left[ f ( \boldsymbol { x } ) _ { g } ( \boldsymbol { x } ) \right]$ 与 $f ^ { \prime } ( x ) g ^ { \prime } ( x )$ ，它们是否相等？$f ( x )$ 与 $g ( x )$ 商的导数是否等于它们导数的商呢？

通过计算可知， $\left[ f ( x ) g \left( x \right) \right] ^ { \prime } = \left( x ^ { 3 } \right) ^ { \prime } = 3 x ^ { 2 }$ ， $f ^ { \prime } \left( x \right) g ^ { \prime } \left( x \right) = 2 x \bullet 1 = 2 x$ ，因此$[ f ( x ) g ( x ) ] ^ { \prime } \ne f ^ { \prime } ( x ) g ^ { \prime } ( x )$ ，同样地， $\left[ { \frac { f ( x ) } { g ( x ) } } \right] ^ { \prime }$ 与 $\frac { f ^ { \prime } ( x ) } { g ^ { \prime } ( x ) }$ 也不相等。

事实上，对于两个函数 $f ( x )$ 和 $g \left( x \right)$ 的乘积（或商）的导数，我们有如下法则：

$$
\left[ f ( x ) g ( x ) \right] ^ { \prime } = f ^ { \prime } ( x ) g ( x ) + f ( x ) g ^ { \prime } ( x ) ;
$$

$$
\left[ { \frac { f ( x ) } { g ( x ) } } \right] ^ { \prime } { = } { \frac { f ^ { \prime } ( x ) g ( x ) { - } f ( x ) g ^ { \prime } ( x ) } { [ g ( x ) ] ^ { 2 } } } \quad ( g ( x ) { \neq } 0 ) .
$$

由函数的乘积的导数法则可以得出

$$
[ c f ( x ) ] ^ { \prime } { = } c ^ { \prime } f ( x ) { + } c f ^ { \prime } ( x ) { = } c f ^ { \prime } ( x ) ,
$$

也就是说，常数与函数的积的导数，等于常数与函数的导数的积，即

$$
[ c f ( x ) ] ^ { \prime } { = } c f ^ { \prime } ( x ) .
$$

例4求下列函数的导数：

(1） $y = x ^ { 3 } \mathrm { e } ^ { x }$ (2) $y = { \frac { 2 \sin x } { x ^ { 2 } } } .$

解：（1) ${ \begin{array} { r l } & { y ^ { \prime } = ( x ^ { 3 } \mathbf { e } ^ { x } ) ^ { \prime } } \\ & { \quad = ( x ^ { 3 } ) ^ { \prime } \mathbf { e } ^ { x } + x ^ { 3 } ( \mathbf { e } ^ { x } ) ^ { \prime } } \\ & { \quad = 3 x ^ { 2 } \mathbf { e } ^ { x } + x ^ { 3 } \mathbf { e } ^ { x } . } \end{array} }$

例5日常生活中的饮用水通常是经过净化的．随着水的纯净度的提高，所需净化费用不断增加.已知将1t水净化到纯净度为 $x \%$ 时所需费用（单位：元）为

$$
c \left( x \right) = \frac { 5 2 8 4 } { 1 0 0 - x } ( 8 0 < x < 1 0 0 ) .
$$

求净化到下列纯净度时，所需净化费用的瞬时变化率：

(1) $90 \%$ (2） $98 \%$

解：净化费用的瞬时变化率就是净化费用函数的导数.

$$
\begin{array} { r l } & { c ^ { \prime } ( x ) = \left( \frac { 5 2 8 4 } { 1 0 0 - x } \right) ^ { \prime } } \\ & { \qquad = \frac { 5 2 8 4 ^ { \prime } \times ( 1 0 0 - x ) - 5 2 8 4 \times ( 1 0 0 - x ) ^ { \prime } } { ( 1 0 0 - x ) ^ { 2 } } } \\ & { \qquad = \frac { 0 \times ( 1 0 0 - x ) - 5 2 8 4 \times ( - 1 ) } { ( 1 0 0 - x ) ^ { 2 } } } \\ & { \qquad = \frac { 5 2 8 4 } { ( 1 0 0 - x ) ^ { 2 } } . } \end{array}
$$

（1）因为 $c ^ { \prime } ( 9 0 ) = { \frac { 5 2 8 4 } { ( 1 0 0 - 9 0 ) ^ { 2 } } } = 5 2 . 8 4$ ，所以，净化到纯净度为 $90 \%$ 时，净化费用的瞬时变化率是52.84元/吨.

(2）因为 $c ^ { \prime } ( 9 8 ) = { \frac { 5 2 8 4 } { ( 1 0 0 - 9 8 ) ^ { 2 } } } = 1 3 2 1$ ，所以，净化到纯净度为 $98 \%$ 时，净化费用的瞬时变化率是1321元/吨.

函数 $f ( x )$ 在某点处导数的大小表示函数在此点附近变化的快慢．由上述计算可知，$c ^ { \prime } ( 9 8 ) = 2 5 c ^ { \prime } ( 9 0 )$ ，它表示净化到纯净度为 $98 \%$ 左右时净化费用的变化率，大约是净化到纯净度为 $90 \%$ 左右时净化费用变化率的25倍．这说明，水的纯净度越高，需要的净化费用就越多，而且净化费用增加的速度也越快.

# 练习

1.运用基本初等函数的导数公式与导数运算法则，重新求解5.1节例2．你是否感觉到运算法则给解题带来的方便简捷？

2.求下列函数的导数：

(1） $y = 2 x ^ { 3 } - 3 x ^ { 2 } - 4$ （2） $y = 3 \cos { x } + 2 ^ { x }$ (3） $y = \operatorname { e } ^ { x } \ln x$ (4） $y = ( x ^ { 2 } + 2 x ) \sqrt { x }$ （5） $_ { y = \frac { \ln x } { x } }$ （6） $y = \tan x$

3.求曲线 $y = x ^ { 2 } + \frac { 3 } { x }$ 在点（1，4）处的切线方程.

$$
\yen 123,456,7
$$

# 5.2.3简单复合函数的导数

# 思考

如何求函数 $y = \ln ( 2 x - 1 )$ 的导数呢？

函数 $y = \ln ( 2 x - 1 )$ 不是由基本初等函数通过加、减、乘、除运算得到的，所以无法用现有的方法求它的导数，下面，我们先分析这个函数的结构特点.

若设 $u = 2 x - 1 \ \left( x > \frac { 1 } { 2 } \right)$ ，则 $y = \ln u$ 从而 $y = \ln ( 2 x - 1 )$ 可以看成是由 $y = \ln u$ 和$u = 2 x - 1 \ \left( x > \frac { 1 } { 2 } \right)$ 经过“复合”得到的，即 $y$ 可以通过中间变量 $u$ 表示为自变量 $_ { \mathcal { X } }$ 的函数.

如果把 $y$ 与 $u$ 的关系记作 $\scriptstyle y = f ( u )$ ， $u$ 与 $\boldsymbol { \mathscr { x } }$ 的关系记作 $u = g \left( x \right)$ ，那么这个“复合”过程可表示为

$$
y = f ( u ) = f ( g ( x ) ) = \ln ( 2 x - 1 ) .
$$

一般地，对于两个函数 $\scriptstyle y = f ( u )$ 和 $u = g \left( x \right)$ ，如果通过中间变量 $u$ ， $y$ 可以表示成 $_ { \mathcal { X } }$ 的函数，那么称这个函数为函数 $\scriptstyle y = f ( u )$ 和 $u = g \left( x \right)$ 的复合函数（compositefunction），记作 $y = f ( g ( x ) )$ ·

我们遇到的许多函数都可以看成是由两个函数经过“复合”得到的．例如，函数 $y =$ $\ln ( 2 x - 1 )$ 由 $y = \ln u$ 和 $u = 2 x - 1 \left( x > \frac { 1 } { 2 } \right)$ 复合而成，又如，函数 $y = \sin { 2 x }$ 由 $y = \sin { u }$ 和 $u = 2 x$ 复合而成.

如何求复合函数的导数呢？我们先来研究 $y = \sin { 2 x }$ 的导数.

一个合理的猜想是，函数 $y = \sin 2 x$ 的导数一定与函数 $y = \sin u$ ， $u = 2 x$ 的导数有关.下面我们就来研究这种关系.

以 $y _ { x } ^ { \prime }$ 表示 $_ y$ 对 $_ { \mathcal { X } }$ 的导数， $y _ { u } ^ { \prime }$ 表示 $y$ 对 $u$ 的导数， $u _ { x } ^ { ' }$ 表示 $u$ 对 $_ { \mathcal { X } }$ 的导数．一方面，

$$
\begin{array} { r l } & { y _ { x } ^ { \prime } = ( \sin 2 x ) ^ { \prime } = ( 2 \sin x \cos x ) ^ { \prime } } \\ & { \quad = 2 \bigl [ ( \sin x ) ^ { \prime } \cdot \cos x + \sin x \cdot ( \cos x ) ^ { \prime } \bigr ] } \\ & { \quad = 2 \bigl [ \cos x \cdot \cos x + \sin x \cdot ( - \sin x ) \bigr ] } \\ & { \quad = 2 ( \cos ^ { 2 } x - \sin ^ { 2 } x ) } \\ & { \quad = 2 \cos 2 x . } \end{array}
$$

另一方面， $y _ { u } ^ { \prime } { = } ( \sin u ) ^ { \prime } { = } { \cos u }$ ， $u _ { x } ^ { \prime } { = } { ( } 2 x { ) } ^ { \prime } { = } 2 .$

可以发现， $y _ { x } ^ { \prime } = 2 \cos 2 x = \cos u \cdot 2 = y _ { u } ^ { \prime } \bullet u _ { x } ^ { \prime }$

一般地，对于由函数 $\scriptstyle y = f ( u )$ 和 $u = g \left( x \right)$ 复合而成的函数 $y = f ( g ( x ) )$ ，它的导数与函数 $y = f ( u ) , \ u = g ( x )$ 的导数间的关系为

$$
\boxed { \begin{array} { r l } { \boldsymbol { y } _ { x } ^ { \prime } = \boldsymbol { y } _ { u } ^ { \prime } \bullet \boldsymbol { u } _ { x } ^ { \prime } . } \end{array} }
$$

即 $y$ 对 $_ { \mathcal { X } }$ 的导数等于 $y$ 对 $u$ 的导数与 $u$ 对 $_ { \mathcal { X } }$ 的导数的乘积.

例6求下列函数的导数：

（1） $y = ( 3 x + 5 ) ^ { 3 }$ （2）y=e-0.05x+1；(3） $y = \ln ( 2 x - 1 ) .$

解：（1）函数 $y = ( 3 x + 5 ) ^ { 3 }$ 可以看作函数 $y = u ^ { 3 }$ 和 $u = 3 x + 5$ 的复合函数.根据复合函数的求导法则，有

$$
\begin{array} { r l } & { y _ { x } ^ { \prime } = y _ { u } ^ { \prime } \bullet u _ { x } ^ { \prime } } \\ & { \quad = ( u ^ { 3 } ) ^ { \prime } \bullet ( 3 x + 5 ) ^ { \prime } } \\ & { \quad = 3 u ^ { 2 } \times 3 } \\ & { \quad = 9 ( 3 x + 5 ) ^ { 2 } . } \end{array}
$$

（2）函数 $y = \mathrm { e } ^ { - 0 . 0 5 x + 1 }$ 可以看作函数 $y = \mathrm { e } ^ { u }$ 和 $u = - 0 . 0 5 x + 1$ 的复合函数．根据复合函数的求导法则，有

$$
\begin{array} { r l } & { y _ { x } ^ { \prime } = y _ { u } ^ { \prime } \bullet u _ { x } ^ { \prime } } \\ & { \quad = ( \mathrm { e } ^ { u } ) ^ { \prime } \bullet ( - 0 . 0 5 x + 1 ) ^ { \prime } } \\ & { \quad = - 0 . 0 5 \mathrm { e } ^ { u } } \\ & { \quad = - 0 . 0 5 \mathrm { e } ^ { - 0 . 0 5 x + 1 } . } \end{array}
$$

（3）函数 $y = \ln ( 2 x - 1 )$ 可以看作函数 $y = \ln u$ 和 $u = 2 x - 1 { \big ( } x > { \frac { 1 } { 2 } } { \big ) }$ 的复合函数.根据复合函数的求导法则，有

$$
\begin{array} { r l } & { y _ { x } ^ { \prime } = y _ { u } ^ { \prime } \bullet u _ { x } ^ { \prime } } \\ & { \quad = ( \ln u ) ^ { \prime } \bullet ( 2 x - 1 ) ^ { \prime } } \\ & { \quad = \frac { 1 } { u } \times 2 } \\ & { \quad = \frac { 2 } { 2 x - 1 } . } \end{array}
$$

例7 某个弹簧振子在振动过程中的位移 $y$ （单位： $\mathrm { m m } , \mathrm { m } ,$ 与时间 $t$ （单位：s)之间的关系为 $y = 1 8 \sin \Bigl ( \frac { 2 \pi } { 3 } t - \frac { \pi } { 2 } \Bigr )$ 求函数 $y$ 在 $t = 3$ s时的导数，并解释它的实际意义.

解：函数 $y = 1 8 \sin \Bigl ( \frac { 2 \pi } { 3 } t - \frac { \pi } { 2 } \Bigr )$ 可以看作函数 $y = 1 8 \sin { u }$ 和 $u = \frac { 2 \pi } { 3 } t - \frac { \pi } { 2 }$ 的复合函数，根据复合函数的求导法则，有 4

$$
\begin{array} { r l } & { y _ { t } ^ { \prime } = y _ { u } ^ { \prime } \bullet u _ { t } ^ { \prime } } \\ & { \quad = ( 1 8 \sin u ) ^ { \prime } \bullet \Big ( \frac { 2 \pi } { 3 } t - \frac { \pi } { 2 } \Big ) ^ { \prime } } \\ & { \quad = 1 8 \cos u \times \frac { 2 \pi } { 3 } } \\ & { \quad = 1 2 \pi \cos \Big ( \frac { 2 \pi } { 3 } t - \frac { \pi } { 2 } \Big ) . } \end{array}
$$

$t = 3$ 时 $y _ { t } ^ { \prime } { = } 1 2 \pi \cos ( \frac { 3 \pi } { 2 } ) { = } 0$

它表示当 $t = 3$ s时，弹簧振子振动的瞬时速度为 $0 \ \mathrm { m m / s }$

1.求下列函数的导数：

(1）y= ${ } y = { \frac { 2 } { \sqrt { 3 x + 1 } } } ;$ （ ${ \begin{array} { r l r l } & { { \mathrm { ( 2 ) ~ } } y = ( 1 { - } 2 x ) ^ { 3 } y } & & { \quad { \mathrm { ( 3 ) ~ } } y = \log _ { 2 } ( 2 x + 1 ) ; } \\ & { } \\ & { { \mathrm { ( 5 ) ~ } } y = \sin \left( { \frac { 3 \pi } { 2 } } { - } 3 x \right) ; } & & { \quad { \mathrm { ( 6 ) ~ } } y = 2 ^ { 2 x + 1 } . } \end{array} }$ （4） $\scriptstyle { y = \cos { \frac { x } { 3 } } } ;$

2.求下列函数在给定点处的导数：

(1) $y = \mathrm { e } ^ { - 2 x + 1 }$ 在 $x = \frac { 1 } { 2 }$ 处的导数；   
（2） $y = \ln ( 5 x + 2 )$ 在 $x = 1$ 处的导数.

3.求曲线 $y = \sqrt [ 3 ] { 3 x - 1 }$ 在点 $\left( { \frac { 2 } { 3 } } , 1 \right)$ 处的切线方程.

# 习题5.2

# 复习巩固

1.求下列函数的导数：

（1） $y = 2 x ^ { 3 } - 3 x ^ { 2 } + 5 ;$ (2） $y = { \frac { 2 } { x } } + { \frac { 4 } { x + 1 } } ;$ （3）y=2²+log2x；（4） $\boldsymbol { y } = \boldsymbol { x } ^ { 5 } \mathrm { e } ^ { \boldsymbol { x } } ;$ $y = { \frac { x ^ { 3 } - 1 } { \sin x } } ;$ (6) $y = { \frac { \sin x } { \sin x + \cos x } } .$

2.求下列函数的导数：

(1） $y = ( x + 1 ) ^ { 9 9 } ;$ （2）y=√2x+1 (3） $y = ( 2 x - 3 ) \sin ( 2 x + 5 ) ;$ 4 $y = { \frac { \cos ( 3 x - 2 ) } { 2 x } } ;$ （5） $y = ( 3 x + 1 ) ^ { 2 } \ln ( 3 x ) ;$ （6） $y = 3 ^ { x } \mathrm { e } ^ { - 3 x } ,$

3.已知函数 $f ( x ) = 1 3 - 8 x + { \sqrt { 2 } } x ^ { 2 }$ ，且 $f ^ { \prime } ( x _ { 0 } ) { = } 4$ ，求 $x _ { 0 }$

4.已知函数 $y = x \ln x$ （1）求这个函数的导数；（2）求这个函数的图象在点（1，0）处的切线方程.

5.求曲线 $y = { \frac { \sin x } { x } }$ 在点 $M ( \pi , ~ 0 )$ 处的切线方程.

# 综合运用

6.已知函数 $f ( x )$ 满足 $f ( x ) { = } f ^ { \prime } \biggl ( \frac { \pi } { 4 } \biggr ) \sin x { - } \cos x$ ，求 $f ( x )$ 在 $x = \frac { \pi } { 4 }$ 处的导数.

7.设函数 $f ( x ) { = } 1 { - } \mathrm { e } ^ { x }$ 的图象与 $_ { x }$ 轴相交于点 $P$ ，求该曲线在点 $P$ 处的切线方程.

8.已知函数 $f ( x ) = { \frac { x ^ { 2 } } { 2 } } + 2 x - 3 \ln x$ ，求 $f ( x )$ 的导数，并求出 $f ^ { \prime } ( x ) { > } 0$ 的解集.

9.氢气是一种由地表自然散发的无味的放射性气体．如果最初有 $5 0 0 ~ \mathrm { g }$ 氢气，那么 $t$ 天后，氢气的剩余量为 $A \left( t \right) = 5 0 0 \times 0 . 8 3 4 ^ { t } \mathrm { ~ g ~ }$

（1）氢气的散发速度是多少？(2） $A ^ { \prime } ( 7 )$ 的值是什么（精确到0.1）？它表示什么意义？

10.设某高山滑雪运动员在一次滑雪训练中滑行的路程l（单位： $\mathbf { m }$ ）与时间 $t$ （单位：s）之间的关系为 $l \left( t \right) = 2 t ^ { 2 } + \frac { 3 } { 2 } t$

（1）求 $l$ 关于 $t$ 的导数，并解释它的实际意义；  
（2）当 $t = 3$ s时，求运动员的滑雪速度；  
（3）当运动员的滑雪路程为 $3 8 ~ \mathrm { m }$ 时，求此时的滑雪速度.

# 拓广探索

11.设曲线 $y = \mathrm { e } ^ { 2 a x }$ 在点(0，1)处的切线与直线 $2 x - y + 1 = 0$ 垂直，求 $a$ 的值.

12.请按步骤，完成下面的任务.

（1）利用信息技术工具，分别画出 $h = 1$ ，0.5，0.1，0.05时，函数 sin(x+h）-sinx的图象.（2）画出函数 $y = \cos x$ 的图象，并与上面的四个图象比较，当 $h$ 越来越小时，你观察到了什么？（3）猜测 $_ { y = \sin x }$ 的导数，它与基本初等函数的导数公式表中 $\sin x$ 的导数公式一样吗？

13.某海湾拥有世界上最大的海潮，假设在该海湾某一固定点处，大海水深 $d$ （单位： $\mathrm { m } )$ ）与午夜后的时间 $t$ （单位：h）之间的关系为 $d ( t ) = 1 0 + 4 \cos { \frac { \pi } { 6 } } t$ 6t.求下列时刻此固定点的水位变化速度（精确到 $0 . 0 1 ~ \mathrm { { m } / h } )$

（1）上午6：00； （2）上午10:00；  
（3）中午12:00； （4）下午4：00.

![](images/9cf147cca3c213036ca94328c84027f25f5702e9e246d1881121e5a591204a0c.jpg)

# 探究与发现

# 牛顿法- -用导数方法求方程的近似解

人们很早以前就开始探索高次方程的数值求解问题.牛顿（IsaacNewton，1643—1727）在《流数法》一书中，给出了高次代数方程的一种数值解法牛顿法，这种求方程根的方法，在科学界已被广泛采用。

下面，我们看看如何求方程 $\cdot { \frac { 1 } { 1 5 } } x ^ { 3 } - { \frac { 3 } { 5 } } x ^ { 2 } + 2 x - { \frac { 1 2 } { 5 } } = 0$ 的根.从函数的观点看，方程 $\cdot { \frac { 1 } { 1 5 } } x ^ { 3 } - { \frac { 3 } { 5 } } x ^ { 2 } + 2 x - { \frac { 1 2 } { 5 } } = 0$ 的根就是函数 $f ( x ) = { \frac { 1 } { 1 5 } } x ^ { 3 } -$ $\frac { 3 } { 5 } x ^ { 2 } + 2 x - \frac { 1 2 } { 5 }$ 的零点，从图形上看，一个函数的零点 $r$ 就是函数 $f ( x )$ 的图象与 $_ { \mathcal { X } }$ 轴的交点的横坐标（图1)．那么，如何求 $r$ 的值呢？

如果可以找到一步一步逼近 $r$ 的 $x _ { 0 }$ ， $x _ { 1 }$ ，…,$x _ { n }$ ，使得当 $n$ 很大时， $\left| { \boldsymbol { x } } _ { n } - { \boldsymbol { r } } \right|$ 很小，那么，我们就可以把 $x _ { n }$ 的值作为 $r$ 的近似值，即把 $\boldsymbol { \mathcal { X } } _ { n }$ 作为方程 $f ( x ) { = } 0$ 的近似解。

牛顿用“作切线”的方法找到了这一串 $x _ { 0 }$ $x _ { 1 }$ ,…, $x _ { n }$ ．当然，要有一个起始点，比如，我们从 $x _ { 0 } = 6$ 开始.

如图1，在横坐标为 $x _ { 0 }$ 的点处作 $f ( x )$ 的切线，切线与 $_ { \mathcal { X } }$ 轴交点的横坐标就是 $x _ { 1 }$ ；用 $x _ { 1 }$ 代替 $x _ { 0 }$ 重复上面的过程得到 $x _ { 2 }$ ；一直继续下去，得到 $x _ { 0 }$ $x _ { 1 }$ ，…， $x _ { n }$ ，从图形上我们可以看到， $x _ { 1 }$ 较 $x _ { 0 }$ 接近 $r$ ， $x _ { 2 }$ 较 $x _ { 1 }$ 接近 $r$ ，等等。它们越来越逼近 $r$ 接下来的任务是计算 $x _ { n }$ ，我们知道， $f ( x )$ 在点 $( x _ { 0 }$ $f ( x _ { 0 } )$ 处切线的斜率是 $f ^ { \prime } ( x _ { 0 } )$ ，因此切线方程为

$$
y - f ( x _ { 0 } ) = f ^ { \prime } ( x _ { 0 } ) ( x - x _ { 0 } ) .
$$

如果 $f ^ { \prime } ( x _ { 0 } ) \neq 0$ ，那么切线与 $_ { \mathcal { X } }$ 轴交点的横坐标是

$$
\scriptstyle x _ { 1 } = x _ { 0 } - { \frac { f ( x _ { 0 } ) } { f ^ { \prime } ( x _ { 0 } ) } } .
$$

继续这个过程，就可以推导出如下求方程根的牛顿法公式：

如果 $f ^ { \prime } ( x _ { n - 1 } ) \neq 0$ ，那么

![](images/aa22d8d1d9d9398b0ad47ffed9b5beebb4286ac648166a0a0a97ab01b1555901.jpg)  
图1

#

$\textcircled{4}$ 起始点当然是越接近零点越好，我们可以事先对零点作一个估计.如果使用信息技术工具，这种估计是比较容易的。

$$
x _ { n } { = } x _ { n - 1 } { - } { \frac { f ( x _ { n - 1 } ) } { f ^ { \prime } ( x _ { n - 1 } ) } } .
$$

对于一个给定的精确度，你能根据上述公式，求出方程 $\cdot { \frac { 1 } { 1 5 } } x ^ { 3 } - { \frac { 3 } { 5 } } x ^ { 2 } + 2 x - { \frac { 1 2 } { 5 } } = 0$ 的近似解吗？

②请同学们自己推导.

进一步思考以下问题：

1.不同的初始值对求方程的近似解有影响吗？如果有，影响在什么地方？2.你还知道其他求方程近似解的方法吗？你认为牛顿法的优点和缺点是什么？

# 5.3 导数在研究函数中的应用

在必修第一册中，我们通过图象直观，利用不等式、方程等知识，研究了函数的单调性、周期性、奇偶性以及最大（小）值等性质．在本章前两节中，我们学习了导数的概念和运算，知道导数是关于瞬时变化率的数学表达，它定量地刻画了函数的局部变化，能否利用导数更加精确地研究函数的性质呢？本节我们就来讨论这个问题.

# 5.3.1函数的单调性

我们先来研究前面学习过的跳水问题.

# 思考

图5.3-1（1）是某跳水运动员的重心相对于水面的高度 $h$ 随时间 $t$ 变化的函数$h \left( t \right) = - 4 . 9 t ^ { 2 } + 4 . 8 t + 1 1$ 的图象，图5.3-1（2）是跳水运动员的速度 $\boldsymbol { v }$ 随时间 $t$ 变化的函数 $v ( t ) = h ^ { \prime } ( t ) = - 9 . 8 t + 4 . 8$ 的图象． $a = \frac { 2 4 } { 4 9 }$ $^ { b }$ 是函数 $h \left( t \right)$ 的零点.

![](images/26cffcdaeaf3e4c03b8625d9a86cd877235bd28517251bcf2158675c806826f8.jpg)  
图5.3-1

运动员从起跳到最高点，以及从最高点到入水这两段时间的运动状态有什么区别？如何从数学上刻画这种区别？

观察图象可以发现：

（1）从起跳到最高点，运动员的重心处于上升状态，离水面的高度 $h$ 随时间 $t$ 的增加

而增加，即 $h \left( t \right)$ 单调递增．相应地， $v ( t ) = h ^ { \prime } ( t ) > 0$

（2）从最高点到入水，运动员的重心处于下降状态，离水面的高度 $h$ 随时间 $t$ 的增加而减小，即 $h \left( t \right)$ 单调递减．相应地， $v ( t ) = h ^ { \prime } ( t ) < 0$

# 思考

我们看到，函数 $h \left( t \right)$ 的单调性与 $\textit { h } ^ { \prime } \left( t \right)$ 的正负有内在联系，那么，我们能否由$h ^ { \prime } ( t )$ 的正负来判断函数 $h \left( t \right)$ 的单调性呢？

对于上述跳水问题，可以发现：

当 $t \in ( 0 , \ a )$ 时， $h ^ { \prime } ( t ) > 0$ ，函数 $h \left( t \right)$ 的图象是“上升”的，函数 $h \left( t \right)$ 在（0，a）内单调递增；

当 $t \in ( a , \ b )$ 时， $h ^ { \prime } \left( t \right) < 0$ ，函数 $h \left( t \right)$ 的图象是“下降”的，函数 $h \left( t \right)$ 在（a，b)内单调递减.

这种情况是否具有一般性呢？

# 观察

观察下面一些函数的图象（图5.3-2)，探讨函数的单调性与导数的正负的关系.

![](images/a45145e5c7daf660c34eee953f4a937ff73f4525bcc730fe90f0e9cdf5526755.jpg)  
图5.3-2

如图5.3-3，导数 $f ^ { \prime } ( x _ { 0 } )$ 表示函数 $y = f ( x )$ 的图象在点$( \boldsymbol { x } _ { 0 }$ ， $f ( x _ { 0 } )$ ）处的切线的斜率．可以发现：

在 ${ \boldsymbol { x } } = { \boldsymbol { x } } _ { 0 }$ 处， $f ^ { \prime } ( x _ { 0 } ) > 0$ ，切线是“左下右上”的上升式，函数 $f ( x )$ 的图象也是上升的，函数 $f ( x )$ 在 ${ \mathcal { x } } = { \mathcal { x } } _ { 0 }$ 附近单调递增；

![](images/8d473d302cd34f3825f5151f55323107e545e42f197eaa1289a92474be71b85d.jpg)  
图5.3-3

在 ${ \boldsymbol { x } } = { \boldsymbol { x } } _ { 1 }$ 处， $f ^ { \prime } ( x _ { 1 } ) { < } 0$ ，切线是“左上右下”的下降式，函数 $f ( x )$ 的图象也是下降的，函数 $f ( x )$ 在 $x = x _ { 1 }$ 附近单调递减.

一般地，函数 $f ( x )$ 的单调性与导函数 $f ^ { \prime } ( x )$ 的正负之间具有如下的关系：

#

在某个区间（α， $^ { b }$ ）内，如果 $f ^ { \prime } \left( x \right) > 0$ ，那么函数$y = f ( x )$ 在区间（α， $^ { b }$ ）内单调递增;

如果在某个区间上恒有 $\boldsymbol { f } ^ { \prime } \left( \boldsymbol { x } \right) = 0$ ，那么函数$f ( x )$ 有什么特性？

在某个区间（α， $^ { b }$ ）内，如果 $f ^ { \prime } \left( x \right) < 0$ ，那么函数$y { = } f ( x )$ 在区间（α， $b$ ）内单调递减.

例1利用导数判断下列函数的单调性：

（1） $f ( x ) = x ^ { 3 } + 3 x$ (2） $f ( x ) = \sin x - x , x \in ( 0 , \pi ) ;$ (3）f（x）=x-1.

解：（1）因为 $f ( x ) = x ^ { 3 } + 3 x$ ，所以

$$
f ^ { \prime } ( x ) { = } 3 x ^ { 2 } + 3 { = } 3 ( x ^ { 2 } + 1 ) > 0 .
$$

所以，函数 $f ( x ) = x ^ { 3 } + 3 x$ 在 $\mathbf { R }$ 上单调递增，如图5.3-4(1）所示.

![](images/7c80d4f34565ddc8d569f30b81a3aaa723c508a6d1b597a6e6183ccf72425066.jpg)  
图5.3-4

（2）因为 $f ( x ) = \sin x - x$ ， $x \in ( 0 , \ \pi )$ ，所以

$$
f ^ { \prime } ( x ) = \cos x - 1 < 0 .
$$

所以，函数 $f ( x ) = \sin x - x$ 在 $( 0 , \ \pi )$ 内单调递减，如图5.3-4（2）所示.

（3）因为f(x)=1-1， $x \in ( - \infty , \ 0 ) \bigcup ( 0 , \ + \infty )$ ，所以

$$
f ^ { \prime } ( x ) { = } \frac { 1 } { x ^ { 2 } } { > } 0 .
$$

所以，函数 $f ( x ) { = } 1 { - } { \frac { 1 } { x } }$ 在区间 $( - \infty , 0 )$ 和（0， $+ \infty )$ 上单调递增，如图5.3-4（3）所示.

例2已知导函数 $f ^ { \prime } ( x )$ 的下列信息：

当 $1 { < x < } 4$ 时， $f ^ { \prime } ( x ) { > } 0$ 当 $x { < } 1$ ，或 $x > 4$ 时， $f ^ { \prime } ( x ) { < } 0$ 当 $x = 1$ ，或 $x = 4$ 时， $f ^ { \prime } ( x ) { = } 0$

试画出函数 $f ( x )$ 图象的大致形状.

解：当 $1 { < x < } 4$ 时， $f ^ { \prime } ( x ) { > } 0$ ，可知 $f ( x )$ 在区间(1，4)内单调递增；

当 $x < 1$ ，或 $x > 4$ 时， $f ^ { \prime } \left( x \right) < 0$ ，可知 $f \left( x \right)$ 在区间（-，1）和（4， $+ \infty ,$ ）上都单调递减；

当 $x = 1$ ，或 $x = 4$ 时， $f ^ { \prime } ( x ) = 0$ ，这两点比较特殊，我们称它们为“稳定点”

综上，函数 $f ( x )$ 图象的大致形状如图5.3-5所示.

![](images/0338ea3d8be9e20f956e63a6cb1a50f5e171054c077049b621c00691ceb50910.jpg)  
图5.3-5

# 思考

请同学们回顾一下函数单调性的定义，并思考在某个区间上单调的函数 $y { = } f ( x )$ 的平均变化率的几何意义与 $f ^ { \prime } ( x )$ 的正负的关系.

# 练习

1.判断下列函数的单调性：

（1） $f ( x ) = x ^ { 2 } - 2 x + 4 ;$ (2） $f ( x ) { \stackrel { } { = } } \mathrm { e } ^ { x } - x .$

2.利用导数讨论二次函数 $f ( x ) = a x ^ { 2 } + b x + c$ 的单调区间.

3.函数 $y = f ( x )$ 的图象如图所示，试画出函数 $y = f ^ { \prime } ( x )$ 在区间（0，b）内图象的大致形状.

![](images/b1a4114d70b66781ec7ba9497bace1b49db28581681df2f1ee649c1aa14a41a1.jpg)  
（第3题）

形如 $f ( x ) = a x ^ { 3 } + b x ^ { 2 } + c x + d$ $a \neq 0$ 的函数应用广泛，下面我们利用导数来研究这类函数的单调性. ， R

例3求函数 $f ( x ) = { \frac { 1 } { 3 } } x ^ { 3 } - { \frac { 1 } { 2 } } x ^ { 2 } - 2 x + 1$ 的单调区间.

解：函数 $f ( x ) = { \frac { 1 } { 3 } } x ^ { 3 } - { \frac { 1 } { 2 } } x ^ { 2 } - 2 x + 1$ 的定义域为R.对 $f ( x )$ 求导数，得

$$
f ^ { \prime } ( x ) = x ^ { 2 } - x - 2 = ( x + 1 ) ( x - 2 ) .
$$

令 $f ^ { \prime } ( x ) { = } 0$ ，解得

$x = - 1$ 和 $x = 2$ 把函数定义域划分成三个区间， $f ^ { \prime } ( x )$ 在各区间上的正负，以及$f ( x )$ 的单调性如表5.3-1所示.

表5.3-1  

<html><body><table><tr><td>x</td><td>（-8，-1）</td><td>-1</td><td>（-1，2）</td><td>2</td><td>（2，+）</td></tr><tr><td>f&#x27;(x)</td><td>+</td><td>0</td><td></td><td>0</td><td>+</td></tr><tr><td>f(x)</td><td>单调递增</td><td>f（-1）= 13-6</td><td>单调递减</td><td>f（2）=-</td><td>单调递增</td></tr></table></body></html>

所以， $f ( x )$ 在（-8，-1)和(2， $+ \infty )$ 上单调递增，在（一1，2)内单调递减，如图5.3-6所示.

![](images/238ca5c0ed25c24e041e138258973f601afc54142cc6fbe02a4733bcceb21feb.jpg)  
图5.3-6

#

如果不用导数的方法，直接运用单调性的定义，你如何求解本题？运算过程麻烦吗？你有什么体会？

一般情况下，我们可以通过如下步骤判断函数 $y = f ( x )$ 的单调性：

第1步，确定函数的定义域；

第2步，求出导数 $f ^ { \prime } ( x )$ 的零点；

第3步，用 $f ^ { \prime } ( x )$ 的零点将 $f ( x )$ 的定义域划分为若干个区间，列表给出 $f ^ { \prime } ( x )$ 在各区间上的正负，由此得出函数 $y = f ( x )$ 在定义域内的单调性.

# 探究

研究对数函数 $y = \ln x$ 与幂函数 $y = x ^ { 3 }$ 在区间（0， $+ \infty )$ ）上增长快慢的情况.

对数函数 $y { = } \ln x$ 的导数为 $y ^ { \prime } { = } \frac { 1 } { x } { > } 0 \ ( x { \in } ( 0 , \ + \infty ) )$ ，所以 $y = \ln x$ 在区间（0， $+ \infty )$ 上单调递增．当 $_ { \mathcal { X } }$ 越来越大时， $y ^ { \prime } { = } \frac { 1 } { x }$ 越来越小，所以函数 $y = \ln x$ 递增得越来越慢，图 象上升得越来越“平缓”（如图5.3-7（1））.

![](images/b970c3163c9102dc6e662874956f9c4a9c0c682fbd0d52aa8c088699d8a2f3ba.jpg)  
图5.3-7

幂函数 $y = x ^ { 3 }$ 的导数为 $y ^ { \prime } { = } 3 x ^ { 2 } { > } 0$ $x \in ( 0 , \ + \infty ) )$ ，所以 $y = x ^ { 3 }$ 在区间（0， $+ \infty )$ 上单调递增.当 $_ { x }$ 越来越大时， $y ^ { \prime } { = } 3 x ^ { 2 }$ 越来越大，函数 $y = x ^ { 3 }$ 递增得越来越快，图象上 升得越来越“陡峭”（如图5.3-7（2））.

一般地，如果一个函数在某一范围内导数的绝对值较大，那么函数在这个范围内变化得较快，这时函数的图象就比较“陡峭”（向上或向下）；反之，函数在这个范围内变化得较慢，函数的图象就比较“平缓”

例4设 $x { > } 0$ ， $f ( x ) { = } \ln x$ ， $g \left( x \right) = 1 - \frac { 1 } { x }$ ， 两个函数的图象如图5.3-8所示．判断 $f ( x )$ ， $g \left( x \right)$ 的图象与 $C _ { 1 }$ ， $C _ { 2 }$ 之间的对应关系.

解：因为 $f ( x ) { = } \ln x$ ， $g \left( x \right) = 1 - \frac { 1 } { x }$ 所以

$$
f ^ { \prime } ( x ) ~ { = } \frac { 1 } { x } , ~ g ^ { \prime } ( x ) ~ { = } \frac { 1 } { x ^ { 2 } } .
$$

当 $x = 1$ 时， $f ^ { \prime } ( x ) { = } g ^ { \prime } ( x ) { = } 1$

当 $0 { < } x { < } 1$ 时， $g ^ { \prime } ( x ) > f ^ { \prime } ( x ) > 1$ ：

当 $x { > } 1$ 时， $0 { < } g ^ { \prime } ( x ) { < } f ^ { \prime } ( x ) { < } 1 .$

所以， $f ( x )$ ， $g \left( x \right)$ 在 $( 0 , \ + \infty )$ 上都是增函数．在区间（0，1）内， $g ( x )$ 的图象比 $f ( x )$ 的图象要“陡峭”；在区间（1， $+ \infty )$ 上， $g ( x )$ 的图象比 $f ( x )$ 的图象要“平缓”

所以， $f ( x )$ ， $g ( x )$ 的图象依次是图5.3-8中的 $C _ { 2 }$ ， $C _ { 1 }$

# 练习

1.判断下列函数的单调性，并求出单调区间：

（1） $f ( x ) = 3 x - x ^ { 3 } ;$ (2） $f ( x ) = x ^ { 3 } - x ^ { 2 } - x .$

2.证明函数 $f ( x ) { = } 2 x ^ { 3 } { - } 6 x ^ { 2 } + 7$ 在区间（0，2）内单调递减.

3.函数 $y = f ^ { \prime } ( x )$ 的图象如图所示，试画出函数 $y = f ( x )$ 图象的大致形状.

![](images/a5ec602ca75803edd2df43f35adf5943a770ee57a71cff3317880ce64f19df73.jpg)  
图5.3-8

![](images/f3c436b13afb380d18383d3636bac6061a442f1ea040cb20623f6dbdb4da9e7e.jpg)  
（第3题）

# 5.3.2函数的极值与最大（小）值

在用导数研究函数的单调性时，我们发现利用导数的正负可以判断函数的增减，如果函数在某些点处的导数为0，那么在这些点处函数有什么性质呢？

# 1.函数的极值

观察图5.3-9，我们发现，当 $t = a$ 时，跳水运动员距水面的高度最大.那么，函数$h \left( t \right)$ 在此点处的导数是多少呢？此点附近的图象有什么特点？相应地，导数的正负性有什么变化规律？

![](images/d8885fd138a3c1d3dd2a9b2cc166644eea85f1dc3226b8bdfe8fd65d7b7a01ad.jpg)  
图5.3-9

放大 $t = a$ 附近函数 $h \left( t \right)$ 的图象，如图5.3-10.可以看出， $\scriptstyle h ^ { \prime } ( a ) = 0$ ；在 $t = a$ 的附近，当 $t { < } a$ 时，函数 $h \left( t \right)$ 单调递增， $h ^ { \prime } ( t ) > 0$ ；当 $t > a$ 时，函数 $h \left( t \right)$ 单调递减， $h ^ { \prime } ( t ) { < } 0 .$ 这就是说，在 $t { = } a$ 附近，函数值先增（当 $t { < } a$ 时， $h ^ { \prime } ( t ) > 0 )$ 后减（当 $t > a$ 时， $h ^ { \prime } ( t ) { < } 0 )$ 这样，当 $t$ 在 $^ a$ 的附近从小到大经过 $\boldsymbol { a }$ 时， $h ^ { \prime } ( t )$ 先正后负，且 $h ^ { \prime } ( t )$ 连续变化，于是有 $h ^ { \prime } ( a ) = 0$

对于一般的函数 $y = f ( x )$ ，是否也有同样的性质呢？

# 探究

如图5.3-11，函数 $y = f ( x )$ 在 $x = a$ $^ { b }$ $c$ ， $d$ ， $e$ 等点处的函数值与这些点附近的函数值有什么关系？ $y = f ( x )$ 在这些点处的导数值是多少？在这些点附近， $y =$ $f ( x )$ 的导数的正负性有什么规律？

![](images/cc3ea5b16d1227af7fc2219cf7fb041eab3af92fcf7d76fa89d115c6ca561383.jpg)  
图5.3-10  
图5.3-11

以 $x = a$ ， $^ { b }$ 两点为例，可以发现，函数 $y = f ( x )$ 在点 $x = a$ 处的函数值 $f ( a )$ 比它在点 $x = a$ 附近其他点处的函数值都小， $f ^ { \prime } ( a ) = 0$ ；而且在点 $x = a$ 附近的左侧 $f ^ { \prime } ( x ) { < } 0$ ，

右侧 $f ^ { \prime } ( x ) > 0 .$ 类似地，函数 $y { = } f ( x )$ 在点 $x = b$ 处的函数值 $f ( b )$ 比它在点 $x = b$ 附近其他点处的函数值都大， $f ^ { \prime } ( b ) = 0$ ；而且在点 $x = b$ 附近的左侧 $f ^ { \prime } ( x ) { > } 0$ ，右侧 $f ^ { \prime } ( x ) { < } 0$

我们把 $a$ 叫做函数 $y { = } f ( x )$ 的极小值点， $f ( a )$ 叫做函数 $y = f ( x )$ 的极小值； $^ { b }$ 叫做函数 $y { = } f ( x )$ 的极大值点， $f ( b )$ 叫做函数 $y = f ( x )$ 的极大值．极小值点、极大值点统称为极值点，极小值和极大值统称为极值（extremum).

极值反映了函数在某一点附近的大小情况，刻画了函数的局部性质.

例5求函数 $f ( x ) = { \frac { 1 } { 3 } } x ^ { 3 } - 4 x + 4$ 的极值.

解：因为 $f ( x ) = { \frac { 1 } { 3 } } x ^ { 3 } - 4 x + 4$ ，所以

$$
f ^ { \prime } ( x ) { = } x ^ { 2 } { - } 4 { = } ( x { + } 2 ) ( x { - } 2 ) .
$$

令 $f ^ { \prime } ( x ) = 0$ ，解得 $x = - 2$ ，或 $x = 2$

当 $_ { \mathcal { X } }$ 变化时， $f ^ { \prime } ( x )$ ， $f ( x )$ 的变化情况如表5.3-2所示.

表5.3-2  

<html><body><table><tr><td>x</td><td>（-8，-2）</td><td>-2</td><td>(-2，2）</td><td>2</td><td>（2，+∞）</td></tr><tr><td>f&#x27;(x)</td><td>+</td><td>0</td><td></td><td>0</td><td>+</td></tr><tr><td>f(x)</td><td>单调递增</td><td>2</td><td>单调递减</td><td></td><td>单调递增</td></tr></table></body></html>

因此，当 $x = - 2$ 时， $f ( x )$ 有极大值，并且极大值为

$$
f ( - 2 ) = { \frac { 2 8 } { 3 } } ;
$$

当 $x = 2$ 时， $f ( x )$ 有极小值，并且极小值为

$$
f ( 2 ) = - { \frac { 4 } { 3 } } .
$$

#

极大值一定大于极小值吗？

函数 $f ( x ) = { \frac { 1 } { 3 } } x ^ { 3 } - 4 x + 4$ 的图象如图5.3-12所示.

?

![](images/56f593239283dcfd1d6b6b9e1fccefbe8e8b4c2d240e46f137d13e31f81e843b.jpg)  
图5.3-12

# 思考

导数值为0的点一定是函数的极值点吗？

导数值为0的点不一定是函数的极值点，例如，对于函数 $f ( x ) = x ^ { 3 }$ ，我们有 $f ^ { \prime } ( x ) =$ $3 { x } ^ { 2 }$ 虽然 $f ^ { \prime } ( 0 ) { = } 0$ ，但由于无论 $x > 0$ ，还是 $x < 0$ ，恒有 $f ^ { \prime } ( x ) > 0$ ，即函数 $f ( x ) = x ^ { 3 }$ 是增函数，所以0不是函数 $f ( x ) = x ^ { 3 }$ 的极值点，一般地，函数 $y = f ( x )$ 在一点处的导数值为0是函数 $y { = } f ( x )$ 在这点取极值的必要条件，而非充分条件.

一般地，可按如下方法求函数 $y { = } f ( x )$ 的极值：

解方程 $f ^ { \prime } ( x ) { = } 0$ ，当 $f ^ { \prime } ( x _ { 0 } ) { = } 0$ 时：

（1）如果在 $x _ { 0 }$ 附近的左侧 $f ^ { \prime } ( x ) { > } 0$ ，右侧 $f ^ { \prime } ( x ) { < } 0$ ，那么 $f ( x _ { 0 } )$ 是极大值；  
(2）如果在 $x _ { 0 }$ 附近的左侧 $f ^ { \prime } ( x ) { < } 0$ ，右侧 $f ^ { \prime } ( x ) { > } 0$ ，那么 $f ( x _ { 0 } )$ 是极小值.

# 练习

1.函数 $f ( x )$ 的导函数 $y { = } f ^ { \prime } ( x )$ 的图象如图所示，试找出函数 $f ( x )$ 的极值点，并指出哪些是极大值点，哪些是极小值点.

2.求下列函数的极值：

(1） $f ( x ) { = } 6 x ^ { 2 } { - } x { - } 2$ （2） $f ( x ) = x ^ { 3 } - 2 7 x$ （3） $f ( x ) { = } 6 + 1 2 x - x ^ { 3 }$ （4） $f ( x ) = 3 x - x ^ { 3 }$

![](images/4f4b34dbf5151a8c2d03ed96864d2e322ada959b935b9436085c9ce555a5495f.jpg)  
（第1题）

# 2.函数的最大（小）值

我们知道，极值反映的是函数在某一点附近的局部性质，而不是函数在整个定义域内的性质，也就是说，如果 $x _ { 0 }$ 是函数 $y = f ( x )$ 的极大（小）值点，那么在 $x = x _ { 0 }$ 附近找不到比 $f ( x _ { 0 } )$ 更大（小）的值，但是，在解决实际问题或研究函数的性质时，我们往往更关心函数在某个区间上，哪个值最大，哪个值最小．如果 $x _ { 0 }$ 是某个区间上函数 $y = f ( x )$ 的最大（小）值点，那么 $f ( x _ { 0 } )$ 不小（大）于函数 $y { = } f ( x )$ 在此区间上的所有函数值.

图5.3-13是函数 $y = f ( x )$ ， $x \in [ a , b ]$ 的图象，你能找出它的极小值、极大值吗？

![](images/d73fd8ceb3812a2aa47a1fe9096ffaee48f4dcecd45b70294d8a1a7807826cd6.jpg)  
图5.3-13

观察图象，我们发现， $f ( x _ { 1 } )$ ， $f ( x _ { 3 } )$ ， $f ( x _ { 5 } )$ 是函数 $y = f ( x )$ 的极小值， $f ( x _ { 2 } )$ ，$f ( x _ { 4 } )$ ， $f ( x _ { 6 } )$ 是函数 $y { = } f ( x )$ 的极大值.

# 探究

进一步地，你能找出函数 $y = f ( x )$ 在区间 $[ a , b ]$ 上的最小值、最大值吗？

从图5.3-13可以看出，函数 $y = f ( x )$ 在区间 $[ a , b ]$ 上的最小值是 $f ( x _ { 3 } )$ ，最大值是$f ( a )$ ：

在图5.3-14、图5.3-15中，观察 $[ a , b ]$ 上的函数 $y = f ( x )$ 和 $\scriptstyle y = g ( x )$ 的图象，它们在 $[ a , b ]$ 上有最大值、最小值吗？如果有，最大值和最小值分别是什么？

![](images/b46a13f62d58511a66ea4a59ce0bda1d10574b7015394a7d17cd2da892d6d062.jpg)  
图5.3-14

![](images/8705d0c9fb71196a07ed9f2b085edf6e4fa5a4ce6b6930fb63e7762b0d082f86.jpg)  
图5.3-15

一般地，如果在区间 $[ a$ ， $^ { b }$ 上函数 $y = f ( x )$ 的图象是一条连续不断的曲线，那么它必有最大值和最小值.

结合图5.3-14、图5.3-15，以及函数极值中的例子，不难看出，只要把函数 $y =$ $f ( x )$ 的所有极值连同端点的函数值进行比较，就可以求出函数的最大值与最小值.

例6求函数 $f ( x ) = { \frac { 1 } { 3 } } x ^ { 3 } - 4 x + 4$ 在区间[0，3]上的最大值与最小值.

解：由例5可知，在区间[0，3]上，当 $x = 2$ 时，函数$f ( x ) = { \frac { 1 } { 3 } } x ^ { 3 } - 4 x + 4$ 有极小值，并且极小值为 $f ( 2 ) = - { \frac { 4 } { 3 } }$

又由于

$$
f ( 0 ) = 4 , \ f ( 3 ) = 1 ,
$$

所以，函数 $f ( x ) = { \frac { 1 } { 3 } } x ^ { 3 } - 4 x + 4$ 在区间[0，3]上的最大值是4，最小值是 $\frac { 4 } { 3 }$ （

![](images/d6bae9f8b7ad1a964019e3f3bfb9505304621ce926a01c0be1c42b032c509244.jpg)  
图5.3-16

上述结论可以从函数 $f ( x ) = { \frac { 1 } { 3 } } x ^ { 3 } - 4 x + 4$ 在区间[0，3]上的图象（图5.3-16）得到直观验证.

一般地，求函数 $y { = } f ( x )$ 在区间 $[ a , b ]$ 上的最大值与最小值的步骤如下：

（1）求函数 $y { = } f ( x )$ 在区间 $( a , b )$ 内的极值；

（2）将函数 $y { = } f ( x )$ 的各极值与端点处的函数值 $f ( a )$ ， $f ( b )$ 比较，其中最大的一个是最大值，最小的一个是最小值.

观察例4中的图5.3-8，我们发现，当 $x > 0$ 时，

$$
1 - { \frac { 1 } { x } } \leqslant \ln x .
$$

怎样证明这个结论呢？

我们将不等式 $\textcircled{1}$ 转化为

$$
\frac { 1 } { x } - 1 + \ln x \geqslant 0 .
$$

设 $s ( x ) { = } \frac { 1 } { x } { - } 1 { + } \ln x$ ，那么

$$
s ^ { \prime } ( x ) = - \frac { 1 } { x ^ { 2 } } + \frac { 1 } { x } = \frac { x - 1 } { x ^ { 2 } } .
$$

令 $s ^ { \prime } ( x ) { = } 0$ ，解得 $x = 1$

当 $_ { x }$ 变化时， $s ^ { \prime } ( x )$ ， $s ( x )$ 的变化情况如表5.3-3所示.

表5.3-3  

<html><body><table><tr><td>x</td><td>（0，1）</td><td>1</td><td>（1，+∞）</td></tr><tr><td>s(x)</td><td></td><td>0</td><td>+</td></tr><tr><td>s(x)</td><td>单调递减</td><td>0</td><td>单调递增</td></tr></table></body></html>

所以，当 $x = 1$ 时， $s ( x )$ 取得最小值．所以

$$
\begin{array} { r } { s \left( x \right) \geqslant s \left( 1 \right) = 0 , } \end{array}
$$

即

$$
\frac { 1 } { x } - 1 + \ln x \geqslant 0 .
$$

所以，当 $x { > } 0$ 时， $1 - { \frac { 1 } { x } } \leqslant \ln x$

# 练习

1.参考求函数极值的练习，求下列函数在给定区间上的最大值与最小值：

(1) $f ( x ) = 6 x ^ { 2 } - x - 2 , x \in [ 0 , 2 ] ;$ (2） $f ( x ) = x ^ { 3 } - 2 7 x$ ， $x \in [ - 4$ ，4]；3 $f ( x ) = 6 + 1 2 x - x ^ { 3 } , x \in \left[ - { \frac { 1 } { 3 } } , \ 3 \right] ;$ (4） $f ( x ) = 3 x - x ^ { 3 } , \ x \in [ 2 , \ 3 ] .$

2.证明不等式： $x - 1 { \geqslant } \ln x$ ，x∈(0,+）.

下面我们通过实例说明如何利用导数解决与函数相关的问题.

例7给定函数 $f ( x ) = ( x + 1 ) \mathrm { e } ^ { x }$

（1）判断函数 $f ( x )$ 的单调性，并求出 $f ( x )$ 的极值；  
（2）画出函数 $f ( x )$ 的大致图象；  
（3）求出方程 $f ( x ) { = } a$ （ $a \in \mathbf { R } )$ 的解的个数.

解：（1）函数的定义域为R.

$$
{ \begin{array} { r l } & { f ^ { \prime } ( x ) = ( x + 1 ) ^ { \prime } \mathrm { e } ^ { x } + ( x + 1 ) ( \mathrm { e } ^ { x } ) ^ { \prime } } \\ & { \qquad = \mathrm { e } ^ { x } + ( x + 1 ) \mathrm { e } ^ { x } } \\ & { \qquad = ( x + 2 ) \mathrm { e } ^ { x } . } \end{array} }
$$

令 $f ^ { \prime } ( x ) { = } 0$ ，解得 $x = - 2$ $f ^ { \prime } ( x )$ ， $f ( x )$ 的变化情况如表5.3-4所示.

表5.3-4  

<html><body><table><tr><td>x</td><td>（-∞，-2）</td><td>-2</td><td>（-2，+∞0）</td></tr><tr><td>f&#x27;(x)</td><td></td><td>0</td><td>+</td></tr><tr><td>f(x)</td><td>单调递减</td><td>12</td><td>单调递增</td></tr></table></body></html>

所以， $f ( x )$ 在区间（一， $- 2 ,$ ）上单调递减，在区间（－2， $+ \infty$ ）上单调递增.当 $x = - 2$ 时， $f ( x )$ 有极小值 $f ( - 2 ) = - \frac { 1 } { \mathrm { e } ^ { 2 } }$

（2）令 $f ( x ) { = } 0$ ，解得 $x = - 1$   
当 $x { < } - 1$ 时， $f ( x ) { < } 0$ ；当 $x { > } - 1$ 时， $f ( x ) { > } 0$   
所以， $f ( x )$ 的图象经过特殊点 $A \left( - 2 , \ { - } { \frac { 1 } { \mathrm { e } ^ { 2 } } } \right)$ ， ，B(-1,0），C(0，1).  
当 $x {  } { - } \infty$ 时，与一次函数相比，指数函数 $y = \mathrm { e } ^ { - x }$ 呈爆炸性增长，从而 $f ( x ) { = } \frac { x + 1 } { \mathrm { e } ^ { - x } } {  } 0 ;$ 当 $x  + \infty$ 时， $f ( x ) {  } + \infty$ ， $f ^ { \prime } ( x ) {  } + \infty .$ 饭

根据以上信息，我们画出 $f ( x )$ 的大致图象如图5.3-17所示.

![](images/7fa52872720ba1e9483d68faddde238287176d10360a972d2d922a78e3cdf044.jpg)  
图5.3-17

（3）方程 $f ( x ) { = } a$ （ $a \in \mathbf { R } \mathbf { \Psi } ,$ 的解的个数为函数 $y = f ( x )$ 的图象与直线 $y = a$ 的交点个数.

由（1）及图5.3-17可得，当 $x = - 2$ 时， $f ( x )$ 有最小值 $f ( - 2 ) = - { \frac { 1 } { \mathrm { e } ^ { 2 } } } .$ 所以，关于方程 $f ( x ) = a$ （ $a \in \mathbf { R } )$ 的解的个数有如下结论：  
当 $a < - \frac { 1 } { \mathrm { e } ^ { 2 } }$ 时，解为0个；  
当 $a = - \frac { 1 } { \mathrm { e } ^ { 2 } }$ 或 $a \geqslant 0$ 时，解为1个；  
当 $- \frac { 1 } { e ^ { 2 } } < a < 0$ 时，解为2个.

由例7可见，函数 $f ( x )$ 的图象直观地反映了函数 $f ( x )$ 的性质，通常，可以按如下步骤画出函数 $f ( x )$ 的大致图象：

（1）求出函数 $f ( x )$ 的定义域；（2）求导数 $f ^ { \prime } ( x )$ 及函数 $f ^ { \prime } ( x )$ 的零点；（3）用 $f ^ { \prime } ( x )$ 的零点将 $f ( x )$ 的定义域划分为若干个区间，列表给出 $f ^ { \prime }$ （x）在各区间上的正负，并得出 $f ( x )$ 的单调性与极值；

（4）确定 $f ( x )$ 的图象所经过的一些特殊点，以及图象的变化趋势；

（5）画出 $f ( x )$ 的大致图象.

下面我们通过实例介绍导数在解决实际问题中的应用.

问题 饮料瓶大小对饮料公司利润的影响

（1）你是否注意过，市场上等量的小包装的物品一般比大包装的要贵些？你想从数学上知道它的道理吗？ D Y ?

（2）是不是饮料瓶越大，饮料公司的利润越大？

例8 某制造商制造并出售球形瓶装的某种饮料，瓶子的制造成本是 $0 . 8 \pi r ^ { 2 }$ 分，其中 $r$ （单位：cm）是瓶子的半径．已知每出售 $1 ~ \mathrm { m L }$ 的饮料，制造商可获利0.2分，且制造商能制作的瓶子的最大半径为 $6 ~ \mathrm { c m }$

（1）瓶子半径多大时，能使每瓶饮料的利润最大？

（2）瓶子半径多大时，每瓶饮料的利润最小？

解：由题意可知，每瓶饮料的利润是

$$
y = f ( r ) = 0 . 2 \times \frac { 4 } { 3 } \pi r ^ { 3 } - 0 . 8 \pi r ^ { 2 } = 0 . 8 \pi \Big ( \frac { r ^ { 3 } } { 3 } - r ^ { 2 } \Big ) , \ 0 < r \leqslant 6 .
$$

所以

$$
f ^ { \prime } ( r ) { = } 0 . 8 \pi ( r ^ { 2 } { - } 2 r ) .
$$

令 $f ^ { \prime } ( r ) { = } 0$ ，解得 $r = 2$

当 $r \in ( 0 , \ 2 )$ 时， $f ^ { \prime } ( r ) { < } 0$ ；当 $r \in ( 2 , \ 6 )$ 时， $f ^ { \prime } ( r ) > 0$

因此，当半径 $r > 2$ 时， $f ^ { \prime } ( r ) > 0$ ， $f ( r )$ 单调递增，即半径越大，利润越高；当半径$r { < } 2$ 时， $f ^ { \prime } ( r ) { < } 0$ ， $f ( r )$ 单调递减，即半径越大，利润越低.

（1）半径为 $6 ~ \mathrm { c m }$ 时，利润最大.

（2）半径为 $2 \mathrm { \ c m }$ 时，利润最小，这时 $f ( 2 ) < 0$ ，表示此种瓶内饮料的利润还不够瓶子的成本，此时利润是负值.

换一个角度：如果我们不用导数工具，直接从函数$f ( r )$ 的图象（图5.3-18）上观察，你有什么发现？

从图象上容易看出，当 $r = 3$ 时， $f ( 3 ) = 0$ ，即瓶子的半径是 $3 \ \mathrm { c m }$ 时，饮料的利润与饮料瓶的成本恰好相等；当 $r { > } 3$ 时，利润才为正值.

当 $r \in ( 0$ ，2)时， $f ( r )$ 单调递减，你能解释它的实际意义吗？

通过此问题的解决，我们很容易回答开始时的问题.请同学们自己作出回答.

![](images/c2b33d69719d8d17ca73dab00744a175579c344aee50bc66cfd497d2ed76fc86.jpg)  
图5.3-18

# 练习

1.利用函数的单调性，证明下列不等式，并通过函数图象直观验证：

$$
\sin x < x , x \in ( 0 , \pi ) .
$$

2.如图，用铁丝围成一个上面是半圆，下面是矩形的图形，其面积为 $a \mathrm { ~ m ~ } ^ { 2 }$ ，为使所用材料最省，圆的直径应为多少？

![](images/b7d4efb2ea3639068fe86d8079d67ca414d16d148430f03318273a194d80aac7.jpg)  
（第2题）

# 习题5.3

# 复习巩固

1.判断下列函数的单调性，并求出单调区间：

（1） $f ( x ) = - 2 x + 1 ;$ (2） $f ( x ) = x + \cos x , x \in \left( 0 , \ { \frac { \pi } { 2 } } \right) ;$ （3） $f ( x ) { = } 2 x { - } 4 ;$ （4） $f ( x ) = 2 x ^ { 3 } + 4 x$

2.判断下列函数的单调性，并求出单调区间：

(1） $f ( x ) = x ^ { 2 } + 2 x - 4 ;$ (2） $f ( x ) = 2 x ^ { 2 } - 3 x + 3 ;$ （3） $f ( x ) = 3 x + x ^ { 3 }$ （4） $f ( x ) = x ^ { 3 } + x ^ { 2 } - x .$

3.如图，已知汽车在笔直的公路上行驶.

（1）如果 $y = f ( t )$ 表示时刻 $t$ 时汽车与起点的距离，请标出汽车速度等于0的点；（2）如果 $y = f ( t )$ 表示时刻 $t$ 时汽车的速度，那么（1）中标出点的意义是什么？

![](images/b36740ebdce4316e5be641d4cdbb001bf229bf6e37b33716653b68711cc48abc.jpg)  
（第3题）

![](images/b5428fcde1c1a0f39809f61b93b02f18f7afa0821fef1cd20d6651b81e61c0a1.jpg)  
（第4题）

4.导函数 $y { = } f ^ { \prime } ( x )$ 的图象如图所示，在标记的点中，在哪一点处

（1）导函数 $y = f ^ { \prime } ( x )$ 有极大值？（2）导函数 $y { = } f ^ { \prime } ( x )$ 有极小值？（3）函数 $f ( x )$ 有极大值？（4）函数 $f ( x )$ 有极小值？

5.求下列函数的极值：

(1) $f ( x ) = 6 x ^ { 2 } + x + 2 ;$ (2） $f ( x ) { = } x ^ { 3 } - 1 2 x ;$ （3） $f ( x ) = 6 - 1 2 x + x ^ { 3 } ;$ （4） $f ( x ) = 4 8 x - x ^ { 3 } .$

6.参照第5题，求下列函数在给定区间上的最大值与最小值：

（1） $f ( x ) = 6 x ^ { 2 } + x + 2$ ，x∈[-1,1]；(2） $f ( x ) = x ^ { 3 } - 1 2 x , x \in [ - 3 , \ 3 ] ,$ $f ( x ) { = } 6 { - } 1 2 x + x ^ { 3 }$ x∈[-，1]；（4） $f ( x ) = 4 8 x - x ^ { 3 }$ ， $x \in [ - 3$ ，5].

# 综合运用

7.将一条长为 $l$ 的铁丝截成两段，分别弯成两个正方形，要使两个正方形的面积和最小，两段铁丝的长度分别是多少？ R

8.将一个边长为 $^ a$ 的正方形铁片的四角截去四个边长均为 $_ { \mathcal { X } }$ 的小正方形，做成一个无盖方盒.

（1）试把方盒的容积 $V$ 表示为 $_ { x }$ 的函数；

(2） $_ { x }$ 多大时，方盒的容积 $V$ 最大？

9.用测量工具测量某物体的长度，由于工具的精度以及测量技术的原因，测得 $n$ 个数据

$$
a _ { 1 } , a _ { 2 } , a _ { 3 } , \cdots , a _ { n } .
$$

证明：用 $_ n$ 个数据的平均值

$$
x = \frac { 1 } { n } \sum _ { i = 1 } ^ { n } a _ { i }
$$

表示这个物体的长度，能使这 $n$ 个数据的方差

$$
f ( x ) = { \frac { 1 } { n } } \sum _ { i = 1 } ^ { n } { ( x - a _ { i } ) ^ { 2 } }
$$

最小.

10.已知某商品的生产成本 $C$ 与产量 $q$ 之间的关系为 $C { = } 1 0 0 { \ + } 4 q$ ，单价 $\phi$ 与产量 $q$ 之间的关系为$\scriptstyle { p = 2 5 - { \frac { 1 } { 8 } } q }$ 8q.产量q为何值时，利润最大？

11.已知某商品进价为 $a$ 元/件，根据以往经验，当售价是 $b \left( b \geqslant { \frac { 4 } { 3 } } a \right)$ 元/件时，可卖出 $c$ 件市场调查表明，当售价下降 $10 \%$ 时，销量可增加 $40 \%$ ．现决定一次性降价，售价为多少时，可获得最大利润？

12.利用函数的单调性，证明下列不等式，并通过函数图象直观验证：

$$
\mathrm { e } ^ { x } > 1 + x , x \neq 0 ; \qquad ( 2 ) \ln x < x < \mathrm { e } ^ { x } , x > 0 .
$$

# 拓广探索

13.利用信息技术工具，根据给定的 $\boldsymbol { a }$ ， $^ { b }$ ， $c$ ， $d$ 的值，可以画出函数

$$
f ( x ) = a x ^ { 3 } + b x ^ { 2 } + c x + d ( a \neq 0 )
$$

的图象，当 $a = - 4 , \ b = 1 , \ c = 5 , \ d = - 1$ 时， $f ( x )$ 的图象如图所示，改变 $a$ ， $^ { b }$ ， $c$ ， $d$ 的值，观察图象的形状：

（1）你能归纳函数 $f ( x )$ 图象的大致形状吗？它的图象有什么特点？你能从图象上大致估计它的单调区间吗？

（2）运用导数研究它的单调性，并求出相应的单调区间.

![](images/ff394f989d0f16bdffb3dcf574f4ca4429cf0b7d1b346cf6af76945232cfb52a.jpg)  
（第13题）

# 图形技术与函数性质

信息技术发展到今天，图形计算器、计算机软件的图形技术功能已经非常强大，函数作图和分析功能是图形技术的一个重要方面，主要包括：

1.由函数 $f ( x )$ 的解析式直接画出其图象；  
2.移动光标，显示函数 $f ( x )$ 图象上任一点的坐标；  
3.由函数 $f ( x )$ 的解析式及其图象求出其导函数 $f ^ { \prime } ( x )$ ，并画出其图象；  
4.显示并求出经过函数 $f ( x )$ 图象上某点切线的斜率和切线的方程；  
5.求出并显示函数 $f ( x )$ 的零点和极值点；  
6.对函数 $f ( x )$ 图象的局部进行放大和缩小；  
7.求出函数 $f ( x )$ 在定义域内某个闭区间上的最大值与最小值.

![](images/ee84a0d1be05654dac7285d72a3871b21acb67ae89b289587e1ea718218dbc1a.jpg)  
图1、图2展示了上述部分功能.  
图1

![](images/3ce54bdb6043ad73bb5f304d9d2ccb80f250f1f11cbbd5580935c552587877c8.jpg)  
图2

图形计算器和计算机软件提供的函数作图和分析功能，对我们把握函数的性质有重要的帮助：一方面，通过画出函数的图象，对图象进行观察和分析，并作出猜想和发现，从而探讨函数的性质；另一方面，用导数研究函数的性质后，可用图形技术进行直观验证，两者相辅相成.

# 微积分的创立与发展

17世纪中叶，数学史上发生了一件具有划时代意义的重大事件，那就是微积分的诞生.

微积分的创立有深刻的时代背景，从欧洲文艺复兴时期到17世纪上半叶，社会、经济、科学、贸易、航运等的发展，对数学提出了新的要求，紧接着函数概念的引入，微积分应运而生，这是继欧氏几何后数学史上最伟大的创造，微积分的创立不仅是数学思想史上的里程碑，也是科学思想史上的里程碑。

微积分是如何创立的？又是如何发展的？它在数学史和人类社会的发展中起了什么作用？请你按以下要求，查阅与微积分有关的文献，自己选题，写一份研究报告或一篇数学小论文.

# 一、主题

1.微积分创立与发展的过程.  
2.微积分对人类文明的主要贡献。

# 二、实施建议

1.选题：根据个人兴趣，围绕主题，初步确定选题范围.  
2.分组：将相近选题的 $5 \sim 6$ 人分为一个小组，确定一名组长.  
3.分配任务：根据个人的具体情况，经小组共同商议，由组长确定每人的具体任务.  
4.搜集资料：针对具体的研究报告或论文题目，通过网络、书店、图书馆等多种途径搜集素材，包括文字、图片、数据以及音像资料，并记录相关资料。  
5.素材汇总：用研究报告或论文的形式展现小组的实践成果.  
6.在全班范围内进行交流、讨论和总结。

# 三、参考选题

1.微积分创立的背景与过程。2.微积分的完善与发展.3.微积分创立与发展的过程中，作出重要贡献的数学家以及他们的主要贡献.4.微积分在数学思想史和科学思想史上的价值。

# 小结

# 一、本章知识结构

导数的概念 导数的运算 数在函平均速度 瞬时速度抽象 导数的概念 导数的几何意义 导数公式 基本初等函数的 导数的四则运算法则 简单复合函数的导数 函数的单调性 最大（小）值 函数的极值与割线斜率 切线斜率

# 二、回顾与思考

在本章，我们通过丰富的实际背景，经历由平均变化率过渡到瞬时变化率的过程，得到导数的概念及其几何意义，在此基础上给出导数的运算法则，进而运用导数研究函数的单调性、极值和最大（小）值等性质，并利用导数解决简单的实际问题.

导数是关于瞬时变化率的数学表达，客观世界中大量的变化率问题都可以用导数加以刻画，导数的几何意义直观地反映了函数的局部变化情况，学习时应注意体会导数的内涵与思想，并体会极限的思想。

对很多运动变化问题的研究最后都会归结为对各种函数的研究，其中函数的增减，以及增减的范围、增减的快慢等是最基本的问题，导数简明地回答了这些问题：由 $f ^ { \prime } ( x )$ 的正负可知函数 $f ( x )$ 是增还是减，由 $f ^ { \prime } ( x )$ 绝对值的大小可知函数变化得急剧还是平缓，不仅如此，导数也是研究函数极值问题从而是解决优化问题的一种通法，导数定量地刻画了函数的局部变化规律，是研究函数性质的基本工具，利用导数研究函数的性质，思路清晰，步骤明确，既快捷又易于掌握，学习时应通过具体实例感受导数在研究函数和解决实际问题中的作用，体会导数的意义.

请你带着下面的问题，复习一下全章内容吧。

1.平均变化率与瞬时变化率之间有什么内在联系？有人说，“导数就是瞬时变化率”，对此你有什么认识？

2.你能从物理和几何两方面解释导数的意义吗？

3.利用导数定义推导函数 $y { = } f ( x )$ 的导数时，其基本步骤是什么？  
4.导数的四则运算法则是什么？如何求简单复合函数的导数？  
5.利用导数研究函数性质的基本步骤是什么？  
6.通过本章的学习，你对“导数是研究函数性质的基本工具”有什么体会？

# 复习参考题5

# 复习巩固

1.已知点 $P$ 和点 $Q$ 是曲线 $y = x ^ { 2 } - 2 x - 3$ 上的两点，且点 $P$ 的横坐标是1，点 $Q$ 的横坐标是4.求：

（1）割线 $P Q$ 的斜率； （2）点 $P$ 处的切线方程.

2.求下列函数的导数：

$$
{ \begin{array} { l l l l l } { y = 2 x \tan x ; } & { \qquad } & & { \mathrm { ( 2 ) } ~ y = 2 ^ { x } \ln x ; } & { } & { \mathrm { ( 3 ) } ~ y = ( x - 2 ) ^ { 3 } ( 3 x + 1 ) ^ { 2 } ; } \\ { y = { \cfrac { x ^ { 2 } } { ( 2 x + 1 ) ^ { 3 } } } ; } & { } & & { \mathrm { ( 5 ) } ~ y = { \mathrm { e } } ^ { - 2 x + 1 } \cos ( - x ^ { 2 } + x ) ; } & { } & { \mathrm { ( 6 ) } ~ y = { \cfrac { \sin 2 x } { \sqrt { x } } } . } \end{array} }
$$

3.已知函数 $y = f ( x )$ 的图象是下列四个图象之一，且其导函数 $y { = } f ^ { \prime } ( x )$ 的图象如图所示，则该函数的图象是（）.

![](images/01874f6a092ba1d4a6d9861d71a6b065d05b008802ef671aecf1f3c04493d475.jpg)

4.求下列曲线在给定点处的切线方程：

$\scriptstyle { y = x - { \frac { 1 } { x } } }$ 1，6； (2) $y = { \frac { \mathrm { e } ^ { 2 x - 1 } } { x ^ { 2 } } } , \ ( { \frac { 1 } { 2 } } , \ 4 ) .$

5.一个距地心距离为 $r$ ，质量为 $m$ 的人造卫星，与地球之间的万有引力 $F$ 由公式 $F = { \frac { G M m } { r ^ { 2 } } }$ 给出，其中 $M$ 为地球质量， $G$ 为引力常量．求 $F$ 对于 $r$ 的瞬时变化率.

6.一杯 $8 0 ~ ^ { \circ } \mathrm { C }$ 的热红茶置于 $2 0 ~ ^ { \circ } C$ 的房间里，它的温度会逐渐下降，温度 $T$ （单位： $\mathcal { C }$ ）与时间 $t$ （单位： $\operatorname* { m i n }$ 之间的关系由函数 $T = f ( t )$ 给出.

（1）判断 $f ^ { \prime } ( t )$ 的正负，并说明理由.  
（2） $f ^ { \prime } ( 3 ) = - 4$ 的实际意义是什么？如果 $f ( 3 ) = 6 5 ^ { \circ } \mathrm { C }$ ，你能画出函数 $f ( t )$ 在 $t = 3$ 时图象的大致形状吗？

7.求函数 $f ( x ) = \sqrt [ 3 ] { x ^ { 2 } }$ 的单调区间.

8.已知函数 $f ( x ) = x ^ { 2 } + p x + q$ ，试确定 $\phi$ $q$ 的值，使得当 $x = 1$ 时，$f ( x )$ 有最小值4.

9.已知函数 $f ( x ) { = } x ( x { - } c ) ^ { 2 }$ 在 $x = 2$ 处有极大值，求 $c$ 的值.

10.如图，过点 $P ( 1 , \ 1 )$ 作直线 $A B$ ，分别与 $x$ 轴的正半轴、 $_ y$ 轴的正半轴交于点 $A$ ， $B$ 当直线 $A B$ 在什么位置时， $\triangle A O B$ 的面积最小？最小面积是多少？

![](images/4b1eff5293dc2e7950a68ab0eaf718b18c597489d9cd496672b0a4222aef967c.jpg)  
（第10题）

# 综合运用

11.如图，直线l和圆 $P$ ，当 $l$ 从 $l _ { 0 }$ 开始在平面上按逆时针方向绕点 $O$ 匀速转动（转动角度不超过 $9 0 ^ { \circ }$ ）时，它扫过的圆内阴影部分的面积 $S$ 是时间 $t$ 的函数.这个函数的图象大致是（）.

![](images/9de298cd2664eab3830dd7a300b177d130d2c443c26f3ffeab600a6c31daeb8e.jpg)  
（第11题）

![](images/c5a512abe4fe8a0e4ff89ab059158cf82fb6e18084f0eba1116e3f9a577bbe52.jpg)

12.当室内的有毒细菌开始增加时，就要使用杀菌剂．刚开始使用的时候，细菌数量还会继续增加，随着时间的增加，它增加的幅度逐渐变小，到一定时间，细菌数量开始减少，已知使用杀菌剂 $^ { \mathrm { ~ t ~ h ~ } }$ 后的细菌数量为 $b ( t ) = 1 0 ^ { 5 } + 1 0 ^ { 4 } t - 1 0 ^ { 3 } t ^ { 2 }$

（1）求细菌数量在 $t = 5$ 与 $t = 1 0$ 时的瞬时变化率.

（2）细菌数量在哪段时间增加，在哪段时间减少？为什么？

13.已知曲线 $y = x + \ln x$ 在点（1，1）处的切线与曲线 $y = a x ^ { 2 } + ( 2 a + 3 ) x + 1$ 只有一个公共点，求 $a$ 的值.

14.用总长 $1 4 . 8 \mathrm { ~ m ~ }$ 的钢条制作一个长方体容器的框架，如果所制容器底面一边的长比另一边的长多 $0 . 5 \textrm { m }$ ，那么高为多少时容器的容积最大？最大容积是多少？

15.用半径为 $R$ 的圆形铁皮剪出一个圆心角为 $\alpha$ 的扇形，制成一个圆锥形容器.扇形的圆心角 $\alpha$ 为多大时，容器的容积最大？

# 拓广探索

16.已知A，B两地的距离是 $1 3 0 ~ \mathrm { k m } ,$ 根据交通法规，两地之间的公路车速应限制在 $5 0 { \sim } 1 0 0 \ \mathrm { k m / h } .$ 假设油价是7元/L，以 $x \ \mathrm { k m / h }$ 的速度行驶时，汽车的耗油率为 $\left( 3 + \frac { x ^ { 2 } } { 3 6 0 } \right) \mathrm { L } / \mathrm { h }$ ，司机每小时的工资是35元．那么最经济的车速是多少（精确到 $\mathrm { { 1 ~ k m / h } ) }$ ？如果不考虑其他费用，这次行车的总费用是多少（精确到1元）？

17.作函数 $y = { \frac { \operatorname { e } ^ { x } ( 2 x - 1 ) } { x - 1 } }$ 的大致图象.

18.已知函数 $f ( x ) { = } \mathrm { e } ^ { x } { - } \ln ( x { + } m )$ .当 $m { \leqslant } 2$ 时，求证 $f ( x ) { > } 0$

19.已知函数 $f ( x ) = a \mathrm { e } ^ { 2 x } + ( a - 2 ) \mathrm { e } ^ { x } - x$

（1）讨论 $f ( x )$ 的单调性； （2）若 $f ( x )$ 有两个零点，求 $a$ 的取值范围.

<html><body><table><tr><td>中文</td><td>英文 页码</td></tr><tr><td>数列</td><td>sequence of number 3</td></tr><tr><td>等差数列</td><td>arithmetic progression 13</td></tr><tr><td>公差</td><td>common difference 13</td></tr><tr><td>等差中项</td><td>arithmetic mean 13</td></tr><tr><td>等比数列</td><td>geometric progression 28</td></tr><tr><td>公比</td><td>common ratio 28</td></tr><tr><td>等比中项</td><td>geometric mean 28</td></tr><tr><td>数学归纳法</td><td>mathematical induction 46</td></tr><tr><td>瞬时速度</td><td>instantaneous velocity 60</td></tr><tr><td>导数</td><td>derivative 65</td></tr><tr><td>切线</td><td>tangent line 67</td></tr><tr><td>导函数</td><td>derived function 69</td></tr><tr><td>复合函数</td><td>composite function 79</td></tr><tr><td>极值</td><td>extremum 91</td></tr></table></body></html>

# 后 记

本册教科书是人民教育出版社课程教材研究所中学数学课程教材研究开发中心依据教育部《普通高中数学课程标准（2017年版）》编写的，经国家教材委员会2019年审查通过.

本册教科书的编写，集中反映了我国十余年来普通高中课程改革的成果，吸取了2004年版《普通高中课程标准实验教科书·数学（A版）》的编写经验，凝聚了参与课改实验的教育专家、学科专家、教材编写专家、教研人员和一线教师，以及教材设计装帧专家的集体智慧.本书插图绘制为王俊宏，为本书提供照片的有东方IC图片网（第39，58页各一张图）等.

我们感谢2004年版《普通高中课程标准实验教科书·数学（A版）》的主编刘绍学，副主编钱珮玲、章建跃，以及所有编写人员.我们感谢所有对教科书的编写、出版、试教等提供过帮助与支持的同仁和社会各界朋友。

本册教科书出版之前，我们通过多种渠道与教科书选用作品（包括照片、画作）的作者进行了联系，得到了他们的大力支持.对此，我们表示衷心的感谢！恳请未联系到的作者与我们联系，以便及时支付稿酬.

我们真诚地希望广大教师、学生及家长在使用本册教科书的过程中提出宝贵意见.我们将集思广益，不断修订，使教科书趋于完善.

联系方式电话：010-58758866电子邮箱：jcfk $@$ pep.com.cn