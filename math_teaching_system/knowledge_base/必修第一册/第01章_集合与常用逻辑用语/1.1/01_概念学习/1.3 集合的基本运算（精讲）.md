---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 31
source_file: 1.3 集合的基本运算（精讲）（解析版）.md
title: 1.3 集合的基本运算（精讲）（解析版）
type: concept
---

# 1.3 集合的基本运算（精讲）

![](images/2fa5646ef4891ffeb3b9d8d21c5e8617a258275ab899a25f23e3e9e90c1c7dcf_1.jpg)

# 考点展现

# 一．并集

1.自然语言：由所有属于集合 $A$ 或属于集合 $B$ 的元素组成的集合，称为集合 $A$ 与 $B$ 的并集.

2.符号语言： $A \cup B { = } \{ x | x { \in } A$ ，或 $x { \in } B \}$ .  
3.图形语言：如图所示.

![](images/d63f52f289ba28d428ef68289a4ff38b2e30e594622ad5c3c7a8f43d8768ff45_1.jpg)

4.运算性质： $A \cup B { = } B \cup A$ ， $A \subseteq A \cup B$ ， $B \subseteq A \cup B$ ， $A \cup A { = } A$ ， $A \cup \varnothing = \varnothing \cup A { = } A .$ .  
如果 $A \subseteq B$ ，则 $A \cup B { = } B$ ，反之也成立.

5.概念理解

并集中的“或”指的是只要满足其中一个条件即可，符号语言“ $\textstyle { \mathfrak { s } } _ { x \in A }$ ，或 $x \in B ^ { \prime }$ 包含三种情况：“ $\iota _ { x \in A }$ 但 $x \notin B ^ { \prime }$ ，“ $\dot { x } \in B$ 但 $x \notin A ^ { \prime \prime }$ ，“ $\mathfrak { x } \in A$ 且 $x \in B ^ { \prime }$ ，如下图所示：

![](images/2c11d3e8e4690e6b447fdfc895eed5e101d77a40fffa7515afacb783138189e7_1.jpg)

$x \in A$ ,但xB $x \in A$ ，且 $x \in B$ $x \in B$ ，但 $x$ A

# 二．交集

1.自然语言：由所有属于集合 $A$ 且属于集合 $B$ 的元素组成的集合，称为 $A$ 与 $B$ 的交集.

2.符号语言： $A \cap B = \{ x | x \in A$ 且 $x { \in } B \}$ .

3. 图形语言：如图所示.

![](images/4b46c02538602727ae83d687559cb3a95c65aabe7f8e85f0a9b067855d1fa2e9_1.jpg)

4.运算性质： $A \cap B { = } B \cap A$ ， $A \cap B { \subseteq } A$ ， $A \cap B { \subseteq } B$ ， $A \cap A = A$ ， $A \cap \emptyset = \emptyset \cap A = \emptyset$ .如果 $A \subseteq B$ ，则 $A \cap B { = } A$ ，反之也成立.

5.概念理解

（1）交集概念中的“且”即“同时”的意思，两个集合交集中的元素必须同时是两个集合中的元素.  
（2）两个集合若没有公共元素，则二者的交集为 $\emptyset$ .

# 三．全集、补集的概念

1.全集

$\textcircled{1}$ 定义：如果一个集合含有所研究问题中涉及的所有元素，那么就称这个集合为全集.  
$\textcircled{2}$ 记法：全集通常记作 $U .$ .

2.补集

（1）文字语言：对于一个集合 $A$ ，由全集 $U$ 中不属于集合A的所有元素组成的集合称为集合 A相对于全集 $U$ 的补集，记作 $\complement _ { U } A$ （2）符号语言： $\complement _ { U } A = \{ x | x \in U$ ，且 $x { \notin A } \}$ （3）图形语言

![](images/f8eec65e552f8d96376cb0813861263c6d6a0c344df0a069e5821d617bd69639_1.jpg)

(4)符号 $\mathsf { C } _ { U } A$ 有三层意思：  
$\textcircled{1} A$ 是 $U$ 的子集，即 $A \subseteq U$ ；  
$\textcircled { 2 } \mathsf { C } _ { U } A$ 表示一个集合，且 $( \complement _ { U } A ) { \subseteq } U$ ；  
$\textcircled{3} \mathsf { C } _ { U } A$ 是 $U$ 中不属于 $A$ 的所有元素组成的集合，即 $\complement _ { U } A = \{ x | x \in U$ ，且 $x { \notin A } \}$ ．

# 一．求集合并集的两种方法

1.定义法：对于元素个数有限的集合，可直接根据集合的并集定义求解，但要注意集合中元素的互异性

2.数形结合法：对于元素个数无限的集合，进行并集运算时，可借助数轴求解．注意两个集合的并集等于两个集合在数轴上的相应图形所覆盖的全部范围，建立不等式时，要注意端点值是否能取到，最好是把端点值代入题目验证

# 二．求两个集合的交集的方法

1.对于元素个数有限的集合，可逐个挑出两个集合的相同元素，但要注意集合中元素的互异性2.对于元素个数无限的集合，可借助数轴求交集，两集合的交集对应的是表示两集合的相应图形所覆盖的公共区域

3.求集合A∩B的常见类型

(1)若 A，B 的元素是方程的根，则应先解方程求出方程的根后，再求两集合的交集.(2)若 A，B 的元素是有序数对，则 A∩B 是指两个方程组成的方程组的解集，交集是点集.(3)若 A，B 是无限数集，可以利用数轴来求解，但要注意利用数轴表示不等式时，含有端点的值用实心点表示，不含有端点的值用空心圈表示.

# 三．求补集的方法

1.列举法表示：从全集 $U$ 中去掉属于集合 $A$ 的所有元素后，由所有余下的元素组成的集合.

2.由不等式构成的无限集表示：借助数轴，取全集 $U$ 中集合 $A$ 以外的所有元素组成的集合.

# 四．利用集合交集、并集的性质

1.依据： $A \cap B { = } A \Leftrightarrow A { \subseteq } B$ ， $A \cup B { = } A \Leftrightarrow B { \subseteq } A .$ .

2.关注点：当集合 $A \subseteq B$ 时，若集合 $A$ 不确定，运算时要考虑 $A = \emptyset$ 的情况，否则易漏解.

3.求解与不等式有关的集合问题的方法

解决与不等式有关的集合问题时，画数轴(这也是集合的图形语言的常用表示方式)可以使问题变得形象直观，要注意求解时端点的值是否能取到.

4.要进行集合运算时，首先必须熟练掌握基本运算法则，可按照如下口诀进行：交集元素仔细找，属于 A 且属于 $B$ ；  
并集元素勿遗漏，切忌重复仅取一；  
全集 $U$ 是大范围，去掉 $U$ 中 $A$ 元素，剩余元素成补集

# 考法解读

# 考点一 并集

【例1-1】（2023浙江省宁波市）已知集合 $A = \{ 0 , 1 , 2 \}$ ， $B = \{ - 1 , 0 \}$ ，则 $A \cup B = \mathrm { ~ ( ~ ) ~ }$

A． B． C． D．

【答案】D

【解析】因为 $A = \{ 0 , 1 , 2 \}$ $B = \{ - 1 , 0 \}$ 所以 $A \cup B = \left\{ - 1 , 0 , 1 , 2 \right\}$ .故选：D

【例 1-2】（2022 秋·江西景德镇）集合 $A = \left\{ x | 0 < x < 8 \right\} , B = \left\{ x | \frac { 1 } { 2 } < x \leq 1 0 \right\}$ ，则 $A \cup B = \left( \begin{array} { l l } { \mathbf { \Sigma } } & { \mathbf { \Sigma } } \end{array} \right)$ ）

A． B $\left\{ x | 0 < x \le 1 0 \right\}$ C． $\left\{ x \big | \frac { 1 } { 2 } \leq x < 8 \right\}$ D $\left\{ x \big \vert \frac { 1 } { 2 } < x \leq 1 0 \right\}$

【答案】B

【解析】因为 ， $A = \left\{ x | 0 < x < 8 \right\} B = \left\{ x | \frac { 1 } { 2 } < x \leq 1 0 \right\}$ ，所以 $_ { A \cup B } = ^ { \left\{ x \middle | 0 < x \leq 1 0 \right\} }$ .故选：B.

# 【一隅三反】

1．（2023 春·广西）已知集合 ， ，则 $A \cup B$ 中的元素个数为（ ）

A．3 B．4 C．5 D．6

【答案】B

【解析】由题设 $B = \left\{ 1 \right\}$ ，所以 $A \cup B = \{ - 1 , 0 , 1 , 2 \}$ ，故其中元素共有4 个．故选：B

2．（2023·安徽）设集合 ， ， 则 $A \cup B = \left( \begin{array} { l l } { \mathbf { \Sigma } } & { \mathbf { \Sigma } } \end{array} \right)$

A． $( - \infty , 4 )$ C． D． B．

【答案】D

【解析】集合 $A = \left\{ x | - 1 \leq x \leq 3 \right\} , B = \left\{ x | 0 < x < 4 \right\}$ ，则 故选：D.

3．（2023·辽宁大连）已知集合 $M , N$ ，满足 $M = M \cup N$ ，则（ ）

A． $M \subseteq N$ B． $N \subseteq M$ C． $N \in M$ D． $M \in N$

【答案】B

【解析】集合与集合的关系不能用元素与集合的关系来表示，故C、D 错误，而 $M = M \cup N$ 说明 $N$ 中元素都在集合 $M$ 中，故 $N \subseteq M$ .故选：B.

# 考点二 交集

【例 2-1】（2023 云南省）设集合 $A = \left\{ x | 0 < x < 4 \right\}$ ， $B = \left\{ 2 , 3 , 4 , 5 , 6 \right\}$ ，则 $A \cap B = \mathbf { \Phi } _ { ( } \quad )$

A． B． C． D

【答案】A

【解析】由题意可得： $A \cap B = \left\{ 2 , 3 \right\}$ .故选：A

【例 2-2】（2023 四川省成都市）已知集合 $A = \left\{ x | - 2 < x < 2 \right\}$ ， $B = \left\{ x | x > { \sqrt { 3 } } \right\}$ ，则 $A \cap B = { \textrm { ( ) } }$

A． B． C． D．

【答案】C

【解析】 $A = \left\{ x | - 2 < x < 2 \right\} \quad B = \left\{ x { \big | } x > { \sqrt { 3 } } \right\}$ 则 $A \cap B = \left\{ x { \big | } { \sqrt { 3 } } < x < 2 \right\}$ .故选：C

# 【一隅三反】

1．（2023 春·北京海淀）已知集合 ， ， 则 $A \cap B = { \mathrm { ~ ( ~ ) ~ } }$

A． B． {-10] C． D．

【答案】B

【解析】因为集合 $A = \{ - 1 , 0 , 1 \}$ ， $B = \left\{ \left. x \right| - 1 \leq x < 1 \right\}$ 则 $A \cap B = \left\{ - 1 , 0 \right\}$ .故选：B.

2．（2023 春·浙江温州）设集合 $A = \{ 0 , 1 , 2 , 3 \}$ ， $B = \left\{ 2 , 3 , 4 , 5 \right\}$ ，则 $A \cap B = \mathbf { \Phi } _ { ( } \quad )$

A． B． C． D．

【答案】B

【解析】由题， $A \cap B = \left\{ 2 , 3 \right\}$ .故选：B

3．（2022 秋·江西赣州·）已知集合 $A = \{ x { | { ( x - 2 ) } { ( 2 x + 1 ) } \le 0 \} } , B$ $B = \left\{ { x } | { x } \mathrm { { < } } 1 \right\}$ ，则 $A \cap B = { \textrm { ( ) } }$

A． $\left\{ x \bigg \vert - \frac { 1 } { 2 } \leq x \leq 1 \right\}$ B $\left\{ x \bigg \vert - \frac { 1 } { 2 } \leq x < 1 \right\}$ C． $\left\{ x | 1 \leq x \leq 2 \right\}$ $\left. \begin{array} { l l } { \boxed { x } \boxed { x \leq - \frac { 1 } { 2 } } } \end{array} \right\}$

【答案】B

【解析】由题意得， $A = \{ x | ( x - 2 ) ( 2 x + 1 ) \le 0 \} = \{ x \bigg | - \frac 1 2 \leq x \le 2 \} \quad B = \{ x | x < 1 \} \quad \quad \quad \ A \cap B = \{ x \bigg | - \frac 1 2 \leq x < 1 \}$ 故选：B

# 考点三 补集、全集

【例 3】（2023 贵州省）已知集合 ， $B = \left\{ x | 3 \leq x < 4 \right\}$ ，则 （ ）

A． B C $\mathsf { \Sigma } _ { \mathsf { D } _ { \cdot } }  \quad \left\{ x \vert 2 \leq x < 3 \atop \exists \underset { \prime } { \rtimes } 4 < x \leq 5 \right\}$

【答案】B

【解析】因为集合 $A = \left\{ x | 2 \leq x \leq 5 \right\} \quad \ B = \left\{ x | 3 \leq x < 4 \right\} \quad \quad \Leftrightarrow B = \left\{ x | 2 \leq x < 3 \quad 4 \leq x \leq 5 \right\}$ .故选：B.

# 【一隅三反】

1．（2023·天津河北）设全集 $U = \left\{ - 2 , - 1 , 0 , 1 , 2 \right\}$ ，集合 ， 集合 ， 则 $( \pmb { \hat { \wp } } A ) \cap B =$

（ ）

A． B． C．

【答案】C

【解析】因为 $U = \left\{ - 2 , - 1 , 0 , 1 , 2 \right\}$ $A = \{ - 2 , - 1 \}$ 所以 $\pmb { \hat { \theta } } A = \left\{ 0 , 1 , 2 \right\}$ 又 $B = \left\{ - 2 , - 1 , 0 , 1 \right\}$ 所以$( \pmb { \hat { \varphi } } _ { } A ) _ { \cap B } = \left\{ 0 , 1 \right\}$

故选：C.

$U = \{ x { \in } { \mathbf { Z } } \| x | { \mathbf { < } } 3 \}$ $A = \{ x | x ^ { 3 } - x = 0 \}$ ${ \big / } A =$ 2．（2023 海南省）设全集 ，集合 ，则 （ ）

A． B． C． D．

【答案】C

【解析】 $U = \{ x \in \mathbf { Z } | \left| x \right| < 3 \} = \{ x \in \mathbf { Z } | - 3 < x < 3 \} = \left\{ - 2 , - 1 , 0 , 1 , 2 \right\}  ,$ $A = \{ x | x ^ { 3 } - x = 0 \} = \{ x | x ( x - 1 ) ( x + 1 ) = 0 \} = \{ - 1 , 0 , 1 \} , \quad \spadesuit A = \{ - 2 , 2 \} .$ .故选:C.

# 考点四 集合运算的综合运用

【例 4】（2023 春·湖南·高三校联考阶段练习）已知集合 ， ， ， 则$\smash { \boldsymbol { A } \cap \left( \Leftrightarrow B \right) }$ （ ）

A． B． C． D．

【答案】C

【解析】由题可得 $\pmb { \hat { \wp } B } = \left\{ 4 , 5 \right\}$ 则 $\boldsymbol { A } \cap \left( \boldsymbol { \Leftrightarrow } \boldsymbol { B } \right) = \left\{ 4 \right\}$ .故选：C.

# 【一隅三反】

1．（2023·云南）已知全集 $U = \left\{ 1 , 2 , 3 , 4 , 5 \right\} , A = \left\{ 1 , 2 , 3 \right\} , B = \left\{ 3 , 4 , 5 \right\}$ 则 （ ）

A． B． C． D． ①

【答案】D

【解析】因为 $U = \left( 1 , 2 , 3 , 4 , 5 \right) , A = \left( 1 , 2 , 3 \right) \quad \quad \oplus A = \left\{ 4 , 5 \right\} \quad \quad B = \left[ 3 , 4 , 5 \right] \quad \quad \oplus B = \left[ 1 , 2 \right] \quad \quad \left( \ddagger _ { \emptyset } A \right) \cap \left( \mathbf { \Phi } _ { U } B \right) \ : \ : \ : _ { \emptyset }$ 故选：D.

2．（2023 山东）（多选）已知全集 U={1,2,3,4,5,6}，集合 $P { = } \{ 1 , 3 , 5 \}$ ， $Q { = } \{ 1 , 2 , 4 \}$ ，则下列结论正确的是（）

A． $P \cap Q = \{ 1 \}$ B． ={1,2,3,4,5,6} C $( \ A \cap \dag ) \cup Q _ { \ = \{ 1 , 2 , 4 , 6 \} }$ D ={3,5}

【答案】ACD

【解析】 $\cdot$ ， $\cdot$ ，∴ ={1}， ={1,2,3,4,5}.   
又 $\oplus P _ { = \{ 2 , 4 , 6 \} }$ ， ={3,5,6}， ∴ ={1,2,4,6}， ={3,5}.故选：ACD.

3．（2023·河北）（多选）已知集合 $A { = } \{ x | { - } 1 { < x \leq } 3 \}$ ，集合 $B { = } \{ x | | x | { \leq } 2 \}$ ，则下列关系式正确的是（）

A． $A \cap B = \emptyset$ $B , A \cup B { = } \{ x | { - } 2 { \leq } x { \leq } 3 \}$ C $\cdot \ A \cup \begin{array} { c } { { \hat { \pmb { \varphi } } { } ^ { B } } } \\ { { \overline { { { \bf \varphi } } } { } ^ { B } = \{ x | x \leq - 1 \ \overrightarrow { \nv { = } } \chi \ v { x } { } x > 2 \} } } \end{array} \qquad \begin{array} { c } { { \sf D . } } \\ { { \sf D . } } \end{array} \ A \cap \begin{array} { c } { { \hat { \pmb { \varphi } } { } ^ { B } } } \\ { { \overline { { { \bf \varphi } } } { } ^ { B } = \{ x | 2 < x \leq 3 \} } } \end{array}$

【答案】BD

【解析】因为 $A = \{ x | - 1 < x \leq 3 \} , B = \{ x | | x | \leq 2 \} = \{ x | - 2 \leq x \leq 2 \} ,$ ，  
所以 $A \cap B = \{ x | - 1 < x \leq 3 \} \cap \{ x | - 2 \leq x \leq 2 \} = \{ x | - 1 < x \leq 2 \}$ ，故 A 错误；  
$A \cup B = \{ x | - 1 < x \leq 3 \} \cup \{ x | - 2 \leq x \leq 2 \} = \{ x | - 2 \leq x \leq 3 \} ,$ ，故 B 正确；  
因为 $\scriptstyle { \widehat { \pmb { \mathscr { P } } } } ^ { B }$ 或 $\cdot$ ，所以 $\bullet B$ 或 $\_$ 或x＞－1}，故C 错误； $\bullet B$ 或 $x > 2 \} = \{ x | 2 < x \leq 3 \}$ ，故D 正确.

故选：BD.

# 考点五 韦恩图

【例 5-1】（2023·四川成都）已知集合 $M = \left\{ 1 , 2 , 3 , 4 , 5 \right\}$ ， $N = \{ 1 , 3 , 5 , 7 , 9 \}$ ，且 $M$ ， 都是全集 的子集，则下图韦恩图中阴影部分表示的集合为（ ）

![](images/f2303352c59dbea446a67fe39aa8caf374417252baf0ee4e9d6f2105be5661e4.jpg)

A． B．C． D，{1,2,3,4,5,7,9}

【答案】C

M ={1,2,3,4,5] $N = \{ 1 , 3 , 5 , 7 , 9 \}$   
【解析】因为 ，  
所以 $M \cap N = \left\{ 1 , 3 , 5 \right\}$ 图中阴影部分表示的集合为 $\pmb { \hat { \varphi } } ( M \cap N )$ ， 所以 $\pmb { \hat { \varphi } } ( M \cap N ) = \{ 7 , 9 \}$ .故选：C

【例 5-2】（2023·北京）设集合 $A = \{ x \mid - 1 \leq x \leq 2 \}$ ， $B = \{ x \mid 0 \leq x \leq 4 \}$ ，则 图阴影区域表示的集合是（ ）

![](images/50140ce2490e580806e7bfc0d244a467d816fae61a0fda341a57441079b39267.jpg)

A． $\{ x | 0 \leq x \leq 2 \}$ B． $\{ x | 1 \leq x \leq 2 \}$ C． $\{ x | 0 \leq x \leq 4 \}$ D． $\{ x \mid 1 \leq x \leq 4 \}$

【答案】A

【解析】由题意可知， 图阴影区域表示的集合是 $A \cap B$ ,所以 $A \cap B = \{ x | - 1 \leq x \leq 2 \} \cap \{ x | 0 \leq x \leq 4 \} = \{ x | 0 \leq x \leq 2 \}$ 故选：A.

【例 5-3】（2023·山西）设全集 $U$ 及集合 $M$ 与 $N$ ，则如图阴影部分所表示的集合为（ ）

![](images/0af6c80f4e046b2e6ac85340a937d177b6774f9a90006b8ca349c6449a79f3e5.jpg)

A． $M \cap N$ B． $M \cup N$ MnN (MUN)C． D．

【答案】D

【解析】依题意图中阴影部分所表示的集合为 $\pmb { \hat { \wp } } ( M \cup N )$ 故选：D【例 5-4】（2023·北京·北京四中校考模拟预测）有三支股票 $A , B , C , 2 8$ 位股民的持有情况如下：每位股民至少持有其中一支股票.在不持有 股票的人中，持有 $B$ 股票的人数是持有 $C$ 股票的人数的 2 倍.在持有 股票的人中，只持有 股票的人数比除了持有 股票外，同时还持有其它股票的人数多1.在只持有一支股票的人中，有一半持有 股票.则只持有 $B$ 股票的股民人数是（ ）

A．7 B．6 C．5 D．4

【答案】A

【解析】由题意，设只持有 股票的人数为 $X$ ，  
则持有 股票还持有其它殸票的人数为 $X \textrm { - } 1$ (图中 $d + e + f$ 的和 )，  
∵只持有一支股票的人中, 有一半没持有 $B$ 或 $C$ 股票,  
∴只持有了 $B$ 和 $C$ 股票的人数和为 $X$ (图中 $b + c$ 部分) .  
假设只同时持有了 $B$ 和 $C$ 股票的人数为 $a$ ,$X + X - 1 + X + a = 2 8$ , 即 $3 X + a = 2 9$ ，  
则 的取值可能是 ,  
与之对应的 值为 ,  
∵没持有 股票的股民中，持有 $B$ 股票的人数是持有 $C$ 股票的人数的2 倍  
$\therefore a + b = 2 ( a + c ) , \textcircled { \sharp } X \cdot a = 3 c ,$ $\scriptstyle . \ X = 8 , a = 5$ 时满足题意，此时 $c = 1 , b = 7$ ,

∴只持有 $B$ 股票的股民人数是 ,故选：A.

![](images/639363506d3a2405b3a4902630ded902b3d45a1c1cba0d7bcef4bc87b6a6717b.jpg)

# 【一隅三反】

1．（2023·安徽安庆）设全集 $= \left\{ - 2 , - 1 , 0 , 1 , 2 \right\} , A = \left\{ x \left| x ^ { 2 } - 1 = 0 \right. \right\} , B = \left\{ x \left| ( x - 1 ) ( x - 2 ) = 0 \right. \right\}$ ， 则图中阴影部分所表示的集合为（ ）

![](images/2e797958bd9b1bbab7cead53eb5c612add1d7964d01119c60aa7d2ca1d1be44a.jpg)

A． B． C． D．

【答案】D

【解析】由 $\boldsymbol { \chi } ^ { 2 } - 1 = 0$ ，解得 $x = 1$ 或 $\chi = - 1$ ，所以 $A = \left\{ x { \big | } x ^ { 2 } - 1 = 0 \right\} = \left\{ 1 , - 1 \right\}$ ，$( x - 1 ) ( x - 2 ) = 0$ ， 解得 $x = 1$ 或 $x = 2$ ，所以 $B = \stackrel { \textstyle \{ \vphantom { \sum _ { i } ^ { i } } x | ( x - 1 ) ( x - 2 ) = 0  \kern - delimiterspace } 2  {  \kern - delimiterspace } ( x - 1 ) ( x - 2 ) = 0 \} = \stackrel { \textstyle \{ 1 , 2 \} } {  \kern - delimiterspace } ( 1 , 2 \} ,$ ，所以 $A \cup B = \left\{ - 1 , 1 , 2 \right\}$ 1 又 $U = \left\{ - 2 , - 1 , 0 , 1 , 2 \right\}$ 则图中阴影部分为 $\pmb { \hat { \psi } } ( \boldsymbol { A } \cup \boldsymbol { B } ) = \left( - 2 , 0 \right]$ 故选：D

2．（2023 春·湖南）已知全集 $U = \mathbf { R }$ ，集合 $A = \{ x \in \mathbf { Z } | 0 < | x | \leq 2 \}$ ， $B = \left\{ - 1 , 0 , 1 , 2 , 3 \right\}$ ， 则图中阴影部分表示的集合为（ ）

![](images/f10425c6dd1e9ef151ab0c0ab86bf8d4f0fef162f0c344f46df4a3f7b0fd57e5.jpg)

A． B． c.{-2,0,2} D．

【答案】D

【解析】全集为 $U$ ，集合 $\begin{array} { r l } { 1 , 1 , 2 \big | } & { { } B = \ - 1 , 0 , 1 , 2 , 3 \big | \quad A \cap B = \{ - 1 , 1 , 2 \big \} , A \cup B = \{ - 2 , - 1 , 0 , 1 , 2 , 3 \} } \end{array}$ ,图中  
阴影部分表示是 $A \cup B$ 去掉 $A \cap B$ 的部分，故表示的集合是 $\left\{ - 2 , 0 , 3 \right\}$   
故选：D

3．（2023·四川成都）已知集合 $M = \left\{ x | - { \sqrt { 3 } } \leq x \leq { \sqrt { 3 } } \right\} , N = \left\{ x | - 3 \leq x \leq 1 \right\}$ ， 且 $M$ ， $N$ 都是全集 $U$ 的子集，则如图的韦恩图中阴影部分表示的集合为（ ）

![](images/a0df8ea7081ad40db5b91f986d7baffabbe715960c1909fe9b4e9083a61af62f.jpg)

A． B． $\left\{ x | - 3 \leq x \leq 1 \right\}$ C． D．

【答案】C

【解析】由韦恩图可知阴影部分表示 ，$M = \left\{ x \mid - { \sqrt { 3 } } \leq x \leq { \sqrt { 3 } } \right\} , N = \left\{ x \mid - 3 \leq x \leq 1 \right\} \qquad N \cap \left( \bigoplus { M } \right) = \left\{ x \Big | - 3 \leq x < - { \sqrt { 3 } } \right\}$ 故选：C.

4．（2023·黑龙江）如图， $I$ 是全集， ， $B$ ， $C$ 是 $I$ 的三个子集，则图中阴影部分表示（ ）

![](images/a0b31d4febf6f2482846abfa48f60f794ab511e60ffa1d1b75c2a06fca368ffe.jpg)

A． B． AnCn(B) AnBn(c) BnCn(A) C． D．

【答案】B

【解析】如图所示，对于 A， $A \cap B \cap C$ 对应的是区域 1；  
对于 B， $A \cap C \cap ( \pmb { \hat { \varphi } } B )$ 对应的是区域2；AnBn(oc)  
对于 C， 对应的是区域 3；BnCn(oA)  
对于 D， 对应的是区域4

故选：B．

![](images/49e91ee61d3f4db43ffa30be3ca0ae1afec33175a16aca9549e351588504bcec.jpg)

5．（2022 秋·江西景德镇）某城市数、理、化竞赛时，高一某班有 26 名学生参加数学竞赛，25 名学生参加物理竞赛，23 名学生参加化学竞赛，其中参加数、理、化三科竞赛的有 7 名，只参加数、物两科的有 6名，只参加物、化两科的有8 名，只参加数、化两科的有5 名．若该班学生共有51 名，则没有参加任何竞赛的学生共有（ ）名

A．7 B．8 C．9 D．10

【答案】D

【解析】画三个圆分别代表数学、物理、化学的人，  
因为有 26 名学生参加数学竞赛，25 名学生参加物理竞赛，23 名学生参加化学竞赛，  
参加数、理、化三科竞赛的有7 名，只参加数、化两科的有5 名，  
只参加数、物两科的有 6 名，只参加物、化两科的有 8 名，  
所以单独参加数学的有 $2 6 - ( 6 + 7 + 5 ) = 8$ 人，25-(6+7+8)=4 .23- (5+7+8)=3  
单独参加物理的有 人，单独参加化学的有 1

故参赛人数共有 $8 + 4 + 3 + 6 + 7 + 8 + 5 = 4 1$ 人，没有参加任何竞赛的学生共有 $5 1 - 4 1 = 1 0$ 人.故选：D.

![](images/82200dad4f05b206976f4476826fefb24a4f944ce0560148d6497fca75a7e232.jpg)

# 考点六 求参数

【例6-1】（2023·湖北荆门）已知集合 $A = \{ a , 5 - a , 4 \}$ ， $B = \{ 3 , 2 a + 1 \}$ ， $A \cup B = \left\{ 2 , 3 , 4 , 5 \right\}$ ， 则 $a = \mathrm { ~ ( ~ \begin{array} { l } { \mathrm { ~ ) ~ } } \\ { \mathrm { ~ ) ~ } } \end{array}  ) }$

A．1 B．2 C．3 D．4

【答案】B

【解析】A={a,5-a,4},B={3,2a+1},AUB={2,3,4,5}  
当 $2 a + 1 = 2$ 即a= $a = \frac { 1 } { 2 }$ 时， $A = \{ \frac { 1 } { 2 } , \frac { 9 } { 2 } , 4 \} , B = \{ 3 , 2 \}$ ， 不符合题意；  
当 $2 a + 1 = 5$ 即 $a = 2$ 时， $A = \{ 2 , 3 , 4 \} , B = \{ 3 , 5 \}$ ，此时 $A \cup B = \{ 2 , 3 , 4 , 5 \}$ .所以 $a = 2$ .故选：B.

【例6-2】（2023春·江西景德镇）设集合 $M = \left\{ x | - 3 < x < 7 \right\} , N = \left\{ x | 2 - t < x < 2 t + 1 , t \in \mathbb { R } \right\}$ ，若$M \cup N = M$ ，则实数 $t$ 的取值范围为（ ）

A． $t \leq \frac 1 3$ $\mathsf { B } . \quad \frac { 1 } { 3 } < t < 3$ C． D．

【答案】C

【解析】因为 $M \cup N { = } M$ ，所以 $N \subseteq M$   
当 $2 - t \geq 2 t + 1$ ，即 $t \leq \frac 1 3$ 时， $N = \varnothing \subseteq M$ ，符合题意；  
当 时，则 $\left\{ \begin{array} { l l } { 2 t + 1 \leq 7 } \\ { 2 - t \geq - 3 } \\ { 2 t + 1 > 2 - t } \end{array} \right.$ 解得 $\frac { 1 } { 3 } < t \leq 3$ ，综上所述实数 的取值范围为 .故选：C.$N \neq \emptyset$ t t<3

【例 6-3】（2023·河南开封）设集合 或 $A = \{ x \mid x < 2 \max 2 _ { \overrightarrow { \overrightarrow { \bigtriangledown } } } x \geq 4 \} , B = \{ x \mid a \leq x \leq a + 1 \}$ 若 $( \bullet \boldsymbol { A } ) \cap B = \varnothing$ ，则 的取值范围是（ ）

A． $a \leq 1$ 或 $a > 4$ B． $a < 1$ 或 $a \ge 4$ C． $a < 1$ D． $a > 4$

【答案】B

【解析】由集合 $A = \{ x \mid x < 2 x \geq 4 \}$ 得 $\scriptstyle { \widehat { \mathfrak { R } } } ^ { A = \{ x \vert 2 \leq x < 4 \} }$ 又集合 $B = \left\{ \chi | \ a \leq x \leq a + 1 \right\}$ 且 $( \bullet \boldsymbol { A } ) \cap B = \varnothing$ ，则 $a + 1 < 2$ 或 $a \ge 4$ ，即 $a < 1$ 或 $a \ge 4$ .故选：B.

【例 6-4】（2023 春·四川南充）（多选）已知全集 $U = \mathrm { R }$ ，集合$A = \left\{ x | - 2 \leq x \leq 7 \right\} , B = \left\{ x | m + 1 \leq x \leq 2 m - 1 \right\}$ ，则使 成 立的实数 $m$ 的取值范围可能是（ ）

A. $\left\{ m \vert 6 \leq m \leq 1 0 \right\}$ B $\left\{ m \vert - 2 < m < 2 \right\}$ C． $\left\{ m \vert - 2 < m < - { \frac { 1 } { 2 } } \right\}$ $\mathsf { D } . \quad \left\{ m \mid 5 < m \leq 8 \right\}$

【答案】BC

B=Θ $m + 1 > 2 m - 1$ $m { < } 2$ B=R【解析】 $\textcircled{1}$ 当 时， 令 ， 得 此时 符合题意；$\textcircled{2}$ 当 $B \neq \boldsymbol { \mathcal { O } }$ 时 $\mathrm { , } m + 1 \leq 2 m - 1 , \Zeta \ m _ { , \mathrm { \normalfont ~  ~ } } m \geq 2 , \Zeta \ m _ { , \mathrm { \normalfont ~  ~ } } \overbrace { \varphi } B = \{ x \big | x < m + 1 \atop \overrightarrow { \bigtriangledown \big _ { \ast } } \big \} x > 2 m - 1 \} ,$ 因为 $A \subseteq { \widehat { \otimes } } B$ ，所以 $m + 1 > 7$ 或 $2 m - 1 < - ~ 2$ ，解得 $m { > } 6$ 或 $m < - \ \frac { 1 } { 2 }$ ，因为 $m \ge 2$ ，所以 $m { > } 6$ .综上， $\cdot$ 的取值范围为 $m { < } 2$ 或 $m { > } 6$ ，故选：BC

# 【一隅三反】

1．（2022 秋·辽宁沈阳）设全集 $U = \left. 2 , 3 , m ^ { 2 } + m - 2 \right.$ ，集合 $A = \left\{ | m + 1 | , 2 \right\} , \spadesuit A = \left\{ 4 \right\}$ ，则 $m = \mathrm { ~ ( ~ ) ~ }$ ）

A． B．2 C． D．

【答案】B

【解析】由题意全集 $U = \left. 2 , 3 , m ^ { 2 } + m - 2 \right.$ ， 集合 $A = \left\{ | m + 1 | , 2 \right\} , \spadesuit A = \left\{ 4 \right\}$ ，

可得 $m ^ { 2 } + m - 2 = 4$ ，解得 $m = - 3$ 或 $m = 2$ ，  
$\scriptstyle m = - 3 \qquad | m + 1 | = 2$ 则 $A = \left\{ 2 , 2 \right\}$ 不合题意，  
$m = 2 _ { \sharp \cdot } , \sp A = \left\{ 2 , 3 \right\} _ { \bf \epsilon , \epsilon } \bullet A = \left\{ 4 \right\}$ 符合题意，故 ， 故选：B.

2．（2023 吉林）已知集合 ， ．

（1）若 $A \cap B \neq \theta$ ，实数 $a$ 的取值范围是 （2）若 $A \cap B \neq A$ ，实数 $a$ 的取值范围是 （3）若 $A \cup B = B$ ，实数 $a$ 的取值范围是

【答案】

$$
( - \infty , 4 ) \quad [ - 2 , + \infty ) \quad ( - \infty , - 2 )
$$

【解析】 $\textcircled{1}$ 若 $A \cap B \neq \theta$ ，得 $a < 4$ ，所以实数 $\cdot$ 的取值范围是 $( - \infty , 4 )$ ；  
$\textcircled{2}$ 因为 $A \cap B = A$ ， 即 $A \subseteq B$ ， 所以 $a < - 2$ ， 所以若 $A \cap B \neq A$ ， 则 $a \ge - 2$ ， 则实数 $\cdot$ 的取值范围是 ；  
$\cdot$ 若 $A \cup B = B$ ，即 $A \subseteq B$ ，所以 $a < - 2$ ，则实数 $a$ 的取值范围是 $( - \infty , - 2 )$ .  
故答案为： $( - \infty , 4 )$ ； ② ； $( - \infty , - 2 )$ 3．（2023·福建）在 $\textcircled { 1 } A \cup B = B$ ； $\textcircled { 2 } A \cap B = \varnothing$ 这二个条件中任选一个，补充到本题第（2）问的横线处，求解下列问题.  
问题：已知集合 $A = \left\{ x | a - 1 \leq x \leq a + 1 \right\} , B = \left\{ x | \cdot 1 \leq x \leq 3 \right\} .$ (1)当 $a = 2$ 时，求 $A \cup B$ ；  
(2)若_ _，求实数 $a$ 的取值范围.

【答案】 $\left\{ x \vert - 1 \leq x \leq 3 \right\}$ (2)答案见解析

【解析】（1）当 $a = 2$ 时，集合 $A = \left\{ x | 1 \leq x \leq 3 \right\} , B = \left\{ x | - 1 \leq x \leq 3 \right\}$ ， 所以 $A \cup B = \left( x | - 1 \leq x \leq 3 \right)$

（2）若选择 $A \cup B = B$ ，则 $A \subseteq B$ ，

因为 $A = \left\{ x | a - 1 \leq x \leq a + 1 \right\}$ ， 所以 $A \neq \emptyset$ ，又 $B = \left\{ x | - 1 \leq x \leq 3 \right\}$ ，$\textstyle { \int } a - 1 \geq - 1$   
所以 $\textstyle { \left| { a + 1 \leq 3 } \right. }$ ，解得 $0 \leq a \leq 2$ ，所以实数 $a$ 的取值范围是 $\scriptstyle \left\{ a | 0 \leq a \leq 2 \right\}$   
若选择 $\cdot$ ， $A \cap B = \emptyset$   
因为 $A = \left\{ x | a - 1 \leq x \leq a + 1 \right\} , \beta \neq \lfloor x \leq 0 , \mathbb { X } ^ { B = \left\{ x | - 1 \leq x \leq 3 \right\} }$   
所以 $a \cdot 1 > 3$ 或 $a + 1 < - 1$ ，解得 $a > 4$ 或 $a < - 2$ ，  
所以实数 $a$ 的取值范围是 $\left\{ a | a > 4 \quad a < - 2 \right\}$

4．（2023·陕西渭南）已知集合 $P = \left\{ x \vert x < - 1 \vert \ast \lbrack x > 6 \right\}$ ， $Q = \left\{ x | 1 - m \leq x \leq 1 + m \right\}$ ，全集为 .

(1)求集合 $\looparrowleft$ ， (2)若 $( { \sqrt { \hbar } } P ) \cup Q = \mathbf { \lambda } _ { \mathbf { R } } P$ ，求实数 $m$ 的取值范围.

【答案】 ${ \bf \Phi } _ { \left( 1 \right) } \bigg \{ x \Big \vert - 1 \le x \le 6 \Big \} _ { \ ( 2 ) } \left( - \infty , 2 \right]$ 【解析】 （1） $\therefore P = \left\{ x \mid x \langle - 1 \overrightarrow { \sf a x } x \rangle { \bf 6 } \right\} \quad \cdot \bullet P = \left\{ x \mid - 1 \leq x \leq 6 \right\}$

（2）由 $( \ J _ { \mathfrak { A } } P ) \cup Q = \ J _ { \mathfrak { e } } P _ { \mathfrak { f } _ { \mathfrak { A } } ^ { \boxplus } , } \quad Q \subseteq \bullet P ,$   
当 $Q = \oslash$ 时，由 $Q = \left\{ x | 1 - m \leq x \leq 1 + m \right\}$ ，可得 $- \ m > 1 + m$ ，即 $m < 0$ ；  
当 $Q \neq Q$ 时，由 $Q = \left\{ x | 1 - m \leq x \leq 1 + m \right\}$ 且 ，  
可得 $\begin{array} { l } { \{ 1 - \ m \leq 1 + m  } \\ {  \{ 1 - \ m \geq - 1  } \\ {  1 + m \leq 6  } \end{array}$ ，解得 ，综上所述，实数 m 的取值范围为 .$0 \leq m \leq 2$

5．（2022 秋·福建福州）已知集合 $A = \left\{ x \in \mathbf { N } { \big | } { \frac { 1 } { 3 } } < x < 4 \right\}  , \quad B = \left\{ x | a x - 1 \geq 0 \right\}$ ， ．请从① ，②

$A \cap B = A$ ， $\boldsymbol { \mathfrak { ‰} }$ 这三个条件中选一个填入（2）中横线处，并完成第（2）问的解答．（如果选择多个条件分别解答，按第一个解答计分）

(1)当 时 ，求 ；(2)若_ ，求实数 $a$ 的取值范围【答案】 $( 1 ) { \overset { } { A } } \cap B = \left\{ 2 , 3 \right\}$ ；

[1,+8)(2)条件选择见解析，

【解析】（1）由题意得， $A = \left\{ x \in { \bf N } \big | { \frac { 1 } { 3 } } < x < 4 \right\} = \left\{ 1 , 2 , 3 \right\} \ .$ 当 $a = \frac { 1 } { 2 }$ $B = \left\{ x { \big | } { \frac { 1 } { 2 } } x - 1 \geq 0 \right\} = \left\{ x { \big | } x \geq 2 \right\}$   
$A \cap B = \left\{ 2 , 3 \right\}$   
（2）选择 $\textcircled{1}$   
$\because A \cup B = B , \therefore A \subseteq B ,$   
当 $a = 0$ 时， $B = \emptyset$ ，不满足 $A \subseteq B$ ，舍去；  
当 $a > 0$ 时， $B = \left\{ x { \big | } x \geq { \frac { 1 } { a } } \right\}$ 要使 $A \subseteq B$ ， 则 $\frac { 1 } { a } \leq 1$ ，解得 $a \ge 1$ ；当 $a < 0$ 时， $B = \left\{ x { \big | } x \leq { \frac { 1 } { a } } \right\} .$ 此时 $\frac { 1 } { a } < 0$ 不满足 $A \subseteq B$ ，舍去．[1,+∞)  
综上，实数 的取值范围为  
选择 $\textcircled{2}$   
$\because A \cap B = A , \therefore A \subseteq B ,$ 当 $a = 0$ 时， $B = \emptyset$ ，不满足 $A \subseteq B$ ，舍去；  
当 $a > 0$ 时， $B = \left\{ x { \big | } x \geq { \frac { 1 } { a } } \right\}$ ，要使 $A \subseteq B$ ，则 $\frac { 1 } { a } \leq 1$ ，解得 $a \ge 1$ ；当 $a < 0$ 时， $B = \left\{ x \vert x \leq \frac { 1 } { a } \right\} \qquad \frac { 1 } { a } < 0$ ， 不满足 $A \subseteq B$ ，舍去[1,+∞)  
综上，实数 的取值范围为  
选择 $\cdot$   
$A \cap ( \bullet B ) = \emptyset \qquad A \subseteq B$   
当 $a = 0$ 时， $B = \emptyset$ ，不满足 $A \subseteq B$ ，舍去；  
当 $a > 0$ 时， $B = \left\{ x { \big | } x \geq { \frac { 1 } { a } } \right\}$ 要使 $A \subseteq B$ ，则 $\frac { 1 } { a } \leq 1$ 解得 $a \ge 1$ ；当 $a < 0$ 时 ， $B = \left\{ x { \big | } x \leq { \frac { 1 } { a } } \right\} _ { \mathrm { { . } } }$ ， 此时 $\frac { 1 } { a } < 0$ ， 不满足 $A \subseteq B$ ，舍去．[1,+∞)  
综上，实数 $a$ 的取值范围为