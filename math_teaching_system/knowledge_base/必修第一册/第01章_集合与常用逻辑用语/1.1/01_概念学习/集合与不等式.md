---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 12
source_file: 高考数学-上分要义-高中数学重要公式荟萃.md
title: 高考数学-上分要义-高中数学重要公式荟萃
type: concept
---

# 集合与不等式

# 1.交集的性质

对于任意两个集合 $A , B$ ,都有$( 1 ) A \cap B = B \cap A \qquad ( 2 ) A \cap A = A$ (3 $\dag ) A \cap \dag$ (4)若 $A \subseteq B$ ,则 $A \cap B = A$

# 2.并集的性质

对于任意两个集合 $A , B$ ,都有$( 1 ) A \cup B = B \cup A \qquad ( 2 ) A \cup A = A$ (3 $\ O ) A \cup \bigotimes \ O = \bigotimes \cup A = A$ (4)若 $A \subseteq B$ ,则 $A \cup B = B$

# 3.补集的性质

对于任意两个集合 $A , B$ ,全集 $U$ ,都有（ $\begin{array} { r l r } & { ( 1 ) A \cup ( \complement _ { v } A ) = U } & { ( 2 ) A \cap ( \complement _ { v } A ) = \emptyset } \\ & { ( 3 ) \complement _ { v } ( \complement _ { v } A ) = A } & { ( 4 ) \complement _ { v } ( A \cap B ) = ( \complement _ { v } A ) \cup ( \complement _ { v } B ) } \\ & { ( 5 ) \complement _ { v } ( A \cup B ) = ( \complement _ { v } A ) \cap ( \complement _ { v } B ) } \end{array}$

# 4.常用不等式

(1） $a , b \in \mathbf { R } , a ^ { 2 } + b ^ { 2 } \geqslant 2 a b$ （当且仅当 $a = b$ 时,等号成立)(2) $a , b > 0 , { \frac { a + b } { 2 } } \geq { \sqrt { a b } }$ （当且仅当 $a = b$ 时,等号成立)$3 ) a , b > 0 , \sqrt { \frac { a ^ { 2 } + b ^ { 2 } } { 2 } } \geqslant \frac { a + b } { 2 } \geqslant \sqrt { a b } \geqslant \frac { 2 } { \frac { 1 } { a } + \frac { 1 } { b } } ($ 当且仅当$a = b$ 时,等号成立)

# 函数与导数

1.指数幂的运算性质$( \ 1 \ ) a ^ { \prime } a ^ { s } = a ^ { r + s } ( \ a > 0 , r , s \in \mathbf { R } )$ (2） $( a ^ { r } ) ^ { s } = a ^ { r s } \big ( a > 0 , r , s \in \mathbf { R } \big )$ (3） $( a b ) ^ { r } = a ^ { r } b ^ { r } ( a > 0 , b > 0 , r \in { \bf R } )$

# 2.对数的运算性质

如果 $a { > } 0$ ，且 $a \neq 1 , M > 0 , N > 0$ ,那么

（1）积的对数： $\log _ { a } ( M N ) = \log _ { a } M + \log _ { a } N$ (2)商的对数 $\log _ { a } { \frac { M } { N } } \ l = \log _ { a } M \ l - \log _ { a } N$ (3)幂的对数： $\log _ { a } M ^ { n } = n \log _ { a } M ( n \in \mathbf { R } )$

# 3.对数恒等式

=N(a>0,且a≠1,N>）

4.对数换底公式$\log _ { a } b = { \frac { \log _ { c } b } { \log _ { c } a } } ( a > 0$ ，且 $a \neq 1 , c > 0$ ，且 $c \neq 1 , b > 0 )$

# 5.基本初等函数的导数公式

(1） $C ^ { \prime } { = } 0$ （ $C$ 为常数)(2) $( x ^ { \alpha } ) ^ { \prime } = \alpha x ^ { \alpha - 1 } \mathopen { } \mathclose \bgroup ( \alpha \in \mathbf { R }$ 且 $\alpha \neq 0$ ）

(3)(sin x)'=cos x(4)(cos x)'=-sin x  
（5）(lnx）=1  
(6) $( \log _ { a } x ) ^ { \prime } { = } { \frac { 1 } { x \ln a } } ( a { > } 0$ ，且 $a \neq 1$ ）  
(7) $\mathbf { \Psi } ( \mathbf { e } ^ { x } ) \mathbf { \Psi } ^ { \prime } = \mathbf { e } ^ { x }$ (8) $( a ^ { x } ) ^ { \prime } = a ^ { x } \ln a ( a { > } 0$ ，且 $a \neq 1$ ）

# 6.导数的运算法则

$$
{ \begin{array} { r l } & { 1 ) \left[ f ( x ) \pm g ( x ) \right] ^ { \prime } = f ^ { \prime } ( x ) \pm g ^ { \prime } ( x ) } \\ & { 2 ) \left[ f ( x ) \cdot g ( x ) \right] ^ { \prime } = f ^ { \prime } ( x ) g ( x ) + f ( x ) g ^ { \prime } ( x ) } \\ & { 3 ) \left[ { \cfrac { f ( x ) } { g ( x ) } } \right] ^ { \prime } = { \cfrac { f ^ { \prime } ( x ) g ( x ) - f ( x ) g ^ { \prime } ( x ) } { \left[ g ( x ) \right] ^ { 2 } } } ( g ( x ) \neq 0 ) } \end{array} }
$$

# 三角函数与解三角形

1.同角三角函数的基本关系$\sin ^ { 2 } \alpha + \cos ^ { 2 } \alpha = 1 \tan \alpha = { \frac { \sin \alpha } { \cos \alpha } } { \bigg ( } \alpha \neq k \pi + { \frac { \pi } { 2 } } , k \in \mathbf { Z } { \bigg ) }$

2.正弦、余弦的诱导公式 $\sin ( \frac { n \pi } { 2 } { + } \alpha ) = \{ \begin{array} { l l } { \big ( - 1 \big ) ^ { \frac { n } { 2 } } \sin \alpha \big ( n \arg x \big | \big | \# / \frac { \gg } { \Re } / \mathcal { X } \big ) } \\ { \big ( - 1 \big ) ^ { \frac { n - 1 } { 2 } } \cos \alpha \big ( n \stackrel {  } { \ J } / \frac {  } { \ r } / \frac {  } { \star } / \mathcal { X } \big ) } \end{array}  ,$ $\cos ( \frac { n \pi } { 2 } { + } \alpha ) = \{ \begin{array} { l l } { { \displaystyle ( - 1 ) ^ { \frac { n } { 2 } } \cos \alpha \big ( n \stackrel { {  } } {  } \jmath \big / \big | \mathbb { H } \big > / \tilde { \mathcal { X } } \big \rangle } }  \\ { { \big ( - 1 \big ) ^ { \frac { n + 1 } { 2 } } \sin \alpha \big ( n \stackrel { {  } } {  } \jmath \stackrel { {  } } {  } / \tilde { \mathcal { X } } \big ) } } \end{array}  ,$ （202

3.两角和与差的正弦、余弦和正切公式$\begin{array} { l } { { \mathrm { S } _ { ( \alpha \pm \beta ) } : \sin \big ( \alpha \pm \beta \big ) = \sin \alpha \cos \beta \pm \cos \alpha \sin \beta } } \\ { { \mathrm { C } _ { ( \alpha \pm \beta ) } : \cos \big ( \alpha \pm \beta \big ) = \cos \alpha \cos \beta \mp \sin \alpha \sin \beta } } \\ { { \mathrm { T } _ { ( \alpha \pm \beta ) } : \tan \big ( \alpha \pm \beta \big ) = \displaystyle \frac { \tan \alpha \pm \tan \beta } { 1 \mp \tan \alpha \tan \beta } } } \end{array}$

# 4.二倍角公式

$$
\begin{array} { l } { { \mathrm { S } _ { _ { 2 \alpha } } : \sin 2 \alpha = 2 \sin \alpha \cos \alpha } } \\ { { \mathrm { C } _ { _ { 2 \alpha } } : \cos 2 \alpha = \cos ^ { 2 } \alpha - \sin ^ { 2 } \alpha = 1 - 2 \sin ^ { 2 } \alpha = 2 \cos ^ { 2 } \alpha - 1 } } \\ { { \mathrm { T } _ { _ { 2 \alpha } } : \tan 2 \alpha = \displaystyle \frac { 2 \tan \alpha } { 1 - \tan ^ { 2 } \alpha } } } \end{array}
$$

# 5.辅助角公式

$$
\begin{array} { l } { { a \sin \ x + b \cos \ x = \sqrt { { a ^ { 2 } + b ^ { 2 } } } \left( \frac { a } { { \sqrt { { a ^ { 2 } + b ^ { 2 } } } } } \sin \ x + \frac { b } { { \sqrt { { a ^ { 2 } + b ^ { 2 } } } } } \cos \ x \right) } } \\ { { \ } } \\ { { \qquad = \sqrt { { a ^ { 2 } + b ^ { 2 } } } \left( \sin \ x \cos \ \varphi + \cos \ x \sin \ \varphi \right) } } \\ { { \ } } \\ { { \qquad = \sqrt { { a ^ { 2 } + b ^ { 2 } } } \sin ( x + \varphi ) \left( \ddagger { \cdot } { \mathsf { H } } \tan \ \varphi = \frac { b } { a } \right) } } \end{array}
$$

# 6.正弦定理

$\frac { a } { \sin A } = \frac { b } { \sin B } = \frac { c } { \sin C } = 2 R ( R$ 为 $\triangle A B C$ 的外接圆半径)

用角表示边： $a = 2 R \sin \ A$ $b = 2 R \sin \ B$ $c = 2 R \sin { \mathrm { ~ } C }$

# 7.余弦定理

$\begin{array} { c c } { { a ^ { 2 } = b ^ { 2 } + c ^ { 2 } - 2 b c \cos \ A \qquad } } & { { b ^ { 2 } = a ^ { 2 } + c ^ { 2 } - 2 a c \cos \ B } } \\ { { } } & { { } } \\ { { c ^ { 2 } = a ^ { 2 } + b ^ { 2 } - 2 a b \cos \ C \qquad } } & { { } } \end{array}$ 求角：co $\mathrm { ~ s ~ } A = \frac { b ^ { 2 } + c ^ { 2 } - a ^ { 2 } } { 2 b c }$ cos $B = \frac { a ^ { 2 } + c ^ { 2 } - b ^ { 2 } } { 2 a c }$ cos $C = \frac { a ^ { 2 } + b ^ { 2 } - c ^ { 2 } } { 2 a b }$

# 平面向量与复数

# 1.向量的模

设 $\pmb { a } = ( \boldsymbol { x } , \boldsymbol { y } )$ ，则 $\vert a \vert ^ { 2 } = x ^ { 2 } + y ^ { 2 }$ 或 $\vert a \vert = \sqrt { x ^ { 2 } + y ^ { 2 } }$ 若 $A \left( x _ { 1 } , y _ { 1 } \right) , B \left( x _ { 2 } , y _ { 2 } \right) ,$ ， 则 $\vert \overrightarrow { A B } \vert = \sqrt { \left( x _ { 2 } - x _ { 1 } \right) ^ { 2 } + \left( y _ { 2 } - y _ { 1 } \right) ^ { 2 } }$

# 2.向量的夹角

设 $^ { a , b }$ 都是非零向量， $\pmb { a } = \left( x _ { 1 } , y _ { 1 } \right) , b = \left( x _ { 2 } , y _ { 2 } \right)$ ,夹角为 $\theta$ ,则cos $\theta = \frac { \pmb { a } \cdot \pmb { b } } { \vert \pmb { a } \vert \vert \pmb { b } \vert } { = } \frac { x _ { 1 } x _ { 2 } + y _ { 1 } y _ { 2 } } { \sqrt { x _ { 1 } ^ { 2 } + y _ { 1 } ^ { 2 } } \sqrt { x _ { 2 } ^ { 2 } + y _ { 2 } ^ { 2 } } }$

3.复数的模 若 $z = a + b \mathrm { i } \left( a , b \in \mathbf { R } \right)$ ,则 $| z | = | a + b | | = \sqrt { a ^ { 2 } + b ^ { 2 } }$

4.复数的四则运算

(1) $\mathrm { ~ ( ~ } a + b \mathrm { i ~ ) + ( ~ } c + d \mathrm { i } \mathrm { ) = ~ ( ~ } a + c \mathrm { ~ ) + ( ~ } b + d \mathrm { ) \mathrm { i } }$ (2) $\mathrm { ~ ( ~ } a + b \mathrm { i } \mathrm { ) - } \mathrm { ( ~ } c + d \mathrm { i } \mathrm { ) = ~ ( ~ } a - c \mathrm { ) + ( ~ } b - d \mathrm { ) \mathrm { i } \mathrm { } }$ (3) $\left( a + b \mathrm { i } \right) \left( c + d \mathrm { i } \right) = \left( a c - b d \right) + \left( b c + a d \right) \mathrm { i }$ (4) $( a + b \mathbf { i } ) \div ( c + d \mathbf { i } ) = \frac { a c + b d } { c ^ { 2 } + d ^ { 2 } } + \frac { b c - a d } { c ^ { 2 } + d ^ { 2 } } \mathbf { i } ( c + d \mathbf { i } \neq 0 )$

# 数列

# 1.等差数列

(1)通项公式： $\scriptstyle ( a _ { n } = a _ { 1 } + ( n - 1 ) d$ （其中首项是 $a _ { \scriptscriptstyle 1 }$ ,公差是 $d$ ）(2）前 $n$ 项和公式 $\cdot S _ { n } = \frac { n \left( a _ { 1 } + a _ { n } \right) } { 2 } = n a _ { 1 } + \frac { n \left( n - 1 \right) } { 2 } d$ (3)等差中项：若 $A$ 是 $^ { a }$ 与 $b$ 的等差中项，则 $A = \frac { a + b } { 2 }$

# 2.等比数列

(1)通项公式： $a _ { n } = a _ { 1 } q ^ { n - 1 }$ （其中首项是 $a _ { \scriptscriptstyle 1 }$ ,公比是 $q$ ）

(2)前 $\boldsymbol { n }$ 项和公式 $ { \boldsymbol { S } } _ { n } = \left\{ \begin{array} { l l } { n a _ { 1 } , q = 1 , } \\ { \phantom { - } a _ { 1 } - a _ { n } q } \\ { \phantom { - } 1 - q } \end{array} \right. ,$

(3)等比中项:若G是a与b的等比中项,则- ${ \frac { G } { a } } = { \frac { b } { G } }$ 即 $G ^ { 2 } = a b$ （或 $G = \pm { \sqrt { a b } }$ ,等比中项有两个）

# 立体几何

# 几何体的侧面积与体积

设 $c ^ { \prime } , c$ 分别为上、下底面的周长， $h$ 为高， $h ^ { \prime }$ 为斜高，

$\mathbf { \xi } _ { l }$ 为母线长， $r$ 为圆柱、圆锥的底面半径， $r _ { 1 } , r _ { 2 }$ 分别表示圆台的上、下底面半径， $R$ 为球的半径

<html><body><table><tr><td>名称</td><td>侧面积</td><td>体积</td></tr><tr><td>直棱柱</td><td>S=ch</td><td>V=S·h</td></tr><tr><td>正棱锥</td><td>S=ch 2</td><td>V=- S·h 3</td></tr><tr><td>正棱台</td><td>= 一 (c+c&#x27;)h&#x27; 2 SM</td><td>3 V=h(SLN+S下N+√SENE·STR）</td></tr><tr><td>圆柱</td><td>S=2πrh</td><td>V=πr²h</td></tr><tr><td>圆锥</td><td>S=Trl</td><td>3m²h V=</td></tr><tr><td>圆台</td><td>S=T(r+r）l</td><td>V=πh(r²+r，·r+r2） 3</td></tr><tr><td>球</td><td></td><td>V</td></tr></table></body></html>

# 解析几何

# 距离公式

(1)两点间的距离  
平面上的两点 $P _ { \mathrm { _ 1 } } ( x _ { \mathrm { 1 } } , y _ { \mathrm { 1 } } )$ $P _ { \ 2 } ( x _ { 2 } , y _ { 2 } )$ 间的距离公式  
$| P _ { 1 } P _ { 2 } | = { \sqrt { \left( x _ { 1 } - x _ { 2 } \right) ^ { 2 } + \left( y _ { 1 } - y _ { 2 } \right) ^ { 2 } } }$   
特别地，原点 $O ( 0 , 0 )$ 与任一点 $P ( x , y )$ 的距离 $\mid O P \mid =$   
$\sqrt { x ^ { 2 } + y ^ { 2 } }$   
(2)点到直线的距离  
点 $P _ { 0 } \left( x _ { 0 } , y _ { 0 } \right)$ 到直线 $l \colon A x + B y + C = 0$ 的距离 $d =$   
$\frac { \mid A x _ { 0 } + B y _ { 0 } + C \mid } { \sqrt { A ^ { 2 } + B ^ { 2 } } }$   
(3)两平行线间的距离  
两条平行线 $A x + B y + C _ { 1 } = 0$ 与 $A x + B y + C _ { 2 } = 0$ 间的距离 $d =$   
$\frac { \mid C _ { 1 } - C _ { 2 } \mid } { \sqrt { A ^ { 2 } + B ^ { 2 } } }$

# 1统计与概率

1.排列数公式$\operatorname { A } _ { n } ^ { m } = n { \big ( } n - 1 { \big ) } \cdots { \big ( } n - m + 1 { \big ) } = { \frac { n ! } { { \big ( } n - m { \big ) } ! } } { \big ( } n , m \in \mathbf { N } ^ { \cdot }$ ，且$m \leqslant n ,$ .注：规定 $0 ! = 1$

2.组合数公式$\mathrm { C } _ { n } ^ { \prime \prime } = \frac { \mathrm { A } _ { n } ^ { m } } { \mathrm { A } _ { m } ^ { m } } = \frac { n \left( n - 1 \right) \cdots \left( n - m + 1 \right) } { m ! } = \frac { n ! } { m ! \left( n - m \right) ! } \left( n , m \in \mathbb { Z } \right) ,$ $\mathbf { N } ^ { * }$ ，且 $m \leqslant n$ .注：规定 $\mathrm { C } _ { n } ^ { n } = 1 , \mathrm { C } _ { n } ^ { 0 } = 1$

3.二项展开式的通项公式$\left( a + b \right) ^ { n }$ 的二项展开式的通项 $T _ { \scriptscriptstyle k + 1 } = \mathrm { C } _ { \scriptscriptstyle n } ^ { \scriptscriptstyle k } a ^ { \scriptscriptstyle n - k } b ^ { k } \big ( k = 0 , 1$ ，$2 , \cdots , n )$

# 4.条件概率

$( 1 ) P ( B | A ) = { \frac { P ( A B ) } { P ( A ) } } =$ 事件 $A$ 和事件 $B$ 同时发生的概率事件 $A$ 发生的概率  
$( 2 ) P ( B | A ) { = } { \frac { n ( A B ) } { n ( A ) } } { = }$ 事件 $A B$ 所包含的样本点的个数事件 $A$ 所包含的样本点的个数