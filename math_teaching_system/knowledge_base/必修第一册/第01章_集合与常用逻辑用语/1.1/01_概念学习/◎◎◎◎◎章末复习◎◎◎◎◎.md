---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 中等
estimated_study_time: 12
source_file: 专题12 函数的概念与性质（知识梳理）-2020-2021学年高一数学单元复习（人教A版2019必修第一册）.md
title: 专题12 函数的概念与性质（知识梳理）-2020-2021学年高一数学单元复习（人教A版2019必修第一册）
type: problem_type
---

# ◎◎◎◎◎章末复习◎◎◎◎◎

# 1. 知识系统整合

![](images/449791a49c45558cb3476e2b77157bbbab2102414c943460542636602423853a.jpg)

# 2. 规律方法收藏

# 1.同一函数的判定方法

(1)定义域相同； (2)对应关系相同(两点必须同时具备)

# 2.函数解析式的求法

(1)定义法；  
(2)换元法；  
(3)待定系数法；  
(4)解方程(组)法；

(5)赋值法

# 3.函数的定义域的求法

(1)已给出函数解析式：函数的定义域是使解析式有意义的自变量的取值集合

(2)实际问题：求函数的定义域既要考虑解析式有意义，还应考虑使实际问题有意义

(3)复合函数问题

$\textcircled{1}$ 若函数 $f ( x )$ 的定义域为 $[ a , \ b ]$ ，函数 $f [ g ( x ) ]$ 的定义域应由 $\scriptstyle a \leqslant g ( x ) \leqslant b$ 解出；

$\textcircled{2}$ 若函数 $f [ g ( x ) ]$ 的定义域为 $[ a , \ b ]$ ，则函数 $f ( x )$ 的定义域为函数 $g ( x )$ 在 $[ a , \ b ]$ 上的值域

注意： $\textcircled{1}$ 函数 $f ( x )$ 中的 $\pmb { X }$ 与函数 $f [ g ( x ) ]$ 中的 $g ( x )$ 地位相同．

$\textcircled{2}$ 定义域所指永远是 $x$ 的范围．

# 4.函数值域的求法

(1)配方法(二次或四次)；(2)判别式法；  
(3)换元法；  
(4)函数的单调性法

5.判断函数单调性的步骤

(1)设 $X _ { 1 }$ ， $X _ { 2 }$ 是所研究区间内任意两个自变量的值，且 $x _ { 1 } { < } x _ { 2 }$ ；(2)判定 $f ( x _ { 1 } )$ 与 $f ( x _ { 2 } )$ 的大小：作差比较或作商比较；(3)根据单调性定义下结论

# 6.函数奇偶性的判定方法

首先考查函数的定义域是否关于原点对称，再看函数 $\boldsymbol { f } ( - \boldsymbol { x } )$ 与 $f ( x )$ 之间的关系： $\textcircled{1}$ 若函数 $r ( - x ) { = } f ( x )$ ，则 $f ( x )$ 为偶函数；若函数 $r ( - x ) { = } - f ( x )$ ，则 $f ( x )$ 为奇函数； $\textcircled{2}$ 若 $\pmb { f ( - x ) } -$ $\scriptstyle f ( x ) = 0$ ，则 $f ( x )$ 为偶函数；若 $\scriptstyle f ( x ) + f ( - x ) = 0$ ，则 $f ( x )$ 为奇函数； $\textcircled { 3 } vec { \mathcal { H } } { = } 1 ( f ( - x ) { \neq } 0 )$ ，则$f ( x )$ 为偶函数；若 $= - 1 ( f ( - x ) { \neq } 0 )$ ，则 $f ( x )$ 为奇函数

# 7.幂函数的图象特征

(1)幂函数的图象一定会出现在第一象限内，一定不会出现在第四象限内，图象最多只能同时出现在两个象限内，至于是否在第二、三象限内出现，则要看幂函数的奇偶性

(2)幂函数的图象在第一象限内的变化规律为：在第一象限内直线 $x = 1$ 的右侧，图象从下到上，相应的指数由小到大，直线 $x = 1$ 的左侧，图象从下到上，相应的指数由大到小．

# 8.函数的应用

解决函数应用题关键在于理解题意，提高阅读能力．一方面要加强对常见函数模型的理解，弄清其产生的实际背景，把数学问题生活化；另一方面，要不断拓宽知识面，增加间接的生活阅历，诸如了解一些物价、行程、产值、利润、环保等实际问题，及有关角度、面积、体积、造价的问题，培养实际问题数学化的意识和能力

# 3 学科思想培优

# 一、函数的定义域

函数的定义域是指函数 $y { = } f ( x )$ 中自变量 $x$ 的取值范围．确定函数的定义域是进一步研究函数其他性质的前提，而研究函数的性质，利用函数的性质解决数学问题是中学数学的重要组成部分．所以熟悉函数定义域的求法，对于函数综合问题的解决起着至关重要的作用

$3  { \boldsymbol { X } } ^ { 2 }$ [典例 1] (1)函数 $f ( x ) = \sqrt { 1 - \ x } + ( 3 x - 1 ) ^ { 0 }$ 的定义域是(　　)

A. $( - \infty , \frac { 1 } { 3 } )$ B. $( \frac 1 3 , 1 )$ C $\therefore ( - \frac { 1 } { 3 } , \frac { 1 } { 3 } )$ $\mathrm { D } . \qquad \quad ( \mathrm { - } \infty , \frac 1 3 ) \cup ( \frac 1 3 , 1 )$

(2)已知函数 $y = f ( x + 1 )$ 的定义域是[－2,3]，则 $y { = } f ( 2 x { - } 1 )$ 的定义域是(　　)

A. $[ 0 , \frac { 5 } { 2 } ]$ B．[－1,4] C.[－5,5] D．[－3,7] 【答案】(1)D (2)A

【解析】(1)由题意，得 $\left\{ \begin{array} { l } { \displaystyle 1 - \ x > 0 } \\ { \displaystyle 3 x - 1 \neq 0 } \\ { \displaystyle \sum _ { \ell \neq 1 } \subsetneq 1 \boxed { \mp } x < 1 \boxplus x \neq } \end{array} \right. \frac { 1 } { 3 } .$

(2)设 $\scriptstyle u = x + 1$ ，由 $- 2 { \leqslant } x { \leqslant } 3$ ，得 $- 1 { \leqslant } x + 1 { \leqslant } 4$ ，所以 $y { = } f ( u )$ 的定义域为[－1,4]．再由5 $[ 0 , \frac { 5 } { 2 } ]$ $- 1 { \leqslant } 2 x - 1 { \leqslant } 4$ ，解得 $0 \leqslant x \leqslant 2$ ，即函数 $y { = } f ( 2 x { - } 1 )$ 的定义域是

# 二、分段函数问题

所谓分段函数是指在定义域的不同子区间上的对应关系不同的函数．分段函数是一个函数而非几个函数，其定义域是各子区间的并集，值域是各段上值域的并集．分段函数求值等问题是高考常考的问题

$\textstyle { \left\lceil 2 x + a , x < 1 \right\rceil }$ [典例 2]　已知实数 $a \neq 0$ ，函数 $f ( x ) = { \stackrel { \textstyle 1 - x - 2 a , x \geq 1 } { } }$ 若 $\scriptstyle f 1 - a ) = f ( 1 + a )$ ，则 $^ { a }$ 的值_

【答案】 － 4

【解析】 $\textcircled{1}$ 当 $1 - a { < } 1$ ，即 $a { > } 0$ 时，此时 $a + 1 { > } 1$ ，  
3由 $f ( 1 - a ) { = } f ( 1 + a )$ ，得 $2 ( 1 - a ) + a = - ( 1 + a ) - 2 a$ ，解得 $a = - 2$ (舍去)；$\textcircled{2}$ 当 $1 - a { > } 1$ ，即 $a { < } 0$ 时，此时 $a + 1 { < } 1$ ，由 $f ( 1 - a ) { = } f ( 1 + a )$ ，得 $- ( 1 - a ) - 2 a = 2 ( 1 + a )$   
3 $\scriptstyle { \underline { { a } } } = - { \frac { 3 } { 4 } }$ $+ a$ ，解得 $a { = } - 4$ ，符合题意．综上所述， .

# 三、函数的单调性与奇偶性

单调性是函数的一个重要性质，某些数学问题，通过函数的单调性可将函数值间的关系转化为自变量之间的关系进行研究，从而达到化繁为简的目的，特别是在比较大小、证明不等式、求值或求最值、解方程(组)等方面应用十分广泛  
奇偶性是函数的又一重要性质，利用奇偶函数图象的对称性可以缩小问题研究的范围，常能使求解的问题避免复杂的讨论

[典例 3]（2020·邢台市第二中学高一开学考试）设函数 $y = f ( x )$ 的定义域为 $\pmb R$ ，并且满足， ，当 $\chi > 0$ 时， $f ( x ) > 0$ .

（1）求 $f ( 0 )$ 的值；

（2）判断函数的奇偶性；  
（3）如果 $f ( x ) + f ( 2 + x ) < 2$ ，求 $_ x$ 的取值范围.

【解析】（1）令 ， $\begin{array} { r } { \ _ { , } \left. \begin{array} { l } { _ { X } = y = 0 } \\ { } \end{array} \right. , | \mathfrak { M } | ^ { \int ( 0 ) } = f ( 0 ) + f ( 0 ) \ _ { , } \ _ { . } \cdot ^ { \phantom { } } f ( 0 ) = 0 } \end{array} .$

（2） $\ L _ { \stackrel { . } { \gamma } } y = - \ L _ { x }  { } _ { , \ L _ { \stackrel { . } { \jmath } } } f ( 0 )  = f ( x ) + f ( - x ) = 0 \ L _ { , }$ $f ( - x ) = - f ( x )$ 1 ，故函数 $f ( x )$ 是 上的奇函数.

（3）任取 $x _ { 1 } , x _ { 2 } \in \mathrm { R }$ 且 ，则 .$\because f ( x _ { _ 2 } ) . . \ f ( x _ { _ 1 } )$   
$\begin{array} { r l } & { = f \left( x _ { 2 } - x _ { 1 } + x _ { 1 } \right) - f \left( x _ { 1 } \right) } \\ { } & { } \\ { } & { = f \left( x _ { 2 } - x _ { 1 } \right) + f \left( x _ { 1 } \right) - f \left( x _ { 1 } \right) } \\ { } & { } \\ { } & { = f \left( x _ { 2 } - x _ { 1 } \right) > 0 \ , } \end{array}$   
∴ .故 是 上的增函数.

$\ldots t \left( { \frac { 1 } { 2 } } \right) = 1 , \ldots t \left( 1 \right) = f \left( { \frac { 1 } { 2 } } + { \frac { 1 } { 2 } } \right) = f \left( { \frac { 1 } { 2 } } \right) + f \left( { \frac { 1 } { 2 } } \right) = 2 ,$ ∵ f(x)+ f(2+x)<2$\therefore f ( x ) + f ( 2 + x ) = f \left[ ( x + ( 2 + x ) \right] = f \left( 2 x + 2 \right) < f ( 1 ) .$

又由 $y = f ( x )$ 是定义在 $R$ 上的增函数，得 $2 x + 2 < 1$ ，解得 $x < - \ \frac { 1 } { 2 }$

# 四、函数图象及应用

函数的图象是函数的重要表示方法，它具有明显的直观性，通过函数的图象能够掌握函数重要的性质，如单调性、奇偶性等．反之，掌握好函数的性质，有助于函数图象正确地画出．函数图象广泛应用于解题过程中，利用数形结合解题具有直观、明了、易懂的优点

[典例 4]　设函数 $f ( x ) = x ^ { 2 } - 2 | x | - 1 ( - 3 \leq x \leq 3 ) .$

(1)证明：函数 $f ( x )$ 是偶函数；  
(2)画出这个函数的图象；  
(3)指出函数 $f ( x )$ 的单调区间，并说明在各个单调区间上 $f ( x )$ 的单调性；(4)求函数的值域

【解析】(1)证明：∵函数 $f ( x )$ 的定义域关于原点对称，且 $f ( - x ) { = } ( - x ) ^ { 2 } { - } 2 | { - } x | { - } 1$

$= x ^ { 2 } - 2 | x | - 1 = f ( x ) ,$ ，即 $f ( - x ) { = } f ( x )$ ， $\therefore f ( x )$ 是偶函数

(2)当 $0 \leqslant x \leqslant 3$ 时，  
$f ( x ) { = } x ^ { 2 } { - } 2 x { - } 1 { = } ( x { - } 1 ) ^ { 2 } { - } 2 .$   
当 $- 3 { \leqslant } x { \ < } 0$ 时， $f ( x ) { = } x ^ { 2 } { + } 2 x { - } 1 { = } ( x { + } 1 ) ^ { 2 } { - } 2$ .即 $\displaystyle { f ( x ) = { \overbrace { \left( x + 1 \right) ^ { 2 } } } - 2 ( - 3 \leq x < 3 ) }$   
根据二次函数的作图方法，可得函数图象如下图．  
(3)函数 $f ( x )$ 的单调区间为[－3，－1)，[－1,0)，[0,1)，[1,3]．  
$f ( x )$ 在区间[－3，－1)和[0,1)上单调递减，  
在[－1,0)和[1,3]上单调递增．

![](images/aad434a280af608582f1897989e5b456ea2791d148c06c29485687320cfdc6cd.jpg)

(4)当 $0 \leqslant x \leqslant 3$ 时，函数 $f ( x ) { = } ( x { - } 1 ) ^ { 2 } { - } 2$ 的最小值为－2，最大值为 $f ( 3 ) { = } 2$ ；当－ $3 { \leqslant } x { < } 0$ 时，函数 $f ( x ) { = } ( x { + } 1 ) ^ { 2 } { - } 2$ 的最小值为－2，最大值为 $\scriptstyle f ( - 3 ) = 2$ .故函数 $f ( x )$ 的值域为[－2,2].

# 五、幂函数的图象问题

对于给定的幂函数图象，能从函数图象的分布、变化趋势、对称性等方面研究函数的

定义域、值域、单调性、奇偶性等性质．注意图象与函数解析式中指数的关系，能够根据图象比较指数的大小

[典例 5]　如图是幂函数 $y = x ^ { a }$ ， $y = x ^ { b }$ ， $y { = } x ^ { c }$ ， $y = x ^ { d }$ 在第一象限内的图象，则a，b，c， $d$ 的大小关系为(　　)

![](images/44f658ed57dacbed4788bcf45cdc8ff8be85c69aba3e3ab218662d785576d799.jpg)

A.a<b<c<d B. $. a { < } b { < } d { < } c$ $C . b { < } a { < } c { < } d$ D.b<a<d<c

【答案】A

【解析】由幂函数的图象特征可知，在第一象限内直线 $x { = } 1$ 的右侧，图象从下到上，相应的指数由小到大．故选 A.

# 六、函数模型及其应用

建立恰当的函数模型解决实际问题的步骤：

(1)对实际问题进行抽象概括，确定变量之间的主被动关系，并用 $x$ ， $y$ 分别表示；

(2)建立函数模型，将变量 $y$ 表示为 $x$ 的函数，此时要注意函数的定义域；

(3)求解函数模型，并还原为实际问题的解

[典例 6]　已知 A， $B$ 两城市相距 $1 0 0 \mathrm { k m }$ ，在两地之间距离 $A$ 城市 $x \mathrm { k m }$ 的 $D$ 处修建一垃圾处理厂来解决A， $B$ 两城市的生活垃圾和工业垃圾．为保证不影响两城市的环境，垃圾处理厂与市区距离不得少于 $1 0 \mathrm { k m } ,$ .已知垃圾处理费用和距离的平方与垃圾量之积的和成正比，比例系数为0.25.若A城市每天产生的垃圾量为20 t， $B$ 城市每天产生的垃圾量为 $1 0 ~ \mathrm { t } .$ ．

(1)求 $x$ 的取值范围；  
(2)把每天的垃圾处理费用 $y$ 表示成 $x$ 的函数；  
(3)垃圾处理厂建在距离 A 城市多远处，才能使每天的垃圾处理费用最少？

【解析】(1)由题意可得 $x { \geqslant } 1 0 , 1 0 0 { - } x { \geqslant } 1 0 .$ .所以 $1 0 { \leqslant } x { \leqslant } 9 0$ .所以 $x$ 的取值范围为[10,90](2)由题意，得 $y = 0 . 2 5 [ 2 0 x ^ { 2 } + 1 0 ( 1 0 0 - x ) ^ { 2 } ]$ ，15即 $y = 2 \ x ^ { 2 } - 5 0 0 x + 2 5 0 0 0 ( 1 0 { \leq } x { \leq } 9 0 ) .$ ．$\exists y = { \frac { 1 5 } { 2 } } _ { x ^ { 2 } - 5 0 0 x + 2 5 0 0 0 } = { \frac { 1 5 } { 2 } } ( x - { \frac { 1 0 0 } { 3 } } ) ^ { 2 } + { \frac { 5 0 0 0 0 } { 3 } } ( 1 0 \leq x \leq 9 0 ) _ { \mathrm { { m } } }$ 100，则当 $\scriptstyle x = 3$ 时， $y$ 最小．100即当垃圾处理厂建在距离 $A$ 城市 $3 ~ \mathrm { \ k m }$ 时，才能使每天的垃圾处理费用最少