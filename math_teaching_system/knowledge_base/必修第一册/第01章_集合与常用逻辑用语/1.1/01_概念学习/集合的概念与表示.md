---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 23
source_file: L1-1-集合的概念与表示.md
title: L1-1-集合的概念与表示
type: problem_type
---

# 集合的概念与表示

一本一讲，共26讲（建议5个月学习）

10个知识点+5个题型+23个视频

每周一讲（建议学习时间90分钟）

![](images/2519433eb0f4679aee4c68f38b8c35209963723cf39210d755adcdf02cfafdf3.jpg)

# 视频内容研发团队

学而思优秀老师

学而思优秀老师和一线高级教师联合创作本书试题，并精心录制讲解视频学而思图书APP扫码即可观看

![](images/b6e7e303bdcd096c22fb7fe331015f84facfd32e166cc9093875d16ac70d95d0.jpg)

# 傅博宇 老师

毕业于北京大学元培学院  
网校和北大数院高考数学研究联合课题组成员；  
网校高中创新产品部负责人；  
荣获学而思网校“桃李满天下奖”“出类拔萃奖”等；腾讯网中国好老师；  
青少年教育导师认证；  
科学家长观体系的创立者

# 王侃老师

![](images/366a88a628b7fdbe75a9e54f3ef7babdb2ec34ea4a90856b8bb201e80e156250.jpg)

毕业于北京大学数学系  
学而思网校高中数学教研奠基人；  
学而思网校高中数学S级教师；  
荣获学而思网校“突出贡献奖” “桃李天下奖”等；擅长总结题型特点，提炼思想方法；  
擅长分层教学，因材施教

# 付恒岩 老师

![](images/38bdc14765aff236d82d6ef80fe58df5361b3f05668bc15baa2644006897691b.jpg)

毕业于大连理工大学  
网校高中部理科主讲岗后培训师；  
2020年荣获学而思网校最具魅力奖；  
2019年、2020年荣获学而思网校诲人不倦奖；2020年荣获学而思网校高考优秀评卷人；  
2021年担任新浪教育高考数学直播解析特邀嘉宾；“停课不停学”公益课高中数学主讲老师

# 武洪姣 老师

![](images/4e1095df139504d89d5e52a2ad5a6a011cdffcc15cace03cdb1107209a9d2564.jpg)

14年线上线下教学经验；  
学而思网校高中理科教研负责人；  
学而思高中数学特级教师;  
在教学的过程中擅长归纳题型，方法和技巧；  
在高中数学模块中最擅长讲解圆锥曲线和导数；  
无论你是从小数学不好，还是数学一直拔尖，都可以在武老师的课堂上收获很多

# 1级

# 集合的概念与表示

一本一讲，共26讲（建议5个月学习）

10个知识点 $+ 5$ 个题型 $+ 2 3$ 个视频每周一讲（建议学习时间90分钟）

2199-e+²  
= 2×2- e°+(8³）²  
二 4-1+4  
=7

# 图书在版编目（CIP）数据

学而思秘籍．高中数学思维突破1级/学而思教研中心编写组编著．--北京：现代教育出版社，2022.3ISBN 978-7-5106-8657-3

I. $\textcircled{1}$ 学…Ⅱ. $\textcircled{1}$ 学…Ⅲ. $\textcircled{1}$ 中学数学课－高中一教学参考资料IV. $\textcircled{1}$ G634

中国版本图书馆CIP数据核字（2022）第036149号

# 学而思秘籍高中数学思维突破1级

编著：学而思教研中心编写组  
出品人：陈琦  
选题策划：王春霞  
责任编辑：魏星顾林  
装帧设计：学而思教研中心设计组·于磊王琳  
出版发行：现代教育出版社  
地 址：北京市东城区鼓楼外大街26号荣宝大厦三层  
邮 编：100120  
电 话：010-64251036（编辑部）010-64256130（发行部）  
印开印字版印书定 刷：北京世纪恒宇印刷有限公司本： $8 8 9 \mathrm { m m } \times 1 1 9 4 \mathrm { m m }$ 1/16张：36数：724千字次：2022年3月第1版次：2022年3月第1次印刷号：ISBN 978-7-5106-8657-3价：239.00元（含视频课程）

# Planning Committee

# 学而思图书策划委员会

主 编：学而思教研中心编写组副主编：汪玲玲李奎廷执行主编： 张卡特　乔　巍　郭忠秀编 者： 武洪姣付恒岩　傅博宇成文波牛术强王侃徐强郑赢景肖龙　谭茗心　刘　坤　董宇喆沈思含宗倩

知识点睛 学百思华籍高中数学退维突验1额知识点晴  
梳理归纳知识，筑牢学科根基 偶要合的在意一个元素都在集合{中，且p(x)是它的 >一个特征性质，那么集合A可以表示为|xeJ|p（x)丨，这种用集合中  
提取关键信息，标注重要结论 述法 元素的特征性质来措述集合的方法称为特征性质描述法，简称描 学西思秘霜鹿中散学思组突破1级x的取值集合 模块1零点区间问题集合的代表元章元累的特杠性原考探虹 APP扫码观看本模块讲解视频使用自然语富表乐下列桑合： 知识与方法例题与练习全程跟老师高效学知识(1)|ε6N[1<s5}； (2)|xeRjI <x≤5|；(5)|x|x³ −1 =0,xe R} ; (6)|x e R|s² −1 =0}; 知识点晴(7)|x|²-1 =0);； (8)1(x,y) y ².地（函为（ （t》能（2)表示的元素是实数时，的取值范图可以略，其况不 （1）的元数时，的取值范用可省略，其情况不 则x时做这个函数的零点 注(3）描述法表示集合的方法不唯 上y）能分对于中，一般采用描述法表示，它的优点是形式简 上1的零点是x=1，而不是（1,0）.希龄零点的来深(1)代数法：求方程f(s)=0的实数根；（2）几何法：面出函数y=f（x）的图象，从图象上找出零点.零点与函数密象交点函数F(x）=f（x）-g（x）的零点就是方程f（x）=g（x）的实数根，也是函数y/（x）与x,"g（x)的图象交点的模坐标学点存在性定理若函数f（s）在闭区间[a,b]上的图象是一条连续不断的曲线，并且在区间嘲点的函数值荐号相反，即f(a）f(6）<0，则在区间（a,b）  
精讲精练 内，函数f(x)至少有一个零点，  
剖析典型例题，提升解题能力  
总结方法技巧，升华解题思维 预习第1请，集合的概念与示精讲精练拍照批改移判讨错国用自然语言表示下列集合：A|x∈Ry=3x-2},BE21,C=（(x,y)ly =3x -21. 学图思程线高中教学要相突破1版EE》精讲精练拍照批改秒判对错考点3：求函数零点个数21\*\*\*已知集合A=1,2,4集8[y则集的个数为（ B.5 C6 函数/（a）2²+-5的零点数为 B》 2\*\*\*函数f(x)=|x-2|-lnx在定义域内零点的个数为A B.1c. 2 D.3\*\*★BE 函数f（x)=s²-4x+5-2lnx的零点的个数为用州举法表示集合=meNz] （x2》  
APP扫码观看本模块  
讲解视频  
全程跟老师，高效学知识！  
由学而思资深老师对本模块知 10  
识、例题和练习进行系统讲解，

并归纳对应考点的解题技巧，总结解题方法，让学生能够举一反三，通过一道题，学会一类题，从而提升学生的解题能力，实现数学思维突破。

# 序言

66

# 亲爱的同学：

很开心能见到你，本书将带领你进入全新的领域。在过去的学习中，无论你是卓越还是略有失意，此刻将是新的征途的开始。你是否已准备好谱写新的辉煌？

在这里，你会知道许多著名数学家的趣事，你也会揭开数学神秘的面纱，了解数学之美。

数学家高斯说：“数学是一切科学的皇后。”数学是通往星辰大海的密钥，是国防科技的护盾，是我们脚下这片土地的未来。数学家华罗庚说过：“宇宙之大，粒子之微，火箭之速，化工之巧，地球之变，生物之谜，日用之繁，无处不用数学。”

# 对于本书的学习，给你一些建议：

第一，提前预习。“凡事预则立，不预则废。”提前预习，学习才会更有效。  
第二，APP扫码观看视频。积极思考，认真做笔记，总结解题方法、技巧。  
第三，及时复习。“温故而知新，可以为师矣。”不忘温习，方得始终。  
第四，及时完成测试。今朝有题今朝做，明朝又有明朝事。  
第五，整理错题，学会总结。聪明人知错就改，糊涂人有错就瞒。  
最后，青春的帆已扬起，只待你乘风破浪，勇往直前。

![](images/f2fad9fcb781d0bb2930499d3287e3ac743aa96d5d3c837f82817b55b13ea08d.jpg)

# 预习篇

第1讲集合的概念与表示· 提升篇第2讲集合的关系与运算 第11讲集合重难点专题第3讲解不等式 第12讲常用逻辑用语第4讲函数的概念与三要素 第13讲基本不等式第5讲函数的单调性（一） 第14讲函数三要素专题第6讲函数的奇偶性（一） 第15讲函数的单调性（二）第 $^ 7$ 讲指数幂运算与幂函数 第16讲函数的奇偶性（二）第8讲指数函数 第17讲抽象函数第9讲对数运算 第18讲指数幂运算与指数函数第10讲对数函数 第19讲对数运算与对数函数第20讲函数与方程第21讲恒成立与存在性问题第22讲三角函数基本概念与诱导公式第23讲三角函数的图象与性质  
模块1 集合的概念 2 第24讲三角公式的应用技巧  
模块2 集合的表示 8第25讲三角恒等变换重点题型第26讲正弦型函数的图象与性质参考答案

# 集合的概念与表示

# 直击课堂

<html><body><table><tr><td>知识模块</td><td>知识点</td><td>对应例题</td><td>星标统计</td></tr><tr><td rowspan="5">集合的概念</td><td>元素与集合的概念</td><td rowspan="2">例1</td><td rowspan="5">★9道题 ★★10道题 ★★★7道题 ★★★★1道题</td></tr><tr><td>集合中元素的特性</td></tr><tr><td>集合的分类</td><td rowspan="3">例2</td></tr><tr><td>空集</td></tr><tr><td>常见数集的符号 元素与集合的关系</td></tr><tr><td rowspan="4">集合的表示</td><td>列举法</td><td>例3</td></tr><tr><td>描述法</td><td>例4</td></tr><tr><td>图示法</td><td></td></tr><tr><td>区间法</td><td>例5</td></tr></table></body></html>

# 学习目标

$\textcircled{1}$ 了解集合的有关概念，知道常用数集及其记法.

$\textcircled{2}$ 理解并掌握元素与集合的关系.

$\textcircled{8}$ 理解并掌握集合的表示方法.

# 模块1集合的概念

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# $\textcircled{1}$ 元素与集合的概念

一般地,把一些能够确定的不同的对象看成一个整体，就说这个整体是由这些对象的全体构成的集合(或集).构成集合的每个对象叫做这个集合的元素(或成员).

# $\textcircled { 2 }$ 集合中元素的特性

(1)确定性：$\textcircled{1}$ 确定性是判断是否可以构成集合最重要的标准;  
$\textcircled{2}$ 判断以客观事实为标准.  
(2)互异性：给定的集合，集合中的元素一定互不相同。  
(3)无序性：集合中的元素不区分顺序.顺序不同，元素相同的集合是同一个集合.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例1

# （1）★

现有以下说法,其中正确的是(

$\textcircled{1}$ 接近于0的数的全体构成一个集合;  
$\textcircled{2}$ 正方体的全体构成一个集合；  
$\textcircled{3}$ 未来世界的高科技产品构成一个集合;  
$\textcircled{4}$ 不大于3的所有自然数构成一个集合.

A $\textcircled{1} \textcircled{2}$ B. $\textcircled{2} \textcircled{3}$ C. $\textcircled{3} \textcircled{4}$ D. ②④

# 学习笔记

# （2）★

设集合A是以 $1 , k ^ { 2 } , 2 k \mathrm { ~ - ~ } 1$ 为元素的集合，则实数 $k$ 的取值范围 是

# 学习笔记

# 变式1

# （1）★

考察下列每组对象,能组成一个集合的是(

$\textcircled{1}$ 一中高一年级聪明的学生；  
$\textcircled{2}$ 直角坐标系中横、纵坐标相等的点;  
$\textcircled{3}$ 不小于3的正整数;  
$\textcircled{4} \sqrt { 3 }$ 的近似值.

A $\textcircled{1} \textcircled{2}$ B. $\textcircled{3} \textcircled{4}$ C. ②③ D. ①③

# 学习笔记

# （2）★★★

已知集合A含有三个元素 $a - 3 , 2 a - 1 , a ^ { 2 } - 4$ ,若-3是集合A中的元素,则实数 ${ \mathbf { } } a = { \mathbf { } } .$

# 学习笔记

# 知识点睛

# $\textcircled { 8 }$ 集合的分类

集合可以根据它含有的元素的个数分为两类：含有有限个元素的集合叫做有限集；含有无限个元素的集合叫做无限集.

# $\textcircled{4}$ 空集

我们把不含有任何元素的集合叫做空集，记作

# $\textcircled { 5 }$ 常用数集的符号

数学中一些常用的数集及其记法：

<html><body><table><tr><td colspan="6">常用数集</td></tr><tr><td>集合</td><td>自然数集</td><td>正整数集</td><td>整数集</td><td>有理数集</td><td>实数集</td></tr><tr><td>符号</td><td>N</td><td>N*或N+</td><td>Z</td><td>Q</td><td>R</td></tr></table></body></html>

# $\textcircled{1}$ 元素与集合的关系

集合通常用英文大写字母 $A , B , C$ ，…来表示,元素通常用英文小写字母 $a , b , c$ ,…来表示.如果 $^ { a }$ 是集合A的元素，记作 $a \in A$ ，读作“ $\mathbf { \Omega } _ { a }$ 属于 $A ^ { \prime \prime }$   
如果 $^ { a }$ 不是集合A的元素，记作 $a \not \in A$ ，读作“ $^ { a }$ 不属于 $A ^ { \prime \prime }$

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例2★

用 $\in$ ，填空.

-1 N； |-3| N\*；   
1-2 Z； 3.14 Q；   
T Q； 5 Q；   
√2 R. 2

# 学习笔记

# 变式2

# （1）★

下列选项正确的是( ）

A. $0 \in \mathbf { N } \mathbf { \Psi } ^ { * }$ B.TR C.1Q D.0∈Z

# 学习笔记

# （2）★★★★

下列叙述中正确的个数是( $\textcircled{1}$ 若 $- \mathbf { \nabla } a \in \mathbf { Z }$ ，则 ${ a \in \mathbf { Z } }$ $\textcircled{2}$ 若 $- \boldsymbol { a } \not \in \mathbf { N }$ ，则 $\ b { a } \in \mathbf { N }$ $\textcircled{3} a \in \mathbf { Z }$ ，若 $- \boldsymbol { a } \not \in \mathbf { N }$ ，则 $a \in \mathbf { N }$ $\textcircled{4} a \in \mathbf { Z }$ ，若 $\boldsymbol a \in \mathbf { N }$ ，则 $- a \not \in \mathbf { N } .$

A.0 B.1 C.2 D.3

# 学习笔记

# 模块2集合的表示

# APP扫码观看本模块讲解视频

知识与方法例题与练习 全程跟老师 高效学知识

# 知识点睛

# $\textcircled{1}$ 集合的表示方法—列举法

我们可以把集合的所有元素都列举出来,写在大括号“}}”内,用“,”分隔.这种表示集合的方法叫做列举法.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

例3 用列举法表示下列集合：

（1大于2且小于10的偶数组成的集合；

（2） 满足 $2 x - 7 < 0$ 的正整数解组成的集合;

③方程 $\left( x - 1 \right) \left( x + 2 \right) { } ^ { 2 } = 0$ 的解组成的集合;

（4）★★ 二元方程 $x + y = 6 \left( x \in \mathbf { N } ^ { * } , y \in \mathbf { N } \right)$ 的解组成的集合.

# 学习笔记

# 变式3

# （1）★

下列集合表示正确的是(

A.{2,4} B.{2,4,4} C.(1,2,3) D.高个子男生

# 学习笔记

# （2）★★

把方程 $x ^ { 2 } - 3 x + 2 = 0$ 的解集,用列举法表示为(

A.{1,2} B. $\{ x = 1 , 2 \}$ C. $\left\{ x ^ { 2 } - 3 x + 2 = 0 \right\}$ D. (1,2)

# 学习笔记

# 知识点睛

# 集合的表示方法—描述法

如果集合A中的任意一个元素 $x$ 都在集合 $I$ 中，且 $p \left( x \right)$ 是它的一个特征性质,那么集合A可以表示为 $\{ x \in I | p ( x ) \}$ ,这种用集合中元素的特征性质来描述集合的方法称为特征性质描述法，简称描述法.

$$
\begin{array} { c } { \Big \downarrow } \\ { \{ x \in I \vert p ( \underset { \mathbb { R } } { x } ) \} } \\ { \mathcal { I } ^ { } } \end{array}
$$

集合的代表元素元素的特征性质

# 思考探究

使用自然语言表示下列集合：

(1) $\{ x \in \mathbf { N } \mid 1 < x { \leqslant } 5 \} \ $ (2) $\{ x \in \mathbf { R } \ | 1 < x \leqslant 5 \}$ (3) $\{ \ : y \in \mathbf { N } \ : | 1 < y \leqslant 5 \ : \} \ : ;$ (4) $\{ y \in \mathbf { N } \mid 1 < x { \leqslant } 5 \}$ (5) $\{ x \mid x ^ { 2 } - 1 = 0 , x \in \mathbf { R } \} \ ;$ (6) $\{ x \in \mathbf { R } \ | x ^ { 2 } - 1 = 0 \}$ (7) $\{ x \vert x ^ { 2 } - 1 = 0 \} \ ;$ (8) $\{ \ : ( x , y ) \ : | y = x ^ { 2 } \ : \} .$

# 注意

(1）集合的描述与宇母的选取无关。(2)表示的元素是实数时，数的取值范围可以省略，其他情况不能省略(3)描述法表示集合的方法不唯一。（4)对于无限集，一般采用描述法表示，它的优点是形式简洁，能充分体现集合中元素的特征.

# 精讲精练

# 拍照批改秒判对错

# 例4

# （1）★★

用自然语言表示下列集合： $A = \left\{ x \in \mathbf { R } \left| y = 3 x - 2 \right. \right\}$ ， $B = \left\{ \gamma \in \mathbf { R } \vert y = 3 x - \right.$ 2， $C = \left\{ \left( x , y \right) \big | y = 3 x - 2 \right\} .$

# 学习笔记

# （2）★★★

已知集合 $A = \{ 1 , 2 , 4 \}$ ，集合 $B = \left\{ z \left| z = { \frac { x } { y } } , x \in A , y \in A \right. \right\}$ ,则集合 $B$ 中元素的个数为（ ）

A.4 B.5 C.6 D.7

# 学习笔记

# （3）★★★

用列举法表示集合 $M = \left\{ m \in { \bf N } \left| { \frac { 1 0 } { m + 1 } } \in { \bf Z } \right. \right\} =$

# 学习笔记

# 变式4

# （1）★★

用自然语言表示下列集合： $A = \left\{ x \in \mathbf { R } \left| y = x ^ { 2 } + 1 \right. \right\}$ ， $B = \left\{ \gamma \in \mathbf { R } \vert y = x ^ { 2 } \ + \right.$ 1 $C = \left\{ \left( x , y \right) \big | y = x ^ { 2 } + 1 \right\} .$

# 学习笔记

# （2）★★

集合 $\{ { \bf \Pi } ( x , y ) \mid x y { \bf \geq } 0 , x \in { \bf R } , y \in { \bf R } \}$ 是指（ ）

A．第一象限内的所有点  
B．第三象限内的所有点  
C．第一象限和第三象限内的所有点  
D.不在第二象限、第四象限内的所有点

# 学习笔记

# （3）★★★

集合 $M = \left\{ a \left| { \frac { 6 } { a + 1 } } \in \mathbf { N } \right. \right.$ 且 $a \in \mathbf { Z } \Big \}$ ,用列举法表示集合 $M =$

# 学习笔记

# （4）★★★

已知集合 $A = \{ 0 , 1 , 2 \}$ $B = \left\{ z \left| z = x + y , x \in A , y \in A \right. \right\}$ ，则 $\boldsymbol { B } = \left( \begin{array} { l l l } { \begin{array} { r l } \end{array} } & { \begin{array} { r l } \end{array} } \end{array} \right)$

A $\{ 0 , 1 , 2 , 3 , 4 \}$ B.{0,1,2} C. {0,2,4} D.{1,2}

# 学习笔记

# 知识点睛

# $\textcircled { 8 }$ 集合的表示方法—图示法

我们常用平面内一条封闭曲线的内部表示一个集合，这种方式可以很形象地表示出集合之间的关系,这种图形通常叫做维恩（Venn)图.

![](images/bf8237c5314e663b698b76613f93ed22ab236eb7d2c47a448b2f58e5b4cb4f19.jpg)

# 4集合的表示方法—区间法

设 $a , b \in \mathbb { R }$ ，且 $a < b$ ,我们规定：

(1)满足 $a \leqslant x \leqslant b$ 的实数 $x$ 的集合叫做闭区间，表示为 $[ a , b ]$ ：

(2)满足 $a < x < b$ 的实数 $x$ 的集合叫做开区间,表示为 $( a , b )$

(3)满足 $a < x \leqslant b$ 或 $a \leqslant x < b$ 的实数 $x$ 的集合叫做半开半闭区间，分别表示为 $( a , b ]$ 或 $[ a , b )$ ：

这里的实数 $^ { a }$ 与 $b$ 都叫做相应区间的端点.

(4)实数集 $\mathbf { R }$ 可以用区间表示为 $( \mathbf { \nabla } - \infty , + \infty )$ ，“ $\infty$ ”读作“无穷大”，6 －8”读作“负无穷大”，“ $+ \infty$ ”读作“正无穷大”,这样我们可以把满足$A = \left\{ x \mid x > a \right\}$ 与 $B = \left\{ x \left| x \leqslant a \right. \right\}$ 的集合表示为 $( a , + \infty )$ 与 $( \mathrm { ~ - ~ } \infty \mathrm { ~ } , a ]$

<html><body><table><tr><td>定义</td><td>数轴表示</td><td>符号</td><td>名称</td></tr><tr><td>{xla≤x≤b}</td><td>a</td><td>[a,b]</td><td>闭区间</td></tr><tr><td>{x|a&lt;x&lt;b}</td><td>→ a</td><td>（a，b）</td><td>开区间</td></tr><tr><td>{xla≤x&lt;b}</td><td>+ → a bx</td><td>[a,6)</td><td>半开半闭区间</td></tr><tr><td>{xla&lt;x≤b}</td><td>→ a 6x</td><td>(a,b]</td><td>半开半闭区间</td></tr><tr><td>{x|x≥a}</td><td>→ a x</td><td>[a,+∞）</td><td></td></tr><tr><td>{x|x≤a}</td><td>+ → x a</td><td>（18,a]</td><td></td></tr><tr><td>{x|x&gt;a}</td><td>6 → a x</td><td>（a,+∞）</td><td></td></tr><tr><td>{x|x&lt;a}</td><td>1a</td><td>（18,a）</td><td></td></tr></table></body></html>

# 精讲精练

# 拍照批改秒判对错

例5 用区间表示集合.

1 $\{ x \mid \mid x \mid \leqslant 1 \}$

（2）★★ $\{ y \vert y = { \sqrt { x } } + 2 \}$ ；

（3）★★ $\{ y \vert y = - x ^ { 2 } + 2 x \}$ ;

【4）★★ $\{ y \vert y = x ^ { 2 } - 2 x + 1 , x > 0 \}$

# 学习笔记

# 变式5

# （1）★★

集合 $\textstyle { \left\{ { \boldsymbol { y } } \right| } _ { \boldsymbol { y } } = - \boldsymbol { x } ^ { 2 } + 3 \boldsymbol { x } - 1  \left\{ \begin{array} { r l r } \end{array} \right.$ 用区间表示为

# 学习笔记

# （2）★★★

不等式 $\left( 2 x - 3 \right) \left( 6 - x \right) < 0$ 的解集用区间表示为

# 学习笔记

# （3）★★★

不等式 $x ^ { 2 } - 4 x - 5 \leqslant 0$ 的解集用区间表示为

# 学习笔记

# 学习总结

![](images/5f6010a6c02065d79e7640a01da453319519b420c4f1324990b0f0f80eb23c63.jpg)

# 提升篇你会遇见

（预习篇·P3,例1(2)）设集合 $A$ 是以 $1 , k ^ { 2 } , 2 k - 1$ 为元素的集合，则实数 $k$ 的取值范 围是

(提升篇)已知集合 $A$ ，若 $a \in A , { \frac { 1 } { 1 - a } } \in A$ ，求满足什么条件时，A中至少有三个元素.

【点石成金】以上两题都考查了集合中元素个数的问题.对比发现，预习篇题目比较简单，已经具体给出集合中元素，而提升篇题目则比较抽象，需要我们先找到集合中元素的具体形式，然后再根据集合中元素的性质，求出参数满足的条件.这类题目预习篇并没有见过，是我们提升篇解决的重难点，期待我们提升篇的学习吧！

# 学而思秘籍系列图书|数学

# 思维培养

![](images/5b7d657815eb7b5970896a5fa8740b7b485f92381bc31465253be6eae44a62bb.jpg)

# 小学秘籍系列

学而思积淀近20年教研经验，培养受益一生的能力。

# 思维提升

![](images/2aa2a35ed3a680fd36f12ab72e825fd3a1e491fc9294b14a9f45ea8238fab9a0.jpg)

# 初中秘籍系列

全面覆盖初中基础知识和重难点，帮助学生夯实基础，拓展认知。

# 思维突破

![](images/935707d9788226cc61bc5b8090af2ddd0764905a8fe4ac8afdd96d8e13ee7854.jpg)

# 高中秘籍系列

全面覆盖高中基础知识和重难点，帮助学生提升能力，突破思维。

# 学而思秘籍系列图书|语文

# 提升素养

![](images/7ed21a41ec3052c97b961e5af3b5441995bf52c85bd95f836d14f0a7e7f18eaf.jpg)

# 小学秘籍系列

5大模块+2条主线，能力与素养双向提升。

能力训练

![](images/b14eaa832ed2d6849ac078725c168caf15e21cd64d448001273a2a59cb75585a.jpg)

# 初中秘籍系列

融合课改四大核心素养，培养爱阅读、 善写作、勤思考、会学习的学生。

# 创新体系|真题研习

思维创新 大大通关N用数学

# 思维创新大通关数学

攻克数学思维难题，通向理想中学。

# 大家一起来“升级

# 参与方式

您在使用本书时，如有任何疑问或对图书有任何建议，请扫码进行反馈，并查看反馈采纳结果。

# 奖励

您的反馈一经采纳，我们将会送出总价值35元的图书抵扣券（相同内容的反馈，依据反馈时间，奖励前三位）。请扫码关注公众号，并在对话框中发送反馈时填写的手机号，领取抵扣券。

![](images/d980bbc2c308db2747ea5ec5f7706b8a25b7b4166a326eae7bc02bf6c02555cf.jpg)

# 合理规划学习时间

先自己定一个目标，即制定半年学习规划。

2 再将目标细化到每一周，每周学习一本（平均5个考点）。

3 配套课堂巩固的练习， 让学习更有效！

![](images/61ca463cf78d937f94aec6387cc78284669ca0790dd86a82e04e4d0db7f85dcc.jpg)

![](images/8d82ce1ce9802d1cee500c3f4c7ed59127e45918f46d879b39c9276b2358b131.jpg)  
·共6级·每级17-26讲