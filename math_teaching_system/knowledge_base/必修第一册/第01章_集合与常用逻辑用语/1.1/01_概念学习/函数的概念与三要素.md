---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 15
source_file: L1-4-函数的概念与三要素.md
title: L1-4-函数的概念与三要素
type: problem_type
---

# 函数的概念与三要素

一本一讲，共26讲（建议5个月学习）

5个知识点+6个题型+28个视频

每周一讲（建议学习时间90分钟）

![](images/40a33b4268eb7d558a3de19f4c90ea527e1446514b4484088005c5576b128af8.jpg)

# 视频内容研发团队

学而思优秀老师

学而思优秀老师和一线高级教师联合创作本书试题，并精心录制讲解视频学而思图书APP扫码即可观看

![](images/4c0078e22396be601e0c1d9e414b25a760ab8b6be8826435fa979dddbb8c926c.jpg)

# 傅博宇 老师

毕业于北京大学元培学院  
网校和北大数院高考数学研究联合课题组成员；  
网校高中创新产品部负责人；  
荣获学而思网校“桃李满天下奖”“出类拔萃奖”等；腾讯网中国好老师；  
青少年教育导师认证；  
科学家长观体系的创立者

# 王侃老师

![](images/0bae371165ac420f041ad5040bb9d587c31696de259bf456b14317c3294da9ea.jpg)

毕业于北京大学数学系  
学而思网校高中数学教研奠基人；  
学而思网校高中数学S级教师；  
荣获学而思网校“突出贡献奖” “桃李天下奖”等；擅长总结题型特点，提炼思想方法；  
擅长分层教学，因材施教

# 付恒岩 老师

![](images/a85019fd801e708d0e7fca899b8ec8da1caa6ca7966966e49ffaa638dd2a95c1.jpg)

毕业于大连理工大学  
网校高中部理科主讲岗后培训师；  
2020年荣获学而思网校最具魅力奖；  
2019年、2020年荣获学而思网校诲人不倦奖；2020年荣获学而思网校高考优秀评卷人；  
2021年担任新浪教育高考数学直播解析特邀嘉宾；“停课不停学”公益课高中数学主讲老师

# 武洪姣 老师

![](images/a1cc9a26d4837a41bac727c0f97370f9733063af0328252f83371e86453703cc.jpg)

14年线上线下教学经验；  
学而思网校高中理科教研负责人；  
学而思高中数学特级教师;  
在教学的过程中擅长归纳题型，方法和技巧;  
在高中数学模块中最擅长讲解圆锥曲线和导数；  
无论你是从小数学不好，还是数学一直拔尖，都可以在武老师的课堂上收获很多

# 1级

# 函数的概念与三要素

一本一讲，共26讲（建议5个月学习）

5个知识点 $+ 6$ 个题型 $+ 2 8$ 个视频每周一讲（建议学习时间90分钟）

210g9-eln²+382  
= 2×2-e°+(8)²  
二 4-1+4  
=7

# 预习篇

第1讲集合的概念与表示 提升篇第2讲集合的关系与运算第11讲集合重难点专题第3讲解不等式第12讲常用逻辑用语第4讲函数的概念与三要素·第13讲基本不等式第5讲函数的单调性（一） 第14讲函数三要素专题第6讲函数的奇偶性（一） 第15讲函数的单调性（二）第 $7$ 讲指数幂运算与幂函数 第16讲函数的奇偶性（二）第8讲指数函数 第17讲抽象函数第9讲对数运算 第18讲指数幂运算与指数函数第10讲对数函数 第19讲对数运算与对数函数第20讲函数与方程第21讲恒成立与存在性问题第22讲三角函数基本概念与诱导公式第23讲三角函数的图象与性质  
模块1 函数的概念 2 第24讲三角公式的应用技巧  
模块2 函数的三要素第25讲三角恒等变换重点题型第26讲正弦型函数的图象与性质参考答案

# 函数的概念与三要素

# 直击课堂

<html><body><table><tr><td>知识模块</td><td>知识点</td><td>对应例题</td><td>星标统计</td></tr><tr><td rowspan="2">函数的概念</td><td rowspan="2">函数的概念</td><td>例1</td><td rowspan="6"></td></tr><tr><td>例2</td></tr><tr><td rowspan="5">函数的三要素</td><td>函数的定义域</td><td>例3</td><td rowspan="4">★6道题 ★★12道题 ★★★7道题 ★★★★1道题</td></tr><tr><td>函数的值域</td><td>例4</td></tr><tr><td>函数的表示方法</td><td>例5</td></tr><tr><td>相等函数</td><td>例6</td></tr></table></body></html>

# 学习目标

$\textcircled{1}$ 了解函数定义，能用集合与对应的语言来刻画函数，体会对应关系在刻画函数概念中的应用.

$\textcircled{2}$ 能根据实际问题情境选择恰当的方法表示一个函数以获取有用的信息，培养学生灵活运用知识的能力.

$\textcircled{6}$ 初步体会运用函数知识解决实际问题的方法.

$\textcircled{4}$ 体会数形结合思想在理解函数概念中的重要作用，在图形变化中感受数学的直观美.

# 模块1函数的概念

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# 函数的概念

设集合A是非空的数集,对于 $A$ 中的任意实数 $_ x$ ，按照确定的对应关系 $f ,$ 都有唯一确定的实数值 $y$ 与它对应,则这种对应关系叫做集合 $A$ 上的一个函数.记作 $y = f ( x ) , x \in A .$ 也常写作函数 $f$ 或函数 $f ( x )$ ,其中 $_ x$ 叫做自变量， $x$ 的取值范围 $A$ 叫做函数的定义域；与 $x$ 的值对应的 $y$ 值叫做函数值,函数值的集合 $\{ f ( x ) ~ | x \in A \}$ 叫做函数的值域.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例1

# （1）

下列几组对应关系中，哪几个是函数？

![](images/8b6048b99ca7607ab2057dea2a04a3cc26c3679d9fd7221cb9b4115b3f021cf2.jpg)

# 学习笔记

# （2）★

根据你对函数概念的理解,下列曲线中， $\mathcal { I }$ 不是 $_ x$ 的函数的是( ）

![](images/d902b9b16d980812bd7a1b562029391cc594bbea1c0148201793f486f6b69cdb.jpg)

![](images/39c41f1ca38851f0bbc0040cfa788fd382aa7f8bee6e3087f5b27ee12d12699a.jpg)

# 学习笔记

# 变式1

# （1）★

如图所示，不可能表示函数的是(

![](images/8d97976c65cf2788301840af562a346f0b8e4cb2e6ac93d7a7f7e189f3a126a4.jpg)

# 学习笔记

# （2）★★

由下列各式能确定 $y$ 是 $x$ 的函数的是( 1

A. $x ^ { 2 } + y ^ { 2 } = 1$ B. $x ^ { 2 } - y + 3 = 0$ C. $y = { \sqrt { x - 3 } } + { \sqrt { 2 - x } } + 2$ D.以上都不是

# 学习笔记

# 例2★★

已知函数f（x）=√x+².

$( 1 ) f ( 1 ) = \_ f ( 4 ) = \_ \qquad \cdot$ (2)当 $a > 0$ 时 $, f ( a ) = \mathrm { ~ , ~ } f ( a + 1 ) = \mathrm { ~ \small ~  ~ } .$

# 学习笔记

# 变式2

# (1）★

设函数 $f ( x ) = x ^ { 2 } - 3 x + 3$ ，则 $f ( a ) - f ( \mathbf { \theta } - a )$ 等于（ ）

A.0 B. $- 6 a$   
C. $2 a ^ { 2 } + 2$ D. $2 a ^ { 2 } - 6 a + 2$

# 学习笔记

# （2）★★

$f ( x ) = { \frac { x - 1 } { x + 1 } }$ 試则 $f ( x ) \ + f \left( { \frac { 1 } { x } } \right) = ($

A. （202 $\frac { 1 - x } { 1 + x }$ B. $\frac { 1 } { x }$ C.1 D.0

# 学习笔记

# 模块2函数的三要素

# APP扫码观看本模块讲解视频

1知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# $\textcircled{1}$ 函数的定义域

自变量的取值范围叫定义域.

# 注意

定义域是自变量的取值范围,必须写成集合的形式.

目前涉及的函数定义域的限制主要有：

D分母不为零：

偶次被开方数非负；

非零实数的零次方才有意义。

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例3

# （1）★★

函数y=√16-x² 的定义域是(

A $[ \ - 4 , 0 ) \cup ( 0 , 4 ]$ B.[-4,4]C. $( \ - \infty \ , - 4 ] \cup [ 4 \AA , + \infty \ )$ D.[-4,0）U[4，+∞）

# 学习笔记

# （2）★★

函数 $y = \sqrt { 1 - x ^ { 2 } } - \sqrt { x ^ { 2 } - 1 }$ 的定义域是

# 学习笔记

# （3）★★★

函数 $f ( x ) = \left( x - { \frac { 1 } { 2 } } \right) ^ { 0 } + { \frac { | x ^ { 2 } - 1 | } { \sqrt { x + 2 } } }$ +²-1的定义域为(

A $\left( \ - 2 , { \frac { 1 } { 2 } } \right)$ B. $( \ - 2 , + \infty \ )$ C $\left( \ - 2 , { \frac { 1 } { 2 } } \right) \cup \left( { \frac { 1 } { 2 } } , + \infty \right)$ D $\left( { \frac { 1 } { 2 } } , + \infty \right)$

# 学习笔记

# 变式3

# （1）★★

函数 $y = { \sqrt { 2 - x } } + { \frac { 1 } { \sqrt { x + 1 } } }$ 的定义域是(

A.（-1,2] B.[-1,2] C. (-1,2) D.[-1,2)

# 学习笔记

# （2）★★★

函数y=(x-1）° 的定义域是

# 学习笔记

# 知识点睛

# 函数的值域

函数 $\scriptstyle y = f ( x )$ 中,函数值的集合 $\{ f ( x ) ~ | x \in A \}$ 叫做函数的值域.部分常见函数的值域

<html><body><table><tr><td></td><td>一次函数</td><td>二次函数 f（x）=kx+b(k&gt;0）|f（x）=ax²+bx+c(a&gt;0）</td><td>反比例函数 f（x）=（k&gt;0） x</td></tr><tr><td>图象</td><td>0 X</td><td>0 X</td><td>X C</td></tr><tr><td>定义域以及 对应的值域</td><td>定义域：R 值域：R</td><td>定义域：R 值域：( +8 4a (4ac-b²</td><td>定义域：（-8,0)U(0，+∞） 值域：（-8,0)U(0，+8）</td></tr></table></body></html>

![](images/c516d81454b7ce7397a8f3661048126ffabbf25b58c75842d27de445975df544.jpg)

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例4

# （1）

函数 $f ( x ) = x + 1 , x \in \{ - 1 , 1 , 2 \}$ 的值域是( ）

A. 0,2,3 B.0≤y≤3 C. {0,2,3} D.[0,3]

# 学习笔记

# （2）★

若 $y = x ^ { 2 } - 2 x - 3 , x \in ( - 1 , 2 ]$ ,则函数的值域为(

A.[-4,5] B.(-4,5) C.（-4,5] D.[-4,0)

# 学习笔记

# （3）★★

函数 $\scriptstyle \gamma = { \frac { 1 } { \displaystyle x ^ { 2 } + 1 } }$ 的值域是(

A $( \mathbf { \partial } - \infty , - 1 )$ B.（0,+∞） C.[1,+∞） D.(0,1]

# 学习笔记

# （4）★★

函数 $y = \sqrt { 5 + 4 x - x ^ { 2 } }$ 的值域是

# 学习笔记

# 变式4

# (1）

函数 $f ( x ) = x ^ { 2 } - x$ 的定义域为{0,1,2},则值域为

# 学习笔记

# （2）★★★

函数 $y = \vert x + 1 \vert + \vert x - 1$ |的值域为(

A $( 0 , + \infty )$ B.（2,+∞） C.[0,+∞） D.[2,+∞）

# 学习笔记

# （3）★★★★

函数 $y = { \frac { 1 } { \sqrt { - x ^ { 2 } - 6 x - 5 } } }$ 的值域为( ）

A $\left[ { \frac { 1 } { 2 } } , + \infty \right]$ B $\left[ { \frac { 1 } { 3 } } , + \infty \right]$ c.[、+） D.[,+∞0）

# 学习笔记

# 知识点睛

# $\textcircled{8}$ 函数的表示方法

函数的表示方法有三种：

(1)列表法：列出自变量与对应函数值的表格来表达两个变量之间的关系的方法.(2)图象法：把一个函数定义域内的每个自变量 $x$ 的值和它对应的函数值 $f ( x )$ 构成的有序实数对 $( x , f ( x ) )$ 作为点的坐标,所有这些点的集合就称为函数 $\scriptstyle { y = f ( x ) }$ 的图象,即 $F = \{ P ( x , y ) | y =$ $f ( x ) , x \in A \} .$ (3)解析式法：用代数式(或解析式)表示两个变量之间的函数对应关系的方法,如 $y =$ $2 x - 6 .$

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例5

# （1）★★

已知 $f ( x - 1 ) = x ^ { 2 } + 6 x$ ，则 $f ( x )$ 的表达式是( ）

A. $f ( x ) = x ^ { 2 } + 4 x - 5$ B. $f ( x ) = x ^ { 2 } + 8 x + 7$   
C. $f ( x ) = x ^ { 2 } + 2 x - 3$ D. $f ( x ) = x ^ { 2 } + 6 x - 1 0$

# 学习笔记

# （2）★★★

已知 $f ( { \sqrt { x } } + 1 ) = x + 2 { \sqrt { x } }$ ，则 $f ( x ) = ( \qquad )$

A. $x ^ { 2 } - 1$ B. $x ^ { 2 } + 1$   
C. $x ^ { 2 } + 1 \left( x \geqslant 1 \right)$ D. $x ^ { 2 } - 1 \left( x \geqslant 1 \right)$

# 学习笔记

# 变式5

# （1）★★★

已知 $f ( x - 1 ) = x ^ { 2 } + 4 x - 5$ ，则 $f ( x )$ 的表达式是(

A. $f ( x ) = x ^ { 2 } + 6 x$ B. $f ( x ) = x ^ { 2 } + 8 x + 7$ （204 C. $f ( x ) = x ^ { 2 } + 2 x - 3$ D. $f ( x ) = x ^ { 2 } + 6 x - 1 0$

# 学习笔记

# （2）★★★

已知 $f ( { \sqrt { x - 3 } } ) = x - 2$ ，则 $f ( x ) = ( \qquad )$

A. $x ^ { 2 } + 1 \left( x \geqslant 0 \right)$ B. $x ^ { 2 } + 1 \left( x \in \left[ \ - 3 , 0 \right] \right)$   
C. $x ^ { 2 } + 2 ( x { \geqslant } 0 )$ D. $x ^ { 2 } + 2$

# 学习笔记

# 知识点睛

# 相等函数

当两个函数的定义域与对应关系相同时，则两个函数相等.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例6★★

下列各组函数表示同一函数的是(

A. $f ( x ) = { \sqrt { x ^ { 2 } } }$ ，， $\varrho ( { x } ) = ( \sqrt { x } ) ^ { 2 }$ B. $f ( x ) = x , g ( x ) = \sqrt [ 3 ] { x ^ { 3 } }$ C. $f ( x ) = 1 , g ( x ) = x ^ { 0 }$ D.f(x）=x+1,g(x）=²-1

# 学习笔记

# 变式6

# （1）★★

下列各组函数是相等函数的一组是( ）

A.f（x）=x+2,g（x）=2²-4 B. $f ( x ) = \left( x - 1 \right) ^ { \circ } , g ( x ) = 1$   
C. $f ( x ) = |  x | , g ( x ) = \sqrt { x ^ { 2 } }$ （204 D. $f ( x ) = { \sqrt { - 2 x ^ { 3 } } } , g ( x ) = x \ { \sqrt { - 2 x } }$

# 学习笔记

# （2）★★

下列各组函数是相等函数的为(

A.f（x）=x+2,g(x）=²-4 $\mathrm { B } . \ f ( x ) = ( x - 1 ) ^ { 2 } , g ( x ) = x - 1$ C. $f ( x ) = x ^ { 2 } + x + 1 , g ( t ) = t ^ { 2 } + t + 1 \mathrm { D . } f ( x ) = \sqrt { x ^ { 2 } } , g ( x ) = \sqrt [ 3 ] { x ^ { 3 } }$

# 学习笔记

# 学习总结

![](images/7325897c3c665ea3c62ee9823bb2a2d1cd33f21dcf5bd14d76cfbf364f1d3186.jpg)

# 提升篇你会遇见

（预习篇·P12,例4(3)）函数 $y = \frac { 1 } { x ^ { 2 } + 1 }$ 的值域是(

A.（-∞,-1） B. $( 0 , + \infty )$ C.[1,+∞） D.(0,1]

(提升篇)已知函数 $y = \frac { x + 1 } { x ^ { 2 } + 3 } ( a > 0 , 0 \leqslant x \leqslant a )$ ，若函数 $f ( x )$ 的值域 为 $\left[ \frac { 1 } { 3 } , \frac { 1 } { 2 } \right]$ ，则实数 $^ { a }$ 的取值范围是

【点石成金】以上两题都考查了分式值域问题，预习篇题目可以直接按照逐层求值域方法来求，而提升篇题目较为复杂，已知分式函数值域，反求参数的取值范围.这是我们提升篇解决的重难点，一起期待提升篇的学习吧！

# 学而思秘籍系列图书|数学

# 思维培养

# 思维提升

# 思维突破

![](images/3b4af2596caf05cfd9bb8f0d38261477ccc61c5efb3ef9093a5c5807346595d8.jpg)

# 小学秘籍系列

学而思积淀近20年教研经验，培养受益一生的能力。

![](images/2bc627a1832ec28e1b22745dafb4f02d68e2d9cb75771c273fc394250cee33f7.jpg)

# 初中秘籍系列

全面覆盖初中基础知识和重难点，帮助学生夯实基础，拓展认知。

学而思秘籍 集合的5.95高中数学思维突破++ @....

# 高中秘籍系列

全面覆盖高中基础知识和重难点，帮助学生提升能力，突破思维。

# 学而思秘籍系列图书|语文

# 提升素养

能力训练

![](images/f419b641d98fcf08eb159bffe0cbb4013ede9abd2a1e8d2cfaf371de98ff8f90.jpg)

# 小学秘籍系列

5大模块+2条主线，能力与素养双向提升。

![](images/66f2b766ff8f4e279e1a45f549e9a6c5ad5a529f01f386683a1acf218f15ec43.jpg)

# 初中秘籍系列

融合课改四大核心素养，培养爱阅读、 善写作、勤思考、会学习的学生。

# 创新体系|真题研习

![](images/7689f45099d076f456e3b830d762ec04a5d4fbe89a6de0bd0530240652b079da.jpg)

# 思维创新大通关数学

攻克数学思维难题，通向理想中学。

# 大家一起来“升级

# 参与方式

您在使用本书时，如有任何疑问或对图书有任何建议，请扫码进行反馈，并查看反馈采纳结果。

![](images/bb19a229094c9e77dd1d911fd5b131b252ff15941141a820a9ec534546e04eb7.jpg)

# 奖励

您的反馈一经采纳，我们将会送出总价值35元的图书抵扣券（相同内容的反馈，依据反馈时间，奖励前三位）。请扫码关注公众号，并在对话框中发送反馈时填写的手机号，领取抵扣券。

![](images/ebe315a1399349db082da98295f9ad2be7b5555b44e68b84eebbf59e1f849756.jpg)

# 合理规划学习时间

先自己定一个目标，即制定半年学习规划。

2 再将目标细化到每一周，每周学习一本（平均5个考点）。

3 配套课堂巩固的练习， 让学习更有效！

![](images/f0a8615ffeca9bf91a1827bc1877e4edbf676226c49a0c56938bf4c78aded728.jpg)

![](images/640d0dc645bc169e21132152a52e87ec23440b6a51757b8d2a369087a56e9f3c.jpg)  
·共6级·每级17-26讲