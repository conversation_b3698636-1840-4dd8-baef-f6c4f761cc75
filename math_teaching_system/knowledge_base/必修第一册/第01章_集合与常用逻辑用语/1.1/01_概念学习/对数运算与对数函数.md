---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 15
source_file: L1-19-对数运算与对数函数.md
title: L1-19-对数运算与对数函数
type: problem_type
---

# 对数运算与对数函数

一本一讲，共26讲（建议5个月学习）

5个考点+8个题型+26个视频

每周一讲（建议学习时间90分钟）

![](images/16b3469784da78a854bd1674421a805feeb8e48c9aeab2c40555780906f9feec.jpg)

# 视频内容研发团队

学而思优秀老师

学而思优秀老师和一线高级教师联合创作本书试题，并精心录制讲解视频学而思图书APP扫码即可观看

![](images/a28405fe956326bc5900a31e52e2a1c8408cc7473ccfa8780b4ef844de35fc48.jpg)

# 傅博宇 老师

毕业于北京大学元培学院  
网校和北大数院高考数学研究联合课题组成员；  
网校高中创新产品部负责人；  
荣获学而思网校“桃李满天下奖”“出类拔萃奖”等；腾讯网中国好老师；  
青少年教育导师认证；  
科学家长观体系的创立者

# 王侃老师

![](images/21cf89bb8f2fb07e2f0ad0221be3cd751e236672e1bfec3986fe71b61cb6a397.jpg)

毕业于北京大学数学系  
学而思网校高中数学教研奠基人；  
学而思网校高中数学S级教师；  
荣获学而思网校“突出贡献奖” “桃李天下奖”等；擅长总结题型特点，提炼思想方法；  
擅长分层教学，因材施教

# 付恒岩 老师

![](images/c40d50fb9a691a6e363a087a22967880e7a33f1496ae9715a58728556cf36365.jpg)

毕业于大连理工大学  
网校高中部理科主讲岗后培训师；  
2020年荣获学而思网校最具魅力奖；  
2019年、2020年荣获学而思网校诲人不倦奖；2020年荣获学而思网校高考优秀评卷人；  
2021年担任新浪教育高考数学直播解析特邀嘉宾；“停课不停学”公益课高中数学主讲老师

# 武洪姣 老师

![](images/374dd425e629806baf4af8d7b23a21d954864d96e234fcf7a431daa219000cdf.jpg)

14年线上线下教学经验；  
学而思网校高中理科教研负责人；  
学而思高中数学特级教师;  
在教学的过程中擅长归纳题型，方法和技巧;  
在高中数学模块中最擅长讲解圆锥曲线和导数；  
无论你是从小数学不好，还是数学一直拔尖，都可以在武老师的课堂上收获很多

1级

# 对数运算与对数函数

一本一讲，共26讲（建议5个月学习）

5个考点 $^ { + 8 }$ 个题型 $+ 2 6$ 个视频每周一讲（建议学习时间90分钟）

1210g9-eln+382二 2×2- e°+(8§)²二 4-1+4=1

# 预习篇

第1讲集合的概念与表示 提升篇第 $^ 2$ 讲集合的关系与运算第11讲集合重难点专题第3讲解不等式第12讲常用逻辑用语第4讲函数的概念与三要素第13讲基本不等式第5讲函数的单调性（一）第14讲函数三要素专题第6讲函数的奇偶性（一）第15讲函数的单调性（二）第 $^ 7$ 讲指数幂运算与幂函数第16讲函数的奇偶性（二）第8讲指数函数第17讲抽象函数第9讲对数运算第18讲指数幂运算与指数函数第10讲对数函数第19讲对数运算与对数函数第20讲函数与方程第21讲恒成立与存在性问题第22讲三角函数基本概念与诱导公式  
模块1 对数运算 2第23讲三角函数的图象与性质  
模块2 对数函数 8第24讲三角公式的应用技巧第25讲三角恒等变换重点题型第26讲正弦型函数的图象与性质参考答案

# 对数运算与对数函数

# 直击课堂

<html><body><table><tr><td>知识模块</td><td>考点</td><td>对应例题</td><td>星标统计</td></tr><tr><td rowspan="3">对数运算</td><td>对数的运算</td><td>例1</td><td rowspan="9"></td></tr><tr><td rowspan="2">换底公式</td><td>例2</td></tr><tr><td>例3</td></tr><tr><td rowspan="5">对数函数</td><td>对数函数的图象与性质</td><td>例4</td><td rowspan="5">★★3道题 ★★★18道题 ★★★★3道题</td></tr><tr><td>对数的大小比较</td><td>例5</td></tr><tr><td rowspan="3">对数型函数</td><td>例6</td></tr><tr><td>例7</td></tr><tr><td>例8</td></tr></table></body></html>

# 学习目标

$\textcircled{1}$ 掌握积、商、幂的对数运算，熟练应用换底公式.

$\textcircled{2}$ 掌握对数函数的图象与性质，会利用对数函数的图象与性质比较大小.

$\textcircled{6}$ 能够解决对数型函数的相关问题.

# 模块1对数运算

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# ①对数的概念

如果 $a ^ { b } = N ( a > 0$ ，且 $a \neq 1$ ),那么我们把 $b$ 叫做以 $a$ 为底 $N$ 的对数,记作 $b = \log _ { a } N$ ,其中 $^ { a }$ 叫做对数的底数， $N$ 叫做真数.

图示：

指数 $b \in \mathbb { R }$ 对数 幂 $N { > } 0$ 真数 指数式 $a ^ { b } { = } N$ 底数 $\mathrm { l o g } _ { a } N { = } \dot { b }$ 对数式 a>0且a≠1

# ②对数的性质

根据对数的定义，对数 $\log _ { a } N ( a > 0$ ，且 $a \neq 1$ )具有如下性质：

$\textcircled{1}$ 0和负数没有对数，即 $N > 0$ ；$\textcircled{2}$ 1的对数为0，即 $\log _ { a } 1 = 0 .$ $\textcircled{3}$ 底的对数等于1，即logaa=1.

# $\textcircled { 8 }$ 指对互化

指数式 $a ^ { b } = N ( a > 0 , a \neq 1 )$ 和对数式 $b = \log _ { a } N ( a > 0 , a \not = 1 )$ 是等价的.具体对应关系如下：

<html><body><table><tr><td rowspan="2"></td><td rowspan="2">式子</td><td colspan="3">名称</td></tr><tr><td>a</td><td>b</td><td>N</td></tr><tr><td>指数式</td><td>a =N(a&gt;0,a≠1)</td><td>底数</td><td>指数</td><td>幂</td></tr><tr><td>对数式</td><td>b=logaN(a&gt;0,a≠1,N &gt;0)</td><td>底数</td><td>对数</td><td>真数</td></tr></table></body></html>

# 4常用对数与自然对数

（1)以10 为底的对数叫做常用对数,为了简便,通常把底数10略去不写,并把"log"简记为“lg”，即 $\log _ { 1 0 } N = \lg N .$

(2)以e为底的对数称为自然对数,为了简便,通常把底数e略去不写,并把"log”简记为“ln”,即 $\log _ { \mathrm { e } } N = \ln \ N .$ 其中 $\mathrm { e } \approx 2 . 7 1 8 \ 2 8$

# 5对数恒等式

# $\textcircled{1}$ 积、商、幂的对数

（1)积的对数$\log _ { a } ( { M } \cdot { N } ) = \log _ { a } ( { M } + \log _ { a } ( N ($ （积的对数等于对数的和)；推广：log（N $N _ { 2 }$ ）lgNlgN+logN。

(2)商的对数

logaN M=logM-logN（商的对数等于对数的差）；

(3)幂的对数

logM=αlogM（αER）（幂的对数等于底数的对数乘幂指数）.

# $\textcircled { 7 }$ 换底公式

$$
\log _ { b } N = { \frac { \log _ { a } N } { \log _ { a } b } } ( a , b > 0 , a , b \neq 1 , N > 0 )
$$

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点1：对数的运算

# 例1

# （1）★★★

$ { \frac { \lg \ 8 \ + \lg \ 1 2 5 \ - \lg \ 2 \ - \lg \ 5 } { \lg \ \sqrt { 1 0 } \ \cdot \ \lg \ 0 . \ 1 } } = \lg$

# 学习笔记

# （2）★★★

计算： $\log _ { 2 } 5 6 - \log _ { 2 } 7 + \ln { \sqrt { \mathrm { e } } } + 2 ^ { 1 + \log _ { 2 } 3 }$

# 学习笔记

# （3）★★★

计算 $: \lg \ 2 5 + { \frac { 2 } { 3 } } \lg \ 8 + \lg \ 5 \times \lg \ 2 0 + ( \lg \ 2 ) ^ { 2 } .$

# 学习笔记

# （4）★★★

$$
\displaystyle \frac { \lg ~ 3 + \frac { 2 } { 5 } \mathrm { l g ~ 9 ~ + } \frac { 3 } { 5 } \mathrm { l g ~ \sqrt { 2 7 } ~ - l g ~ \sqrt { 3 } ~ } } { \lg ~ 8 1 - \mathrm { l g ~ 2 7 } } .
$$

# 学习笔记

# 达标检测1★★★

计算： $\mathbf { \nabla } _ { 2 } \log _ { 3 } 2 - \log _ { 3 } \frac { 3 2 } { 9 } + \log _ { 3 } 8 - 3 \log _ { 5 } 5 =$

# 学习笔记

# 考点2：换底公式

# 例2

# （1）★★★

已知 $\log _ { 2 } 3 = p$ ，请用 $p$ 表示 $\log _ { 1 8 } 2 4$

# 学习笔记

# （2）★★★

已知 $\log _ { 8 } 3 = p$ $\log _ { 3 } 5 = q$ ，那么 $\mathrm { l g } 5 = \mathrm { } .$ （用 $p , q$ 表示).

# 学习笔记

# 例3★★★

若3²=4=36,则²+1

# 学习笔记

# 达标检测2★★★

已知 $3 ^ { m } = 5 ^ { n } = k$ 且 $\frac { 1 } { m } + \frac { 1 } { n } = 2$ 则 $k$ 的值为(

A $\sqrt { 1 5 }$ B.5 C.5 D.225

# 学习笔记

# 模块2对数函数

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# $\textcircled { 1 }$ 对数函数

我们把函数 $\gamma = \log _ { a } x ( a > 0$ 且 $a \neq 1$ )叫做对数函数,其中 $x$ 是自变量,函数的定义域是 $\left( \mathrm { ~ 0 ~ , ~ } + \infty \right)$ ,值域为实数集R.

# 2对数函数的图象和性质

<html><body><table><tr><td colspan="2">函数</td><td colspan="2">y=logax(a&gt;0且a≠1）</td></tr><tr><td>底的范围</td><td>0&lt;a&lt;1</td><td>a&gt;1</td></tr><tr><td colspan="2">图象</td><td>iy=logx(a&gt;1） N(1,0） -- x 0 10x y=logax(0&lt;a&lt;1） ：</td></tr><tr><td colspan="2">定义域</td><td colspan="2">（0，+8）</td></tr><tr><td colspan="2">值域</td><td colspan="2">R</td></tr><tr><td rowspan="3">性质</td><td>过定点</td><td colspan="2">(1,0)</td></tr><tr><td>函数 值范围</td><td>当𝑥&gt;1时,y&lt;0; 当0&lt;𝑥&lt;1时,y&gt;0</td><td>当𝑥&gt;1时,y&gt;0； 当0&lt;x&lt;1时,y&lt;0</td></tr><tr><td>单调性</td><td>单调递减</td><td>单调递增</td></tr></table></body></html>

# 精讲精练

# 拍照批改秒判对错

# 考点3：对数函数的图象与性质

# 例4

# （1）★★★

函数 $f ( x ) = { \frac { 1 } { \ln ( x + 1 ) } } + { \sqrt { 4 - x ^ { 2 } } }$ 的定义域为(

A. $[ \ - 2 , 0 ) \cup ( 0 , 2 ]$ B.（-1,0)U(0,2] C.[-2,2] D.（-1,2]

# 学习笔记

# （2）★★★

设 $a > 0$ 且 $a \neq 1$ ，函数 $f ( x ) = \log _ { a } { \left( 2 x - 1 \right) } + 1$ 的图象恒过定点 $P$ ，则 $P$ （204的坐标是（ ）

A. (1,1) B.(-1,1) C. (1,-1) D.(-1,-1)

# 学习笔记

# （3）★★★

已知 $f ( x ) = a ^ { x } , g ( x ) = \log _ { a } x ( a > 0$ 且 $a \neq 1$ )，如果 $f ( 3 ) \cdot g ( 3 ) < 0$ ,那么$f \left( x \right)$ 与 $g ( x )$ 在同一平面直角坐标系内的图象可能是下图中的（）

![](images/5f2ec10ebafb8e1bde58667c71cc91ef4f6e487c048dae4cd272d30a80a5f512.jpg)

# 学习笔记

# 达标检测3★★★

已知 $\log _ { 4 m } ( m - 2 ) > 0$ ，则 $m$ 的取值范围是(

A. $( 3 , + \infty )$ B.（-∞,3)C. $\big ( \mathrm { ~ - ~ } \infty \mathrm { ~ , ~ } { - 3 } \big )$ D.（2,+∞）

# 学习笔记

# 考点4：对数的大小比较

# 例5

# （1）★★★

比较大小(填 $^ { 6 6 } > ^ { 9 9 6 6 } < ^ { 9 9 }$ 或“ $= ^ { \mathfrak { s } }$ ）

① logo.12 013 $\log _ { 0 . 1 } 2 0 1 4$   
② log1.52 014 （204 $\log _ { 2 } 2 \ 0 1 4$ ;   
1 1   
③log0.52 014 log0.82 014

# 学习笔记

# （2）★★★

设劵 $a , b , c$ 均为正数，且 $2 ^ { a } = \log _ { 2 } ^ { } a , \left( { \frac { 1 } { 2 } } \right) ^ { b } = \log _ { 2 } ^ { } b , \left( { \frac { 1 } { 2 } } \right) ^ { c } = \log _ { 2 } c .$ 则

A. $a < b < c$ （204 B. $c < b < a$   
C. $c < a < b$ D. $b < a < c$

# 学习笔记

# 达标检测4★★★

若 $a = \ln \pi , b = \lg 6 , c = \log _ { 0 . 2 } 8$ ，则（ ）

A. $a > b > c$ B. $b > a > c$   
C. $c > a > b$ D. $b > c > a$

# 学习笔记

![](images/979fe544ac384dffc4bde2e5ae87ed8990bbdcbb82e090418b11907049935c00.jpg)

# 考点5：对数型函数

# 例6

# （1）★

函数 $f \left( x \right) = \log _ { 5 } \left( 2 x + 1 \right)$ 的单调递增区间是

# 学习笔记

# （2）★★

函数 $y = \log _ { 0 . 5 } \left( x ^ { 2 } - 2 x \right)$ 的单调递增区间是

# 学习笔记

# （3）★★★★

函数 $y = - \log _ { 2 } \left( \ | x - 2 \ | - 8 \right)$ 的单调递减区间为

# 学习笔记

# 达标检测5★★★

函数 $y = \log _ { 3 } ( { \ - x ^ { 2 } + x + 6 } )$ 的单调递减区间是( ）

A $\left( \mathbf { \nabla } - \infty \mathbf { \nabla } , \frac { 1 } { 2 } \right]$ B $\left( 0 , { \frac { 1 } { 2 } } \right)$ C $\left[ { \frac { 1 } { 2 } } , 3 \right]$ D.（3,+∞）

# 学习笔记

# 例7

# （1）★★★★

已知函数 $f ( x ) = \mathrm { l g } ( \mathbf { \partial } - x ^ { 2 } + 2 a x )$ 在区间(1,2)上是减函数,则实数 $a$ 的取值集合是

# 学习笔记

# （2）★★★★

已知函数 $f \left( x \right) = \log _ { 2 } \frac { a x - 2 } { x - 1 } , a$ 为常数.若 $f \left( x \right)$ 在区间(2,4)上是减函数,求 $a$ 的取值范围.

# 学习笔记

# 例8★★

给出下列函数：

$\begin{array} { l } { \displaystyle \mathbb { D } f ( \boldsymbol { x } ) = \mathrm { l g } \Big ( 2 ^ { \boldsymbol { x } } + \frac { 1 } { 2 ^ { \boldsymbol { x } } } \Big ) ; } \\ { \displaystyle \mathbb { 2 } f ( \boldsymbol { x } ) = \left| \mathrm { l g } \boldsymbol { x } \right| ; } \\ { \displaystyle \mathbb { 3 } f ( \boldsymbol { x } ) = \mathrm { l g } \left| \boldsymbol { x } \right| ; } \\ { \displaystyle \mathbb { 4 } f ( \boldsymbol { x } ) = \mathrm { l g } \big ( 1 - \boldsymbol { x } \big ) - \mathrm { l g } \big ( 1 + \boldsymbol { x } \big ) . } \end{array}$ 其中奇函数有 ,偶函数有

![](images/6c976c32a04437aa2a96c323235f17c1324dfb41698fe814d2beb6dbb925a3f3.jpg)

# 学习笔记

# 达标检测6★★★

设函数 $f ( x ) = \ln ( 2 + x ) + \ln ( 2 - x )$ ，则 $f \left( x \right)$ 是(

A．奇函数，且在(0,2)上是增函数B.奇函数，且在(0,2)上是减函数C．偶函数，且在(0,2)上是增函数D．偶函数，且在(0,2)上是减函数

# 学习笔记

# 学习总结

![](images/491a5e75e634bac2ba7976c8f92c094b03112817c4190e41149986c325b0cb8c.jpg)

# 直击高考

设 $a = \log _ { 0 . 2 } ( 0 . 3 , b = \log _ { 2 } 0 . 3$ ，则（

A. $a + b < a b < 0$ B. $a b < a + b < 0$   
C. $a + b < 0 < a b$ D. $a b < 0 < a + b$

# 学而思秘籍系列图书|数学

# 思维培养

# 思维提升

# 思维突破

![](images/4c1fc79aa512685a4234f4ac398ee973c59bafc33367a26d4f1d716bf15e8e5d.jpg)

# 小学秘籍系列

学而思积淀近20年教研经验，培养受益一生的能力。

![](images/6bc12b0e2044427376759fa9c0cb9835ddd392a850fabc41ee84e532258a9c78.jpg)

# 初中秘籍系列

全面覆盖初中基础知识和重难点，帮助学生夯实基础，拓展认知。

学而思 8秘籍 集合的595高中数学思维突破-1-3,++

# 高中秘籍系列

全面覆盖高中基础知识和重难点，帮助学生提升能力，突破思维。

# 学而思秘籍系列图书|语文

# 提升素养

# 能力训练

![](images/d185ea3b2038b98ec68cefd212a3da8d2180335e1153fd27995d6aa7cffe26a3.jpg)

# 小学秘籍系列

5大模块+2条主线，能力与素养双向提升。

![](images/d6a06dbcd03c4fafbf50025e2fa1b11265fb1ea0b66f61fdb9dff6244d9fecd5.jpg)

# 初中秘籍系列

融合课改四大核心素养，培养爱阅读、 善写作、勤思考、会学习的学生。

# 创新体系|真题研习

![](images/7d45b3a986d935d078d23f7d4aca57fb579e291563b79c8b31a7fab0a361abbd.jpg)

# 思维创新大通关数学

攻克数学思维难题，通向理想中学。

# 大家一起来“升级

# 参与方式

您在使用本书时，如有任何疑问或对图书有任何建议，请扫码进行反馈，并查看反馈采纳结果。

![](images/098c07871ba5a31b26cdf28b8ef160777bbe52364d7a9726a7b31698c9c980d6.jpg)

# 奖励

您的反馈一经采纳，我们将会送出总价值35元的图书抵扣券（相同内容的反馈，依据反馈时间，奖励前三位）。请扫码关注公众号，并在对话框中发送反馈时填写的手机号，领取抵扣券。

![](images/56a10e7345628f1e24948bcaa805f26e6ceaaa82cbcb7186b7c885a2b5d3a7ec.jpg)

# 合理规划学习时间

先自己定一个目标，即制定半年学习规划。再将目标细化到每一周，每周学习一本（平均5个考点）。3 配套课堂巩固的练习，让学习更有效！

![](images/f393c172abab994988f4c790fcf18c00ca7b0e7e841f1860b111c12f5c477aa1.jpg)

![](images/5b843888f2016f24db7745b6720898f2a05210ca4e9d1dc085b63d5fb99fb403.jpg)  
·共6级·每级17-26讲