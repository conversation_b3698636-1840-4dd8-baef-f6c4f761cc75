---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 23
source_file: 第3节 集合的基本运算（习题册+答案册）.md
title: 第3节 集合的基本运算（习题册+答案册）
type: practice
---

# 强化训练

# A 组 夯实基础

1．(2024·北京怀柔模拟） 已知集合 $A = \{ x \vert 3 - x > 1 \}$ ， $B = \{ 0 , 1 , 2 , 3 , 4 \}$ ，则 $A \cap B = { \textrm { ( ) } }$

A. {3,4}B. {2,3,4} C．{0,1}D. {0,1,2}

1. C解析：由 $3 - x > 1$ 可得 $x < 2$ ，所以 $A = \{ x \mid x < 2 \}$ ，又 $B = \{ 0 , 1 , 2 , 3 , 4 \}$ ，所以 $A \cap B = \{ 0 , 1 \}$

2．(2024·甘肃白银模拟) 已知集合 $A = \{ x | - 3 < x \leq 2 \}$ ， $B = \{ x | - 2 \leq x < 3 \}$ 则 $A \cup B = \mathrm { ~ ( ~ ) ~ }$

A. $\{ x | - 2 < x < 2 \}$ B. $\{ x | - 2 \leq x \leq 2 \}$   
C. $\{ x | - 2 < x \leq 3 \}$ D. $\{ x | - 3 < x < 3 \}$

2.D解析：如图， $A \bigcup B = \{ x \mid - 3 < x < 3 \}$ 效 A $B$ 本通→x-3 2 2 3

3．（2024·四川泸州二模） 已知全集 $U = \left\{ x \mid x + 2 > 0 \right\}$ ，集合 $A = \{ x \mid x \geq 1 \}$ ， 则 $\complement _ { \upsilon } { A } = \textrm { ( ) }$

A. $\{ x | - 2 < x < 1 \}$ B. $\{ x | - 2 < x \leq 1 \}$   
C. $\{ x | x \leq 1 \}$ D. $\{ x \mid x < 1 \}$

3.A

解析：由 $x + 2 > 0$ 可得 $x > - 2$ ，所以 $U = \{ x \mid x > - 2 \}$ ，又 $A = \{ x \mid x \geq 1 \}$ ，所以 $\complement _ { U } A = \{ x \mid - 2 < x < 1 \}$ ：

4．（2024·河南驻马店模拟）

为了坚持“五育”并举，全面发展素质教育，某学校在课余时间提供了多种社团供学生们选择，每位同学都可以选择多种社团，其中选择舞蹈社团或园艺社团的同学有90人，选择舞蹈社团的同学有55人，选择园艺社团的同学有60人，则同时选择舞蹈社团和园艺社团的同学人数是

4.25

解析：题干涉及两类人，且彼此有重叠，这种情况可考虑用容斥原理来分析各部分的人数，设选择舞蹈、园艺社团的学生分别构成集合 $A$ ， $B$

由题意， $\mathrm { c a r d } ( { \cal A } { \sf U } B ) = 9 0 ~ , ~ \mathrm { c a r d } ( { \cal A } ) = 5 5 ~ , ~ \mathrm { c a r d } ( B ) = 6 0 ~ ,$ 由容斥原理， $\operatorname { c a r d } ( A \bigcup B ) = \operatorname { c a r d } ( A ) + \operatorname { c a r d } ( B ) - \operatorname { c a r d } ( A \bigcap B ) ,$ 所以 $\operatorname { c a r d } ( A \bigcap B ) = \operatorname { c a r d } ( A ) + \operatorname { c a r d } ( B ) - \operatorname { c a r d } ( A \bigcup B )$ $= 5 5 + 6 0 - 9 0 = 2 5$

# B组 强化能力

5．（2024·北京海淀开学考试） 若全集 $I = \mathbf { R }$ ，集合 $A = \{ x \mid x > 0 \}$ ， $B = \{ x \vert - 1 \leq$ $x < 2 \}$ ，则 $\complement _ { \mathit { \Pi } _ { I } } ( \mathit { \Pi } \ A \bigcup \ B ) = \ \left( \begin{array} { l l l } { \mathrm {  ~ \Gamma ~ } } & { } \end{array} \right)$

A. $\{ x \mid x < - 1 \}$ B. $\{ x \mid x \leq - 1 \}$   
C. $\{ x \mid x \geq - 1 \}$ D. $\{ x \mid x \geq 2 \}$

5.A

解析：如图， $A \bigcup B = \{ x \mid x \geq - 1 \}$ ，故 $\complement _ { \ u { I } } ( A \cup B ) = \{ x \mid x < - 1 \}$

B A →x 0 2

6．(2024·陕西西安模拟）

若全集 $U = \mathbf { R }$ ，集合 $A = \{ 0 , 1 , 2 , 3 , 4 , 5 , 6 \}$ ， $B =$ $\{ x \mid x < 3 \}$ ，则图中阴影部分表示的集合为 ·

![](images/120405e1fe4a0bba661956b3cd9bd18e88c1abbe60444eb92028390f284dd4e5.jpg)

6. {3,4,5,6}

解析：阴影部分表示在 $A$ 中把 $A$ 和 $B$ 公共的元素去掉后，余下的部分，即 $\complement _ { A } ( A \cap B )$ ，故先求 $A \cap B$ 由题意， $A$ 中的元素0，1，2也在集合 $B$ 中，其余元素不在集合 $B$ 中，所以 $A \bigcap B = \{ 0 , 1 , 2 \}$ ，故所给图中阴影部分表示的集合为 $\complement _ { _ { A } } ( A \cap B ) = \{ 3 , 4 , 5 , 6 \}$ ：

7．（2024·辽宁大连期末）设全集 $U = \mathbf { R }$ ，集合 $A = \{ x \mid x \geq 2 \}$ ， $B = \{ x | 1 < x <$

3}，则图中阴影部分表示的集合为

![](images/0b3b9acac0d785cc1dc02ce5cb6aba9d6b2da033233601747c2e258501b069a0.jpg)

7. $\{ x \mid 1 < x < 2 \}$

解析：阴影部分可看成在集合 $B$ 中把 $A \cap B$ 那一块去掉后余下的部分，即 $\complement _ { B } ( A \cap B )$ ，故先求 $A \cap B$ ，如图， $A \bigcap B = \{ x \mid 2 \leq x < 3 \}$ ，故所求阴影部分表示的集合为 $\complement _ { _ B } ( A \bigcap B ) = \{ x \mid 1 < x < 2 \}$

$$
\frac { \ln B \prod _ { i = 1 } ^ { B } A } { 1 } \underset { 2 } { \underbrace { 2 } } \frac { 1 } { 3 } \underset { 3 } { \longrightarrow } x
$$

【反思】若题干给出了具体的集合，让求Venn 图中的某一部分，则应先分析该部分可由所给集合进行怎样的运算得到.

8．（2023·全国乙卷） 设全集 $U = \mathbf { R }$ ，集合 $M = \{ x \mid x < 1 \}$ ， $N =$ $\{ x | - 1 < x < 2 \}$ ，则 $\{ x | x \geq 2 \} = { \pmod { } }$

A. $\complement _ { U } ( M \cup N )$ B. $N \cap \complement _ { v } M$   
C. $\complement _ { U } ( M \cap N )$ D. $M \cap \complement _ { v } N$

8.A

解析：从M, $N$ 出发，通过正面推理得到 $\{ x \mid x \geq 2 \}$ 不易，考虑逐个验证选项，看谁是 $\left\{ x \mid x \geq 2 \right\} ,$ A项，如图1， $M \cup N = \{ x \mid x < 2 \}$ ，所以 $\complement _ { U } ( M \cup N )$   
$= \{ x \mid x \geq 2 \}$ ，故A项正确；此为单选题，到此已可结束，但我们把后面的选项也做个分析，B项， $\complement _ { U } M = \{ x \mid x \geq 1 \}$ ，如图2， $N \cap \complement _ { v } M = \{ x | 1 \leq x < 2 \}$ ，  
故B项错误;  
C项，由图1可知 $M \cap N = \{ x | - 1 < x < 1 \}$ ，  
所以 $\complement _ { \scriptscriptstyle U } ( M \cap N ) = \{ x \mid x \leq - 1$ 或 $x \geq 1 \}$ ，故 $\textrm { C }$ 项错误;  
D项， $\complement _ { \scriptscriptstyle U } N = \{ x \mid x \leq - 1$ 或 $x \geq 2 \}$ ，  
所以如图3， $M \cap \complement _ { \scriptscriptstyle U } N = \{ x \mid x \leq - 1 \}$ ，故 $\mathrm { D }$ 项错误.

$$
\xrightarrow [ - 1 ] { M \bigsqcup _ { i = 1 } ^ { N } \bigsqcup _ { i = 1 } ^ { N } } \xrightarrow [ - 1 ] { \longrightarrow \bigvee \bigsqcup _ { i = 1 } ^ { N } \bigsqcup _ { 2 } ^ { \complement _ { U } M } } \xrightarrow [ x ] { \overline { { \complement _ { U } N \bigsqcup _ { i = 1 } ^ { N } \bigsqcup _ { i = 1 } ^ { \bigstar } \bigsqcup _ { i = 1 } ^ { \bigstar } } } }
$$

图1

图2

图3

9．（2023·全国甲卷）

设集合 $A = \left\{ x \mid x = 3 k + 1 , k \in \mathbf { Z } \right\}$ ， $B = \{ x \vert x = 3 k + 2 $ $k \in \mathbf { Z } \}$ ， $U$ 为整数集，则 $\complement _ { \mathit { u } } ( \mathit { A } \cup \mathit { B } ) = \mathit { \Omega } ( \mathit { \Omega } )$

A. $\{ x \vert x = 3 k , k \in \mathbf { Z } \}$ B． $\displaystyle \{ x \mid x = 3 k - 1 , k \in \mathbf { Z } \}$ C. $\left\{ x \mid x = 3 k - 2 , k \in \mathbf { Z } \right\}$ D.Q

9.A

解法1： $A \bigcup B = \{ x \mid x = 3 k + 1$ 或 $x = 3 k + 2 , k \in \mathbf { Z } ;$ ，  
它包括除以3后余数为1或2的整数，整数除以3余数只能是 $0 , \ 1 , \ 2$ ，所以取补集后剩下的是余数为0的，  
所以 $\complement _ { \scriptscriptstyle U } ( A \cup B ) = \{ x \mid x = 3 k , k \in \mathbf { Z } \}$ ：  
解法2：若想象不出 $\complement _ { U } ( A \cup B )$ 中的元素有哪些，也可罗列部分元素来看规律，  
由题意，集合 $A = \{ \cdots , - 5 , - 2 , 1 , 4 , 7 , \cdots \}$ ，  
集合 $B = \{ \cdots , - 4 , - 1 , 2 , 5 , 8 , \cdots \}$ ，  
所以 $A \bigcup B = \{ \cdots , - 5 , - 4 , - 2 , - 1 , 1 , 2 , 4 , 5 , 7 , 8 , \cdots \}$ ，  
故 $\complement _ { \scriptscriptstyle U } ( A \cup B ) = \{ \cdots , - 3 , 0 , 3 , 6 , \cdots \} = \{ x \mid x = 3 k , k \in \mathbf { Z } \} .$

10．（2024·新疆模拟）

设集合 $A = \{ 1 , 2 \}$ ， $B = \{ m , m + 1 \}$ ，若 $A \bigcap B = \{ n \}$ ，则 $m =$ （）

A.0 B.1C.0或1 D.0或2

10.D

解析： $A \cap B = \{ n \}$ 意味着A， $B$ 只有1个公共元素，注意到 $A$ 的元素都已知，故就讨论公共元素是 $A$ 中的谁,若 $A \cap B = \{ 1 \}$ ，则 $1 \in B$ ，所以 $m = 1$ 或 $m + 1 = 1$ ，  
故 $m = 1$ 或 $0$ ，由 $1 \in B$ 求出的 $m$ 不能保证 $A \cap B = \{ 1 \}$ ，故还需代回集合 $B$ 去检验,  
当 $m = 1$ 时， $B = \{ 1 , 2 \}$ ，此时 $A \cap B = \{ 1 , 2 \}$ ，不合题意，  
当 $m = 0$ 时， $B = \{ 0 , 1 \}$ ，满足 $A \cap B = \{ 1 \}$ ；  
若 $A \cap B = \{ 2 \}$ ，则 $2 \in B$ ，所以 $m = 2$ 或 $m + 1 = 2$ ，  
故 $m = 2$ 或1（舍去，前面已经检验过这种情况)，,  
当 $m = 2$ 时， $B = \{ 2 , 3 \}$ ，满足 $A \cap B = \{ 2 \}$ ：  
综上所述， $m = 0$ 或2.

11．（2024·湖南长沙期末）已知全集为 $U$ ，集合 $M , \ N$ 满足 $M \subsetneq N \subsetneq U$ ，则下列运算结果为 $U$ 的是（）

A. $M \cup N$ （204 B. $( \complement _ { U } N ) \bigcup ( \complement _ { U } M )$   
C. $M \cup ( \complement _ { U } N )$ D. $N \cup ( \complement _ { U } M )$

11.D

解法1：A项，因为 $M \subsetneq N \subsetneq U$ ，所以 $M \cup N = N \neq U$ ，  
故A项错误;  
B项， $\complement _ { U } N$ 如图1的阴影部分， $\complement _ { v } M$ 如图2的阴影部分，所以 $( \complement _ { \scriptscriptstyle U } N ) \bigcup ( \complement _ { \scriptscriptstyle U } M ) = \complement _ { \scriptscriptstyle U } M \neq U$ ，故 $\mathrm { B }$ 项错误;  
C项，如图3， $M \cup ( \complement _ { U } N )$ 为阴影部分，  
所以 $M \cup ( \complement _ { U } N ) \neq U$ ，故C项错误;  
D项， $\complement _ { U } M$ 如图2的阴影部分，再把 $N$ 加进去，可以覆盖全集

$U$ 的所有元素，从而 $N \bigcup ( \complement _ { U } M ) = U$ ，故 $\mathrm { D }$ 项正确.

![](images/1acac126bde080be9a22b024e5e7bb52e70fb383f05c0aaf1bd87e845fccf09f.jpg)  
图1

![](images/89337c348a57eed7abd2daccefa2385b5a34744afdd8a3eacc8fb9be68f57eed.jpg)  
图2

![](images/bb1a09871961cd0be6c8d4be3bd82c0e17d93a50772b35b921676a43031e1c24.jpg)  
图3

解法2：所给条件 $M \subsetneq N \subsetneq U$ 比较简单，也可考虑举出具体的集合M, $N ,$ $U$ 来分析选项，设 $U = \{ 1 , 2 , 3 \}$ ， $M = \{ 1 \}$ ， $N = \{ 1 , 2 \}$ ，满足 $M \subsetneqq N \subsetneq U$ ，  
A项， $M \bigcup N = \{ 1 , 2 \} \neq U$ ，故A项错误;  
B项， $\complement _ { U } N = \{ 3 \}$ ， $\complement _ { U } M = \{ 2 , 3 \}$ ，  
所以 $( \complement _ { \scriptscriptstyle U } N ) \bigcup ( \complement _ { \scriptscriptstyle U } M ) = \{ 2 , 3 \} \neq U$ ，故 $\mathrm { B }$ 项错误;  
C项， $M \bigcup ( \complement _ { \scriptscriptstyle U } N ) = \{ 1 \} \bigcup \{ 3 \} = \{ 1 , 3 \} \neq U$ ，故C项错误;  
$\mathrm { D }$ 项， $N \bigcup ( \complement _ { \scriptscriptstyle U } M ) = \{ 1 , 2 \} \cup \{ 2 , 3 \} = \{ 1 , 2 , 3 \} = U$ ，故 $\mathrm { D }$ 项正确.

12．(2024·河北石家庄模拟)

已知全集 $U = \mathbf { R }$ ，集合 $A = \{ x | - 2 \leq x \leq 5 \}$ ， $B = \left\{ x \vert a \leq x \leq 2 - a \right\} .$ （204

(1）当 $a = - 2$ 时，求 $A \cap ( \complement _ { U } B )$ (2）若 $\boldsymbol { A } \bigcup ( \complement _ { \scriptscriptstyle U } \boldsymbol { B } ) = \mathbf { R }$ ，求实数 $a$ 的取值范围.

12．解：（1）当 $a = - 2$ 时， $B = \{ x | - 2 \leq x \leq 4 \}$ ，所以 $\complement _ { \scriptscriptstyle U } B = \{ x \mid x < - 2$ 或 $x > 4 \}$ ，又 $A = \{ x \mid - 2 \leq x \leq 5 \}$ ，如图1， $A \bigcap ( \complement _ { v } B ) = \{ x \mid 4 < x \leq 5 \}$

![](images/4715897a930adb5e541378d1f9bc59e76368f478552e7389b91808e7086d7faf.jpg)  
图1

(2）（集合 $B$ 中 $a \leq x \leq 2 - a$ 的两边都含参，所以 $B$ 可能为空集，此时 $\complement _ { U } B = \mathbf { R }$ ，当然满足要求，先考虑这种情>当 $B = \emptyset$ 时， $a > 2 - a$ ，解得： $a > 1$ ，此时 $\complement _ { U } B = \mathbf { R }$ ，  
所以 $\boldsymbol { A } \bigcup ( \complement _ { \scriptscriptstyle U } \boldsymbol { B } ) = \mathbf { R }$ ，满足题意；  
当 $B \neq \emptyset$ 时，首先， $a \leq 2 - a$ ，所以 $a \leq 1$ ，  
其次， $\complement _ { U } B = \{ x \mid x < a$ 或 $x > 2 - a \}$ ，如图2,  
要使 $\boldsymbol { A } \bigcup ( \complement _ { \scriptscriptstyle U } \boldsymbol { B } ) = \mathbf { R }$ ，应有 $\left\{ { a \geq - 2 \atop 2 - a \leq 5 } \right.$ 解得： $a \geq - 2$ ，  
结合 $a \leq 1$ 可得 $- 2 \leq a \leq 1$ ：  
综上所述，实数 $a$ 的取值范围是 $\{ a | a \ge - 2 \}$ ：

![](images/07bd990bece1caae28ef45aca9ff8daa80ab7a5a7cceb388e75da6d88b923d80.jpg)  
图2

13．（2024·重庆期末）

已知集合 $A = \{ x \mid 2 - m \leq x \leq m \}$ ， $B = \{ x | 1 \leq x \leq 2 \}$ （1）当 $m = 2$ 时，求 $\complement _ { \mathbf { R } } ( A \cup B )$ (2）若 $A \cap B = A$ ，求实数 $m$ 的取值范围.

13．解：（1）当 $m = 2$ 时， $A = \{ x | 0 \leq x \leq 2 \}$ ，又 $B = \{ x | 1 \leq x \leq 2 \}$ ，所以 $A \bigcup B = \{ x \mid 0 \leq x \leq 2 \}$ ，故 $\complement _ { \mathbf { R } } ( A \cup B ) = \{ x \mid x < 0$ 或 $x > 2 \}$ ：（2）因为 $A \cap B = A$ ，所以 $A \subseteq B$ ，（看到 $A \subseteq B$ ，想到先考虑 $A$ 为空集的情况)当 $A = { \emptyset }$ 时， $2 - m > m$ ，解得： $m < 1$ ，此时满足 $A \subseteq B$ ;当 $A \neq { \emptyset }$ 时，首先， $2 - m \leq m$ ，解得： $m \geq 1$ ，其次，如图，要使 $A \subseteq B$ ，应有 $\left\{ { \begin{array} { l } { 2 - m \geq 1 } \\ { m \leq 2 } \end{array} } \right.$ 解得： $m \leq 1$ ，结合 $m \geq 1$ 可得 $m = 1$ ：综上所述，实数 $m$ 的取值范围是 $\{ m \vert m \leq 1 \}$ ：

![](images/27198298c12e7255224697239f5b49c6583d747415a95d6e0e215a4861d485fd.jpg)

# C组拓展提升

14．(2024·河南郑州模拟）

某年级先后举办了数学、历史、音乐讲座，其中有75人听了数学讲座，68人听了历史讲座，61人听了音乐讲座，记 $A = \{ x \vert x$ 是听了数学讲座的学生}， $B = \{ x \mid x$ 是听了历史讲座的学生}， $C = \{ x \vert x$ 是听了音乐讲座的学生}．用card $( M )$ 来表示有限集合 $M$ 中元素的个数，若 $\operatorname { c a r d } ( A \cap B ) = 1 7$ ， $\operatorname { c a r d } ( A \cap C )$ $= 1 2$ ，card $( B \cap C ) = 9$ ， $A \cap B \cap C = \emptyset$ ，则（）

A.carc $| ( A \cup B ) = 1 4 3$ B.c $\operatorname { a r d } ( A \cup B \cup C ) = 1 6 6$ C.card $( B \cup C ) = 1 2 9$ D.card $( A \cap B \cap C ) = 3 8$

14.B

解析：观察已知和选项发现可用容斥原理处理，由题意，car $\mathrm { d } ( A ) = 7 5$ ， $\operatorname { c a r d } ( B ) = 6 8$ ， $\mathrm { c a r d } ( C ) = 6 1$ ，C $\mathrm { a r d } ( A \bigcap B ) = 1 7 , \mathrm { c a r d } ( A \bigcap C ) = 1 2 , \mathrm { c a r d } ( B \bigcap C ) = 9 ,$ 由 $A \cap B \cap C = \emptyset$ 可得 $\operatorname { c a r d } ( A \bigcap B \bigcap C ) = 0$ ，故 $\mathrm { D }$ 项错误;A项，由容斥原理， $\operatorname { c a r d } ( A \bigcup B ) = \operatorname { c a r d } ( A ) + \operatorname { c a r d } ( B )$ $- \operatorname { c a r d } ( A \bigcap B ) = 7 5 + 6 8 - 1 7 = 1 2 6 \neq 1 4 3$ ，故A项错误;B项，由容斥原理， $\operatorname { c a r d } ( A \cup B \cup C ) = \operatorname { c a r d } ( A ) +$ α $\operatorname { a r d } ( B ) + \operatorname { c a r d } ( C ) - \operatorname { c a r d } ( A \bigcap B ) - \operatorname { c a r d } ( A \bigcap C ) - \operatorname { c a r d } ( B \bigcap C )$ $+ \operatorname { c a r d } ( A \bigcap B \bigcap C ) = 7 5 + 6 8 + 6 1 - 1 7 - 1 2 - 9 + 0 = 1 6 6$ ，故B项正确;C 项，由容斥原理， $\operatorname { c a r d } ( B \bigcup C ) = \operatorname { c a r d } ( B ) + \operatorname { c a r d } ( C )$ $- \mathrm { c a r d } ( B \bigcap C ) = 6 8 + 6 1 - 9 = 1 2 0$ ，故 $\mathrm { C }$ 项错误.

15．（2024·全国竞赛）设集合 $A = \{ 1 , 2 , 3 , 4 \}$ ， $B = \{ 1 , 2 \}$ ，若 $C \subseteq A$ 且 $B \cap C$ $\neq \emptyset$ ，则所有满足条件的集合 $C$ 的个数为

15.12

解法1：逐一罗列 $C$ 的可能情况较麻烦，我们先来分析 $C$ 要满足的两个条件，  
因为 $C \subseteq A$ ，所以 $C$ 是 $A$ 的子集,  
因为 $A$ 有4个元素，所以其子集有 $2 ^ { 4 } = 1 6$ 个，  
再考虑 $B \cap C \neq \emptyset$ ，看看上述16个子集中哪些要剔除，  
因为 $B \cap C \neq \emptyset$ ，所以元素1和2至少有1个在 $C$ 中，  
这意味着元素1和2都不在 $C$ 中的情况是要剔除的，这种情况有几种呢？将 $A$ 中的元素 $1 , 2$ 去掉后为 $\{ 3 , 4 \}$ ，该集合有几个  
子集，元素1和2都不在 $C$ 中的情况就有几种，  
集合{3,4}有 $2 ^ { 2 } = 4$ 个子集，它们也都是 $A$ 的子集,  
若以这些子集作为 $C$ ，则不满足 $B \cap C \neq \emptyset$ ，  
所以所有满足条件的集合 $C$ 的个数为 $1 6 - 4 = 1 2$ ：  
解法2：能否正面求解？我们先看 $C$ 的元素应满足的条件，  
因为 $C \subseteq A$ ，所以 $C$ 的元素不超出1，2，3，4,又 $B \cap C \neq \emptyset$ ，所以元素1，2至少有一个要出现在 $C$ 中，  
于是可把 $A$ 的元素拆成1和2，3和4两部分来考虑，集合 $C$ 的元素就从这两部分里选，  
对于元素1和2，出现在 $C$ 中的可以只有1，可以只有2，也可以1，2都有，共3种情况，  
以“只有1”为例，再考虑元素3和4，集合{3,4}有 $2 ^ { 2 } = 4$ 个子集，这4个子集中的元素各自都可以和元素1组成一个集合 $C$ 所以有4种情况，同理，“只有 $2 ^ { \prime \prime }$ ，以及“1，2都有”各自也有4种情况，  
所以满足条件的集合 $C$ 共有 $4 + 4 + 4 = 1 2$ 个.

16．（2024·全国竞赛）（多选）设全集为 $U$ ，设 $A$ ， $B$ 是两个集合，定义集合 $T ( A , B ) = ( A \cap \complement _ { \scriptscriptstyle U } B ) \bigcup ( B \cap \complement _ { \scriptscriptstyle U } A )$ ，则下列说法正确的是（）

A. $T ( A , A ) = \emptyset$ B. $T ( \mathcal { O } , A ) = A$ C. $T ( A , U ) = A$ D. T(A,B)=T(B,A)

16．ABD

解析： $T ( A , B )$ 的定义式结构较复杂，不太容易用Venn 图分析其结果，考虑直接将选项代入定义式分析，A项，由题意， $T ( A , A ) = ( A \cap \complement _ { \scriptscriptstyle U } A ) \cup ( A \cap \complement _ { \scriptscriptstyle U } A )$   
$= A \cap \complement _ { \scriptscriptstyle U } A = \emptyset$ ，故A项正确;  
B项， $T ( \mathcal { D } , A ) = ( \otimes \cap \complement _ { \upsilon } A ) \cup ( A \cap \complement _ { \upsilon } \mathcal { D } ) \textcircled { 1 } ,$   
下面分别计算 $\emptyset \cap \complement _ { v } A$ 和 $A \cap \complement _ { U } O$   
空集与任意集合的交集都是空集，所以 $\varnothing \cap \complement _ { { \scriptscriptstyle U } } A = \emptyset$ ，  
又 $\complement _ { U } \emptyset = U$ ，所以 $A \bigcap \complement _ { \scriptscriptstyle U } \emptyset = A \bigcap U = A$ ，  
代入 $\textcircled{1}$ 得 $T ( \emptyset , A ) = \emptyset \bigcup A = A$ ，故B项正确；  
C项， $T ( A , U ) = ( A \cap \complement _ { \mathit { U } } U ) \bigcup ( U \cap \complement _ { \mathit { U } } A )$   
$= ( A \cap \emptyset ) \cup ( U \cap \complement _ { \upsilon } A ) = \emptyset \bigcup \complement _ { \upsilon } A = \complement _ { \upsilon } A$ $T ( B , A ) = ( B \cap { \complement _ { \scriptscriptstyle U } A } ) \bigcup ( A \cap { \complement _ { \scriptscriptstyle U } B } )$ ，故c项错误高中数学一本通  
$= ( A \cap \complement _ { \scriptscriptstyle U } B ) \cup ( B \cap \complement _ { \scriptscriptstyle U } A ) = T ( A , B )$ ，故 $\mathrm { D }$ 项正确.

【反思】在抽象的集合运算问题中，如果Venn 图不好画，可考虑直接用并、交、补的基本性质来进行推理.

17．（2024·陕西西安期末（改）） 已知集合 $A = \{ x \mid x ^ { 2 } - 2 x + 9 - a = 0 \} \ , \quad B = \{ x \mid a x ^ { 2 }$ $- 4 x + 1 = 0 \}$ ，若集合 $A$ ， $B$ 中至少有一个是非空集合，则实数 $a$ 的取值范围是 ·

17. $\{ a | a \leq 4$ 或 $a \geq 8 \}$

解析： $A , \ B$ 至少有一个非空，可能的情形较多，其反面只有 $A , \ B$ 都为空集 $1$ 种情况，故先从反面考虑，再取  
假设 $A$ ， $B$ 都是空集，则方程 $x ^ { 2 } - 2 x + 9 - a = 0$ 和  
$a x ^ { 2 } - 4 x + 1 = 0$ 都没有实数解，  
对于 $x ^ { 2 } - 2 x + 9 - a = 0$ ，应有 $\Delta _ { 1 } = ( - 2 ) ^ { 2 } - 4 ( 9 - a )$ （204  
$= 4 a - 3 2 < 0$ ，解得： $a < 8$ $\textcircled{1}$ ，  
对于方程 $a x ^ { 2 } - 4 x + 1 = 0 \ , a = 0$ 与 $a \neq 0$ 时的分析的方法不同，故考虑分类讨论，  
当 $a = 0$ 时，方程 $a x ^ { 2 } - 4 x + 1 = 0$ 即为 $- 4 x + 1 = 0$ ，  
解得： $x = \frac { 1 } { 4 }$ 所以该方程有实数解，  
当 $a \neq 0$ 时，要使方程 $a x ^ { 2 } - 4 x + 1 = 0$ 没有实数解，  
应有 $\Delta _ { 2 } = ( - 4 ) ^ { 2 } - 4 a = 1 6 - 4 a < 0$ ，解得： $a > 4$ ，  
所以当方程 $a x ^ { 2 } - 4 x + 1 = 0$ 没有实数解时， $a > 4 \textcircled { 2 }$   
综合 $\textcircled{1} \textcircled{2}$ 可得 $4 < a < 8$ ，由题意， $A$ ， $B$ 至少有1个是非空集合，所以取补集得 $a \leq 4$ 或 $a \geq 8$ ：

18．(2024·山东青岛模拟) 已知集合 $D = \{ x \mid a x ^ { 2 } + b x + 1 = 0 \}$ ， $E = \left\{ x \mid x ^ { 2 } + a x + b = 0 \right\}$ ，若 $D \cap E \neq \emptyset$ ，且 $- 3 \in ( \complement _ { \mathbf { R } } D ) \cap E$ ，则 $a + b =$ （204

18．-1

解析：怎样翻译 $- 3 \in ( \complement _ { \mathbb { R } } D ) \cap E \textrm { : }$ 可翻译为 $- 3 \in E$ 且 $- 3 \notin D$ ，其中 $- 3 \in E$ 可用于寻找 $a , \ b$ 的关系，进而消元,  
由题意， $- 3 \in E$ 且 $- 3 \notin D$ ，所以 $x = - 3$ 是方程 $x ^ { 2 } + a x + b = 0$ 的解，故 $( - 3 ) ^ { 2 } + a \cdot ( - 3 ) + b = 0$ ，化简得： $b = 3 a - 9$ $\textcircled{1}$ ，  
求 $a , \ b$ 还差一个方程，怎样建立？有了式 $\textcircled{1}$ ，可代回 $E$ 的方程并求解，得到集合 $E _ { i }$ ，再来看条件 $D \cap E \neq \emptyset$   
将 $\textcircled{1}$ 代入 $x ^ { 2 } + a x + b = 0$ 可得 $x ^ { 2 } + a x + 3 a - 9 = 0$ ，  
即 $( x + 3 ) ( x + a - 3 ) = 0$ ，解得： $x = - 3$ 或 $3 - a$ ，  
这里必有 $3 - a \neq - 3$ ，否则 $E = \{ - 3 \}$ ，不能同时满足 $D \cap E$   
$\neq \emptyset$ 和 $- 3 \notin D$ ，所以 $E = \{ - 3 , 3 - a \}$ ，  
元素 $- 3$ 不在 $D$ 中，而 $D$ ， $E$ 又有公共元素，所以只能 $3 - a$ 在 $D$ 中，由此可再建立一个方程,  
因为 $D \cap E \neq \emptyset$ ，且 $- 3 \notin D$ ，所以 $3 - a \in D$ ，  
将 $x = 3 - a$ 代入 $a x ^ { 2 } + b x + 1 = 0$ 得 $a ( 3 - a ) ^ { 2 } + b ( 3 - a ) + 1 = 0$ ，  
结合式 $\textcircled{1}$ 可得 $a ( 3 - a ) ^ { 2 } + ( 3 a - 9 ) ( 3 - a ) + 1 = 0$ ，  
所以 $a ( a - 3 ) ^ { 2 } - 3 ( a - 3 ) ^ { 2 } + 1 = ( a - 3 ) ^ { 3 } + 1 = 0$ ，  
从而 $\left( a - 3 \right) ^ { 3 } = - 1$ ，故 $a - 3 = - 1$ ，所以 $a = 2$ ，  
代入 $\textcircled{1}$ 得 $b = - 3$ ，所以 $a + b = - 1$ ：