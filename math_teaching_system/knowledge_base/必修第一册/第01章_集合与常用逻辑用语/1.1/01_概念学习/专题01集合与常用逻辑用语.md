---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 48
source_file: 专题01 集合与常用逻辑用语（解析版）.md
title: 专题01 集合与常用逻辑用语（解析版）
type: problem_type
---

# 专题01集合与常用逻辑用语

# 考情概览

<html><body><table><tr><td>命题解读</td><td>考向</td><td>考查统计</td></tr><tr><td>1.高考对集合的考查，重点是集合间的 基本运算，主要考查集合的交、并、补 运算，常与一元二次不等式解法、一元 一次不等式解法、分式不等式解法、指 如下两点：</td><td>交集的运算</td><td>2022·新高考I卷，1 2023·新高考I卷，1 2024·新高考I卷，1</td></tr><tr><td rowspan="3">数、对数不等式解法结合. 2.高考对常用逻辑用语的考查重点关注</td><td>根据集合的包含关系求参数</td><td>2022·新高考Ⅱ卷，1 2023·新高考Ⅱ卷，2</td></tr><tr><td>充分必要条件的判定</td><td>2023·新高考I卷，7</td></tr><tr><td>全称、存在量词命题真假的判断</td><td>2024·新高考Ⅱ卷，2</td></tr></table></body></html>

# 2024年真题研析

# 命题分析

2024年高考新高考Ⅱ卷未考查集合，I卷依旧考查了集合的交集运算，常用逻辑用语在新高考Ⅱ卷中考查了全称、存在量词命题真假的判断，这也说明了现在新高考“考无定题”，以前常考的现在不一定考了，抓住知识点和数学核心素养是关键！集合和常用逻辑用语考查应关注：（1）集合的基本运算和充要条件；（2)集合与简单的不等式、函数的定义域、值域的联系。预计 2025年高考还是主要考查集合的基本运算。

# 试题精讲

1．（2024 新高考卷·1）已知集合 $A = \left\{ x | - 5 < x ^ { 3 } < 5 \right\} , B = \{ - 3 , - 1 , 0 , 2 , 3 \}$ ，则 $A \cap B = { \textrm { ( ) } }$

A. $\{ - 1 , 0 \}$ B. {2,3} C. $\{ - 3 , - 1 , 0 \}$ D. {-1,0,2}

【答案】A

【分析】化简集合A，由交集的概念即可得解.

【详解】因为 $A = \left\{ x | - { \sqrt [ { 3 } ] { 5 } } < x < { \sqrt [ { 3 } ] { 5 } } \right\} , B = \left\{ - 3 , - 1 , 0 , 2 , 3 \right\}$ ，且注意到 $1 < \sqrt [ 3 ] { 5 } < 2$ ，从而 $A \bigcap B = \left\{ - 1 , 0 \right\}$ ：

故选：A.

2．（2024 新高考Ⅱ卷·2）已知命题 $p$ ： $\forall x \in \mathbf { R }$ ， $\vert x + 1 \vert > 1$ ；命题 $q$ ： $\exists x > 0$ ， $x ^ { 3 } = x$ ，则（）

A. $p$ 和 $q$ 都是真命题 B. $\neg p$ 和 $q$ 都是真命题C. $p$ 和 $\neg q$ 都是真命题 D. $\neg p$ 和 $\neg q$ 都是真命题

【答案】B

【分析】对于两个命题而言，可分别取 $x { = } - 1$ $x = 1$ ，再结合命题及其否定的真假性相反即可得解.

【详解】对于 $p$ 而言，取 $x { = } { - } 1$ ，则有 $\left| x + 1 \right| = 0 < 1$ ，故 $p$ 是假命题， $\neg p$ 是真命题，对于 $q$ 而言，取 $x = 1$ ，则有 $x ^ { 3 } = 1 ^ { 3 } = 1 = x$ ，故 $q$ 是真命题， $\neg q$ 是假命题，  
综上， $\neg p$ 和 $q$ 都是真命题.  
故选：B.

# 近年真题精选

1．（2022新高考I卷·1）若集合 $M = \{ x \vert \sqrt { x } < 4 \}$ ， $N = \{ x \vert 3 x \ge 1 \}$ ，则 $M \cap N = { \mathrm { ~ ( ~ ) ~ } }$

$$
\mathrm { ~  ~ { ~ A ~ } ~ } \cdot \left\{ x \Big \vert 0 \leq x < 2 \right\} \phantom { + } \mathrm { ~  ~ { ~ \cal ~ B ~ } ~ } \mathrm { ~  ~ { ~ \cal ~ B ~ } ~ } \cdot \begin{array} { r { ~ } { \biggr \{ x \Big \vert \frac { 1 } { 3 } \leq x < 2 \biggr \} } \phantom { + { ~  ~ \cal ~ C ~ } } \subset . \left\{ x \Big \vert 3 \leq x < 1 6 \right\} \phantom { + { ~  ~ \cal ~ D ~ } \cdot ~ } \mathrm { ~  ~ { ~ \cal ~ D ~ } ~ } . } \end{array}
$$

【答案】D

【分析】求出集合 $M , N$ 后可求 $M \cap N$ ：

【详解】 $M = \{ x \mid 0 \leq x < 1 6 \} , N = \{ x \mid x \geq { \frac { 1 } { 3 } } \}$ 故 $M \cap N = \left\{ x { \left| \frac { 1 } { 3 } \leq x < 1 6 \right. } \right\}$ 故选：D

2．（2023新高考I卷·1）已知集合 $M = \left\{ - 2 , - 1 , 0 , 1 , 2 \right\}$ ， $N = \left\{ x { \left| x ^ { 2 } - x - 6 \geq 0 \right. \right\} }$ ，则 $M \cap N =$ （）

A. $\{ - 2 , - 1 , 0 , 1 \}$ B. {0,1,2} C. $\left\{ - 2 \right\}$ D.{2}

【答案】C

【分析】方法一：由一元二次不等式的解法求出集合 $N$ ，即可根据交集的运算解出.  
方法二：将集合 $M$ 中的元素逐个代入不等式验证，即可解出.【详解】方法一：因为 $N = \left\{ x \middle | x ^ { 2 } - x - 6 \geq 0 \right\} = \left( - \infty , - 2 \right] \cup \left[ 3 , + \infty \right) , \forall \Pi \ M = \left\{ - 2 , - 1 , 0 , 1 , 2 \right\} ,$ 所以 $M \cap N = \left\{ - 2 \right\}$   
故选：C.

方法二：因为 $M = \left\{ - 2 , - 1 , 0 , 1 , 2 \right\}$ ，将 $- 2 , - 1 , 0 , 1 , 2$ 代入不等式 $x ^ { 2 } - x - 6 \geq 0$ ，只有 $^ { - 2 }$ 使不等式成立，所以

$M \cap N = \left\{ - 2 \right\} .$ 故选： C.

3．（2022 新高考Ⅱ卷·1）已知集合 $A = \left\{ - 1 , 1 , 2 , 4 \right\} , B = \left\{ x \left\| x - 1 \right\| \leq 1 \right\}$ ，则 $A \cap B = { \textrm { ( ) } }$

A.{-1,2} B.{1,2} C. {1,4} D. $\{ - 1 , 4 \}$

【答案】B

【分析】方法一：求出集合 $B$ 后可求 $A \cap B$ ：

【详解】[方法一]：直接法  
因为 $B = \left\{ x | 0 \leq x \leq 2 \right\}$ ，故 $A \cap B = \left\{ 1 , 2 \right\}$ ，故选：B.  
[方法二]：【最优解】代入排除法  
$x { = } - 1$ 代入集合 $B = \left\{ x \Vert x - 1 \Vert \leq 1 \right\}$ ，可得 $2 \leq 1$ ，不满足，排除 A、D;$x = 4$ 代入集合 $B = \left\{ x \Vert x - 1 \Vert \leq 1 \right\}$ ， 可得 $3 \leq 1$ ，不满足，排除C.故选：B.【整体点评】方法一：直接解不等式，利用交集运算求出，是通性通法;  
方法二：根据选择题特征，利用特殊值代入验证，是该题的最优解.

4．（2023新高考Ⅱ卷·2）设集合 $A = \left\{ 0 , - a \right\}$ ， $B = \left\{ 1 , a - 2 , 2 a - 2 \right\}$ ，若 $A \subseteq B$ ，则 $a = \mathrm { ~  ~ { ~ \left( ~ \begin{array} { l } { \mathrm { ~ \theta ~ } } \\ { \mathrm { ~ \theta ~ } } \end{array} \right) } ~ }$ ：

A.2 B.1 C. $\frac { 2 } { 3 }$ D.-1

【答案】B

【分析】根据包含关系分 $a - 2 = 0$ 和 $2 a - 2 = 0$ 两种情况讨论，运算求解即可.

【详解】因为 $A \subseteq B$ ，则有：  
若 $a - 2 = 0$ ，解得 $a = 2$ ，此时 $A = \left\{ 0 , - 2 \right\}$ ， $B = \left\{ 1 , 0 , 2 \right\}$ ，不符合题意;若 $2 a - 2 = 0$ ，解得 $a = 1$ ，此时 $A = \left\{ 0 , - 1 \right\}$ ， $B = \left\{ 1 , - 1 , 0 \right\}$ ，符合题意；综上所述： $a = 1$ ：  
故选：B.

5．（2023新高考卷·7）记 $S _ { n }$ 为数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和，设甲： $\left\{ a _ { n } \right\}$ 为等差数列；乙： $\{ { \frac { S _ { n } } { n } } \}$ 为等差数列，则（）

A．甲是乙的充分条件但不是必要条件B．甲是乙的必要条件但不是充分条件C．甲是乙的充要条件D．甲既不是乙的充分条件也不是乙的必要条件

【答案】C

【分析】利用充分条件、必要条件的定义及等差数列的定义，再结合数列前 $\cdot$ 项和与第 $\cdot$ 项的关系推理判

断作答.,

【详解】方法1，甲： $\left\{ a _ { n } \right\}$ 为等差数列，设其首项为 $a _ { 1 }$ ，公差为 $d$ ，  
则 $S _ { n } = n a _ { 1 } + { \frac { n ( n - 1 ) } { 2 } } d , { \frac { S _ { n } } { n } } = a _ { 1 } + { \frac { n - 1 } { 2 } } d = { \frac { d } { 2 } } n + a _ { 1 } - { \frac { d } { 2 } } , { \frac { S _ { n + 1 } } { n + 1 } } - { \frac { S _ { n } } { n } } = { \frac { d } { 2 } } ,$   
因此 $\{ { \frac { S _ { n } } { n } } \}$ 为等差数列，则甲是乙的充分条件；  
反之，乙： $\{ { \frac { S _ { n } } { n } } \}$ 为等差数列， 即 $\frac { S _ { n + 1 } } { n + 1 } - \frac { S _ { n } } { n } = \frac { n S _ { n + 1 } - ( n + 1 ) S _ { n } } { n ( n + 1 ) } = \frac { n a _ { n + 1 } - S _ { n } } { n ( n + 1 ) }$ 为常数，设为 $t$ ，髙即 $\frac { n a _ { n + 1 } - S _ { n } } { n ( n + 1 ) } = t$ ，则 $S _ { n } = n a _ { n + 1 } - t \cdot n ( n + 1 )$ ，有 $S _ { n - 1 } = ( n - 1 ) a _ { n } - t \cdot n ( n - 1 ) , n \geq 2$ ，  
两式相减得： $\displaystyle a _ { n } = n a _ { n + 1 } - ( n - 1 ) a _ { n } - 2 t n$ ，即 $a _ { n + 1 } - a _ { n } = 2 t$ ，对 $n = 1$ 也成立,  
因此 $\left\{ a _ { n } \right\}$ 为等差数列，则甲是乙的必要条件，  
所以甲是乙的充要条件，C 正确.  
方法2，甲： $\left\{ a _ { n } \right\}$ 为等差数列，设数列 $\left\{ a _ { n } \right\}$ 的首项 $a _ { 1 }$ ，公差为 $d$ ，即 $S _ { n } = n a _ { 1 } + { \frac { n ( n - 1 ) } { 2 } } d$ ，實则 $\frac { S _ { n } } { n } = a _ { 1 } + \frac { ( n - 1 ) } { 2 } d = \frac { d } { 2 } n + a _ { 1 } - \frac { d } { 2 }$ 因此 $\{ { \frac { S _ { n } } { n } } \}$ 为等差数列，即甲是乙的充分条件；反之，乙： $\{ { \frac { S _ { n } } { n } } \}$ 为等差数列，即 $\frac { S _ { n + 1 } } { n + 1 } - \frac { S _ { n } } { n } = D , \frac { S _ { n } } { n } = S _ { 1 } + ( n - 1 ) D$   
即 $S _ { n } = n S _ { 1 } + n ( n - 1 ) D$ ， $S _ { n - 1 } = ( n - 1 ) S _ { 1 } + ( n - 1 ) ( n - 2 ) D$   
当 $n \geq 2$ 时，上两式相减得： $S _ { n } - S _ { n - 1 } = S _ { 1 } + 2 ( n - 1 ) D$ ，当 $n = 1$ 时，上式成立，  
于是 $a _ { n } = a _ { 1 } + 2 ( n - 1 ) D$ ，又 $a _ { n + 1 } - a _ { n } = a _ { 1 } + 2 n D - [ a _ { 1 } + 2 ( n - 1 ) D ] = 2 D$ 为常数，  
因此 $\left\{ a _ { n } \right\}$ 为等差数列，则甲是乙的必要条件，  
所以甲是乙的充要条件.  
故选：C

# 必备知识速记

# 一、元素与集合

1、集合的含义与表示

某些指定对象的部分或全体构成一个集合．构成集合的元素除了常见的数、点等数学对象外，还可以是其他对象.

2、集合元素的特征

（1）确定性：集合中的元素必须是确定的，任何一个对象都能明确判断出它是否为该集合中的元素.  
（2）互异性：集合中任何两个元素都是互不相同的，即相同元素在同一个集合中不能重复出现.

（3）无序性：集合与其组成元素的顺序无关.

3、元素与集合的关系元素与集合之间的关系包括属于(记作 $a \in A$ )和不属于(记作 $a \not \in A$ )两种.

4、集合的常用表示法

集合的常用表示法有列举法、描述法、图示法(韦恩图).

# 5、常用数集的表示

<html><body><table><tr><td>数集</td><td>自然数集</td><td>正整数集</td><td>整数集</td><td>有理数集</td><td>实数集</td></tr><tr><td>符号</td><td>N</td><td>N或N</td><td>Z</td><td>@</td><td>R</td></tr></table></body></html>

# 二、集合间的基本关系

（1）子集：一般地，对于两个集合 $A \setminus B$ ，如果集合 $A$ 中任意一个元素都是集合 $B$ 中的元素，我们就说这两个集合有包含关系，称集合 $A$ 为集合 $B$ 的子集，记作 $A \subseteq B$ （或 $B \supseteq A ^ { \prime } $ ，读作“ $A$ 包含于 $B ^ { \ ' }$ （或“ $B$ 包含 $\boldsymbol { A } ^ { \flat } )$ ：

（2）真子集：对于两个集合 $A$ 与 $B$ ，若 $A \subseteq B$ ，且存在 $b \in B$ ，但 $b \not \in A$ ，则集合 $A$ 是集合 $B$ 的真子集，记作 $A \subsetneq B$ （或 $B \mathcal { \vec { z } } A$ ）.读作“ $A$ 真包含于 $B$ ”或“ $B$ 真包含 $A$ ”

（3）相等：对于两个集合 $A$ 与 $B$ ，如果 $A \subseteq B$ ，同时 $B \subseteq A$ ，那么集合 $A$ 与 $B$ 相等，记作 $A = B$ ：

（4）空集：把不含任何元素的集合叫做空集，记作 $\emptyset$ ； $\emptyset$ 是任何集合的子集，是任何非空集合的真子集.

# 三、集合的基本运算

（1）交集：由所有属于集合 $A$ 且属于集合 $B$ 的元素组成的集合，叫做 $A$ 与 $B$ 的交集，记作 $A \cap B$ ，即$A \cap B = \left\{ x \mid x \in A { \mathrm { E } } \bot x \in B \right\} .$

（2）并集：由所有属于集合 $A$ 或属于集合 $B$ 的元素组成的集合，叫做 $A$ 与 $B$ 的并集，记作 $A \cup B$ ，即$\begin{array} { r } { A \cup B = \{ x \vert x \in A \bot  \bot x \in B \} . } \end{array}$ （204

（3）补集：对于一个集合 $A$ ，由全集 $U$ 中不属于集合 $A$ 的所有元素组成的集合称为集合 $A$ 相对于全集 $U$ 的补集，简称为集合 $A$ 的补集，记作 $C _ { U } A$ ，即 $C _ { U } A = \{ x \vert x \in U$ 且 $x \notin A \}$ ：

# 四、集合的运算性质

$$
\begin{array} { r l } & { A \bigcap A = A , A \bigcap \emptyset = \emptyset , A \bigcap B = B \bigcap A , A \cap B \subseteq A , A \cap B \subseteq B \cdot } \\ & { A \bigcup A = A , A \bigcup \emptyset = A , A \bigcup B = B \bigcup A , A \subseteq A \cup B , B \subseteq A \cup B \cdot } \\ & { A \bigcap ( C _ { U } A ) = \emptyset , A \bigcup ( C _ { U } A ) = U , C _ { U } ( C _ { U } A ) = A \cdot } \\ & { A \cap B = A \Leftrightarrow A \cup B = B \Leftrightarrow A \subseteq B \Leftrightarrow \complement _ { t } B \subset \complement _ { t } A \Leftrightarrow A \cap \complement _ { t } B = \emptyset } \end{array}
$$

# 【集合常用结论】

（1）若有限集 $A$ 中有 $n$ 个元素，则 $A$ 的子集有 $2 ^ { n }$ 个，真子集有 $2 ^ { n } - 1$ 个，非空子集有 $2 ^ { n } - 1$ 个，非空真子集有 $2 ^ { n } - 2$ 个.

（2）空集是任何集合 $A$ 的子集，是任何非空集合 $B$ 的真子集.(3) $A \subseteq B \Leftrightarrow A \bigcap B = A \Leftrightarrow A \bigcup B = B \Leftrightarrow C _ { U } B \subseteq C _ { U } A \ .$ (4) $C _ { U } ( { \cal A } \bigcap { \cal B } ) = ( C _ { U } { \cal A } ) \bigcup ( C _ { U } { \cal B } ) , C _ { U } ( { \cal A } \bigcup { \cal B } ) = ( C _ { U } { \cal A } ) \bigcap ( C _ { U } { \cal B } ) \ .$

# 五、充分条件、必要条件、充要条件

1、定义如果命题"若 $p$ ，则 $q$ "为真（记作 $p \Longrightarrow q$ ），则 $p$ 是 $q$ 的充分条件；同时 $q$ 是 $p$ 的必要条件.

2、从逻辑推理关系上看

（1）若 $p \Longrightarrow q$ 且 $q \nRightarrow p$ ，则 $p$ 是 $q$ 的充分不必要条件；  
(2）若 $p \nRightarrow q$ 且 $q \Longrightarrow p$ ，则 $p$ 是 $q$ 的必要不充分条件；  
（3）若 $p \Longrightarrow q$ 且 $q \Longrightarrow p$ ，则 $p$ 是 $q$ 的的充要条件（也说 $p$ 和 $q$ 等价）；  
（4）若 $p \nRightarrow q$ 且 $q \nRightarrow p$ ，则 $p$ 不是 $q$ 的充分条件，也不是 $q$ 的必要条件.

# 六、全称量词与存在量词

（1）全称量词与全称量词命题．短语"所有的”、“任意一个"在逻辑中通常叫做全称量词，并用符号“ $\forall$ ”表示．含有全称量词的命题叫做全称量词命题．全称量词命题"对 $M$ 中的任意一个 $x$ ，有 $p ( x )$ 成立"可用符号简记为“ $\forall x \in M , p ( x ) ^ { \prime \prime }$ ，读作“对任意 $x$ 属于 $M$ ，有 $p ( x )$ 成立”.

（2）存在量词与存在量词命题．短语"存在一个”、“至少有一个"在逻辑中通常叫做存在量词，并用符号“"表示．含有存在量词的命题叫做存在量词命题．存在量词命题"存在 $M$ 中的一个 $x _ { 0 }$ ，使 $p ( x _ { 0 } )$ 成立"可用符号简记为" $\exists x _ { 0 } \in M , P ( x _ { 0 } ) ^ { \mathfrak { N } }$ ，读作"存在 $M$ 中元素 $x _ { 0 }$ ，使 $p ( x _ { 0 } )$ 成立”（存在量词命题也叫存在性命题）.

七、含有一个量词的命题的否定

（1）全称量词命题 $p : \forall x \in M , p ( x )$ 的否定 $\neg p$ 为 $\exists x _ { 0 } \in M$ ， $\neg p ( x _ { 0 } )$ ：（2）存在量词命题 $p : \exists x _ { 0 } \in M , p ( x _ { 0 } )$ 的否定 $\neg p$ 为 $\forall x \in M , \lnot p ( x )$ ：注：全称、存在量词命题的否定是高考常见考点之一.

# 【常用逻辑用语常用结论】

1、从集合与集合之间的关系上看 设 $A = \left\{ x \mid p ( x ) \right\} , B = \left\{ x \mid q ( x ) \right\}$

（1）若 $A \subseteq B$ ，则 $p$ 是 $q$ 的充分条件（ $p \Longrightarrow q$ ）， $q$ 是 $p$ 的必要条件；若 $A \subsetneq { \mathbb { B } }$ ，则 $p$ 是 $q$ 的充分不必要条件， $q$ 是 $p$ 的必要不充分条件，即 $p \Longrightarrow q$ 且 $q \nRightarrow p$ ；  
注：关于数集间的充分必要条件满足：“小 $\Rightarrow$ 大”.  
（2）若 $B \subseteq A$ ，则 $p$ 是 $q$ 的必要条件， $q$ 是 $p$ 的充分条件；  
(3）若 $A = B$ ，则 $p$ 与 $q$ 互为充要条件.

# 集合三模题

# 一、单选题

1．（2024·河南·三模）命题" $\exists x > 0 , x ^ { 2 } + x - 1 > 0$ "的否定是（）

A. $\forall x > 0 , x ^ { 2 } + x - 1 > 0$ B. $\forall x > 0 , x ^ { 2 } + x - 1 \leq 0$   
C. $\exists x \leq 0 , x ^ { 2 } + x - 1 > 0$ $\mathrm { ~ \mathsf ~ { ~ D ~ . ~ } ~ } \exists x \leq 0 , x ^ { 2 } + x - 1 \leq 0$

【答案】B

【分析】根据存在量词命题的否定形式，即可求解.

【详解】根据存在量词命题的否定为全称量词命题，即命题" $\exists x > 0 , x ^ { 2 } + x - 1 > 0$ "的否定为“ $\forall x > 0 , x ^ { 2 } + x - 1 \leq 0 ^ { , }$ ，故选：B.

2．（2024·湖南长沙·三模）已知集合 $M = \left\{ x | | x | { \leqslant } 2 \right\}$ ， $N = \{ x \vert \mathrm { l n } x < 1 \}$ ，则 $M \cap N = { \mathrm { ~ ( ~ ) ~ } }$

A. [2,e) B. [-2,1] c. [0,2) D. (0,2]

【答案】D

【分析】由对数函数单调性解不等式，化简 $N$ ，根据交集运算求解即可.

【详解】因为 $M = \left[ - 2 , 2 \right] , N = \left( 0 , { \mathrm e } \right)$ 所以 $M \cap N = \left( 0 , 2 \right]$ ：  
故选：D.

3. （2024-河北衡水·三模）已知集合 $A = \left\{ 1 , 2 , 3 , 4 , 5 \right\}$ ， $B = \left\{ x { \big | } - 1 \leq \log { \big ( } x - 1 { \big ) } \leq { \frac { 1 } { 2 } } \right\}$ ，则 $A \cap B = { \textrm { ( ) } }$

A $\left\{ x \vert \frac { 1 1 } { 1 0 } \leq x \leq 5 \right\}$ B.(2.3.4 C. {2,3} D妝 $\cdot \quad \left\{ x \Big | \frac { 1 1 } { 1 0 } \leq x \leq 3 \right\}$

【答案】B

【分析】求得 $B = \left\{ x \Big | \frac { 1 1 } { 1 0 } \leq x \leq \sqrt { 1 0 } + 1 \right\}$ ，可求 $A \cap B$

【详解】 $B = \left\{ x { \big | } - 1 \leq \lg ( x - 1 ) \leq { \frac { 1 } { 2 } } \right\} = \left\{ x { \big | } { \frac { 1 1 } { 1 0 } } \leq x \leq { \sqrt { 1 0 } } + 1 \right\}$ 又 $A = \{ 1 , 2 , 3 , 4 , 5 \}$ ，故 $A \bigcap B = \{ 2 , 3 , 4 \}$ ，  
故选：B.

4．（2024·陕西·三模）已知集合 $A = \left\{ x | - 1 \leq x \leq 2 \right\} , B = \left\{ x | - x ^ { 2 } + 3 x > 0 \right\}$ ，则 $A \cup B =$ （）

A.R B. (0,2] ${ \mathrm { C } } , \ { \bigl [ } { - } 1 , 0 { \bigr ) }$ D. [-1,3)

【答案】D

【分析】先解一元二次不等式求出集合 $B$ ，再根据集合并集定义计算即可.

【详解】由 $- x ^ { 2 } + 3 x > 0$ ，解得 $0 < x < 3$ ，所以集合 $B = \{ x \mid 0 < x < 3 \}$ ，所以 $A \cup B = \{ x | - 1 \leq x < 3 \}$ ，所以 $A \cup B = [ - 1 , 3 )$ ：  
故选：D.

5．（2024·安徽·三模）已知集合 $A = \left\{ x \lvert - 5 \leq x \leq 1 \right\}$ ， $B = \left\{ x | x > - 2 \right\}$ ，则图中所示的阴影部分的集合可以表示为（）

![](images/d19a7ef50bdca80196ed778d79308bc1c7a97062693f98f915ada6d294319b44.jpg)

A. $\left\{ x | - 2 \leq x \leq 1 \right\}$ B. $\left\{ x | - 2 < x \leq 1 \right\}$ C. $\left\{ x | - 5 \leq x \leq - 2 \right\}$ D.{x|-5≤x<-2}

【答案】C

【分析】图中所示的阴影部分的集合为 $\complement _ { \mathfrak { R } } B \cap A$ ，结合集合的运算即可得解.

【详解】由图可知，阴影部分表示的集合的元素为 $\complement _ { \mathfrak { R } } B \cap A$ ，而 $A = \left\{ x \lvert - 5 \leq x \leq 1 \right\}$ ， $B = \left\{ x | x > - 2 \right\}$ ，则 $\complement _ { \lambda } B = \left\{ x \middle | x \leq - 2 \right\}$ 得 $\complement _ { \mathfrak { R } } B \cap A = \left\{ x | - 5 \leq x \leq - 2 \right\}$   
故所求集合为 $\left\{ x | - 5 \leq x \leq - 2 \right\}$   
故选：C.

6．（2024·湖南长沙·三模）已知直线 $l : k x - y + { \sqrt { 2 } } k = 0$ ，圆 $O : x ^ { 2 } + y ^ { 2 } = 1$ ，则“ $k < 1$ "是"直线l上存在点 $P$ ，使点 $P$ 在圆 $O$ 内"的（）

A．充分不必要条件 B．必要不充分条件C．充要条件 D．既不充分也不必要条件

【答案】B

【分析】由直线与圆相交可求得 $- 1 < k < 1$ ，则通过判断 $- 1 < k < 1$ 与 $k < 1$ 的关系可得答案.

【详解】由直线/上存在点 $P$ ，使点 $P$ 在圆 $O$ 内，得直线 $l$ 与圆 $O$ 相交，即 $\frac { \left| \sqrt { 2 } k \right| } { \sqrt { k ^ { 2 } + 1 } } < 1 ,$ 解得 $- 1 < k < 1$ ，即 $k \in \left( - 1 , 1 \right)$ ，  
因为 $k < 1$ 不一定能得到 $- 1 < k < 1$ ，而 $- 1 < k < 1$ 可推出 $k < 1$ ，  
所以" $k <$ "是"直线/上存在点 $P$ ，使点 $P$ 在圆 $O$ 内"的必要不充分条件.

# 故选：B

7．（2024·湖北荆州·三模）已知集合 $A = \left\{ x { \big | } 2 x - x ^ { 2 } \leq 0 \right\}$ ， $B = \complement _ { \mathbf { R } } A$ ，其中 $\mathbf { R }$ 是实数集，集合 $C = \left( - \infty , 1 \right]$ ，则$B \cap C = { \mathrm { ~ ( ~ ) ~ } }$

A. $\left( - \infty , 0 \right]$ B. (0,1] C. $\left( - \infty , 0 \right)$ D. (0,1)

【答案】B

【分析】解出一元二次不等式后，结合补集定义与交集定义计算即可得.

【详解】由 $2 x - x ^ { 2 } \leq 0$ 可得 $x \le 0$ 或 $x \ge 2$ ，则 $B = \complement _ { \mathbf { R } } A = \left\{ x { \big | } 0 < x < 2 \right\}$ 又 $C = \left( - \infty , 1 \right]$ ，故 $B \cap C = \left( 0 , 1 \right]$ ：  
故选：B.

8．（2024·北京·三模）已知集合 $A = \left\{ x | \mathrm { l n } x < 1 \right\}$ ，若 $a \not \in A$ ，则 $a$ 可能是（）

A. B.1 C.2 D.3

【答案】D

【分析】解对数不等式化简集合A，进而求出 $a$ 的取值集合即得.

【详解】由 $\ln x < 1$ ，得 $0 < x < \mathsf { e }$ ，则 $A = \{ x | 0 < x < \mathbf { e } \}$ ， $\complement _ { \mathbb { R } } A = \{ x \mid x \leq 0$ 或 $\geq { \mathsf { e } } \}$ ，由 $a \not \in A$ ，得 $a \in \complement _ { \mathbb { R } } A$ ，显然选项 ABC 不满足， $\mathbf { D }$ 满足.  
故选：D

9. （2024·河北衡水·三模）已知函数 $f ( x ) = { \left( 2 ^ { x } + m \cdot 2 ^ { - x } \right) } \sin x$ ，则“ $m ^ { 2 } = 1$ ”是“函数 $f ( x )$ 是奇函数"的（）

A．充分不必要条件 B．必要不充分条件C．充要条件D．既不充分也不必要条件

【答案】B

【分析】由函数 $f ( x )$ 是奇函数，可求得 $m = 1$ ，可得结论.

【详解】若函数 $f ( x )$ 是奇函数，  
则 $f ( x ) + f ( - x ) = \left( 2 ^ { x } + m \cdot 2 ^ { - x } \right) \sin x - \left( 2 ^ { - x } + m \cdot 2 ^ { x } \right) \sin x = ( 1 - m ) \left( 2 ^ { x } - 2 ^ { - x } \right) \sin x = 0$ 恒成立，即 $m = 1$ ，而 $m ^ { 2 } = 1$ ，得 $m = \pm 1$ ·  
故“ $m ^ { 2 } = 1 ^ { \cdot }$ ”是"函数 $f ( x )$ 是奇函数"的必要不充分条件.  
故选：B.

10．（2024·内蒙古·三模）设 $\alpha$ ， $\beta$ 是两个不同的平面， $m$ ， $l$ 是两条不同的直线，且 $\alpha \cap \beta = l$ 则 $m / / l$ ”是“ $m / / \beta$ 且 $m / \alpha$ "的（）

A．充分不必要条件 B．充分必要条件C．必要不充分条件 D．既不充分也不必要条件

【答案】C

【分析】根据题意，利用线面平行的判定定理与性质定理，结合充分条件、必要条件的判定方法，即可求解.

【详解】当 $m / / l$ 时， $m$ 可能在 $\alpha$ 内或者 $\beta$ 内，故不能推出 $m / / \beta$ 且 $m / \alpha$ ，所以充分性不成立；当 $m / / \beta$ 且 $m / \alpha$ 时，设存在直线 $n \subset \alpha$ ， $n \not \subset \beta$ ，且 $n / m$ ，  
因为 $m / / \beta$ ，所以 $n / \beta$ ，根据直线与平面平行的性质定理，可知 $n / l$ ，  
所以 $m / / l$ ，即必要性成立，故“ $m / / l$ ”是“ $m / / \beta$ 且 $m / \alpha$ "的必要不充分条件.  
故选：C.

11．（2024·北京·三模）已知 $A = \{ x { | \log _ { 2 } { ( x - 1 ) } \leq 1 \} }$ ， $B = \left\{ x \Big \| x - 3 \Big | > 2 \right\}$ ，则 $A \cap B = { \textrm { ( ) } }$

A.空集 B.{x|x≤3或 $x > 5 \}$ C. $\left\{ x { \big | } x \leq 3 \right.$ 或 $x > 5$ 且 $x \neq 1 \}$ D．以上都不对

【答案】A

【分析】先求出集合 $A , B$ ，再由交集的定义求解即可.

【详解】 $A = \{ x | \log _ { 2 } { ( x - 1 ) } \leq \log _ { 2 } { 2 } \} = \{ x | 0 < x - 1 \leq 2  \} = \{ x | 1 < x \leq 3  \} ,$   
$B = \left\{ x \middle | x - 3 > 2 x - 3 < - 2 \right\} = \left\{ x \middle | x < 1 x > 5 \right\}$   
所以 $A \cap B = \emptyset$ ：  
故选：A

12．（2024·四川·三模）已知集合 $A = \left\{ 0 , 3 , 5 \right\}$ ， $B = \left\{ x { \big | } x { \big ( } x - 2 { \big ) } = 0 \right\}$ ，则 $A \cap B = { \textrm { ( ) } }$

A.Q B. $\left\{ 0 \right\}$ C. {0,2,3,5} D.{0,3}

【答案】B

【分析】将集合 $B$ 化简，然后结合交集的运算，即可得到结果.

【详解】由题意 $B = \left\{ x { \big | } x ( x - 2 ) = 0 \right\} = \left\{ 0 , 2 \right\}$ ，所以 $A \cap B = \left\{ 0 , 3 , 5 \right\} \cap \left\{ 0 , 2 \right\} = \left\{ 0 \right\}$ 故选：B.

13. （2024·重庆·三模）已知集合 $A = \{ x \in \mathbf { R } { | x ^ { 2 } - x - 2 < 0  } \} , B = \{ y | \ y = 2 ^ { x } , x \in A \}$ ，则 $A \cap B =$ （）

A. (-1,4) B $\left( { \frac { 1 } { 4 } } , 1 \right)$ $\left( { \frac { 1 } { 2 } } , 1 \right)$ $\mathbf { D } . \left( { \frac { 1 } { 2 } } , 2 \right)$

【答案】D

【分析】解一元二次不等式求解集合A，根据指数函数单调性求解值域得集合B，然后利用交集运算求解即可.

【详解】 $A = \left\{ x \in \mathbf { R } { \Big | } x ^ { 2 } - x - 2 < 0 \right\} = \left\{ x \in \mathbf { R } { \Big | } { \big ( } x - 2 { \big ) } { \big ( } x + 1 { \big ) } < 0 \right\} = \left\{ x \in \mathbf { R } { \big | } - 1 < x < 2 \right\} = ( - 1 , 2 )$ 實则 $B = \left\{ y \middle | y = 2 ^ { x } , x \in \left( - 1 , 2 \right) \right\} = \left\{ y \middle | \frac { 1 } { 2 } < y < 4 \right\} = \left( \frac { 1 } { 2 } , 4 \right)$

所以 $A \bigcap B = \left( { \frac { 1 } { 2 } } , 2 \right)$ 故选：D

14. （2024·北京·三模）“ ${ \triangle A B C }$ 为锐角三角形"是"sin $A > \cos B$ ， $\sin B > \cos C$ ，sin $C > \cos A$ ”的（）

A．充分不必要条件 B．必要不充分条件C．充分必要条件 D．既不充分也不必要条件【答案】C【分析】根据诱导公式及正弦函数的单调性，再结合充分条件和必要条件的定义即可得解.

【详解】充分性：  
因为 ${ \triangle A B C }$ 为锐角三角形,  
所以 $A + B > \frac { \pi } { 2 }$ ， 即 $\frac { \pi } { 2 } > A > \frac { \pi } { 2 } - B > 0$ ，  
所以si $\displaystyle { \mathfrak { n } } A > \sin \left( { \frac { \pi } { 2 } } - B \right) = \cos B$ ，  
同理可得 $\sin B > \cos C$ ， $\sin C > \cos A$ ，  
故充分性得证;  
必要性：  
因为sin $A > \cos B$ ，所以sin $A > \sin \left( { \frac { \pi } { 2 } } - B \right)$ 因为 $0 < B < \pi$ ，所以 $- \frac { \pi } { 2 } < \frac { \pi } { 2 } - B < \frac { \pi } { 2 }$   
若 $A > \frac { \pi } { 2 }$ 则 $A + B > \frac { \pi } { 2 }$   
若 $A \leq \frac { \pi } { 2 }$ 则 $A > \frac { \pi } { 2 } - B$ ，所以 $A + B > \frac { \pi } { 2 }$ 综上， $A + B > \frac { \pi } { 2 }$   
同理 $B + C > \frac { \pi } { 2 } , A + C > \frac { \pi } { 2 }$   
所以 ${ \triangle A B C }$ 为锐角三角形,  
必要性得证,  
综上所述，为充分必要条件.  
故选：C.

15．（2024·上海·三模）设 $1 < a < b$ ，集合 $A = \left\{ 1 , a , b \right\}$ ，集合 $B = \left\{ t { \left| t = x y + \frac { y } { x } , x , y \in A , x \neq y \right. } \right\}$ ，对于集合 $B$ 有下列两个结论： $\textcircled{1}$ 存在 $a$ 和 $^ { b }$ ，使得集合 $B$ 中恰有5个元素； $\textcircled{2}$ 存在 $a$ 和 $^ { b }$ ，使得集合 $B$ 中恰有4个元素．则下列判断正确的是（）

A. $\textcircled{1} \textcircled{2}$ 都正确B. $\textcircled{1} \textcircled{2}$ 都错误 C. $\textcircled{1}$ 错误， $\textcircled{2}$ 正确D. $\textcircled{1}$ 正确， $\textcircled{2}$ 错误

【答案】A

【分析】由题意可知 $2 a < 2 b , a + \frac { 1 } { a } < b + \frac { 1 } { b } < a b + \frac { a } { b } < a b + \frac { b } { a }$ 对于 $\textcircled { 1 0 }$ 举例分析判断即可，对于 $\textcircled{2}$ ，若$\left\{ \begin{array} { l l } { \displaystyle 2 a = b + \frac { 1 } { b } } \\ { \displaystyle 2 b = a b + \frac { a } { b } } \end{array} \right.$ 则 $b + \frac { 1 } { b } = 2 \sqrt { b }$ ， 然后构造函数，利用导数结合零点存性定理可确定出 $b$ ，从而可进行判断.

【详解】当 $x = 1 , y = a$ 时， $t = x y + { \frac { y } { x } } = a + a = 2 a$ ，  
当 $x = 1 , y = b$ 时， $t = x y + \frac { y } { x } = b + b = 2 b$ ，  
当 $x = a , y = 1$ 时， $t = x y + { \frac { y } { x } } = a + { \frac { 1 } { a } }$ ，  
当 $x = a , y = b$ 时， $t = x y + { \frac { y } { x } } = a b + { \frac { b } { a } }$   
当 $x = b , y = 1$ 时， $t = x y + { \frac { y } { x } } = b + { \frac { 1 } { b } }$   
当 $x = b , y = a$ 时， $t = x y + { \frac { y } { x } } = a b + { \frac { a } { b } }$ ，  
因为 $1 < a < b$ ，所以 $2 a < 2 b , a + \frac { 1 } { a } < b + \frac { 1 } { b } < a b + \frac { a } { b } < a b + \frac { b } { a } ,$   
当 $a = \frac { 3 } { 2 } , b = \sqrt { 3 }$ 时， $2 a = 3 , 2 b = 2 { \sqrt { 3 } } , a + { \frac { 1 } { a } } = { \frac { 3 } { 2 } } + { \frac { 2 } { 3 } } = { \frac { 1 3 } { 6 } } , b + { \frac { 1 } { b } } = { \sqrt { 3 } } + { \frac { 1 } { \sqrt { 3 } } } = { \frac { 4 { \sqrt { 3 } } } { 3 } }$   
$a b + { \frac { b } { a } } = { \frac { 3 } { 2 } } \sqrt { 3 } + { \frac { 2 } { 3 } } \sqrt { 3 } = { \frac { 1 3 } { 6 } } \sqrt { 3 } \ , a b + { \frac { a } { b } } = { \frac { 3 } { 2 } } \sqrt { 3 } + { \frac { 3 } { 2 } } \times { \frac { \sqrt { 3 } } { 3 } } = 2 \sqrt { 3 } \ ,$   
所以 $B = \left\{ 3 , 2 { \sqrt { 3 } } , { \frac { 1 3 } { 6 } } , { \frac { 4 } { 3 } } { \sqrt { 3 } } , { \frac { 1 3 } { 6 } } { \sqrt { 3 } } \right\}$ 有5个元素，所以 $\textcircled{1}$ 正确，$\left\{ \begin{array} { l l } { \displaystyle 2 a = b + \frac { 1 } { b } } \\ { \displaystyle 2 b = a b + \frac { a } { b } } \end{array} \right. ,$ $4 b = \left( b + { \frac { 1 } { b } } \right) ^ { 2 }$ ， 得 $b + { \frac { 1 } { b } } = 2 { \sqrt { b } }$ ，$f ( x ) = x + { \frac { 1 } { x } } - 2 { \sqrt { x } } ( x > 1 )$ ，则 $f ^ { \prime } ( x ) = 1 - { \frac { 1 } { { x } ^ { 2 } } } - { x } ^ { - { \frac { 1 } { 2 } } } ( x > 1 ) { } _ { } :$   
令 $g ( x ) = 1 - { \frac { 1 } { x ^ { 2 } } } - x ^ { - { \frac { 1 } { 2 } } } ( x > 1 )$ ，则 $g ^ { \prime } ( x ) = \frac { 2 } { x ^ { 3 } } + \frac { 1 } { 2 } x ^ { - \frac { 3 } { 2 } } > 0 ( x > 1 )$   
所以 $g ( x )$ 在 $( 1 , + \infty )$ 上递增，即 $f ^ { ' } ( x )$ 在 $( 1 , + \infty )$ 上递增，  
所以当x>2时，f(x>(2)=√  
所以 $f ( x )$ 在 $( 2 , + \infty )$ 上递增,  
因为 $f ( 2 ) = 2 + { \frac { 1 } { 2 } } - 2 { \sqrt { 2 } } < 0 , f ( 4 ) = 4 + { \frac { 1 } { 4 } } - 2 { \sqrt { 4 } } = { \frac { 1 } { 4 } } > 0$   
所以存在 $b \in ( 2 , 4 )$ ，使 $f ( b ) = 0$ ，即存在 $b \in ( 2 , 4 )$ ， $b + { \frac { 1 } { b } } = 2 { \sqrt { b } }$ 成立，  
此时 $a = { \frac { 1 } { 2 } } { \binom { b + { \frac { 1 } { b } } } { b } }$ ，  
所以存在a和 $\mathbf { b }$ ，使得集合B中恰有4个元素，所以 $\textcircled{4}$ 正确，  
故选：A

【点睛】关键点点睛：判断结论 $\textcircled{2}$ 的关键是构造函数，利用导数和零点存在性定理分析判断.

# 二、多选题

16．（2024·江西南昌·三模）下列结论正确的是（）

A.若 $\left\{ x | x + 3 > 0 \right\} \cap \left\{ x | x - a < 0 \right\} = \emptyset$ ，则 $a$ 的取值范围是 $a < - 3$ B.若 $\left\{ x | x + 3 > 0 \right\} \cap \left\{ x | x - a < 0 \right\} = \emptyset$ ，则 $a$ 的取值范围是 $a \leq - 3$ C.若 $\left\{ x { \big | } x + 3 > 0 \right\} \cup \left\{ x { \big | } x - a < 0 \right\} = \mathbf { R }$ ，则 $a$ 的取值范围是 $a \ge - 3$ （204 D.若 $\left\{ x { \big | } x + 3 > 0 \right\} \cup \left\{ x { \big | } x - a < 0 \right\} = \mathbf { R }$ ，则 $a$ 的取值范围是 $a > - 3$

【答案】BD

【分析】先将条件等价转化，然后根据对应范围判断命题的真假即可.

【详解】对于选项A和B, $\left\{ x { \big | } x + 3 > 0 \right\} = \left\{ x { \big | } x > - 3 \right\} \qquad \left\{ x { \big | } x - a < 0 \right\} = \left\{ x { \big | } x < a \right\}$ 若 $\{ x | x > - 3 \} \cap \{ x | x < a \} = \emptyset$ ，则 $a$ 的取值范围是 $a \leq - 3$ ，所以A错误，B正确；对于选项 $\cdot$ 和D，若 $\{ x | x > - 3 \} \cup \{ x | x < a \} = \mathrm { R }$ ，则 $a$ 的取值范围是 $a > - 3$ ，所以D正确，C错误.故选：BD.

17．（2024·辽宁·三模）已知 $\operatorname* { m a x } \left\{ x _ { 1 } , x _ { 2 } , \cdots , x _ { n } \right\}$ 表示 $x _ { 1 } , x _ { 2 } , \cdots , x _ { n }$ 这 $n$ 个数中最大的数．能说明命题“ $\forall a , b , c$ ，$\epsilon \mathrm { R } \ , \operatorname* { m a x } \left\{ a , b \right\} + \operatorname* { m a x } \left\{ c , d \right\} \geq \operatorname* { m a x } \left\{ a , b , c , d \right\}$ "是假命题的对应的一组整数 $a$ ， $^ { b }$ ， $c$ ， $d$ 值的选项有（）

A.1，2,3，4 B.-3，-1，7，5  
C.8，-1， $^ { - 2 }$ ， $^ { - 3 }$ D.5，3，0，-1

【答案】BC

【分析】根据 $\operatorname* { m a x } \left\{ x _ { 1 } , x _ { 2 } , \cdots , x _ { n } \right\}$ 的含义说明AD 不符合题意，举出具体情况说明BC，符合题意即可.

【详解】对于A，D，从其中任取两个数作为一组，剩下的两数作为另一组，由于这两组数中的最大的数都不是负数，其中一组中的最大数即为这四个数中的最大值，故都能使得命题" $\forall a , b , c , d \in \mathbb { R }$ ， $\operatorname* { m a x } { \left\{ a , b \right\} } + \operatorname* { m a x } { \left\{ c , d \right\} } \geq \operatorname* { m a x } { \left\{ a , b , c , d \right\} }$ "成立；

对于B，当 $\operatorname* { m a x } { \left\{ a , b \right\} } = \operatorname* { m a x } { \left\{ - 3 , - 1 \right\} } = - 1 , \operatorname* { m a x } { \left\{ 7 , 5 \right\} } = 7$ 时，而 $\operatorname* { m a x } \left\{ - 3 , - 1 , 7 , 5 \right\} = 7$ ，此时 $- 1 + 7 < 7$ ，即命题“ $\forall a , b , c$ ， $d \in \mathbb { R }$ ， $\operatorname* { m a x } { \left\{ a , b \right\} } + \operatorname* { m a x } { \left\{ c , d \right\} } \geq \operatorname* { m a x } { \left\{ a , b , c , d \right\} }$ "是假命题;对于C，当 $\operatorname* { m a x } { \left\{ a , b \right\} } = \operatorname* { m a x } { \left\{ 8 , - 1 \right\} } = 8 , \operatorname* { m a x } { \left\{ - 2 , - 3 \right\} } = - 2$ 时，而 $\operatorname* { m a x } { \left\{ 8 , - 1 , - 2 , - 3 \right\} } = 8$ ，此时 $- 2 + 8 < 8$ ，即命题“ $\forall a , b , c$ ， $d \in \mathbb { R }$ ， $\operatorname* { m a x } { \left\{ a , b \right\} } + \operatorname* { m a x } { \left\{ c , d \right\} } \geq \operatorname* { m a x } { \left\{ a , b , c , d \right\} }$ "是假命题;故选：BC

18．（2024·重庆·三模）命题"存在 $x > 0$ ，使得 $m x ^ { 2 } + 2 x - 1 > 0$ "为真命题的一个充分不必要条件是（）

A. $m > - 2$ B. $m > - 1$ C. $m > 0$ （204 D. m>1

【答案】CD

【分析】根据题意，转化为存在 $x > 0$ ，设定 $m > \frac { 1 - 2 x } { x ^ { 2 } }$ 利用二次函数的性质，求得 $\frac { 1 - 2 x } { x ^ { 2 } }$ 的最小值为 $^ { - 1 }$ ，求得 $m$ 的取值范围，结合充分不必要条件的定义和选项，即可求解.

【详解】由题意，存在 $x > 0$ ，使得 $m x ^ { 2 } + 2 x - 1 > 0$ ，即 $m > \frac { 1 - 2 x } { x ^ { 2 } } = ( \frac { 1 } { x } ) ^ { 2 } - 2 \times \frac { 1 } { x } = ( \frac { 1 } { x } - 1 ) ^ { 2 } - 1 ,$ 当 ${ \frac { 1 } { x } } - 1 = 0$ 时，即 $x = 1$ 时， $\frac { 1 - 2 x } { x ^ { 2 } }$ 的最小值为 $^ { - 1 }$ ，故 $m > - 1$ ：  
所以命题"存在 $x > 0$ ，使得 $m x ^ { 2 } + 2 x - 1 > 0$ "为真命题的充分不必要条件是 $\{ m | m  - 1 \}$ 的真子集，结合选项可得，C和 $\cdot$ 项符合条件.  
故选：CD.

19. （2024·黑龙江齐齐哈尔·三模）已知 $a , b > 0$ ，则使得“ $a > b$ "成立的一个充分条件可以是（）

$$
\mathrm { ~ \mathsf ~ { ~ A ~ . ~ } ~ } \frac { 1 } { \alpha } < \frac { 1 } { b } \qquad \mathrm { ~ \mathsf ~ { ~ B ~ . ~ } ~ } \mid a - 2 \mid \gamma \mid b - 2 \mid \qquad \mathrm { ~ \mathsf ~ { ~ C ~ . ~ } ~ } \ a ^ { 2 } b - a b ^ { 2 } > a - b \quad \mathrm { ~ \mathsf ~ { ~ D ~ . ~ } ~ } \ln \left( a ^ { 2 } + 1 \right) > \ln \left( b ^ { 2 } + 1 \right)
$$

【答案】AD

【分析】由不等式的性质可判断AD；取特值可判断 $\cdot$ ; $a ^ { 2 } b - a b ^ { 2 } > a - b$ 可化为 $a + { \frac { 1 } { a } } > b + { \frac { 1 } { b } }$ 结合 $\mathbf { y } = \mathbf { x } + { \frac { 1 } { \mathbf { x } } }$ 的单调性可判断C.

【详解】对于A，因为 $a b > 0$ $\frac { b } { a b } < \frac { a } { a b }$ 故 $a > b$ 故 $\mathbf { A }$ 选项正确;  
对于B，取 $a = 1 , b = 2$ ，此时满足 $1 > 0$ ，但 $a < b$ ， $\mathbf { B }$ 选项错误;  
对于C， $a ^ { 2 } b - a b ^ { 2 } > a - b$ 可得： $a ^ { 2 } b + b > a b ^ { 2 } + a$ ，  
则 $b { \Big ( } a ^ { 2 } + 1 { \Big ) } > a { \Big ( } b ^ { 2 } + 1 { \Big ) }$ ，因为 $a , b > 0$ ， 囍即 ${ \frac { a ^ { 2 } + 1 } { a } } > { \frac { b ^ { 2 } + 1 } { b } }$   
所以 $a + { \frac { 1 } { a } } > b + { \frac { 1 } { b } }$ ， 因为函数 $\mathbf { y } = \mathbf { x } + { \frac { 1 } { \mathbf { x } } }$ 在 $( 0 , + \infty )$ 不单调，所以C选项错误;对于 $\cdot$ ，由 $\ln \left( a ^ { 2 } + 1 \right) > \ln \left( b ^ { 2 } + 1 \right) \cdot$ 可知， $a ^ { 2 } > b ^ { 2 }$ ，因为 $a , b > 0$ ，  
所以 $a > b$ ，故 $\mathbf { D }$ 选项正确,

# 故选：AD.

20．（2024·安徽安庆·三模）已知集合 $A = \left\{ x \in \mathbf { Z } { \big | } x ^ { 2 } - 2 x - 8 < 0 \right\}$ ，集合 $B = \left\{ x { \big | } 9 ^ { x } > 3 ^ { m } , m \in \mathbf { R } , x \in \mathbf { R } \right\}$ ，若 $A \cap B$ （202 有且仅有3个不同元素，则实数 $m$ 的值可以为（）

A.0 B.1 C.2 D.3

【答案】AB

【分析】解一元二次不等式可得A，结合指数函数性质可解出 $B$ ，结合交集性质即可得解.

【详解】由 $x ^ { 2 } - 2 x - 8 < 0$ ，解得 $- 2 < x < 4$ ，  
故 $A = \left\{ x \in Z { \left| x ^ { 2 } - 2 x - 8 < 0 \right. \right\} } = \left\{ - 1 , 0 , 1 , 2 , 3 \right\} ,$   
由 $9 ^ { x } > 3 ^ { m }$ ，可得 $x > \frac { m } { 2 }$ ，  
$B = \left\{ x { \left| { \mathfrak { g } } ^ { x } > 3 ^ { m } , m \in \mathbf { R } , x \in \mathbf { R } \right. \right\} } = \left\{ x { \left| x > \frac { m } { 2 } , m \in \mathbf { R } , x \in \mathbf { R } \right. } \right\}$   
要使 $A \cap B$ 有且仅有3个不同元素，则 $0 \leq \frac { m } { 2 } < 1$ <m<1，解得0≤m<2,故选：AB.

# 三、填空题

21．（2024·湖南长沙·三模）已知集合 $A = \left\{ 1 , 2 , 4 \right\}$ ， $B = \left\{ a , a ^ { 2 } \right\}$ ，若 $A \cup B = A$ ，则 $a = \_$

【答案】2

【分析】由 $A \cup B = A$ 得 $B \subseteq A$ ，令 $a = 1$ $a = 2$ 、 $a = 4$ 求出集合B，即可求解.

【详解】由 $A \cup B = A$ ，得 $B \subseteq A$ ：  
当 $a = 1$ 时， $a = a ^ { 2 }$ ，不满足元素的互异性，舍去；当 $a = 2$ 时， $B = \{ 2 , 4 \}$ ，满足 $B \subseteq A$ ，符合题意；当 $a = 4$ 时， $B = \{ 4 , 1 6 \}$ ，不满足 $B \subseteq A$ ，舍去.综上， $a = 2$   
故答案为：2

22．（2024·上海·三模）已知集合 $A = \left\{ 0 , 1 , 2 \right\}$ ， $B = \{ x { | x ^ { 3 } - 3 x \leq 1 \} }$ ，则 $A \cap B = .$

【答案】{0,1}

【分析】把集合中的元素代入不等式 $x ^ { 3 } - 3 x \leq 1$ 检验可求得 $A \cap B = \{ 0 , 1 \}$ ：

【详解】当 $x = 0$ 时， $0 ^ { 3 } - 3 \times 0 = 0 \leq 1$ ，所以 $0 \in B$ ，  
当 $x = 1$ 时， $1 ^ { 3 } - 3 \times 1 = - 2 \leq 1$ ，所以 $1 \in B$ ，  
当 $x = 2$ 时， $2 ^ { 3 } - 3 \times 2 = 2 > 1$ ，所以 $2 \notin B$ ，  
所以 $A \cap B = \{ 0 , 1 \}$ ：  
故答案为： $^ { \{ 0 , 1 \} }$ ：

23．（2024·湖南衡阳·三模）已知集合 $A = \left\{ a , a + 1 \right\}$ ，集合 $B = \left\{ x \in \mathbf { N } | x ^ { 2 } - x - 2 \leq 0 \right\}$ ，若 $A \subseteq B$ ，则 $a = .$

【答案】0或1

【分析】先求出集合 $B$ ，再由 $A \subseteq B$ 可求出 $a$ 的值.

【详解】由 $x ^ { 2 } - x - 2 \leq 0$ ，得 $( x + 1 ) ( x - 2 ) \leq 0$ ，解得 $- 1 \leq x \leq 2$ ，  
因为 $x \in \mathrm { N }$ ，所以 $x = 0 , 1 , 2$ ，  
所以 $B = \left\{ 0 , 1 , 2 \right\}$ ，  
因为 $A = \left\{ a , a + 1 \right\}$ ，且 $A \subseteq B$ ，  
所以 $a = 0$ 或 $a = 1$ ，  
故答案为：0或1

24.（2024湖南部阳·三樓） $A = \{ x \in \mathbf { N } | \log _ { 2 } ( x - 3 ) \leq 2 \} , \quad B = \{ x { | \frac { x - 3 } { x - 7 } \leq 0 \} }$ $A \cap B = \_$

【答案】 $\{ 4 , 5 , 6 \}$

【分析】根据对数不等式求集合A，根据分式不等式求集合B，进而可得 $A \cap B$ ：

【详解】若 $\log _ { 2 } ( x - 3 ) \leq 2$ ，则 $0 < x - 3 \leq 4$ ，解得 $3 < x \le 7$ ，  
所以 $A = \left\{ x \in \mathbf { N } | 3 < x \leq 7 \right\} = \left\{ 4 , 5 , 6 , 7 \right\}$   
若 ${ \frac { x - 3 } { x - 7 } } \leq 0$ ， 则 $\left\{ { \begin{array} { l } { \left( x - 3 \right) \left( x - 7 \right) \leq 0 } \\ { x - 7 \neq 0 } \end{array} } \right.$ ， 解得 $3 \leq x < 7$ ，  
所以 $B = \left\{ x | 3 \leq x < 7 \right\}$   
所以 $A \cap B = \left\{ 4 , 5 , 6 \right\}$ ：  
故答案为： $\{ 4 , 5 , 6 \}$ ：

25．（2024·安徽·三模）已知集合 $A = \{ \lambda , 2 , - 1 \} , B = \{ y | y = x ^ { 2 } , x \in A \}$ ，若 $A \cup B$ 的所有元素之和为12，则实数 $\lambda =$

【答案】 $^ { - 3 }$

【分析】分类讨论 $\lambda$ 是否为 $1 , - 2$ ，进而可得集合B，结合题意分析求解.

【详解】由题意可知： $\lambda \neq - 1$ 且 $\lambda \neq 2$ ，  
当 $x = \lambda$ ，则 $y = \lambda ^ { 2 }$ ；当 $x = 2$ ，则 $y = 4$ ；当 $x { = } - 1$ ，则 $y = 1$ ；  
若 $\lambda = 1$ ，则 $B = \{ 1 , 4 \}$ ，此时 $A \cup B$ 的所有元素之和为6，不符合题意，舍去；若 $\lambda = - 2$ ，则 $B = \{ 1 , 4 \}$ ，此时 $A \cup B$ 的所有元素之和为4，不符合题意，舍去；若 $\lambda \neq 1$ 且 $\lambda \neq - 2$ ，则 $B = \left\{ 1 , 4 , \lambda ^ { 2 } \right\}$ ，故 $\lambda ^ { 2 } + \lambda + 6 = 1 2$ ，解得 $\lambda = - 3$ 或 $\lambda = 2$ （舍去）；综上所述： $\lambda = - 3$

故答案为： $^ { - 3 }$ ：

26. （2024·山东聊城·三模）已知集合 $A = \left\{ 1 , 5 , a ^ { 2 } \right\} , B = \left\{ 1 , 3 + 2 a \right\}$ ，且 $A \cup B = A$ ，则实数 $a$ 的值为

【答案】3

【分析】由集合的包含关系，有 $3 + 2 a = 5$ 或 $3 + 2 a = a ^ { 2 }$ ，解出 $a$ 的值代入检验可得答案.

【详解】 $A \cup B = A$ ，则 $B \subseteq A$ ，有 $3 + 2 a = 5$ 或 $3 + 2 a = a ^ { 2 }$ ，解得 $a = 1$ 或 $a = - 1$ 或 $a = 3$ ，其中 $a = \pm 1$ 时，与集合中元素的互异性矛盾，舍去，  
所以实数 $a$ 的值为3.  
故答案为：3

27．（2024·重庆·三模）已知集合 $A = \{ x { | x ^ { 2 } - 5 x + 6 = 0  } \} , B = \{ x { | - 1 < x < 5 , x \in \mathbf { N } \} }$ ，则满足 $A \subseteq C \boxed { \square B }$ 的集合 $C$ 的个数为

【答案】7

【分析】化简集合 $A , B$ ，结合求集合的子集的结论求结果.

【详解】集合 $A = \left\{ x \vert ~ x ^ { 2 } - 5 x + 6 = 0 \right\} = \left\{ 2 , 3 \right\}$ ， $B = \{ x { | - 1 < x < 5 , x \in \mathbf { N } \} } = \{ 0 , 1 , 2 , 3 , 4 \}$ :满足 $A \subseteq C \boxed { \square \mathbf { B } }$ 的集合 $C$ 中必有元素2，3,   
所以求满足 $A \subseteq C \boxed { \square \mathbf { B } }$ 的集合 $C$ 的个数即求 $\{ 0 , 1 , 4 \}$ 集合的真子集个数，   
所以满足 $A \subseteq C \boxed { \square \mathbf { B } }$ 的集合 $C$ 的个数为 $2 ^ { 3 } - 1 = 7$ 个.   
故答案为：7.

28．（2024·天津·三模）己知全集 $U = \{ x \in \mathbf { N } ^ { * } | x \leq 7 \}$ ，集合 $A = \left\{ 1 , 2 , 3 , 6 \right\}$ ，集合 $B = \{ x \in \mathbf { Z } | | x | < 5 \}$ ，则 $( \complement _ { \Uparrow } A ) \bigcap B = \qquad , A \cup B = \qquad .$

【答案】{4} $\{ - 4 , - 3 , - 2 , - 1 , 0 , 1 , 2 , 3 , 4 , 6 \}$

【分析】根据题意，分别求得 $U = \{ 1 , 2 , 3 , 4 , 5 , 6 , 7 \}$ 和 $B = \{ - 4 , - 3 , - 2 , - 1 , 0 , 1 , 2 , 3 , 4 \}$ ，结合集合运算法则，即可求解.

【详解】由全集 $U = \left\{ x \in \mathbf { N } ^ { * } \mid x \leq 7 \right\} = \left\{ 1 , 2 , 3 , 4 , 5 , 6 , 7 \right\}$   
集合 $A = \left\{ 1 , 2 , 3 , 6 \right\}$ ，集合 $B = \{ x \in \mathbf { Z } | | x | < 5 \} = \{ - 4 , - 3 , - 2 , - 1 , 0 , 1 , 2 , 3 , 4 \}$ ，  
可得 $\complement _ { \scriptscriptstyle U } A = \{ 4 , 5 , 7 \}$ ，则 $( \complement _ { U } A ) \cap B = \left\{ 4 \right\}$ ， $\begin{array} { r } { A \bigcup B = \{ - 4 , - 3 , - 2 , - 1 , 0 , 1 , 2 , 3 , 4 , 6 \} . } \end{array}$   
故答案为： $\{ 4 \}$ ; $\{ - 4 , - 3 , - 2 , - 1 , 0 , 1 , 2 , 3 , 4 , 6 \}$ ：

29．（2024·山东泰安·三模）已知集合 $A = \left\{ x { \Biggl | } { \frac { x + 2 } { x - 2 } } \leq 0 \right\}$ ， $B = \left\{ x { \big | } \log _ { 2 } x \geq a \right\}$ 若 $B \subseteq \left( \complement _ { \mathbb { R } } A \right)$ 则 $a$ 的取值范围 是

【答案】 $[ 1 , + \infty )$

【分析】求出集合 $A , B$ ，根据包含关系确定范围即可.

【详解】由 ${ \frac { x + 2 } { x - 2 } } \leq 0$ ， 得 $- 2 \leq x < 2$ ，  
所以 $A = \left\{ x \vert - 2 \leq x < 2 \right\}$ ，则 $\complement _ { \mathbb { R } } A = \left\{ x \vert x < - 2 \right.$ 或 $x \geq 2 \}$   
由 $\log _ { 2 } x \geq a$ ，得 $x \geqslant 2 ^ { a }$ ，  
又 $B \subseteq \left( \complement _ { \mathbb { R } } A \right)$ ，所以 $2 ^ { a } \geq 2$ ，  
解得 $a \geq 1$ ：  
故答案为： $[ 1 , + \infty )$ ·

30．（2024·宁夏银川·三模）已知命题 $p$ ：关于 $x$ 的方程 $x ^ { 2 } - a x + 4 = 0$ 有实根；命题 $q$ ：关于 $x$ 的函数 $y = \log _ { 3 } \left( 2 x ^ { 2 } + a x + 3 \right)$ 在 $\left[ 3 , + \infty \right)$ 上单调递增，若 $\mathbf { \hat { \boldsymbol { p } } }$ 或 $q ^ { \ast }$ ”是真命题， $^ { \ast } p$ 且 $q ^ { \mathrm { i } }$ ”是假命题，则实数 $a$ 的取值范围 是

【答案】 $\left( - \infty , - 7 \right] \cup \left( - 4 , 4 \right)$

【分析】分分别求出两个命题为真命题时 $a$ 的范围，再分 $p$ 真 $q$ 假和 $p$ 假 $q$ 真两种情况讨论即可得解.

【详解】由关于 $\mathbf { X }$ 的方程 $x ^ { 2 } - a x + 4 = 0$ 有实根,  
得 $\Delta = a ^ { 2 } - 1 6 \geq 0$ ，解得 $a \ge 4$ 或 $a \leq - 4$ ，  
由关于 $\mathbf { X }$ 的函数 $y = \log _ { 3 } \left( 2 x ^ { 2 } + a x + 3 \right)$ 在 $\left[ 3 , + \infty \right)$ 上单调递增，  
得 $\left\{ - { \frac { a } { 4 } } \leq 3 \right.$ ，解得 $a > - 7$ ，[2×3² +3a+3>0  
因为"p或 $\bar { }$ "是真命题，“p且q"是假命题，  
所以 $p , q$ 一真一真一假,  
当 $p$ 真 $q$ 假时， $\left\{ \begin{array} { l l } { a \geq 4 \exists { \cdot } \emptyset a \leq - 4 } \\ { a \leq - 7 } \end{array} \right.$ 解得 $a \leq - 7$ ，  
当 $p$ 假 $q$ 真时， $\displaystyle { \left\{ - 4 < a < 4 \atop a > - 7 \right.}  $ ， 解得 $- 4 < a < 4$ ，  
综上所述， $a \in \left( - \infty , - 7 \right] \cup ( - 4 , 4 )$ ：  
故答案为： $\left( - \infty , - 7 \right] \cup \left( - 4 , 4 \right)$ ：