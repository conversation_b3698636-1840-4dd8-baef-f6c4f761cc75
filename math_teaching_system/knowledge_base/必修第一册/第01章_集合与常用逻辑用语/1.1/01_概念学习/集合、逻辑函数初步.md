---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 40
source_file: L5-1-集合、逻辑、函数初步.md
title: L5-1-集合、逻辑、函数初步
type: problem_type
---

# 集合、逻辑函数初步

一本一讲，共26讲（建议5个月学习）

14个考点+14个题型+32个视频

每周一讲（建议学习时间90分钟）

![](images/129498049941491da2c6700c8ab8dc8425329baa2b3df2d867968a5064cf86b1.jpg)

# 视频内容研发团队

学而思优秀老师

学而思优秀老师和一线高级教师联合创作本书试题，并精心录制讲解视频学而思图书APP扫码即可观看

![](images/154639cc26d15107c20e5ee2564b71d247f6e00d28d081a0f79c0840a90d736b.jpg)

# 傅博宇 老师

毕业于北京大学元培学院  
网校和北大数院高考数学研究联合课题组成员；  
网校高中创新产品部负责人；  
荣获学而思网校“桃李满天下奖”“出类拔萃奖”等；腾讯网中国好老师；  
青少年教育导师认证；  
科学家长观体系的创立者

![](images/5140aa0832e8857c904ffaed85a2eb0bb66815056b00e53f85d5bd8d5f48ef5d.jpg)

# 王侃老师

毕业于北京大学数学系  
学而思网校高中数学教研奠基人；  
学而思网校高中数学S级教师；  
荣获学而思网校“突出贡献奖”“桃李天下奖”等；擅长总结题型特点，提炼思想方法；  
擅长分层教学，因材施教

![](images/fa25cf43789495fd4f2d3c51549837f30014d920a432df5fbf4e8cae3d55bf48.jpg)

# 付恒岩 老师

毕业于大连理工大学  
网校高中部理科主讲岗后培训师；  
2020年荣获学而思网校最具魅力奖；  
2019年、2020年荣获学而思网校诲人不倦奖；2020年荣获学而思网校高考优秀评卷人；  
2021年担任新浪教育高考数学直播解析特邀嘉宾；“停课不停学”公益课高中数学主讲老师

![](images/859127c77dc6712b11eab8974177d0e1117e108296953dc7642eeb9e059cf0d7.jpg)

# 武洪姣 老师

14年线上线下教学经验；  
学而思网校高中理科教研负责人；  
学而思高中数学特级教师；  
在教学的过程中擅长归纳题型，方法和技巧；  
在高中数学模块中最擅长讲解圆锥曲线和导数；  
无论你是从小数学不好，还是数学一直拔尖，都可以在武老师的课堂上收获很多

5级

# 集合、逻辑函数初步

一本一讲，共26讲（建议5个月学习）

14个考点 $+ 1 4$ 个题型 $+ 3 2$ 个视频

每周一讲（建议学习时间90分钟）

![](images/c428c21a2058591b2295185aaae95dde31654a8ee9fb4f26bc398e3dfd705a48.jpg)

# 图书在版编目（CIP）数据

学而思秘籍．高中数学思维突破5级/学而思教研中心编写组编著．--北京：现代教育出版社，2022.3ISBN978-7-5106-8661-0

I. $\textcircled{1}$ 学…Ⅱ. $\textcircled{1}$ 学…Ⅲ. $\textcircled{1}$ 中学数学课－高中一教学参考资料IV. $\textcircled{1}$ G634

中国版本图书馆CIP数据核字（2022）第036150号

# 学而思秘籍高中数学思维突破5级

编著：学而思教研中心编写组  
出品人：陈琦  
选题策划：王春霞  
责任编辑：魏星李雨卉  
装帧设计：学而思教研中心设计组·于磊王琳  
出版发行：现代教育出版社  
地 址：北京市东城区鼓楼外大街26号荣宝大厦三层  
邮 编：100120  
电 话：010-64251036（编辑部）010-64256130（发行部）  
印开印字版印书定 刷：北京世纪恒宇印刷有限公司本： $8 8 9 \mathrm { m m } \times 1 1 9 4 \mathrm { m m }$ 1/16张：38数：765千字次：2022年3月第1版次：2022年3月第1次印刷号：ISBN978-7-5106-8661-0价：239.00元（含视频课程）

# Planning Committee

# 学而思图书策划委员会

主编：学而思教研中心编写组  
副主编：汪玲玲李奎廷  
执行主编：张卡特乔巍郭忠秀  
编者：武洪姣　付恒岩　傅博宇　成文波牛术强王侃 徐强郑 贏景肖龙谭茗心刘坤 董宇喆 沈思含 宗倩  
知识点睛 幸面思秘路中教学退维安城5 模块1集合  
梳理归纳知识，筑牢学科根基 APP扫码观看本模块讲解视频知识与方法@例题与练习全程跟老师高效学知识  
提取关键信息，标注重要结论 知识点晴 学思秘县高中教学联增突站5号（1集合的概念 模块1正弦型函数的图象与性质一般地，把一些能够确定的不同的对象看成一个整体，就说这个的全成的合（集）集合的个 APP扫码观看本模块讲解视频（2）集合元索的性质 知识与方法例题与练习全程最老师高效学知识①确定性：一个元素在不在集合中是客观确定的（3集合中的元案不有虑取序 素与集合的关系有两种：属于（记作aA）和不属于（记作adA） 知识点睛（4）常用数集 的常用数集集合然数集正整数集整数集有理数集实数集复数集 轴为向的平移安：移个单位得号 N NN Z Q RC πin( x +}).集仓的费5方法 2轴方向的缩的模标原集合的常用表下方证有列基法，述法，图示过en图数② 的4伯得，-Any轴方向的伸缩变换与伸缩系数A保持一致，但抽方向的伸缩变换与伸缩系数u恰好相反（2）示例（1-2）:y\*）象变换的两种建径：  
精讲精练·  
方法点拨 ②  
剖析典型例题，提升解题能力 招计第1讲集合、谨设，语数极步精讲精练拍照批改秒判对错  
总结方法技巧，升华解题思维 考点12：函数定义域的求解与应用第15读会数的选用-零点个数与颈车点用联方法点拨函数（s）零点个数等于方程fis）=0实数根的个数，研究函数零点个数，一般需要先求出函数的所有单调区间，阿断函数在定义域区同喝点，极值点，无穷远处函数值的正负，据承数的零点存在定理判断各个单调区间是否有零点，最终得到所有零点个数相海的机值无无间场车调正间上，零点存在当一最有三开类型（1）相邻极值与此点（无穷远点或函数值不存存的区间暖点，下同）处函数值极限同号，如f）在极值点=a的极值a）<0,目1当x>a时，(x）<0恒成立（这一点需要自已对函影变（2）已知函数f（s)的定义域为（-1,0），倒函数f（2s-1）的 形，探索和证明），则（）在（a，·）无零点；） （2）函数板值的正负和函数在这个单调区间上的单调性决定了函数在此单调区间上无零A(-1,1） C.（-1,0) D. 点，如/x)在极值点x=a的极值jla）>0,目f（x)在（a,）上单调递增，购（x)在(a,）无零点（3）函数的根值和在此点函数值的极限登号，则函数在此单调区间上有一个零点，但不能用极限案说期零点的有在，需要在区间上投到一点，使得这一点的函数值与板值点的函数值  
APP扫码观看本模块 （1）利用导数判断函数的单调性，求出所有单调区间：（2）求出函数的各个核值；  
讲解视频 3段零  
全程跟老师，高效学知识！  
由学而思资深老师对本模块知  
识、例题和练习进行系统讲解，  
并归纳对应考点的解题技巧，

![](images/88fd4a93dc7d21635fb4979776ee2a5ca8e2a32d48656290e1542888213b143f.jpg)

总结解题方法，让学生能够举一反三，通过一道题，学会一类题，从而提升学生的解题能力，实现数学思维突破。

知识与方法 例题与练习

66

# 序言

# 亲爱的同学：

很开心能见到你，本书将带领你进入全新的领域。在过去的学习中，无论你是卓越还是略有失意，此刻将是新的征途的开始。你是否已准备好谱写新的辉煌？

在这里，你会知道许多著名数学家的趣事，你也会揭开数学神秘的面纱，了解数学之美。

数学家高斯说：“数学是一切科学的皇后。”数学是通往星辰大海的密钥，是国防科技的护盾，是我们脚下这片土地的未来。数学家华罗庚说过：“宇宙之大，粒子之微，火箭之速，化工之巧，地球之变，生物之谜，日用之繁，无处不用数学。

# 对于本书的学习，给你一些建议：

第一，提前预习。“凡事预则立，不预则废。”提前预习，学习才会更有效。  
第二，APP扫码观看视频。积极思考，认真做笔记，总结解题方法、技巧。  
第三，及时复习。“温故而知新，可以为师矣。”不忘温习，方得始终。  
第四，及时完成测试。今朝有题今朝做，明朝又有明朝事。  
第五，整理错题，学会总结。聪明人知错就改，糊涂人有错就瞒。  
最后，青春的帆已扬起，只待你乘风破浪，勇往直前。

# 提升篇

第1讲集合、逻辑、函数初步  
第2讲基本初等函数与函数的性质  
第3讲不等式  
第4讲导数（一）  
第5讲导数（二）  
第6讲三角函数  
第7讲平面向量与解三角形  
第8讲数列（一）  
第9讲立体几何  
第10讲解析几何

<html><body><table><tr><td>模块1</td><td>集合 2</td></tr><tr><td>模块2</td><td>常用逻辑用语 9</td></tr><tr><td>模块3</td><td>函数初步 19</td></tr></table></body></html>

# 冲刺篇

第11讲函数性质的应用  
第12讲函数零点与恒成立问题  
第13讲导数的应用一单调性与极值最值  
第14讲导数的应用一恒成立问题  
第15讲导数的应用一—零点个数与隐零点问题  
第16讲导数的应用一极值点偏移  
第17讲三角函数与平面向量  
第18讲解三角形  
第19讲数列（二）  
第20讲三视图与球的切接问题  
第21讲空间中的角  
第22讲统计概率  
第23讲圆锥曲线的几何性质  
第24讲几何条件的代数转化  
第25讲圆锥曲线中的最值范围问题第26讲定点定值问题与轨迹方程问题  
参考答案

# 集合、逻辑、函数初步

# 直击课堂

<html><body><table><tr><td>知识模块</td><td>考点</td><td></td><td>对应例题</td><td>考查概率</td></tr><tr><td>集合</td><td colspan="2">集合元素的互异性 集合间的美系述逆向求参 集合的交并补运算 四种命题及其真假判断</td><td>例1 例2 例4</td><td>★★☆☆☆ ★★★★★ ★★★★★</td></tr><tr><td>常用逻辑用语</td><td colspan="2">命题的充要性判断 逻辑联结词及真假判断 全称量词命题、存在量词命题的否定书写 常用逻辑综合</td><td>例5 例6 例7 例8 例9</td><td>★★☆☆☆ ★★★☆☆ ★★★★☆ ★★★☆☆ ★★★★☆</td></tr><tr><td>函数初步</td><td>映射与函数的概念 已知解析式求值 函数定义域的求解与应用 函数值域 函数解析式的求解</td><td>例10 例11 例12 例13 例14</td><td>★☆☆☆☆ ★★★★☆ ★★★★☆ ★★★★☆ ★★★☆☆</td></tr></table></body></html>

# 本讲解读

本讲共分为集合、常用逻辑用语、函数初步三个模块.这三个模块均属于高考的必考和热点内容.模块一集合的考查重点是集合与集合之间的关系与运算，直接考查形式基本固定为一道选择题,难度为基础题;模块二常用逻辑用语的考查热点是充分条件与必要条件的判断，直接考查形式基本固定为一道选择题,难度同样为基础题;模块三函数初步在高考中考查重点是分段函数的自变量和函数值的求解，或者研究含参数的分段函数问题.本讲将对以上三个模块中的多个高考热点逐一讲解.

# 模块1集合

# APP扫码观看本模块讲解视频

# 知识点睛

# $\textcircled { 1 }$ 集合的有关概念

(1)集合的概念

一般地，把一些能够确定的不同的对象看成一个整体，就说这个整体是由这些对象的全体构成的集合(或集).构成集合的每个对象叫做这个集合的元素.

(2)集合元素的性质  
$\textcircled{1}$ 确定性：一个元素在不在集合中是客观确定的.  
$\textcircled{2}$ 互异性：集合中的元素是彼此不同的。  
$\textcircled{3}$ 无序性：集合中的元素不考虑顺序.  
(3）元素与集合的关系  
元素与集合的关系有两种：属于（记作 $a \in A$ )和不属于（记作 $a \not \in A$ ）

（4）常用数集

<html><body><table><tr><td colspan="6">常用数集</td></tr><tr><td>集合</td><td>自然数集</td><td>正整数集</td><td>整数集</td><td>有理数集实数集复数集</td><td></td><td></td></tr><tr><td>符号</td><td>N</td><td>N*或N+</td><td>Z</td><td>Q</td><td>R</td><td>C</td></tr></table></body></html>

# ②集合的表示方法

集合的常用表示方法有列举法、描述法、图示法(Venn图、数轴)和区间法.

# 精讲精练

# 拍照批改秒判对错

# 考点1：集合元素的互异性

# 例1

(1)若 $2 \in \left\{ 1 , a , a ^ { 2 } - a \right\}$ ，则 $a = \left( \begin{array} { l l l } \end{array} \right)$

A.-1 B.0 C.2 D.2或-1

# 学习笔记

(2)设集合 $A = \{ x , x ^ { 2 } , x y \}$ ， $B = \left\{ 1 , x , y \right\}$ ，且 $A = B$ ，则 $x + y =$

# 学习笔记

# 考点2：集合描述法

# 例2

(1)已知集合 $M = \{ x \ | y = \sqrt { x - 1 } \ \}$ ， $N = \{ y \vert y = { \sqrt { x - 1 } } \}$ ，则 $M$ 与 $N$ 的关系为（）

$$
= N \qquad \mathrm { B . ~ } M \subseteq N \qquad \mathrm { C . ~ } M \supseteq N \qquad \mathrm { D . ~ } M \cap N = \emptyset
$$

# 学习笔记

（2）若集合 $P = \{ 0 , 1 , 2 \} , Q = \{ ( x , y ) | { \{ { x - y + 1 > 0 , \atop x - y - 2 < 0 , }  } x , y \in P \} $ ，则集合 $O$ 中元素的个数是（）

A.3 B.5 C.7 D.9

# 学习笔记

# 知识点晴

# 集合间的关系

(1)子集与真子集

如果集合 $A$ 中的每一个元素都是集合 $B$ 中的元素，则称集合 $A$ 是集合 $B$ 的子集，记作： $A \subseteq$ $B$ 或 $B \supseteq A$ ；如果集合 $A$ 是集合 $B$ 的子集,并且 $B$ 中至少有一个元素不属于 $A$ ，那么集合 $A$ 叫做集合 $B$ 的真子集，记作： $A \subsetneq B$ 或 $B \supsetneq A$

(2)集合相等

如果集合A的每一个元素都是集合 $B$ 的元素( $A \subseteq B$ ，且集合 $B$ 的每一个元素也是集合 $A$ 的元素( $B \subseteq A { \mathrm { ~ } }$ ，那么我们称集合 $A$ 等于集合 $B .$ 记作： $A = B$

# 4有限集合的子集个数

若集合A中有 $n$ 个元素，则集合A的子集有2”个，真子集有2”-1个.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点3：集合间的关系判断与逆向求参

# 例3

(1)若集合 $A = \left\{ x \left| \sqrt { x } = \sqrt { x ^ { 2 } - 2 } , x \in \mathbf { R } \right. \right\} , B = \left\{ 1 , m \right\}$ ，若 $A \subseteq B$ ，则 $m$ 的值为）

A.2 B.-1 C. $- 1$ 或2 D.2或

# 学习笔记

(2）设集合 $A = \{ x \mid a \leqslant x \leqslant 2 a + 1 \}$ ， $B = \left\{ x \ | 2 a - 1 \leqslant x \leqslant 5 a + 1 \right\}$ ，若 $A \cup B$ $= B$ ，则实数 $a$ 的取值范围是

# 学习笔记

# 知识点睛

# $\textcircled { 6 }$ 集合的交并补运算

交集 $A \cap B = \{ x \mid x \in A$ 且 $x \in B \}$

![](images/62fc5d648d7d3a48bcef64066867743db8cc8443c2a0c4309f3140d0b8b7f6b2.jpg)

并集： $A \cup B = \{ x \mid x \in A$ 或 $x \in B \}$

A B

补集： $) \ _ { U } A = \{ x \mid x \in U$ 且 $x \not \in A  \}$

![](images/2a0d61e9b5959e4099a641281baf7bad894fb184fae444aab2a77d7ba0b6be22.jpg)

# $\textcircled{6}$ 集合的运算性质

（1)（AnB)CA,（AnB)CB;AC(AUB),BC(AUB);  
（2)AnB=AACB;AUB=A⇔BCA；  
（3)An(CA)=O,AU(CA)=U,C(CA）=A.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点4：集合的交并补运算

# 例4

（1）已知集合 $A = \left\{ x \ | 0 \leqslant x \leqslant 5 \ \right\} , B = \left\{ \ x \in \mathbf { N } ^ { * } | x - 1 \leqslant 2 \ \right\}$ ，则 $A \cap B$ $\mathbf { \Sigma } = \left( \begin{array} { l l l } \end{array} \right)$

A. $\{ x \mid 1 \leqslant x \leqslant 3 \}$ B. $\{ x \ | 0 \leqslant x \leqslant 3 \}$ C. $\{ 0 , 1 , 2 , 3 \}$ D.{1,2,3

# 学习笔记

（2）设常数 $a \in \mathbb { R }$ ，集合 $A = \left\{ x \mid ( x - 1 ) ( x - a ) \geq 0 \right\}$ $B = \{ x \mid x \geqslant a - 1 \}$ ，若$A \cup B = \mathbf { R }$ ，则 $a$ 的取值范围为（ ）

A. $( \mathbf { \partial } - \infty , 2 )$ $\begin{array} { l } { { \mathrm { B . ~ ( ~ - } \infty ~ , 2 ] } } \\ { { \mathrm { ~ } } } \\ { { \mathrm { D . ~ } [ 2 , + \infty ~ ) } } \end{array}$   
C. $( 2 , + \infty )$

# 学习笔记

# 模块2常用逻辑用语

# APP扫码观看本模块讲解视频

# 知识点睛

# $\textcircled{1}$ 四种命题

（1）命题

能够判断真假的语句叫做命题.其中判断为真的命题叫做真命 题，判断为假的命题叫做假命题.

(2）四种命题

命题“若 $p$ ，则 $q$ ”是由条件 $p$ 和结论 $q$ 组成的，对 $p , q$ 进行“换位”和"换质(否定)”后，可以构成四种不同形式的命题.

$\textcircled{1}$ 原命题：若 $p$ ，则 $q$ $\textcircled{2}$ 原命题的逆命题：若 $q$ ，则 $p$ $\textcircled{3}$ 原命题的否命题：若非 $p$ ，则非 $q$ $\textcircled{4}$ 原命题的逆否命题;若非 $q$ ，则非 $p$

若p，则q 互逆 若q，则p A 1 五否 渔逆 为 ↓   
若非p，则非q 互逆 若非q，则非p

（3）四种命题之间的关系$\textcircled{1}$ 逆命题与否命题互为逆否命题；$\textcircled{2}$ 互为逆否命题的两个命题等价(同真或同假).因此证明原命题，也可以改证它的逆否命题；

$\textcircled{3}$ 互逆或互否的两个命题不等价.

# ②充分条件与必要条件

<html><body><table><tr><td>pq但qp</td><td>p是q的充分不必要条件； q是p的必要不充分条件</td></tr><tr><td>q→p但pq</td><td>p是q的必要不充分条件； q是p的充分不必要条件</td></tr><tr><td>pq且q=p</td><td>p与q互为充要条件</td></tr><tr><td>pq且qp</td><td>p是q的既不充分也不必要条件； q是p的既不充分也不必要条件</td></tr></table></body></html>

![](images/316939b58da11faad2d20145d4b193ef291b121d8e68a913e64ec79f639ad977.jpg)

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点5：四种命题及其真假判断

# 例5

(1)命题“若 $x , y$ 都是偶数,则 $x + y$ 也是偶数”的逆否命题是（ ）

A.若 $x + y$ 是偶数，则 $x$ 与 $y$ 不都是偶数B.若 $x + y$ 是偶数，则 $x$ 与 $y$ 都不是偶数C.若 $x + y$ 不是偶数，则 $x$ 与 $y$ 不都是偶数D.若 $x + y$ 不是偶数，则 $x$ 与 $y$ 都不是偶数

![](images/c932d2bb64076f0a33d0b05d0c9b91e751be9f1d957976804e1c548b401ef9a5.jpg)

# 学习笔记

（2）原命题：“设 $a , b , c \in \mathbb { R }$ ，若 $a > b$ ,则 $a c ^ { 2 } > b c ^ { 2 }$ ”,以及它的逆命题、否命题、逆否命题中,真命题共有（ ）

A.0个 B.1个 C.2个 D.4个

# 学习笔记

# 考点6：命题的充要性判断

# 例6

（1）设 $x \in \mathbb { R }$ ，则“ $| x - 2 ~ | < 1$ ”是“ $x ^ { 2 } + x - 2 > 0$ ”的（

A.充分不必要条件 B.必要不充分条件C.充要条件 D.既不充分也不必要条件

# 学习笔记

（2）设 $p$ ：实数 $x , y$ 满足 $x > 1$ 且 $y > 1 \ , q$ ：实数 $x , y$ 满足 $x + y > 2$ ，则 $p$ 是 $q$ 的（）

A.充分不必要条件 B.必要不充分条件C.充要条件 D.既不充分也不必要条件

# 学习笔记

# 知识点睛

# $\textcircled { 6 }$ 逻辑联结词

（1）且：记作 $p \wedge q$ ，读作“ $p$ 且 $q ^ { \dprime }$   
用“且”可定义集合的交集 $A \cap B = \{ x \mid x \in A$ 且 $x \in B \}$ (2）或：记作 $p \vee q$ ，读作“ $p$ 或 $q$ ；  
用“或”可定义集合的并集： $A \cup B = \{ x \mid x \in A$ 或 $x \in B \}$ (3)非：记作一 $p$ ，读作“非 $p$ ”或“ $p$ 的否定”  
用“非”可定义集合的补集： $, \quad u ^ { \prime } , u = \{ x \in U \mid ^ { \neg } x \in A \}$

$\textcircled{4}$ 含逻辑联结词命题的真值表

<html><body><table><tr><td>P</td><td>q</td><td>pq</td><td>pVq</td><td>p</td></tr><tr><td>真</td><td>真</td><td>真</td><td>真</td><td>假</td></tr><tr><td>真</td><td>假</td><td>假</td><td>真</td><td>假</td></tr><tr><td>假</td><td>真</td><td>假</td><td>真</td><td>真</td></tr><tr><td>假</td><td>假</td><td>假</td><td>假</td><td>真</td></tr></table></body></html>

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点7：逻辑联结词及真假判断

# 例7

若 $\neg$ $( p \land q )$ 为假命题,则(

A. $p$ 为真命题， $q$ 为假命题 B. $p$ 为假命题， $q$ 为假命题$\mathrm { C } . p$ 为真命题， $q$ 为真命题 D. $p$ 为假命题， $q$ 为真命题

# 学习笔记

# 知识点睛

# $\textcircled{3}$ 量词

(1)量词的意义与符号表示

<html><body><table><tr><td>量词名称</td><td>常见量词</td><td>表示符号</td><td>命题简记</td></tr><tr><td>全称量词</td><td>所有、一切、每一个、全部、任意等</td><td>A</td><td>Ax∈M,p(x)</td></tr><tr><td>存在量词</td><td>存在、至少有一个、某个、有些等</td><td></td><td>x∈M,p(x）</td></tr></table></body></html>

(2)全称量词命题与存在量词命题的真假判断

$\textcircled{1}$ 要说明一个全称量词命题是假命题，只需要找到一个反例即可；而要说明一个全称量词命题是真命题，则需进行证明.

$\textcircled{2}$ 要判断一个存在量词命题是真的，只要找到一个例子就可以;但要说明一个存在量词命题是假的，需要证明 $\forall x \in M , p ( x )$ 不成立.

# $\textcircled{6}$ 含一个量词的命题的否定

(1)存在量词命题的否定  
存在量词命题 $p \colon \exists x _ { 0 } \in A , p ( x _ { 0 } )$ ；它的否定是 $\neg p : \forall x \in A , \neg p ( x )$ (2)全称量词命题的否定  
全称量词命题 $\boldsymbol { q } : \forall \boldsymbol { x } \in \boldsymbol { A } , p ( \boldsymbol { x } )$ ;它的否定是 $\neg \ q : \exists x _ { 0 } \in A , \neg \ p ( x _ { 0 } )$

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点8：全称量词命题、存在量词命题的否定书写

# 例8

(1)已知命题 $p : \forall x \in I , x ^ { 3 } - x ^ { 2 } + 1 \leqslant 0$ ，则 $\ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l \ l a t \ l a t \ l a t \ l a n t \ l a n t \ l a n t \ l a n t \ l a n t r m a n m a n m a n m a n m a n m a n m a n m a n m a n m a n m a n m a n m a n m a n m a n m a n m a n m a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n a n$ 是（

A. $\forall x \in I , x ^ { 3 } - x ^ { 2 } + 1 > 0$   
B. $\forall x \notin I , x ^ { 3 } - x ^ { 2 } + 1 > 0$   
C. $\exists x _ { \scriptscriptstyle \mathrm { 0 } } \in I , x _ { \scriptscriptstyle \mathrm { 0 } } ^ { \scriptscriptstyle 3 } - x _ { \scriptscriptstyle \mathrm { 0 } } ^ { \scriptscriptstyle 2 } + 1 > 0$   
D. $\exists x _ { \scriptscriptstyle 0 } \notin I , x _ { \scriptscriptstyle 0 } ^ { \scriptscriptstyle 3 } - x _ { \scriptscriptstyle 0 } ^ { \scriptscriptstyle 2 } + 1 > 0$

# 学习笔记

(2）命题“ $\exists x _ { \mathrm { o } } \in \left( 0 , + \infty \right) , \ln x _ { \mathrm { o } } = x _ { \mathrm { o } } - 1$ ”的否定是（

A. $\exists x _ { 0 } \in ( 0 , + \infty )$ ,lnx≠x-1B. $\mid x _ { \scriptscriptstyle ( ) } \notin \left( \ 0 \right) , + \infty \ \quad$ ， $\ln x _ { \scriptscriptstyle 0 } = x _ { \scriptscriptstyle 0 } - 1$ C. $\forall x \in ( 0 , + \infty )$ ， $\ln x \neq x - 1$ D. $\forall x \in ( 0 , + \infty )$ ， $\ln x = x - 1$

# 学习笔记

（3）命题“ $\forall \ : x \in \mathbb { R }$ ， $\exists n \in \mathbf { N } ^ { * }$ ，使得 $n \geq x ^ { 2 }$ ”的否定形式是（

A. $\forall ~ x \in \mathbf { R }$ ,3 $n \in \mathbf { N } ^ { * }$ ，使得 $n < x ^ { 2 }$ B. $\forall \ : x \in \mathbf { R }$ $\forall n \in \mathbf { N } ^ { * }$ ，使得 $n < x ^ { 2 }$ C.3 $x \in \mathbb { R }$ ,3 $\boldsymbol { n } \in \mathbf { N } \ { } ^ { * }$ ，使得 $n < x ^ { 2 }$ D.3 $x \in \mathbb { R }$ ， $\forall n \in \mathbf { N } ^ { * }$ ，使得 $n < x ^ { 2 }$

# 学习笔记

# 考点9：常用逻辑综合

# 例9

（1)已知下列命题：

$\textcircled{1}$ 命题“ $\forall x \in \mathbf { R } , x ^ { 2 } + 3 < 5 x ^ { \prime }$ 的否定是“ $x \in \mathbf { R } , x ^ { 2 } + 3 < 5 x ^ { , , }$

$\textcircled{2}$ 已知 $p , q$ 为两个命题,若“ $p \vee q$ ”为假命题，则“ $( \neg \ p ) \land ( \neg \ q )$ ”为真命题；

$\textcircled{3}$ $) ^ { * } a > 2 \ 0 1 5 ^ { \prime }$ ”是“ $a > 2 \ 0 1 7$ ”的充分不必要条件；

$\textcircled{4}$ “若 $x y = 0$ ，则 $x = 0$ 且 $y = 0$ ”的逆否命题为真命题.

其中，所有真命题的序号是

![](images/b54d89534f5f3fb19d33a498a7686334279ba0079570d015150d9e78c335992b.jpg)

# 学习笔记

(2)已知命题 $p$ ：“关于 $x$ 的方程 $x ^ { 2 } - 4 x + a = 0$ 有实根”，若 $\neg \ p$ 为真命题的充分不必要条件为 $a > 3 m + 1$ ，则实数 $m$ 的取值范围是（ ）

A. $[ 1 , + \infty )$ $\mathrm { B } , ( 1 , + \infty )$ C. $( \mathbf { \partial } - \infty , 1 )$ D.(-8,1]

# 学习笔记

# 模块3函数初步

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# $\textcircled{1}$ 映射与函数

（1）函数的概念

设A，B是非空的数集，如果按照某种确定的对应关系f，使对于集合A中的任意一个数x，在集合 $B$ 中都有唯一确定的数f（x）和它对应，那么就称f：AB为从数集A到数集 $B$ 的一个函数，记作y=f（x)，xEA.

其中， $x$ 叫做自变量， $x$ 的取值范围A叫做函数的定义域；与 $x$ 的值相对应的 $y$ 值叫做函数值，函数值形成的数集 $\{ f ( x ) \ | x \in A \}$ 叫做函数的值域.

自变量取值范围 义域↑ → 雨数的二要素y=f(x），x∈A→ 对应关系取值范围  
函数值 值域$\{ y \mid y = f ( x ) , x \in A \}$

(2)映射的概念

设 $A , B$ 是两个给定的非空集合，如果按照某种对应关系 $f ,$ 对A内任意一个元素 $x$ ,在 $B$ 中有唯一确定的元素 $y$ 与 $_ x$ 对应，则称 $f$ 为集合A到集合 $B$ 的映射.记作 $f { \cdot } A { \longrightarrow } B$ ，称 $y$ 是 $x$ 在映射 $f$ 下的象， $_ x$ 称作 $y$ 的原象.

# 函数的表示方法

(1)列表法

(2)图象法：用函数上所有点的坐标构成的图形表示集合，即 $F = \{ P ( x , y ) | y = f ( x )$ ，$x \in A \}$

(3)解析法：用具体的解析表达式表示两个变量之间的函数对应关系的方法.

# $\textcircled{8}$ 分段函数

在函数定义域内，对于自变量 $x$ 的不同取值范围，有着不同的对应关系，这样的函数叫做分段函数.

# $\textcircled { 4 }$ 复合函数

如果 $y$ 是 $u$ 的函数，记 $\scriptstyle { y = f ( u ) , u }$ 是 $x$ 的函数，记 $u = g ( x )$ ，且 $\boldsymbol { g } ( \boldsymbol { x } )$ 的值域与 $f ( u )$ 的定义域的交集非空,则通过 $u$ 确定了 $y$ 是 $x$ 的函数 $\boldsymbol { y } = f [ \varrho ( \boldsymbol { x } )$ ，这时 $y$ 叫做 $x$ 的复合函数，其中 $u$ 叫做中间变量， $\scriptstyle { y = f ( u ) }$ 叫做外层函数， $u = g ( x )$ 叫做内层函数.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点10：映射与函数的概念

# 例10

给出下列四个说法：

$\textcircled { 1 } f ( x ) = x ^ { 0 }$ 与 $\varrho ( { x } ) = 1$ 是同一个函数；  
$\textcircled { 2 } y = f ( x )$ ， $x \in \mathbb { R }$ 与 $\scriptstyle y = f ( x + 1 )$ ， $x \in \mathbb { R }$ 是同一个函数；  
$\textcircled { 3 } y = f ( x )$ ， $x \in \mathbb { R }$ 与 $\mathbf { \gamma } _ { y } = \mathbf { \gamma } _ { f ( \mathbf { \gamma } _ { t } ) }$ ， $t \in \mathbb { R }$ 是同一个函数；  
$\textcircled{4}$ 定义域和值域相同的函数是同一个函数.  
其中正确的个数是(

A.3 B.2 C.1 D.0

# 学习笔记

# 考点11：已知解析式求值

# 例11

（1)已知 $f ( x ) = \left\{ { \begin{array} { l } { 8 ^ { x + 1 } , x \leqslant 0 , } \\ { f ( x - 1 ) + 1 , x > 0 , } \end{array} } \right.$ 则 $f \left( { \frac { 4 } { 3 } } \right)$ 的值为（

A.2 B.3 C.4 D.16

# 学习笔记

(2）函数 $f ( x ) = \left\{ { \begin{array} { l l } { { \ln x , x > 0 , } } \\ { { } } \\ { { x + 1 , x < 0 , } } \end{array} } \right.$ 则 $f ( x ) > - 1$ 的解集为(

A. $( \mathbf { \nabla } - 2 , + \infty )$ B.(-2,0) $\mathrm { C . ~ ( ~ - 2 , 0 ) ~ U \left( \frac { 1 } { e } , + \infty \right) }$ $\mathrm { D } . \left( \frac { 1 } { \mathrm { e } } , + \infty \right)$

# 学习笔记

# 知识点睛

# $\textcircled{5}$ 定义域

函数定义域的限制条件

（1）分式分母不为零;  
（2）偶次根式被开方数大于等于零：  
（3）零的零次方没有意义：  
（4）对数函数真数大于零：  
（5）求正切的角不能等于 $\frac { \pi } { 2 } + k \pi$ $k \in \mathbf { Z }$

# $\textcircled{6}$ 值域

求函数值域的常用方法

(1)观察法：对于结构简单的复合函数，可以通过直接观察解析式，利用相对应的简单初等函数间接得到所求函数的值域，也称为逐层求值域法.

(2)换元法：通过将解析式中某一部分作整体换元，将函数转化为较简单的形式.比如 $\gamma = a x + b \pm { \sqrt { c x + d } }$ 型的函数，可设 ${ \sqrt { c x + d } } = t ( t$ $\geqslant 0$ ),转化为二次函数求值域.

(3)分离常数法：分式型的函数可通过分离常数，转化为简单的函 数来求值域.

(4)基本不等式法：此法求函数值域时，要注意条件“一正,二定，三相等”

（5)利用函数的单调性：判断函数单调性，确定函数的单调区间，进而求出值域.

(6)数形结合：若函数解析式的几何意义较明显，如距离、斜率等，可用数形结合的方法.

# $\textcircled{8}$ 对应关系

求函数解析式的一般方法

(1)待定系数法：已知函数类型，设出函数的标准形式，建立方程（组)确定系数即可.

(2）配凑法或换元法：

已知 $f [ h ( x ) ] = g ( x )$ ，求 $f ( x )$ 的问题，往往把右边的 $g ( x )$ 整理成或配凑成只含 $h \left( x \right)$ 的式子，然后用 $x$ 将 $h ( x )$ 代换；或设 $h ( x ) = t$ ，从中解出 $x$ ，代入 $g ( x )$ 进行换元，应用换元法时，要注意新元的取值范围.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点12：函数定义域的求解与应用

# 例12

（1）函数 $f ( x ) = { \frac { \sqrt { x + 4 } + { \sqrt { 1 - 2 x } } } { x ^ { 2 } - 1 } }$ 的定义域为（ 1

A莊 $\cdot \left[ \frac { 1 } { 2 } , 1 \right) \cup ( 1 , + \infty )$ B.[-4,-1)U(-1.]c.[-4,2)u(2,1) D.[-4,1）U（1,+∞）

![](images/fb6dae95115b35dd829e915fea5277d2fd1acb93737cefa9552ffac39f6844c4.jpg)

# 学习笔记

(2)已知函数 $f ( x )$ 的定义域为 $( \phantom { - } - 1 , 0 )$ ，则函数 $f ( 2 x - 1 )$ 的定义域为）

A.(-1,1) $\mathrm { B . ~ } \left( 0 , \frac { 1 } { 2 } \right) \qquad \mathrm { C . ~ } ( { \bf \kappa } - 1 , 0 ) \qquad \mathrm { D . ~ } \left( \frac { 1 } { 2 } , 1 \right)$

# 学习笔记

（3)已知函数 $y = f ( x ^ { 2 } - 1 )$ 的定义域为 $[ \ - { \sqrt { 3 } } \ , { \sqrt { 3 } } \ ]$ ,则函数 $\scriptstyle y = f ( x )$ 的定义域为

# 学习笔记

# 考点13：函数值域

# 例13

$f ( x ) = { \left\{ \begin{array} { l l } { \log _ { \frac { 1 } { 2 } } x , x \geq } \\ { } \\ { 2 ^ { x } , x < 1 } \end{array} \right. }$ 1，（1）函数 的值域为( ）

A.[-1,2] B.(-8,2） C.(0，+∞） D.(-8，-2）

# 学习笔记

(2）函数 $y = 2 x + \sqrt { 1 - 2 x }$ 的值域是( ）

A.(-8,1] $\mathbf { B } . \left( \begin{array} { l l } { - \infty \ , { \frac { 5 } { 4 } } } \end{array} \right) \qquad \mathrm { C } . \left[ 1 , + \infty \ \right) \qquad \mathrm { D } . \left[ { \frac { 5 } { 4 } } , + \infty \ \right)$

# 学习笔记

（3）求 $\scriptstyle y = { \frac { 2 x - 3 } { x + 1 } }$ 的值域：

# 学习笔记

# 考点14：函数解析式的求解

# 例14

（1)已知函数 $f ( { \sqrt { x } } + 1 ) = x + 1$ ，则函数 $f ( x )$ 的解析式为（

（204 $\mathrm { A } . f ( x ) = x ^ { 2 }$ $\operatorname { B } . f ( x ) = x ^ { 2 } + 1 ( x \geq 1 )$ $\operatorname { C } . f ( x ) = x ^ { 2 } - 2 x + 2 ( x \geq 1 )$ $\operatorname { D } . f ( x ) = x ^ { 2 } - 2 x ( x \geq 1 )$

# 学习笔记

(2）已知 $f ( x )$ 满足 $2 f ( x ) + f ( 2 - x ) = x + 3$ ，求 $f ( x )$ 的解析式.

# 学习笔记

# 自我测试

# 拍照批改秒判对错

# 测试1

如果 $A = \left( { \begin{array} { l } { - 1 } \end{array} } , + \infty \right)$ ,那么正确的结论是(

$$
\mathrm { ~ \mathcal ~ { ~ A ~ . ~ } ~ 0 ~ } \subseteq A \mathrm { ~ \mathcal ~ { ~ B . ~ } ~ } \left\{ 0 \right\} \in A \mathrm { ~ \mathcal ~ { ~ C . ~ } ~ } \left\{ 0 \right\} \not \subseteq A \mathrm { ~ \mathcal ~ { ~ D . ~ } ~ } \partial \in A
$$

# 测试2

已知集合 $A = \{ \begin{array} { r l } \end{array}  - 1 , 1 \}$ ， $B = \{ x \ : | m x = 1 \}$ ，且 $A \cup B = A$ ，则 $m$ 的值为（ ）

A.1 B. $- 1$ （204 C.1或 $- 1$ D.1或-1或0

# 测试3

直角坐标平面除去两点 $A ( 1 , 1 ) , B ( 2 , - 2 )$ 可用集合表示为（ ）

A. $\{ { \bf \Phi } ( x , y ) { \bf \Phi } | x { \neq } 1 , y { \neq } 1 , x { \neq } 2 , y { \neq } - 2 \}$   
B號 $\cdot \left\{ ( x , y ) \left| { \binom { x \neq 1 } { y \neq 1 } } , \neq \right\} \right. _ { y \neq - 2 } ^ { x \neq 2 } \rangle$ $ \therefore \{ ( x , y ) | { \binom { x \neq 1 } { y \neq 1 } } , \operatorname { H } { \binom { x \neq 2 } { y \neq - 2 } } \}$   
D. $\left\{ \begin{array} { l } { { ( x , y ) \ | \big [ ( x - 1 ) ^ { 2 } + ( y - 1 ) ^ { 2 } \big ] \big [ ( x - 2 ) ^ { 2 } + ( y + 2 ) ^ { 2 } \big ] \ne 0 } } \end{array} \right\}$

# 测试4

已知集合 $M = \left\{ \begin{array} { l l } { - 1 , 0 , 1 } \end{array} \right\}$ ， $N = \left\{ x \ | x = a b , a , b \in M \right.$ ，且 $a \neq b \}$ ，则集合 $M$ 与集合 $N$ 的关系是（ ）

$$
\begin{array} { r } { \mathrm { ~  ~ { ~ \mathcal ~ { ~ B ~ } ~ } ~ } . M \cap N = N \qquad \mathrm { ~  ~ { ~ C ~ } ~ } . M \cup N = N \qquad \mathrm { ~  ~ { ~ D ~ } ~ } . M \cap N = \emptyset } \end{array}
$$

# 测试5

下列说法正确的是( ）

A.若 $a \in \mathbb { R }$ ，则“ $\frac { 1 } { a } < 1$ ”是“ $a > 1$ ”的必要不充分条件B.“ $_ p \wedge q$ 为真命题”是“ $p \vee q$ 为真命题”的必要不充分条件C.若命题 $p$ ：“ $\forall x \in \mathbf { R } , \sin x + \cos x \leqslant { \sqrt { 2 } } $ ，则 $\lnot p$ 是真命题D.命题“ $\exists x _ { 0 } \in \mathbf { R } , x _ { 0 } ^ { 2 } + 2 x _ { 0 } + 3 < 0 ^ { \prime }$ ”的否定是“ $\forall x \in \mathbf { R } , x ^ { 2 } + 2 x + 3 > 0$

# 测试6

下列四个说法：

$\textcircled{1}$ 若函数的定义域和对应关系确定，则值域也就确定了；  
$\textcircled{2}$ 若函数的值域只含有一个元素，则定义域也只含有一个元素；  
$\textcircled{3}$ 若 $f ( x ) = 5 ( x \in \mathbf { R } )$ ，则 $f ( \pi ) = 5$ 一定成立；  
$\textcircled{4}$ 函数就是两个集合之间的对应关系.  
其中正确说法的个数为( ）

A.1 B.2 C.3 D.4

# 测试7

若 $f ( x ) = { \frac { 1 } { \log _ { { \frac { 1 } { 2 } } } ( 2 x + 1 ) } }$ 则 $f ( x )$ 的定义域为(

$$
\begin{array} { l l } { { \mathrm { A . ~ \left( \begin{array} { l } { { - \frac { 1 } { 2 } , 0 } } \end{array} \right) ~ } } } & { { \mathrm { B . ~ \left( \begin{array} { l } { { - \frac { 1 } { 2 } , + \infty } } \end{array} \right) ~ } } } \\ { { \mathrm { C . ~ \left( \begin{array} { l } { { - \frac { 1 } { 2 } , 0 } } \end{array} \right) \cup ( 0 , + \infty ~ ) ~ } } } & { { \mathrm { D . ~ \left( \begin{array} { l } { { - \frac { 1 } { 2 } , 2 } } \end{array} \right) ~ } } } \end{array}
$$

# 测试8

下列四个函数 ${ \mathrm { ; } } { \mathrm { } } { \mathrm { } } \textcircled { \mathrm { 1 } } y = 3 - x { \mathrm { ; } } { \mathrm { } } \textcircled { \mathrm { 2 } } y = \frac { 1 } { x ^ { 2 } + 1 } { \mathrm { ; } } \textcircled { \mathrm { 3 } } y = x ^ { 2 } + 2 x - 1 0 { \mathrm { ; } }$ $\textcircled { 4 } y = \left\{ { - \frac { x \left( x \leqslant 0 \right) } { x } } , \right.$ 其中值域为 $\mathbf { R }$ 的函数有( ）

A.1个 B.2个 C.3个 D.4个

# 学而思秘籍系列图书|数学

# 思维培养

# 思维突破

![](images/aa4d3ed9de433bb1daf6220dd90a52a5a14fab9934cb9f685a62d3e1c0f749d0.jpg)

# 思维提升

# 小学秘籍系列

学而思积淀近20年教研经验，培养受益一生的能力。

学而思秘籍 有理数数轴初中数学思维提升重++ ..

# 初中秘籍系列

全面覆盖初中基础知识和重难点，帮助学生夯实基础，拓展认知。

![](images/83116a4e04bad1927bc7ffe00f6d76013cb2762639f3e37ee62a0c56b45208cd.jpg)

# 高中秘籍系列

全面覆盖高中基础知识和重难点，帮助学生提升能力，突破思维。

# 学而思秘籍系列图书|语文

# 提升素养

![](images/2d97f2695c112943800022f53f264ff3160dfe4c55be5d375aa2866e3ef2404c.jpg)

# 小学秘籍系列

5大模块+2条主线，能力与素养双向提升。

# 能力训练

![](images/454be892bcf93d0a98706866391af9fcc685024b1e02b43ae7d69db1cacaecbd.jpg)

# 初中秘籍系列

融合课改四大核心素养，培养爱阅读、 善写作，勤思考，会学习的学生。

# 创新体系|真题研习

![](images/3a89370b5e28d5284af4bd7457297416ab53072fe57f07749e9e92e37265f4e7.jpg)

# 思维创新大通关数学

攻克数学思维难题，通向理想中学。

# 大家一起来“升级”

# 参与方式

您在使用本书时，如有任何疑问或对图书有任何建议，请扫码进行反馈，并查看反馈采纳结果。

# 奖励

您的反馈一经采纳，我们将会送出总价值35元的图书抵扣券（相同内容的反馈，依据反馈时间，奖励前三位）。请扫码关注公众号，并在对话框中发送反馈时填写的手机号，领取抵扣券。

# 合理规划学习时间

先自己定一个目标，即制定半年学习规划。

![](images/40e3fc9eadc109d79e9ebfc479998b1057d43f225c64aee769af552b66c19231.jpg)

2 再将目标细化到每一周，每周学习一本（平均5个考点）。

3 配套课堂巩固的练习， 让学习更有效！

![](images/1a173b3a943c91ba75486a2da215a6962a510ce2186b4daa72cc4810c68c289f.jpg)  
·共6级·每级17-26讲