---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 基础
estimated_study_time: 22
source_file: 3.1  函数的概念及表示（精讲）（原卷版）.md
title: 3.1  函数的概念及表示（精讲）（原卷版）
type: concept
---

# 3.1 函数的概念及表示（精讲）

![](images/95c1c59d123bf38529ca91885608aebad4d27f4b1534784564c5e6a418d981f0.jpg)

# 一．函数的概念

<html><body><table><tr><td colspan="2">概念</td><td>一般地，设A，B是非空的实数集，如果对于集合A中的任意一个数x，按照某种确 定的对应关系f，在集合B中都有唯一确定的数y和它对应，那么就称f：A→B为从</td></tr><tr><td rowspan="2">三</td><td>对应关系</td><td>集合A到集合B的一个函数 y=f(x)，x∈A</td></tr><tr><td>定义域</td><td>x的取值范围</td></tr><tr><td>要素</td><td>值域</td><td>与x对应的y的值的集合{f(x)x∈A}</td></tr></table></body></html>

1.函数的概念抓住两点

$\textcircled{1}$ 可以“多对一”、“不可一对多”$\textcircled{2}$ 集合 $A$ 中的元素无剩余，集合 $B$ 中的元素可剩余.2.对于 ${ } ^ { \left\{ 6 \right\} } f ( x ) ^ { \mathrm { 3 } }$ 中的 $^ { 6 6 } x ^ { , 5 }$ ，即可以是一个数，也可以是一个代数式.

# 二.区间

设 $a$ ， $\mathbf { \Pi } _ { b \in \mathbf { R } }$ ，且 $a { < } b$ ，规定如下：

<html><body><table><tr><td>定义</td><td>名称</td><td>符号</td><td>数轴表示</td><td></td></tr><tr><td>{xlasxsb)</td><td>闭区间</td><td>[a,b]</td><td>a</td><td>b</td></tr><tr><td>{x1a&lt;x&lt;b}</td><td>开区间</td><td>(a,b)</td><td>a</td><td>一</td></tr><tr><td>{x1asx&lt;b)</td><td>半开半闭区间</td><td>[a，b)</td><td></td><td></td></tr><tr><td>{x1Q&lt;x&lt;b}</td><td>半开半闭区间</td><td>(a，b]</td><td>a b</td><td></td></tr><tr><td>{xx&gt;a}</td><td></td><td>[a，+∞0)</td><td>a</td><td></td></tr><tr><td>{x&gt;a）</td><td></td><td>（a，+∞）</td><td>a</td><td></td></tr><tr><td>{x1x&lt;a）</td><td></td><td>（18，a]</td><td></td><td>a</td></tr><tr><td>{xxca）</td><td></td><td>（18，a）</td><td></td><td>a</td></tr><tr><td>R</td><td></td><td>（18，+8）</td><td></td><td></td></tr></table></body></html>

1.区间的左端点必小于右端点；

2.区间符号里面的两个字母(或数字)之间用“，”隔开；

3.用数轴表示区间时，要特别注意属于这个区间端点的实数用实心点表示，不属于这个区间端点的实数用空心点表示；

4.无穷大(∞)是一个符号，不是一个数，因此它不具备数的一些性质和运算法则；

5.包含端点用闭区间，不包含端点用开区间，以“＋∞”或“－∞”为区间的一个端点时，这一端必须是小括号．

# 三.同一个函数

1.前提条件： $\textcircled{1}$ 定义域相同； $\textcircled{2}$ 对应关系相同.  
(2)结论：这两个函数为同一个函数.

# 四.常见函数的值域

(1)一次函数 $\scriptstyle f ( x ) = a x + b ( a \neq 0 )$ 的定义域为 $\mathbf { R }$ ，值域是 R.(2)二次函数 $f ( x ) { = } a x ^ { 2 } { + } b x { + } c ( a { \neq } 0 )$ 的定义域是 R，  
当 $a { > } 0$ 时，值域为，  
当 $a { < } 0$ 时，值域为.

# 五.函数的三种表示方法

<html><body><table><tr><td>表示法</td><td>定义</td></tr><tr><td>解析法</td><td>用数学表达式表示两个变量之间的对应关系</td></tr><tr><td>图象法</td><td>用图象表示两个变量之间的对应关系</td></tr><tr><td>列表法</td><td>列出表格来表示两个变量之间的对应关系</td></tr></table></body></html>

# 六.分段函数

分段函数在书写时要用大括号，把各段函数合并写成一个函数的形式，并写出各段的定义域.

1.一般地，分段函数就是在函数定义域内，对于自变量 $x$ 的不同取值范围，有着不同的对应关系的函数.2.分段函数是一个函数，其定义域、值域分别是各段函数的定义域、值域的并集；各段函数的定义域的交集是空集.  
3.作分段函数图象时，应分别作出每一段的图象.

4.注意事项

(1)分段函数是一个函数而不是几个函数；  
(2)分段函数中各段自变量的取值范围的交集是空集；  
(3)处理分段函数问题时，首先要确定自变量的取值属于哪一个范围，从而选择相应的对应关系.

# 思路点拨

# 一.根据图形判断对应关系是否为函数的方法

1.任取一条垂直于 $X$ 轴的直线l；

2.在定义域内平行移动直线 $I ;$ ；

3.若l与图形有且只有一个交点，则是函数；若在定义域内没有交点或有两个或两个以上的交点，则不是

函数.

# 二.判断一个对应关系是否为函数的方法

![](images/8c64d07e5585aeadbc316fa397d9cef7ce64fff2b4fd697872e291b08e3dc18e.jpg)

# 三．求函数定义域

1.如果 $\boldsymbol { \mathscr { f } } ( \boldsymbol { \boldsymbol { x } } )$ 是整式，那么函数的定义域是实数集R.

2.如果 $\boldsymbol { \mathscr { f } } ( \boldsymbol { \boldsymbol { x } } )$ 是分式，那么函数的定义域是使分母不等于零的实数的集合.

3.如果 $\boldsymbol { \mathscr { f } } ( \boldsymbol { \boldsymbol { x } } )$ 是偶次根式，那么函数的定义域是使根号内的式子不小于零的实数的集合.

4.如果 $f ( \boldsymbol { \boldsymbol { \chi } } )$ 是由几部分构成的，那么函数的定义域是使各部分都有意义的实数的集合，也就是使各部分有意义的实数的集合的交集.

5.如果 $\boldsymbol { \mathscr { f } } ( \boldsymbol { \boldsymbol { x } } )$ 是根据实际问题列出的，那么函数的定义域是使解析式本身有意义且符合实际意义的实数的集合.

# 四．判断两个函数为同一函数

1.定义域、对应关系两者中只要有一个不相同就不是同一函数，即使定义域与值域都相同，也不一定是同一函数.

2.函数是两个数集之间的对应关系，所以用什么字母表示自变量、因变量是没有限制的.

3.在化简解析式时，必须是等价变形.

# 五．求函数值

1.方法： $\textcircled{1}$ 已知 $\boldsymbol { \mathscr { f } } ( \boldsymbol { \boldsymbol { x } } )$ 的解析式时，只需用 $^ { a }$ 替换解析式中的 $X$ 即得 $\boldsymbol { \mathscr { f } } ( \boldsymbol { a } )$ 的值；$\textcircled{2}$ 求 $\scriptstyle f ( g ( a ) )$ 的值应遵循由里往外的原则.

2.关注点：用来替换解析式中 $X$ 的数 $^ { a }$ 必须是函数定义域内的值，否则求值无意义.

# 六．求函数值域的常用方法

1.观察法：通过对解析式的简单变形和观察，利用熟知的基本函数的值域，求出函数的值域.  
2.配方法：若函数是二次函数形式，即可化为 $y = a x ^ { 2 } + b x + c ( a \neq 0 )$ 型的函数，则可通过配方再结合二次函数的性质求值域，但要注意给定区间的二次函数最值的求法.  
3.换元法：通过对函数的解析式进行适当换元，可将复杂的函数化归为简单的函数，从而利用基本函数自变量的取值范围求函数的值域.  
4.分离常数法：此方法主要是针对分式函数，即将分式函数转化为“反比例函数”的形式，便于求值域.七．求函数解析式

1.已知 $f ( g ( x ) ) = h ( x )$ 求 $\boldsymbol { \mathscr { f } } ( \boldsymbol { \boldsymbol { x } } )$ ，常用的有两种方法：

(1)换元法：即令 $ t { = } g ( x )$ 解出 $X ,$ ，代入 $h ( x )$ 中得到一个含 $t$ 的解析式，即为函数解析式，注意换元后新元的范围.  
(2)配凑法：即从 $f ( g ( x ) )$ 的解析式中配凑出“ $g ( \boldsymbol { x } ) ^ { \prime }$ ”，即用 $g ( \boldsymbol { x } )$ 来表示 $h ( x )$ ，然后将解析式中的 $g ( \boldsymbol { x } )$ 用 $X$ 代替即可.  
2.方程组法：当同一个对应关系中的含有自变量的两个表达式之间有互为相反数或互为倒数关系时，可构造方程组求解.  
3.待定系数法求函数解析式  
已知函数的类型，如是一次函数、二次函数等，即可设出 $f ( \boldsymbol { \boldsymbol { \chi } } )$ 的解析式，再根据条件列方程(或方程组)，通过解方程(组)求出待定系数，进而求出函数解析式.

# 八．分段函数

1.函数值的方法

(1)先确定要求值的自变量属于哪一段区间；(2)然后代入该段的解析式求值，直到求出值为止.当出现 $\boldsymbol { \mathscr { f } } ( \boldsymbol { f } ( \boldsymbol { \chi } _ { 0 } ) )$ 的形式时，应从内到外依次求值.2.已知分段函数的函数值求对应的自变量的值，可分段利用函数解析式求得自变量的值，但应注意检验函数解析式的适用范围，也可先判断每一段上的函数值的范围，确定解析式再求解.

3.由分段函数的图象确定函数解析式的步骤：

(1)定类型：根据自变量在不同范围内图象的特点，先确定函数的类型；  
(2)设函数式：设出函数的解析式；  
(3)列方程(组)：根据图象中的已知点，列出方程或方程组，求出该段内的解析式；  
(4)下结论：最后用“{”表示出各段解析式，注意自变量的取值范围.

4.作分段函数图象的注意点

作分段函数的图象时，定义域内各分界点处的取值情况决定着图象在分界点处的断开或连接，特别注意端点处是实心点还是空心点.

# 考法解读

# 考点一 函数关系的判断

【例 1-1】（203·江苏扬州）下列对应是集合 到集合 $B$ 的函数的是（ ）

A． $A = B = \mathrm { R }$ ， $f : x \to y = 1$ B． $\scriptstyle A = Z , B = \mathrm { Q } , f : x \to y = { \frac { 1 } { x } }$

C． ，D． $A = [ 0 , + \infty ) , B = \mathrm { R } , f : x  y = \pm \sqrt { x }$

【例 1-2】（2023·内蒙古赤峰）下面图象中，不能表示函数的是（ ）

![](images/973c05b7c78e71745a0ae60dc343b73239890b919ff7736297ccf05caf67d169.jpg)

# 【一隅三反】

1．（2023·江苏）（多选）下列对应关系是实数集 上的函数的是（　　）

A． ：把 对应到 B． ：把 对应到C． $h$ ：把 $X$ 对应到 $\frac { 1 } { x }$ D． ：把 对应到 $\sqrt { x }$

2．（2022·江西景德镇）（多选）托马斯说：“函数是近代数学思想之花”，根据函数的概念判断：下列关系属于集合 $A = \{ - 1 , 0 , 1 \}$ 到集合 $B = \left\{ 0 , 1 \right\}$ 的函数关系的是（ ）

A． B． C． D．

3．（2023·云南昆明）已知集合 $A = \left\{ x | 0 \leq x \leq 4 \right\}$ ， 集合 $B = \left\{ x | 0 \leq x \leq 2 \right\}$ 下列图象能建立从集合A 到集合$B$ 的函数关系的是（ ）

![](images/b6793d7ab7d22adfc314d3e9f95091b672f17da57dd52033ddd15c4699d537cc.jpg)

4．（2023·北京）（多选）下列是函数图象的是（ ）

![](images/5a7c8b0dbd858b8b25574e54f8ed7c3febc4db7994790e42b4c5f047fd70cfeb.jpg)

# 考点二 区间的表示

【例 2】（2022 广东湛江）把下列数集用区间表示：

(1){x|x≥－1}； (2){x|x<0}； $( 3 ) \{ x | - 1 { < } X { < } 1 \}$ ； $( 4 ) \{ x | 0 < x < 1$ 或 $2 \leqslant X \leqslant 4 \}$ .

# 【一隅三反】

1．（2023 云南大理）集合 可用区间表示为（　　）

A． B． C． D．

2．（2023 广州）若实数 $\chi$ 满足 $\left\{ x \vert 3 \leq x < 7 \right\}$ ，则用区间表示为（　　）

A． B． C． D．

# 考点三 函数的定义域

【例 3-1】（1）（2023·湖南衡阳）函数 $y = { \frac { 1 } { x - 1 } } + { \sqrt { x + 2 } }$ 的定义域为

（2）（2023·江苏·高一假期作业）求函数 $y = \frac { \sqrt { x + 2 } } { \sqrt { 6 - 2 x } - 1 }$ 的定义域为

【例3-2】（1）（2023·海南）已知函数 $f \left( x \right)$ 的定义域为 $( 0 , 1 )$ ，则函数 $f \left( x ^ { 2 } \right)$ 的定义域是

（2）（2023·上海）已知函数 的 定义域为[－2,2]，则函数 的定义域为_

（3）（2022 广西）函数 $f \left( 3 x + 1 \right)$ 的定义域为 $\left[ 1 , 7 \right]$ ， 则函数 $f \left( x \right)$ 的定义域是

【例 3-3】（2023·河北衡水）已知函数 $y = f \left( x \right)$ 的定义域为 $\left[ 0 , 4 \right]$ ，则函数 $y = \frac { f ( x + 1 ) } { \sqrt { x - 1 } } + ( x - 2 ) ^ { 0 }$ 的定义域是（ ）

A． B． (1,2)u(2,5) C． （1,2)u(2,3] D．

【例3-4】（2023 安徽）已知等腰三角形ABC 的周长为10,且底边长y 关于腰长x 的函数关系为 $\scriptstyle v = 1 0 - 2 x ,$ ,则函数的定义域为( )

A． $\{ x | x { \in } \mathbb { R } \}$ B．{x|x>0} C． $\{ x | 0 < x < 5 \}$ D．

# 【一隅三反】

1．（2023·福建）函数 的定义域为_

2．（2023·全国·高一专题练习）函数 $f ( x ) = { \sqrt { \frac { x - 1 } { x ^ { 2 } + 1 } } }$ 的定义域为

3．（2022 秋·福建）已知函数 $f \left( x \right)$ 的定义域为 $\left[ - 1 , 1 \right]$ 则 $y = \frac { f \left( x + 1 \right) } { \sqrt { x ^ { 2 } - 2 x - 3 } }$ 的定义域为_

4．（2023·湖南）函数 $f \left( - 2 x + 1 \right)$ 的定义域为 $\left[ - 2 , 1 \right]$ ，则f(x-1）的 的定义域为

5．（2022 秋·山东烟台·高一校考阶段练习）如图，某小区有一块底边和高均为 $4 0 \mathsf { m }$ 的锐角三角形空地，现规划在空地内种植一边长为 $x$ （单位：m）的矩形草坪（阴影部分），要求草坪面积不小于 $3 3 6 \mathrm { m } ^ { 2 }$ ，则 $x$ 的取值范围为

![](images/5ac1b1cf73edd4b3288801f1fd4e2bf2a979ceaf2891beda941885bcb8a75d0d.jpg)

# 考点四 同一函数的判断

【例 4】（2022 秋·福建福州）下列函数表示同一个函数的是（ ）.

$f \left( x \right) = \frac { \sqrt { x ^ { 2 } } } { x } \mathinner { \overleftrightarrow { \ v { x } } } _ { \varTheta } \left( x \right) = x ^ { 0 }$ B． $f \left( x \right) = \sqrt { x - 1 } \cdot \sqrt { x + 1 }$ 与 $g \left( x \right) = \sqrt { \left( x - 1 \right) \left( x + 1 \right) }$ C． $\scriptstyle y = { \sqrt { - 2 x ^ { 3 } } }$ 与 $y = x { \sqrt { - 2 x } }$ D． 与

# 【一隅三反】

1．（2023·全国·高一假期作业）下列各函数中，与函数 表示同一函数的是（ ）

A． $f ( x ) \neq x |$ B． $f ( x ) = \pm | x |$ C $f ( x ) = { \frac { x ^ { 2 } } { \mid x \mid } }$ D． $f ( x ) = x ^ { 0 } \cdot \left| x \right|$

2．（2023·山东）下列每组中的函数是同一个函数的是（ ）

A． ， B $\left. \begin{array} { l l } { \right\} _ { \cdot } } & { f \left( t \right) = \left| t \right| , } & { g \left( x \right) = \sqrt { x ^ { 2 } } } \end{array}$ C． $f \left( x \right) = \sqrt { - 2 x ^ { 3 } } , g \left( x \right) = \sqrt { - 2 x } \mathrm { ~ \qquad ~ } \mathrm { ~ } \mathrm { ~ } \mathrm { ~ } \mathrm { ~ } \mathrm { ~ } \mathrm { ~ } \mathrm { ~ } \mathrm { ~ } \mathrm { ~ } \mathrm { ~ } \mathrm { ~ } \mathrm { ~ } \mathrm { ~ } f \left( x \right) = \frac { x ^ { 2 } - 9 } { x - 3 } , g \left( x \right) = x + 3$

3．（2023 河南）下列各组函数为同一函数的是（　　）

$$
\begin{array} { r l } & { \underset { \triangledown } { \widehat { \mathbb { D } } } ^ { f } \left( x \right) = x ^ { 2 } - 2 x - 1 \underset { \triangledown } { \underline { { \operatorname { \Pi } } } } g \left( s \right) = s ^ { 2 } - 2 s - 1 ; } \\ & { \underset { \triangledown } { \widehat { \mathbb { D } } } ^ { f } \left( x \right) = \sqrt { - x ^ { 3 } } \underset { \triangledown } { \underline { { \operatorname { \Pi } } } } _ { \triangledown } g \left( x \right) = x \sqrt { - x } ; } \\ & { \underset { \triangledown } { \widehat { \mathbb { D } } } ^ { f } \left( x \right) = x \underset { \triangledown } { \underline { { \operatorname { \Pi } } } } _ { \triangledown } g \left( x \right) = \sqrt { x ^ { 2 } } . } \end{array}
$$

A． $\textcircled{1} \textcircled{2}$ B． $\textcircled{1}$ C． $\textcircled{2}$ D． $\textcircled{3}$

# 考点五 三种函数的表示方法

【例 5】（2023 新疆）某公共汽车，行进的站数与票价关系如下表：

<html><body><table><tr><td></td><td></td><td></td><td>行进的站数|1234</td><td></td><td>5</td><td></td><td>67</td><td>8</td><td>9</td></tr><tr><td>票价</td><td></td><td></td><td></td><td>11122</td><td></td><td>2</td><td>3</td><td>3</td><td>3</td></tr></table></body></html>

此函数的关系除了图表之外，能否用其他方法表示？

# 【一隅三反】

1．（2023 内蒙古）公司生产了 10 台机器，每台售价 3000 元，试求售出台数 $x$ 与收款数 y 之间的函数关系，分别用列表法、图象法、解析法表示出来.

2．（2022·高一课时练习）某种笔记本的单价是 5 元，买 $\chi ( x \in \{ 1 , 2 , 3 , 4 , 5 \} )$ 个笔记本需要 y 元，试用函数的三种表示法表示函数 $y = f ( x )$ .

3．（2022·高一课时练习）已知完成某项任务的时间 与参加完成此项任务的人数 $\boldsymbol { \chi }$ 之间满足关系式，当 时， ；当 时， ，且参加此项任务的人数不能超过 8．

（1）写出 $t$ 关于 $X$ 的解析式；（2）用列表法表示此函数；（3）画出此函数的图象

# 考点六 求函数值或值域

【例6-1】（2023 湖北）已知 ， ， 求：( $\begin{array} { r l } & { \operatorname * { \phantom { = } } _ { 1 ) } f \left( 2 \right) , g \left( 2 \right) } \\ & { \phantom { = } } \\ & { \phantom { = } _ { 2 ) } f \left( g \left( _ { 2 } \right) \right) , g \left( _ { f } \left( _ { 2 } \right) \right) } \\ & { \phantom { = } } \\ & { \phantom { = } _ { 3 } f \left( g \left( _ { x } \right) \right) , g \left( _ { f } \left( _ { x } \right) \right) } \\ & { \phantom { = } } \end{array}$

【例 6-2】（2023·江苏）试求下列函数的定义域与值域

$\scriptstyle ( 1 ) ^ { y = \left( x - 1 \right) ^ { 2 } + 1 } , x \in \{ - 1 , 0 , 1 , 2 , 3 \}  ,$ (2) ；  
$\begin{array} { l } { { ( 3 ) ^ { y } = \displaystyle \frac { 5 x + 4 } { x - 1 } ; } } \\ { { ( 4 ) ^ { y } = x - \sqrt { x + 1 } . } } \end{array}$

# 【一隅三反】

1．（2023·江苏连云港）（多选）下列函数与 $y = x ^ { 2 } - 2 x + 3$ 的值域相同的是（ ）

A ． $y = 4 x { \left( x \geq } { \frac { 1 } { 2 } } { \right) }$ B $y = { \frac { 1 } { \left| x \right| } } + 2$ C． D $\scriptstyle  \begin{array} { l } { { \cdot } } \\ { { \begin{array} { r l } { y = 2 x - { \sqrt { x - 1 } } } \end{array} } } \end{array}$

2．（2022 秋·浙江杭州）求下列函数的值域.

$$
{ } _ { 1 ) } f \left( x \right) = 2 x + 4 { \sqrt { 1 - x } } _ { ; { \left( 2 \right) } } f \left( x \right) = { \frac { 5 x + 4 } { x - 2 } } _ { ; { \left( 3 \right) } } f \left( x \right) = x ^ { 2 } - 2 x - 3 _ { , } x \in \left( - 1 , 4 \right] _ { . } \left( 4 \right) \quad y = { \frac { x ^ { 2 } + x + 1 } { x } } _ { \left( 2 \right) } f \left( x \right) = x ^ { 2 } - 2 x - 3 _ { , } x \in \left( - 1 , 2 \right) .
$$

# 考点七 函数解析式

f（x）【例7】（2023·江西南昌）根据下列条件，求 的解析式.(1)已知

(2)已知 $f \left( x \right) + 2 f \left( - x \right) = 3 x ^ { 2 } - 2 x$ (3)已知 $f \left( x \right)$ 是二次函数，且满足 $f \left( 0 \right) = 1 , f \left( x + 1 \right) - f \left( x \right) = 2 x$

# 【一隅三反】

1．（2023·云南大理）根据下列条件，求 $f \left( x \right)$ 的解析式

$f \left( x \right) _ { \ast \ast \ast \ast \ast } f \left( x + 1 \right) = x ^ { 2 } + 4 x + 1$ (1)已知

(2)已知 $f \left( x \right)$ 是一次函数，且满足 $3 f \left( x + 1 \right) - f \left( x \right) = 2 x + 9$

(3)已知 $f \left( x \right)$ 满足 $2 f \left( { \frac { 1 } { x } } \right) + f \left( x \right) = x { \left( x \neq 0 \right) }$

2．（2023·全国·专题练习）根据下列条件，求函数 $f ( x )$ 的解析式

(1)已知 ， 则 $f ( x )$ 的解析式为

(2)已知 $f ( x )$ 满足 $2 f ( x ) + f \left( { \frac { 1 } { x } } \right) = 3 x$ ，求 的解析式

(3)已知 $f ( 0 ) { = } 1$ ，对任意的实数 $x , \ y$ 都有 $f ( x - y ) = f ( x ) \cdot y ( 2 x - y + 1 )$ ，求 $f ( x )$ 的解析式．

# 考点八 分段函数

【例 8-1】（2023·广西梧州）已知函数 . $f \left( x \right) = \left\{ x ^ { 2 } + 1 , - 1 \leq x \leq 1 \atop 1 + \frac { 1 } { x } , x > 1  \right.$

(1)求 $f ( f ( - 2 ) )$ 的值；  
(2)若 ， 求 $x _ { 0 }$ 的值.

【例8-2】（2023·高一课时练习）已知函数 $f \left( x \right) = \left\{ x ^ { 2 } - 2 x , 0 < x \leq 4 \right. .$

（1）求 的 值；

f(x）（2）画出函数 的图象

【例8-3】（2022 秋·广东深圳）已知 $f \left( x \right) = x ^ { 2 } - 2 \lvert x \rvert + 2$

(1)用分段函数的形式表示该函数.

(2)画出 区间 上的的图象；

(3)根据图象写出 区间 上的值域.

# 【一隅三反】

1．（2022 秋·广东汕头·高一校考期中）已知函数 $f \left( x \right) = \left\{ { 2 , - 1 \leq x \leq 1 } , \right.$

$$
. f \left( - { \frac { 3 } { 2 } } \right) , f \left( { \frac { 1 } { 2 } } \right) , f \left( f \left( { \frac { 1 } { 2 } } \right) \right) ;
$$

(2)若 ， 求 的值．

2．（2023·全国·高一假期作业）已知函数 $f ( x ) = \left\{ \begin{array} { l l } { - x ^ { 2 } + 2 x ( 0 \leq x \leq 2 ) } \\ { x ^ { 2 } + 2 x ( - 2 \leq x < 0 ) . } \end{array} \right.$

![](images/5f9842453da4e1f4daf9f56063e6b4d34e164dbd87c3816294c835d4947cb496.jpg)

(1) 求 ， 的 值；

(2)作出函数的简图；  
(3)由简图指出函数的值域；

3．（2023·全国·高一专题练习）已知函数 $f \left( x \right) = \left\{ { \begin{array} { c } { { 2 } } \\ { { x } } \\ { { x ^ { 2 } - 3 , x < 2 } } \end{array} } \right.$

![](images/4356d918921bc010221c134d32e3aeb5a463e168c76300064bd13b3e08728af6.jpg)

(1)在所给坐标系中作出 $y = f \left( x \right)$ 的简图；

(2)解不等式 .

4．（2023·山东枣庄）某市“招手即停”公共汽车的票价按下列规则制定： $\textcircled{1}$ 公里以内(含 公里)，票价元； $\textcircled{2} 5$ 公里以上，每增加 公里，票价增加 元(不足 公里的按 公里计算)．如果某条线路的总里程为公里，  
(1)请根据题意，写出票价与里程之间的函数关系式；  
(2)画出该函数的图像