---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 31
source_file: 4.4 对数函数（精讲）（解析版）.md
title: 4.4 对数函数（精讲）（解析版）
type: practice
---

# 4.4 对数函数（精讲）

# 思维导图

一般地，函数 $\scriptstyle { \mathbf { y } } = \log _ { a } { \mathbf { x } } ( a > 0$ ，且 $\pmb { a } \neq \mathbf { 1 } )$ 叫做对数函数，其中 $_ { x }$ 是自变量，

![](images/ce9d0ac0d30e2eb507fb89eff874ff7c1ce093b95f99cd04de5cf59a2d40309b_1.jpg)

![](images/1794ca74dddc9182d5c5af1c0956d08b12ce7004cbaadfb8fec5bbebb497ae0c_1.jpg)

![](images/12412a6fd266ff880abb4dd589a68f0bd43d63a0e89b2b3b2cc4fa7266aadaec.jpg)

# 例题剖析

# 考点一 对数函数辨析

【例 1-1】（2022·全国·高一课时练习）下列函数是对数函数的是（ ）

$$
y = \log _ { a } \left( 2 x \right) \qquad \ \mathrm { ~ B _ { \rho } ~ } \mathrm { ~ y = l g 1 0 ^ { x } ~ } \qquad \mathrm { ~ C _ { \rho } ~ } \mathrm { ~ y = l o g _ { \rho } \left( { \chi ^ 2 + x } \right) ~ } \ \mathrm { ~ \Gamma _ { D , } ~ } \mathrm { ~ y = l n ~ } x
$$

【答案】D

【解析】因为函数 $y = \log _ { a } \chi _ { \textit { a } > 0 }$ ( 且 )为对数函数，所以ABC 均为对数型复合函数，而D 是底数为自然常数的对数函数.故选：D.

【例1-2】（2022西藏）若函数 $y = \log _ { a } x + a ^ { 2 } - 3 a + 2$ 为对数函数，则 （ ）

A． B． C． D．

【答案】B

【解析】由题可知：函数 $y = \log _ { a } x + a ^ { 2 } - 3 a + 2$ 为对数函数所以 $a ^ { 2 } - 3 a + 2 = 0 \Rightarrow a = 1$ 或 $a = 2$ ，又 $a > 0$ 且 $a \neq 1$ 所以 $a = 2$ 故选：B

# 【一隅三反】

1．（2022 哈尔滨）下列函数中，是对数函数的是（ ）

A． $y { = } \log x a ( x { > } 0$ 且 $x { \neq } 1$ ) B．y＝log2x－1C． D． $y { = } \log _ { 5 } x$

【答案】D

【解析】A、B、C 都不符合对数函数的定义，只有 $\mathrm { D }$ 满足对数函数定义．故选：D.

2．（2021·全国·高一课时练习）已知函数 $\textcircled { 1 } y = 4 ^ { x }$ ； $\scriptstyle ( { \overline { { 2 } } } ) ^ { y = \log _ { x } 2 }$ ； ${ \textcircled { 3 } } ^ { y = - \log _ { 3 } \chi }$ ； $\textcircled { 4 } ^ { y = } \log _ { 0 . 2 } \sqrt { x }$ ； $\textcircled{5}$ $y = \log _ { 3 } x + 1$ $_ { \textcircled { 6 } } y = \log _ { 2 } ( x + 1 )$ .其中是对数函数的是（ ）

A． $\textcircled{1} \textcircled{2} \textcircled{3 }$ B． $\textcircled{3} \textcircled{4} \textcircled{5}$ C． $\textcircled{3} \textcircled{4}$ D． $\textcircled{2} \textcircled{4} \textcircled{6}$

【答案】C

【解析】根据对数函数的定义，只有符合 $y = \log _ { a } X$ （ $a > 0$ 且 $a \neq 1$ ）形式的函数才是对数函数，其中 $\boldsymbol { X }$ 是自变量， $a$ 是常数.易知， $\textcircled{1}$ 是指数函数； $\textcircled{2}$ 中的自变量在对数的底数的位置，不是对数函数； $\cdot$ 中$y = - \log _ { 3 } x = \log _ { \frac { 1 } { 3 } } x$ 是对数函数； $\textcircled{4}$ 中 $y = \log _ { 0 . 2 } \sqrt { x } = \log _ { 0 . 0 4 } x$ ，是对数函数； $\textcircled{5}$ 中函数显然不是对数函数，由此可知只有 $\textcircled{3} \textcircled{4}$ 是对数函数.  
故选：C.

3．（2022·全国·高一课时练习）函数 $f \left( x \right) = \left( a ^ { 2 } - a + 1 \right) \log _ { \left( a + 1 \right) } x$ 是对数函数，则 $f \left( 8 \right) = $ _

【答案】3

【解析】由对数函数的概念可知 $\left\{ \begin{array} { l l } { a ^ { 2 } - a + 1 = 1 } \\ { a + 1 > 0 } \\ { a + 1 \neq 1 } \end{array} \right.$ ，解得 所以 $f \left( x \right) { = } { \log } _ { 2 } x$ ，$a = 1$   
则 $f \left( 8 \right) = \log _ { 2 } 8 = 3$ .故答案为:3.

# 考点二 对数函数的三要素

【例 2-1】（2022·湖北省）函数 定义域为（ ）

A． B． (n 2,1) u (1,2) D.[1n2,1)U(L2] C．

【答案】C

【解析】因为 $f \left( x \right) = \ln \left( \mathrm { e } ^ { x } - 2 \right) + \frac { \left( x - 1 \right) ^ { 0 } } { \sqrt { 2 - x } }$ 所以 $\left\{ \begin{array} { l l } { \mathbf { e } ^ { x } - 2 > 0 } \\ { x - 1 \neq 0 } \\ { 2 - x > 0 } \end{array} \right.$ 解得 且In2<x<2x≠1所以函数的定义域为 $( \ln 2 , 1 ) \cup ( 1 , 2 )$ 故选：C

【例2-2】（2022·陕西）函数 $f \left( x \right) { = } \log \left( 4 ^ { x } - 2 ^ { x + 1 } + 1 1 \right)$ 的最小值是（ ）

A．10 B．1 C．11 D．

【答案】B

【解析】设 $t = 4 ^ { x } - 2 ^ { x + 1 } + 1 1$ ，则 $y = \lg t$ ，因为 $t = 4 ^ { x } - 2 ^ { x + 1 } + 1 1 = \left( 2 ^ { x } \right) ^ { 2 } - 2 \cdot 2 ^ { x } + 1 1 = \left( 2 ^ { x } - 1 \right) ^ { 2 } + 1 0 \geq 1 0 ,$ 所以 ， 所以 $f \left( x \right) { = } \log \left( 4 ^ { x } - 2 ^ { x + 1 } + 1 1 \right)$ 的最小值为 1，选：B

【例 2-3】（2022·全国·高一单元测试）已知函数 $f \left( x \right) = { \frac { 1 } { \sqrt { x + 2 a } } }$ 的定义域为 $\mathrm { A }$ ，函数

$y = g \left( x \right) = \log _ { 2 } \left( x ^ { 2 } - x + \frac { 9 } { 4 } \right)$ 的值域为 $B$ ，若 $A \subseteq B$ ，则 $a$ 的取值范围为（ ）

A． $\left( - \ { \frac { 1 } { 2 } } , + \infty \right)$ B $\quad \left\{ \begin{array} { c } { { \displaystyle - \frac { 1 } { 2 } , + \infty } } \\ { { \hphantom { + \ddots } } } \end{array} \right\}$ C． D $\left( - \infty , - { \frac { 1 } { 2 } } \right)$

【答案】D

【解析】根据题意得 $A = \left\{ x | x > - 2 a \right\}$ ， 因为 $x ^ { 2 } - x + { \frac { 9 } { 4 } } = \left( x - { \frac { 1 } { 2 } } \right) ^ { 2 } + 2 \geq 2$ ， 所以 $\log _ { 2 } \left( x ^ { 2 } - x + { \frac { 9 } { 4 } } \right) \geq \log _ { 2 } 2 = 1$ 所以 ，由 可得 ， 则a≤- $a \leq - \ { \frac { 1 } { 2 } }$ 故选：D

# 【一隅三反】

1．（2021·云南省）已知 ，则函数 $f \left( x \right)$ 的定义域为_

【答案】 1

3x>4【解析】由题意可知，要使 有意义，则 $\left\lceil 4 x - 3 > 0 \right\rceil$ 解得 3 ， 即$f \left( x \right) = \sqrt { \log _ { 0 . 5 } \left( 4 x - 3 \right) }$ $\lfloor \log _ { 0 . 5 } ( 4 x - 3 ) \geq 0$ 4<x≤1$\frac { 3 } { 4 } < x \leq 1$ 所以函数 $f \left( x \right)$ 的定义域为 $\left( { \frac { 3 } { 4 } } , 1 \right]$ .故答案为： $\left( { \frac { 3 } { 4 } } , 1 \right]$

2．（2022·贵州·）函数 $y { = } 2 { + } \log _ { 2 } ( x ^ { 2 } { + } 3 ) ( x { \geq } 1 )$ 的值域为（ ）

A．(2， $+ \infty )$ B．(－∞，2)C．[4， $+ \infty )$ D．[3， $+ \infty )$

【答案】C

【解析】令 $t = x ^ { 2 } + 3 \geq 4$ ，又因为 $y = 2 + \log _ { 2 } { t }$ 在 $[ 4 , + \infty )$ 上递增，所以 $y \geq 2 + \log _ { 2 } 4 = 4$ ，所以 $-$ 的值域为 [4， $+ \infty )$ ，故选：C

3（2022·全国·高一单元测试）已知函数 $f \left( x \right) = \log _ { a } x + 2$ （ $a > 0$ ，且 $a \neq 1 \cdot$ ）在 上的值域为 ， 则实数 $a$ 的值是（ ）

1 A． $\sqrt { 3 }$ B． C． D．

【答案】A

【解析】若 ， 则 $f \left( x \right) = \log _ { a } x + 2$ 在 [1,3] 上单调递减，则 $\log _ { a } 3 + 2 \leq f \left( x \right) \leq 2$ 不符合题意；若 ， 则 $f \left( x \right) = \log _ { a } x + 2$ 在 [1,3] 上单调递增，则 $2 \leq f \left( x \right) \leq \log _ { a } 3 + 2$ ，又因为 的值域为 ， 所以 $\log _ { a } 3 + 2 = 4$ ， 解得 故选：A．

# 考点三 对数函数的单调性

【例 3-1】（2022·全国·高一课时练习）函数 $y = \log _ { 2 } ( 2 x - x ^ { 2 } )$ 的单调递减区间为（ ）

A． B． (1,2] C． D． [0,1)

【答案】A

【解析】由 $2 x - \ x ^ { 2 } > 0$ ， 得 $0 < x < 2$ ， $\scriptstyle t = 2 x - x ^ { 2 }$ ： 则 ，$t = 2 x - x ^ { 2 } \quad ( 0 , 1 )$ 上递增，在 $( 1 , 2 )$ 上递减，因为 $y = \log _ { 2 } t$ 在定义域内为增函数，所以 $y = \log _ { 2 } ( 2 x - x ^ { 2 } )$ 的单调递减区间为 ， 故选：A

【例3-2】（2022·四川）已知 $f \left( x \right) = \left\{ { { \begin{array} { l } { \left( { 3 a - 1 } \right) x + 4 a , x < 1 } \\ { \log _ { a } x , x \geq 1 } \end{array} } } \right.$ 是 $( - \infty , + \infty )$ 上的减函数，那么 $a$ 的取值范围是（ ）

A． B. B． $\left( 0 , { \frac { 1 } { 3 } } \right)$ C． $\left[ { \frac { 1 } { 7 } } , { \frac { 1 } { 3 } } \right]$ D． $\left( { \frac { 1 } { 7 } } , 1 \right)$

【答案】C

【解析】当 $x < 1$ ， $f \left( x \right) = \left( 3 a - 1 \right) x + 4 a$ 是减函数，所以 $3 a - 1 < 0$ ， 即a< $a < \frac { 1 } { 3 } \cdots \textcircled { 1 }$ ；  
${ \underline { { \underline { { \operatorname { u } } } } } } \ x \geq 1 , \quad f \left( x \right) = \log _ { a } x$ 也是减函数，故 $0 < a < 1 \ldots \textcircled { 2 }$   
在衔接点 $\cdot$ ，必须要有 $\left( 3 a - 1 \right) \times 1 + 4 a \geq \log _ { a } 1 = 0$ 成立，才能保证 $f \left( x \right) \quad x \in \left( - \infty , + \infty \right)$ 上是减函数，即$a \geq \frac { 1 } { 7 } \ldots \ldots \textcircled { 3 }$ ，∴由 $\textcircled{1} \textcircled{2} \textcircled{3 }$ 取交集，得： $\frac { 1 } { 7 } \leq a < \frac { 1 } { 3 }$ ；故选：C.

# 【一隅三反】

1．（2021·浙江·玉环中学高一阶段练习）函数 $f \left( x \right) = \log _ { \frac { 1 } { 2 } } \left( - x ^ { 2 } + 3 x - 2 \right)$ 的单调递减区间为（ ）

A． $\left( - \infty , { \frac { 3 } { 2 } } \right)$ B． $\left( 1 , { \frac { 3 } { 2 } } \right)$ C． D $\left( { \frac { 3 } { 2 } } , + \infty \right)$

【答案】B

【解析】由 得： $1 < x < 2$ ， 即 定义域为 ；  
令 $t = - x ^ { 2 } + 3 x - 2$ ，则 $t$ 在 $\left( 1 , { \frac { 3 } { 2 } } \right)$ 上单调递增，在 $\left( { \frac { 3 } { 2 } } , 2 \right)$ 上单调递减；  
$\scriptstyle y = \log _ { 1 } t$ 在 $\left( 0 , + \infty \right)$ 上单调递减，$\therefore f ( x ) = \log _ { \frac { 1 } { 2 } } ( - x ^ { 2 } + 3 x - 2 )$ 的单调递减区间为 $\left( 1 , { \frac { 3 } { 2 } } \right)$ .  
故选：B.

2．（2022·山东 ）已知 $a > 0$ 且 $a \neq 1$ ，函数 $f \left( x \right) = \left\{ \begin{array} { l l } { ( 2 - a ) x - 3 a + 3 , x < 1 } \\ { \log _ { a } x , x \geq 1 } \\ { \qquad \mathrm { ~ i ~ } } \end{array} \right.$ ，满足 $\boldsymbol { X } _ { 1 } \neq \boldsymbol { X } _ { 2 }$ 时，恒有$\frac { f \left( x _ { _ 1 } \right) - f \left( x _ { _ 2 } \right) } { x _ { _ 1 } - x _ { _ 2 } } > 0$ 成立，那么实数 $a$ 的取值范围（ ）

A． B． C． D．

【答案】D

【解析】由题可知函数 $f \left( x \right)$ 在区间 $\mathbf { R }$ 上为增函数，则 $\left\{ \begin{array} { l l } { 2 \cdot a > 0 } \\ { a > 1 } \\ { ( 2 \cdot a ) \cdot 3 a + 3 \leq 0 } \end{array} \right.$ 解可得 $\frac { 5 } { 4 } \leq a < 2$ .故选：D.

3．（2021·云南）若函数 $f ( x ) = \log _ { \frac { 1 } { 2 } } ( - x ^ { 2 } + 4 x + 5 )$ 在区间 $( 3 m - 2 , m + 2 )$ 内单调递增，则实数 $m$ 的取值范围为__

【答案】 $\frac { 4 } { 3 } \leq m < 2$

【解析】由 $\chi ^ { 2 } + 4 \chi + 5 > 0$ 可得 $\scriptstyle x ^ { 2 } - 4 x - 5 < 0$ ，解得 $1 < x < 5$ ，  
函数 $f ( x ) = \log _ { \frac { 1 } { 2 } } ( - x ^ { 2 } + 4 x + 5 )$ 是由 $y = \log _ { \frac { 1 } { 2 } } t$ 和 $t = - x ^ { 2 } + 4 x + 5$ 复合而成，  
又 $t = - x ^ { 2 } + 4 x + 5 = - ( x - 2 ) ^ { 2 } + 9$ 对称轴为 $x = 2$ 开口向下，  
所以 $t = - x ^ { 2 } + 4 x + 5$ 在 上单调递增，在 上单调递减，  
因为 $y = \log _ { \frac { 1 } { 2 } } t$ 为减函数，  
所以 $f ( x ) = \log _ { \frac { 1 } { 2 } } ( - x ^ { 2 } + 4 x + 5 )$ 的单调增区间为 ，  
因为 在区间 $( 3 m - 2 , m + 2 )$ 内单调递增，  
所以 $\left\{ \begin{array} { l } { 3 m - 2 \geq 2 } \\ { m + 2 \leq 5 } \\ { 3 m - 2 < m + 2 } \end{array} \right.$ ， 解得 $\frac { 4 } { 3 } \leq m < 2$ ，  
所以实数 $m$ 的取值范围为 $\frac { 4 } { 3 } \leq m < 2$ ， 故答案为： $\frac { 4 } { 3 } \leq m < 2$

# 考点四 对数函数单调性的运用

【例4】（2022·福建福州·高一期中）已知 $a = \lg 2 , b = \log _ { 2 } 3 , c = \log _ { 3 } 4$ ，则 $a$ b $c$ 的大小关系为（ ）

A． $a > b > c$ B． $a < b < c$ C． $a < c < b$ D． $c < a < b$

【答案】C

【解析】对数函数 $y = \mathrm { l g } \ x , y = \log _ { 2 } x , \log _ { 3 } x$ 都是 $( 0 , + \infty )$ 上的增函数， $2 < 3 < 4$ 于是得 ，$\log _ { 2 } 3 > 1 , \log _ { 3 } 4 > 1 \qquad \log _ { 3 } 2 \cdot \log _ { 3 } 4 < ( { \frac { \log _ { 3 } 2 + \log _ { 3 } 4 } { 2 } } ) ^ { 2 } = ( \log _ { 3 } 2 { \sqrt { 2 } } ) ^ { 2 } < 1$ ，而 $\log _ { 3 } 2 > 0$ ，于是得：$\log _ { 3 } 4 < { \frac { 1 } { \log _ { 3 } 2 } } = \log _ { 2 } 3$ 所以有 $a < c < b$ .故选：C

# 【一隅三反】

1．（2022·江苏高一开学考试）已知 $a = \log _ { 7 } 2$ ， $b = \log _ { 0 . 7 } 0 . 2$ ， $c = 0 . 7 ^ { 0 . 2 }$ ，则 ， ， 的大小关系为

A． $a < c < b$ B． $a < b < c$ C． $b < c < a$ D.c<a<b

【答案】A

【解析】 ， $a = \log _ { 7 } 2 < \frac { 1 } { 2 } , b = \log _ { 0 . 7 } 0 . 2 > \log _ { 0 . 7 } 0 . 7 = 1 , 0 . 7 < c = 0 . 7 ^ { 0 . 2 } < 1  , a < c < b  ,$ ， 故选 A.

2．（2021·河北沧州市一中高一开学考试）已知 $a = \log _ { 5 } 2$ ， $b = \mathrm { l o g } _ { 0 . 5 } 0 . 2$ ， ， 则 $a , b , c$ 的大小关系为

A． $a < c < b$ B． $a < b < c$ C． $b < c < a$ D． $c < a < b$

【答案】A

$$
a = \log _ { 5 } 2 < \log _ { 5 } \sqrt { 5 } < \frac { 1 } { 2 } , ~ b = \log _ { 0 . 5 } 0 . 2 > \log _ { 0 . 5 } 0 . 2 5 = 2 ,
$$

$0 . 5 ^ { 1 } < 0 . 5 ^ { \circ . 2 } < 0 . 5 ^ { \circ }$ ， 故 $\frac { 1 } { 2 } < c < 1$ ， 所以 $a < c < b$ 故选A

3．（2022·河南濮阳·高一期末（文））已知 $a = \log _ { 3 } 4$ ， $\boldsymbol { b } = \mathrm { l o g } _ { 4 } 5$ ， $c = \frac { 3 } { 2 }$ ，则有（ ）

A． $a > b > c$ B． $c > b > a$ C． $a > c > b$ D． $c > a > b$

【答案】D

【解析】依题意， $0 < 4 ^ { 2 } < 3 ^ { 3 }$ ， $\therefore 4 < 3 ^ { \bar { 2 } }$ ，  
是单调递增， $\therefore \log _ { 3 } 4 < \log _ { 3 } 3 ^ { \frac { 3 } { 2 } } = \frac { 3 } { 2 } , \because a < c ,$ ∵0<5²<4³， :.5<4²  
是单调递增， $\therefore \log _ { 4 } 5 < \log _ { 4 } 4 ^ { \frac { 3 } { 2 } } = \frac { 3 } { 2 } , ~ \cdots b < c ,$ $\because 4 ^ { 4 } > 3 ^ { 5 } > 0 \therefore 4 > 3 ^ { \frac { 5 } { 4 } }$   
是单调递增， $\therefore \log _ { 3 } 4 > \log _ { 3 } 3 ^ { \frac { 5 } { 4 } } = \frac { 5 } { 4 } \cdots a > \frac { 5 } { 4 }$ $\because 0 < 5 ^ { 4 } < 4 ^ { 5 } \therefore 5 < 4 ^ { \frac { 5 } { 4 } }$   
是单调递增， $\therefore \log _ { 4 } 5 < \log _ { 4 } 4 ^ { \frac { 5 } { 4 } } = \frac { 5 } { 4 } \cdots b < \frac { 5 } { 4 }$ 综上所述， $c > a > b$ .  
故选：D

# 考点五 对数函数的定点

【例5】（2022·广西）若 $a > 0 _ { \mathrm { ~ E ~ } } a \neq 1$ ，则函数 $y = \log _ { a } ( x - 1 ) + 2$ 的图像恒过定点（ ）

A．（2，1） B．（1，2） C．（2，3） D．（2，2）

【答案】D

【解析】根据对数函数的性质，当 时，则 $y = \log _ { a } 1 + 2 = 2$ ， 则函数过定点 .  
故选：D.

# 【一隅三反】

1．（2021·江苏·高一专题练习）已知函数 的图象过定点 $( m , n )$ ， 则 f(x)=m²x²+mx+1 在上的值域是（ ）

A． $\begin{array} { r } { \mathrm { ~ \mathsf ~ { \Sigma ~ } ~ } _ { \mathrm { \tiny ~ B } , \mathsf { \Sigma } } \left[ \frac { 1 } { 8 } , 2 \sqrt { 2 } \right] \qquad \mathrm { ~ \mathsf ~ { \Sigma ~ } ~ } _ { \mathrm { \tiny ~ C } , } \left[ 2 \sqrt { 2 } , 4 \right] \qquad \mathrm { ~ \mathsf ~ { \Sigma ~ } ~ } _ { \mathrm { \tiny ~ D } , } \left[ 4 , 4 \sqrt { 2 } \right] } \end{array}$

【答案】B

【解析】函数 的图象过定点 ， 所以 ，  
$f \left( x \right) = 2 ^ { - 2 x ^ { 2 } + 2 x + 1 } = 2 ^ { - 2 \left( x - { \frac { 1 } { 2 } } \right) ^ { 2 } + { \frac { 3 } { 2 } } }$   
由于 ，所以 $- 3 \leq - 2 ( x - \frac { 1 } { 2 } ) ^ { 2 } + \frac { 3 } { 2 } \leq \frac { 3 } { 2 }$   
所 以 $f \left( x \right) \in \left[ { \frac { 1 } { 8 } } , 2 { \sqrt { 2 } } \right] .$

故选：B

2．（2022·全国·高一课时练习）函数 $y = \log _ { a } ( 2 x - 3 ) + 4$ 的图象恒过定点 ，则 $M$ 为（ ）

(2,4) (2,5) A． B． (1,4) (1,5) C． D．

【答案】A

【解析】函数 $y = \log _ { a } ( 2 x - 3 ) + 4$ ， 令 $2 x - 3 = 1$ 解得 x=2 此时 y=log.1+4=4 所以函数

$y = \mathrm { l o g } _ { a } ( 2 x - 3 ) + 4$ 恒过定点 (2,4) 故选：A

3．（2022·浙江丽水·高一开学考试）已知函 数 （ 且 ）的图象过定点 ，正数 $m$ 、 $n$ 满足 $m + n = s t$ ，则（ ）

A． $m ^ { 2 } + n ^ { 2 } \leq 3 2 \qquad \mathrm { C . } _ { m n \geq 1 6 }$ D． ${ \frac { 1 } { m } } + { \frac { 1 } { n } } \geq { \frac { 1 } { 2 } }$

【答案】D

【解析】因为 $f \left( x \right) = \log _ { a } \left( x - 1 \right) + 4$ $\cdot a > 0  _ { \mathrm { \tiny ~ H } } a \neq 1 )$ 令 ， 解得 ， 所以 $f \left( 2 \right) { = } { \log } _ { a } 1 { + } 4 { = } 4$ ，即函数过定点 ， 所以 $m + n = 8$ ， 故 $\cdot$ 错误；  
因为 $m > 0$ 、 $n > 0$ ， $m ^ { 2 } + n ^ { 2 } \geq \frac { ( m + n ) ^ { 2 } } { 2 } = 3 2$ ， 当且仅当 $m = n = 4$ 时取等号， $m n \leq \left( { \frac { m + n } { 2 } } \right) ^ { 2 } = 1 6$ ， 当且仅当m=n=4 时取等号， $\frac { 1 } { m } + \frac { 1 } { n } = \frac { 1 } { 8 } \left( \frac { 1 } { m } + \frac { 1 } { n } \right) \left( m + n \right) = \frac { 1 } { 8 } \left( 2 + \frac { n } { m } + \frac { m } { n } \right) \geq \frac { 1 } { 8 } \left[ 2 + 2 \sqrt { \frac { n } { m } \cdot \frac { m } { n } } \right] = \frac { 1 } { 2 } \left( 2 + \frac { n } { m } + \frac { 1 } { n } \right) \geq 0 .$ 当且仅当 $m = n = 4$ 时取等号.故选：D

# 考点六 反函数

【例 6】（2021·山西省长治市第二中学校高一阶段练习）若函数 的 反函数的图象过点 (1,3) ， 则 （ ）

A． B．1 C．2 D．3

【答案】B

【解析】依题意，函数 $f \left( x \right) = \log _ { a } x \left( a > 0 , a \neq 1 \right)$ 的反函数是 $y = a ^ { x }$ 即函数 $y = a ^ { x }$ 的图象过点 ，  
$_ { | | } a = 3 , \quad f ( x ) = \log _ { 3 } x$ 于是得 $f \left( \log _ { 2 } 8 \right) { = } \log _ { 3 } ( \log _ { 2 } 8 ) { = } \log _ { 3 } 3 { = } 1$   
所以 $f \left( \log _ { 2 } 8 \right) = 1$ .故选：B

1．（2021·江苏·高一专题练习）与函数 $y = \left( { \frac { 1 } { 4 } } \right)$ 的图象关于直线 $y = x$ 对称的函数是（ ）

y=4x y =4\* A． B． C． $y = \log _ { { \frac { 1 } { 4 } } } x$ D $y = \log _ { 4 } x$

【答案】C

【解析】因为函数 $y = a ^ { x } \mathop { \lrcorner } \mathop { y } = \log _ { a } x$ （ $a > 0$ 且 $a \neq 1$ ）互为反函数，且这两个函数的图象关于直线 $y = x$ 对$y = \left( { \frac { 1 } { 4 } } \right) ^ { \cdot }$ X称，因此，与函数 的图象关于直线 $y = x$ 对称的函数是 $y = \log _ { { \frac { 1 } { 4 } } } x$ .故选：C.

2．（2022 湖南）函数 y=3x+1 的反函数的表达式为（ ）

y=log x-1 A． B．

【答案】B

$y = 3 ^ { x + 1 }$ $x = \log _ { 3 } y - 1$ y=x y=logx-1  
【解析】由 得 ， 令 得 ，$y = 3 ^ { x + 1 }$ $y = \log _ { 3 } x - 1$   
所以函数 的反函数的表达式为 故选：B

3．（2022 湖南）函数 $y = a ^ { x }$ （ $a > 0$ ，且 $a \neq 1$ ）的反函数的图象过点 $\left( { \sqrt { a } } , a \right)$ ，则 $a$ 的值为（ ）

B． 1 A．2 C．2 或 D．3

【答案】B

【解析】法一：函数 $\scriptstyle y = a ^ { x } \quad a > 0$ ，且 $a \neq 1$ ）的反函数为 （ ，且 ）故 的图象过点 ，则

法二：∵函数 $y = a ^ { x }$ （ $a > 0$ ，且 $a \neq 1$ ）的反函数的图象过点 ，  
∴函数 $\scriptstyle y = a ^ { x } ( a > 0$ ，且 $a \neq 1$ ）的图象过点 $\left( { \boldsymbol { a } } , { \sqrt { a } } \right)$ ，  
$a ^ { a } = { \sqrt { a } } = a ^ { \frac { 1 } { 2 } }$ ，即 $a = \frac { 1 } { 2 }$   
故选：B

# 考点七 对数函数的图像

【例 7】（2022·全国·高一课时练习）函数 $y = \left| \lg ( x + 1 ) \right|$ 的图像是（ ）

![](images/85203792074c7be55fd2281eb58a6c498a269aa96a4b204b6c7f006205e61626.jpg)

【答案】A

【解析】由于函数 $y = \mathrm { l g } ( x + 1 )$ 的图象可由函数 $y = \lg x$ 的图象左移一个单位而得到，函数 $y = \lg x$ 的图象与X (1,0)  
轴的交点是   
故函数 $y = \mathrm { l g } ( x + 1 )$ 的图象与 $\boldsymbol { \chi }$ 轴的交点是 $( 0 , 0 )$ ，即函数 $y = \left| \lg ( x + 1 ) \right|$ 的图象与 $\boldsymbol { \chi }$ 轴的公共点是 $( 0 , 0 )$ ，显然四个选项只有A 选项满足.  
故选：A.

# 【一隅三反】

1．（2023·全国·高三专题练习）在同一直角坐标系中，函数 $\scriptstyle y = \log _ { a } \left( - \chi \right)$ ， ，且 的图象可能是（ ）

![](images/47707ba97e456019caa2db35fae4ae2e3c469e369c7c76fd807494a65c7f7ffe.jpg)

【答案】C

【解析】因为函数 $y = \log _ { a } \left( - x \right)$ 的图象与函数 $y = \log _ { a } X$ 的图象关于 轴对称，  
所以函数 $y = \log _ { a } \left( - x \right)$ 的图象恒过定点 $( - 1 , 0 )$ ， 故选项 A、B 错误；  
当 $a > 1$ 时，函数 $y = \log _ { a } \chi ⨏ _ { \mathscr { L } } ( 0 , + \infty )$ 上单调递增，所以函数 $y = \log _ { a } \left( - x \right) _ { \# } \left( - \infty , 0 \right)$ 上单调递减，又 $y = \frac { a - 1 } { x } ( a > 1 )$ 在 $\left( - \infty , 0 \right)$ 和 $\left( 0 , + \infty \right)$ 上单调递减，故选项 D 错误，选项 C 正确.  
故选：C.

2．（2021·浙江·玉环中学高一阶段练习）函数 $f ( x ) = \left( { \frac { 1 } { 4 } } \right) ^ { 2 }$ 与 $g ( x ) = - \log _ { 4 } x$ 的大致图像是（ ）

![](images/e6d6aa85784b1dd90a95d88953f7f88c70dc1b11ebf77aac5d7aafe6429a0694.jpg)

C $x$ D $x$ $O$ $O$

【答案】A

【解析】因为 $f ( x ) = \left( { \frac { 1 } { 4 } } \right) ^ { 1 }$ 在定义域 $\mathrm { R }$ 上单调递减，  
又 $g ( x ) = - \log _ { 4 } x = \log _ { 4 ^ { ^ { \cdot 1 } } } x = \log _ { \frac { 1 } { 4 } } x$ ， ， 所以 $g ( x )$ 在定义域 $\left( 0 , + \infty \right)$ 上单调递减，故符合条件的只有A；  
故选：A  
3．（2022·重庆市）若函数 $f ( x ) = ( k - 1 ) a ^ { x } - a ^ { - x } ( a > 0 _ { }$ 在 上既是奇函数，又是减函数，则  
g(x)=log.(x+k)的图象是（ ）

![](images/39895d6b904b913c54f675daef6c0f017d615158555ab69359dddbd951087ec2.jpg)

【答案】A

【解析】由于 是 上的奇函数，所以 $f \left( 0 \right) = k - 1 - 1 = 0 , k = 2$ ，$f \left( x \right) = a ^ { x } - \frac { 1 } { a ^ { x } }$ 为减函数，所以 ，  
所以 $g \left( x \right) = \log _ { a } \left( x + 2 \right) , x > - 2 , \quad g \left( x \right) _ { \textstyle \mathcal { X } ) } \left( - 2 , + \infty \right) \operatorname { \mathbb { \Sigma } } _  \textstyle \operatorname { \cdot } \operatorname { \cdot } \operatorname { \cdot } \operatorname { \cdot } \operatorname { \cdot } \operatorname { \cdot } \operatorname { \cdot } \operatorname { \cdot } \operatorname { \mathrm { i } } ^ { \zeta } \operatorname { \mathbb { \Sigma } } \operatorname { \mathbb { \Sigma } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \Sigma } \operatorname { \overline { \operatorname { \Sigma } } } \operatorname { \overline { \operatorname \Sigma } } \operatorname { \Sigma } \operatorname { \Sigma }$   
所以BCD 选项错误，A 选项正确.故选：A

# 考点八 对数函数的综合运用

【例 8】（2021·全国·高一课时练习）已知函数 $f \left( x \right) = \log _ { \frac { 1 } { 2 } } \left( x ^ { 2 } - m x - m \right) .$

f(x）  
(1)若 ， 求函数 的定义域  
(2)若函数 $f \left( x \right)$ 的值域为 $\mathrm { R }$ ，求实数 $m$ 的取值范围

(3)若函数 $f \left( x \right)$ 在区间 $\left( \mathbf { - \infty } , 1 \mathbf { - \infty } { \sqrt { 3 } } \right)$ 上是增函数，求实数 $m$ 的取值范围．

【答案】(1) $\big ( \mathbf { - \infty } , \frac { 1 \mathbf { - } \sqrt { 5 } } { 2 } \big ) \cup ( \frac { 1 + \sqrt { 5 } } { 2 } , \mathbf { + \infty } ) _ { \qquad ; }$   
(2) $m \in ( - \infty , - 4 ] \cup [ 0 , + \infty )$   
(3) $^ { 2 \geq m \geq 2 ( 1 - \sqrt { 3 } ) } .$

【解析】（1）由题设， $\chi ^ { 2 } - \chi - 1 > 0$ ，则 $x > { \frac { 1 + { \sqrt { 5 } } } { 2 } }$ 或 $x < \frac { 1 - \sqrt { 5 } } { 2 }$ 所以函数定义域为$( - \infty , \frac { 1 - \sqrt { 5 } } { 2 } ) \cup ( \frac { 1 + \sqrt { 5 } } { 2 } , + \infty )$

（2）由函数 $f \left( x \right)$ 的值域为R，则 $( 0 , + \infty ) \quad y = x ^ { 2 } - m x - m$ 值域的子集，所以 $\Delta = m ^ { 2 } + 4 m \geq 0$ ， 即$m \in ( - \infty , - 4 ] \cup [ 0 , + \infty )$

（3）由 在 $( - \infty , \frac { m } { 2 } )$ 上递减，在 $( { \frac { m } { 2 } } , + \infty )$ 上递增，而 $y = \log _ { \frac { 1 } { 2 } } t$ 在定义域上递减，  
所以 $f ( x )$ 在 $( - \infty , \frac { m } { 2 } )$ 上递增，在 $( { \frac { m } { 2 } } , + \infty )$ 上递减，m ≥1-√2  
$\begin{array} { r l } { _ { f } \left( _ { x } \right) } & { { } \left( _ { - \infty , 1 - } \sqrt { 3 } \right) } \end{array}$ 上是增函数，故 $\left\lfloor t \left( 1 - { \sqrt { 3 } } \right) \geq 0 \right.$ ， 可得2 ≥m ≥2(1- √)

# 【一隅三反】

1．（2022·贵州毕节·高一期末）已知函数 $f ( x ) = \log _ { 3 } ( x + a )$ 的定义域为 ， 且 $f ( x )$ 的图象经过点

(1)求函数 $f ( x )$ 的解析式；$g ( x ) = \left( { \frac { 1 } { 4 } } \right) ^ { x - 2 } - \ f ( x )$   
(2)求函数 的最大值；$h ( x ) = f \left( x ^ { 2 } \right) - \ f ( x - 2 )$   
(3)求函数 的值域  
【答案】 ${ \bf \Phi } _ { ( 1 ) } \left. \begin{array} { c } { { f ( x ) = \log _ { 3 } ( x + 2 ) } } \\ { { } } \end{array} \right. _ { ; }$   
(2)3；  
(3) $\left[ \log _ { 3 } { \frac { 1 1 } { 3 } } , \log _ { 3 } { \frac { 9 } { 2 } } \right]$   
【解析】 $\cdot$ ∵ 的图象经过点 ， $\scriptstyle \mathtt { p g } _ { 3 } ( 7 + a ) = 2 \qquad 7 + a = 9 \qquad a = 2 \qquad f ( x ) = \log _ { 3 } ( x + 2 )$   
(2) $g ( x ) = \left( \frac { 1 } { 4 } \right) ^ { x - 2 } \cdot \log _ { 3 } ( x + 2 )$ 定义域为 ，  
∴函数 $y = \left( { \frac { 1 } { 4 } } \right) ^ { x - }$ 在 上单调递减， $f ( x ) = \log _ { 3 } ( x + 2 )$ 在 上单调递增，  
∴函数 $g ( x )$ 在 上单调递减，∴函数 $g ( x )$ 的最大值为 $g ( 1 ) = \left( \frac { 1 } { 4 } \right) ^ { 1 - 2 } \cdot \log _ { 3 } ( 1 + 2 ) = 4 \cdot 1 = 3 \cdot$   
$h ( x ) = f \left( x ^ { 2 } \right) - \ f \left( x - 2 \right) = \log _ { 3 } \left( x ^ { 2 } + 2 \right) - \log _ { 3 } x = \log _ { 3 } { \frac { x ^ { 2 } + 2 } { x } } = \log _ { 3 } \left( x + { \frac { 2 } { x } } \right)$   
∵函数 $f ( x )$ 的定义域为 ， $\left\{ \begin{array} { l l } { 1 \leq x ^ { 2 } \leq 1 6 , } \\ { 1 \leq x - 2 \leq 1 6 } \end{array} \right.$ ，解得 $3 \le x \le 4$ ，  
∴函数 $h ( x )$ 的定义域为 ，  
∵对勾函数 $\scriptstyle u = x + { \frac { 2 } { x } }$ 在 上单调递增，而函数 $y = \log _ { 3 } u$ 是增函数，  
∴函数 $h ( x )$ 在 $[ 3 , 4 ]$ 上单调递增，

$\therefore h ( x ) _ { \mathrm { m i n } } = h ( 3 ) = \mathrm { l o g } _ { 3 } \frac { 1 1 } { 3 } , ~ h ( x ) _ { \mathrm { m a x } } = h ( 4 ) = \mathrm { l o g } _ { 3 } \frac { 9 } { 2 } ,$ ∴函数 $h ( x )$ 的值域为 $\left\lceil \log _ { 3 } \frac { 1 1 } { 3 } , \log _ { 3 } \frac { 9 } { 2 } \right\rceil .$

2．（2022·山西·朔州市朔城区第一中学校高一开学考试）设 （），且 $f \left( 2 \right) { = } \log _ { 2 } 3$

f(x）(1)求实数 的值及函数 的定义域；(2)求函数 $f \left( x \right)$ 在区间 $\left[ 0 , { \frac { 3 } { 2 } } \right]$ 上的最大值.f(x) (- 1,3)【答案】 $\cdot$ ； 的定义域为(2) 上的最大值为 2.

【解析】(1)  
$f \left( x \right) = \log _ { \alpha } \left( 1 + x \right) + \log _ { \alpha } \left( 3 - x \right) \quad a > 0 \quad a \neq 1 \qquad f \left( 2 \right) = \log _ { 2 } 3 $   
所以 $f \left( 2 \right) = \log _ { a } 3 + \log _ { a } 1 = \log _ { 2 } 3$ ，解得： $a { = } 2$ .$\left\lceil 1 + x > 0 \right.$   
所以 $f \left( x \right) = \log _ { 2 } \left( 1 + x \right) + \log _ { 2 } \left( 3 - x \right)$ 的定义域需满足： $\left\lfloor 3 - \right. x > 0$ ，解得： $1 < x < 3$ ，f(x) (-1,3)  
即函数 的定义域为  
(2)  
$f \left( x \right) = \log _ { 2 } \left( 1 + x \right) + \log _ { 2 } \left( 3 - x \right) = \log _ { 2 } \left( - x ^ { 2 } + 2 x + 3 \right) = \log _ { 2 } \left[ - \left( x - 1 \right) ^ { 2 } + 4 \right] .$   
任取 $0 \leq x _ { 1 } < x _ { 2 } \leq 1$ ， ${ \underset { \smile } { \circleddash } } g ^ { ( \chi ) } = - \left( x - 1 \right) ^ { 2 } + 4$ ， 则 ，所以 $f \left( x _ { _ 1 } \right) < f \left( x _ { _ 2 } \right)$ ， 所以 $f \left( x \right) \quad \left[ 0 , 1 \right]$ 上单增；  
任取 $1 \leq x _ { 1 } < x _ { 2 } \leq \frac { 3 } { 2 }$ 1 令 $g \left( x \right) = - \left( x - 1 \right) ^ { 2 } + 4$ ，则 $g ( x _ { \scriptscriptstyle 1 } ) > g ( x _ { \scriptscriptstyle 2 } )$ ，所以 $f \left( x _ { \scriptscriptstyle 1 } \right) > f \left( x _ { \scriptscriptstyle 2 } \right)$ ，所以 $f \left( x \right) _ { \mathcal { \left/ { \ddag } \right} } \left[ 1 , \frac { 3 } { 2 } \right]$ 上单减.  
所以 $f \left( x \right) \quad \left[ 0 , 1 \right]$ 上单增，在 $\left[ 1 , { \frac { 3 } { 2 } } \right]$ 上单减.  
所以 在 0, ]上的最大值为 $f \left( 1 \right) = \log _ { 2 } \left( 1 { \mathrm { ~ } } + 1 \right) + \log _ { 2 } \left( 3 - 1 \right) = 2 .$