---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 15
source_file: L1-8-指数函数.md
title: L1-8-指数函数
type: problem_type
---

第8讲 预习篇

# 指数函 数

一本一讲，共26讲（建议5个月学习）

6个知识点+8个题型+22个视频

每周一讲（建议学习时间90分钟）

9 =2×2-e°+（8）² 210939-eln1+382二 4-1+4=7

# 视频内容研发团队

学而思优秀老师

学而思优秀老师和一线高级教师联合创作本书试题，并精心录制讲解视频学而思图书APP扫码即可观看

![](images/0735cf6474e66f2dff9dad4bf058b1d11d82a2e2412bc9e246dc7406cc669824.jpg)

# 傅博宇 老师

毕业于北京大学元培学院  
网校和北大数院高考数学研究联合课题组成员；  
网校高中创新产品部负责人；  
荣获学而思网校“桃李满天下奖”“出类拔萃奖”等；腾讯网中国好老师；  
青少年教育导师认证；  
科学家长观体系的创立者

# 王侃老师

![](images/796a9d4866080ebe44798ce7d99d4c341b98c4a63509b6a412b9414d7ff7985e.jpg)

毕业于北京大学数学系  
学而思网校高中数学教研奠基人；  
学而思网校高中数学S级教师；  
荣获学而思网校“突出贡献奖” “桃李天下奖”等；擅长总结题型特点，提炼思想方法；  
擅长分层教学，因材施教

# 付恒岩 老师

![](images/0005059fc2c3320f63fd5dfc70a469244570cf5e9821c7137afa4306b757e2a3.jpg)

毕业于大连理工大学  
网校高中部理科主讲岗后培训师；  
2020年荣获学而思网校最具魅力奖；  
2019年、2020年荣获学而思网校诲人不倦奖；2020年荣获学而思网校高考优秀评卷人；  
2021年担任新浪教育高考数学直播解析特邀嘉宾；“停课不停学”公益课高中数学主讲老师

# 武洪姣 老师

![](images/0b5ba4f705d06413d9c9f90ab5331f2a9b7dbdd0f63a63117cfe7378c72cd08e.jpg)

14年线上线下教学经验；  
学而思网校高中理科教研负责人；  
学而思高中数学特级教师;  
在教学的过程中擅长归纳题型，方法和技巧;  
在高中数学模块中最擅长讲解圆锥曲线和导数；  
无论你是从小数学不好，还是数学一直拔尖，都可以在武老师的课堂上收获很多

#

# 指数函数

一本一讲，共26讲（建议5个月学习）

6个知识点 $^ { + 8 }$ 个题型 $+ 2 2$ 个视频每周一讲（建议学习时间90分钟）

210939-eln+38  
= 2×2-e°+(8§）²  
二 4-1+4  
=7

# 预习篇

第1讲集合的概念与表示第 $^ 2$ 讲集合的关系与运算第3讲解不等式  
第4讲函数的概念与三要素第5讲函数的单调性（一）第6讲函数的奇偶性（一）第 $7$ 讲指数幂运算与幂函数第8讲指数函数  
第9讲对数运算  
第10讲对数函数

<html><body><table><tr><td>模块1</td><td>指数函数的概念</td><td>2</td></tr><tr><td>模块2</td><td>指数函数的图象与性质</td><td>5</td></tr><tr><td>模块3</td><td>指数幂的大小比较</td><td>14</td></tr></table></body></html>

# 提升篇

第11讲集合重难点专题第12讲常用逻辑用语第13讲基本不等式第14讲函数三要素专题第15讲函数的单调性（二）第16讲函数的奇偶性（二）第17讲抽象函数第18讲指数幂运算与指数函数第19讲对数运算与对数函数第20讲函数与方程第21讲恒成立与存在性问题第22讲三角函数基本概念与诱导公式第23讲三角函数的图象与性质第24讲三角公式的应用技巧第25讲三角恒等变换重点题型第26讲正弦型函数的图象与性质参考答案

# 指数函数

# 直击课堂

<html><body><table><tr><td>知识模块</td><td>知识点</td><td>对应例题</td><td>星标统计</td></tr><tr><td rowspan="2">指数函数的概念</td><td rowspan="2">指数函数的概念</td><td>例1</td><td rowspan="9">★2题题</td></tr><tr><td>例2</td></tr><tr><td rowspan="3">数</td><td rowspan="2">指数函数的图象</td><td>例3</td></tr><tr><td>例4</td></tr><tr><td>例5 指数函数的性质 例6</td></tr><tr><td rowspan="3">指数幂的大小比较</td><td>同底的幂比大小</td><td rowspan="3">例7</td></tr><tr><td>同指数的幂比大小</td><td></td></tr><tr><td>既不同底也不同 指数的幂比大小</td><td>例8</td></tr></table></body></html>

# 学习目标

$\textcircled{1}$ 理解指数函数的定义、图象与性质.

$\textcircled { 2 }$ 掌握指数函数的定义域、值域的求法.

$\textcircled { 6 }$ 能利用指数函数的性质解决相关问题.

$\textcircled{4}$ 能利用单调性比较指数幂的大小.

# 模块1指数函数的概念

# APP扫码观看本模块讲解视频

1知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# 指数函数的概念

一般地，函数 $\gamma = a ^ { x } ( a > 0$ 且 $a \neq 1$ )叫做指数函数,其中 $x$ 是自变量，函数的定义域是R.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例1

# （1）★

下列函数一定是指数函数的是(

A. $y = 2 ^ { x + 1 }$ B. $y = x ^ { 3 }$ C. $y = 3 \cdot 2 ^ { x }$ D. $y = 3 \AA ^ { - x }$

# 学习笔记

# （2）★★

下列函数中指数函数的个数是(

$$
\textcircled { 1 } y = 2 ^ { x } ; \textcircled { 2 } y = x ^ { 2 } ; \textcircled { 3 } y = 3 ^ { x + 3 } ; \textcircled { 4 } y = x ^ { x } ; \textcircled { 5 } y = ( 6 a - 3 ) ^ { x } \bigg ( a > \frac { 1 } { 2 } ,
$$

A.0 B.1 C.2 D.3

# 学习笔记

# 变式1★

$y = x ^ { 3 } , y = \left( { \frac { 1 } { 2 } } \right) ^ { x } , y = 4 x ^ { 2 } , y = x ^ { 2 } + 1 , y = \left( x - 1 \right) ^ { 2 } , y = x , y = a ^ { x } ( a > 1 )$ ，上述函数是指数函数的个数是（）

A.1 B.2 C.3 D.4

# 学习笔记

# 例2★★

函数 $f ( x ) = \left( a ^ { 2 } - 3 a + 3 \right) a ^ { x }$ 是指数函数，则 $a$ 的值为

# 学习笔记

# 变式2★★

若函数 $f ( x ) = \left( a ^ { 2 } - 2 a - 2 \right) a ^ { x }$ 是指数函数，则 $^ { a }$ 的值是(

A.-1 B.3 C.3或-1 D.2

# 学习笔记

# 模块2指数函数的图象与性质

# APP 扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# 指数函数的图象与性质

(1)指数函数 $\gamma = a ^ { x } ( a > 0 , a \neq 1 )$ 的图象

<html><body><table><tr><td>函数</td><td colspan="2">y =a(a&gt;0,a≠1)</td></tr><tr><td rowspan="2">图象</td><td>0&lt;a&lt;1</td><td>a&gt;1</td></tr><tr><td></td><td>a (1,a) 1 1</td></tr><tr><td>0</td><td>X 0</td><td>1 x</td></tr></table></body></html>

(2)指数函数 $\gamma = a ^ { x } ( a > 0 , a \neq 1 )$ 的性质

<html><body><table><tr><td rowspan="7">性质</td><td>定义域</td><td>R</td></tr><tr><td>值域</td><td>（0，+8）</td></tr><tr><td>奇偶性</td><td>非奇非偶</td></tr><tr><td>单调性</td><td>α &gt;1时,增函数;0&lt;α&lt;1时,减函数</td></tr><tr><td>函数值 变化情况</td><td>(1)a² &gt;0 (2)a°=1,a¹ =a (3)a&gt;1时 ①当𝑥&gt;0时,a²&gt;1 ②当𝑥&lt;0时,0&lt;a²&lt;1 (4)0&lt;a&lt;1时</td></tr></table></body></html>

(3)底数对指数函数图象的影响

<html><body><table><tr><td>增减情况</td><td>同增</td><td>同减</td></tr><tr><td>底的关系</td><td>a&gt;b&gt;1</td><td>1&gt;a&gt;b&gt;0</td></tr><tr><td>图象</td><td>a /bx V 0</td><td>ax↑y x</td></tr><tr><td>性质</td><td>①若𝑥&gt;0,则a&gt;b&gt;1 ②若x&lt;0,则1&gt;b&gt;a²&gt;0</td><td>①若x&gt;0,则1&gt;a&gt;b&gt;0 ②若𝑥&lt;0,则b&gt;a²&gt;1</td></tr></table></body></html>

# 思考探究

1.画出函数 $f ( x ) = 2 ^ { x }$ 与 $\varrho ( { x } ) = 3 ^ { x }$ 的图象，你发现了什么？2.以上两个函数都满足 $a > 1$ ，若 $0 < a < 1$ ,则图象又会是什么样？请以函数 $h ( x ) = \left( { \frac { 1 } { 2 } } \right) ^ { x }$ 为例画出图象，并与函数 $f ( x ) = 2 ^ { x }$ 画在同一坐标系中进行对比.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例3★★

函数 $f ( x ) = a ^ { x + 1 } - 1$ （ $a > 0$ ，且 $a \neq 1$ )图象恒过的定点构成的集合是

A $\left\{ \begin{array} { l l } { ( { \bf \nabla } - 1 , { \bf \nabla } - 1 ) } \end{array} \right\}$ B.{(0,1)} C.{（-1,0）} D.Q

# 学习笔记

# 变式3

# （1）★★

函数 $\gamma = a ^ { x - 1 } \left( a > 0 \right)$ ，且 $a \neq 1$ )的图象必经过点(

$\left( { \frac { 1 } { 4 } } , 1 \right)$ B. (0,1) C. (1,1) D.(2,1)

# 学习笔记

# （2）★★

当 $a > 0$ 且 $a \neq 1$ 时,函数 $f ( x ) = a ^ { x - 1 } + 3$ 的图象一定经过( ）

A. (4,1) B. (1,4) C. (1,3) D.(-1,3)

# 学习笔记

# 例4★★★

函数 $\gamma = a ^ { x } - a ( a > 0 , a \neq 1 )$ 的图象可能是(

![](images/9ea5bb2708494f7e46bef477f376881ade4424012daa11e644548dcfea474551.jpg)

# 学习笔记

# 变式4★★★★

函数 $\gamma = a ^ { x } - \frac { 1 } { a } ( a > 0 , a \neq 1 )$ 的图象可能是(

![](images/526926f05d034a1c4c9a2553371b2202264fdd8393a2da621bb472faddbba441.jpg)

![](images/863747faa7f2c4f5e6483b9c1c308aba82c9a71a238dbba035fbb07257c776ba.jpg)

# 学习笔记

# 例5★★★

函数 $\gamma = a ^ { x } ( a > 0$ 且 $a \neq 1$ )与函数 $y = \left( a - 1 \right) x ^ { 2 } - 2 x - 1$ 在同一个坐标系内的图象可能是（）

![](images/59019bb812e5c2d158cec97c1087891534409caf16d7c34a3b94ab9944670010.jpg)

![](images/4243bbd80644dc69ea50054aaca6e1af427db4f7a41fb648c9ee06312c874afc.jpg)

# 学习笔记

# 变式5★★★

若 $a > 1$ ,则函数 $\boldsymbol { y } ^ { \boldsymbol { = } } \boldsymbol { a } ^ { \boldsymbol { x } }$ 与 $\ y = ( \ 1 \ - a ) x ^ { 2 }$ 的图象可能是下列四个选项中的（ ）

![](images/2e85ef45738338d0c678fdd825e4989c7481b2e30da0f8f5fe9985584aaaead6.jpg)

# 学习笔记

# 例6★★★

图中的曲线 $C _ { 1 } , C _ { 2 } , C _ { 3 } , C _ { 4 }$ 是指数函数 $\boldsymbol { y } = \boldsymbol { a } ^ { x }$ 的图象,而 $a \in \left\{ { \frac { \sqrt { 2 } } { 3 } } , { \frac { 1 } { 3 } } , \sqrt { 5 } , \pi \right\}$ 则图象 $C _ { 1 } , C _ { 2 } , C _ { 3 } , C _ { 4 }$ 对应的函数的底数依次是 ，

![](images/eee99c2638b58a10682cde55f75d4f11faedaf150afad2714e83b64f88d90151.jpg)

![](images/82d1c82fe0a32d5bffe535d73000ae78eac7a2425d47622d19e9282893c6cc09.jpg)

# 变式6★★

已知 $y _ { 1 } = \left( { \frac { 1 } { 3 } } \right) ^ { x } , y _ { 2 } = 3 ^ { x } , y _ { 3 } = 1 0 \ ^ { - x } , y _ { 4 } = 1 0 ^ { x }$ ,则在同一坐标系内,它们的图象为（ ）

![](images/e389d6eac66f29731f801a4a25fd0c7335752cc637412306ab2277873c271ee5.jpg)

# 例7★★

已知函数 $g ( x ) = { \bigg ( } { \frac { 1 } { 3 } } { \bigg ) } ^ { x }$ ，

$\textcircled{1}$ 当 $x \in \left[ \begin{array} { l } { - 2 , 1 } \end{array} \right)$ 时,函数的值域为

$\textcircled{2}$ 当 $x \in { \big ( } - \infty , 1 { \big ] }$ 时,函数的值域为

$\textcircled{3}$ 当陽 $\displaystyle { \boldsymbol { g } } ( { \boldsymbol { x } } ) \in \left( { \frac { 1 } { 9 } } , + \infty \right)$ 时，函数的定义域为

# 学习笔记

# 变式7★★

函数 $\gamma = \left( \frac { 1 } { 2 } \right) ^ { x } \left( x \geqslant 8 \right)$ 的值域是(

A.R B $\left( ^ { 0 } , { \frac { 1 } { 2 5 6 } } \right]$ $\Big ( - \infty , \frac { 1 } { 2 5 6 } \Big ]$ Dä $\left[ \frac { 1 } { 2 5 6 } , + \infty \right]$

# 学习笔记

# 模块3指数幂的大小比较

# APP扫码观看本模块讲解视频

1知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# 幂的大小比较有三类题型

（1)同底的幂比大小；  
(2)同指数的幂比大小;  
(3)既不同底也不同指数但容易找到中间值的幂比大小.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例8★★

比较下列各题中两个值的大小：

$\textcircled { 1 } 3 ^ { - 0 . 5 } , 3 ^ { - 0 . 6 } ; \textcircled { \scriptsize { 2 } } \left( \frac { 1 } { 5 } \right) ^ { - 0 . 5 } , \left( \frac { 1 } { 5 } \right) ^ { - 0 . 6 } ; \textcircled { \scriptsize { 3 } } 3 ^ { - 1 . 2 } , 4 ^ { - 1 . 2 } ; \textcircled { \scriptsize { 4 } } 0 . 7 ^ { 0 . 3 } , 0 . 8 ^ { 0 . 3 } ;$ ⑤0.7-0.1,7-07;⑥0.5,0.5.

# 学习笔记

# 变式8

# （1）★★

设 $x = 0 . 2 ^ { 0 . 3 } , y = 0 . 3 ^ { 0 . 2 } , z = 0 . 3 ^ { 0 . 3 }$ ，则 $x , y , z$ 的大小关系为（

A $x < z < y$ （204 B. $y < x < z$ C. $y < z < x$ D. z<y<x

# 学习笔记

# (2）★★

已知 $a = 1 . 6 ^ { ^ { 0 . 3 } } , b = 1 . 6 ^ { ^ { 0 . 8 } } , c = 0 . 7 ^ { ^ { 0 . 8 } }$ ，则（ ）

A. $c < a < b$ B. $a < b < c$ C. $b > c > a$ D.a>b>c

# 学习笔记

# 学习总结

![](images/bd97752e6dd802e8cf766867c3c1ec64c2dae80e9181f4060e68f06264e53b01.jpg)

# 提升篇你会遇见

（预习篇·P13,变式7)函数 $\gamma = \left( { \frac { 1 } { 2 } } \right) ^ { x } \left( x \geqslant 8 \right)$ 的值域是(

A.R B $\left( ^ { 0 } , { \frac { 1 } { 2 5 6 } } \right]$ $\left( \mathbf { \varepsilon } - \infty ~ , \frac { 1 } { 2 5 6 } \right]$ D $\left[ \frac { 1 } { 2 5 6 } , + \infty \right]$

（提升篇）函数 $f ( x ) = 4 ^ { - x + 1 } + \left( { \frac { 1 } { 2 } } \right) ^ { x } + 1 \left( x \geqslant 1 \right)$ 的值域为

【点石成金】以上两题都考查了指数函数求值域问题.对比发现，预习篇题目比较简单，直接画出指数函数图象即可求值域.但是提升篇中题目明显相对比较复杂，需要我们对原函数进行变形，然后构造二次函数进行求解.

# 学而思秘籍系列图书数学

# 思维培养

![](images/b1eca8d0929d581f51b4766d69561c6f4185b27991400fd4150b8ed7883abf55.jpg)

# 小学秘籍系列

学而思积淀近20年教研经验，培养受益一生的能力。

# 思维提升

![](images/8128cb9d6a5942507b890d0fe95b6213363ba2fc90881fb24fa65ed4d412e33b.jpg)

# 初中秘籍系列

全面覆盖初中基础知识和重难点，帮助学生夯实基础，拓展认知。

# 思维突破

![](images/214a48edbecb7ee2e81847a6be3b8ee006af95f16996f592ef1a87e0252e8b92.jpg)

# 高中秘籍系列

全面覆盖高中基础知识和重难点，帮助学生提升能力，突破思维。

# 学而思秘籍系列图书|语文

# 提升素养

![](images/bfa9e62fe46315d10cee048f4c94e10904d9f7d730d19ff0716c8a81eab5863f.jpg)

# 小学秘籍系列

5大模块+2条主线，能力与素养双向提升。

# 能力训练

![](images/19111f334479f8eebe5766f96ee33009553a708e86dcf28bfffc4c0d5ef9867e.jpg)

# 初中秘籍系列

融合课改四大核心素养，培养爱阅读、 善写作、勤思考、会学习的学生。

# 创新体系|真题研习

![](images/5dbedfd905a50056fb3d62ef27090c6136ac458bf9ae988e0b96874fcf4b71bf.jpg)

# 思维创新大通关 数学

攻克数学思维难题，通向理想中学。

# 大家一起来“升级

# 参与方式

您在使用本书时，如有任何疑问或对图书有任何建议，请扫码进行反馈，并查看反馈采纳结果。

![](images/3b1e95aae04af2e5e9edd7435030bf823b5cc0c5c3f7c9a75a291b3d3665cc79.jpg)

# 奖励

您的反馈一经采纳，我们将会送出总价值35元的图书抵扣券（相同内容的反馈，依据反馈时间，奖励前三位）。请扫码关注公众号，并在对话框中发送反馈时填写的手机号，领取抵扣券。

![](images/116980b822aa1c265de7064a643a5c6bbe8d83dfdaba4dc59ed5cbe76d607fac.jpg)

# 合理规划学习时间

先自己定一个目标，即制定半年学习规划。再将目标细化到每一周，每周学习一本（平均5个考点）。3 配套课堂巩固的练习，让学习更有效！

![](images/0e1b8f2c25ba4894a705f243cad545a2c0f168c3183d839b3cf4d91cd20904d3.jpg)

![](images/82815d2cce8fbfdd4cf54f35ccfa6cf06b35979e02d2f91b6958af8039d97d15.jpg)  
·共6级·每级17-26讲