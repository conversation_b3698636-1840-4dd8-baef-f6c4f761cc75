---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 17
source_file: L1-6-函数的奇偶性（一）.md
title: L1-6-函数的奇偶性（一）
type: problem_type
---

第6讲 预习篇

# 函数的奇偶性（一）

一本一讲，共26讲（建议5个月学习）

7个知识点+8个题型+27个视频

每周一讲（建议学习时间90分钟）

![](images/187e1c06814d46159d4527862c6b2b87167b188a25622f6a57c474b144576411.jpg)

# 视频内容研发团队

学而思优秀老师

学而思优秀老师和一线高级教师联合创作本书试题，并精心录制讲解视频学而思图书APP扫码即可观看

![](images/932bf4a6db547e4e71a7739bf16563111937a6e347c9681c84c8460ca0967c1c.jpg)

# 傅博宇 老师

毕业于北京大学元培学院  
网校和北大数院高考数学研究联合课题组成员；  
网校高中创新产品部负责人；  
荣获学而思网校“桃李满天下奖”“出类拔萃奖”等；腾讯网中国好老师；  
青少年教育导师认证；  
科学家长观体系的创立者

# 王侃老师

![](images/2c007b0f1dd5193c4e18f3ee46425161ccbb50691f3ec73863facb9737bd3720.jpg)

毕业于北京大学数学系  
学而思网校高中数学教研奠基人；  
学而思网校高中数学S级教师；  
荣获学而思网校“突出贡献奖” “桃李天下奖”等；擅长总结题型特点，提炼思想方法；  
擅长分层教学，因材施教

![](images/fe2fc90654658b619486a21d58456caf05abc8a0024bcfa0a61aa7c7f6a9e8eb.jpg)

# 付恒岩 老师

毕业于大连理工大学  
网校高中部理科主讲岗后培训师；  
2020年荣获学而思网校最具魅力奖；  
2019年、2020年荣获学而思网校诲人不倦奖；2020年荣获学而思网校高考优秀评卷人；  
2021年担任新浪教育高考数学直播解析特邀嘉宾；“停课不停学”公益课高中数学主讲老师

# 武洪姣 老师

![](images/cdbb7de2c2e73ec447df330ca4b6a8e98afe736157882f9b8a437eaf65a2a007.jpg)

14年线上线下教学经验；  
学而思网校高中理科教研负责人；  
学而思高中数学特级教师；  
在教学的过程中擅长归纳题型，方法和技巧;  
在高中数学模块中最擅长讲解圆锥曲线和导数；  
无论你是从小数学不好，还是数学一直拔尖，都可以在武老师的课堂上收获很多

1级

# 函数的奇偶性(一)

一本一讲，共26讲（建议5个月学习）

7个知识点 $^ { + 8 }$ 个题型 $+ 2 7$ 个视频每周一讲（建议学习时间90分钟）

210g9-eln1+32  
= 2×2-e°+(8§)²  
二 4-1+4  
=7

# 预习篇

第1讲集合的概念与表示 提升篇第2讲集合的关系与运算第11讲集合重难点专题第3讲解不等式第12讲常用逻辑用语第4讲函数的概念与三要素第13讲基本不等式第5讲函数的单调性（一）第14讲函数三要素专题第6讲函数的奇偶性（一）·第15讲函数的单调性（二）第7讲指数幂运算与幂函数 第16讲函数的奇偶性（二）第8讲指数函数 第17讲抽象函数第9讲对数运算 第18讲指数幂运算与指数函数第10讲对数函数 第19讲对数运算与对数函数  
6 第20讲函数与方程第21讲恒成立与存在性问题第22讲三角函数基本概念与诱导公式  
模块1 奇偶性的概念与判定 2 第23讲三角函数的图象与性质  
模块2 奇偶性的四则运算 8 第24讲三角公式的应用技巧  
模块3 奇偶性的应用 11第25讲三角恒等变换重点题型第26讲正弦型函数的图象与性质参考答案

# 函数的奇偶性（一）

# 直击课堂

<html><body><table><tr><td>知识模块</td><td>知识点</td><td>对应例题</td><td>星标统计</td></tr><tr><td rowspan="3">奇偶性的概念与判定</td><td>奇函数、偶函数的定义</td><td rowspan="2">例1</td><td rowspan="9">★4道题 ★★11道题 ★★★8道题 ★★★★1道题</td></tr><tr><td>函数的奇偶性</td></tr><tr><td>函数奇偶性的判定</td></tr><tr><td>奇偶性的四则运算</td><td>奇偶性的四则运算</td><td>例2 例3</td></tr><tr><td rowspan="5">奇偶性的应用</td><td rowspan="2">利用函数奇偶性 的定义求函数值</td><td>例4</td></tr><tr><td>例5</td></tr><tr><td rowspan="2">利用函数</td><td></td></tr><tr><td>侧6</td></tr><tr><td>利用函数奇偶</td><td>例8</td><td></td></tr></table></body></html>

# 学习目标

$\textcircled{1}$ 通过数形结合，深刻理解函数的奇偶性概念，会判断函数的奇偶性.

$\textcircled { 2 }$ 掌握利用奇偶性求解对称函数解析式的方法.

# 模块1奇偶性的概念与判定

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# $\textcircled { 1 }$ 奇函数、偶函数的定义

设函数的定义域为 $I$ ，如果对 $I$ 内的任意一个 $x$ ,都有 $- \boldsymbol { x } \in I$ ，且$f ( \mathbf { \partial } - x ) = f ( x )$ ，则这个函数叫做偶函数.

设函数的定义域为 $I$ ，如果对 $I$ 内的任意一个 $x$ ，都有 $- \boldsymbol { x } \in I$ ，且$f ( \mathbf { \partial } - x ) = - f ( x )$ ，则这个函数叫做奇函数.

# 函数的奇偶性

如果函数 $\scriptstyle { y = f ( x ) }$ 是奇函数或偶函数，就说函数 $\scriptstyle y = f ( x )$ 具有奇偶性；反之，称函数 $\scriptstyle { y = f ( x ) }$ 不具有奇偶性.

# $\textcircled{8}$ 函数奇偶性的判定

判断函数的奇偶性，一般有以下几种方法：

(1)定义法

用定义法判断函数奇偶性的步骤：

$\textcircled{1}$ 确定函数的定义域，并判断其定义域是否关于坐标原点对称。

$\textcircled{2}$ 若函数的定义域不关于原点对称，则可立即判断该函数既不是奇函数也不是偶函数；若函数的定义域关于坐标原点对称，再判断$f ( \mathbf { \theta } - \mathbf { \mathfrak { x } } )$ 是否等于 $\pm f ( x )$ ,或判断 $f ( x ) \pm f ( - x )$ 是否等于零.

(2)图象法

可以通过函数的图象直观看出函数的奇偶性，奇函数的图象关于原点对称，偶函数的图象关于 $y$ 轴对称.

# 思考探究

观察下面四个函数的图象,回答以下问题：

![](images/eb50441721af0245215b515b457be88b43c4827e712e3a9f3124ce35d9938f8d.jpg)

（1)观察图象,从对称的角度思考,它们有什么共同的特征？

(2)分别求当自变量 $x = \pm 1$ ， $\pm 2$ 时的函数值,从中你能发现什么规律?

(3)是否对于定义域内所有的 $_ x$ ,都有类似的情况？

# 重点笔记

![](images/ef3ef93dffe25acd0b1468809de958d5cb564c65b5c11c626965fb1a0813c771.jpg)

# 精讲精练

# 拍照批改秒判对错

# 例1

# 1

下面是一些函数的图象，根据图象判断它们的奇偶性.

![](images/783538697f30fa9b905a55214b5c13178c6d4fd37eb4503a5976e157bb1805f7.jpg)

![](images/56ddf920d2595a2c40b76768da438f5967723cd71677f66257fcc0dd2952adda.jpg)

# 学习笔记

# （2）★★

判断下列函数的奇偶性：

$$
\textcircled { 1 } f ( x ) = 3 x ; \textcircled { 2 } f ( x ) = \frac { 1 } { \sqrt { x } } ; \textcircled { 3 } f ( x ) = x - \frac { 1 } { x } ; \textcircled { 4 } f ( x ) = 3 .
$$

# 学习笔记

# 变式1

# （1）

根据图象依次判断函数 $\textcircled{1} \textcircled{2} \textcircled{3} \textcircled { 4 }$ 的奇偶性 （如“AABA”).

![](images/****************************************c3e7957d5fe0ecb57dffff00.jpg)

A.奇函数 B.偶函数C．既是奇函数又是偶函数 D．既不是奇函数也不是偶函数

# 学习笔记

# (2）★★

依次判断下列函数的奇偶性 （如“AABA”）.

$$
\textcircled { 1 } f ( x ) = 3 x - 1 ; \textcircled { 2 } f ( x ) = - \frac { 2 } { x } ; \textcircled { 3 } f ( x ) = 0 ; \textcircled { 4 } f ( x ) = x ^ { 2 } - \left| x \right| + 1 .
$$

A.奇函数 B.偶函数C．既是奇函数又是偶函数 D．既不是奇函数也不是偶函数

# 学习笔记

# （3）★★★

函数 $f ( x ) = \left( x - 1 \right) \sqrt { \frac { 1 + x } { 1 - x } } \left( \begin{array} { l l } { \right. } \end{array}$

A．是奇函数 B．是偶函数C．既是奇函数又是偶函数 D．既不是奇函数也不是偶函数

# 学习笔记

# 例2★

证明 $f ( x ) = x ^ { 4 } + { \frac { 1 } { x ^ { 2 } } } + 1$ 是偶函数.

# 学习笔记

# 变式2★

证明 $g ( x ) = x ^ { 3 } + { \frac { 1 } { x } }$ 是奇函数.

# 学习笔记

# 模块2 奇偶性的四则运算

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

$f ( x ) \pm g ( x ) , f ( x ) g ( x ) , f ( x ) \div g ( x )$ 的奇偶性与 $f ( x ) , g ( x )$ 的奇偶性的关系：

<html><body><table><tr><td>f(x）</td><td>g(x）</td><td>kf（x）</td><td>f（x）±g(x)</td><td>f（x)g(x)</td><td>f（x）÷g(x) （g(x）≠0）</td></tr><tr><td>奇函数</td><td>奇函数</td><td>奇函数</td><td>奇函数</td><td>偶函数</td><td>偶函数</td></tr><tr><td>偶函数</td><td>偶函数</td><td>偶函数</td><td>偶函数</td><td>偶函数</td><td>偶函数</td></tr><tr><td>奇函数</td><td>偶函数</td><td>奇函数</td><td>非奇非偶函数</td><td>奇函数</td><td>奇函数</td></tr><tr><td>偶函数</td><td>奇函数</td><td>偶函数</td><td>非奇非偶函数</td><td>奇函数</td><td>奇函数</td></tr></table></body></html>

![](images/af3d9ac8c571395c86eb7ab8f6475ad350169310fb01231777f08442ba0a893e.jpg)

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例3★★

如果 $f ( x )$ 是定义在 $\mathbf { R }$ 上的奇函数,那么下列函数中，一定为偶函数的是

$$
\begin{array} { r } { y = x - f ( x ) \mathrm { ~ \small ~ \displaystyle ~ { ~ B . ~ } ~ } y = \frac { f ( x ) } { x ^ { 2 } + 1 } \mathrm { ~ \small ~ \displaystyle ~ { ~ C . ~ } ~ } y = x ^ { 2 } + f ( x ) \mathrm { ~ \small ~ \displaystyle ~ { ~ D . ~ } ~ } y = x f ( x ) } \end{array}
$$

# 学习笔记

# 变式3

# （1）★★

下列函数是偶函数的是(

A. $f ( x ) = x ^ { 3 } + x ^ { 2 }$ （204 B. $\scriptstyle { y = x ^ { 3 } - x }$ C. $f ( x ) = { \frac { x ^ { 2 } + | x | } { x } }$ D. $f ( x ) = | x | x ^ { 6 }$ （204

# 学习笔记

# （2）★★★

已知函数 $f ( x ) = a x ^ { 2 } + b x + c ( a \neq 0 )$ 是偶函数，则函数 $\varrho ( { \boldsymbol { x } } ) = a x ^ { 3 } + b x ^ { 2 } +$ $c x ( a { \neq } 0 )$ 是（）

A.奇函数 B.偶函数C.非奇非偶函数 D．既是奇函数又是偶函数

![](images/4777fee02ec33ecda7fdee5b8fba153f9b974f74604655f3fa45decef07b0eb4.jpg)![](images/cb1f5d13fe89508406d6ddcc896349bc3d54875f5edf61c76a1f684b61ad8922.jpg)

# 模块3奇偶性的应用

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# 1利用函数奇偶性的定义求函数值

已知 $f ( a )$ 求 $f ( \mathbf { \partial } - a )$ 的思路：判断 $f ( x )$ 的奇偶性或构造已知奇偶性的函数,利用奇偶性找出 $f ( a )$ 与 $f ( \mathbf { \partial } - a )$ 的关系,若还有其他条件,再利用其转化,进而求 $f ( \mathbf { \partial } - a )$

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例4

# （1）★

已知函数 $f ( x )$ 是定义域为 $\mathbf { R }$ 的奇函数，且 $f ( 1 ) = - 2$ ,那么f（-1）+$f ( 0 ) = \left( \begin{array} { l l l } { \begin{array} { r l } \end{array} } & { \begin{array} { r l } \end{array} } \end{array} \right)$

A. $^ { - 2 }$ B.0 C.1 D.2

# 学习笔记

# （2）★★

设 $f ( x )$ 是定义在R上的偶函数，当 $x \leqslant 0$ 时， $f ( x ) = 2 x ^ { 2 } - x$ ，则 $f ( 1 ) =$ （）

A.-1 B.-3 C.1 D.3

# 学习笔记

# 变式4★★

已知函数 $f ( x )$ 是定义域为R的奇函数，且 $x \in ( \ - \infty \ , 0 )$ 时， $f ( x ) = x +$ 1,则f(7)等于（

A.2 B.4 C.6 D.8

# 学习笔记

# 例5★★★★

已知函数 $y = f ( x ) + x + 1$ 是奇函数，且 $f ( 2 ) = 3$ ，则 $f ( \mathbf { \Sigma } - 2 ) = ( \mathbf { \Sigma } )$

A.-7 B.0 C.-3 D.-5

![](images/8c8ea98073a17da544d80448f7282a597a379747bc49bdd957030fbd525b97e0.jpg)

# 学习笔记

# 变式5★★★

已知函数 $\scriptstyle { y = f ( x ) - 2 x }$ 是偶函数，且 $f ( 1 ) = 2$ ，则 $f ( \mathbf { \partial } - 1 ) = ( \mathbf { \partial } \mathbf { \partial } )$

A.2 B.-2 C.0 D.1

# 学习笔记

# 知识点睛

# ②利用函数奇偶性求参数

利用函数的奇偶性求参数一般有两种题型：

(1)定义域含参：根据定义域关于原点对称列式求解;(2)解析式含参：可以代入特殊值求解，如 $f ( 1 ) = \pm f ( - 1 ) .$ ，还可以根据 $f ( x ) = f ( - x )$ 或$f ( x ) = - f ( - x )$ 列式,比较各项的系数求解.若函数为奇函数，且在原点有定义，可直接根据$f ( 0 ) = 0$ 求解.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例6

# （1）★★

已知一个奇函数的定义域为 $\{ \ - 1 , 2 , a , b \}$ ，则 $a + b = ( \qquad )$

A.-1 B.1 C.0 D.2

# 学习笔记

# （2）★★

已知 $ f ( x ) = a x ^ { 2 } + b x$ 是定义在 $[ a - 1 , 2 a ]$ 上的偶函数,那么 $a + b$ 的值是）

A. $- \frac { 1 } { 3 }$ B. $\frac 1 3$ C. $- { \frac { 1 } { 2 } }$ D.

# 学习笔记

# 变式6★★

若函数 $f ( x ) = a x ^ { 2 } + b x + 2 a + b$ 是偶函数,定义域为 $[ a - 1 , a ]$ ，则 $a =$ $, b = \_ .$

# 学习笔记

# 例7★★

设函数 $f ( x ) = x ^ { 3 } \left( x + 1 \right) \left( x + a \right)$ 是奇函数,则 $a = \_$

# 学习笔记

# 变式7★★★

已知函数 $f ( x ) = { \frac { 1 } { x + 2 } } + { \frac { a } { x - 2 } }$ 是奇函数，则 $a = .$

# 学习笔记

# 知识点睛

# $\textcircled { 8 }$ 利用函数奇偶性求解析式

已知函数的奇偶性及其在定义域内某区间上的解析式,求该函数在整个定义域上的解析式的步骤如下：

(1)求哪个区间上的解析式， $x$ 就设在哪个区间上；  
(2）把 $x$ 对称转化到已知区间上，代入已知区间的函数解析式中；(3)利用 $f ( x )$ 的奇偶性把 $f ( \mathbf { \theta } - \mathbf { \mathcal { x } } )$ 改写成 $- f ( x )$ 或 $f ( x )$ ,从而求出 $f ( x )$ (4)列出 $f ( x )$ 在定义域内的解析式.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例8

# （1）★★★

已知函数 $f ( x )$ 是 $\mathbf { R }$ 上的奇函数,且当 $x > 0$ 时， $f ( x ) = - x + 1$ ，试求 $f ( x )$ 的解析式.

# 学习笔记

# （2）★★★

已知函数 $f ( x )$ 是定义在 $\mathbf { R }$ 上的偶函数，当 $x \geqslant 0$ 时， $f ( x ) = x ^ { 3 } + x$ ,则当$x < 0$ 时， $f ( x ) = ( \qquad )$

A $x ^ { 3 } - x$ B.-x³-x C. $- { x ^ { 3 } } + x$ D.x²+x

# 学习笔记

# 变式8

# （1）★★★

已知函数 $f ( x )$ 是偶函数，当 $x > 0$ 时， $f ( x ) = - x ^ { 2 } + x$ ,那么当 $x < 0$ 时，$f ( x ) = ( \qquad )$

A $x ^ { 2 } - x$ B. $x ^ { 2 } + x$ C.-x²+x D.-x²-x

# 学习笔记

# （2）★★★

已知 $f ( x )$ 为奇函数，当 $x > 0$ 时， $f ( x ) = x { \bigl ( } 1 + x { \bigr ) }$ ，那么 $x < 0$ 时， $f ( x )$ 等于（）

A. $- x ( 1 - x )$ B. $x ( 1 - x )$ C. $- x ( 1 + x )$ D. x(1 +x)

# 学习笔记

# 学习总结

![](images/b4a6bde4ad81a36d45ff6c52adb967ea718cefca4e4469f83388c27f81787686.jpg)

# 提升篇你会遇见

已知函数 $f ( x )$ 是偶函数，当 $x \geqslant 0$ 时， $f ( x ) = x + 1$ 设函数 $g ( x ) =$ $f ( t - x ) - f ( x )$ 的零点为 $x _ { 0 }$ ，且 $x _ { 0 } \in \left[ { 1 , 2 } \right]$ ，则非零实数 $t$ 的取值范围是

【点石成金】预习篇学习了函数奇偶性的简单应用，提升篇会学习单调性与奇偶性的综合应用，并且此题将零点问题转换为交点问题，利用图象法来解决函数问题，是提升篇学习的重难点！

# 学而思秘籍系列图书|数学

# 思维培养

![](images/69e7cd9fc0ca9f0d6e789ed7e87de1ec30672f1e57faefd3798e62724a351301.jpg)

# 小学秘籍系列

学而思积淀近20年教研经验，培养受益一生的能力。

# 思维提升

![](images/7be7aebeae77c3e56c4bf0c20f0187a1effa33efe899991adb535fc033075375.jpg)

# 思维突破

# 初中秘籍系列

全面覆盖初中基础知识和重难点，帮助学生夯实基础，拓展认知。

学而思 8秘籍 集合的护-43.Rn高中数学思维突破++

# 高中秘籍系列

全面覆盖高中基础知识和重难点，帮助学生提升能力，突破思维。

# 学而思秘籍系列图书|语文

# 提升素养

# 能力训练

# 小学秘籍系列

![](images/a73072a53c4890652c0b728df2f80d513b75b8b5b188506d42c6e07ed1003730.jpg)

5大模块+2条主线，能力与素养双向提升。

![](images/8405461b6e1caf1d5969b1e29e12b042754a6fc82fdaca10276bade4707027a1.jpg)

# 初中秘籍系列

融合课改四大核心素养，培养爱阅读、 善写作、勤思考、会学习的学生。

# 创新体系|真题研习

![](images/e53dac56e6b6b5233797d22a5e6994e40d5de25e13e87faeab4f5fe9b212c471.jpg)

# 思维创新大通关 数学

攻克数学思维难题，通向理想中学。

# 大家一起来“升级’

# 参与方式

您在使用本书时，如有任何疑问或对图书有任何建议，请扫码进行反馈，并查看反馈采纳结果。

# 奖励

您的反馈一经采纳，我们将会送出总价值35元的图书抵扣券（相同内容的反馈，依据反馈时间，奖励前三位）。请扫码关注公众号，并在对话框中发送反馈时填写的手机号，领取抵扣券。

![](images/bdb6d62d3dfcabedfeac59fbf3ed5ef1ced0436dbde793697bc089f39fbd0d9f.jpg)

# 合理规划学习时间

先自己定一个目标，即制定半年学习规划。  
2 再将目标细化到每一周，每周学习一本（平均5个考点）。  
3 配套课堂巩固的练习，让学习更有效！

![](images/2f6c8711e1817dca8ab9bf0ebabc3297d9d02e54fb4a1433e8bee05b8d0bb1a4.jpg)

![](images/ba69c8394c02bc6210cb70143d63779f74efce5a8a4051b08bc4dc81934dacd6.jpg)  
·共6级·每级17-26讲