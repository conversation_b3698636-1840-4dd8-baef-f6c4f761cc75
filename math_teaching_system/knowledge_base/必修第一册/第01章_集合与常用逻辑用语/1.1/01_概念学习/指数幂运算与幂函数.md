---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 20
source_file: L1-7-指数幂运算与幂函数.md
title: L1-7-指数幂运算与幂函数
type: problem_type
---

# 指数幂运算与幂函数

一本一讲，共26讲（建议5个月学习）

7个知识点+7个题型+21个视频

每周一讲（建议学习时间90分钟）

![](images/c311cb8e6a566a3778366db920bd237655bea4b17b85f7b279504da5b6c9150e.jpg)

# 视频内容研发团队

学而思优秀老师

学而思优秀老师和一线高级教师联合创作本书试题，并精心录制讲解视频学而思图书APP扫码即可观看

![](images/1b206a6b9489c88c03e842aa9e357f87abe1f0adfff7bcbad8799b53ff5eb0f4.jpg)

# 傅博宇 老师

毕业于北京大学元培学院  
网校和北大数院高考数学研究联合课题组成员；  
网校高中创新产品部负责人；  
荣获学而思网校“桃李满天下奖”“出类拔萃奖”等；腾讯网中国好老师；  
青少年教育导师认证；  
科学家长观体系的创立者

![](images/3fbafbf05896adce63cef5babab6503ff210af58e12b2bbc83eeb1df58aebfe9.jpg)

# 王侃老师

毕业于北京大学数学系  
学而思网校高中数学教研奠基人；  
学而思网校高中数学S级教师；  
荣获学而思网校“突出贡献奖” “桃李天下奖”等；擅长总结题型特点，提炼思想方法；  
擅长分层教学，因材施教

![](images/b7e12a3d7c06badc4ac0f10651bd8eb5d637870e5212456b83666806e43854d7.jpg)

# 付恒岩 老师

毕业于大连理工大学  
网校高中部理科主讲岗后培训师；  
2020年荣获学而思网校最具魅力奖；  
2019年、2020年荣获学而思网校诲人不倦奖；2020年荣获学而思网校高考优秀评卷人；  
2021年担任新浪教育高考数学直播解析特邀嘉宾；“停课不停学”公益课高中数学主讲老师

![](images/c9faa7e5897feca69aa26e6aab8005a3b49c30c43c0a027a224bd0ad26712033.jpg)

# 武洪姣 老师

14年线上线下教学经验；  
学而思网校高中理科教研负责人；  
学而思高中数学特级教师;  
在教学的过程中擅长归纳题型，方法和技巧;  
在高中数学模块中最擅长讲解圆锥曲线和导数；  
无论你是从小数学不好，还是数学一直拔尖，都可以在武老师的课堂上收获很多

1级

# 指数幂运算与幂函数

一本一讲，共26讲（建议5个月学习）

7个知识点 $+ 7$ 个题型 $+ 2 1$ 个视频每周一讲（建议学习时间90分钟）

210g39-eln²+382  
= 2×2-e°+(8§)²  
二 4-1+4  
=7

# 预习篇

第1讲集合的概念与表示 提升篇第2讲集合的关系与运算第11讲集合重难点专题第3讲解不等式第12讲常用逻辑用语第4讲函数的概念与三要素第13讲基本不等式第5讲函数的单调性（一）第14讲函数三要素专题第6讲函数的奇偶性（一）第15讲函数的单调性（二）第 $^ 7$ 讲指数幂运算与幂函数·第16讲函数的奇偶性（二）第8讲指数函数 第17讲抽象函数第 $^ { 9 }$ 讲对数运算 第18讲指数幂运算与指数函数第10讲对数函数 第19讲对数运算与对数函数  
. 第20讲函数与方程第21讲恒成立与存在性问题第22讲三角函数基本概念与诱导公式  
模块1 指数幂运算 2 第23讲三角函数的图象与性质  
模块2 幂函数的概念 10 第24讲三角公式的应用技巧  
模块3 幂函数的图象与性质 13第25讲三角恒等变换重点题型第26讲正弦型函数的图象与性质参考答案

# 指数幂运算与幂函数

# 直击课堂

<html><body><table><tr><td>知识模块</td><td>知识点</td><td>对应例题</td><td>星标统计</td></tr><tr><td rowspan="3">指数幂运算</td><td>整数指数幂</td><td>例1</td><td rowspan="9">★2道题 ★★13道题 ★★★3道题</td></tr><tr><td>分数指数幂</td><td>例2</td></tr><tr><td>无理数指数幂</td><td>例3</td></tr><tr><td rowspan="2">幂函数的概念</td><td>幂函数的概念</td><td>例4</td></tr><tr><td>幂函数的特征</td><td>例5</td></tr><tr><td rowspan="2">幂函数的图象与性质</td><td>幂函数的图象</td><td>例6</td></tr><tr><td>幂函数的性质</td><td>例7</td></tr></table></body></html>

# 学习目标

$\textcircled{1}$ 掌握分式指数幂的概念，理解分数指数幂的运算性质.

$\textcircled{2}$ 掌握根式的概念，能进行分数指数幂与根式的互化.

$\textcircled{6}$ 通过实例，了解幂函数的概念.

$\textcircled{4}$ 了解几种不同类型幂函数的图象和变化情况.

$\textcircled{5}$ 会应用幂函数的性质解决一些简单问题.

# 模块1指数幂运算

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# 整数指数幂

(1)整数指数幂的法则  
$\textcircled { 1 } a ^ { m } \cdot a ^ { n } = a ^ { m + n } ; \textcircled { 2 } \left( a ^ { m } \right) ^ { n } = a ^ { m n } ; \textcircled { 3 } \frac { a ^ { m } } { a ^ { n } } = a ^ { m - n } \left( a \neq 0 \right) ;$   
$\textcircled { 4 } ( a b ) ^ { m } = a ^ { m } b ^ { m }$ ， $( m , n$ 为整数)  
(2)零指数幂和负整数指数幂规定如下：  
$a ^ { 0 } = 1 \left( a \neq 0 \right) , a ^ { - n } = \frac { 1 } { a ^ { n } } ( a \neq 0 , n \in { \bf N } ^ { * } )$

# 分数指数幂

(1)方根概念的推广

如果存在实数 $x$ ，使得 ${ \boldsymbol { x } } ^ { n } = a ( a \in \mathbf { R } , n > 1 , n \in \mathbf { N } ^ { * } \ \mathrm { ~ , ~ }$ ，那么 $x$ 叫做 $a$ 的 $n$ 次方根.求 $a$ 的 $n$ 次方根,叫做把 $a$ 开 $n$ 次方,称作开方运算.

# 注意

(1)当 $\sqrt [ n ] { a }$ 有意义时， $\sqrt [ n ] { a }$ 叫做根式， $n$ 叫做根指数.  
当 $a = 0$ 时：0的任何次方根都是0,即 ${ \sqrt [ n ] { 0 } } = 0$   
当 $a \neq 0$ 时： $n$ 为偶数时： $\textcircled{1} a > 0 : n$ 次方根有两个 $\sqrt [ n ] { a }$ ， $- { \sqrt [ n ] { a } }$ $\textcircled { 2 } a < 0$ ：无偶次方根.$n$ 为奇数时： $n$ 次方根 $\sqrt [ n ] { a }$ 唯一存在，正负与 $^ { a }$ 相同.

# 注意

(2) $( \sqrt [ n ] { a } ) ^ { n } = a ( n > 1 , n \in \mathbf { N } ^ { * } )$ （当 $n$ 为偶数时,必须有 $a \geqslant 0$ ，否则没有意义）；当 $n$ 为奇数时， ${ \sqrt [ n ] { a ^ { n } } } = a$ 当 $n$ 为偶数时， ${ \sqrt [ n ] { a ^ { n } } } = | a | .$ 若加上限制条件 $a > 0$ ，此时对任意 $\boldsymbol n \in \mathbb { N } ^ { * }$ ，都有 $( { \sqrt [ n ] { a } } ) ^ { n } = { \sqrt [ n ] { a ^ { n } } } = a .$

(2)正分数指数幂

$$
a ^ { \frac { 1 } { n } } = { \sqrt [ n ] { a } } \left( a > 0 \right) ; a ^ { \frac { m } { n } } = \left( { \sqrt [ n ] { a } } \right) ^ { m } = { \sqrt [ n ] { a ^ { m } } } \left( a > 0 , m , n \in \mathbf { N } ^ { * } , m , n \right)
$$

(3)负分数指数幂$a ^ { - { \frac { m } { n } } } = { \frac { 1 } { a ^ { { \frac { m } { n } } } } } ( a > 0 , m , n \in \mathbf { N } ^ { * } , m , n$ 互质）

(4)有理数指数幂的运算法则 $( a > 0 , b > 0 , r , s \in { \bf Q } )$ $\textcircled { 1 } a ^ { r } a ^ { s } = a ^ { r + s } ; \textcircled { 2 } ( a ^ { s } ) ^ { r } = a ^ { r s } = ( a ^ { r } ) ^ { s } ; \textcircled { 3 } ( a b ) ^ { r } = a ^ { r } b ^ { r } .$

# $\textcircled { 8 }$ 无理数指数幂（阅读）

通过上面分数指数幂的学习,我们将指数的取值范围由整数推广到了有理数,那么当指数是无理数时，我们又应当如何理解它？比如$5 ^ { \sqrt { 2 } }$ .我们现在还不能给出无理指数幂严格的定义,只能通过一个具体的例子进行感性的认识. $5 ^ { \sqrt { 2 } }$ 是一个确定的实数,我们可以通过 $\sqrt { 2 }$ 的不足近似值与过剩近似值去近似地去计算它的值,如下：

<html><body><table><tr><td></td><td></td><td></td><td>V2的过剩近似值5的过剩近似值2的不足近似值5的不足近似值</td></tr><tr><td>1.5</td><td>11.180 339 89</td><td>1.4</td><td>9.518 269 694</td></tr><tr><td>1.42</td><td>9.829 635 328</td><td>1.41</td><td>9.672 669 973</td></tr><tr><td>1.415</td><td>9.750 851 808</td><td>1. 414</td><td>9.735 171 039</td></tr><tr><td>1.414 3</td><td>9.739 872 62</td><td>1. 414 2</td><td>9. 738 305 174</td></tr><tr><td>1.414 22</td><td>9.738 618 643</td><td>1. 414 21</td><td>9.738461907</td></tr><tr><td>1.414 214</td><td>9.738 524 602</td><td>1.414 213</td><td>9.738508928</td></tr><tr><td>1.414 213 6</td><td>9.738518332</td><td>1.414 213 5</td><td>9.738516765</td></tr></table></body></html>

续表  

<html><body><table><tr><td>√2的过剩近似值</td><td>5的过剩近似值</td><td>√2的不足近似值</td><td>5的不足近似值</td></tr><tr><td>1. 414 213 57</td><td>9.738 517 862</td><td>1. 414 213 56</td><td>9.738 517 705</td></tr><tr><td>1.414 213 563</td><td>9. 738 517 752</td><td>1. 414 213 562</td><td>9.738 517 736</td></tr><tr><td>：</td><td>：</td><td>：</td><td>：</td></tr></table></body></html>

由上表我们知道,当 $\sqrt { 2 }$ 的过剩近似值从大于 $\cdot \sqrt { 2 }$ 的方向逼近 $\sqrt { 2 }$ 时， $5 ^ { \sqrt { 2 } }$ 的近似值从大于 $5 ^ { \sqrt { 2 } }$ 的方向逼近 $5 ^ { \sqrt { 2 } }$ ；当 $\sqrt { 2 }$ 的不足近似值从小于 $\cdot \sqrt { 2 }$ 的方向逼近 $\sqrt { 2 }$ 时， $5 ^ { \sqrt { 2 } }$ 的近似值从小于 $5 ^ { \sqrt { 2 } }$ 的方向逼近 $5 ^ { \sqrt { 2 } }$ 。

一般地,当 $a > 0 , \alpha$ 是无理数时， $a ^ { \alpha }$ 都有意义，且有理指数幂的运算法则在无理数指数幂中仍然成立.

于是,对于实数指数幂,我们有：

(1)当 $a > 0 , \alpha$ 为任意实数值时，实数指数幂 $a ^ { \alpha }$ 都有意义.

(2)有理指数幂的运算法则在实数指数幂中仍然成立.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例1★

化简或求值.  
(1) $\left( a - b \right) ^ { \scriptscriptstyle 0 } \left( a \ne b \right) .$ 2 $\Big . \Big . \Big . \Big . \Big . \Big . \Big ( - \frac { 1 } { 2 } \Big ) ^ { - 3 } .$   
(3） $\left( 2 x \right) ^ { - 3 } \left( x \neq 0 \right) .$

# 学习笔记

# 变式1

# （1）★★

$( \ - 2 ) ^ { 4 } + ( \ - 2 ) ^ { 3 } + \left( \ - { \frac { 1 } { 2 } } \right) ^ { - 3 } - \left( \ - { \frac { 1 } { 2 } } \right) ^ { 3 }$ 的值为(

A7 Bä $\frac { 1 } { 8 }$ C.-24 D.-8

# 学习笔记

# (2)★

计算 $4 + 2 ^ { 0 } - \left( { \frac { 1 } { 1 6 } } \right) ^ { - { \frac { 1 } { 2 } } } = - \qquad $

# 学习笔记

# 例2★★

计算：

①（√-32)； $\begin{array} { c } { { \textcircled { 2 } \sqrt [ 3 ] { \left( \ - 2 \right) ^ { 3 } } \ ; } } \\ { { \textcircled { 4 } \sqrt [ 4 ] { \left( \ - 8 \right) ^ { 4 } } \ ; } } \\ { { \textcircled { 6 } \sqrt [ 3 ] { \left( 3 \ - \pi \right) ^ { 3 } } . } } \end{array}$   
③√（-6)²；  
⑤√(x-y）²；

# 学习笔记

# 变式2★★

已知 $a < \frac { 1 } { 4 }$ 则 $\sqrt [ 4 ] { \left( 4 a - 1 \right) ^ { 2 } }$ 化简的结果是(

A. $\sqrt { 4 a - 1 }$ B.-√4a-1 C.√1-4a D.-√1-4a

# 学习笔记

# 例3

# （1）★★

计算下列各式.

2 1 ①27 = ;②25= 3 4 ;④121 =

![](images/e00f1b2e87f5056a42f6b4957e03122e128e123f53473fad18b7c286b81c2f09.jpg)

# 学习笔记

# （2）★★

将 $\sqrt [ 3 ] { - 2 \sqrt { 2 } }$ 化为分数指数幂的形式为（

A.-2 B.-23 C.-2 D.-2

# 学习笔记

# （3）★★

化简 $\frac { \sqrt [ 3 ] { a ^ { 2 } } \cdot ( a ^ { \frac { 1 } { 6 } } ) ^ { 4 } } { \sqrt [ 3 ] { a } } = ( \qquad )$

A.a B.a-² C $\sqrt [ 3 ] { a ^ { 4 } }$ D.a4

# 学习笔记

# 变式3

# （1）★★

$$
; \left( 2 { \frac { 3 } { 5 } } \right) ^ { 0 } + 2 ^ { - 2 } \times \left( 2 { \frac { 1 } { 4 } } \right) ^ { - { \frac { 1 } { 2 } } } - \left( 0 . 0 1 \right) ^ { 0 . 5 } = \left( \begin{array} { l l l } { { } } & { { } } & { { } } \end{array} \right)
$$

A. $\frac { 3 } { 5 }$ B.16 C. $\frac { 4 } { 5 }$ D.1

# 学习笔记

# （2)★★

化简 $\frac { \sqrt { a ^ { 3 } \sqrt { b } } } { \sqrt { a b ^ { 2 } } } ( a > 0 , b > 0 )$ 的结果为(

A $a b ^ { - \frac { 3 } { 4 } }$ B.ab4 1 C.ab 3 D.ab 1

![](images/6fb3f0c8091d7fb6b84a5356494d9115ad51f2d6fc6562a7d9dff3da286c7ef0.jpg)

# 学习笔记

# 模块2幂函数的概念

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# $\textcircled{1}$ 幂函数的概念

一般地,形如 $y = x ^ { \alpha } , \alpha \in \mathbb { R }$ 的函数称为幂函数,其中 $\alpha$ 为常数.

# 幂函数的特征

（1）x°的系数为1；  
（2）x的底数是自变量x，指数α为常数。

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例4★★

下列函数：

$\textcircled { 1 } y = x ^ { 2 } ; \textcircled { 2 } y = \left( \frac { 1 } { 4 } \right) ^ { x } ; \textcircled { 3 } y = 5 x ^ { 3 } ; \textcircled { 4 } y = x ^ { 2 } + 1 ;$ $\textcircled { 5 } y = 3 x ; \textcircled { 6 } y = a ^ { x } ( a > 1 , a$ 为常数); $\textcircled { 7 } y = ( \mathbf { \Sigma } - x ) ^ { 4 }$ 其中是幂函数的有

# 学习笔记

# 变式4★★

下列函数是幂函数的有 个

$$
\begin{array} { r } { \textcircled { 1 } y = ( x - 1 ) ^ { 2 } ; \textcircled { 2 } y = 2 ^ { x } ; \textcircled { 3 } y = 2 x ^ { 2 } ; \textcircled { 4 } y = - x ; \textcircled { 5 } y = ( \mathbf { \Sigma } - x ) ^ { 2 } . } \end{array}
$$

# 学习笔记

# 例5★★

已知 $y = ( m ^ { 2 } + 2 m - 2 ) x ^ { \frac { 1 } { m ^ { 2 } - 1 } } + 2 n - 3$ 是幂函数,求 $m _ { \setminus } n$ 的值.

# 学习笔记

# 变式5★★

已知函数 $f ( x ) = \left( m ^ { 2 } - 3 \right) x ^ { m ^ { 2 } - 4 m + 3 }$ ，当 $m$ 取（ )时是幂函数.

A.2 B.-2 C.2或-2 D.1

# 学习笔记

# 模块3幂函数的图象与性质

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

$\textcircled{1}$ 幂函数 $y = x ^ { \alpha } { \Big ( } \alpha = 1 , 2 , 3 , { \frac { 1 } { 2 } } , - 1 { \Big ) }$ 的图象

![](images/7eb367ff9b84b407b4e0ff1a25603b2f86110b644676e4bd4c759699136918c4.jpg)

# 重点笔记

幂函数 $y = x ^ { \alpha } { \Big ( } \alpha = 1 , 2 , 3 , { \frac { 1 } { 2 } } , - 1 { \Big ) }$ 的性质

<html><body><table><tr><td>函数</td><td>y=x</td><td>y=x²</td><td>y=x</td><td>y=x2</td><td>y=x-1</td></tr><tr><td>定义域</td><td>R</td><td>R</td><td>R</td><td>[0,+8）</td><td>（18,0）U(0,+8）</td></tr><tr><td>值域</td><td>R</td><td>[0，+8）</td><td>R</td><td>[0，+8）</td><td>（18,0)U(0,+∞）</td></tr><tr><td>奇偶性</td><td>奇函数</td><td>偶函数</td><td>奇函数</td><td>非奇非偶函数</td><td>奇函数</td></tr><tr><td>单调性</td><td>在R上是 增函数</td><td>在[0,+8） 上是增函数; 在（18,0] 上是减函数</td><td>在R上 上是增函数</td><td>在[0，+8） 上是增函数</td><td>在（0，+∞） 上是减函数； 在（18,0） 上是减函数</td></tr><tr><td></td><td></td><td></td><td>过点(0,0),(1,1)(0,0),(1,1)(0,0）,(1,1)</td><td>(0,0)，(1,1)</td><td>(1,1)</td></tr></table></body></html>

# $\textcircled{8}$ 幂函数的图象和性质

（1)所有的幂函数在区间 $( 0 , + \infty )$ 上都有定义，并且图象都过点(1,1).(2)若 $\alpha > 0$ ,则幂函数的图象过原点，并且在区间 $[ 0 , + \infty )$ 上是增函数.(3)若 $\alpha < 0$ ，则幂函数在区间 $( 0 , + \infty )$ 上是减函数，在第一象限内，当 $x$ 从右边趋于0时，图象在 $y$ 轴右方无限地逼近 $y$ 轴；当 $x$ 趋于 $+ \infty$ 时,图象在 $x$ 轴上方无限地逼近 $x$ 轴.（4)幂函数的奇偶性：设幂函数 $\scriptstyle \gamma = { x ^ { \alpha } } ( \alpha = { \frac { q } { p } } , p , q \in \mathbf { Z }$ 且 $p , q$ 互质)，若 $p , q$ 同时为奇数，则幂函数 $y = x ^ { \alpha }$ 是奇函数.若 $p$ 为奇数， $q$ 为偶数，则幂函数 $\boldsymbol { y } ^ { \aa } = \boldsymbol { x } ^ { \boldsymbol { \alpha } }$ 是偶函数.若 $p$ 为偶数，则 $q$ 为奇数，此时幂函数 $y = x ^ { \alpha }$ 既不是奇函数也不是偶函数.

# $\textcircled{4}$ 画幂函数图象的步骤

(1)确定定义域；  
(2)判断函数在第一象限的单调性；  
(3)画出第一象限的图象；  
(4)根据奇偶性补出剩余图象.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例6★★★

幂函数 $\gamma = { x ^ { m } } , \gamma = { x ^ { n } } , \gamma = { x ^ { p } }$ 的图象如图所示，以下结论正确的是( ）

A. $m > n > p$   
B. $m > p > n$   
C. $n > p > m$   
D. $p > n > m$

# 学习笔记

![](images/592702e0c6c469bbd99f6f5ecee16fde6680053d8508893a06d5def304aba253.jpg)

# 变式6★★

已知幂函数y=x"中的α分别为3,， $^ 3 , \frac 1 2 , - 1$ ，则它们对应的图象依次是(

A $C _ { 2 } , C _ { 1 } , C _ { 3 }$ B. $C _ { 1 } , C _ { 3 } , C _ { 2 }$   
C. $C _ { 3 } , C _ { 2 } , C _ { 1 }$ D. $C _ { 1 } , C _ { 2 } , C _ { 3 }$

# 学习笔记

![](images/940cf4fd677cb051faa7f904450a5befb51bcc8614fef2ece8f6b2ea7b9ad7e1.jpg)

# 例7★★★

函数 $\gamma = x ^ { \frac { 5 } { 4 } }$ 的图象是(

![](images/089dfbfca48da9b2d0694e8d38290a86187130a505068dea8d86447949ddb06b.jpg)

# 学习笔记

# 变式7★★★

函数 $\scriptstyle y = x ^ { \frac { 4 } { 3 } }$ 的图象是(

![](images/5979898bb084104b6ff4deeee497e2d4267af43b06853181a6970bab560af040.jpg)

# 学习笔记

# 学习总结

![](images/eb028468f021e636c2a253cbc691b9bdbed996072bc325c0abde43739f471d64.jpg)

# 提升篇你会遇见

已知 $a > 0$ ，且 $\boldsymbol { a } - \boldsymbol { a } ^ { - 1 } = 3$ ，求值：

$\textcircled { 1 } a ^ { 2 } + a ^ { - 2 }$ $\textcircled { 2 } \frac { ( a ^ { 3 } + a ^ { - 3 } ) ( a ^ { 2 } + a ^ { - 2 } - 3 ) } { a ^ { 4 } - a ^ { - 4 } } .$

【点石成金】预习篇我们学习的幂运算，只是进行简单的运算，提升篇会涉及代数变形技巧，利用到平方和公式等，需要我们对问题进行分析变形到题目中已知条件，进行化简求值！

# 学而思秘籍系列图书数学

# 思维培养

# 思维提升

# 思维突破

![](images/c1ad98110b8969ddf544c119184e12f8e424c32a1fc613a9068e37351cf431c5.jpg)

# 小学秘籍系列

学而思积淀近20年教研经验，培养受益一生的能力。

品 学而思 秘籍有理数 数轴基   
初中数学   
思维提升 点   
1 ++ .

# 初中秘籍系列

全面覆盖初中基础知识和重难点，帮助学生夯实基础，拓展认知。

![](images/f4cfaac692b624f0edf5c2c2452b1cc327dea9684e625e8dcdc4c189c27718cc.jpg)

# 高中秘籍系列

全面覆盖高中基础知识和重难点，帮助学生提升能力，突破思维。

# 学而思秘籍系列图书|语文

# 提升素养

能力训练

![](images/5b6746b66810665d23027d99e35619bfd8f4d4ca23f49cff23066ea04ca92850.jpg)

# 小学秘籍系列

5大模块+2条主线，能力与素养双向提升。

![](images/0133a80233fbd253414cfd0ae843c51bd8fd78e1c5d9d07e4ea01b1b0a5b4497.jpg)

# 初中秘籍系列

融合课改四大核心素养，培养爱阅读、 善写作、勤思考、会学习的学生。

# 创新体系|真题研习

![](images/d30c58c847c4fcb9bcb4d5c380bed53d9f785b54ab33f3bc19b485f010f51068.jpg)

# 思维创新大通关数学

攻克数学思维难题，通向理想中学。

# 大家一起来“升级’

# 参与方式

您在使用本书时，如有任何疑问或对图书有任何建议，请扫码进行反馈，并查看反馈采纳结果。

![](images/3748c178a20ea75abfeec3012fd6d040c784c657d17396edc7a99007749707b9.jpg)

# 奖励

您的反馈一经采纳，我们将会送出总价值35元的图书抵扣券（相同内容的反馈，依据反馈时间，奖励前三位）。请扫码关注公众号，并在对话框中发送反馈时填写的手机号，领取抵扣券。

![](images/dbc160b9139a37dabc6031838deb9191690c788b490c6a79d5aa401a1f9837e2.jpg)

# 合理规划学习时间

先自己定一个目标，即制定半年学习规划。

![](images/b36550620f4b914c98e633cc2c242e80791789de8f031bf6a30c409e1028d132.jpg)

2 再将学细华到每一5个考点）。

3 配套课堂巩固的练习， 让学习更有效！

![](images/e2701f4eef4f2f063dc8f7497d5322585f78e8dd33ddd82285b24236dc0a4023.jpg)  
·共6级·每级17-26讲