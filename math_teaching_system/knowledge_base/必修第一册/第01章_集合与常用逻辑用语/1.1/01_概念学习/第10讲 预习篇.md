---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 15
source_file: L1-10-对数函数.md
title: L1-10-对数函数
type: problem_type
---

第10讲 预习篇

# 对数 函 数

一本一讲，共26讲（建议5个月学习）

6个知识点+8个题型+23个视频

每周一讲（建议学习时间90分钟）

![](images/f6e33588616319cd28887878c013556f5c819053d867a21396addbda0c3fbd63.jpg)

# 视频内容研发团队

学而思优秀老师

学而思优秀老师和一线高级教师联合创作本书试题，并精心录制讲解视频学而思图书APP扫码即可观看

![](images/45d0943b704d8307560e7ae86ec33c3c9f9274a76c894e1739da88d838a2a1a6.jpg)

# 傅博宇 老师

毕业于北京大学元培学院  
网校和北大数院高考数学研究联合课题组成员；  
网校高中创新产品部负责人；  
荣获学而思网校“桃李满天下奖”“出类拔萃奖”等；腾讯网中国好老师；  
青少年教育导师认证；  
科学家长观体系的创立者

![](images/37039e9e8134f395e51d11a57ccb326c7417c79666ee08a5ab0454a418d64391.jpg)

# 王侃老师

毕业于北京大学数学系  
学而思网校高中数学教研奠基人；  
学而思网校高中数学S级教师；  
荣获学而思网校“突出贡献奖” “桃李天下奖”等；擅长总结题型特点，提炼思想方法；  
擅长分层教学，因材施教

![](images/8f17917bc92edc08aa82968e1fdb64b2a97c5b36cc75b695768a0a1c51721f31.jpg)

# 付恒岩 老师

毕业于大连理工大学  
网校高中部理科主讲岗后培训师；  
2020年荣获学而思网校最具魅力奖；  
2019年、2020年荣获学而思网校诲人不倦奖；2020年荣获学而思网校高考优秀评卷人；  
2021年担任新浪教育高考数学直播解析特邀嘉宾；“停课不停学”公益课高中数学主讲老师

# 武洪姣 老师

![](images/82c53a16f53de907cbdfb2acb7012c335e5d8734594bf0f4865fdf56ebd51c0e.jpg)

14年线上线下教学经验；  
学而思网校高中理科教研负责人；  
学而思高中数学特级教师;  
在教学的过程中擅长归纳题型，方法和技巧;  
在高中数学模块中最擅长讲解圆锥曲线和导数；  
无论你是从小数学不好，还是数学一直拔尖，都可以在武老师的课堂上收获很多

1级

# 对娄 数 函 数

一本一讲，共26讲（建议5个月学习）

6个知识点 $^ { + 8 }$ 个题型 $+ 2 3$ 个视频每周一讲（建议学习时间90分钟）

10  
2109-el+2  
= 2×2-e°+(8§)²  
二 4-1+4  
=7

# 预习篇

第1讲集合的概念与表示 提升篇第 $^ 2$ 讲集合的关系与运算第11讲集合重难点专题第3讲解不等式第12讲常用逻辑用语第4讲函数的概念与三要素第13讲基本不等式第5讲函数的单调性（一）第14讲函数三要素专题第6讲函数的奇偶性（一）第15讲函数的单调性（二）第7讲指数幂运算与幂函数第16讲函数的奇偶性（二）第8讲指数函数第17讲抽象函数第9讲对数运算第18讲指数幂运算与指数函数第10讲对数函数第19讲对数运算与对数函数  
6第20讲函数与方程第21讲恒成立与存在性问题第22讲三角函数基本概念与诱导公式  
模块1 对数函数的概念 2 第23讲三角函数的图象与性质  
模块2 对数函数的图象与性质 6 第24讲三角公式的应用技巧  
模块3 对数的大小比较 12第25讲三角恒等变换重点题型第26讲正弦型函数的图象与性质参考答案

# 对数函数

# 直击课堂

<html><body><table><tr><td>知识模块</td><td>知识点</td><td>对应例题</td><td>星标统计</td></tr><tr><td rowspan="3">对数函数的概念</td><td rowspan="3">对数函数的概念</td><td>例1</td><td rowspan="9"></td></tr><tr><td>例2</td></tr><tr><td>例3</td></tr><tr><td rowspan="3">对数</td><td>对数函数的图象</td><td>例4</td><td rowspan="6">★4道题 ★★★11道题 ★★★★1道题</td></tr><tr><td rowspan="2">对数函数的性质</td></tr><tr><td>例5 例6</td></tr><tr><td rowspan="3">对数的大小比较</td><td>底数相同</td><td rowspan="2">例7</td></tr><tr><td>真数相同</td></tr><tr><td>底数不同,真数也不同</td><td>例8</td></tr></table></body></html>

# 学习目标

$\textcircled{1}$ 掌握对数函数的概念，熟悉对数函数的图象.

$\textcircled{2}$ 通过观察对数函数的图象，发现并归纳对数函数的性质.

$\textcircled{6}$ 培养学生数形结合的思想以及分析推理的能力.

$\textcircled{4}$ 掌握对数函数的性质，能初步运用性质解决问题.

# 模块1 对数函数的概念

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# 对数函数的概念

一般地,我们把函数 $\gamma = \log _ { a } x ( a > 0$ ，且 $a \neq 1$ )叫做对数函数,其中 $x$ 是自变量,函数的定义域是 $( 0 , + \infty )$ ,值域是R.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例1★

下列函数是对数函数的是(

A. $y = \log 1 0 ^ { x }$ B. $\boldsymbol { y } = \log _ { 3 } \boldsymbol { x } ^ { 2 }$ C. $y = \ln x$ D. $y = \log _ { \frac { 1 } { 3 } } ( x - 1 )$ （204

# 学习笔记

# 变式1★

下列函数是对数函数的是(

A $y = \log _ { 3 } \left( x + 1 \right)$ （204 B. $\gamma = \log _ { a } \left( 2 x \right)$ （ $a > 0$ 且 $a \neq 1$ ） C. $\scriptstyle \gamma = \log _ { a } x ^ { 2 } ( a > 0$ 且 $a \neq 1$ ） D. $y = \lg x$

# 学习笔记

# 例2★★

对数函数 $f ( x )$ 的图象经过点 $\left( { \frac { 1 } { 4 } } , 2 \right)$ 则 $f ( x ) =$

# 学习笔记

# 变式2★★

已知对数函数的图象过点 $M ( 9 , 2 )$ ,则此对数函数的解析式为（ ）

A. $\scriptstyle \gamma = \log _ { 2 } x \left( x > 0 \right)$ B. $\gamma = \mathrm { l o g } _ { 3 } x ( x > 0 )$ （204 C. $\scriptstyle y = \log _ { \frac { 1 } { 3 } } x \left( x > 0 \right)$ $\mathrm { D } . \gamma = \log _ { 2 } ^ { 1 } x \left( x > 0 \right)$

# 学习笔记

# 例3★★★

函数 $f ( x ) = ( a ^ { 2 } - a + 1 ) \log _ { ( a + 1 ) } x$ 是对数函数，则实数 $a = { _ { \mathrm { ~ , ~ } } }$

# 学习笔记

# 变式3★★★

函数 $f ( x ) = ( a ^ { 2 } + a - 5 ) \log _ { a } x$ 为对数函数，则 $f { \biggl ( } { \frac { 1 } { 8 } } { \biggr ) }$ 等于（

A.3 B.-3 C. -log6 D. -log8

# 学习笔记

# 模块2 对数函数的图象与性质

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

对数函数 $y = \log _ { a } x \left( a > 0 \right. ,$ 且 $a \neq 1 )$ 的图象与性质

(1)图象与性质

<html><body><table><tr><td></td><td>a&gt;1</td><td>0&lt;a&lt;1</td></tr><tr><td>图象</td><td>y↑ x=1 iy=logx(a&gt;1） 0 (1,0）x -</td><td>y↑x=1 = = 可 (1.0) x y=logax(0&lt;a&lt;1）</td></tr><tr><td rowspan="5">性质</td><td colspan="2">①定义域为(0，+α）</td></tr><tr><td colspan="2">②值域为R</td></tr><tr><td colspan="2">③当x=1时,y=0,即函数图象过定点(1,0)</td></tr><tr><td>&lt;1时,y&lt;0</td><td>④当𝑥&gt;1时,y&gt;0;当0&lt;𝑥|④当𝑥&gt;1时,y&lt;0;当0&lt;𝑥&lt; 1时,y&gt;0</td></tr><tr><td>⑤在(0，+α）上是增函数</td><td>⑤在(0，+α）上是减函数</td></tr></table></body></html>

(2)指数函数与对数函数的性质比较(3)不同底数的对数函数图象比较

<html><body><table><tr><td></td><td>指数函数</td><td>对数函数</td></tr><tr><td>函数式</td><td>y =a²(a&gt;0,a≠1)</td><td>y =logax(a&gt;0,a≠1）</td></tr></table></body></html>

续表  

<html><body><table><tr><td></td><td>指数函数</td><td>对数函数</td></tr><tr><td>特点</td><td>底数是常数,指数是变量</td><td>底数是常数,真数是变量</td></tr><tr><td>定义域</td><td>（18，+8）</td><td>（0，+∞）</td></tr><tr><td>值域</td><td>（0，+∞）</td><td>（18，+8）</td></tr><tr><td>图象</td><td>y=ax →y y=ax |0&lt;a&lt;1 /a&gt;1 （0,1） 0</td><td>y→ y=logxa&gt;1 可 （1,0） y=logx 0&lt;a&lt;1</td></tr><tr><td>性质1</td><td>y&gt;0</td><td>x&gt;0</td></tr><tr><td>性质2</td><td>①0&lt;a&lt;1,当𝑥&lt;0时,𝑦 &gt; 1;当𝑥&gt;0时,0&lt;𝑦&lt;1 ②a&gt;1,当𝑥&lt;0时,0&lt;γ&lt;|②a&gt;1,当0&lt;x&lt;1时,𝑦&lt; 1;当𝑥&gt;0时,y&gt;1</td><td>①0&lt;a&lt;1,当0&lt;x&lt;1时, y &gt;0;当𝑥&gt;1时,y&lt;0 0;当𝑥&gt;1时,y&gt;0</td></tr><tr><td>单调性</td><td>当0&lt;α&lt;1时,在（-∞， +∞）上单调递减; 当a&gt;1时,在（18，+8） 上单调递增</td><td>当0&lt;a&lt;1时,在（-∞, +∞）上单调递减; 当a&gt;1时,在（18，+∞） 上单调递增</td></tr><tr><td>奇偶性</td><td>既不是奇函数也不是偶函数</td><td>既不是奇函数也不是偶 函数</td></tr></table></body></html>

1 y=logx y=logx -y=logx y=logx 由图象可得 $b > a > 1 > d > c > 0$

# 精讲精练

# 拍照批改秒判对错

# 例4★★

求函数的定义域.$\begin{array} { l } { ( 1 ) y = \log _ { 2 } \left( x ^ { 2 } - 4 x - 5 \right) ; } \\ { ( 2 ) y = \sqrt { \log _ { 0 . 5 } \left( 4 x - 3 \right) } . } \end{array}$

# 学习笔记

# 变式4

# （1）★★

函数 $y = \log _ { 2 } { \left( 9 - x ^ { 2 } \right) }$ 的定义域为( ）

A. $\{ x \mid - 3 < x < 3 \}$ （204 B. $\left\{ x \vert x < - 3 \right.$ 或 $x > 3 \left\{ \begin{array} { l l } { \begin{array} { r l r } \end{array} } \end{array} \right.$ C $\left\{ x \mid x < 3 \right\}$ D. $\left\{ x \mid x > - 3 \right\}$

# 学习笔记

# （2）★★

函数y=1og（2x-2） 的定义域为(

A $\{ x \mid x > 1 \}$ B $\left\{ x | x > 1 \right.$ $x \neq { \frac { 3 } { 2 } } \Bigg \}$ C $\left\{ x | x > 1 \right.$ $x \neq { \frac { 3 } { 2 } } \Bigg \}$ D $\left\{ x \vert x \neq { \frac { 3 } { 2 } } \right\}$

# 学习笔记

# 例5★★

若 $a > 0$ ，且 $a \neq 1$ ，函数 $y = \log _ { a } { ( x + 1 ) } + 1$ 的图象恒过定点 $P$ ，则 $P$ 点的坐标是（ ）

A.(1,0) B.(-2,0) C. (2,0) D. (0,1)

# 学习笔记

# 变式5★★

若 $a > 0$ ，且 $a \neq 1$ ，则函数 $y = \log _ { a } \left( 3 x - 5 \right) + 7$ 的图象过定点(

A. (3,5) B. (5,7) C. (3,7) D. (2,7)

# 学习笔记

# 例6★★

如图所示的三个对数函数的图象，下列选项正确的是( ）

A. $0 < c < b < 1 < a$ B. $0 < b < c < 1 < a$ C. $1 < b < c < a$ （204 D. $1 < c < b < a$ （204

# 学习笔记

![](images/fec2f21bdc9c44a926726b24f7d0f4eb7d44aadb429c7fa3c2bc2761b17c9d87.jpg)

# 变式6★★

如图所示的曲线是对数函数 $\gamma = \log _ { a } x$ 的图象.已知 $^ { a }$ 从3 $, { \frac { 4 } { 3 } } , { \frac { 3 } { 5 } } , { \frac { 1 } { 1 0 } }$ 中國取值,则相应曲线 $C _ { 1 } , C _ { 2 } , C _ { 3 } , C _ { 4 }$ 的 $^ { a }$ 值依次为（ ）

${ \sqrt { 3 } } , { \frac { 4 } { 3 } } , { \frac { 3 } { 5 } } , { \frac { 1 } { 1 0 } }$ $\mathbf { B } . \ { \sqrt { 3 } } \ { , } { \frac { 4 } { 3 } } , { \frac { 1 } { 1 0 } } , { \frac { 3 } { 5 } }$ C. ${ \frac { 4 } { 3 } } , { \sqrt { 3 } } , { \frac { 3 } { 5 } } , { \frac { 1 } { 1 0 } }$ D. ${ \frac { 4 } { 3 } } , \sqrt 3 , { \frac { 1 } { 1 0 } } , { \frac { 3 } { 5 } }$

![](images/7dc95632dc42424a70317c76c51643c22de3206b742e563ebea3540c2ec519fd.jpg)

![](images/348263548f98b95a4aca0bcc0f344ca53ae87afc05faa5f5954d53e58de2d636.jpg)

# 模块3对数的大小比较

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# 两个对数式的大小比较有以下三种情况

（1)底数相同；  
(2)真数相同;  
(3)底数不同，真数也不同.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例7★★★

比较下列各组中两个值的大小.

$\textcircled { 1 } \log _ { 3 } 1 . 9 , \log _ { 3 } 2$ ②l0g,4,l0g.14; ③log3,l0g.32； ④ log.Tr,log3. 141.

# 学习笔记

# 变式7

# （1）★

比较下列数的大小：$\begin{array} { r } { \log _ { 2 } \pi ( \mathrm { ~  ~ { ~ \mathbf ~ { ~ \phi ~ } ~ } ~ } ) \log _ { 2 } 0 . 9 . } \end{array}$

A.> B.< C.=

# 学习笔记

# (2)★

log20.3（ ) log0.20.3.

A.> B.< C.=

# 学习笔记

# （3）★★

log10.3（ )log20.8.

A.> B.< C.=

# 学习笔记

# 4）★★

loge( )log2.718.

A.> B.<C.= D.与 $a$ 的大小有关

# 学习笔记

# 例8★★★

若 $\log _ { m } 9 < \log _ { n } 9 < 0$ ，那么 $m _ {  } , n$ 满足的条件是( ）

A. $m > n > 1$ B. $0 < m < n < 1$   
C. $n > m > 1$ D. $0 < n < m < 1$

# 学习笔记

# 变式8★★★★

若实数 $a , b , c$ 满足 $\log _ { a } 3 < \log _ { b } 3 < \log _ { c } 3$ ，则下列关系中不可能成立的是（）

A. $0 < b < a < 1 < c$ B. $1 < b < a < c$ （204 C. $1 < c < b < a$ （204 D. $0 < a < 1 < c < b$

# 学习笔记

# 学习总结

![](images/211e9d56fc6558cbb20326b08ca857d3e9ace6fb9e48c93d9d48747e6654e00c.jpg)

# 提升篇你会遇见

函数 $f ( x ) = \log _ { 0 . 5 } \left( x ^ { 2 } - a x + 3 a \right)$ 的值域为R，则实数 $^ { a }$ 的取值范围是

![](images/25adc7a71f83f28c2067b7f59ed5c91e2a17f72e2c6fb22756dbc2c1d15a5475.jpg)

# 学而思秘籍系列图书|数学

# 思维培养

![](images/294c27d978d9da96b70388791c9a2cac7cab2cca92033fad1a7a267e73020b57.jpg)

# 小学秘籍系列

学而思积淀近20年教研经验，培养受益一生的能力。

# 思维提升

![](images/86b92abae709450f0375edf788259cca0cd267659b6633feae7cd8db55096865.jpg)

# 初中秘籍系列

全面覆盖初中基础知识和重难点，帮助学生夯实基础，拓展认知。

# 思维突破

.2 学而思秘籍 集合的帜  
高中数学  
思维突破-2-41  
++ ....

# 高中秘籍系列

全面覆盖高中基础知识和重难点，帮助学生提升能力，突破思维。

# 学而思秘籍系列图书|语文

# 提升素养

能力训练

![](images/9fdd4760f395aad122b9f7343fba39d68d66c9addb25866bd06e2603c078539c.jpg)

# 小学秘籍系列

5大模块+2条主线，能力与素养双向提升。

![](images/ce7f5db2b136189c6f9d1f37511ae440d8bdd35ebd48aefdb2f69c5fe7170494.jpg)

# 初中秘籍系列

融合课改四大核心素养，培养爱阅读、 善写作、勤思考、会学习的学生。

# 创新体系|真题研习

![](images/ade0ff485b41ba6b78db7d2d359e744a2850bbfd4ac03479c970bdf4da275165.jpg)

# 思维创新大通关 数学

攻克数学思维难题，通向理想中学。

# 大家一起来“升级

# 参与方式

您在使用本书时，如有任何疑问或对图书有任何建议，请扫码进行反馈，并查看反馈采纳结果。

# 奖励

您的反馈一经采纳，我们将会送出总价值35元的图书抵扣券（相同内容的反馈，依据反馈时间，奖励前三位）。请扫码关注公众号，并在对话框中发送反馈时填写的手机号，领取抵扣券。

![](images/8a0794cc8e95e78b1405c29c6385a829eaa5a0e4654d83458e1fcb9cfc852dbe.jpg)

# 合理规划学习时间

先自己定一个目标，即制定半年学习规划。

![](images/000437d5891f381c6afb5383be73f89d1ca6a5b3231d1cb6cb99f58b4ac80a53.jpg)

2 再将學細化到每一5个考点）。

3 配套课堂巩固的练习， 让学习更有效！

![](images/d8d7bc4b4b8b796ab6671af5f4b0ab92055fa7665b3d4a277cddefbb0ba5d9b0.jpg)  
·共6级·每级17-26讲