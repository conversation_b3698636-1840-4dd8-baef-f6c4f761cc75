---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 中等
estimated_study_time: 12
source_file: L4-17-计数问题的基本方法.md
title: L4-17-计数问题的基本方法
type: skill
---

# 计数问题的基本方法

一本一讲，共23讲（建议5个月学习）

5个考点+14个题型+22个视频

每周一讲（建议学习时间90分钟）

![](images/a19c9187b37d645795eb4f0d2d8ad123584c02465dc5be55f910b640f33b0ec4.jpg)

# 视频内容研发团队

学而思优秀老师

学而思优秀老师和一线高级教师联合创作本书试题，并精心录制讲解视频学而思图书APP扫码即可观看

![](images/6a669edfc8b1bef9faa619f2128914e2ca5319b677737a5223ac8bea2483b69d.jpg)

# 傅博宇 老师

毕业于北京大学元培学院  
网校和北大数院高考数学研究联合课题组成员；  
网校高中创新产品部负责人；  
荣获学而思网校“桃李满天下奖”“出类拔萃奖”等；腾讯网中国好老师；  
青少年教育导师认证；  
科学家长观体系的创立者

# 王侃老师

鼎

毕业于北京大学数学系  
学而思网校高中数学教研奠基人；  
学而思网校高中数学S级教师；  
荣获学而思网校“突出贡献奖” “桃李天下奖”等；擅长总结题型特点，提炼思想方法；  
擅长分层教学，因材施教

# 付恒岩 老师

![](images/b51d9c25137322b6b4d1c38ddd103a235fab09e3517e05c9c30289153705d871.jpg)

毕业于大连理工大学  
网校高中部理科主讲岗后培训师；  
2020年荣获学而思网校最具魅力奖；  
2019年、2020年荣获学而思网校诲人不倦奖；2020年荣获学而思网校高考优秀评卷人；  
2021年担任新浪教育高考数学直播解析特邀嘉宾；“停课不停学”公益课高中数学主讲老师

![](images/534ce9c799c0a6a9019d3f325e5afc2036fc36a7402ca09ff6b2e0f999d52ef2.jpg)

# 武洪姣 老师

14年线上线下教学经验；  
学而思网校高中理科教研负责人；  
学而思高中数学特级教师;  
在教学的过程中擅长归纳题型，方法和技巧;  
在高中数学模块中最擅长讲解圆锥曲线和导数；  
无论你是从小数学不好，还是数学一直拔尖，都可以在武老师的课堂上收获很多

4级

# 计数问题的基本方法

一本一讲，共23讲（建议5个月学习）

5个考点 $+ 1 4$ 个题型 $+ 2 2$ 个视频

每周一讲（建议学习时间90分钟）

![](images/3066f22d995a10174cd2eb6e413d8a98ac9fb4d06941b88fccb74f19d1425594.jpg)

# 预习篇

第1讲导数的概念与运算 提升篇第 $^ 2$ 讲导数与函数的单调性第8讲导数的运算与几何意义第 $^ 3$ 讲导数与函数的极值、最值第4讲复数 第 $^ { 9 }$ 讲利用导数研究单调性第10讲极值与最值第5讲计数原理与排列组合第11讲零点个数问题第6讲条件概率与事件的独立性第12讲隐零点方法第 $7$ 讲离散型随机变量第13讲单变量恒成立问题第14讲双变量恒成立问题第15讲端点效应第16讲构造合适的函数  
模块1 枚举法 1  
模块2 加乘原理 3 第17讲计数问题的基本方法  
模块3 典型的排列问题 6第18讲计数重点考法归纳第19讲二项式定理第20讲概率进阶第21讲离散型随机变量及其分布第22讲典型分布列第23讲成对数据的统计分析参考答案

# 模块1枚举法

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 精讲精练

# 拍照批改秒判对错

# 考点1：枚举法

# 例1★★

用10元.5元和1元来支付20元钱的书款,不同的支付方法种数为（ ）.

A.3 B.5 C.9 D.12

# 学习笔记

# 例2★★★

设A是整数集的一个非空子集,对于 $k \in A$ ,如果 $k - 1 \not \in A$ 且 $k + 1 \not \in A$ ，那么称 $k$ 是A的一个“孤立元”.给定 $S = \left\{ 1 , 2 , 3 , 4 , 5 , 6 , 7 , 8 \right\}$ ，则 $S$ 的3个元素构成的所有集合中,其元素都是“孤立元"的集合个数是（ ）

A.6 B.15 C.20 D.25

# 学习笔记

# 例3★★★★

甲、乙、丙三人互相传球,由甲开始发球,并作为第一次传球,经过5次传球后，球仍回到甲的手中，则不同的传球方式有 种.

# 学习笔记

# 达标检测1★★★

若数列 $\left\{ a _ { n } \right\}$ 满足规律： $a _ { 1 } > a _ { 2 } < a _ { 3 } > \cdots < a _ { 2 n - 1 } > a _ { 2 n } < \cdots$ ,则称数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 为波浪数列,将1,2,3,4,5这五个数排成一个无重复数字的波浪数列,则排法种数为（ ）

A. 12 B.14 C.16 D.18

# 学习笔记

# 模块2加乘原理

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# $\textcircled{9}$ 分类加法计数原理

完成一件事有两类不同方案,在第1类方案中有 $m$ 种不同的方法,在第2类方案中有 $n$ 种不同的方法,那么完成这件事共有 $N = m + n$ 种不同的方法.

# 分步乘法计数原理

完成一件事需要两个步骤,做第1步有 $m$ 种不同的方法,做第2步有 $n$ 种不同的方法，那么完成这件事共有 $N = m \times n$ 种不同的方法.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点2：加乘原理

# 例4★★★

设4名学生报名参加同一时间安排的3项课外活动方案有 $a$ 种,这4名学生在运动会上共同争夺100 米、跳远、铅球3项比赛的冠军的可能结果有 $b$ 种,则 $( a , b )$ 为（ ）

A. $( 3 ^ { 4 } , 3 ^ { 4 } )$ B.(4³,34) C.(34，4³) D.（A,A)

# 学习笔记

# 例5★★★

某城市的电话号码,由六位升为七位(首位数字均不为零),则该城市可增加的电话部数是（ ）

A. $9 \times 8 \times 7 \times 6 \times 5 \times 4 \times 3$ B. $8 \times 9 ^ { 6 }$ C. $9 \times 1 0 ^ { 6 }$ （204 D.8.1 ×106

# 学习笔记

# 例6★★★

3张1元币,4张1角币，1张5分币,2张2分币，可组成 种不同的币值(1张不取,即0元0角0分不计在内).

# 学习笔记

# 达标检测2★★★

一植物园参观路径如下图所示,若要全部参观并且路线不重复,则不同的参观路线种数为（

![](images/b7d6eb146b4d5af75c59cda729bad171491897268e7bad4c33b1cf6ef7cd6f03.jpg)

A.6 B.8 C.36 D.48

# 学习笔记

# 模块3典型的排列问题

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# $\textcircled { 1 }$ (相邻条件)捆绑法

指在解决对于某几个元素要求相邻的问题时,先整体考虑,将相邻元素视作一个整体参与排序，再单独考虑这个整体内部各元素间顺序.其首要特点是相邻.

# $\textcircled { 2 }$ (不相邻条件)插空法

指在解决对于某几个元素要求不相邻的问题时,先将其他元素排好,再将指定的不相邻的元素插入已排好元素的间隙或两端位置.其首要特点是不相邻.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点3：相邻与不相邻问题

# 例7★★

六个人站一排,甲、乙必须相邻,共有 种站法.

# 学习笔记

# 达标检测3★★

六个人站一排,甲、乙必须相邻,丙、丁必须相邻,共有 种站法.

# 学习笔记

# 例8★★★

六个人站一排,甲、乙、丙均不相邻,则站法种数为( ）

A. 144 B.120 C. 96 D.64

# 学习笔记

# 例9★★★★

甲、乙、丙、丁、戊5人站成一排,要求甲、乙均不与丙相邻,则不同的排法有（）

A.72种 B.54种 C.36种 D.24种

# 学习笔记

# 知识点睛

# 定序问题

对于定序问题,可先不考虑顺序限制,排列后,再除以定序元素的全排列.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点4：定序问题

# 例10★★

$A , B , C , D , E$ 排成一列,要求 $A , B , C$ 在排列中顺序为"A、B、C”或“ $C , B$ 、$A ^ { \prime \prime }$ （可以不相邻)，这样的排列有（

A.12种 B.20种 C.40种 D.60种

# 学习笔记

# 例11★★★★

某车队有7辆车,现在要调出4辆,再按一定顺序出去执行任务，要求甲、乙两车必须参加,而且甲车比乙车先开出,那么不同的调度方案有种.

# 学习笔记

# 达标检测4★★★

甲、乙、丙3位志愿者安排在周一至周五的5天中参加某项志愿者活动,要求每人参加一天且每天至多安排一人，并要求甲安排在另外两位前面，则不同的安排方法共有（ ）

A.20种 B.30种 C.40种 D.60种

# 学习笔记

# 知识点睛

# $\textcircled { 4 9 }$ 特元特位问题(特殊优先原则）

(1)特点:排列时,某个(或某些)元素一定在(或一定不在)某个(或某些)位置.  
(2)基本原则：特殊元素(或特殊位置)优先原则.  
(3)解题思路：$\textcircled{1}$ 以元素为主体，即先满足特殊元素的要求，再考虑其他元素.$\textcircled{2}$ 以位置为主体,即先满足特殊位置的要求,再考虑其他位置.$\textcircled{3}$ 先不考虑附加条件，正难则反.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点5：特元特位问题

# 例12★★★

1,2,3,4,5能组成 个没有重复数字的五位奇数；  
0,1,2,3,4能组成 个没有重复数字的五位奇数.

# 学习笔记

# 例13★★★

某地奥运火炬接力传递路线共分6段,传递活动分别由6名火炬手完成.如果第一棒火炬手只能从甲、乙、丙三人中产生,最后一棒火炬手只能从甲、乙两人中产生，则不同的传递方案共有 种.（用数字作答）

# 学习笔记

# 例14★★★★

某学校周五安排有语文、数学、英语、物理、化学、体育六节课,要求体育不排在第一节课,数学不排在第四节课,则这天课程表的不同排法种数为（）

A. 600 B.288 C. 480 D. 504

# 学习笔记

# 达标检测5★★★★

某台小型晚会由6个节目组成,演出顺序有如下要求：节目甲必须排在前两位,节目乙不能排在第一位,节目丙必须排在最后一位.则该台晚会节目演出顺序的编排方案共有（ ）

A.36种 B.42 种 C.48种 D.54种

# 学习笔记

# 直击高考

从1,3,5,7,9中任取2个数字,从0,2,4,6中任取2个数字,一共可以组成 个没有重复数字的四位数.（用数字作答)

# 自我测试

# 拍照批改秒判对错

# 测试1

将长为15 的木棒截成长为整数的三段,使它们构成一个三角形的三边，则得到的不同三角形的个数为（ ）

A.8 B.7 C.6 D.5

# 测试2

$A , B , C , D , E$ 五个字母排成一排,若 $A , B$ 必须不相邻， $D , E$ 必须相邻,则不同的排法种数为（ ）

A.8 B.12 C.16 D.24

# 测试3

某人将密码"19 923”记错密码数字顺序,他可能犯的错误次数最多是（假定错误不重犯)（ ）

A.120 B.119 C. 60 D.59

# 学而思秘籍系列图书数学

# 思维培养

![](images/3bda468d4f439cdc3ae5c4146f611dc374895745e530cc0b8f3120b54dffc238.jpg)

# 小学秘籍系列

学而思积淀近20年教研经验，培养受益一生的能力。

# 思维提升

![](images/d3000704fb4d229a3d5979d4eeb218943497ea230c2208e785e22a1126523ba7.jpg)

# 初中秘籍系列

全面覆盖初中基础知识和重难点，帮助学生夯实基础，拓展认知。

# 思维突破

![](images/f484df5069d79b5edb285876b74039a8173403d00ba09436707a0329988a2978.jpg)

# 高中秘籍系列

全面覆盖高中基础知识和重难点，帮助学生提升能力，突破思维。

# 学而思秘籍系列图书|语文

# 提升素养

![](images/8f309e57169924030c390a1bcf54b13ec4f3ef9634536b251914ac69f3e68d8f.jpg)

# 小学秘籍系列

5大模块 $^ +$ 2条主线，能力与素养双向提升。

# 能力训练

![](images/a73f77511b62768c2730805016e2fded6effb9282edb510b847e373c0bcb8f6e.jpg)

# 初中秘籍系列

融合课改四大核心素养，培养爱阅读、 善写作、勤思考、会学习的学生。

# 创新体系|真题研习

![](images/d4d222b2c280d1ad3822b2e33486b8003073daafda9914c7b72888fbbc75c128.jpg)

# 思维创新大通关数学

攻克数学思维难题，通向理想中学。

# 大家一起来“升级

# 参与方式

您在使用本书时，如有任何疑问或对图书有任何建议，请扫码进行反馈，并查看反馈采纳结果。

# 奖励

您的反馈一经采纳，我们将会送出总价值35元的图书抵扣券（相同内容的反馈，依据反馈时间，奖励前三位）。请扫码关注公众号，并在对话框中发送反馈时填写的手机号，领取抵扣券。

![](images/7d6acd16ebb0dc3211d5d2b0b454d209ac3a7dcb1d0a207f42e5c334d6eb7b6d.jpg)

# 合理规划学习时间

先自己定一个目标，即制定半年学习规划。

![](images/bf1e18a940d9a19d50a0ee053db7e49b477a8d2a646ab1f3c5b845e1f3628c0c.jpg)

2 再将目标细化到每一周，每周学习一本（平均5个考点）。

3 配套课堂巩固的练习， 让学习更有效！

![](images/de65f8fb3dda65d3895583ba010746c445a0e683d999e95f7663d31fbee22908.jpg)  
·共6级·每级17-26讲