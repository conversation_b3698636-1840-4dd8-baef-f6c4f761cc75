---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 120
source_file: 【高中数学】【凤凰新高考】高中数学大梳理---基本题型与解题方法大梳理宝典（干货版）（109页）（完整版）（龙艳文老师著）.md
title: 【高中数学】【凤凰新高考】高中数学大梳理---基本题型与解题方法大梳理宝典（干货版）（109页）（完整版）（龙艳文老师著）
type: skill
---

# 赠 册

高中学生必备宝典

1本大梳理+1本干货

180种基本类型，覆盖高考考点

助你理清高中数学脉络，掌握解题方法，备战高考

# 基本类型与解题方法高中数学大梳理

（干货版）

龙艳文著

# 类型一：集合的表示

方法：优先判断集合的表示形式.

注意：下面四种形式的区别，  
$A = \{ x \mid y = x ^ { 2 } + 1 \}$   
$B = \{ y | y = x ^ { 2 } + 1 \}$ ，  
$C = \{ ( x , y ) | y = x ^ { 2 } + 1 \} ,$   
$D = \{ y = x ^ { 2 } + 1 \}$

# ■类型二：集合子集

方法：与不等式有关的集合问题，通过画数轴分析.

注意： $\textcircled{1} B { \subseteq } A$ ， $A \cap B { = } \emptyset$ 时，优先考虑空集情况；$\textcircled{2}$ 端点的取舍；$\textcircled{3}$ 不等式之间交或并的关系；$\textcircled{4}$ 二次方程形式的单元素集合要考虑 $\varDelta = 0$

# 类型三：集合相等

注意：集合求解后一定要检验，如考虑集合元素的互异性.

# 类型四：集合关系与运算

方法：通过画数轴分析.  
结论： $A { \subseteq } B {  } A \cap B { = } A$ ; $A \subseteq B \Longleftrightarrow A \cup B = B$   
注意： $\textcircled{1}$ 树立端点意识,即对端点进行检验；$\textcircled{2}$ 代人集合求解后要检验.

# 类型五：Venn图的应用

方法：利用Venn图的直观性.

# 类型一：充分条件、必要条件、充要条件

情形1:充分或必要条件的判断.

结论： $\textcircled{1}$ 如果 $\phi { \Rightarrow } q$ ，且 $q \not \Rightarrow \boldsymbol { \phi }$ ,那么称 $\boldsymbol { \mathscr { P } }$ 是 $q$ 的充分不必要条件；$\textcircled{2}$ 如果 $\phi \not \Rightarrow q$ ，且 $q \Rightarrow \boldsymbol { \phi }$ ,那么称 $\boldsymbol { \phi }$ 是 $q$ 的必要不充分条件；$\textcircled{3}$ 如果 $\phi { \Rightarrow } q$ ，且 $q \Longrightarrow { p }$ ,那么称 $\boldsymbol { \phi }$ 是 $q$ 的充要条件,记作 $\phi { \Longleftrightarrow } q$ ;$\textcircled{4}$ 如果 $\phi \not \Rightarrow q$ ，且 $q \not \Rightarrow \boldsymbol { \phi }$ ,那么称 $\boldsymbol { \mathscr { P } }$ 是 $q$ 的既不充分又不必要条件.

注意：找特殊情况来否定.

情形2：从集合观点看充分或必要条件.

结论：设集合 $A = \{ x \mid x$ 满足条件 $\boldsymbol { \mathscr { p } } \}$ ， $B { = } \{ x \mid x$ 满足条件 $q \}$

$\textcircled{1}$ 若 $A \subseteq B$ ，则 $\boldsymbol { \phi }$ 是 $q$ 的充分条件；   
$\textcircled{2}$ 若 $B \subseteq A$ ，则 $\boldsymbol { \mathscr { P } }$ 是 $q$ 的必要条件；   
$\textcircled{3}$ 若 $A = B$ ，则 $\boldsymbol { \mathscr { P } }$ 是 $q$ 的充要条件. 情形3：充要条件证明.   
方法：证明充要性条件时，既要证明充分性，又要证明必要性.

# 类型二：全称量词命题与存在量词命题

方法：（1）判断是否为存在或全称命题，

$\textcircled{1}$ 全称量词：“所有的”“任意一个"等，用 $\forall$ 表示；全称量词命题 $\boldsymbol { \phi }$ ： $\forall x \in M$ ， $\phi ( x )$ ;全称量词命题 $\boldsymbol { \phi }$ 的否定 $\lnot p$ ： $\exists x \in M$ ， $\lnot p ( \boldsymbol { x } )$   
$\textcircled{2}$ 存在量词：“存在一个”“至少有一个"等，用表示；存在性量词命题 $\boldsymbol { \phi }$ ： $\exists \in M$ ， $\phi ^ { ( \boldsymbol { x } ) }$ ;

存在性量词命题 $\boldsymbol { \phi }$ 的否定 $\neg \ p \colon \forall x \in M , \ \neg \ p ( x ) .$

(2) $\textcircled{1}$ 对一个命题进行否定就得到一个新的命题，这两个命题的关系是“一真一假"或"此假彼真”$\textcircled{2}$ 从集合观点看，原命题的否定是原命题对应集合的补集.

# 第 章 从函数观点看一元二次方程与一元二次不等式

# □ 类型一：一元二次不等式

情形1:解一元二次不等式.

方法： $\textcircled{1}$ 判断方程根是否存在,若存在求出方程的根；$\textcircled{2}$ 比较根的大小，结合二次函数图象与 $_ { x }$ 轴的关系,求解二次不等式.

注意：开口方向和等号.

情形2：已知一元二次不等式的解集.

方法：若不等式 $a x ^ { 2 } + b x + c < 0 ( a > 0 )$ 的解集为 $( \alpha , \beta )$ ，则 $\alpha \bullet \beta$ 为方程$a x ^ { 2 } + b x + c = 0$ 的两根.

注意：考虑解集的形式与二次项系数正负的关系.

# 类型二：分式不等式

方法：(1) ${ \frac { f ( x ) } { g ( x ) } } > 0$ 等价于 $f \left( x \right) g \left( x \right) > 0 ; \frac { f ( x ) } { g ( x ) } < 0$ 等价于$f ( x ) g ( x ) { < } 0 .$

（2）（x）> ${ \frac { f ( x ) } { g ( x ) } } { \geqslant } 0$ ≥0等价于/f(x）g(x）≥0，f（x） ≤0等价于{f（x）g(x）≤0，

注意：考虑分母不为0的情况.

# 类型三：不等式恒成立

情形1:二次不等式恒成立问题.

方法1:结合二次函数图象分析恒正或恒负情况.  
注意： $\textcircled{1}$ 二次项系数为0、正和负的情况； $\textcircled{2} \Delta$ 为0、正和负的情况.  
方法2：参变分离法.情形2：一次不等式恒成立问题.  
方法：结合一次函数图象分析.

$\textcircled{1}$ 若不等式 $a x + b \geqslant 0$ 对任意 $x \in [ m , n ]$ 恒成立，则 $\begin{array} { c } { { \left. \int f ( m ) \geq 0 \right. } } \\ { { \left. \begin{array} { l l l } { { \large ( f ( n ) \geq 0 . } } \end{array} \right. } } \end{array}$ $\textcircled{2}$ 若不等式 $a x + b \leqslant 0$ 对任意 $x \in [ m , n ]$ 恒成立，则 $\left\{ { \begin{array} { l } { f ( m ) \leqslant 0 , } \\ { f ( n ) \leqslant 0 . } \end{array} } \right.$ 注意：比较(1)和(2),辨别主元的不同.

# ■类型四：二次不等式分类讨论

方法：一元二次不等式分类讨论依次从四个方面考虑：$\textcircled{1}$ 分析二次项系数为0、正和负的情况；  
$\textcircled{2}$ 判断二次方程根是否存在(优先考虑用十字相乘法求根);  
$\textcircled{3}$ 比较二次方程根的大小；  
$\textcircled{4}$ 结合二次函数图象求解不等式.

注意：二次项系数正负和不等号方向的关系.

# 类型一：基本不等式求最值

情形1:利用基本不等式求最值.

方法：如果 $a , b$ 是正数，那么 ${ \sqrt { a b } } \leqslant { \frac { a + b } { 2 } }$ (当且仅当 $a = b$ 时，等号成立)；基本不等式的变形： $a + b \geqslant 2 { \sqrt { a b } }$ 或 $a b { \leqslant } ( \frac { a + b } { 2 } ) ^ { 2 }$ （

当和为定值时，可求积的最大值;当积为定值时，可求和的最小值，即“一正二定三相等”

注意：当原式的和(或积)不为定值形式时，“凑"和(或积)为定值形式.

情形2：利用基本不等式求较复杂形式最值.

方法：将原式转化为(部分)满足“一正二定三相等"的形式.

注意：等号成立满足一定条件,若取不到等号,可利用 $f ( x ) = x + \frac { a } { x } ( a >$

0)型函数的单调性.(详见"类型二")

类型二： $f ( x ) = x + { \frac { a } { x } }$ 型函数

方法：对于f(x)=x+a  
$\textcircled{1}$ 当 $a = 0$ 时， $f ( x ) { = } x$   
$\textcircled{2}$ 当 $a < 0$ 时，如图 $4 \div 1 , f ( x )$ 在 $( - \infty , 0 ) , ( 0 , + \infty )$ 上为增函数.

![](images/8c184e18e377b3de9c6fea4f3b413696739c352429a31f7dc89661a1ab29bbe0.jpg)  
图4-1

![](images/f56edd1708d1d7c755d684dd95961c3f2fc40b916c6058c4fb4947a1c82020f7.jpg)  
图4-2

$\textcircled{3}$ 当 $a > 0$ 时,如图 $4 \div 2 , f ( x )$ 在 $( - \infty$ ， $- \sqrt { a } )$ ， $( { \sqrt { a } } , + \infty )$ 上为增函数；在 $( - \sqrt { a } \ , \ 0 )$ ，（0, ${ \sqrt { a } } ~ )$ 上为减函数.

注意:在解答题中利用函数 $f \left( x \right) = x + \frac { a } { x }$ 的单调性时,要先用导数(或定义)进行证明.

# □ 类型三：一元变量最值问题

方法：对于 $f ( x ) { = } { \frac { a x ^ { 2 } + b x + c } { m x + n } }$ （或 $f ( x ) = { \frac { m x + n } { a x ^ { 2 } + b x + c } } )$ 型，令 $m x + n = t$ 进行换元，转化为 $f ( x ) = x + { \frac { a } { x } }$ 型问题.

变式1：对于 $f ( x ) = a x + b + { \frac { m x + n } { p x + q } }$ 或 $f ( x ) = a x + b + { \frac { m x ^ { 2 } + n x + l } { p x + q } } )$ 型，令 $p x + q = t$ ：

变式2：对于f（x)= $f ( x ) { = } { \frac { a x ^ { 2 } + b x + c } { m x ^ { 2 } + n x + l } }$ 型，转化为 $f ( x ) { = } M { + } { \frac { \not p x + q } { m x ^ { 2 } + n x + l } }$ mx²+nx+2，再令 $p x + q = t$

# 类型四：多元变量最值问题

情形1:形如,已知含有 $a + b$ ， $a b$ ， $a ^ { 2 } + b ^ { 2 }$ 的形式,求其中一个的最值.

方法：转化为下列三个不等关系中的一种.

三个不等关系：

$\textcircled{1}$ a， $b \in \mathbf { R }$ ， $a ^ { 2 } + b ^ { 2 } { \geqslant } 2 a b$ ,当且仅当 $a = b$ 时取等号；   
$\textcircled{2} a > 0$ ， $b > 0$ ， $a + b \geqslant 2 { \sqrt { a b } }$ ,当且仅当 $a = b$ 时取等号；   
③a， $b \in \mathbf { R }$ ， $\frac { a ^ { 2 } + b ^ { 2 } } { 2 } { \geq } ( \frac { a + b } { 2 } ) ^ { 2 }$ a+b)²,当且仅当a=b时取等号.

上述三个不等式揭示了 $a ^ { 2 } + b ^ { 2 } , a b , a + b$ 三者间的关系.

情形2：形如， $\textcircled{1}$ 已知 $a x + b y = M$ ，求 ${ \frac { m } { x } } + { \frac { n } { y } }$ 的最值; $\textcircled{2}$ 已知 ${ \frac { a } { x } } + { \frac { b } { y } } = M$ 求$m x + n y$ 的最值.

方法：两式相乘展开后用基本不等式.

情形3：形如,已知 $a x + b y + c x y = 0$ ，求 $\phi x + q y$ 的最值.

方法1:将ax+by+cxy=0同除以xy,化为°+ $\frac { a } { x } + \frac { b } { y } = M$ 形式.

方法2：消元(注意元范围).

情形4:形如,已知 $a x + b y + c x y + d = 0$ ，求 $p x + q y$ 的最值.

方法1:将 $a x + b y + c x y + d = 0$ 化为 $( \ p x + q ) ( m y + n ) = M$ 形式.

方法2：消元(注意元范围).

# 方法选择

优先分析是否直接(或变形、换元、配凑等)符合上述结构形式，再考虑通过消元转化为一元函数问题.

# 第1讲函数概念及其性质(1)

函数概念、分段函数、解析式、定义域

# 一类型一：函数的概念

情形1:函数的判断.

方法：若 $f ( x )$ 为函数,则对于定义域内的任意一个 $_ { x }$ 都只有唯一一个 $y$ 与之对应.

情形2:同一函数的判断.

方法：若为同一函数,则满足定义域、解析式均相同.

# 类型二：分段函数

情形1:分段函数求值.

方法： $\textcircled{1}$ 由自变量范围确定分段对应的函数表达式；$\textcircled{2}$ 对于复合函数求值，采取由内向外求.

情形2:分段函数的处理方法.

方法： $\textcircled{1}$ 分段处理，如分类讨论分析；$\textcircled{2}$ 整体处理,如画函数图象分析.

注意：分段函数中自变量 $_ { x }$ 的分段区间不重复、不遗漏.

# 类型三：解析式求法

方法1:换元法、配凑法,适用于已知 $f ( g ( x ) )$ ，求 $f ( x )$ 的情形.

方法2:待定系数法,适用于已知函数类型的情形.

# 结论：

一次函数一般设为： $f ( x ) { = } a x { + } b ( a { \neq } 0 )$

二次函数一般设为：

$\textcircled{1}$ 一般式： $f ( x ) { = } a x ^ { 2 } { + } b x { + } c ( a { \neq } 0 )$ $\textcircled{2}$ 顶点式： $f ( x ) { = } a ( x { - } h ) ^ { 2 } { + } k ( a { \neq } 0 )$ $\textcircled{3}$ 零点式： $f ( x ) { = } a \left( x { - } x _ { 1 } \right) ( x { - } x _ { 2 } ) ( a { \neq } 0 ) .$

注意：方程恒成立问题,如 $a x ^ { 2 } + b x + c = 0$ 对任意 $x \in \mathbf { R }$ 恒成立，则 $a = b =$ $c = 0 .$ （注意与解方程 $a x ^ { 2 } + b x + c = 0$ 的区别)

方法3：方程组法，适用于如下三种形式：  
$\textcircled { 1 } a f ( x ) + b f ( - x ) = \square ;$   
$\textcircled { 2 } a f ( x ) + b f \big ( \frac { 1 } { x } \big ) = \bigsqcup ;$   
$\textcircled{3}$ 已知 $f ( x )$ 为奇函数， $g \left( x \right)$ 为偶函数， $a f ( x ) + b g ( x ) = \square$

# 类型四：定义域求法

方法1:自然型,函数的定义域是使解析式有意义的自变量的取值集合.

注意：函数定义域必须写成集合或区间形式.

方法2：已知 $f ( x )$ 的定义域为 $D$ ，求 $f ( g ( x ) )$ 的定义域.由 $g \left( x \right) \in D$ ，解得 $_ { \mathcal { X } }$ 的范围即为 $f ( g ( x ) )$ 的定义域.

方法3：已知 $f ( g \left( x \right) )$ 的定义域为 $D$ ，求 $f ( x )$ 的定义域.由 $x \in D$ ,求出 $g \left( x \right)$ 的值域,即为 $f ( x )$ 的定义域.

特例：函数的定义域为 $\mathbf { R }$ 问题.

方法：转化为不等式恒成立问题.对于形如 $a x ^ { 2 } + b x + c > 0$ 对任意 $x \in \mathbf { R }$ 恒成立问题,结合二次函数图象分析.

注意： $\textcircled{1}$ 优先考虑二次项系数为0、正和负的情况；$\textcircled{2} \Delta \leqslant 0$ 还是 $\Delta < 0$

补充：不等式恒成立问题.

方法：优先用参变分离法.

如：对任意 $x \in D$ ， $f ( x ) { \geqslant } g \left( x \right)$ 恒成立，分离参数得：$\textcircled{1}$ 对任意 $x \in D$ ， $a \geqslant H ( x )$ 恒成立，则 $a \geqslant H ( x ) _ { \mathrm { m a x } }$ $\textcircled{2}$ 对任意 $x \in D$ ， $a { \leqslant } H ( x )$ 恒成立，则 $a \leqslant H \left( x \right) _ { \mathrm { m i n } }$

# 第2讲 函数概念及其性质（2）

值域

方法1:图象法,适用于能作出图象的值域问题(一切尽在图形中).

方法2：单调性法,适用于能判断出单调性的函数.

方法3：复合函数法，适用于求 $f ( g \left( x \right) )$ 的值域.先求 $g \left( x \right)$ 的值域，再以$g \left( x \right)$ 的值域作为 $f ( x )$ 的定义域,求出 $f ( x )$ 的值域即可.

方法4:部分分式法,适用于分子、分母次数相同的分式函数.如 $f \left( x \right) =$ $_ { b - } { \underline { { a d } } }$ $\frac { a x + b } { c x + d }$ 的形式，化为 $f ( x ) { = } \frac { a } { c } { + } \frac { c } { c x { + } d } \big ( x { \neq } { - } \frac { d } { c } \big )$ 形式,再结合图象分析.

方法5:换元法,对复杂(或特定)形式进行换元.

注意：换元优先考虑元的范围.

# 第3讲 函数概念及其性质(3)

单调性、奇偶性

# ■类型一：判断函数奇偶性

方法1:定义法，先判断定义域是否关于原点对称，再判断 $f \left( - x \right)$ 与$f ( x )$ 的关系.

结论：对于 $f ( x ) g ( x )$ ，

$\textcircled{1}$ 若 $f ( x )$ 与 $g \left( x \right)$ 同奇或同偶,则 $f ( x ) g ( x )$ 为偶函数；  
$\textcircled{2}$ 若 $f ( x )$ 与 $g \left( x \right)$ 一奇一偶,则 $f ( x ) g ( x )$ 为奇函数.  
方法2：图象法，判断图象关于 $y$ 轴或原点对称.

# 方法选择

优先考虑用图象法，用定义法先判断定义域是否关于原点对称.

注意：证明函数奇偶性只能用定义法.

# 类型二：已知函数奇偶性求值

方法1:若函数为奇函数且0在定义域内,则 $f ( 0 ) { = } 0$

方法2:特殊值法,如奇函数用 $f ( - 1 ) { = } { - } f ( 1 )$ ,偶函数用 $f ( - 1 ) { = } f ( 1 )$

方法3:定义法，即奇函数用 $f ( - x ) { = } { - } f ( x )$ ,偶函数用 $f ( - x ) { = } f ( x )$ ，化成方程恒成立的形式.

# 方法选择

优先用方法1,再用方法2,注意检验.但如果是解答题，必须用定义证明其奇偶性.

# 类型三：判断函数单调性

方法1:图象法.

注意：图象断开的函数的单调区间不能写成并集形式.

方法2:复合函数法，即 $f ( g ( x ) )$ 的单调性为"同增异减”

设复合函数 $_ { y } = f ( g ( x ) )$ ,其中 $u = g \left( x \right) ,$ $A$ 是 $_ { y } = f ( g ( x ) )$ 定义域的某

个子区间， $B { = } \{ u \vert u { = } g ( x )$ ， $x \in A \}$

$\textcircled{1}$ 若 $u = g \left( x \right)$ 在 $A$ 上是增(或减)函数， $y { = } f ( u )$ 在 $B$ 上是增(或减)函数，则函数 $_ { y } = f ( g ( x ) )$ 在 $A$ 上是增函数；

$\textcircled{2}$ 若 $u = g \left( x \right)$ 在 $A$ 上是增(或减)函数， $\scriptstyle y = f ( u )$ 在 $B$ 上是减(或增)函数,则函数 $_ { y } = f ( g ( x ) )$ 在 $A$ 上是减函数.

注意：求单调区间优先考虑函数定义域.

方法3：定义法,利用作差或作商判断.

注意： $\textcircled{1}$ 定义法的规范表达；$\textcircled{2}$ 作差后化成因式的积的形式.

方法4:导数法.

# 方法选择

判断函数的单调性优先考虑定义域，选择方法先考虑图象法，再考虑复合函数法，最后考虑导数法，但别忘了定义法.

注意：证明单调性只能用导数法或定义法.

# 类型四：已知单调性，求参数范围

方法：结合函数的图象和单调性分析参数的范围.  
注意：分段函数分界点处函数值的大小.

# 圖 类型五：利用单调性处理不等问题

方法：构造成 $f ( \_ { } { } , \_ { f } ($ __)的形式,利用单调性判断.若给出的函数单调性不明确，应优先判断函数的单调性.

注意： $\textcircled{1}$ 优先考虑函数的定义域；$\textcircled{2}$ 自变量必须在同一单调区间内才能利用单调性.

# 类型六：奇偶性、单调性综合

情形1:已知函数的奇偶性,根据函数 $y$ 轴一侧的解析式求另一侧解析式.

方法：先设未知区间（如 $x { < } 0 \AA$ ),利用奇偶性将自变量转化到已知区间，再代入函数解析式.

注意：若函数为奇函数，且0在定义域内，考虑 $f ( 0 ) { = } 0$ 的情况.

情形2:函数奇偶性的性质.

$\textcircled{1}$ 若函数为奇函数，则它在 $y$ 轴两侧单调性相同； 若函数为偶函数，则它在 $y$ 轴两侧单调性相反. 简称：奇同偶反. $\textcircled{2}$ 若函数 $f ( x )$ 是偶函数，则 $f ( x ) { = } f ( | x | )$

情形3：抽象函数处理方法.  
方法1:特殊化，即找到满足条件的特殊函数(或图象)分析.  
方法2：代换，即对函数中 $x \ : , \ : y$ ,可以用数值、式子、字母等形式代换.情形4:抽象函数形式的解不等式.  
方法：先化为 $f ( \_ f < f ( \_ )$ 形式，再利用单调性求解.  
注意：考虑原不等式中自变量满足定义域.

# 方法选择

已知函数奇偶性，那么已知 $y$ 轴一侧的解析式(图象、单调性)，能得到另一侧的解析式(图象、单调性等),从而只需分析一侧的函数性质，所以处理一些函数问题,如最值、解不等式、图象等，应先分析函数的奇偶性，再判断函数的性质.

# 第4讲函数概念及其性质(4)图象变换

# 一类型一：函数图象的变换

方法：

# （一）对称变换

$\textcircled { 1 } y = - f ( x )$ 的图象与 $y { = } f ( x )$ 的图象关于 $_ { \mathcal { X } }$ 轴对称；  
$\textcircled { 2 } y = f ( - x )$ 的图象与 $y { = } f ( x )$ 的图象关于 $y$ 轴对称；  
$\textcircled { 3 } \ : y = - f ( - x )$ 的图象与 $y { = } f ( x )$ 的图象关于原点对称.

# （二）平移变换

$\textcircled{1}$ (上下平移） $y = f ( x ) + a$ ：将 $y { = } f ( x )$ 的图象向上 $( a > 0 )$ 或向下 $( a < 0 )$ 平移 $| a |$ 个单位,得到 $y = f ( x ) + a$ 的图象；$\textcircled{2}$ (左右平移) $y = f ( x + a )$ ：将 $y { = } f ( x )$ 的图象向左 $( a > 0 )$ 或向右 $( a < 0 )$ 平移 $\lvert a \rvert$ 个单位，得到 $y = f ( x + a )$ 的图象.

# （三）翻折变换

$\textcircled{1}$ （上下翻折） $y = \vert f ( x ) \vert$ ：将 $y { = } f ( x )$ 在 $_ { \mathcal { X } }$ 轴上方的图象保留不变，再将$y { = } f ( x )$ 在 $_ { \mathcal { X } }$ 轴下方的图象对称翻折到 $_ { \mathcal { X } }$ 轴的上方,得到 $y = \vert f ( x ) \vert$ 的图象；$\textcircled{2}$ (左右翻折) $y = f ( \mid x \mid )$ ：将 $y = f ( x )$ 在 $y$ 轴右方的图象保留不变（但$y { = } f ( x )$ 在 $y$ 轴左方的图象要擦去),再将 $y = f ( x )$ 在 $y$ 轴右方的图象对称复制到 $y$ 轴左方，得到 $y = f ( \mid x \mid$ ）的图象.

# （四）伸缩变换

$\textcircled { 1 } \ y { = } A f ( x ) ( A { > } 0 )$ ：将 $y = f ( x )$ 图象各点的纵坐标伸长 $( A { > } 1 )$ 或缩小（ $\scriptstyle ( 0 < A < 1 )$ 到原来的 $A$ 倍,横坐标不变；

$\textcircled { 2 } \ y = f ( \omega x ) \left( \omega > 0 \right)$ ：将 $y = f ( x )$ 图象各点的横坐标缩短 $( \omega { > } 1 )$ 或伸长$( 0 { < } \omega { < } 1 )$ 到原来的 $\frac { 1 } { \omega }$ 倍,纵坐标不变.

注意：所有与 $_ { \mathcal { X } }$ 有关的变换只能针对 $_ { x }$

结论1: $\textcircled{1}$ 若 $f ( a + b x ) = f ( b - k x ) ( k \neq 0 )$ ，则 $f \left( x \right)$ 的图象关于 $x =$ ${ \frac { a + b } { 2 } } $ 对称； $\textcircled{2}$ 若 $f ( a + k x ) = - f ( b - k x ) ( k \neq 0 )$ ，则 $f \left( x \right)$ 的图象关于

$( \frac { a + b } { 2 } , ~ 0 )$

$\textcircled{3}$ 若 $f \left( a + k x \right) + f \left( b - k x \right) = c \left( k \neq 0 \right)$ ，则 $f \left( x \right)$ 的图象关于$\textstyle { \bigl ( } { \frac { a + b } { 2 } } , { \frac { c } { 2 } } { \bigr ) }$ 对称

结论2： $\textcircled{1}$ 若 $f ( x + m ) { = } f ( x ) ( m { \neq } 0 )$ ，则 $f ( x )$ 的周期为 $\mid m \mid$ ; $\textcircled{2}$ 若 $f ( x + m ) { = } { - } f ( x ) ( m { \neq } 0 )$ ，则 $f ( x )$ 的周期为 $\mid 2 m \mid$ $\textcircled{3}$ 若樓 $f ( x + m ) { = } \frac { 1 } { f ( x ) } ( m { \neq } 0 )$ 则 $f ( x )$ 的周期为 $\mid 2 m \mid$

# ■类型二：作图

方法：根据已知的初等函数图象，利用函数图象变换，作出新函数图象.其中作图要求：

$\textcircled{1}$ 优先确定定义域；  
$\textcircled{2}$ 标注特殊点（如与 $x$ 轴交点，与 $y$ 轴交点，最值点)；  
$\textcircled{3}$ 分析函数性质(如对称轴、单调性、周期性等)；  
$\textcircled{4}$ 标注渐近线或趋近点.

类型三：识图

方法：

函数图象的识别常见途径有：

$\textcircled{1}$ 从函数的定义域，判断图象的左右端点位置；  
$\textcircled{2}$ 从函数的值域,判断图象的上下最值位置；  
$\textcircled{3}$ 从函数的单调性，判断图象的升降；  
$\textcircled{4}$ 从函数的奇偶性，判断图象的对称性；  
$\textcircled{5}$ 从函数的周期性，判断图象重复特点；  
$\textcircled{6}$ 从基本初等函数的图象进行变换,如平移变换、翻折变换;  
$\textcircled{7}$ 考虑某些特殊的位置(如取特殊值，与 $_ { \mathcal { X } }$ 轴交点，与 $y$ 轴交点等)；  
$\textcircled{8}$ 从变化的快慢，判断图象陡峭程度；  
$\textcircled{9}$ 分析函数图象的渐近线或趋近点.

# 类型四：用图（数形结合）

方法：

利用函数的图象研究函数的性质的一般途径有：

$\textcircled{1}$ 从图象的最高点、最低点，分析函数的最值；

$\textcircled{2}$ 从图象的对称性，分析函数的奇偶性；

$\textcircled{3}$ 从图象的趋势变化,分析函数的单调性、周期性;

$\textcircled{4}$ 利用函数图象与 $_ { x }$ 轴的交点在 $_ { x }$ 轴上方或下方,分析方程的解和不等式的解；

$\textcircled{5}$ 利用两个函数图象的交点及上、下位置关系，分析方程的解和两个函数式的大小关系.

# 第5讲二次函数解析式和最值

# 类型一：二次函数解析式

方法： $\textcircled{1}$ 当已知顶点坐标或对称轴或最值时，考虑设 $y = a ( x - h ) ^ { 2 } +$ $k \left( a \ne 0 \right)$ ;$\textcircled{2}$ 当已知与 $_ { \mathcal { X } }$ 轴两交点坐标时，考虑设 $y { = } a ( x { - } x _ { 1 } ) ( x { - } x _ { 2 } ) ( a { \neq } 0 )$ $\textcircled{3}$ 当没有出现 $\textcircled{1} \textcircled{2}$ 中情形时，考虑设 $y = a x ^ { 2 } + b x + c ( a \neq 0 )$ ：

# ■类型二：二次函数给定区间最值

情形1:对于 $f ( x ) = a x ^ { 2 } + b x + c ( a > 0 )$ 在区间 $[ m , n ] ( m { < } n )$ 的最值.

方法：确定开口方向，把握对称轴与区间的位置关系,情形如下：

<html><body><table><tr><td>位置</td><td>①区间的左侧</td><td>②区间的中间偏左</td><td>③区间的中间偏右</td><td>④区间的右侧</td></tr><tr><td>范围</td><td>b2 &lt;m</td><td>m&lt; b20 m+n</td><td>m+n b20 n</td><td>n≤ 6-20</td></tr><tr><td>图象</td><td>m n</td><td>m+n 2 m n</td><td>m+n 2 n m</td><td>n m 文</td></tr><tr><td>最值</td><td>ymax=f(n） ymin=f(m）</td><td>b 2a ymax=f(n) ymin=f（</td><td>b 1 ymax=f(m) ymin=f（ 2a</td><td>ymin=f(n） ymax=f(m）</td></tr></table></body></html>

情形2:求二次函数最值的分类讨论.

方法： $\textcircled{1}$ 考虑对称轴与区间相对位置的四种情况(左、中偏左、中偏右、右)；$\textcircled{2}$ 再根据具体问题对四种情况进行合并(或取舍).

注意：二次函数的开口方向.

# 第6讲 指数、对数

# 类型一：指数幂的运算

方法：指数幂运算通常将含根式形式化成指数幂形式，将底数化为最简形式.

结论 $: x + x ^ { - 1 } = ( x ^ { \frac { 1 } { 2 } } + x ^ { - { \frac { 1 } { 2 } } } ) ^ { 2 } - 2 , x + x ^ { - 1 } = ( x ^ { \frac { 1 } { 2 } } - x ^ { - { \frac { 1 } { 2 } } } ) ^ { 2 } + 2 ,$ $x ^ { 2 } + x ^ { - 2 } = ( x + x ^ { - 1 } ) ^ { 2 } - 2 , x ^ { 2 } + x ^ { - 2 } = ( x - x ^ { - 1 } ) ^ { 2 } + 2 .$

# ■类型二：对数运算

结论 $\mathbf { 1 } : a ^ { b } = N \Longleftrightarrow \log _ { a } N = b$

结论2：对数恒等式： $a ^ { \log _ { a } N } = N ( a { > } 0 , a { \neq } 1 , N { > } 0 ) .$ 对数换底公式 $: \log _ { a } N = { \frac { \log _ { c } N } { \log _ { c } a } } ( a > 0 , a \not = 1 , N > 0 , c > 0 , c \not = 1 ) .$ 特别地， $\textcircled { 1 } \log _ { a ^ { m } } N ^ { n } = \frac { n } { m } \mathrm { l o g } _ { a } N ; \textcircled { 2 } \log _ { a } b = \frac { 1 } { \log _ { b } a } .$ 结论 $3 : \lg 5 = 1 - \lg 2$

# 第7讲幂函数、指数函数、对数函数

# 类型一：幂函数的图象和性质

结论1:一般地,函数 $y = x ^ { \alpha }$ 叫作幂函数,其中 $_ { x }$ 是自变量， $\alpha$ 是常数.

结论2：

（1）五个幂函数的图象：

![](images/0f9061a110960f4db3e6076b05b499710625fb3893918cd855f14f0306337cde.jpg)  
图5-1

（2）幂函数的性质：

<html><body><table><tr><td>幂函数</td><td>y=x</td><td>y=x²</td><td>y=x³</td><td>=x</td><td>y=x-1</td></tr><tr><td>定义域</td><td>R</td><td>R</td><td>R</td><td>[0，+8)</td><td>(+0）U</td></tr><tr><td>值域</td><td>R</td><td>[0，+∞）</td><td>R</td><td>[0，+∞)</td><td>{yly∈R,且y≠0}</td></tr><tr><td>奇偶性</td><td>奇</td><td>偶</td><td>奇</td><td>非奇非偶</td><td>奇</td></tr><tr><td>单调性</td><td>增</td><td>在，]上递</td><td>增</td><td>增</td><td>在（上减</td></tr><tr><td>公共点</td><td colspan="5">都经过点(1，1)</td></tr></table></body></html>

# 方法：

解决幂函数图象问题的方法有：

（1）依据图象确定幂指数 $\alpha$ 与0，1的大小关系,即根据幂函数在第一象限内的图象(类似于 $y = x ^ { - 1 }$ 或 $y = x ^ { \frac { 1 } { 2 } }$ 或 $y = x ^ { 3 }$ )来判断.

（2）依据图象高低判断幂指数大小,相关结论为：

$\textcircled{1}$ 在(0，1)上，指数越大，幂函数图象越靠近 $_ { \mathcal { X } }$ 轴(简记为"指大图低")；$\textcircled{2}$ 在 $( 1 , + \infty$ )上，指数越大，幂函数图象越远离 $_ { x }$ 轴（简记为“指大图高").

# 类型二：指数函数的定义域、值域、单调性的应用

情形1:指数方程与不等式问题.  
方法：等式(不等式)两边化同底.情形2:与指数函数有关的值域问题.  
方法1:复合函数法,转化为利用指数函数的单调性；  
方法2：换元法.  
注意：若底数为字母,则需考虑分类讨论.

# 类型三：大小比较

方法：大小比较问题,先与特殊值(如0，1，一1等)比较，再利用单调性比较.

# ■类型四：对数函数的定义域、值域、单调性的应用

情形1:对数方程与不等式问题.  
方法：等式(不等式)两边化同底.化同底的方法： $0 = \log _ { a } 1$ $1 = \log _ { a } a = a ^ { 0 }$ ; $M = \log _ { a } a ^ { M } = a ^ { \log _ { a } M }$

注意：考虑对数的定义域或者求解后检验.

情形2：与对数有关值域问题.  
方法1:复合函数法,转化为利用对数函数的单调性.  
方法2：换元法.  
注意： $\textcircled{1}$ 优先考虑定义域； $\textcircled{2}$ 若底数为字母，则需考虑分类讨论.情形3：与对数有关单调性问题.  
方法：利用复合函数，结合图象分析.  
注意：优先考虑定义域.

情形 $4 { : \boldsymbol { y } } = \log _ { a } f ( { \boldsymbol { x } } )$ 定义域为 $\mathbf { R }$ 和值域为 $\mathbf { R }$ 问题.

方法：若 $_ { y } = \log _ { a } f ( x )$ 定义域为 $\mathbf { R }$ ，则 $f ( x ) { > } 0$ 在 $\mathbf { R }$ 上恒成立.若 $_ { y } = \log _ { a } f ( x )$ 值域为 $\mathbf { R }$ ，则 $f ( x )$ 的值域包含 $( 0 , + \infty )$

注意：比较两个问题的区别.

# 第8讲函数零点

# 类型一：函数零点判断问题

方法1:转化为两个函数，数形结合分析.

合理转化为两个函数的图象(图象易画出)的交点个数问题.两个函数的图象交点的个数，就是函数零点的个数.

注意：函数图象的变化趋势(如渐近线，趋近点等).

方法2:利用零点存在定理.

利用函数零点存在定理进行判断，但必须结合函数的图象与性质(如单调性、奇偶性、周期性、对称性)才能确定函数的零点个数.

结论： $\textcircled{1}$ 不间断函数 $y = f ( x )$ 在区间 $( a , b )$ 上满足 $f \left( a \right) f \left( b \right) { < } 0$ ，则$f ( x )$ 在 $( a , b )$ 上至少存在一个零点.

$\textcircled{2}$ 二次函数 $y = f ( x )$ 在区间 $( a , b )$ 上满足 $f ( a ) f ( b ) { \ < } 0$ ，则 $f ( x )$ 在 $( a$ ，$^ { b }$ )上存在唯一一个零点.

函数 $y { = } f ( x )$ 在区间 $( a , b )$ 上单调,且 $f ( a ) f ( b ) { \ < } 0$ ，则 $f ( x )$ 在 $( a , b )$ 上存在唯一一个零点.

方法3：解方程.

函数零点问题转化为方程解的问题，即通过解方程判断函数是否有零点，其中方程有几个解就对应有几个零点.

# ■类型二：函数零点的应用

情形1:已知函数零点个数求参数范围.

方法：转化为两个函数，数形结合分析.合理转化为两个函数的图象（图象易画出)的交点个数问题.两个函数的图象交点的个数，就是函数零点的个数.

注意：函数图象的变化趋势(如渐近线,趋近点等).

情形2:已知函数存在零点求参数范围.

方法1:转化为方程有解问题.

方法2:参变分离,即 $a = F ( x )$ ，则 $a$ 的范围即为 $F ( x )$ 值域.

方法3:利用零点存在定理.

结论：已知函数 $y { = } f ( x )$ 在区间 $( a , b )$ 上单调,若 $f ( x )$ 在 $( a , b )$ 上存在零

点，则 $f ( a ) f ( b ) { \ < } 0 .$

注意：若 $y = f ( x )$ 在区间 $( a , b )$ 上不是单调函数,则上述结论不一定成立.

# ■类型三：二次形式函数零点分布

方法1:韦达定理法.如对于二次方程根的分布情况(注：两根也包含两个相等的根),

$\textcircled{1}$ 两根均为正，则 $\left\{ \begin{array} { l } { \Delta \geqslant 0 , } \\ { x _ { 1 } + x _ { 2 } > 0 , } \\ { x _ { 1 } x _ { 2 } > 0 . } \end{array} \right.$   
$\textcircled{2}$ 两根均大于 $m$ ，则 $\left\{ \begin{array} { l l } { \Delta \geqslant 0 , } \\ { ( x _ { 1 } - m ) + ( x _ { 2 } - m ) > 0 , } \\ { ( x _ { 1 } - m ) ( x _ { 2 } - m ) > 0 . } \end{array} \right.$   
$\textcircled{3}$ 两根均为负，则 $\left\{ \begin{array} { l } { \Delta \gtrless 0 , } \\ { x _ { 1 } + x _ { 2 } < 0 , } \\ { x _ { 1 } x _ { 2 } > 0 . } \end{array} \right.$   
$\textcircled{4}$ 两根均小于 $m$ ，则 $\left\{ \begin{array} { l l } { \Delta \gg 0 , } \\ { ( x _ { 1 } - m ) + ( x _ { 2 } - m ) < 0 , } \\ { ( x _ { 1 } - m ) ( x _ { 2 } - m ) > 0 . } \end{array} \right.$ $\begin{array} { r } { \left\{ \begin{array} { l l } { \Delta \geqslant 0 ( \overline { { \mathbb { H } } } ) ; } \\ { x _ { 1 } x _ { 2 } < 0 . } \end{array} \right. } \end{array}$ 省略)，   
$\textcircled{5}$ 一根为正且一根为负，则   
$\textcircled{6}$ 一根大于 $m$ 且一根小于 $m$ ，则 $\left\{ \begin{array} { l l } { \Delta \geq 0 ( \overrightarrow { \mathrm { H } } / / / / / / \Sigma ) , } \\ { ( x _ { 1 } - m ) ( x _ { 2 } - m ) < 0 . } \end{array} \right.$

方法2:图象分析法.

# 优先发现不变性

质(如定点，定轴，二项系数为0 →转化为一次或常数形式  
某点的值恒正负)√二次形式 考虑四个条件：→二项系数为正零点分布 分析二次函数图象②△的正负或为0； 判断零点分布二项系数为负 $\textcircled{3}$ 对称轴位置；==== $\textcircled{4}$ 特殊点函数值的正负或为0.

# 第9讲 函数模型

# 一、解应用题的一般步骤：

第一步：审题一弄清题意,分清条件和结论,理顺数量关系；第二步:建模—将文字语言转化成数学语言，用数学知识建立相应的数学模型；第三步:解模——求解数学模型,得到数学结论；第四步：还原——将用数学方法得到的结论还原为实际问题的意义.第五步：反思回顾——对于数学模型得到的数学结果,必须验证这个数学结果对实际问题的合理性.

# 二、几类函数模型

<html><body><table><tr><td>函数模型</td><td>函数解析式</td></tr><tr><td>一次函数模型</td><td>f(x)=ax+b(a，b为常数,a≠0)</td></tr><tr><td>反比例函数模型</td><td>f（x)=k +b (k，b为常数,k≠0) X</td></tr><tr><td>二次函数模型</td><td>f(x)=ax²+bx+c (a，b，c为常数，a≠0)</td></tr><tr><td>指数函数模型</td><td>f(x)=ba²+c (a,b，c为常数,b≠0，a&gt;0且a≠1)</td></tr><tr><td>对数函数模型</td><td>f(x)=blogax+c (a，b，c为常数,b≠0，a&gt;0且a≠1)</td></tr><tr><td>幂函数模型</td><td>f(x)=ax&quot;+b(a，b为常数,a≠0)</td></tr></table></body></html>

# 第1讲任意角的三角函数

# 类型一：角的概念及其表示

方法：掌握轴线角、象限角、终边所在范围角的表示.  
注意：角的表示不唯一.

# □ 类型二：三角函数定义与符号

方法：利用定义 $\sin \alpha = { \frac { y } { r } }$ ， cos $\alpha { = } \frac { x } { r }$ ， tan $\alpha { = } \frac { y } { x }$ ,求三角函数的值或判断三角函数值的正负.

角的终边为直线或射线时，取特殊点，利用定义求解.

注意： $\textcircled{1}$ 终边在一条直线上时，取点应考虑不同象限的两种情况.$\textcircled{2}$ 利用定义求三角函数值时考虑 $r { > } 0$ $\textcircled{3}$ 判断三角函数值正负时，考虑象限角和轴线角.

# 类型三：弧长公式、扇形面积

方法：掌握扇形中半径、弧长、弦长、圆心角、面积之间的关系，弧长公式： $l { = } _ { \alpha } R$ ，扇形面积公式： $S = \frac { 1 } { 2 } l R = \frac { 1 } { 2 } \alpha R ^ { 2 }$

# 一类型四：三角函数线

情形1:比较大小.  
方法：利用三角函数线比较大小.  
结论：有向线段MP叫作角的正弦线，有向线段OM叫作角的余弦线，有向

线段AT叫作角的正切线.

![](images/0601b82a9f2d05bacb72975b328c7cadfe63cea4c61e8c6d192e7b90223e28de.jpg)  
图6-1

情形2:求角的范围.  
方法：利用三角函数线求角的范围.  
注意：考虑三角函数线的正负.

# 第2讲同角三角函数关系和诱导公式

# 类型一：同角三角函数关系

情形1:知一求其余三角函数值.

方法1：利用 $\sin ^ { 2 } \alpha + \cos ^ { 2 } \alpha = 1$ 和tan $\alpha { = } \frac { \sin \alpha } { \cos \alpha }$ 建立方程求解.

方法2:先确定正负，再借助于直角三角形求值.  
注意：根据角的范围确定三角函数值正负.

情形2：三角函数间关系.方法：发现三角函数之间的隐含关系，即 $\sin ^ { 2 } \alpha + \cos ^ { 2 } \alpha = 1$ 注意：考虑三角函数值的取值范围来检验.

# 类型二：关于sin $\propto$ 与cos $\alpha$ 的齐次式

方法：采取同除以cos $\alpha$ 或 $\cos ^ { 2 } \alpha$ ），转化为tan $\alpha$ 相关形式.如果不是齐次分式形式,可借助 $1 { = } \sin ^ { 2 } \alpha { + } \cos ^ { 2 } \alpha$ 构造齐次分式形式.

# 类型三：弦切互化

方法1:切化弦,即将 tan $\alpha$ 化为 $\sin \alpha$ 与 $\cos \alpha$ 相关形式.  
方法2:弦化切，即将 $\sin \alpha$ 与 $\cos \alpha$ 化为tan $\alpha$ 相关形式.

# 解题策略

在三角函数值问题中出现较多的函数名时，采取异名化同名，如切化弦、齐次时弦化切等，从而减少三角函数名.

类型四：sin $\propto + \cos \ \alpha$ ，sin $\alpha - \cos \alpha$ ，sinαcos间关系式方法： $\sin \alpha + \cos \alpha$ ， $\sin \alpha ^ { -- } \cos \alpha$ ， $\sin \alpha \cos \alpha$ 间的关系如下：

sin a+cos a ↑→ sin 2a→sin acos α →sinα和cos $\alpha$ →tan $\alpha$ √ sina-cosα

注意： $\textcircled{1}$ 无法确定值的正负时可根据三角函数值的正负（或与特殊角的三角函数值比较)来缩小角的范围.$\textcircled{2}$ 发现 $\sin \alpha + \cos \alpha$ ， $\sin \alpha ^ { - } \cos \alpha$ ， $\sin \alpha \cos \alpha$ 之间的隐含关系.

# 类型五：诱导公式

方法：在角中出现 $\frac { \pi } { 2 }$ 的整数倍时，可先用诱导公式化简.

诱导公式记忆规律：奇变偶不变，符号看象限.

注意：利用诱导公式化简时,若前后角度相差 $\frac { \pi } { 2 }$ 的奇数倍，应改变三角函数名；“符号看象限"中的符号与函数值本身的符号有区别.

# 第3讲三角函数的图象与性质(1)

# 类型一：三角函数定义域问题

形如 $\scriptstyle y = A \sin ( \omega x + \varphi )$ 定义域问题.

方法：将 $\omega x { + } \varphi$ 看成整体，转化为 $y = \sin x$ 定义域问题,通过观察 $y = \sin { x }$ 的图象(或利用三角函数线)列出不等式求解.

# ■类型二：三角函数值域问题

情形1:形如 $\scriptstyle y = A \sin ( \omega x + \varphi )$ 值域问题.

方法：将 $\omega x + \varphi$ 看成整体,转化为 $y = \sin { x }$ 值域问题,通过观察 $y = \sin { x }$ 的图象求值域.

情形2：形如 $y = a \sin ^ { 2 } \omega x + b \sin \omega x \cos \omega x + c \cos ^ { 2 } \omega x$ 的形式.

方法：先利用降幂公式化为一次形式，再用辅助角公式化为 $y =$ $A \sin ( 2 \omega x + \varphi )$ 形式求值域.

情形3：形如 $\textcircled{1}$ 含有 $\sin ^ { 2 } x$ ， $\cos x$ 或 $\sin x ,$ 和 $\cos ^ { 2 } x$ ， $\sin x$ 或 $\cos x \textrm { \textbf { \ } }$ 形式；$\textcircled{2}$ 含有 $\sin x \pm \cos x$ ，sir $1 \ : \mathcal { X } \cos \mathcal { X }$ 形式.

方法：利用换元法转化为二次函数值域问题.

情形4:形如分子、分母含有 $\sin x$ ， $\mathrm { c o s } \ x$ 的一次形式.

方法1：化为 $\sin ( \omega x + \varphi ) = M$ 或 $\cos ( \omega x + \varphi ) = M )$ 形式，再利用三角函数的有界性 $( \mid \sin x \mid \leqslant 1$ ， $| \cos x | { \leqslant } 1 )$ 求值域.

方法2：利用斜率的几何意义.

# 类型三：单调性问题

情形1:求三角函数单调区间.

方法：将 $\scriptstyle y = A \sin ( \omega x + \varphi )$ 中的 $\omega x + \varphi$ 看成整体,转化为找出 $y = \sin { x }$ 的单调区间，再解不等式求出原单调区间.

注意： $\textcircled{1}$ 优先考虑定义域；$\textcircled{2}$ 先将 $\omega$ 化为正数，考虑 $A$ 的正负对单调性的影响；

$\textcircled{3}$ 将单调区间写成区间形式.

情形2:判断三角函数单调区间.

方法1：将 $\scriptstyle y = A \sin ( \omega x + \varphi )$ 中的 $\omega x + \varphi$ 看成整体，转化为求 $y = \sin { x }$ 的单调区间，求出该单调区间，判断已知区间是否为该单调区间的子集.

方法2：将 $y = A \sin ( \omega x + \varphi )$ 中的 $\omega x + \varphi$ 看成整体，由已知的区间求出$\omega x + \varphi$ 的范围,转化为判断 $y = \sin x$ 的单调性问题.

注意：(1)先将 $\omega$ 化为正数；(2)考虑 $A$ 的正负对单调性的影响.

# 一类型四：周期性问题

方法1:转化为 $\scriptstyle y = A \sin ( \omega x + \varphi )$ 的形式，利用 $T = \frac { 2 \pi } { | \boldsymbol { \omega } | }$ 求周期.

注意：公式中 $\omega$ 含绝对值.方法2：利用图象发现函数的周期.方法3：利用定义求周期，即 $f ( x + T ) = f ( x )$

# 第4讲三角函数的图象与性质(2)

# ■类型一：奇偶性问题

方法：判断奇偶性首先要判断定义域是否关于原点对称，再用定义或图象判断.

# 类型二：对称问题

情形1:求三角函数的对称轴(或对称中心).

方法1:如,将 $\scriptstyle y = A \sin ( \omega x + \varphi )$ 中 $\omega x + \varphi$ 看成整体,转化为找 $y = \sin x$ 的对称轴(或对称中心),再建立等式求解.

<html><body><table><tr><td>函数</td><td>对称轴</td><td>对称中心</td></tr><tr><td>y=sin x</td><td>x= π+kπ，k∈z</td><td>(kπ，0)，k∈Z</td></tr><tr><td>y=cos x</td><td>x=kπ,k∈Z</td><td>（+π，0），,kz</td></tr><tr><td>y=tan x</td><td>无</td><td>(（,0）,ke</td></tr></table></body></html>

情形2:判断三角函数的对称轴(或对称中心).

方法1:求出 $\scriptstyle y = A \sin ( \omega x + \varphi )$ 的对称轴(或对称中心),判断已知的对称轴(或对称中心)是否符合.

方法2：对于函数 $\scriptstyle y = A \sin ( \omega x + \varphi )$ 或 $y { = } A \cos ( \omega x { + } \varphi )$ ，$\textcircled{1}$ 若 ${ \boldsymbol { x } } = { \boldsymbol { x } } _ { 0 }$ 为对称轴 $\Longleftrightarrow f ( x _ { 0 } ) = \pm A$ $\textcircled{2}$ 若 $( x _ { 0 } , 0 )$ 为对称中心 $\scriptstyle \iff f ( x _ { 0 } ) = 0$

推论：对于函数 $\scriptstyle y = A \sin ( \omega x + \varphi )$ 或 $y { = } A \cos ( \omega x { + } \varphi )$ ，

$\textcircled{1}$ 若函数 $y = f ( x )$ 为偶函数 $\Longleftrightarrow f ( 0 ) = \pm A$ $\textcircled{2}$ 若函数 $y = f ( x )$ 为奇函数 $\Longleftrightarrow f ( 0 ) { = } 0$

注意：上述结论仅适用于函数 $\scriptstyle y = A \sin ( \omega x + \varphi )$ 或 $y { = } A \cos ( \omega x { + } \varphi )$

方法3:已知对称轴(或对称中心)时,可采取特殊值法.

# 解题策略

研究三角函数的性质,形如 $\scriptstyle y = A \sin ( \omega x + \varphi )$ 或 $y = A \cos ( \omega x + \varphi )$ 是绝大多数问题的归结点.将 $\omega x + \varphi$ 看成整体，可以解决其定义域、值域、单调性、对称轴、对称中心等问题.

# 类型三：图象变换

方法：

（1）平移变换

$\textcircled{1}$ （上下平移） $\ b { y } = \ b { f } ( \ b { x } ) + \ b { a }$ ：将 $y = f ( x )$ 的图象向上 $( a > 0 )$ 或向下 $( a <$ 0)平移 $\left| a \right|$ 个单位长度，得到 $y = f ( x ) + a$ 的图象；$\textcircled{2}$ (左右平移） $\scriptstyle y = f ( x + a )$ ：将 $y = f ( x )$ 的图象向左 $( a > 0 )$ 或向右 $( a <$ 0)平移 $\left| a \right|$ 个单位长度，得到 $y = f ( x + a )$ 的图象.

（2）伸缩变换

$\textcircled { 1 } \ y { = } A f ( x ) ( A { > } 0 )$ ：将 $y = f ( x )$ 图象上的每一个点的纵坐标变为原来的 $A$ 倍(横坐标不变);

$\textcircled { 2 } \ y = f ( \omega x ) ( \omega > 0 )$ ：将 $y { = } f ( x )$ 图象上的每一个点的横坐标变为原来的$\frac { 1 } { \omega }$ 倍(纵坐标不变).

注意：左右平移变换和横坐标伸缩变换针对 $_ { x }$ 进行变化.

# ■类型四：求三角函数解析式

方法：求 $f ( x ) { = } A \sin ( \omega x { + } \varphi ) { + } B ( A { > } 0 )$ 的解析式步骤：

$\textcircled{1}$ 由周期 $T = \frac { 2 \pi } { \left| { \boldsymbol { \omega } } \right| }$ 得 $\omega$ ;$\textcircled{2}$ 由 $\begin{array} { r } { \{ \stackrel { { \left\{ A + B = y _ { \mathrm { m a x } } , \atop - A + B = y _ { \mathrm { m i n } } , \right\} } } { \left\{ B = \frac { y _ { \mathrm { m a x } } - y _ { \mathrm { m i n } } } { 2 } , \right. } } ,  \end{array}$

$\textcircled{3}$ 将特殊点代人求 $\varphi$ (尽量代人最高点或最低点).

# 第5讲三角恒等变换

# ■类型一：两角和与差的正弦、余弦、正切

方法1:两角和与差的正弦、余弦、正切公式.  
$S _ { ( \alpha \pm \beta ) }$ ： $\sin ( \alpha \pm \beta ) = \sin \alpha \cos \beta \pm \cos \alpha \sin \beta ;$   
$C _ { ( \alpha \pm \beta ) }$ $\operatorname { \mathtt { t } } _ { \beta } ) : \cos ( \alpha \pm \beta ) = \cos \alpha \cos \beta \mp \sin \alpha \sin \beta ;$   
$T _ { ( \alpha \pm \beta ) } : \tan ( \alpha \pm \beta ) = \frac { \tan \alpha \pm \tan \beta } { 1 mp \tan \alpha \tan \beta } \big ( \alpha , \beta , \alpha \pm \beta \mp \frac { \pi } { 2 } + k \pi , k \in { \bf Z } \big ) .$

S(+） 以-β代替β Sa-B) Ca+ C 相除 相除 以-β代替β Ta+ T

注意： $\textcircled{1}$ 讨论角的范围确定三角函数值的正负和角的大小；$\textcircled{2}$ 求角时合理选择三角函数名.

方法2:观察角的联系,实现角的统一,如将"未知角"化为"已知角”

常见的配角技巧：

$2 \alpha = ( \alpha + \beta ) + ( \alpha - \beta ) , \alpha = ( \alpha + \beta ) - \beta , \beta = { \frac { \alpha + \beta } { 2 } } - { \frac { \alpha - \beta } { 2 } } , \alpha = { \frac { \alpha + \beta } { 2 } } + { \frac { \alpha - \beta } { 2 } }$ $\frac { \alpha - \beta } { 2 } = ( \alpha + \frac { \beta } { 2 } ) - ( \frac { \alpha } { 2 } + \beta ) \frac { \varkappa } { \Psi }$ ：

方法3:三角函数名的统一.

结论1:辅助角公式： $a \sin x + b$ cos $\scriptstyle x = { \sqrt { a ^ { 2 } + b ^ { 2 } } } \sin ( x + \varphi )$ （其中 sin $\varphi =$ ${ \frac { b } { \sqrt { a ^ { 2 } + b ^ { 2 } } } } , \cos \varphi = { \frac { a } { \sqrt { a ^ { 2 } + b ^ { 2 } } } } \Big ) .$

结论2：

由 $\sin \alpha + \cos \beta { = } a$ ， $\cos \alpha + \sin \beta { = } b$ ,两式平方,得 $\sin ( \alpha + \beta ) = { \frac { a ^ { 2 } + b ^ { 2 } - 2 } { 2 } } ,$ 由 $\sin \alpha + \cos \beta { = } a$ ， $\cos \alpha - \sin \beta { = } b$ ,两式平方,得 $\sin ( \alpha - \beta ) = { \frac { a ^ { 2 } + b ^ { 2 } - 2 } { 2 } }$ 由 $\cos \alpha + \cos \beta { = } a$ ， $\sin \alpha + \sin \beta { = } b$ ,两式平方,得 $\cos ( \alpha - \beta ) = { \frac { a ^ { 2 } + b ^ { 2 } - 2 } { 2 } } ,$ 由 $\cos \alpha + \cos \beta { = } a$ ， $\sin \alpha - \sin \beta = b$ ,两式平方,得 $\cos ( \alpha + \beta ) = { \frac { a ^ { 2 } + b ^ { 2 } - 2 } { 2 } }$

# 类型二：二倍角公式

方法：二倍角公式：  
$S _ { 2 \alpha }$ ： $\sin { 2 \alpha } { = } 2 \sin { \alpha } \cos { \alpha }$   
$C _ { 2 \alpha }$ : cos $2 \alpha = \cos ^ { 2 } \alpha - \sin ^ { 2 } \alpha = 2 \cos ^ { 2 } \alpha - 1 = 1 - 2 \sin ^ { 2 } \alpha ;$   
T2α: tan 2α=1-tan²a $2 \alpha = \frac { 2 \tan \alpha } { 1 - \tan ^ { 2 } \alpha } ( \alpha \neq k \pi + \frac { \pi } { 2 }$ 且 $\alpha { \neq } \frac { k \pi } { 2 } { + } \frac { \pi } { 4 } , \ k { \in } { \bf Z } ) .$

注意;二倍角是相对的,例如， $\frac { \alpha } { 2 }$ 是 $\frac { \alpha } { 4 }$ 的二倍角， $3 \alpha$ 是 $\frac { 3 \alpha } { 2 }$ 的二倍角.

# 类型三：升幂、降幂

方法： $\textcircled{1}$ 降幂公式 $\cos ^ { 2 } \alpha { = } \frac { 1 { + } \cos 2 \alpha } { 2 } , \sin ^ { 2 } \alpha { = } \frac { 1 { - } \cos 2 \alpha } { 2 } .$ $\textcircled{2}$ 升幂公式： $1 + \cos 2 \alpha = 2 \cos ^ { 2 } \alpha$ ，1-cos 2α=2sin²a.结论 $\mathtt { : } \sqrt { 1 + \sin 2 \alpha } = | \sin \alpha + \cos \alpha | \ ; \sqrt { 1 - \sin 2 \alpha } = | \sin \alpha - \cos \alpha | \ ;$ $\scriptstyle { \sqrt { 1 + \cos 2 \alpha } } = { \sqrt { 2 } } \mid \cos \alpha \mid ; { \sqrt { 1 - \cos 2 \alpha } } = { \sqrt { 2 } } \mid \sin \alpha \mid .$

# ■类型四：三角恒等变换

方法：观察角的联系,实现角的统一(如将未知角化为已知角).

注意： $\textcircled{1}$ 判断角的范围，确定三角函数值的正负或角的值.若在已知范围内不能确定时,利用三角函数值的正负(或与特殊角的三角函数值大小比较)来缩小角的范围；

$\textcircled{2}$ 合理选择三角函数名求角的值.

方法：三角变换的策略：角、名、形、幂.

$\textcircled{1}$ 角：观察角的联系,实现角的统一；  
$\textcircled{2}$ 名：弦切互化,异名化同名,实现函数名的统一；形：公式变形与逆用；幂：平方降幂,根式升幂.

常见形式：（辅助角公式 $) a \sin \alpha + b \cos \alpha = \sqrt { a ^ { 2 } + b ^ { 2 } } \sin ( \alpha + \varphi ) ;$ （降幂 ${ \mathrm { : } } ) \sin \alpha \cos \alpha = { \frac { 1 } { 2 } } \sin 2 \alpha , \sin ^ { 2 } \alpha = { \frac { 1 - \cos 2 \alpha } { 2 } } , \cos ^ { 2 } \alpha = { \frac { 1 + \cos 2 \alpha } { 2 } } ,$ （升幂） $1 - \cos \alpha = 2 \sin ^ { 2 } \frac { \alpha } { 2 }$ ， $1 + \cos \alpha = 2 \cos ^ { 2 } \frac { \alpha } { 2 }$ 2；（切化弦） $\ldots + b$ tan $\alpha = { \frac { a \cos \alpha + b \sin \alpha } { \cos \alpha } }$

# 解题策略

三角变换问题先观察角的联系，分析角的变化，实现角的统一，从而决定解题方向，再结合三角函数名、公式的变形、幂的升降，做出公式的选择.

# 类型五：三角恒等变换与三角函数

方法：通过三角恒等变换,将函数化为 $\scriptstyle y = A \sin ( \omega x + \varphi ) + B$ 的形式.

# 第1讲三角形中有关问题

# 类型一：三角形中边角计算

情形1:三角形内边角计算.

方法：三角形中三角 $( A , B , C )$ 或三边 $( a , b , c )$ 中四个量利用正弦定理或余弦定理可建立关系式，从而进行边角计算.

注意：求出角的正弦值时,利用大边对大角和三角形内角和为 $\pi$ 进行取舍.

情形2：综合几何图形中边角计算.

方法：关键寻找不同三角形之间的边角联系，从而借助一个三角形求得另一个三角形的边角.

# 解题策略

正弦、余弦定理的本质是三角形中六个基本量中四个量可以建立一些关系式,如涉及三边一角考虑用余弦定理，两边两角考虑用正弦定理.

# 类型二：角角转化

方法：角角转化.

$\textcircled{1}$ 利用 $A + B + C = _ { \pi }$ 消元实现三角化两角.$\textcircled{2}$ 若已知一个角或角的关系式，可以将三角统一为一个角形式.结论：在△ABC中， $. A + B + C = \pi$ ，则$\sin ( A + B ) = \sin C$ ， $\cos ( A + B ) = - \cos C$ ， $\tan ( A + B ) = - \tan C .$ $\sin { \frac { A + B } { 2 } } = \cos { \frac { C } { 2 } } , \cos { \frac { A + B } { 2 } } = \sin { \frac { C } { 2 } } .$

# ■类型三：边角转化

方法：边角互化，即关于含有边角的关系式，利用 $\textcircled { 1 } a = 2 R \sin \ A$ ， $b =$ $2 R \sin B$ ， $c = 2 R \sin C$ 或 $\textcircled { 2 } \cos A = \frac { b ^ { 2 } + c ^ { 2 } - a ^ { 2 } } { 2 b c }$ 等,将边化角或将角化边.

# 方法选择

边角转化时合理选择转化的方向，通常角化边思维简单运算复杂,边化角变化复杂但运算简洁.

# 第2讲正弦、余弦定理的应用

方法：将实际问题转化为三角形边角计算问题.

# 类型一：向量的概念

结论：向量有关概念.

$\textcircled{1}$ 向量定义的关键是方向和模.  
$\textcircled{2}$ 非零共线向量的关键是方向相同或相反,模没有限制.  
$\textcircled{3}$ 相等向量的关键是方向相同且模相等.  
$\textcircled{4}$ 单位向量的关键是模都是一个单位长度.  
$\textcircled{5}$ 零向量的关键是模是0,规定零向量与任意向量共线.

# 类型二：向量的运算

方法：利用向量的加法、减法.常用的法则是平行四边形法则和三角形法则.  
共起点的向量求和用平行四边形法则，首尾相连的向量求和用三角形法则.  
共起点的向量求差用三角形法则.

注意：向量加法三角形法则首尾相连,向量的减法共起点.

结论：

$\textcircled{1}$ 若 $M$ 为 $B C$ 中点，则 $ \overrightarrow { A M } { = } \frac { 1 } { 2 } ( \overrightarrow { A B } { + } \overrightarrow { A C } )$ ：

$\textcircled{2}$ 有限个向量 ${ \pmb a } _ { 1 } \beta { \pmb a } _ { 2 } \beta \cdots , { \pmb a } _ { n }$ 相加,可以从点 $O$ 出发,逐一作向量 $\overrightarrow { O A _ { 1 } } =$ ${ \pmb a } _ { 1 }$ ， $\overrightarrow { A _ { 1 } A _ { 2 } } = { \bf { a } } _ { 2 }$ ，…， $\overrightarrow { A _ { n - 1 } A _ { n } } = { \pmb a } _ { n }$ ，则向量 $\overrightarrow { O A _ { n } }$ 为这些向量的和，即 ${ \pmb a } _ { 1 } +$ $\scriptstyle \pm i _ { 2 } + \cdots + a _ { n } = \overrightarrow { O A _ { 1 } } + \overrightarrow { A _ { 1 } A _ { 2 } } + \cdots + \overrightarrow { A _ { n - 1 } A _ { n } } = \overrightarrow { O A _ { n } }$ (向量加法的多边形法则).

当 $A _ { n }$ 和 $O$ 重合时（即上述折线 $O A _ { 1 } A _ { 2 } \cdots A _ { n }$ 成封闭折线时),则和向量为零向量.

# 类型三：平面向量共线定理

结论1:共线向量定理

如果有一个实数 $\lambda$ ，使 $\pmb { b = \lambda a \left( a \neq 0 \right) }$ ,那么向量 $\pmb { b }$ 与 $\pmb { a }$ 是共线向量；

反之，如果向量 $\pmb { b }$ 与 $\pmb { a } \left( \pmb { a } \neq \pmb { 0 } \right)$ 是共线向量，那么有且只有一个实数 $\lambda$ ，使得$\pmb { b } = \lambda \pmb { a }$

结论2:三点共线定理

①平面上三点A，B，C共线存在实数λ,使得AB=λAC(或AB=$\lambda \stackrel { \triangledown } { \vec { B C } } )$ ：$\textcircled{2}$ 平面上三点 $A$ ， $B$ ， $C$ 共线 $\iff$ 存在实数 $\lambda , \mu$ ，使 $\overrightarrow { O A } = \lambda \overrightarrow { O B } + _ { \mu } \overrightarrow { O C }$ （其中$\lambda + \mu { = } 1$ ， $O$ 为平面内的任一点).

# 类型四：平面向量基本定理

结论：平面向量基本定理

如果 $\pmb { e } _ { 1 }$ ， $\boldsymbol { e } _ { 2 }$ 是同一平面内的两个不共线向量,那么对于这一平面内的任一向量 $\pmb { a }$ ,有且只有一对实数 $\lambda _ { 1 } , \lambda _ { 2 }$ ，使 $\mathbf { \pmb { a } } = \lambda _ { 1 } \pmb { e } _ { 1 } + \lambda _ { 2 } \pmb { e } _ { 2 }$

方法：平面向量基本定理解决问题的一般思路：

先选择一组基底，并运用该基底将条件和结论表示为向量的形式，再通过向量的运算来解决.

注意： $\textcircled{1}$ 在基底未给出的情况下，合理地选取基底会给解题带来方便.$\textcircled{2}$ 要熟练运用平面几何的一些性质定理.

# 解题策略

平面内的任一向量可以表示成两个不共线的向量(称为基底)的线性组合，所以平面内所有向量间的运算最终可以转化为两个不共线的向量间的运算.

# 类型五：向量的坐标表示

结论： $\textcircled{1}$ 设 $ { \boldsymbol { a } } = (  { \boldsymbol { { x } } } ,  { \boldsymbol { { y } } } )$ ，则 $| \mathbf { \nabla } \mathbf { a } | = \sqrt { x ^ { 2 } + y ^ { 2 } }$ $\textcircled{2}$ 设 $A ( x _ { 1 } , y _ { 1 } )$ ， $B ( x _ { 2 } , y _ { 2 } )$ ，则 $A B = ( x _ { 2 } - x _ { 1 }$ ， $y _ { 2 } - y _ { 1 } )$ ， $| \overrightarrow { A B } | = \sqrt { ( x _ { 2 } - x _ { 1 } ) ^ { 2 } + ( y _ { 2 } - y _ { 1 } ) ^ { 2 } } .$

# □ 类型六：平面向量的坐标运算

结论1： $\textcircled{1}$ 两向量平行和垂直.设 $\pmb { a } = ( \boldsymbol { x } _ { 1 } , \boldsymbol { y } _ { 1 } )$ ， $\pmb { b } { = } ( x _ { 2 } , y _ { 2 } )$ ，则：$\mathbf { \delta } _ { a } \perp b {  } a \mathbf { \delta } \cdot b = 0 { \Leftrightarrow } x _ { 1 } x _ { 2 } + y _ { 1 } y _ { 2 } = 0$

$$
\scriptstyle a / / b \Longleftrightarrow a = \lambda b \Rightarrow x _ { 1 } y _ { 2 } - x _ { 2 } y _ { 1 } = 0 .
$$

$\textcircled{2}$ 两向量夹角.   
设非零向量 $\pmb { a } = ( \boldsymbol { x } _ { 1 } , \boldsymbol { y } _ { 1 } )$ ， $\pmb { b } { = } ( x _ { 2 } , y _ { 2 } )$ 的夹角为 $\theta$ ， 则 cos $\theta { = } \frac { \textbf { \em a } \cdot \textbf { \em b } } { \mid \textbf { \em a } \mid \textbf { \cdot } \mid b \mid } { = } \frac { x _ { 1 } x _ { 2 } + y _ { 1 } y _ { 2 } } { \sqrt { x _ { 1 } ^ { 2 } + y _ { 1 } ^ { 2 } } \sqrt { x _ { 2 } ^ { 2 } + y _ { 2 } ^ { 2 } } } .$   
$\textcircled{3}$ 两向量夹角为锐角或钝角.   
若向量 ${ \boldsymbol { a } } , { \boldsymbol { b } }$ 的夹角为锐角 $\Longleftrightarrow a \cdot b > 0$ ，且 $a , b$ 不共线； 若向量 ${ \boldsymbol { a } } , { \boldsymbol { b } }$ 的夹角为钝角 $\Longleftrightarrow a \cdot b { < } 0$ ，且 $a , b$ 不共线.

结论 $\pmb { 2 } : \mathbb { \mathcal { D } } | \pmb { a } \pmb { b } | ^ { 2 } = \pmb { a } ^ { 2 } \pm 2 \pmb { a } \cdot \pmb { b } + \pmb { b } ^ { 2 }$ $\textcircled{2}$ 设向量 $a , b$ 的夹角 $\theta$ ，则 $\cos \theta { = } \frac { \pmb { a } \cdot \pmb { b } } { | \pmb { a } | \times | \pmb { b } | }$ 方法：线段长度转化为向量的模，三角形内角转化为向量的夹角.

注意：三角形内角与向量的夹角的区别，向量的夹角必须共起点.

# 补充结论：

平面内有任意三个点 $O , A , B$ ,若 $P$ 是线段 $A B$ 的中点，则 ${ \overrightarrow { O P } } = { \frac { 1 } { 2 } } ( { \overrightarrow { O A } } +$ B).

# 类型七：向量的数量积问题

情形1:处理平面向量数量积的一般方法.

方法1:定义法，通过平移向量，使两个向量共起点，然后利用两向量的模和夹角；

方法2：基底法，即合理选择一组基底（一般选取模和夹角均已知的两个不共线向量),将所求向量均用这组基底表示，从而转化为这两个基向量的运算.

方法3:坐标法，即合理建立坐标系，求出向量所涉及点的坐标，利用向量的坐标运算解决.

情形2：处理平面向量数量积的优化策略.

方法1:合理选择一组基底，将所有向量转化为这一组基底表示，其中选择的基底尽量能已知(或部分已知)它们的模和夹角.

方法2：优先通过建立坐标系利用坐标运算.

情形3:平面向量数量积应用.

方法：合理将非向量形式转化为向量问题.

# 解题策略

当向量的模和夹角易求时可直接使用定义法求数量积，但很多求数量积的问题并不具备上述条件,所以转而使用基底法.但是一旦能够合理建立坐标系，我们应该选择坐标法.涉及长度或角度等非向量形式也可合理转化为向量问题.

# 类型一：复数的概念及运算

情形1:实数和纯虚数的判断.

方法：对于复数 $z = a + b \mathrm { i } ( a , b \in \mathbf { R } )$ ， $\textcircled{1}$ 若 $b = 0$ ，则 $\mathcal { z }$ 为实数； $\textcircled{2}$ 若 $a = 0$ ，且 $b \neq 0$ ，则 $\mathfrak { z }$ 为纯虚数.

注意：纯虚数时考虑 $b \neq 0$

情形2:求复数的模.

方法1:利用 $| z | = | a + b \mathrm { i } | = \sqrt { a ^ { 2 } + b ^ { 2 } }$

方法2:利用复数的模的性质，如： $\vert z _ { 1 } z _ { 2 } \vert = \vert z _ { 1 } \vert \vert \vert z _ { 2 } \vert$ 21 21  
22 22

情形3：求复数.  
方法：设复数为 $a + b \mathrm { i } ( a , b \in \mathbf { R } )$ ,利用条件求 $a , b$ 的值.

# ■类型二：复数的性质

情形1:共轭复数的性质.

结论： $\textcircled { 1 } z \cdot \bar { z } = | z | ^ { 2 } = | \bar { z } | ^ { 2 }$ $\textcircled { 2 } z \in \mathbf { R } \Leftrightarrow \bar { z } = z$ $\textcircled { 3 } z ( z \ne 0 )$ 为纯虚数 $\Longleftrightarrow \overline { z } = - { \boldsymbol z }$ $\textcircled{4}$ 运算性质：$\overline { { z _ { 1 } \pm z _ { 2 } } } = \overline { { z } } _ { 1 } \pm \overline { { z } } _ { 2 } ; \ \overline { { z _ { 1 } \bullet \ z _ { 2 } } } = \overline { { z } } _ { 1 } \bullet \overline { { z } } _ { 2 } ; \ \overline { { \left( \frac { z _ { 1 } } { z _ { 2 } } \right) } } = \overline { { \frac { z _ { 1 } } { \overline { { z } } _ { 2 } } } } .$

情形2:i乘方的周期性.

结论 $\mathrm {  ~ \cdot ~ } \mathrm {  ~ \ i ^ { n } = } \left\{ \begin{array} { l l } { 1 , \ n = 4 k , } \\ { \mathrm {  ~ i ~ } , \ n = 4 k + 1 , } \\ { - 1 , \ n = 4 k + 2 , } \\ { - \mathrm { i } , \ n = 4 k + 3 , } \end{array} \right.$ 其中 $k \in \mathbf { N } .$

# 类型三：复数的几何意义

情形1:复数和向量的关系.

结论： $z = a + b \mathrm { i } ( a , b \in { \bf R } ) {  } Z ( a , b ) {  } \overrightarrow { O Z } = ( a , b ) .$

方法：由于复数、点、向量之间建立了一一对应的关系，因此可把复数、向量与解析几何联系在一起，运用数形结合的方法.

情形2：与复数有关的轨迹问题.

结论： $\textcircled{1}$ 若 $\vert z \vert = r ( r > 0 )$ ，则点 $Z$ 的集合是以原点为圆心， $r$ 为半径的圆. 若 $| z - z _ { 0 } | = r ( r > 0 )$ ，则点 $Z$ 的集合是以 $Z _ { 0 }$ 点为圆心， $r$ 为半径 的圆. $\textcircled{2}$ 若 $| z - z _ { 0 } | \leqslant r ( r > 0 )$ ，则点 $Z$ 的集合是以 $Z _ { 0 }$ 点为圆心， $r$ 为半径 的圆面. 若 $r _ { 1 } { \leqslant } | z - z _ { 0 } | { \leqslant } r _ { 2 } ( r _ { 2 } { > } r _ { 1 } { > } 0 )$ ，则点 $Z$ 的集合是以 $Z _ { 0 }$ 点为圆心， $r _ { 1 } , r _ { 2 }$ 为半径的圆环.

注意：若 $\mid z - z _ { 0 } \mid < r ( r > 0 )$ ,则不包括圆的边界.

$\textcircled{3}$ 若 $\left| z - z _ { 1 } \right| = \left| z - z _ { 2 } \right| ( z _ { 1 } \neq z _ { 2 } )$ ，则点 $Z$ 的集合是 $Z _ { 1 } Z _ { 2 }$ 的垂直平分线.

$\textcircled{4}$ 若 $| z - z _ { 1 } | + | z - z _ { 2 } | = 2 a ( 2 a >  { \boldsymbol { Z } } _ { 1 }  { \boldsymbol { Z } } _ { 2 } )$ ，则点 $Z$ 的集合是以 $Z _ { 1 }$ ， $Z _ { 2 }$ 为 焦点，长轴长为 $2 a$ 的椭圆.

若 $| z - z _ { 1 } | - | z - z _ { 2 } | = 2 a$ （ $\cdot 2 a { < } Z _ { 1 } Z _ { 2 } )$ ，则点 $Z$ 的集合是以 $Z _ { 1 }$ ， $Z _ { 2 }$ 为焦 点，长轴长为 $2 a$ 的双曲线一支.

# 第1讲多面体、平面性质定理、空间直线位置关系

# ■类型一：平面的基本性质及应用

情形1:证明点或线共面问题

方法1:首先由所给条件中的部分线(或点)确定一个平面,然后再证其余的线(或点)在这个平面内.

方法2：将所有条件分为两部分,然后分别确定平面,再证两平面重合.

情形2：证明点共线问题

方法1:先由两点确定一条直线,再证其他各点都在这条直线上.

方法2：直接证明这些点都在同一条特定直线上.

情形3：证明线共点问题

方法：先证其中两条直线交于一点,再证其他直线经过该点.

# ■类型二：空间两条直线的位置关系

情形：判断(证明)异面直线

方法1:反证法,假设两直线共面.

方法2：异面直线判定的一个定理,即过平面内一点与平面外一点的直线，和这个平面内不经过该点的直线是异面直线.

# 第2讲线面平行

# 类型一：证明线面平行与线线平行

情形1:证明线面平行

方法1:构造三角形(中心投影法),转化为线线平行.寻找平面内平行直线步骤如下(如图10-1或10-2)：

$\textcircled{1}$ 在直线和平面外寻找一点 $P$ $\textcircled{2}$ 连接 $P A$ ,交平面 $\alpha$ 于点 $M$ $\textcircled{3}$ 连接 $P B$ ,交平面 $\alpha$ 于点 $N$ ，$\textcircled{4}$ 连接MN，MN即为要找的平行线.

![](images/****************************************9a296a85d2b59d373fc077df.jpg)  
图10-1

![](images/d5257fff726a115731bbca7a8a86c49e72587d52cbcc8780716a00511d00f0bf.jpg)  
图10-2

方法2:构造平行四边形(平行投影法),转化为线线平行.

寻找平面内平行直线步骤如下(如图10-3)：

$\textcircled{1}$ 选择直线上两点 $A$ ，B,构造两平行直线和平面 $\alpha$ 相交于 $M$ ， $N$ ：

$\textcircled{2}$ 连接MN，MN即为要找的平行线.

方法3：构造面面平行.

构造平行平面步骤如下(如图10-4)：

$\textcircled{1}$ 过 $A$ 作 $A C$ 平行于平面 $\alpha$ 内一条直线 $A ^ { \prime } C ^ { \prime }$ $\textcircled{2}$ 连接 $B C$ ; $\textcircled{3}$ 平面ABC 即为所要找的平行平面.

情形2：证明线线平行方法1:利用中位线；

![](images/a7f0d09ed53fe69c1e40db8c0f973b83dfcbcc9fdbe0667f9c69a1ca11bbb2e0.jpg)  
图10-3

![](images/405c8497548f99d12011caea809e0ba3cdd8e009fbceac475d54c0fb8b9da5b2.jpg)  
图10-4

方法2：利用平行四边形；  
方法3：利用平行线段成比例；  
方法4:利用平行公理；  
方法5：利用线面平行性质定理；  
方法6:利用线面垂直性质定理；  
方法7：利用面面平行性质定理.

# 类型二：已知线面平行

方法：过直线 $l$ 取一平面 $\beta$ ，交与 $l$ 平行的已知平面 $\alpha$ 于直线 $m$ ，则 $l / / m$ ：

![](images/d99e75701f55f7721c1ccb37bff40876f0d5c2c4d6e4df603f6faaacbdf6aa3f.jpg)  
图10-5

# 第3讲 线面垂直、线线垂直

情形1:证明线面垂直.  
方法：证明直线与平面内两条相交直线垂直.情形2：证明线线垂直.  
方法1：利用线面垂直；  
方法2：利用线线平行；  
方法3：利用勾股定理；  
方法4：利用等腰三角形三线合一；  
方法5：利用菱形对角线互相垂直；  
方法6：利用四边形为矩形.

情形3：构造垂面. 方法：要证 $l$ 垂直于 $A B$ ,构造垂面，步骤如下： $\textcircled{1}$ 过 $A$ 找垂直于 $l$ 的直线 $A C$ $\textcircled{2}$ 连接 $B C$ ，证 $B C$ 垂直 $l$ ，则 $l$ 上平面ABC.

![](images/6bee2cefea59a83fc8ce60432140aa5aea3b33da407127d8e73bc4543e4e78aa.jpg)  
图10-6

# 第4讲 面面垂直、面面平行

# 类型一：证明面面垂直

情形1:证明面面垂直.  
方法：关键是找到和另一个平面垂直的垂线,转化为线面垂直.

情形2：找垂线.

方法1:在一个平面内找(或作)两平面交线的垂线;

方法2：分别在两个平面内找两条互相垂直的直线,再判断其中一条直线是否垂直于另一个平面内的另一条直线.

# 类型二：已知面面垂直

方法：已知面面垂直时，在其中一个平面内作两个平面交线的垂线，从而转化为线面垂直.

# 一类型三：证明面面平行

方法：在一个平面内寻找两条相交直线,证明直线与另一个平面平行.

注意：证面面平行必须先通过证线面平行，不可以直接通过证线线平行来证面面平行.

# 第5讲线线角

情形1:求异面直线所成角的三个步骤：

$\textcircled{1}$ 作：通过作平行线，得到相交直线；  
$\textcircled{2}$ 证：证明相交直线所成的角或其补角为异面直线所成的角；  
$\textcircled{3}$ 算：通过解三角形,求出该角.

情形2：找异面直线所成角的三种方法：

$\textcircled{1}$ 利用图中已有的平行线平移；  
$\textcircled{2}$ 利用特殊点(线段的端点或中点)作平行线平移;  
$\textcircled{3}$ 补形平移.

# 第6讲线面角

结论：若直线 $O C$ 与 $\mathcal { O A }$ ，OB 所成角相等,则OC 的射影为∠AOB的角平分线，且 $\scriptstyle \cos \angle A O C = \cos \angle A O D \cos \angle C O D$

![](images/27ce98163b11039c8cc2878b9fa33f8231443e9dadb343e13eb80fed35946ae5.jpg)  
图10-7

![](images/bc69c7977264f1155a4b68a8b255b17a9a4248fbdb3c285d049a8b23e167b52d.jpg)  
图10-8

结论： $\textcircled{1}$ 在三棱锥 S-ABC 中,若 $S A { = } S B { = } S C$ ，则 $S$ 在平面ABC 内的射影为 $\triangle A B C$ 的外心.

$\textcircled{2}$ 在三棱锥 S-ABC 中,若 $S A \perp B C$ ， $S B \bot A C$ ，则 $S$ 在平面ABC 内的射影为 $\triangle A B C$ 的垂心.

情形1:求线面角的三个步骤：

$\textcircled{2}$ 证：证明PA与 $A B$ 所成的角为线面角；

$\textcircled{3}$ 算：通过解直角三角形,求出该角.

![](images/****************************************b30aeca62409a834b4e241fe.jpg)  
图10-9

情形2：找垂线的常见方法.

方法1:判断垂足的位置.

方法2：找过斜线与已知平面垂直的平面,斜线与交线所成角即为线面角.

方法3:过斜线上一点找与已知平面垂直的平面,作交线的垂线.

# 第7讲二面角

方法1:定义法,即在两个面内分别作垂直于棱的射线.

方法2：垂线法.

作 $A O \bot \alpha$ ,如图10-10(或 $A O \bot l$ ,如图10-11),过 $A$ 作 $A B \perp l$ ，连接 $B O$ 证∠ABO为 $\alpha ^ { - } l ^ { - } \beta$ 为二面角的平面角.

![](images/de5379eb4f2cca8a8ef74feb4ac1a8dc5b485de43834ff21c30f79196a36da9d.jpg)  
图10-10

![](images/a7e9ca05e727834a94f82ef9a1a081de587305d5445983a266ef3482a3ba9614.jpg)  
图10-11

注意：构造二面角平面角的关键并不是寻找二面角本身,而是找到夹在两平面之间且与一个平面垂直的垂线段(或夹在两平面之间且与棱垂直的线段).如果“找"不到就“作”出来。

方法3:垂面法.作棱的垂面,即作一个平面与棱垂直，则可证该垂面与二面角的两个半平面的交线所成的角就是二面角的平面角.

方法4:间接法.不直接作出二面角的平面角.如图10-13,平面 $\alpha$ 内平面图形 ACD 在平面 $\beta$ 内的射影为平面图形CDE.设二面角大小为 $\theta$ ，射影图形的面积为$S _ { \sharp , \mathfrak { f } }$ ,原来图形的面积为 $S$ ,则cos $\theta = \frac { S _ { \sharp \ j } } { S }$ (证明略).

注意：此方法不能用于解答题.

![](images/eb3c9aa4e1fd6b1fe6374dec5f107d35c78fcbd9da6574fabc1a4d696c138fa7.jpg)  
图10-12

![](images/ad49afc98b521c5a77db5b7540a4d47043be8531ae241865c52c03d73b5ecdf5.jpg)  
图10-13

# 第8讲空间图形的表面积与体积

# ■类型一：表面积

结论：

（1）直棱柱、正棱锥、正棱台的侧面展开图及侧面积公式

<html><body><table><tr><td></td><td>直棱柱</td><td>正棱锥</td><td>正棱台</td></tr><tr><td>侧面展开图</td><td>C h</td><td>7</td><td></td></tr><tr><td>侧面积公式</td><td>S直棱柱侧=ch</td><td>1-2 S正棱锥侧 ch&#x27;</td><td>(c+c&#x27;)h&#x27; S正棱台侧</td></tr></table></body></html>

（2）圆柱、圆锥、圆台的侧面展开图及侧面积公式

<html><body><table><tr><td></td><td>圆柱</td><td>圆锥</td><td>圆台</td></tr><tr><td>侧面展开图</td><td>_2πr 150</td><td>/ 2πr ------ r0</td><td>2πr 2πri / r ·0</td></tr><tr><td>侧面积公式</td><td>S圆柱侧=2πrl</td><td>S圆锥侧=πrl</td><td>S圆台侧=π(r+r&#x27;)l</td></tr></table></body></html>

注意：表面积是侧面积与所有底面面积之和.

# 类型二：体积

方法1:直接法,即求出线与面的垂线,利用公式求体积.

方法2:割补法，即将原几何体割(补)成新几何体，找到原几何体与新几何体的体积关系.

方法3：等体积法,即利用三棱锥转换底面和高求体积.

# 第9讲距离

# ■类型一：异面直线距离

方法1:直接法，即找(或作)出公垂线段.

方法2：垂面法,即当两直线垂直时，找过一条直线且与另一条直线垂直的平面，再过垂足作直线的垂线段.

方法3：转移法，即转化为求直线与平面间距离或平行平面间的距离.

方法4：函数法，即转化为求直线上两点距离的最小值.

![](images/a627d15e88effce2c5a601fa6b4723c8d828bd29d2af9a90d68465c1641a21d4.jpg)  
图10-14

# 类型二：点到面的距离

方法1:直接法，即由该点向平面引垂线段.  
方法2：等体积法，即利用三棱锥的体积进行等积代换.  
方法3：转移法，即利用线面平行或面面平行转移到更易求距离的位置.

# 类型三：线面距、面面距

方法：关于线面、面面距离的问题一般转化为点到平面的距离,将这点的位置选择恰当，可以简化图形，简化运算.

# 第10讲球与组合体

# ■类型一：球的表面积与体积

结论：球的表面积为 $4 \pi R ^ { 2 }$ ;球的体积为 $\frac { 4 } { 3 } \pi R ^ { 3 }$

# 类型二：球的截面圆问题

结论： $\textcircled{1}$ 过球心的截面为大圆，不过球心的截面为小圆.$\textcircled{2}$ 连接球心和截面的小圆圆心的连线垂直于截面圆.$\textcircled{3}$ 如图10-15， $d ^ { 2 } + r ^ { 2 } = R ^ { 2 }$

![](images/823ffe16cb6a3d6b3c0f081833c2ab56d6439a54e1f2bd2f26abdc035e919732.jpg)  
图10-15

# 类型三：球与棱柱、锥体的外接问题

情形1:长方体外接球.

结论1:长方体外接球的直径为长方体的体对角线长，即若长方体的同一顶点的三条棱长分别为 $a \ : , \ : b \ : , \ : c$ ,外接球的半径为 $R$ ，则 $\scriptstyle 2 R = { \sqrt { a ^ { 2 } + b ^ { 2 } + c ^ { 2 } } }$

# 结论2：

（1）同一顶点出发的两两互相垂直的三条棱构成的三棱锥可补成长方体，三棱锥的外接球即为长方体的外接球.

（2）对棱的三棱锥可补形为长方体，如图10-16.

![](images/0dc68a6eec5840a146e5a63bb28a8d3bb1640a11df376430e3bb50cea4c6bb14.jpg)  
图10-16

情形2：直棱柱外接球.

# 结论：

（1）直棱柱外接球的球心到直棱柱底面的距离恰为棱柱高的 $\frac { 1 } { 2 }$ （2）关键是找到由球的半径，球心与球心在底面的射影的连线构成的三角形，通过解三角形求解.$\textcircled{1}$ 球心在底面的射影为底面多边形的外心，$\textcircled{2}$ 若截面圆为三角形外接圆，则外接圆直径 $2 r { = } \frac a { \sin A }$

如：球中内接正三棱柱,如图10-17，18,设正三棱柱的高为 $h$ ，底面三角形边长为 $a$ ，球半径为 $R$ 则 $\big ( \frac { h } { 2 } \big ) ^ { 2 } + \frac { 1 } { 3 } a ^ { 2 } = R ^ { 2 }$

![](images/8bc232bf41a49f93d5e123f706dcfcfd7a3768d3eff630a86ac09b16223da789.jpg)  
图10-17

![](images/c8a19188645c33ba8f61ed9c4134531aa3d4c89c6b08a6ef8d5a2e3b34a03e3f.jpg)  
图10-18

情形3:三棱锥外接球.

方法：关键是找到由球的半径,球心与球心在底面的射影的连线构成的三角形，通过解三角形求解.

结论： $\textcircled{1}$ 球心在底面的射影为底面多边形的外心，$\textcircled{2}$ 若截面圆为三角形外接圆，则外接圆直径 $2 r = \frac { a } { \sin A }$ 如：球中内接正三棱锥,如图10-19，20，21，22.

![](images/ed709d121c2786752a22758bd5c57c8b7568252a5a8cef9401b268adf2abefc4.jpg)  
图10-19

![](images/6d49ca11b8953d3aacc29401c940b6b8f269fa95c1aa691646b8d85d477fa011.jpg)  
图10-20

![](images/4bf3522b7de40068dbdf11648989f06a191c48e4779875521e0b124548e8ec31.jpg)  
图10-21

![](images/d5ef757ca6f4705b365baf031c94727cbccbb9db8a7556360386ea9ed3ee3624.jpg)  
图10-22

设正三棱锥的高为 $h$ ，底面三角形边长为 $a$ ,球半径为 $R$ ，则 $( R - h ) ^ { 2 } +$ ${ \frac { 1 } { 3 } } a ^ { 2 } = R ^ { 2 } .$

如：球中内接正四棱锥,如图10-23，24.

![](images/d694b67a1f054aca6714d9e42b3c91373af0b5296efb0abd32017f4119702b44.jpg)  
图10-23

![](images/db5e9d023b34d6b38093ce39fc32404cb51778836816f78425afd2c911556108.jpg)  
图10-24

设正四棱锥的高为 $h$ ，底面正方形边长为 $a$ ,球半径为 $R$ ，则 $( R - h ) ^ { 2 } +$ ${ \frac { 1 } { 2 } } a ^ { 2 } = R ^ { 2 } .$

# 一类型四：球与组合体内切问题

情形1;球与正方体相切问题.

结论：（1）正方体的内切球问题.

$\textcircled{1}$ 球与正方体各个面均相切，如图10-25，26.

![](images/3d149777a7df2877223f758fc8bfbffa2d977594cc2b019ad3a193ae01ee34ed.jpg)  
图10-25

![](images/f0a1cafe31487b41296ff946252e55fe6ee95ef4aa0a9127e26d869a2a61a29a.jpg)  
图10-26

易知 $r = \frac { 1 } { 2 } a$

$\textcircled{2}$ 球与正方体各条棱均相切,如图10-27，28.

![](images/54fd0e0639377d4e5a97b0033edbdb0f3b1aa0b5049dcb8955422c2a165a999a.jpg)  
图10-27

![](images/5a4b5f0fdda127a3a390f6602c56873c0f71c04bb2e39bac417950d0d328410d.jpg)  
图10-28

易知 $r { = } \frac { \sqrt { 2 } } { 2 } a$

情形2：锥体的内切球.

性质： $\textcircled{1}$ 若球与平面相切,则切点与球心的连线与切面垂直；$\textcircled{2}$ 内切球球心到多面体各面的距离均相等.

如： $\textcircled{1}$ 正三棱锥的内切球,如图10-29，30.

![](images/****************************************f1c2441f99b8d984d4e33ec4.jpg)  
图10-29

![](images/f650cb84180f343c81ceb634a7161e36d48a2f37acae46664c675467bb63af42.jpg)  
图10-30

方法1:截面法.由截面图知Rt△DMERt△RFO,得 $\frac { h - r } { h _ { \scriptscriptstyle 0 } } { = } \frac { r } { \frac { \sqrt { 3 } } { 6 } a }$ $( a , r$ ，

$h \ : , \ : h \ : _ { 0 }$ 分别为正三棱锥的底面边长、内切球半径、高、斜高， $M$ 为底面三角形中心， $F$ 为球 $O$ 与平面 $D A B$ 的切点)

方法2：等体积法.由 $V _ { D - A B C } = \frac { 1 } { 3 } S _ { ☉ } \cdot r$ 得 $r { = } \frac { 3 V _ { D - A B C } } { S _ { ☉ } }$

$\textcircled{2}$ 正四棱锥的内切球问题,如图10-31.

方法1:截面法.由截面图知 Rt△PHFRt△PGO,得 $\frac { h - r } { h _ { \circ } } { = } \frac { r } { \frac { a } { 2 } }$ .(a，r，

$h \ : , \ : h \ : _ { 0 }$ 分别为正四棱锥的底面边长、内切球半径、高、斜高， $H$ 为底面三角形中心， $G$ 为球 $O$ 与平面PCD 的切点)

方法2：等体积法.由 $V _ { P \cdot A B C } = \frac { 1 } { 3 } S _ { ☉ } \cdot r$ 得 $r { = } \frac { 3 V _ { P \mathrm { - } A B C } } { S _ { ☉ } } .$

# 类型五：球与旋转体的外接、内切问题

# 方法：

球与旋转体的外接和内切问题通常需作出其轴截面.

![](images/1ca1c5d56ccad6afe9b4eea5a3b7b60d43a2697b4bba30ae076946dbb985ca9f.jpg)  
图10-31

# 空间角的计算

# 类型一：线线角

方法：取两条直线 $l _ { 1 }$ ， $l _ { 2 }$ 的方向向量 $a , b$ ，两条直线 $l _ { 1 } , \ l _ { 2 }$ 的夹角为 $\theta$ ，则c $\supset \mathrm { s } \theta { \mathsf { = } } | \cos \langle a , b \rangle | = \left| { \frac { a \cdot b } { | a | \cdot | b | } } \right| .$

# 类型二：线面角

方法：设直线的方向向量 $\pmb { a }$ ,平面的法向量 $\pmb { n }$ ，直线与平面所成的角为 $\theta$ ，则 $\sin \theta { = } | \cos \langle a , n \rangle | = \left| { \frac { a \cdot n } { | a | \cdot | n | } } \right| .$

# 类型三：二面角

方法：取两个平面的法向量 $\pmb { n } _ { 1 } , \pmb { n } _ { 2 }$ ,则两个平面所成二面角与 $\langle \pmb { n } _ { 1 } , \pmb { n } _ { 2 } \rangle$ 相等或互补,其中 $\cos \langle { \pmb n } _ { 1 } , { \pmb n } _ { 2 } \rangle = \frac { { \pmb n } _ { 1 } \cdot { \pmb n } _ { 2 } } { \left| { \pmb n } _ { 1 } \right| \cdot \left| { \pmb n } _ { 2 } \right| } .$

# 第1讲直线方程

# 类型一：倾斜角与斜率

结论：直线的斜率： $k = \tan \alpha$ ，倾斜角 $\alpha$ 范围为 ${ [ 0 ^ { \circ } }$ ， $1 8 0 ^ { \circ } )$ 对比：两向量夹角范围： $\left[ 0 ^ { \circ } , \ 1 8 0 ^ { \circ } \right]$ ，线线角范围为 $\left[ 0 ^ { \circ } , \ 9 0 ^ { \circ } \right]$ ，线面角范围为 $\left[ 0 ^ { \circ } , \ 9 0 ^ { \circ } \right]$ ,二面角范围为 $\left[ 0 ^ { \circ } , \ 1 8 0 ^ { \circ } \right]$ 注意：考虑倾斜角为 $9 0 ^ { \circ }$ 时,斜率不存在.

# 类型二：直线方程

直线的方程形式：

<html><body><table><tr><td>形式</td><td>方程</td><td>局限性</td></tr><tr><td>点斜式</td><td>y-y=k(x-x1）</td><td>不能表示垂直于x轴的直线</td></tr><tr><td>斜截式</td><td>y=kx+b</td><td>不能表示垂直于x轴的直线</td></tr><tr><td>两点式</td><td>y-y1_x-x1 y2-y1x2-x1</td><td>不能表示垂直于x轴和y轴的直线</td></tr><tr><td>截距式</td><td>+=1</td><td>不能表示能表示过原点的y的直线.</td></tr><tr><td>一般式</td><td>Ax+By+C=0(A，B不同时为0)</td><td>无</td></tr></table></body></html>

# 第2讲直线与直线的位置关系

# 类型一：两条直线的平行与垂直

结论：（1）设直线 $l _ { 1 }$ ： ${ y = k _ { 1 } x + b _ { 1 } }$ ，直线 $l _ { 2 } : y = k _ { 2 } x + b _ { 2 }$ ，则  
$\textcircled{1} l _ { 1 } / / l _ { 2 }$ 的充要条件是 $\boldsymbol { k } _ { 1 } = \boldsymbol { k } _ { 2 }$ ，且 $b _ { 1 } \neq b _ { 2 }$   
$\textcircled{2} l _ { 1 } \perp l _ { 2 }$ 的充要条件是 $k _ { 1 } k _ { 2 } = - 1$   
注意：斜率不存在时单独讨论,斜率相等时要考虑重合情况.  
（2）设直线 $l _ { 1 } \colon A _ { 1 } x + B _ { 1 } y + C _ { 1 } = 0$ ，直线 $l _ { 2 } \colon A _ { 2 } x + B _ { 2 } y + C _ { 2 } = 0$ ，则$\textcircled{1} l _ { 1 } \perp l _ { 2 }$ 的充要条件是 $A _ { 1 } A _ { 2 } + B _ { 1 } B _ { 2 } = 0$   
$\textcircled{2} l _ { 1 } / / l _ { 2 }$ 的充分条件是 $\frac { A _ { 1 } } { A _ { 2 } } { = } \frac { B _ { 1 } } { B _ { 2 } } { \neq } \frac { C _ { 1 } } { C _ { 2 } }$

# 类型二：距离问题

结论：

（1）两点 $A ( x _ { 1 } , y _ { 1 } ) , B ( x _ { 2 } , y _ { 2 } )$ ，则 $A B { = } \sqrt { ( x _ { 1 } { - } x _ { 2 } ) ^ { 2 } { + } ( y _ { 1 } { - } y _ { 2 } ) ^ { 2 } }$ ：（2）点 $\boldsymbol { P } _ { \mathrm { ~ } } ( \boldsymbol { x } _ { 0 } , \ \boldsymbol { y } _ { 0 } )$ 到直线距离 $l \colon A x + B y + C = 0$ 的距离 $d =$ ${ \frac { | A x _ { 0 } + B y _ { 0 } + C | } { \sqrt { A ^ { 2 } + B ^ { 2 } } } } ;$ （3）两平行直线 $l _ { 1 }$ $: A x + B y + C _ { 1 } = 0$ 和 $l _ { 2 } \colon A x + B y + C _ { 2 } = 0$ 的距离 $d =$ ${ \frac { | C _ { 1 } - C _ { 2 } | } { \sqrt { A ^ { 2 } + B ^ { 2 } } } } ( C _ { 1 } \not = C _ { 2 } ) .$

（4）过定点 $P$ 的直线与另一点 $A$ 距离最大值为 $A P$ ,此时直线与 $A P$ 垂直.

# 类型三：过定点问题

方法1：取 $m$ 的特殊值,求出定点，再验证.  
方法2：转化为方程恒成立的形式求定点.

# 类型四：对称问题

结论：

（1）点关于点对称.

点 $P ( x , y )$ 关于 $A ( a , b )$ 的对称点 $P ( x ^ { \prime } , y ^ { \prime } )$ ，则 $\scriptstyle { \{ \frac { x + x ^ { ' } } { 2 } = a , \atop \lfloor { \frac { y + y ^ { ' } } { 2 } } = b .  } $

（2）点关于直线对称.  
点 $P ( x , y )$ 关于直线 $l$ ： $\scriptstyle y = k x + b$ 的对称点 $P ^ { \prime } ( x ^ { \prime } , y ^ { \prime } )$ ，则 $\left\{ \begin{array} { l l } { \displaystyle { \frac { y - y ^ { \prime } } { x - x ^ { \prime } } } \times k = - 1 , } \\ { \displaystyle { \frac { y + y ^ { \prime } } { 2 } } = k \times { \frac { x + x ^ { \prime } } { 2 } } + b . } \end{array} \right.$

（3）直线关于点对称.

方法1:在一条直线上取两点,转化为点关于点对称；方法2:在一条直线上取一点，转化为点关于点对称，再利用对称直线与原直线平行.（4）直线关于直线对称.求出两直线的交点，再在一条直线上取一点，转化为点关于直线对称.（5）反射问题.人射光线所在直线与反射光线所在直线关于镜面对称.（6）特殊对称问题.$\textcircled{1}$ 曲线 $f ( x , y ) { = } 0$ 与曲线 $f ( x , - y ) { = } 0$ 关于 $_ { x }$ 轴对称；$\textcircled{2}$ 曲线 $f ( x , y ) { = } 0$ 与曲线 $f ( - x , y ) { = } 0$ 关于 $y$ 轴对称；$\textcircled{3}$ 曲线 $f ( x , y ) { = } 0$ 与曲线 $f ( - x , - y ) { = } 0$ 关于原点对称；$\textcircled{4}$ 曲线 $f ( x , y ) { = } 0$ 与曲线 $f ( y , x ) { = } 0$ 关于 $y = x$ 轴对称.

# 第1讲直线与圆相交、相切、相离

# 类型一：直线与圆的位置关系

结论：（1）判断直线与圆的位置关系.

$\textcircled{1}$ 若 $d { < } r$ ，则直线与圆相交；  
$\textcircled{2}$ 若 $d = r$ ，则直线与圆相切；  
$\textcircled{3}$ 若 $d { > } r$ ,则直线与圆相离.  
特别地,若直线过定点，且定点在圆内，则直线与圆一定相交.（2）判断点 $M ( a , b )$ 与圆 $( x - x _ { 0 } ) ^ { 2 } + ( y - y _ { 0 } ) ^ { 2 } = r ^ { 2 }$ 的位置关系.$\textcircled{1}$ 若 $M$ 在圆内，则 $( a - x _ { 0 } ) ^ { 2 } + ( b - y _ { 0 } ) ^ { 2 } < r ^ { 2 }$   
$\textcircled{2}$ 若 $M$ 在圆上，则 $( a - x _ { 0 } ) ^ { 2 } + ( b - y _ { 0 } ) ^ { 2 } = r ^ { 2 }$   
$\textcircled{3}$ 若 $M$ 在圆外，则 $( a - x _ { 0 } ) ^ { 2 } + ( b - y _ { 0 } ) ^ { 2 } > r ^ { 2 } .$

# ■类型二：相交弦问题

结论1:

（1）圆心角 $\theta$ 、弦长 $L$ 、半径 $R$ 和弦心距 $d$ 中三个量可以建立关系式.  
如： $( \frac { L } { 2 } ) ^ { 2 } + d ^ { 2 } = R ^ { 2 } , d = R \cos \frac { \theta } { 2 } , \frac { L } { 2 } = R \sin \frac { \theta } { 2 } ,$ （204 $S _ { \triangle O A B } = \frac { 1 } { 2 } d L = d \sqrt { R ^ { 2 } - d ^ { 2 } }$ .(如图13-1)

![](images/8f077773d2eec282b218d17253b7cbbd9d8792554eb21d29153bfe331c786b62.jpg)  
图13-1

![](images/0fd76f4938d6efaefbcd932c863fc4daea96c02f2ea843483cc6f3e112438157.jpg)  
图13-2

（2）弦的垂直平分线过圆心.

(3)若 $A B$ 为直径,则：(如图13-2)

$\textcircled{1} C$ 在圆 $O$ 外，则 $\angle A C B { < } 9 0 ^ { \circ }$ 或 $\overrightarrow { C A } \cdot \overrightarrow { C B } > 0 )$ ;$\textcircled{2} C$ 在圆 $O$ 上，则 $\angle A C B = 9 0 ^ { \circ }$ 或 ${ \overrightarrow { C A } } \cdot { \overrightarrow { C B } } = 0 )$ ;$\textcircled{3} C$ 在圆 $O$ 内，则 $\angle A C B { > } 9 0 ^ { \circ }$ 或 $\overrightarrow { C A } \cdot \overrightarrow { C B } < 0 )$ ：

结论2：过圆内一定点，最长的弦为直径，最短的弦与过定点的直径所在直线垂直.

# 类型三：相切问题

情形1:一条直线和圆相切.

方法1:利用 $d = r$ ;

方法2：在已知切点坐标的情况下,利用圆心和切点的连线与切线垂直.

注意：优先判断点是否在圆上.

情形2：过一点的两条直线与圆相切.

结论：如图13-3,

(1)在 $\mathrm { R t } \triangle P A C$ 中,切线长 $\scriptstyle P A = { \sqrt { P C ^ { 2 } - R ^ { 2 } } }$ ;

（2）由圆外一点向圆引两条切线时，

$\textcircled { 1 } P , A , B , C$ 四点共圆(或 $A , B , C$ 三点共圆),其中 $P C$ 为直径；

$\textcircled{2}$ 两圆的方程相减可得切点弦所在的直线方程.

$\textcircled{3} P C$ 为∠APB的平分线，且垂直平分线段AB.

$$
\textcircled { 4 } { \mathrm { ~ } } A B { = } 2 \cdot { \frac { P A \cdot A C } { P C } } .
$$

![](images/990d715786d5d8fd42421935de90d28b5c296ada7eabf7d3f7c1ac88d89e799f.jpg)  
图13-3

# ■类型四：圆上点到点（或直线）的距离最值问题

情形1:圆上点到点的距离最值问题.

结论：（1）当点 $M$ 在圆外时，如图13-4:

![](images/102d31aed8ceb1a43f5cd2e095e2023a12f96aa949701d12afe46c00919c0d3d.jpg)  
图13-4

圆上点到点 $M$ 距离,在点 $A$ 处取到最大值 $M C + r$ ，在点 $B$ 取到最小值 $M C - r$ (2）当点 $M$ 在圆内时，如图13-5：

![](images/038e6ad2eddee77590a4388a811d326a580c0650d2e62c756953378a7f96b6d2.jpg)  
图13-5

圆上点到点 $M$ 距离,在点 $A$ 处取到最大值 $M C + r$ ，在点 $B$ 取到最小值$r { - } M C$ ：

情形2:圆上点到直线的距离最值问题.

结论：（1）当直线与圆相离时，如图13-6：

![](images/e4680f8b5b65fa819d736ec39faa22195360656a26ce697ed3e01f8b85d1205c.jpg)  
图13-6

圆上点到直线距离,在点 $A$ 处取到最大值 $d + r$ ，在点 $B$ 取到最小值 $d - r$ （2）当直线与圆相交时，如图13-7：

![](images/98255f272784c0f262d109b2c93f125d63e14089554e8dcc61eb9d1c866d8939.jpg)  
图13-7

优弧上点到直线距离，在点 $A$ 取到最大值 $r { \mathrel { + { d } } }$ ，劣弧上点到直线距离，在点 $B$ 取到最大值 $r - d$

# 第2讲外接圆、圆与圆

# 类型一：外接圆

方法1:三点代入圆的一般方程 $x ^ { 2 } + y ^ { 2 } + D x + E y + F = 0$ ，求解 $D , E , F$ 方法2：三角形两边的垂直平分线交点为圆心.方法3：直角三角形外接圆的直径为直角三角形的斜边.

# 方法选择

优先判断三角形是否为直角三角形.若为直角三角形，用方法3;若只涉及圆心,可用方法2;方法1可直接求出圆心和半径.

# 类型二：数形结合

方法：利用圆的几何特征分析.

# 类型三：圆与圆的位置关系

结论：两圆位置关系的判断

<html><body><table><tr><td>位置关系</td><td>d(两圆心距离)与r1，𝑟2的关系</td><td>公切线条数</td></tr><tr><td>外离</td><td>d&gt;r+r2</td><td>4</td></tr><tr><td>外切</td><td>d=r+r2</td><td>3</td></tr><tr><td>相交</td><td>|r1-r2|&lt;d&lt;r+r2</td><td>2</td></tr><tr><td>内切</td><td>d=|r1-r2l</td><td>1</td></tr><tr><td>内含</td><td>0≤d&lt;|r1-r2|</td><td></td></tr></table></body></html>

# ■类型四：圆与圆相关的性质

结论1:（1）两圆相交时，其方程相减可得相交弦的直线方程.（2）两圆相交时，两圆圆心的连线垂直平分公共弦.

结论2： $\textcircled{1}$ 两圆相切时，两圆圆心的连线过两圆的切点. $\textcircled{2}$ 圆过两点时，两点垂直平分线过圆心.

# 第1讲椭圆标准方程、定义的应用、一点在椭圆上

# 类型一：椭圆方程的标准形式

情形1:椭圆方程的判断.

结论：对于曲线方程 $\frac { x ^ { 2 } } { m } + \frac { y ^ { 2 } } { n } = 1$

（1）若表示椭圆，则 $m { > } 0$ ， $n { \stackrel { \textstyle > 0 } { \textstyle \to } }$ ， $m \neq n$ ;（2）若表示焦点在 $_ { \mathcal { X } }$ 轴上的椭圆，则 $m { > } n { > } 0$ ：（3）若表示焦点在 $y$ 轴上的椭圆，则 $n { > } m { > } 0$

情形2：判断椭圆焦点位置.

方法：关于椭圆的标准方程，判别焦点在哪个轴只要看分母的大小.如果$x ^ { 2 }$ 项的分母大于 $y ^ { 2 }$ 项的分母,那么椭圆的焦点在 $_ { \mathcal { X } }$ 轴上；反之，焦点在 $y$ 轴上.

注意：涉及椭圆方程时，必须先设(或化)为方程的标准形式，并注意区分焦点在哪个轴上.

情形3：椭圆的参数方程.

结论：对于 $\cdot \frac { x ^ { 2 } } { a ^ { 2 } } + \frac { y ^ { 2 } } { b ^ { 2 } } = 1 ( a > b > 0 )$ ，可设为 $\scriptstyle { \left\{ \begin{array} { l l } { x = a \cos \alpha , } \\ { y = b \sin \alpha } \end{array} \right. } ( 0 \leqslant \alpha < 2 \pi ) .$

# 一类型二：椭圆中基本量运算

方法：涉及 $a , b , c$ 的关系式时，利用 $b ^ { 2 } = a ^ { 2 } - c ^ { 2 }$ 消元.  
注意：椭圆离心率的取值范围为(0，1).

# 类型三：椭圆定义的应用

方法：涉及焦半径问题时,优先用定义(第一、二定义).

注意：焦半径范围.

常用结论：

以焦点在 $_ { \mathcal { X } }$ 轴的椭圆内焦点三角形为例.

<html><body><table><tr><td>图形</td><td>y P Fo F</td></tr><tr><td>定义</td><td>PF1+PF2=2a</td></tr><tr><td>离心率</td><td>e PF+PF</td></tr><tr><td>三边与顶角 （∠FPF2）关系</td><td>PF1+PF2=2a， (PF²+PF²-2PF1·PF2cos∠F1PF2=4c²</td></tr><tr><td>通径长</td><td>2×（不能直接用于解答题）</td></tr><tr><td>顶角范围</td><td>∠F1PF2在短轴顶点取最大值(不能直接用于解答题)</td></tr><tr><td>三角形面积</td><td>PF1·PF2sin∠F1PF2= FF2·lypl =b²tan 2 S△F1PF2 FPF2（最后一个不能直接用于解答题）</td></tr><tr><td>焦半径范围</td><td>[a-c，a+c]</td></tr><tr><td>焦半径长</td><td>PF1=a+exp(左焦点),PFz=a-exp(右焦点)</td></tr></table></body></html>

# 类型四：椭圆上仅涉及一个点问题

方法1：设点、代入方程、列式、消元；  
方法2：求点、代入方程、列式、求解.  
注意：考虑 $x _ { 0 }$ （或 $y _ { 0 }$ )的取值范围.

# 第2讲直线与椭圆

# ■类型一：直线与椭圆相交涉及两点问题

# 方法1:韦达定理法

设两点 $A ( x _ { 1 } , y _ { 1 } )$ ， $B ( x _ { 2 } , y _ { 2 } )$ ,直线方程与圆锥曲线方程联立，消去 $y$ 得关于 $_ { \mathcal { X } }$ 的方程 $A x ^ { 2 } + B x + C = 0$ ，由韦达定理得 $x _ { 1 } + x _ { 2 } = - \frac { B } { A }$ A，x1x2 $x _ { 1 } x _ { 2 } { = } \frac { C } { A }$ ，代人已知条件所得式子消去 $x _ { 1 }$ ， $x _ { 2 }$ (其中 $y _ { 1 } , y _ { 2 }$ 通过直线方程化为 $x _ { 1 } , x _ { 2 }$ 相关).

适用：（1）能建立 $x _ { 1 } + x _ { 2 }$ 和 $x _ { 1 } x _ { 2 }$ 的关系式,直接用韦达定理代入消去$x _ { 1 } , x _ { 2 }$ ;

（2）弦长问题.弦长公式 $A B = { \sqrt { 1 + k ^ { 2 } } } \mid x _ { 1 } - x _ { 2 } \mid = { \sqrt { 1 + { \frac { 1 } { k ^ { 2 } } } } } \mid y _ { 1 } - y _ { 2 } \mid .$

特别地，过焦点弦长用焦半径公式.如椭圆过右焦点的弦长 $A B = 2 a ^ { - }$ $e ( x _ { 1 } + x _ { 2 } )$ ：

(3)若 $A , B$ 中已知一个点的坐标,则可以利用韦达定理求出另一个点的坐标.  
特别地，若直线过原点，可以求出两点的坐标.

（4）与弦中点相关问题,其中 $\scriptstyle x _ { \# } = { \frac { x _ { 1 } + x _ { 2 } } { 2 } }$ ， $y _ { \Psi } = k x _ { \Psi } + b .$

注意：（1）设直线方程时讨论垂直于 $_ { \mathcal { X } }$ 轴的情况；（2）求值后检验 $\Delta > 0$ 或通过 $\Delta > 0$ 判断参数范围；（3）根据需要也可消去 $_ { x }$ 得关于 $y$ 的方程.

方法2：两点代入解方程组法

设两点 $A ( x _ { 1 } , y _ { 1 } )$ ， $B ( x _ { 2 } , y _ { 2 } )$ ,代人圆锥曲线方程,如,代人椭圆方程得$\scriptstyle { \left\{ \begin{array} { l l } { { \frac { x _ { 1 } ^ { 2 } } { a ^ { 2 } } } + { \frac { y _ { 1 } ^ { 2 } } { b ^ { 2 } } } = 1 , } \\ { { \frac { x _ { 2 } ^ { 2 } } { a ^ { 2 } } } + { \frac { y _ { 2 } ^ { 2 } } { b ^ { 2 } } } = 1 , } \end{array} \right. }$ 再通过条件建立 $x _ { 1 } , \ y _ { 1 }$ 与 $x _ { 2 } , y _ { 2 }$ 的关系，然后消去其中一些变（量(如消去 $x _ { 2 } , y _ { 2 }$ ,从而解关于 $x _ { 1 } , y _ { 1 }$ 的方程组).

适用：已知 $A , B$ 的多组坐标关系式.如,已知 $A , B$ 与第三点的定比分点关系.

# 方法3：点差法

设两点 $A ( x _ { 1 } , y _ { 1 } ) , B ( x _ { 2 } , y _ { 2 } ) ( x _ { 1 } { \neq } x _ { 2 } )$ ，代入圆锥曲线方程,如,代入椭圆方程得 $\scriptstyle { \left\{ \frac { x _ { 1 } ^ { 2 } } { a ^ { 2 } } + \frac { y _ { 1 } ^ { 2 } } { b ^ { 2 } } = 1 , \atop { \frac { x _ { 2 } ^ { 2 } } { a ^ { 2 } } + \frac { y _ { 2 } ^ { 2 } } { b ^ { 2 } } = 1 , } \right.}  $ 两式相减得 ${ \frac { y _ { 1 } - y _ { 2 } } { x _ { 1 } - x _ { 2 } } } \times { \frac { y _ { 1 } + y _ { 2 } } { x _ { 1 } + x _ { 2 } } } = - { \frac { b ^ { 2 } } { a ^ { 2 } } }$ 即 $k _ { A B } \times \frac { y _ { 0 } } { x _ { 0 } } =$ $- { \frac { b ^ { 2 } } { a ^ { 2 } } }$ ,其中 $A B$ 中点 $M$ 为 $( x _ { 0 } , y _ { 0 } )$ ：

适用：弦中点与弦的斜率相关的问题.

注意：（1）中点 $M$ 在椭圆内，即 $\frac { x _ { 0 } ^ { 2 } } { a ^ { 2 } } + \frac { y _ { 0 } ^ { 2 } } { b ^ { 2 } } < 1$ （2）此结论不能直接用于解答题.

# 类型二：曲线过定点问题

方法1:特殊值法.先取特殊值求出定点，再证明定点在曲线上.特别地,证明直线过定点时,可利用证三点共线的方法，即 $k _ { A P } = k _ { P B }$ 或 $\overrightarrow { A P }$ 与 $\overrightarrow { P B }$ 共线.方法2：方程恒成立法.由曲线方程化为方程恒成立，由各项系数均为0求得定点.

# 第3讲双曲线

# ■类型一：双曲线方程的标准形式

情形1:双曲线方程的判断.结论：对于曲线方程 $\frac { x ^ { 2 } } { m } + \frac { y ^ { 2 } } { n } = 1$

（1）若表示双曲线，则 $m n { < } 0$ （2）若表示焦点在 $_ { \mathcal { X } }$ 轴的双曲线，则 $m { > } 0$ ， $n { < } 0$ （3）若表示焦点在 $y$ 轴的双曲线,则 $n { \stackrel { \textstyle > 0 } { \textstyle \sim } }$ ， $m { < } 0$

情形2：判断双曲线焦点位置.

方法：如果 $x ^ { 2 }$ 项的系数是正数，那么焦点在 $_ { \mathcal { X } }$ 轴上；如果 $y ^ { 2 }$ 项的系数是正数，那么焦点在 $y$ 轴上.

注意：涉及双曲线方程时，必须先设(或化)为方程的标准形式，并注意区分焦点在哪个轴上.

# 结论：

（1）由双曲线方程求渐近线方程，可将1改写为0,求得的直线方程即为双曲线的渐近线方程，如 $\frac { x ^ { 2 } } { a ^ { 2 } } - \frac { y ^ { 2 } } { b ^ { 2 } } = 0$ 0， 或 $\frac { y ^ { 2 } } { a ^ { 2 } } - \frac { x ^ { 2 } } { b ^ { 2 } } = 0 .$

（2）已知双曲线渐近线方程，如，渐近线方程为 $y = k x$ ,则双曲线方程可设为 $( k x ) ^ { 2 } - y ^ { 2 } = \lambda ( \lambda \neq 0 )$

注意：对 $\lambda$ 分类讨论，以区分焦点在哪个轴上.

# ■类型二：双曲线中基本量运算

方法：涉及 $a$ ，b， $c$ 的关系式时,利用 $b ^ { 2 } = c ^ { 2 } - a ^ { 2 }$ 消元.注意：双曲线离心率的取值范围为 $( 1 , + \infty )$ ·

# 类型三：双曲线定义的应用

方法：涉及焦半径问题时，优先用定义(第一、二定义).  
注意：焦半径的范围.

# 常用结论：

以焦点在 $_ { x }$ 轴的双曲线中焦点三角形为例.

<html><body><table><tr><td>图形</td><td>P F O F</td></tr><tr><td>定义</td><td>|PF-PF2|=2a</td></tr><tr><td>离心率</td><td>e= [PF-PF2</td></tr><tr><td>三边与顶角 （∠F1PF2）关系</td><td>(|PF-PF2|=2a, PF²+PF²-2PF1·PF2cOs∠F1PF2=4c²</td></tr><tr><td>通径长</td><td>2×(不能直接用于解答题） a</td></tr><tr><td>三角形面积</td><td>2PF1·PF2sin∠F1PF2 2F1F2·1yp|= 62 2 ∠F1PF2 tan 2 (最后一个不能直接用于解答题) S△F1PF2</td></tr><tr><td>焦半径范围</td><td>以左焦点F为例： 若P在左支上,则PFi≥c-a; 若P在右支上,则PFi≥c十a.</td></tr></table></body></html>

# 第4讲抛物线

# □ 类型一：抛物线方程的标准形式

方法：涉及抛物线方程时,必须先设(或化)为方程的标准形式.  
注意：区分开口方向和焦点在哪个轴上.

# ■类型二：抛物线定义的应用

方法：涉及焦半径问题时，优先用定义.

结论：设过抛物线 $y ^ { 2 } { = } 2 p x ( p { > } 0 )$ 的焦点 $F$ 的弦为 $A B$ ，$\textcircled{1}$ 设 $A ( x _ { 1 } , y _ { 1 } )$ ， $B ( x _ { 2 } , y _ { 2 } )$ ，则 $A B = x _ { 1 } + x _ { 2 } + p$ $\textcircled{2}$ 以 $A B$ 为直径的圆与准线相切；$\textcircled { 3 } \frac { 1 } { F A } + \frac { 1 } { F B }$ 为定值，定值为 $\frac { 2 } { \phi }$

# ■类型三：直线与抛物线

方法1:韦达定理法

设两点 $A ( x _ { 1 } , y _ { 1 } )$ ， $B ( x _ { 2 } , y _ { 2 } )$ ,直线方程与抛物线方程联立，消去 $y$ 得关于 $_ { \mathcal { X } }$ 的方程 $A x ^ { 2 } + B x + C = 0$ ,由韦达定理得 $x _ { 1 } + x _ { 2 } = - \frac { B } { A }$ A，x1x2² $x _ { 1 } x _ { 2 } = \frac { C } { A }$ ，代人已知条件所得式子消去 $x _ { 1 }$ ， $x _ { 2 }$ （其中 $y _ { 1 } , y _ { 2 }$ 通过直线方程化为 $x _ { 1 } , x _ { 2 } )$

适用：(1)能建立 $x _ { 1 } + x _ { 2 }$ 和 $x _ { 1 } x _ { 2 }$ 的关系式，直接用韦达定理代入消去$x _ { 1 }$ ， $x _ { 2 }$ ;

（2）弦长问题.弦长公式 $A B = \sqrt { 1 + k ^ { 2 } } \mid x _ { 1 } - x _ { 2 } \mid = \sqrt { 1 + \frac { 1 } { k ^ { 2 } } } \mid y _ { 1 } - y _ { 2 } \mid .$ 特别地，过焦点弦长用抛物线定义.

(3)若A， $B$ 中已知一个点的坐标，则可以利用韦达定理求出另一个点的坐标.特别地,若直线过原点,可以求出两点的坐标.

（4）与弦中点相关问题,其中 $\boldsymbol { x } _ { \ u { \Psi } } = \frac { \boldsymbol { x } _ { 1 } + \boldsymbol { x } _ { 2 } } { 2 }$ （ ， $y _ { \Psi } = k x _ { \Psi } + b .$

注意：（1）设直线方程时讨论垂直于 $_ { x }$ 轴的情况；

（2）求值后检验 $\Delta > 0$ 或通过 $\Delta > 0$ 判断参数范围;（3）根据需要也可消去 $_ { \mathcal { X } }$ 得关于 $y$ 的方程.如直线过 $_ { \mathcal { X } }$ 轴上一个定点 $( b$ ，0)，可设为 $\scriptstyle x = m y + b$ 特别地,对于抛物线 $y ^ { 2 } = 2 p x$ 与直线联立时，消去 $x$ ,可利用 $x _ { 1 } = \frac { y _ { 1 } ^ { 2 } } { 2 p }$ $x _ { 2 } = \frac { y _ { 2 } ^ { 2 } } { 2 p }$ 消元，不一定要利用直线消元.

方法2：两点代入解方程组法

设两点 $A ( x _ { 1 } , y _ { 1 } ) , B ( x _ { 2 } , y _ { 2 } )$ ，代人抛物线方程，得 $\left\{ { \begin{array} { l } { y _ { 1 } ^ { 2 } = 2 \phi x _ { 1 } , } \\ { y _ { 2 } ^ { 2 } = 2 \phi x _ { 2 } , } \end{array} } \right.$ 再通过条件建立 $x _ { 1 } , y _ { 1 }$ 与 $x _ { 2 } , y _ { 2 }$ 的关系,然后消去其中一些变量(如消去 $x _ { 2 } , y _ { 2 }$ ，从而解关于 $x _ { 1 } , y _ { 1 }$ 的方程组).

适用：已知 $A , B$ 的坐标关系式,如,已知 $A , B$ 与第三点的定比分点关系.

方法3：点差法

设两点 $A \ ( \ x _ { 1 } , \ y _ { 1 } ) , \ B \ ( \ x _ { 2 } , \ y _ { 2 } ) \ ( \ x _ { 1 } \neq x _ { 2 } )$ ，代入抛物线方程得$\left\{ \begin{array} { l l } { y _ { 1 } ^ { 2 } = 2 \phi x _ { 1 } , } \\ { \qquad \mathrm { ~ } } \\ { y _ { 2 } ^ { 2 } = 2 \phi x _ { 2 } , } \end{array} \right.$ 两式相减得 $\frac { y _ { 1 } - y _ { 2 } } { x _ { 1 } - x _ { 2 } } { = } 2 p \times \frac { 1 } { y _ { 1 } + y _ { 2 } }$ ，即 $k _ { A B } \times y _ { 0 } = p$ ,其中 $A B$ 的中P点 $M$ 为 $( x _ { 0 } , y _ { 0 } )$ ：

适用：弦的中点与弦的斜率相关的问题.

# 第5讲 轨迹方程

方法1:直接法.建立坐标系,设动点坐标 $( x , y )$ ,将坐标代入满足的条件，化 简得动点轨迹方程.

注意：通过检验取舍方程的解.

方法2:定义法，即根据动点符合某曲线的定义（如椭圆、双曲线、抛物线、圆等)求方程.

注意：通过检验取舍方程的解.

方法3：相关点法.设未知点为 $( x , y )$ ，已知点为 $( x _ { 0 } , y _ { 0 } )$ ，先寻找 $( x , y )$ 与$( x _ { 0 } , y _ { 0 } )$ 之间的坐标关系，再将已知点代人已知的方程,从而求出未知点的轨迹方程.

方法4:参数法.若动点的横、纵坐标分别随另一变量的变化而变化,则以这个变量为参数，建立轨迹的参数方程，再消去参数.

注意：通过检验取舍方程的解.

# 第1讲 等差数列

# 类型一：等差数列的概念

情形1:判断(或证明)数列是等差数列.

方法1:定义法,即当 $n \in \mathbf { N } ^ { * }$ 时， $\cdot a _ { n + 1 } - a _ { n }$ 为同一常数.

方法2:中项公式法,即当 $\boldsymbol { n } \in \mathbf { N } ^ { \ast }$ 时， $2 a _ { n + 1 } = a _ { n } + a _ { n + 2 }$ 均成立,其推广形式为 $2 a _ { n } = a _ { n - m } + a _ { n + m } ( n , m \in \mathbf { N } ^ { * }$ 且 $n { > } m$ ）

方法3:通项公式为一次函数形式,即 $\scriptstyle a _ { n } = a n + b$

方法4：前 $n$ 项和为不含常数项的二次函数形式,即 $S _ { n } { = } a n ^ { 2 } { + } b n$ ：

注意：方法3、4只能作为判断，不能作为证明.

情形2：已知数列是等差数列.

方法1:定义法,即当 $n { \geqslant } 1$ 且 $\boldsymbol { n } \in \mathbf { N } ^ { \ast }$ 时， $a _ { n + 1 } - a _ { n }$ 为同一常数.

方法2:中项公式法,即当 $n { \geqslant } 1$ 且 $\ b { n } \in \mathbf { N } ^ { \ast }$ 时， $2 a _ { n + 1 } = a _ { n } + a _ { n + 2 }$ 均成立.

方法3:特殊值法，如，假设前3项成等差数列，求出未知数的值，再证明此时对任意 $\boldsymbol { n } \in \mathbf { N } ^ { * }$ 原数列成等差数列.

方法4:设通项公式为一次函数形式，即 $\scriptstyle a _ { n } = a n + b$

方法5：设前 $n$ 项和为不含常数项的二次函数形式,即 $S _ { n } { = } a n ^ { 2 } { + } b n$

情形3：判断数列不是等差数列.

方法：通常用特殊值法，如,取连续3项验证不成等差数列.

# 类型二：等差数列基本量运算

方法：基本量法,即等差数列中,五个基本量 $\ a _ { 1 } , d , n , a _ { n } , S _ { n }$ 中的四个量

可以建立关系式,如,知三求二.

注意：基本量法是数列方法中最基本的方法，虽然它不一定是最简洁的方法.

# 类型三：等差数列性质的应用

结论： $\textcircled{1}$ 在等差数列 $\left\{ a _ { n } \right\}$ 中,若 $m + n { = } p { + } q ( m , n , \ p , \ q { \in } \mathbf { N } ^ { * } )$ ，则 $a _ { m } +$ $a _ { n } = a _ { \scriptscriptstyle  { p } } + a _ { q }$

特别地,若 $m + n { = } 2 p$ ，则 $a _ { m } + a _ { n } = 2 a _ { \rho }$

$\textcircled{2}$ 若 $n$ 为奇数,则 $S _ { n } { = } n a _ { \frac { n + 1 } { 2 } }$ ：

结论： $\textcircled{3}$ 在等差数列 $\left\{ a _ { n } \right\}$ 中， $S _ { n }$ ， $S _ { 2 n } - S _ { n }$ ， $S _ { 3 n } - S _ { 2 n }$ 成等差数列.  
注意：不是 $S _ { n }$ ， $S _ { 2 n }$ ， $S _ { 3 n }$ 成等差数列.

# ■类型四：等差数列前 $\pmb { n }$ 项和 $S _ { n }$ 的最值问题

在等差数列 $\left\{ a _ { n } \right\}$ 中求前 $n$ 项和 $S _ { n }$ 的最值问题

方法1: $\textcircled{1}$ 若 $a _ { 1 } > 0$ ， $d { < } 0$ ,当项数 $m$ 满足 $\cdot \{ \stackrel { \textstyle ( a _ { m } \geq 0 , } { a _ { m + 1 } \leqslant 0 } $ 时， $S _ { m }$ 取最大值.$\textcircled{2}$ 若 $a _ { 1 } { < } 0 , d { > } 0$ ,当项数 $m$ 满足 $\left\{ \begin{array} { l l } { \displaystyle { a _ { m } \leqslant 0 } , } \\ { \displaystyle { a _ { m + 1 } \geqslant 0 } } \end{array} \right.$ 时， $S _ { m }$ 取最小值.

方法2：由 $S _ { n } = A n ^ { 2 } + B n$ ,结合二次函数图象分析,其对应的二次函数 $y =$ $A x ^ { 2 } + B x$ 的图象恒过原点.

# 类型五：几类特殊问题

情形1:等差数列奇偶项.  
方法：对于等差数列 $\left\{ a _ { n } \right\}$ ，  
$\textcircled{1}$ 若 $_ n$ 为奇数,则 $S _ { ☉ } - S _ { \langle \sharp } = a _ { 1 } + \frac { n - 1 } { 2 } d$ ;$\textcircled{2}$ 若莊 $_ n$ 为偶数，则 $S _ { \{ \sharp }  - S _ { \sharp } = \frac { n } { 2 } d$

情形2:三(四)个数成等差数列的表示.

方法： $\textcircled{1}$ 若三个数成等差数列，可设为 $a - d , a , a + d$ $\textcircled{2}$ 若四个数成等差数列,可设为 $a - d$ ， $a$ ， $a + d$ ， $a + 2 d$ ：

# 第2讲 等比数列

# 类型一：等比数列的概念

情形1:判断(或证明)数列是等比数列.

方法1:定义法,即当 $n \in \mathbf { N } ^ { * }$ 时， $\frac { a _ { n + 1 } } { a _ { n } }$ 为同一常数.

方法2：中项公式法，即当 $n \in \mathbf { N } ^ { \ast }$ 时， $a _ { n + 1 } ^ { 2 } = a _ { n } a _ { n + 2 }$ 均成立，其推广形式为$a _ { n } ^ { 2 } = a _ { n - m } + a _ { n + m } ( n , m \in \mathbf { N } ^ { * }$ 且 $n { > } m$ ）

方法3:通项为指数幂形式，即 $a _ { n } = a \cdot q ^ { n }$

注意：方法3只能作为判断，不能作为证明.

情形2：已知数列是等比数列.

方法1:定义法，即当 $n \in \mathbf { N } ^ { * }$ 时， $\frac { a _ { n + 1 } } { a _ { n } }$ 为同一常数.

方法2：中项公式法，即当 $\mathbf { \Phi } _ { n } \in \mathbf { N } ^ { \ast }$ 时， $a _ { n + 1 } ^ { 2 } { = } a _ { n } a _ { n + 2 }$ 均成立.

方法3：特殊值法，如前3项成等比数列，求出未知数的值，再证明此时对任意 $n \in \mathbf { N } ^ { \ast }$ 原数列成等比数列.

方法4:设通项公式为指数幂形式，即 $a _ { n } = a q ^ { n }$

情形3：判断数列不是等比数列.  
方法：通常用特殊值法，即取连续3项验证不成等比数列.

# 类型二：等比数列基本量运算

方法：基本量法,即等比数列中，五个基本量 $a _ { 1 } \colon q , n , a _ { n } \colon S _ { n }$ 中四个量可以建立关系式，如，知三求二.

注意： $\textcircled{1}$ 利用等比数列求和公式要优先考虑 $q = 1$ 的情况；$\textcircled{2}$ 等比数列中任意项均不为0,公比 $q$ 不为0；$\textcircled{3}$ 等比数列的奇数项(或偶数项)同号；$\textcircled{4}$ 基本量法是数列方法中最基本的方法,虽然它不一定是最简洁的方法.

结论： $\textcircled{1}$ 在等比数列 $\left\{ a _ { n } \right\}$ 中，若 $m + n = p + q \left( m , \ n , \ p , \ q \in \mathbf { N } ^ { * } \right)$ ，则$a _ { \scriptscriptstyle m } a _ { \scriptscriptstyle n } = a _ { \scriptscriptstyle P } a _ { \scriptscriptstyle q } .$ 特别地,若 $m + n { = } 2 p$ ，则 $a _ { m } a _ { n } = a _ { \phi } ^ { 2 }$ ：

结论： $\textcircled{2}$ 在等比数列 $\left\{ a _ { n } \right\}$ 中,当 $S _ { n } { \ne } 0$ 时,必有 $S _ { n }$ ， $S _ { 2 n } - S _ { n }$ ， $S _ { 3 n } - S _ { 2 n }$ 成等比数列.

注意：不是 $S _ { n }$ ， $S _ { 2 n }$ ， $S _ { 3 n }$ 成等比数列.

# 类型四：三（四）个数成等比数列的表示

结论： $\textcircled{1}$ 若三个数成等比数列，可设为 $\lbrack { \frac { a } { q } } , \alpha , \alpha q .$ $\textcircled{2}$ 若四个数成等比数列，可设为 $\uplus \_ { q } ^ { a } , \ u , \ u q , \ u q ^ { 2 }$

# 第3讲 数列的通项

类型一： $\underline { { \boldsymbol { a } } } _ { n } - \boldsymbol { a } _ { n - 1 } = f ( n )$ （21

方法：形如 $a _ { n } - a _ { n - 1 } { = } f ( n ) ( n { \in } \mathbf { N }$ 且 $n { \geqslant } 2 )$ 的递推关系，用叠加法，即当 $n \in \mathbf { N }$ 且 $n \geqslant 2$ 时， $a _ { n } = ( a _ { n } - a _ { n - 1 } ) + ( a _ { n - 1 } - a _ { n - 2 } ) + \cdots + ( a _ { 2 } -$ $a _ { 1 } ) + a _ { 1 }$ ：

注意： $n { = } 1$ 需检验.

$\scriptstyle { \frac { a _ { n } } { a _ { n - 1 } } } = f ( n )$

方法：形如 $\scriptstyle { \frac { a _ { n } } { a _ { n - 1 } } } = f ( n ) ( n \in \mathbf { N }$ 且 $n { \geqslant } 2 )$ 的递推关系,用叠乘法，即当 $\mathbf { \Omega } _ { n } \in \mathbf { N }$ 且 $n { \geqslant } 2$ 时， $a _ { n } = { \frac { a _ { n } } { a _ { n - 1 } } } \bullet { \frac { a _ { n - 1 } } { a _ { n - 2 } } } \bullet \cdots \bullet { \frac { a _ { 2 } } { a _ { 1 } } } \bullet a _ { 1 } .$ 注意： $n { = } 1$ 需检验.

# ■类型三：含 ${ \pmb a } _ { n }$ 与 $S _ { n }$ 的递推关系

方法：含有 $a _ { n }$ 与 $S _ { n }$ 的递推关系,利用 $a _ { n } = \left\{ _ { S _ { n } - S _ { n - 1 } , n \geqslant 2 } ^ { a _ { 1 } , n = 1 , } \right.$ 将递推关系转化为两种形式： $\textcircled{1}$ 仅含有 $a _ { n }$ 的关系式； $\textcircled{2}$ 仅含有 $S _ { n }$ 的关系式.

注意： $\textcircled{1}$ 优先考虑 $n { = } 1$ 时， $a _ { 1 } = S _ { 1 }$ 的情况.$\textcircled{2}$ 如果转化为 $a _ { n }$ 不能解决或题意提示先求 $S _ { n }$ ,那么需考虑转化为仅含有 $S _ { n }$ 的关系式.

# 类型四： $\underline { { { a } _ { n } } } = p \pmb { a } _ { n - 1 } + q$

方法：形如 $a _ { n } = p a _ { n - 1 } + q ( n \in \mathbf { N }$ 且 $n { \geqslant } 2 )$ 的递推关系，化为 $a _ { n } + { \frac { q } { p - 1 } } =$ $p \left( a _ { n - 1 } + { \frac { q } { p - 1 } } \right)$ +p1）形式.

令 $b _ { n } = a _ { n } + \frac { q } { p - 1 }$ +1即得b=pbn1，转化成{b}为等比数列，从而求数列

$\left\{ a _ { n } \right\}$ 的通项公式.

类型五： $\scriptstyle a _ { n } = p a _ { n - 1 } + f ( n )$

方法：形如 $a _ { n } = p a _ { n - 1 } + f ( n ) ( n \in \mathbf { N }$ 且 $n { \geqslant } 2 )$ 的递推形式,两边同时除以$\phi ^ { n }$ ，得 $\frac { a _ { n } } { p ^ { n } } { = } \frac { a _ { n - 1 } } { p ^ { n - 1 } } { + } \frac { f ( n ) } { p ^ { n } }$ 令 $b _ { n } { = } \frac { a _ { n } } { p ^ { n } }$ ，得 $b _ { n } = b _ { n - 1 } + { \frac { f ( n ) } { { \boldsymbol { \rho } } ^ { n } } }$ ,转化为利用叠加法求$b _ { n }$ 若 $\cdot { \frac { f ( n ) } { p ^ { n } } }$ 为常数，则 $\left\{ b _ { n } \right\}$ 为等差数列)，从而求数列 $\left\{ a _ { n } \right\}$ 的通项公式.

特例：(1）如 $a _ { n } = p a _ { n - 1 } + m q ^ { n }$ 的递推形式,两边同时除以 $q ^ { n }$ ，得 $\scriptstyle { \frac { a _ { n } } { q ^ { n } } } = { \frac { p } { q } } \times$ $\frac { a _ { n - 1 } } { q ^ { n - 1 } } + m$ 令 $b _ { n } = \frac { a _ { n } } { q ^ { n } }$ ，得 $b _ { n } = \frac { p } { q } \times b _ { n - 1 } + m$ ,转化为类型四.

(2）如 $a _ { n } = p a _ { n - 1 } + q ^ { n - 1 } ( p \neq q )$ ,可凑成 $\nonumber a _ { n } + x \bullet q ^ { n } = p ( a _ { n - 1 } + x \bullet q ^ { n - 1 } )$ ， 直接转化为等比数列.

(3）如 $a _ { n } = p a _ { n - 1 } + a n + b$ ,可凑成 $\begin{array} { r } { a _ { n } + x n + y = p \left[ a _ { n - 1 } + x \left( n - 1 \right) + y \right] } \end{array}$ 直接转化为等比数列.

类型六： $a _ { n } = \frac { p a _ { n - 1 } } { f ( n ) a _ { n - 1 } + m }$

方法：形如 $a _ { n } = \frac { \not P a _ { n - 1 } } { f ( n ) a _ { n - 1 } + m }$ 的递推形式，两边取倒数得 $\frac { 1 } { a _ { n } } { = } \frac { m } { \not p } { \times } \frac { 1 } { a _ { n - 1 } } +$ $\frac { f ( n ) } { \phi }$ 令 $b _ { n } { = } \frac { 1 } { a _ { n } }$ ，得 $b _ { n } { = } { \frac { m } { \phi } } { \times } b _ { n - 1 } { + } { \frac { f ( n ) } { \phi } }$ ,转化为利用叠加法求 $b _ { n }$ ，从而求得 $a _ { n }$

特例：形如 $a _ { n } = \frac { \not P a _ { n - 1 } } { q a _ { n - 1 } + p }$ 的递推形式,两边取倒数得 $\frac { 1 } { a _ { n } } { = } \frac { 1 } { a _ { n - 1 } } { + } \frac { q } { p }$ .令b=$\frac { 1 } { a _ { n } }$ ，得 $b _ { n } = b _ { n - 1 } + \frac { q } { p }$ ，转化成等差数列 $\left\{ b _ { n } \right\}$ ,从而求数列 $\left\{ a _ { n } \right\}$ 的通项公式.

类型七： $p _ { 1 } a _ { 1 } + p _ { 2 } a _ { 2 } + \cdots + p _ { n } a _ { n } = f \left( n \right)$ 或 $\underline { { { a } _ { 1 } { { a } _ { 2 } } \cdots { { a } _ { n } } } } = f \left( n \right)$

方法：（1）形如 $p _ { 1 } a _ { 1 } + p _ { 2 } a _ { 2 } + \cdots + p _ { n } a _ { n } = f ( n )$   
列出 $\begin{array} { r } { \left\{ \begin{array} { l l } { \displaystyle { \phi _ { 1 } a _ { 1 } + \phi _ { 2 } a _ { 2 } + \dots + \phi _ { n } a _ { n } = f ( n ) } , } \\ { \displaystyle { \phi _ { 1 } a _ { 1 } + \phi _ { 2 } a _ { 2 } + \dots + \phi _ { n - 1 } a _ { n - 1 } = f ( n - 1 ) } } \end{array} \right. } \end{array}$ $n \in \mathbf { N } ^ { * }$ 且 $n { \geqslant } 2$ ，，  
两式相减得 f(n)-f(n-1)(n∈N\*且n≥2)，而a1=f(1).(2）形如 $a _ { 1 } a _ { 2 } \cdots a _ { n } { } = f ( n )$ ，  
列出 $\begin{array} { r } { \left\{ \begin{array} { l l } { a _ { 1 } a _ { 2 } \cdots a _ { n } = f ( n ) , } \\ { a _ { 1 } a _ { 2 } \cdots a _ { n - 1 } = f ( n - 1 ) } \end{array} \right. } \end{array}$ $n \in \mathbf { N } ^ { \ast }$ 且 $n { \geqslant } 2$ ，  
两式相除得 $a _ { n } = { \frac { f ( n ) } { f ( n - 1 ) } } ( n \in \mathbf { N } ^ { * }$ 且 $n { \geqslant } 2 )$ ，而 $a _ { 1 } { = } f ( 1 )$ ·注意： $n { = } 1$ 是否满足上述形式需检验.

■类型八： $a _ { n } + a _ { n + 1 } = f ( n )$ 或 $\underline { { \boldsymbol { a } } } _ { n } \pmb { a } _ { n + 1 } = f \left( n \right)$

方法：(1）形如 $a _ { n } + a _ { n + 1 } = f ( n )$ 的递推关系,列出 $\left\{ \begin{array} { l l } { { a _ { n } + a _ { n + 1 } = f ( n ) , } } \\ { { a _ { n + 1 } + a _ { n + 2 } = f ( n + 1 ) . } } \end{array} \right.$ 两式相减得 $\scriptstyle a _ { n + 2 } - a _ { n } = f ( n + 1 ) - f ( n )$ ,即找到隔项间的关系.(2）形如 $a _ { n } a _ { n + 1 } { = } f ( n )$ 的递推关系，列出 $\left\{ \begin{array} { l l } { { a _ { n } a _ { n + 1 } { = } f ( n ) , } } \\ { { a _ { n + 1 } a _ { n + 2 } { = } f ( n { + } 1 ) , } } \end{array} \right.$ 两式相除得 $\scriptstyle { \frac { a _ { n + 2 } } { a _ { n } } } = { \frac { f ( n + 1 ) } { f ( n ) } }$ ,即找到隔项间的关系.

# 类型九：归纳猜想

方法：列出前几项,找到数列的规律(如周期性),利用归纳猜想得数列的项.

# 第4讲 数列求和

# 类型一：分组求和法

方法：形如 $a _ { n } \pm b _ { n }$ 的形式,常采用分组求和法.

# 类型二：裂项相消法

方法：形如 $\frac { 1 } { a _ { n } ( a _ { n } + d ) }$ 或 $\frac { 1 } { { \sqrt { n + d } } + { \sqrt { n } } }$ 等形式，采用裂项相消法，即化为 ${ \frac { 1 } { d } } \times$ $\bigl ( \frac { 1 } { a _ { n } } - \frac { 1 } { a _ { n } + d } \bigr )$ ${ \frac { 1 } { d } } \times ( { \sqrt { n + d } } - { \sqrt { n } } )$ 形式再求和，

# 一类型三：错位相减法

方法：形如 $a _ { n } b _ { n }$ 形式(其中 $\left\{ a _ { n } \right\}$ 为等差数列， $\left\{ b _ { n } \right\}$ 为等比数列),采用错位相减法.

# 类型四：倒序相加法

方法：首、尾对称的两项和为定值的形式，采用倒序相加法.

# 类型五：并项相加法

方法：正负交替出现的数列形式，采用并项相加法.

# 第5讲 数列的单调性与最值问题

# ■类型一：数列的单调性

方法1:转化为函数的单调性，如利用图象分析.

注意：因为数列图象为离散的点，所以在图象分析时考虑与连续函数图象的区别.

方法2：利用 $a _ { n + 1 } - a _ { n }$ 与0的关系(或 $\frac { a _ { n + 1 } } { a _ { n } }$ 与1的关系,其中 $a _ { n } > 0$ 判断(或证明)数列 $\left\{ a _ { n } \right\}$ 的单调性.

# 解题策略

数列是一种特殊的函数，所以数列问题可以优先转化为函数问题.但数列是定义域为 $\mathbf { N } ^ { \ast }$ （或其有限子集 $\left\{ 1 , 2 , \cdots , n \right\} .$ )的函数，所以又有它特殊的处理方式,要注意两者的联系与区别.

# 类型二：数列的最值问题

方法1:函数法.数列的最值问题实质为数列的单调性问题,可先转化为判断相应的函数的单调性,进而求解数列的最值.

注意：数列的定义域为 $\boldsymbol { n } \in \mathbf { N } ^ { * }$ ,在图象上表现为离散的点.

方法2：利用 $a _ { n + 1 } - a _ { n }$ 与0的关系(或 $\cdot \frac { a _ { n + 1 } } { a _ { n } }$ 与1的关系,其中 $a _ { n } > 0$ 判断数列 $\left\{ a _ { n } \right\}$ 的单调性.

方法3：设 $a _ { m }$ 为数列的最大项，则 $\begin{array} { c } { { \left\{ a _ { m } \geq a _ { m + 1 } , \right. } }  \\ { { \left. a _ { m } \geq a _ { m - 1 } . \right. } } \end{array}$ 设 $a _ { m }$ 为数列的最小项，则 $\begin{array} { c } { { \left\{ a _ { m } \leqslant a _ { m + 1 } , \right. } }  \\ { { \left. a _ { m } \leqslant a _ { m - 1 } . \right. } } \end{array}$

# 第1讲导数运算、导数背景

# □ 类型一：导数的运算

结论：

（1）几种常用函数的导数公式如下：

$C ^ { \prime } { = } 0 ( C$ $( \sin x ) ^ { \prime } { = } \cos x$ 为常数)； $\begin{array} { l } { ( x ^ { n } ) ^ { \prime } = n x ^ { n - 1 } ; } \\ { ( \cos x ) ^ { \prime } = - \sin x ; } \\ { ( a ^ { x } ) ^ { \prime } = a ^ { x } \ln a ; } \\ { ( \log _ { a } x ) ^ { \prime } = \displaystyle \frac { 1 } { x \ln a } { = } \frac { 1 } { x } { \log _ { a } } \mathrm { e } . } \end{array}$ $\left( \mathrm { e } ^ { x } \right) ^ { \prime } = \mathrm { e } ^ { x }$   
$( \ln x ) ^ { \prime } { = } { \frac { 1 } { x } } ;$

（2）两个函数四则运算的导数：

$$
( u \pm v ) ^ { \prime } = u ^ { \prime } \pm v ^ { \prime } ; \qquad ( u v ) ^ { \prime } = u ^ { \prime } v + u v ^ { \prime } ; \qquad \big ( \frac { u } { v } \big ) ^ { \prime } = \frac { u ^ { \prime } v - u v ^ { \prime } } { v ^ { 2 } } ( v \mp 0 ) .
$$

# 类型二：导数的背景

情形1:导数的物理背景.

结论：若位移 $s$ 与时间 $t$ 的关系为 $s \left( t \right)$ ，则 $s ^ { \prime } \left( t \right)$ 表示瞬时速度 $\boldsymbol { v } \left( t \right)$ ;$ { \boldsymbol { v } } ^ { \prime } ( t )$ 表示瞬时加速度 $a \left( t \right)$ ：

情形2：导数的切线背景.

（1）求切线方程

方法：设曲线 $f \left( x \right)$ 的切点为 $( \boldsymbol { x } _ { 0 } , \ \boldsymbol { y } _ { 0 } )$ ，则切线方程为 $y - f \left( x _ { 0 } \right) =$ $f ^ { \prime } ( x _ { 0 } ) ( x - x _ { 0 } )$

注意： $\textcircled{1}$ “在”与"过"的区别：“在"表示该点为切点，“过”表示该点不一定为

切点.

$\textcircled{2}$ 切点相关的三个类型：1.求切线斜率;2.切点在曲线上；3.切点不在曲线上.

（2）函数与函数图象相切方法：设切点为 $( x _ { 0 } , y _ { 0 } )$ 则 $\left\{ \begin{array} { l } { { f ^ { \prime } ( x _ { 0 } ) = g ^ { \prime } ( x _ { 0 } ) , } } \\ { { f ( x _ { 0 } ) = g ( x _ { 0 } ) . } } \end{array} \right.$ （3）函数图象上点到直线距离的最值问题方法：找与直线平行，且与函数图象相切的切线.

# 第2讲函数的单调区间、极(或最)值

# ■类型一：单调性

情形1:单调性判断.

结论： $\textcircled{1}$ 如果在某个区间上 $f ^ { \prime } ( x ) { > } 0$ ,那么 $f ( x )$ 为该区间上的增函数；$\textcircled{2}$ 如果在某个区间上 $f ^ { \prime } ( x ) { < } 0$ ,那么 $f ( x )$ 为该区间上的减函数.

□诀：原函数看增减，导函数看正负.  
注意：求单调区间前优先求定义域;单调区间不能用"U”,用“,"或“和”.  
方法：利用导数判断函数的单调性、比较大小或解不等式.

情形2:已知函数在区间上的单调性.

结论1: $\textcircled{1}$ 若 $f ( x )$ 在区间 $D$ 上为增函数，则 $f ^ { \prime } ( x ) { \geqslant } 0$ 在区间 $D$ 上恒成立；$\textcircled{2}$ 若 $f ( x )$ 在区间 $D$ 上为减函数,则 $\boldsymbol { f } ^ { \prime } \left( \boldsymbol { x } \right) { \leqslant } 0$ 在区间 $D$ 上恒成立.

注意：考虑 $f ^ { \prime } ( x ) { = } 0$ 的情况.

结论2：若函数 $f ( x )$ 的单调增(或减)区间为 $( a , b )$ ，则 $f ^ { \prime } ( x ) { = } 0$ 的两解为$a , b$ ：

# 类型二：求函数极（或最）值

求 $f ( x )$ 在闭区间 $[ a , b ]$ 上的最值.

步骤： $\textcircled{1}$ 求函数的定义域；

$\textcircled{2}$ 求 $f ^ { \prime } ( x ) { = } 0$ 在区间内的根；  
$\textcircled{3}$ 讨论极值点两侧的导数的正负确定极大值或极小值.  
$\textcircled{4}$ 将求得的极值与两端点处的函数值进行比较，得到最大值与最小值.

注意： $\textcircled{1} f ( x )$ 在 ${ \boldsymbol { x } } = { \boldsymbol { x } } _ { 0 }$ 处取极值 $\Rightarrow f ^ { \prime } ( x _ { 0 } ) = 0$ $\textcircled { 2 } f ^ { \prime } ( x _ { 0 } ) { = } 0 { \neq } f ( x )$ 在 ${ \boldsymbol { x } } = { \boldsymbol { x } } _ { 0 }$ 处取极值,所以确定极值时必须讨论 极值点两侧的单调性.

# 类型三：单调性与极值的应用

方法：将原函数的极值或单调区间问题转化为 $f ^ { \prime } ( x ) { = } 0$ 有解问题.  
注意：原函数存在极值转化为 $f ^ { \prime } ( x ) { = } 0$ 有解且解的两侧 $f ^ { \prime } ( x )$ 的值异号.

# 第1讲排列组合问题

# 类型一：两个基本计数原理应用

结论：（1）加法原理

做一件事情,完成它可以有 $n$ 类办法，在第一类办法中有 $m _ { 1 }$ 种不同的方法，在第二类办法中有 $m _ { 2 }$ 种不同的方法…在第 $_ n$ 类办法中有 $m _ { n }$ 种不同的方法.那么完成这件事共有 $N { = } m _ { 1 } { + } m _ { 2 } { + } \cdots { + } m _ { n }$ 种不同的方法.

# （2）乘法原理

做一件事情，完成它需要分成 $n$ 个步骤,做第一步有 $m _ { 1 }$ 种不同的方法，做第二步有 $m _ { 2 }$ 种不同的方法…做第 $_ n$ 步有 $m _ { n }$ 种不同的方法,那么完成这件事有 $N { = } m _ { 1 } { \times } m _ { 2 } { \times } { \cdots } { \times } m _ { n }$ 种不同的方法.

# □ 类型二：排列组合问题的基本策略

策略1:列举策略.面对计数较少或限制条件复杂的问题时，可用列举策略，即将所有满足题设条件的排列与组合逐一列举出来.

策略2：先分类后分步策略,即将问题分成若干类,每一类再按步计数.

注意：分类不重复不遗漏,即每两类的交集为空集,所有各类的并集为全集.

策略3：先选后排策略.当排列与组合混合时,采取先选后排的策略.

策略4:特殊元素(位置)优先策略.

策略5：正难则反策略.对有限制条件的问题,先从总体考虑，再把不符合条件的所有情况排除.

# 类型三：排列组合问题的特殊方法

方法1:相邻问题捆绑法,即将需要相邻的元素合并为一个元素，再与其他

元素一起排列，然后合并元素内部再排列.

方法2：不相邻问题插空法，即先排没有限制条件的元素，再将有限制条件的元素按要求插入排好元素之间的空位中.

方法3：定序问题机会均等法，即对于某些元素的顺序固定的排列问题，可先全排，再除以定序元素的全排.

方法4:隔板法,即同种元素的分配问题用隔板法.

# 第2讲二项式定理

# 类型一：二项展开式系数及性质

结论：（1）二项式定理 $( a + b ) ^ { n } = \mathrm { C } _ { n } ^ { \scriptscriptstyle 0 } a ^ { n } + \mathrm { C } _ { n } ^ { \scriptscriptstyle 1 } a ^ { n - 1 } b + \mathrm { C } _ { n } ^ { \scriptscriptstyle 2 } a ^ { n - 2 } b ^ { \scriptscriptstyle 2 } + \cdots +$ $\mathbb { C } _ { n } ^ { r } a ^ { n - r } b ^ { r } + \cdots + \mathbb { C } _ { n } ^ { n } b ^ { n } \left( n \in \mathbf { N } ^ { * } \right)$ ,其中第 $r { \mathrel { + { 1 } } }$ 项 $T _ { r + 1 } { = } C _ { n } ^ { r } a ^ { n - r } b ^ { r }$ ：

注意：第 $r { + 1 }$ 项的二项式系数为 $\mathrm { C } _ { n } ^ { r }$ ，而不是 $\mathrm { C } _ { n } ^ { r + 1 }$   
(2） $( a + b ) ^ { n }$ 的二项展开式的二项式系数有如下性质：  
$\textcircled{1}$ 与首末两项"等距离"的两项的二项式系数相等,即 $\mathrm { C } _ { n } ^ { m } { = } \mathrm { C } _ { n } ^ { n - m }$   
$\textcircled { 2 } \mathrm { C } _ { n } ^ { m } + \mathrm { C } _ { n } ^ { m - 1 } = \mathrm { C } _ { n + 1 } ^ { m }$   
$\textcircled{3} n$ 为偶数时,中间一项的二项式系数最大,即为 $\mathrm { C } _ { n } ^ { \frac { n } { 2 } }$ $n$ 为奇数时,中间两项的二项式系数相等且最大,即为 $\mathrm { C } _ { n } ^ { \frac { n - 1 } { 2 } }$ 和 $\mathrm { C } _ { n } ^ { \frac { n + 1 } { 2 } }$ ：  
$\textcircled { 4 } \mathrm { C } _ { n } ^ { \scriptscriptstyle 0 } + \mathrm { C } _ { n } ^ { \scriptscriptstyle 1 } + \mathrm { C } _ { n } ^ { \scriptscriptstyle 2 } + \cdots + \mathrm { C } _ { n } ^ { n - 2 } + \mathrm { C } _ { n } ^ { n - 1 } + \mathrm { C } _ { n } ^ { \scriptscriptstyle n } = 2 ^ { n } ,$ $\mathrm { C } _ { n } ^ { \scriptscriptstyle 0 } + \mathrm { C } _ { n } ^ { \scriptscriptstyle 2 } + \mathrm { C } _ { n } ^ { \scriptscriptstyle 4 } + \dots = \mathrm { C } _ { n } ^ { \scriptscriptstyle 1 } + \mathrm { C } _ { n } ^ { \scriptscriptstyle 3 } + \mathrm { C } _ { n } ^ { \scriptscriptstyle 5 } + \dots = 2 ^ { n - 1 } \mathrm { \Delta } _ { \mathrm { i } }$   
$\textcircled { 5 } \mathrm { C } _ { m } ^ { m } + \mathrm { C } _ { m + 1 } ^ { m } + \mathrm { C } _ { m + 2 } ^ { m } + \cdots + \mathrm { C } _ { n } ^ { m } = \mathrm { C } _ { n + 1 } ^ { m + 1 } .$

# 类型二：展开式中的特定项问题

方法 $\mathbf { 1 } \colon ( a + b ) ^ { n }$ 的二项展开式中第 $r { \mathrel { + { 1 } } }$ 项为 $T _ { r + 1 } { = } \mathrm { C } _ { n } ^ { r } a ^ { n - r } b ^ { r }$ ：

注意： $\textcircled{1} \mathrm { C } _ { n } ^ { r } a ^ { r } b ^ { n - r }$ 是二项展开式中的第 $r { \mathrel { + { 1 } } }$ 项,而不是第 $r$ 项.$\textcircled{2}$ “第几项”"第几项的二项式系数”"第几项的系数"之间的区别.

方法2：三项展开式化为两项形式或合并为两项形式，从而转化为二项展开式问题.

# ■类型三：展开式中的系数和问题

方法：形如 $( a x + b ) ^ { n }$ ， $( a x ^ { 2 } + b x + c ) ^ { m } \left( x , \ a , \ b \in \mathbf { R } \right)$ 的式子求其展开式的各项系数之和，常用赋值法.

如,二项展开式 $f ( x ) = a _ { 0 } + a _ { 1 } x + a _ { 2 } x ^ { 2 } + a _ { 3 } x ^ { 3 } + \cdots + a _ { n } x ^ { n }$ ，则

$\textcircled { 1 } a _ { 0 } + a _ { 1 } + a _ { 2 } + a _ { 3 } + \dotsb + a _ { n } = f ( 1 )$ $\textcircled { 2 } a _ { 0 } - a _ { 1 } + a _ { 2 } - a _ { 3 } + \dots + ( - 1 ) ^ { n } a _ { n } = f ( - 1 )$ $\textcircled { 3 } a _ { 0 } + a _ { 2 } + a _ { 4 } + a _ { 6 } + \cdots = \frac { f ( 1 ) + f ( - 1 ) } { 2 } ;$

$\textcircled { 4 } a _ { 1 } + a _ { 3 } + a _ { 5 } + a _ { 7 } + \cdots = \frac { f ( 1 ) - f ( - 1 ) } { 2 } ;$ $\textcircled { 5 } a _ { 0 } = f ( 0 ) .$

注意：比较各项系数的和与赋值后的各项系数的和缺少哪些项.

# 类型四：二项式系数或系数的最值

方法： $\textcircled{1}$ 当展开式系数与二项式系数相同或相反时，可利用二项式系数求展开式系数.$\textcircled{2}$ 若展开式中第 $r { \mathrel { + { 1 } } }$ 项的系数最大，则 $\begin{array} { c } { { \left\{ \begin{array} { l l } { { T _ { r + 1 } \geq T _ { r + 2 } } } \\ { { T _ { r + 1 } \geq T _ { r } . } } \end{array} \right. } } \end{array}$

注意：系数最大项与二项式系数最大项的区别.

# 第1讲统计

# ■类型一：抽样方法

方法：分层抽样的步骤：

$\textcircled{1}$ 将总体按一定标准分层；  
$\textcircled{2}$ 计算各层的个体数与总体的个体数的比；  
$\textcircled{3}$ 按各层的个体数占总体的个体数的比确定各层应抽取的样本容量；  
$\textcircled{4}$ 在每一层进行抽样(可用简单随机抽样).

注意：若按比例计算所得的个体数不是整数,可作适当的近似处理.

# ■类型二：频率直方图

方法：

（1）频率直方图的制法.

把横轴分为若干段，每一线段对应一个组的组距，然后以此线段为底作一矩形,它的高等于该组的 $\frac { 1 5 \sqrt { 1 7 } - 3 } { \frac { 1 } { 2 } \textcircled { 1 } 1 5 }$ ，这样得出一系列的矩形，每个矩形的面积恰好是该组上的频率.这些矩形就构成了频率直方图.

（2）频率直方图的性质.

在频率直方图中，每个小矩形的面积等于相应各组的频率,而各组频率之和为1,因此各小矩形的面积之和也等于1.

（3）利用组中值估算平均分.  
平均分为各组的组中值乘以该组对应频率的和.

注意：频率直方图的纵轴为 $\frac { 1 5 \sqrt { 1 1 } - 3 } { \frac { 1 } { \angle A } \frac { 1 } { \angle E } }$ ;频数 $=$ 频率 $\times$ 样本容量.

# 一类型三：统计特征量（1）一平均数，众数，中位数

结论：

①平均数:如果n个数为xi，x2，…，,𝑥n,那么x=χ1+x2++xn.

$\textcircled{2}$ 众数：一般地，我们将一组数据中出现次数最多的那个数据叫作该组数据的众数.

$\textcircled{3}$ 中位数：将一组数据按大小顺序排列.如果数据的个数是奇数，那么排在正中间的数据叫作这组数据的中位数.如果数据的个数是偶数，那么排在正中间位置的两个数据的平均数叫作这组数据的中位数.

# 注意：

$\textcircled{1}$ 平均数、众数、中位数都是刻画数据集中趋势的度量值.

如果一组数据中所有数据的差异不大，那么平均数就能较好地反映这组数据的集中趋势；

当一组数据中个别数据与其他数据的大小差异很大时，通常用中位数描述这组数据的集中趋势；

当一组数据中有较多的重复数据时，常用众数来描述这组数据的集中趋势.

$\textcircled{2}$ 在一组数据中，如果有两个或两个以上数据出现的最多且出现的次数相等,那么这些数据都是这组数据的众数，但中位数是唯一的.

# □ 类型四：统计特征量（2）—极差，方差

结论：

$\textcircled{1}$ 把一组数据的最大值与最小值的差称为极差.

$\textcircled{2}$ 设一组样本数据 $x _ { 1 }$ ， $x _ { 2 }$ ，…， $x _ { n }$ ,其平均数为 $\bar { x }$ ,则称

$$
s ^ { 2 } = { \frac { 1 } { n } } { \big [ } ( x _ { 1 } - { \overline { { x } } } ) ^ { 2 } + ( x _ { 2 } - { \overline { { x } } } ) ^ { 2 } + \cdots + ( x _ { n } - { \overline { { x } } } ) ^ { 2 } { \big ] }
$$

为这个样本的方差，其算术平方根 $s = { \sqrt { { \frac { 1 } { n } } [ ( x _ { 1 } - { \bar { x } } ) ^ { 2 } + ( x _ { 2 } - { \bar { x } } ) ^ { 2 } + \cdots + ( x _ { n } - { \bar { x } } ) ^ { 2 } ] } }$ 为样本的标准差,分别简称为方差、标准差.

注意： $\textcircled{1}$ 极差、方差、标准差都是刻画数据离散程度的度量值.

标准差(方差)越大,数据的离散程度越大;标准差(方差)越小,数据的离散程度越小.

$\textcircled{2}$ 在实际应用中，常常把平均数与标准差结合起来进行决策.在平均值相等的情况下，比较方差或标准差以确定稳定性.

情形1:求 $n$ 个数据的大样本的 $k$ 百分位数方法： $\textcircled{1}$ 将所有数值按从小到大顺序排列；②计算i=n·100；$\textcircled{3}$ 如果 $i$ 是整数，则第 $k$ 百分位数是第 $i$ 项与第 $i + 1$ 项数据的平均数；如果 $i$ 不是整数,那么将其向上取整（即取大于 $i$ 的相邻整数 $j$ ），则第 $k$ 百分位数为第 $j$ 项数值.

注意： $\textcircled{1}$ 求百分位数时，一定要将数据按照从小到大的顺序排列；$\textcircled{2}$ 50百分位数即为中位数,25百分位数称为下四分位数,75百分位数称为上四分位数.

情形2:已知样本数据的频率直方图求 $k$ 百分位数.

方法： $\textcircled{1}$ 先通过计算频率确定 $k$ 百分位数所在组；$\textcircled{2} \textcircled{2} \textcircled{ k }$ 百分位数 $=$ 该组的起始值 $+$ 组距 $\times$ 所占对应比例.

# 类型六：线性回归分析

结论：判断相关关系的方法1- -散点图法.

在散点图中，散点呈从左下向右上方向发展的趋势，我们称这两个变量之间正相关.同理，如果具有相关关系的两个变量的散点图呈从左上逐渐向右下方向发展的趋势，那么称这两个变量之间负相关.

结论：（1）判断相关关系的方法2——相关系数法.

线性相关系数 $r = { \frac { \displaystyle \sum _ { i = 1 } ^ { n } ( x _ { i } - { \bar { x } } ) ( y _ { i } - { \bar { y } } ) } { \displaystyle \sqrt { \sum _ { i = 1 } ^ { n } ( x _ { i } - { \bar { x } } ) ^ { 2 } \sum _ { i = 1 } ^ { n } ( y _ { i } - { \bar { y } } ) ^ { 2 } } } }$ $= \frac { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } y _ { i } - n \bar { x } \bullet \bar { y } } { \sqrt { \big ( \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } ^ { 2 } - n \bar { x } ^ { 2 } \big ) \big ( \displaystyle \sum _ { i = 1 } ^ { n } y _ { i } ^ { 2 } - n \bar { y } ^ { 2 } \big ) } } .$

相关系数 $r$ 具有下列性质：

$\textcircled { 1 } - 1 { \leqslant } r { \leqslant } 1$

$\textcircled{2}$ 当 $r { > } 0$ 时， $y$ 与 $_ { x }$ 呈正相关关系;当 $r { < } 0$ 时， $y$ 与 $x$ 呈负相关关系；

$\textcircled{3} \mid r \mid$ 越接近于1, $y$ 与 $_ { x }$ 相关的程度就越强； $| r |$ 越接近于0, $y$ 与 $x$ 相关的程度就越弱.

通常情况下，当 $| r | > 0 . 5$ 时,认为线性相关关系显著；当 $| r | < 0 . 3$ 时，认为几乎没有线性相关关系.

（2）求线性回归方程

两个具有线性相关关系的变量的一组数据： $( x _ { 1 } , y _ { 1 } ) , ( x _ { 2 } , y _ { 2 } ) , \cdots , ( x _ { n } , y _ { n } ) ,$ 设其线性回归方程为 $\hat { y } = \hat { a } + \hat { b } x$ ，

$$
{ \hat { b } } = { \frac { \displaystyle \sum _ { i = 1 } ^ { n } ( x _ { i } - { \bar { x } } ) ( y _ { i } - { \bar { y } } ) } { \displaystyle \sum _ { i = 1 } ^ { n } ( x _ { i } - { \bar { x } } ) ^ { 2 } } } = { \frac { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } y _ { i } - n { \bar { x } } \bullet { \bar { y } } } { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } ^ { 2 } - n { \bar { x } } ^ { 2 } } } , { \hat { a } } = { \bar { y } } - { \hat { b } } { \bar { x } } .
$$

其中， $\hat { a }$ 称为回归截距, $\hat { b }$ 称为回归系数， $\hat { y }$ 称为回归值.

注意：回归直线一定过样本点的中心 $( { \bar { x } } , { \bar { y } } )$

# 类型七：独立性检验

结论：要推断“I与 $\mathbb { I }$ 有关系”,可按如下的步骤进行：

$\textcircled{1}$ 先提出假设 $H _ { 0 }$ ：I与 $\mathbb { I }$ 没有关系；

$\textcircled{2}$ 列出 $2 \times 2$ 列联表：

<html><body><table><tr><td></td><td>y1</td><td>y2</td><td>总计</td></tr><tr><td>X1</td><td>a</td><td>b</td><td>a+b</td></tr><tr><td>x2</td><td>c</td><td>d</td><td>c+d</td></tr><tr><td>总计</td><td>a+c</td><td>b+d</td><td>a+b+c+d</td></tr></table></body></html>

$\textcircled{3}$ 根据公式 (a+b)(a+c)(b+d)(c+d)，其中n=a+b+c+d,计算 $\chi ^ { 2 }$ 的值；

$\textcircled{4}$ 通过比较 $\chi ^ { 2 }$ 值与临界值,做出判断.

临界表：

<html><body><table><tr><td>P(x²≥x0）0.50</td><td></td><td>0.40</td><td>0.25</td><td>0.15</td><td>0.10</td><td>0.05</td><td>0.025</td><td>0.010</td><td>0.005</td><td>0.001</td></tr><tr><td>x</td><td>0.455</td><td>0.708</td><td>1.323</td><td>2.072</td><td>2.706</td><td>3.841</td><td>5.024</td><td>6.635</td><td>7.879</td><td>10.828</td></tr></table></body></html>

# 第2讲概率

# 类型一：古典概型

方法：试验中所有可能出现的结果数 $n$ ,其中事件 $A$ 包含的结果数 $m$ ，则$P \left( A \right) { = } { \frac { m } { n } } .$

注意：优先判断所研究的事件是不是等可能事件.

等可能事件的特征是：

$\textcircled{1}$ 每一次试验中所有可能出现的结果是有限的；  
$\textcircled{2}$ 每一个结果出现的可能性是相等的.

# ■类型二：互斥事件与独立事件

情形1:利用互斥和独立事件求概率.

结论：(1）若事件 $A , B$ 互斥，则 $P ( A + B ) = P ( A ) + P ( B )$

若事件 $A$ 和 $\bar { A }$ 是对立事件，则 $P ( A ) + P ( { \bar { A } } ) = 1$

推广：若事件 $A _ { 1 } , A _ { 2 } , \cdots , A _ { n }$ 互斥，则 $P ( A _ { 1 } + A _ { 2 } + \cdots + A _ { n } ) = P ( A _ { 1 } ) +$ $P ( A _ { 2 } ) + \cdots + P ( A _ { n } )$

（2）一般地,对于两个随机事件 $A , B$ ,如果 $P ( A B ) { = } P ( A ) P ( B )$ ,那么称$A , B$ 为相互独立事件.

推广：若事件 $A _ { 1 } , \ A _ { 2 } , \cdots , \ A _ { n }$ 相互独立，则 $P ( A _ { 1 } A _ { 2 } \cdots A _ { n } ) = P \left( A _ { 1 } \right)$ ·$P ( A _ { 2 } ) \bullet \cdots \bullet P ( A _ { n } )$

注意：（1）互斥事件的特征.

$\textcircled{1}$ 互斥事件是研究两个事件之间的关系；

$\textcircled{2}$ 所研究的两个事件是在一次试验中涉及；

$\textcircled{3}$ 两个互斥事件不可能同时出现.

（2）互斥事件与对立事件的关系.

互斥事件与对立事件都是研究两个事件的关系，互斥事件是不可能同时发生的两个事件，而对立事件除要求这两个事件不同时发生之外，还要求二者之一必须有一个发生.

因此,对立事件是互斥事件,但互斥事件不一定是对立事件.

从集合的角度， $A$ 与 $\bar { A }$ 为对立事件， $A \cup { \bar { A } } = U$ ， $A \cap { \bar { A } } = \varnothing$

情形2：判断 $A$ ， $B$ 相互独立.方法：两个事件 $A$ ， $B$ 相互独立的充要条件是 $P ( A B ) { = } P ( A ) P ( B )$ ：

# 类型三：条件概率

结论：（1）条件概率定义.

一般地，设 $A , B$ 为两个事件， $P \left( A \right) > 0$ ，我们称 $\frac { P ( A B ) } { P ( A ) }$ 为事件 $A$ 发生的条件下事件 $B$ 发生的条件概率，记为 $P ( B | A )$ ：

（2）条件概率性质.

$\textcircled { 1 } 0 { \leqslant } P ( B | A ) { \leqslant } 1$ ,其中 $P ( \Omega | A ) { = } 1$ ， $P ( \varnothing | A ) { = } 0$ $\textcircled{2}$ 若 $B _ { 1 }$ 和 $B _ { 2 }$ 互斥，则 $P ( ( B _ { 1 } + B _ { 2 } ) | A ) { = } P ( B _ { 1 } | A ) { + } P ( B _ { 2 } | A ) .$ （3）求条件概率方法.

方法1:利用定义，分别求 $P ( A )$ 和 $P ( A B )$ ，得 $P ( B | A ) { = } { \frac { P ( A B ) } { P ( A ) } } .$

方法2：借助古典概型概率公式,先求事件 $A$ 包含的基本事件数 $n \left( A \right)$ ，再求事件 $A$ 与事件 $B$ 的交事件中包含的基本事件数 $n \left( A B \right)$ ，得 $P \left( B \mid A \right) =$ ${ \frac { n ( A B ) } { n ( A ) } } .$

（4）乘法公式： $P ( A B ) { = } P ( B \mid A ) P ( A )$ ,可以用于求两个事件同时发生的概率.

（5）一般地,若事件 $A , B$ 满足 $P ( B | A ) { = } P ( B )$ ,则称事件 $A$ ， $B$ 独立.

# 类型四：全概率公式

结论：（1）全概率公式.

若事件 $A _ { 1 } , A _ { 2 } , \cdots , A _ { n }$ 两两互斥,且它们的和 $\sum _ { i = 1 } ^ { n } A _ { i } = \Omega$ ，且 $P ( A _ { i } ) { > } 0$ ，$i = 1 , 2 , 3 , \cdots , n$ ，则对于 $\varOmega$ 中的任意事件 $B$ ，有

$$
P ( B ) = \sum _ { i = 1 } ^ { n } P ( A _ { i } ) P ( B \mid A _ { i } ) .
$$

这个公式称为全概率公式.

（2）贝叶斯公式\*

若事件 $A _ { 1 }$ ， $A _ { 2 }$ ，…， $A _ { n }$ 两两互斥,且 $\sum _ { i = 1 } ^ { n } A _ { i } = \Omega , P ( A _ { i } ) > 0 , i = 1 , 2 ,$

$3 , \cdots , n$ ,则对于 $\varOmega$ 中的任意事件 $B$ $ ; P ( B ) { > } 0$ ，有

$$
P ( A _ { i } \mid B ) = { \frac { P ( A _ { i } ) P ( B \mid A _ { i } ) } { P ( B ) } } = { \frac { P ( A _ { i } ) P ( B \mid A _ { i } ) } { \displaystyle { \sum _ { j = 1 } ^ { n } } P ( A _ { j } ) P ( B \mid A _ { j } ) } } ,
$$

这个公式称为贝叶斯公式.

# 第3讲 常见分布模型

# ■类型一：随机变量及其分布列

结论：（1）概率分布概念.

一般地,随机变量 $X$ 有 $n$ 个不同的取值,它们分别是 $x _ { 1 }$ ， $x _ { 2 }$ ，， $x _ { n }$ ，且

$$
P ( X = x _ { i } ) = p _ { i } , i = 1 , 2 , \cdots , n ,
$$

则称为随机变量 $X$ 的概率分布列，简称 $X$ 的分布列.

<html><body><table><tr><td>X</td><td>x1</td><td>x2</td><td>…</td><td>Xn</td></tr><tr><td>P</td><td>P1</td><td>P2</td><td>：</td><td>Pn</td></tr></table></body></html>

将上表称为随机变量 $X$ 的概率分布表,统称为随机变量 $X$ 的概率分布，它们具有性质： $\textcircled { 1 } p _ { i } \geqslant 0$ ; $\textcircled { 2 } p _ { 1 } + p _ { 2 } + \cdots + p _ { n } = 1 .$

其中,离散型随机变量在某一范围内取值的概率等于它取这个范围内各个值的概率之和.

（2）求随机变量分布列的步骤.

$\textcircled{1}$ 确定随机变量的所有可能的取值；$\textcircled{2}$ 求每一个随机变量取值的概率.注意： $\textcircled{1}$ 检验所有随机变量的概率和为1.$\textcircled{2}$ 若其中有一个随机变量的概率难求,可以采取间接法.

（3）离散型随机变量的数字特征.

离散型随机变量 $X$ 的概率分布如下所示：

<html><body><table><tr><td>X</td><td>X1</td><td>X2</td><td>：</td><td>Xn</td></tr><tr><td>P</td><td>p1</td><td>P2</td><td></td><td>Pn</td></tr></table></body></html>

其中 $ { \boldsymbol { p } } _ { i } \geqslant 0$ ， $i = 1$ ，2，…， $n$ ， $p _ { 1 } + p _ { 2 } + \cdots + p _ { n } = 1 ,$

$\textcircled{1}$ 均值或数学期望：

随机变量 $X$ 的均值或数学期望,记为 $E \left( X \right)$ 或 $\mu$ ， $E ( X ) = _ { \mu } = _ { x _ { 1 } \pm }$ $x _ { 2 } p _ { 2 } + \cdots + x _ { n } p _ { n }$

均值或数学期望反映了随机变量取值的平均水平.

$\textcircled{2}$ 方差与标准差：

随机变量 $X$ 的方差,记为 $D ( X )$ 或 $\sigma ^ { 2 }$ ，

$\supset ( X ) = \sigma ^ { 2 } = ( x _ { 1 } - \mu ) ^ { 2 } \nmid P _ { 1 } + ( x _ { 2 } - \mu ) ^ { 2 } \nmid P _ { 2 } + \cdots + ( x _ { n } - \mu ) ^ { 2 } \nmid p _ { n } = \sum _ { i = 1 } ^ { n } x _ { i } ^ { 2 } \twoheadrightarrow - \frac { 1 } { 2 } .$ $\mu ^ { 2 }$ ：

$X$ 的方差 $D ( X )$ 的算术平方根称为 $X$ 的标准差,即 $\scriptstyle \sigma = { \sqrt { D ( X ) } }$

随机变量的方差和标准差都反映了随机变量的取值偏离于均值的平均程度.方差或标准差越小，随机变量偏离于均值的平均程度就越小.

$\textcircled{3}$ 均值与方差的性质： $E ( a X + b ) = a E ( X ) + b ;$ $D ( a X + b ) = a ^ { 2 } D ( X ) ( a , b$ 为常数).

# ■类型二：常见分布模型（1）—超几何分布

结论：（1）一般地,若一个随机变量 $X$ 的分布列为

$$
P ( X = r ) = \frac { \mathrm { C } _ { M } ^ { r } \mathrm { C } _ { N - M } ^ { n - r } } { \mathrm { C } _ { N } ^ { n } } ,
$$

其中 $r = 0 , 1 , 2 , \cdots , l , l = \operatorname* { m i n } \{ n , M \} ,$

则称 $X$ 服从超几何分布，记为 $X \sim H \left( n , ~ M , ~ N \right)$ ,并将 $P \left( X = r \right) =$ CuC-M，记为 $H ( r ; n , M , N )$

(2）若 $X { \sim } H ( n , M , N )$ ，则 $E ( X ) = { \frac { n M } { N } }$ ：

# □ 类型三：常见分布模型（2）—二项分布

结论：(1) $n$ 重伯努利试验.  
我们把只含有两个可能结果的试验叫作伯努利试验.  
把一个伯努利试验独立重复进行 $n$ 次所组成的随机试验称为 $n$ 重伯努利试验.

(2）二项分布.若随机变量 $X$ 的分布列为

$$
P ( X = k ) = \complement _ { n } ^ { k } \hat { p } ^ { k } ( 1 - \hat { p } ) ^ { n - k } \left( 0 < \hat { p } < 1 , \ k = 0 , \ 1 , \ 2 , \ \cdots , \ n \right) ,
$$

则称随机变量 $X$ 服从参数为 $n , ~ p$ 的二项分布，记作 $X { \sim } B ( n , p )$

# 注意：

$n$ 重伯努利试验的特征：

$\textcircled{1}$ 试验的次数不止一次，而是多次，即次数 $n { \geqslant } 2$   
$\textcircled{2}$ 每次试验的条件是一样的，即是概率相同的重复性试验；  
$\textcircled{3}$ 每次试验的结果只有事件发生或不发生两种结果；  
$\textcircled{4}$ 每次试验是相对独立的，试验的结果互不影响，同一试验结果可以发生 $k$ 次.

# 典型实例：

反复掷抛一枚均匀的硬币；已知产品合格率的抽样；已知射手命中率的射 击;有放回的抽样等等.

(3）若 $X \sim B \ ( n , \ p )$ ，则 $E \left( X \right) = n p$ ， $D \left( X \right) = n p$ (1-p)， $\sigma =$ $\sqrt { n \boldsymbol { p } ( 1 - \boldsymbol { p } ) }$

（4）二项分布与超几何分布关系.

$\textcircled{1}$ 超几何分布的抽取是不放回抽取，所以各次抽取不独立；二项分布的抽取是有放回抽取，所以各次抽取相互独立.

$\textcircled{2}$ 当 $n$ 较小， $N$ 很大时,超几何分布的概率计算可以近似地用二项分布来代替.因为当 $n$ 较小而产品总数 $N$ 很大时，不放回抽样近似于放回抽样.

# ■类型四：常见分布模型（3）—正态分布

结论：（1）正态密度曲线的性质.  
函数 $P ( x ) { = } \frac { 1 } { \sqrt { 2 \pi } \sigma } \mathrm { e } ^ { - \frac { ( x - \mu ) ^ { 2 } } { 2 \sigma ^ { 2 } } } \left( x \in \mathbf { R } \right)$ 的图象称为正态密度曲线.

$\textcircled{1}$ 当 $x { < } \mu$ 时，曲线上升；当 $x > \mu$ 时，曲线下降;当曲线向左右两边无限延伸时，以 $_ { \mathcal { X } }$ 轴为渐近线；

$\textcircled{2}$ 曲线关于直线 $x = \mu$ 对称；  
$\textcircled{3} \sigma$ 越大，曲线越扁平； $\sigma$ 越小,曲线越尖陡.  
$\textcircled{4}$ 在曲线下方和 $x$ 轴上方范围内的区域面积为1.$\textcircled{5}$ 曲线在 $x = \mu$ 处达到峰值 $\frac { 1 } { \sigma { \sqrt { 2 \pi } } }$

(2）正态分布.

设 $X$ 是一个随机变量,若对任给区间 $( a , b ]$ ， $P$ $\scriptstyle \prime ( a < X \leqslant b )$ 是正态密度曲线下方和 $_ { \mathcal { X } }$ 轴上 $( a , b ]$ 上方所围成的图形的面积，则称随机变量 $X$ 服从参数为$\mu$ 和 $\sigma ^ { 2 }$ 的正态分布，记为 $X { \sim } N ( \mu , \sigma ^ { 2 } )$ ,其中 $\mu$ 和 $\sigma$ 是参数，分别表示总体的平均数与标准差.

结论：

$\textcircled { 1 } P ( X < a ) = 1 - P ( X \geq a ) ;$ $\textcircled { 2 } P ( X < \mu - \sigma ) = P ( X \geqslant \mu + \sigma ) .$

(3） $3 \sigma$ 原则(如图18-1所示).

$$
\begin{array} { l } { { \textcircled { 1 } P ( \mu ^ { - } \sigma { < } X { < } \mu ^ { + } \sigma ) { \approx } 6 8 . 3 \% ; } } \\ { { \textcircled { 2 } P ( \mu ^ { - } 2 \sigma { < } X { < } \mu ^ { + } 2 \sigma ) { \approx } 9 5 . 4 \% ; } } \\ { { \textcircled { 3 } P ( \mu ^ { - } 3 \sigma { < } X { < } \mu ^ { + } 3 \sigma ) { \approx } 9 9 . 7 \% . } } \end{array}
$$

![](images/7258f9377422a99c688eef669311674630c41c0cdb053c970198f1472a3c67c3.jpg)  
图18-1