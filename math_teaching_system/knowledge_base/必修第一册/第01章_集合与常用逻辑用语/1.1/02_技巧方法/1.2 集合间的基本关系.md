---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 31
source_file: 第2节 集合间的基本关系（方法册+习题册）.md
title: 第2节 集合间的基本关系（方法册+习题册）
type: skill
---

# 1.2 集合间的基本关系

# 知识梳理

知识点1：Venn 图

在数学中，我们经常用平面上封闭曲线的内部代表集合，这种图称为Venn 图．例如，假设集合 $A = \{ 1 , 2 , 3 \}$ ， $B$ （204$= \{ 3 , 4 \}$ ， $C = \{ 5 , 6 \}$ ， $D = \{ 7 , 8 \}$ ，则集合 $A$ 与 $B$ ， $C$ 与 $D$ 可以用下面的图表示.

![](images/2c13ba29d27fb078bf73a7dccfd5ce4abe1797f1791c19e5faec8ee840816d1d.jpg)

知识点2：子集、集合相等、真子集

<html><body><table><tr><td></td><td>子集</td><td>集合相等 一般地，如</td><td>真子集</td></tr><tr><td>概念</td><td>一般地，对于 两个集合A, B，如果集合 中素意一个 合B中的元 素，就称集合 A为集合B的 子集</td><td>果集合A的 任何一个元 素都是集合 B的元素， 同时集合 元素都是集 合A的元 素，那么集 合A与集合</td><td>如果AcB，但 存在元素， 集合A是集合B 的真子集</td></tr><tr><td>记法</td><td>AB</td><td>B相等 A=B</td><td>AB或BA</td></tr><tr><td>读法</td><td>“A包含于 B”或“B包 含A”</td><td>A等于B</td><td>“A真包含于 B”或“B真包 含A”</td></tr><tr><td>Venn 图</td><td>B A A(B)</td><td>A(B)</td><td>A B</td></tr><tr><td>性质</td><td>传递性： 若ACB, 则AnC</td><td>若A=B，则 AnB且 ， 明两个集合 相等</td><td>传递性： A，, 则AC</td></tr></table></body></html>

# 知识点3：空集

一般地，我们把不含任何元素的集合叫做空集，记为

# 知识点1、2

【例1】（多选）已知集合 $M = \{ 1 , 2 \}$ ，若 $M \subseteq N$ ，则集合 $N$ 可以为（）

A.{3} B. {1,2,3}C. {1,2} D.{1,3,4}解析：因为 $M \subseteq N$ ，所以 $M$ 中的元素1和2 必须都在 $N$ 中，其他元素在 $N$ 中可有可无，故选项B、C 满足要求.

答案：BC

【例2】下列能正确地表示集合 $M =$ $\{ - 2 , 0 , 1 \}$ 和 $N = \{ x \vert x ^ { 2 } + 2 x = 0 \}$ 关系的是（）

![](images/7ea04a37b73ee095537bd5cd6ae3be5973ddbfbf1c8758b64afff94fee153522.jpg)

解析：由 $x ^ { 2 } + 2 x = 0$ 可得 $x ( x + 2 ) = 0$ ，解得： $x = - 2$ 或0，所以 $N = \{ - 2 , 0 \}$ ，又 $M = \{ - 2 , 0 , 1 \}$ ，所以 $N$ 中的元素 $M$ 中都有，且 $M$ 中存在 $N$ 中没有的元素1,从而 $N \subsetneq M$ ，故选B.

答案：B

【反思】当 $N \subseteq M$ 时， $N$ 中的元素 $M$ 中全都有， $N$ 与 $M$ 可以相等；当 $N \subsetneq M$ 时， $N$ 中的元素 $M$ 中全都有， $N$ 与 $M$ 不能相等.这就是“子集”与“真子集”的区别.

# 知识点3、4

【例3】（多选）下列四个集合中是空集的是（）

$\emptyset$ ，并规定：空集是任何集合的子集．由空集的概念可以看出： $\textcircled{1}$ 空集是它本身的子集； $\textcircled{2}$ 空集是任意非空集合的真子集.

年龄大于200岁的活人这些对象组成的集合都是空集{大于3且小于2的实数绝对值等于-1的实数

# 知识点4：子集个数

对于含有 $n ( n \in \mathbf { N } ^ { * } )$ 个元素的集合 $A$ ，其子集个数为 $2 ^ { n }$ 真子集个数、非空子集个数为 $2 ^ { n } - 1$ ，非空真子集个数为$2 ^ { n } - 2$ ：

我们以一个具体的例子来解释上述结论．假设我准备邀请小帅、小美、小黑到家里玩，他们三人各自都可能来或不来，最终到我家玩的人构成一个集合，显然该集合是{小帅，小美，小黑}的子集，那么子集有几个？我们可以画树状图来找到答案：

![](images/18565d313dacb2d9912b55b4b2c829146ca9122e05dffbae3268702c17eebde2.jpg)

A.{0}   
B. $\{ { \cal { O } } \}$   
C. $\{ x \in \mathbf { R } \mid x ^ { 2 } + 1 = 0 \}$ D. $\{ x \in \mathbf { Z } | 1 < x < 2 \}$

由图可知，每人都有2种情况，三人一共有 $2 \times 2 \times 2 = 8$ 种情况，故子集有8个.

注：暂时不理解子集个数结论的原理不要紧，但结论一定要记住，后面我们学习了“计数原理”就能彻底理解

解析：A项， $\{ 0 \}$ 中有元素0，不是空集，故A项错误;  
B项，表示空集就用符号“ $\emptyset ^ { \prime \prime }$ ，无需再加“{}”，故B项错误；那 $\{ { \emptyset } \}$ 是什么呢？它是一个非空的集合，这个集合里只有一个元素，该元素就是 $\emptyset$ ，相当于这里的空集扮演了元素的角色；  
C项，由 $x ^ { 2 } + 1 = 0$ 可得 $x ^ { 2 } = - 1$ ，初中我们就知道实数的平方非负，所以该方程没有实数解，从而集合 $\{ x \in \mathbf { R } \mid x ^ { 2 } + 1 = 0 \}$ 是空集,故C项正确;  
D 项，表面上看，该集合中有元素，但注意到所给集合中，“|”前面是 $x \in \mathbf { Z }$ ，意味着该集合中的元素只能是整数，而1和2之间没有整数，所以实际上 $\{ x \in \mathbf { Z } | 1 < x < 2 \}$ 里面没有元素，是空集，故 $\mathrm { D }$ 项正确.

答案：CD

【反思】集合允许嵌套，意思是集合中的元素也可以是集合．例如， $A = \{ \{ 1 \} , \{ 1 , 2 \} \}$ 是一个集合，该集合中有两个元素，分别是{}，{,2}，它们都是集合，所以 $A$ 是由两个集合构成的集合．后续我们通过例8来强化对这种情况的理解.

【例4】已知集合 $A = \{ 1 , 2 , 3 \}$ ，则 $A$ 的子集个数为 _；非空真子集个数为解析：集合 $A$ 有3个元素，故其子集个数为 $2 ^ { 3 } = 8$ ，非空真子集个数为 $2 ^ { 3 } - 2 = 6$ ，这里子集个数不多，我们把它们罗列出来看看．想要不重不漏，可按子集中元素的个数来分类罗列，按照子集中元素的个数从低到高，集合 $A$ 的子集有 $\emptyset , \{ 1 \} , \{ 2 \} , \{ 3 \}$ ，{1,2}，{1,3}，{2,3}，{1,2,3} .

答案：8；6

【反思】务必牢记集合的子集个数结论，当集合中元素个数较多时，逐一罗列子集比较繁琐，可以用这一结论来求解问题.

# 本节核心题型

学习本节，最重要的是理解集合间的基本关系，空集作为本节的一个特殊概念，在有关问题中属于易错点，我们设计了类型Ⅰ来让大家初步理解空集．而对于本节的常考题型，主要有根据集合间的包含关系求参，子集个数问题等，我们分别设计了类型Ⅱ和类型I来覆盖它们.

# 类型Ⅰ：对空集的初步理解

【例5】已知集合 $M = \{ x \mid 2 m < x < m + 1 \}$ ，且 $M = \emptyset$ ，则实数 $m$ 的取值范围是解析：怎样能使 $M$ 为空集？只需不存在实数 $x$ 满足 $2 m < x < m + 1$ ，这意味着左边端点不小于右边端点．例如，我们找不到满足 $1 < x < 0$ 的实数 $x ,$ 也找不到满足 $1 < x < 1$ 的实数 $x$ ，但能找到满足 $1 < x < 2$ 的实数x,因为 $M = \left\{ x \vert 2 m < x < m + 1 \right\}$ 是空集，所以 $2 m \geq m + 1$ ，解得： $m \geq 1$ ，故实数 $m$ 的取值范围是 $\{ m \vert m \ge 1 \}$ ：答案： $\{ m \vert m \ge 1 \}$ （204

【变式】已知集合 $\mathcal { A } = \{ x \vert a x ^ { 2 } - 2 a x + a - 1 = 0 \}$ ，若 $A = { \emptyset }$ ，则实数 $a$ 的取值范围是

解析：因为 $A = \emptyset$ ，所以方程 $a x ^ { 2 } - 2 a x + a - 1 = 0$ 没有实数解,  
该方程平方项系数为字母，其是否为0会影响方程的类型，分析方法也不同，故讨论，当 $a = 0$ 时，方程 $a x ^ { 2 } - 2 a x + a - 1 = 0$ 即为 $- 1 = 0$ ，无解，满足 $A = { \emptyset }$ ;  
当 $a \neq 0$ 时，要使方程 $a x ^ { 2 } - 2 a x + a - 1 = 0$ 无解，应有 $\Delta = ( - 2 a ) ^ { 2 } - 4 a ( a - 1 ) = 4 a < 0$ ，所以 $a < 0$ 综上所述，实数 $a$ 的取值范围是 $\{ a | a \le 0 \}$

答案： $\{ a | a \le 0 \}$

【反思】空集的意思就是不含任何元素，反映在不等式（例5）和方程（变式）中就是它们无解.

类型ⅡI：根据集合间的包含关系求参

【例6】已知集合 $A = \{ x | - 2 < x \leq 5 \}$ ， $B = \left\{ x \vert m + 1 \leq x \leq 2 m - 1 \right\}$ ，若 $B \subseteq A$ ，则实数 $m$ 的取值范围 是

解析：注意到集合 $B$ 中不等式两侧均含未知参数，所以 $B$ 可能为空集，得先考虑这种特殊情况，因为空集是任  
意集合的子集，自然能满足 $B \subseteq A$ ，当 $B = \emptyset$ 时， $m + 1 > 2 m - 1 \textcircled { 1 }$ ，  
注意，此处不能取等，因为取等时， $m = 2$ ， $B = \{ x | 3 \leq x \leq 3 \} = \{ 3 \}$ ， $B$ 不是空集， B A+ →x-2 m+1 2m-1 5  
由 $\textcircled{1}$ 解得： $m < 2$ ，此时满足 $B \subseteq A$ ;  
当 $B \neq \emptyset$ 时，首先， $m \geq 2$ ，此时 $A$ 和 $B$ 都是连续取值的集合，要分析它们的包含关系，常画数轴来看，$\begin{array} { c } { { \left( m + 1 > - 2 \right. } } \\ { { \left. 2 m - 1 \leq 5 \right. } } \end{array}$ (不能取等,否则B会比A多出端点处的-2这个元素)  
其次如图，应有(可以取等,因为A取了5,所以不会出现 $B$ 比A多出5这个元素的情况)  
解得： $- 3 < m \leq 3$ ，结合 $m \geq 2$ 可得 $2 \leq m \leq 3$ ；综上所述，实数 $m$ 的取值范围是 $\{ m \vert m \leq 3 \}$ ：

答案： $\{ m \vert m \leq 3 \}$

【反思】 $\textcircled{1}$ 看到 $B \subseteq A$ ，就要联想 $B = \emptyset$ 的情况； $\textcircled{2}$ 分析连续取值集合间的包含关系，常画数轴来看； $\textcircled{3}$ 判断端点能否重合，往往采用“假设检验”的方法，即先假设端点重合，再检验能否满足题设的包含关系.

【变式1】集合 $A = \{ x \mid x < - 1$ 或 $x \geq 3 \}$ ， $B = \{ x \mid a x + 1 \leq 0 \}$ ，若 $B \subseteq A$ ，则实数 $a$ 的取值范围是（ ）

$\left\{ a \bigg \vert - \frac { 1 } { 3 } \leq a \leq 1 \right\}$ $\{ a | a < - 1$ $a \geq 0 \}$ Dä $\left\{ a \biggl | - \frac { 1 } { 3 } \leq a < 0 \right\}$

解析：集合 $B$ 是不等式 $a x + 1 \leq 0$ 的解构成的集合，故先解出该不等式，需讨论 $x$ 的系数 $a$ 的正负才可求解,  
i当 $a > 0$ 时，由 $a x + 1 \leq 0$ 可得 $a x \leq - 1$ ，两端除以 $a$ 得 $x \leq - { \frac { 1 } { a } }$ ，所以 $B = \left\{ x { \Biggl | } x \leq - { \frac { 1 } { a } } \right\}$   
要使 $B \subseteq A$ ，如图1，应有 $- \frac { 1 } { a } < - 1$ （此处不能取等，否则 $B$ 会比 $A$ 多出 $^ { - 1 }$ 这个元素，不满足 $B \subseteq A$ ），  
两端乘以 $- a$ 可得 $1 > a$ ，所以 $a < 1$ ，结合 $a > 0$ 可得 $0 < a < 1$ ：  
(ii)当 $a = 0$ 时， $a x + 1 \leq 0$ 即为 $1 \leq 0$ ，无解，所以 $B = \emptyset$ ，空集是任意集合的子集，故满足 $B \subseteq A$   
(ii)当 $a < 0$ 时，由 $a x + 1 \leq 0$ 可得 $a x \leq - 1$ ，两端除以 $a$ 得 $x \geq - { \frac { 1 } { a } }$ ，所以 $B = \left\{ x { \Biggl | } x \geq - { \frac { 1 } { a } } \right\}$   
要使 $B \subseteq A$ ，如图2，应有 $- { \frac { 1 } { a } } \geq 3$ （此处能取等，因为即使取等， $B$ 也不会比 $A$ 多出元素3，仍满足 $B \subseteq A$ ），  
两端乘以 $a$ 可得 $- 1 \le 3 a$ ，所以 $3 a \geq - 1$ ，故 $a \geq - { \frac { 1 } { 3 } }$ 结合 $a < 0$ 可得 $- \frac { 1 } { 3 } \leq a < 0$   
综上所述，实数 $a$ 的取值范围是 $\left\{ a \bigg \vert - \frac { 1 } { 3 } \leq a < 1 \right\}$

![](images/780fa9ee93784111b81db46904f5d6269a205da88e9197be454fcd0036cd565b.jpg)  
图1

![](images/8f86990dda04d4aac9f8779d5da4518a4f6aea9ea26c3e75595c0b420a9db581.jpg)  
图2

答案：A

【反思】这题增加了一个一元一次不等式的“外皮”，通过讨论求出解集后，便又回到了类似例6的分析.

【变式2】已知集合 $A = \{ m + 2 , 1 , 4 \}$ ， $B = \{ m ^ { 2 } , 1 \}$ ，若 $B \subseteq A$ ，则实数 $m =$ （）

A. $^ { - 1 }$ B. $^ { - 2 }$ 或0 C. $^ { - 2 }$ D. 2解析： $B \subseteq A$ 意味着 $B$ 的元素全部在 $A$ 中，两个集合中都有元素1，故只需看 $B$ 中的 $m ^ { 2 }$ 是 $A$ 中的谁,由题意， $m ^ { 2 } = m + 2$ 或 $m ^ { 2 } = 4$ ，解得： $m = - 1$ 或2或 $^ { - 2 }$ ，求出 $m$ 后，别忘了检验A, $B$ 是否满足元素互异，经检验，当 $m = - 1$ 时， $A$ ， $B$ 不满足元素互异，当 $m = 2$ 时， $A$ 不满足元素互异，所以 $m$ 只能为 $^ { - 2 }$ ：

答案：C

【反思】这题翻译出 $m ^ { 2 } \in A$ 后，接下来的思路和上一节“类型IV：元素与集合关系的分析”思路一样了吧？对于元素个数有限的集合，根据包含关系求参，只需转化为上节所学的集合间元素的对应关系，即可求出参数.

【变式3】已知集合 $A = \{ x \vert x ^ { 2 } - 5 x + 6 = 0 \}$ ， $B = \{ x \vert x ^ { 2 } - 5 x + a = 0 \}$ ，若 $B \subseteq A$ ，求 $a$ 的取值范围  
解：由 $x ^ { 2 } - 5 x + 6 = 0$ 可得 $( x - 2 ) ( x - 3 ) = 0$ ，解得： $x = 2$ 或3，所以 $A = \{ 2 , 3 \}$ ，  
$B \subseteq A$ 意味着方程 $x ^ { 2 } - 5 x + a = 0$ 的解只能在2和3中取，但别忘了，无解也行，此时 $B$ 为 $\emptyset$ ，  
当 $B = \emptyset$ 时，满足 $B \subseteq A$ ，此时方程 $x ^ { 2 } - 5 x + a = 0$ 无解，所以 $\Delta = ( - 5 ) ^ { 2 } - 4 a = 2 5 - 4 a < 0$ ，解得： $a > \frac { 2 5 } { 4 }$   
当 $B \neq \emptyset$ 时， $\Delta = 2 5 - 4 a \geq 0$ ，解得： $a \leq \frac { 2 5 } { 4 }$   
此时解只能在2和3中取，到底是只取2，还是只取3，或者都可以呢，又得讨论，若 $x = 2$ 是方程 $x ^ { 2 } - 5 x + a = 0$ 的解，则 $2 ^ { 2 } - 5 \times 2 + a = 0$ ，解得： $a = 6$ ，  
代回原方程可得 $x ^ { 2 } - 5 x + 6 = 0$ ，解得： $x = 2$ 或3，所以 $B = \{ 2 , 3 \}$ ，满足 $B \subseteq A$ ；  
若 $x = 3$ 是方程 $x ^ { 2 } - 5 x + a = 0$ 的解，则 $3 ^ { 2 } - 5 \times 3 + a = 0$ ，解得： $a = 6$ ，前面已论证此时满足 $B \subseteq A$ 综上所述，实数 $a$ 的取值范围是 $\{ a \vert a > \frac { 2 5 } { 4 }$ 或膚 $a = 6 \Bigg \}$

【反思】相比上一题，我们又增加了方程作为“外皮”，但求出方程的解后就与上一题思路一样了，是否逐渐体会到数学的“举一反三”思想了？再次提醒：涉及含参集合 $B \subseteq A$ 的问题，一定别忘了考虑 $B$ 为 $\emptyset$ 的情形.

类型IⅢI：子集个数问题

【例7】集合 $A = \{ x | - 1 < x < 3 , x \in \mathbf { N } \}$ 的子集个数是解法1：由题意， $A = \{ 0 , 1 , 2 \}$ ，故集合 $A$ 的子集有 $\emptyset$ ， $\{ 0 \} ~ , ~ \{ 1 \} ~ , ~ \{ 2 \} ~ , ~ \{ 0 , 1 \} ~ , ~ \{ 0 , 2 \} ~ , ~ \{ 1 , 2 \} ~ , ~ \{ 0 , 1 , 2 \} ~$ ，共8个.解法2：涉及子集个数，也可直接用结论处理，由题意， $A = \{ 0 , 1 , 2 \}$ ， $A$ 有3个元素，故其子集个数为 $2 ^ { 3 } = 8$ ：

答案：8

【变式1】集合 $A = \{ ( x , y ) | x ^ { 2 } + y ^ { 2 } < 2 , x \in \mathbf { Z } , y \in \mathbf { Z } \}$ 的真子集的个数是 ·解析：求 $A$ 的真子集个数，需先分析集合 $A$ 有几个元素，由于 $x$ 和y都为整数，所以不妨先由 $x ^ { 2 } + y ^ { 2 } < 2$ 估变量的范围，再看范围内有哪些整数，由 $x ^ { 2 } + y ^ { 2 } < 2$ 可得 $x ^ { 2 } < 2 - y ^ { 2 }$ ，因为 $y ^ { 2 } \geq 0$ ，所以 $2 - y ^ { 2 } \leq 2$ ，结合 $x ^ { 2 } < 2 - y ^ { 2 }$ 可得 $x ^ { 2 } < 2$ ，所以 $- { \sqrt { 2 } } < x < { \sqrt { 2 } }$ ，又因为 $x \in \mathbf { Z }$ ，所以 $x$ 只可能取 $^ { - 1 }$ ，0，1,变量 $x$ 只有3个值，情况较少，可逐一代入 $x ^ { 2 } + y ^ { 2 } < 2$ ，再来看 $y$ 的取值,当 $x = - 1$ 时，代入 $x ^ { 2 } + y ^ { 2 } < 2$ 化简得： $y ^ { 2 } < 1$ ，所以 $- 1 < y < 1$ ，结合 $y \in \mathbf { Z }$ 可得 $y$ 只能取0；当 $x = 0$ 时，代入 $x ^ { 2 } + y ^ { 2 } < 2$ 化简得： $y ^ { 2 } < 2$ ，所以 $- \sqrt { 2 } < y < \sqrt { 2 }$ ，结合 $y \in \mathbf { Z }$ 可得 $y$ 可取 $^ { - 1 }$ ，0，1;当 $x = 1$ 时，代入 $x ^ { 2 } + y ^ { 2 } < 2$ 化简得： $y ^ { 2 } < 1$ ，所以 $- 1 < y < 1$ ，结合 $y \in \mathbf { Z }$ 可得 $y$ 只能取0;所以集合 $A = \{ ( - 1 , 0 ) , ( 0 , - 1 ) , ( 0 , 0 ) , ( 0 , 1 ) , ( 1 , 0 ) \}$ ，从而 $A$ 中有5个元素，故 $A$ 的真子集个数为 $2 ^ { 5 } - 1 = 3 1$ ：

答案：31

【反思】在求子集个数的问题中，我们不关心元素本身是什么（不管是例7由数构成的集合，还是变式1由有序数对构成的集合)，我们只在乎有几个元素，进而就可以通过公式求出子集个数.

【变式2】已知集合 ${ \cal A } = \left\{ a \in { \bf N } \bigg | { \frac { 1 6 } { a - 1 } } \in { \bf N } \right\}$ $B = \{ 2 , 3 \}$ ，集合 $C$ 满足 $B \subseteq C \subseteq A$ ，则所有满足条件 的集合 $C$ 的个数为（

A. 7 B.8 C. 15 D.16

解法1：因为 $a \in \mathbf { N }$ ，所以 $a - 1$ 必为整数，故要使 $\frac { 1 6 } { a - 1 }$ 为自然数，则 $a - 1$ 只能为1，2，4，8，16,所以 $a = 2$ ，3，5，9，17，故 $A = \{ 2 , 3 , 5 , 9 , 1 7 \}$ ;  
集合 $A$ ， $B$ 的元素个数都不多，可通过罗列来看满足条件的 $C$ 有几个，  
满足 $B \subseteq C \subseteq A$ 的集合 $C$ 有 $\{ 2 , 3 \}$ ，{2,3,5}，{2,3,9}，{2,3,17}，{2,3,5,9}，{2,3,5,17}，{2,3,9,17},{2,3,5,9,17}，共8个.

解法2：求集合 $A$ 的过程同解法1， $B \subseteq C$ ，意味着 $C$ 中必须有元素2和3，又 $C \subseteq A$ ，所以 $C$ 中的元素不能超出2，3，5，9，17，于是可将 $C$ 拆成两部分，一部分是{2,3}，另一部分是{5,9,17}的子集，两者合在一起即为 $C$ ，故 $C$ 的个数就是{5,9,17}的子集个数，

集合{5,9,17}有3个元素，所以其子集个数为 $2 ^ { 3 } = 8$ ，故满足 $B \subseteq C \subseteq A$ 的集合 $C$ 有8个.

答案：B

【反思】有时题干不会直白地问子集有几个，但通过分析可以发现问题的实质就是求子集个数.

【变式3】若集合 $M = \left\{ x \in \mathbf { R } \mid ( m + 1 ) x ^ { 2 } - m x + m - 1 = 0 \right\}$ 恰有1个真子集，则实数 $m$ 的值为（

A.-1 B. $\frac { 2 { \sqrt { 3 } } } { 3 }$ C. $\pm \frac { 2 { \sqrt { 3 } } } { 3 }$ D. $\pm \frac { 2 { \sqrt { 3 } } } { 3 }$ 或-1 解析：由真子集个数可推知集合的元素个数，进而得到方程 $( m + 1 ) x ^ { 2 } - m x + m - 1 = 0$ 实数解的个数，

设集合 $M$ 的元素个数为 $n$ ，因为 $M$ 有1个真子集，所以 $2 ^ { n } - 1 = 1$ ，解得： $n = 1$ ，  
所以题设条件等价于方程 $( m + 1 ) x ^ { 2 } - m x + m - 1 = 0$ 有且仅有1个实数解,  
该方程平方项系数为字母，其是否为0对方程的类型有影响，分析方法也不同，故讨论，  
当 $m + 1 = 0$ 时， $m = - 1$ ，此时方程 $( m + 1 ) x ^ { 2 } - m x + m - 1 = 0$ 即为 $x - 2 = 0$ ，解得： $x = 2$ ，满足要求;当 $m + 1 \neq 0$ 时， $m \neq - 1$ ，要使方程 $( m + 1 ) x ^ { 2 } - m x + m - 1 = 0$ 有且仅有1个实数解，  
应有 $\Delta = ( - m ) ^ { 2 } - 4 ( m + 1 ) ( m - 1 ) = 4 - 3 m ^ { 2 } = 0$ ，解得： $m = \pm \frac { 2 \sqrt { 3 } } { 3 }$ ；  
综上所述，实数 $m$ 的值为 $\pm \frac { 2 { \sqrt { 3 } } } { 3 }$ 或 $- 1$

答案：D

【反思】要学会翻译各种隐藏的条件，例如这题明面上给的是真子集个数，其实是暗地里告诉了元素个数.

# 补充、拓展

相信大家已经对空集、集合的包含关系有简单的理解了，但其概念其实还存在比较细节的易错点，我们将通过类型IV来加深理解．另外，又该如何严格证明偏复杂集合的包含或者相等关系呢？我们将通过类型 $\mathrm { V }$ 来学习.

# 类型IV：关于集合包含关系的易错辨析

【例8】（多选）已知集合 $A = \{ x \mid x ^ { 2 } - x = 0 \}$ ， $B = \{ x \mid x \subseteq A \}$ ，则下列表示正确的是（

$$
\mathrm { ~ { ~  ~ { ~ \cal ~ A ~ } ~ } ~ } . { \mathcal { D } } \subseteq B \qquad \mathrm { ~ { ~  ~ { ~ \cal ~ B ~ } ~ } ~ } . { \mathcal { D } } \in B \qquad \mathrm { ~ { ~ \cal ~ C ~ } ~ } . { \cal { A } } \subseteq B \qquad \mathrm { ~ { ~  ~ { ~ \cal ~ D ~ } ~ } ~ } . { \cal { A } } \in B 
$$

解析：由 $x ^ { 2 } - x = 0$ 可得 $x ( x - 1 ) = 0$ ，解得： $x = 0$ 或1，所以集合 $A = \{ 0 , 1 \}$ ，  
集合 $B$ 中的 $x \subseteq A$ 怎么理解？我们知道，元素与集合的关系是∈或ε，集合与集合的关系才用と，故这里 $x$ 代表的应该是集合，什么样的集合？因为要满足 $x \subseteq A$ ，所以 $x$ 是 $A$ 的子集，故 $B$ 是由 $A$ 的子集构成的集合，因为 $A$ 的子集有 $\emptyset$ ， $\{ 0 \}$ ，{}，{0,1}，所以 $B = \{ \emptyset , \{ 0 \} , \{ 1 \} , \{ 0 , 1 \} \} \quad \textcircled { 1 } .$   
A项， $\emptyset$ 是任意集合的子集，那当然也是 $B$ 的子集，所以 $\emptyset \subseteq B$ ，故A项正确;  
B项，注意到这里 $\emptyset$ 恰好也是集合 $B$ 中的元素，所以也可以说 $\emptyset \in B$ ，故B项正确;  
C项， $A \subseteq B$ 意味着 $A$ 是 $B$ 的子集，是这样吗？我们不妨通过逐一罗列来看看 $B$ 有哪些子集，  
由 $\textcircled{1}$ 可知 $B$ 的子集有 $, \{ \emptyset \} , \{ \{ 0 \} \} , \{ \{ 1 \} \} , \ \{ \{ 1 \} \} , \ \{ \{ 0 , 1 \} \} , \ \{ \emptyset , \{ 0 \} \} , \ \{ \emptyset , \{ 1 \} \} , \ \{ \emptyset , \{ 0 , 1 \} \} , \ \{ \{ 0 \} , \{ 1 \} \} , \ \{ \{ 0 \} , \{ 1 \} \} \ ,$ $\{ \{ 0 \} , \{ 0 , 1 \} \}$ ，{{},{0,1}}， $\{ \emptyset , \{ 0 \} , \{ 1 \} \}$ ， $\{ \emptyset , \{ 0 \} , \{ 0 , 1 \} \} , \{ \emptyset , \{ 1 \} , \{ 0 , 1 \} \} , \{ \{ 0 \} , \{ 1 \} , \{ 0 , 1 \} \}$ ，{,{0},{},{0,1}},因为 $A = \{ 0 , 1 \}$ ，而{0,1}不是 $B$ 的子集，所以 $A$ 不是 $B$ 的子集，故C项错误,  
事实上，这里{0,1}是 $B$ 中的元素，所以 $A$ 和 $B$ 正确的关系应该是 $A \in B$ ，这恰好就是 $\mathrm { D }$ 项，故D项正确.

答案：ABD

【反思】前面例3已经说明了集合中的元素也可以是集合，通过本题我们能更深入地理解这一现象．元素与集

# 类型 $\mathrm { V }$ ：复杂集合间包含关系的分析与证明

【例9】已知 $\mid M = \left\{ x \mid x = 3 m - 1 , m \in { \bf Z } \right\} ~ , ~ N = \left\{ x \mid x = 3 n + 2 , n \in { \bf Z } \right\} ~ , ~ P = \left\{ x \mid x = 6 p - 1 , p \in { \bf Z } \right\} ~ .$ ，则下列结论正确的是（）

$$
M = P \subsetneq N \qquad \mathrm { B . } \quad P \subsetneq M = N \qquad \mathrm { C . } \quad M \subseteq N \subsetneq P \qquad \mathrm { D . } \quad N \subseteq M \subsetneq P
$$

解法1：观察发现三个集合中的元素比较容易列举，故可考虑列出部分元素，观察三个集合的关系，  
由题意，分别取 $m = - 4$ ， $^ { - 3 }$ ， $^ { - 2 }$ ，-1，0，1，2，3，4可得 $M = \{ \cdots , - 1 3 , - 1 0 , - 7 , - 4 , - 1 , 2 , 5 , 8 , 1 1 , \cdots \}$ ，分别取 $\imath = - 5 \ , - 4 \ , - 3 \ , - 2 \ , - 1 \ , 0 , 1 , 2 , 3$ 可得 $N = \{ \cdots , - 1 3 , - 1 0 , - 7 , - 4 , - 1 , 2 , 5 , 8 , 1 1 , \cdots \}$ ，  
分别取 $p = - 2 , - 1 , 0 , 1 , 2$ 可得 $P = \{ \cdots , - 1 3 , - 7 , - 1 , 5 , 1 1 , \cdots \}$ ，  
观察发现 $M _ { ☉ }$ ， $N$ 的元素相同， $P$ 中的元素 $M$ ， $N$ 中都有，且 $M , \ N$ 中还有 $P$ 中没有的元素，所以 $P \subsetneq M = N$ ：解法2：三个集合中的元素格式都很清晰，也可考虑直接由此分析它们的包含关系，  
在集合 $M = \left\{ x \mid x = 3 m - 1 , m \in \mathbf { Z } \right\}$ 中， $x = 3 m - 1 = 3 ( m - 1 ) + 2 \textcircled { 1 }$   
记 $n = m - 1$ ，则式 $\textcircled{1}$ 即为 $x = 3 n + 2$ ，因为 $m \in \mathbf { Z }$ ，所以 $n \in \mathbf { Z }$ ，从而 $M = \left\{ x \mid x = 3 n + 2 , n \in \mathbf { Z } \right\} = N$ ;  
此时结合选项已可知选B，下面我们也把 $P$ 与 $M$ 的关系做个分析，  
对任意的 $x \in P$ ，设 $x = 6 p - 1 ( p \in \mathbf { Z } )$ ，记 $2 p = m$ ，则 $x = 3 \times 2 p - 1 = 3 m - 1$ 且 $m \in \mathbf { Z }$ ，所以 $x \in M$ ，  
这说明 $P$ 中的任意一个元素都在 $M$ 中，所以 $P \subseteq M$ $\textcircled{2}$ ，  
要说明 $P \subsetneq M$ ，还需在 $M$ 中找到 $P$ 没有的元素．由上面的 $2 p = m$ 可知 $m$ 为偶数，所以 $m$ 为偶数时， $3 m - 1$ 在 $P$ 中，那 $m$ 为奇数时， $3 m - 1$ 很可能就不在 $P$ 中，  
当 $m = 1$ 时， $x = 3 m - 1 = 2$ ，所以 $2 \in M$ ，多 $6 p - 1 = 2$ 可得 $p = \frac { 1 } { 2 }$ ， $p$ 不是整数，所以 $2 \notin P$ ，结合 $\textcircled{2}$ 得劵 $P \subsetneq M$ 答案：B

【反思】判断元素有规律的集合间的包含关系，通过部分罗列来观察是直观的解法，而若要严格论证，则常考虑将包含关系转化为元素是否属于集合的关系：想说明 $A \subseteq B$ ，只需说明 $A$ 中任意一个元素都属于 $B$ ；想说明$A$ 不包含于 $B$ ，只需说明 $A$ 中存在一个元素不属于 $B$

【变式】集合 $X = \{ x \mid x = 2 n + 1 , n \in \mathbf { Z } \}$ ， $Y = \{ y | y = 4 k \pm 1 , k \in \mathbf { Z } \}$ ，试证明： $X = Y$

证明：(要证两个集合相等，常证它们互相包含．所给集合的元素结构特征都很清晰，考虑设元素来分析)  
对任意的 $x \in X$ ，设 $x = 2 n + 1$ ，（接下来需要证明 $x \in Y$ ，观察发现需要把 $2 n$ 转化为 4k．我们知道若对 $n$ 分奇偶  
讨论，则可设 $n = 2 k - 1$ 或 $n = 2 k$ ，由此就能将 $2 n$ 向 $4 k$ 转化，故分奇偶讨论)  
当 $n$ 为奇数时，设 $n = 2 k - 1 ( k \in \mathbf { Z } )$ ，则 $x = 2 ( 2 k - 1 ) + 1 = 4 k - 1 \in Y$ $\textcircled{1}$ ，  
当 $n$ 为偶数时，设 $n = 2 k ( k \in \mathbf { Z } )$ ，则 $x = 2 \times 2 k + 1 = 4 k + 1 \in Y$ $\textcircled{2}$ ，  
综合 $\textcircled{1} \textcircled{2}$ 可知对任意的 $x \in X$ ，都有 $x \in Y$ ，所以集合 $X$ 中的元素都在集合 $Y$ 中，故 $X \subseteq Y$ $\textcircled{3}$ ：  
（再证 $Y \subseteq X$ ，处理方法类似，可设出 $Y$ 中的元素，再论证它也在 $X$ 中）  
对任意的 $y \in Y$ ，设 $y = 4 k + 1$ 或 $y = 4 k - 1$ ，其中 $k \in \mathbf { Z }$ ，  
当 $y = 4 k + 1$ 时，设 $n = 2 k$ ，则 $y = 2 n + 1$ ，且 ${ \boldsymbol { n } } \in \mathbf { Z }$ ，所以 $y \in X$ $\textcircled{4}$ ，  
当 $y = 4 k - 1$ 时， $y = 2 ( 2 k - 1 ) + 1$ ，设 $n = 2 k - 1$ ，则 $y = 2 n + 1$ ，且 ${ \boldsymbol { n } } \in \mathbf { Z }$ ，所以 $y \in X$ $\textcircled{5}$ ，  
综合 $\textcircled{4} \textcircled{5}$ 可知对任意的 $y \in Y$ ，都有 $y \in X$ ，所以集合 $Y$ 中的元素都在集合 $X$ 中，故 $Y \subseteq X$ $\textcircled{6}$ ：  
由 $\textcircled{3}$ 可知 $X = Y$ ：

【反思】 $A = B$ 等价于 $A \subseteq B$ 且 $B \subseteq A$ ，我们常通过论证两个集合相互包含来证明两个集合相等.

# 强化训练

# A 组 夯实基础

1．（2024·重庆长寿期末）

下列命题中，正确的个数有（）

${ \textcircled{1} } A \subseteq A$ ； $\textcircled { 2 } \lbrace 0 \rbrace \in \lbrace 0 , 1 , 2 \rbrace$ ； $\textcircled{3}$ 著名的运动健儿能构成集合； $\textcircled{4} \left\{ 0 \right\} = \textcircled { \times }$ ； $\textcircled{5} \varnothing \subsetneq A$ ； ⑥{0,1,2} η{2,1,0}.

A.1 B.2 C.3 D.5

2．（2024·内江期末） 已知集合 $M = \left\{ x \in \mathbf { N } | 2 x - 3 < 2 \right\}$ ，则 $M$ 的非空子集的个数是 ·

# 一数·高中数学一本通

3．(2024·山东日照模拟）

已知集合 $A$ 满足 $\{ 0 , 1 \} \subseteq A \subsetneq \{ 0 , 1 , 2 , 3 \}$ ，则集合 $A$ 的个数为（）

A.1 B.2 C.3 D.4

# B组 强化能力

4．（2024·河南安阳模拟）（多选）

已知集合 $A = \{ 0 , \emptyset \}$ ，则下列关系正确的是（）

A. $0 \in { \cal A }$ B. ${ \mathcal { O } } \in A$ （204 C. $\emptyset \subsetneq A$ D. $0 \subsetneq A$

5．(2024·山东模拟）

设 $A = \{ 1 , 4 , 2 x \}$ ， $B = \{ 1 , x ^ { 2 } \}$ ，若 $B \subseteq A$ ，则 $x =$ （）

A.0 B.0或2 C.0或 $^ { - 2 }$ D.2或 $^ { - 2 }$

6．（2024·云南昆明模拟）

若集合 $A = \{ x \in \mathbf { Z } | m < x < 4 \}$ 有15个真子集，则实数 $m$ 的取值范围为（）

A. $\{ m | - 1 \leq m < 0 \} \ : \mathrm { ~ B } . \quad \{ m | - 1 < m \leq 0 \}$ C. $\{ m \mid - 1 < m < 0 \}$ D. $\{ m \vert - 1 \leq m \leq 0 \}$ （204

7．(2024·全国模拟）

已知集合 $M = \left\{ x \in \mathbf { R } \mid a x ^ { 2 } + 2 x - 3 = 0 \right\}$ 至多有1个真子集，则 $a$ 的取值范围是

8．(2024·甘肃武威模拟)

已知集合 $A = \{ x | - 7 \leq 2 x - 3 \leq 3 \}$ ， $B = \left\{ x \vert 3 m - 2 < x < m + 1 \right\}$ ，若 $B \subseteq A$ ，则实数 $m$ 的取值范围是（ ）

$\left\{ m \bigg | m \geq \frac { 3 } { 2 } \right\} \qquad \mathrm { B . } \quad \left\{ m \bigg | m > \frac { 3 } { 2 } \right\}$ C. $\{ m \vert m \ge 0 \}$ D. $\{ m \vert m > 0 \}$

9．(2024·上海浦东新区模拟）

已知集合 $A = \{ x \mid x \geq 1$ 或 $x < - 1 \}$ ， $B = \{ x | 2 a < x \leq a + 1 \}$ ，若 $B \subseteq A$ ，则 $a$ 的取值范围是

10．(2024·广东深圳模拟）

$M = \left\{ x { \Biggl | } x = m + { \frac { 1 } { 6 } } , m \in \mathbf { Z } \right\} , N =$ $\left\{ x { \bigg | } x = { \frac { n } { 2 } } - { \frac { 1 } { 3 } } , n \in \mathbf { Z } \right\} , P = \left\{ x { \bigg | } x = { \frac { p } { 2 } } + { \frac { 1 } { 6 } } , p \in \mathbf { Z } \right\} ,$

则 $M , ~ N , ~ P$ 的关系为（）

A. $M = N \subsetneq P$ B. $N = P \subsetneq M$   
C. $M \subsetneq N \subsetneq P$ D. $M \subsetneq N = P$

# C组拓展提升

11．（2024·全国竞赛）

设非空集合 $A \subseteq \{ 1 , 2 , \cdots , 9 \}$ ，且对任意的 $a \in A$ ，都有 $1 0 - a \in A$ ，则这样的 $A$ 的个数为

12．(2024·四川南充模拟）已知集合 $A = \{ x | - 4 \leq x \leq 4 \}$ ， $B = \{ x \vert m + 1 \leq x \leq$ $2 m + 2 , m$ 为常数 $\}$ ：

（1）若 $B \subsetneq A$ ，求实数 $m$ 的取值范围;  
（2）是否存在实数 $m$ ，使 $A \subsetneq B$ ？若存在，求实数 $m$ 的取值范围；若不存在，说明理由.

13．(2024·安徽安庆模拟）

已知 $A = \{ x \mid 0 < a x + 1 \leq 5 \}$ ， $B = \left\{ x \bigg | { - } \frac { 1 } { 2 } < x \leq 2 \right\} .$

（1）若 $A \subseteq B$ ，求实数 $a$ 的取值范围;  
（2）是否存在实数 $a$ ，使得 $A = B ^ { \prime }$ ？若存在，求出 $a$ 的值；若不存在，说明理由.

14．（2024·吉林四平模拟）已知集合 $P = \{ x \in \mathbf { R } \mid x ^ { 2 } - 3 x + b = 0 \}$ ，$\mathcal { Q } = \left\{ x \in \mathbf { R } \mid ( x + 1 ) ( x ^ { 2 } + 3 x - 4 ) = 0 \right\} .$

（1）若 $b = 4$ ，存在集合 $M$ 使得 $P$ 为 $M$ 的真子集且 $M$ 为 $\boldsymbol { Q }$ 的真子集，求这样的集合M;  
（2）若集合 $P$ 是集合 $Q$ 的一个子集，求 $b$ 的取值范围.