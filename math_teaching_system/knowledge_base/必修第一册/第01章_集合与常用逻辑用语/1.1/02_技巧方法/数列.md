---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 20
source_file: L3-1-数列.md
title: L3-1-数列
type: problem_type
---

# 数列

一本一讲，共26讲（建议5个月学习）

6个知识点+8个题型+21个视频

每周一讲（建议学习时间90分钟）

![](images/62f08191e9a591578046e04d31a895dedad2117faba1e929b2f6d4e6256d7e58.jpg)

# 视频内容研发团队

学而思优秀老师

学而思优秀老师和一线高级教师联合创作本书试题，并精心录制讲解视频学而思图书APP扫码即可观看

![](images/17690e860d5830657efd85b3f49fb3e5e1d1d8c7c376dc9e4354b03abf36fa35.jpg)

# 傅博宇 老师

毕业于北京大学元培学院  
网校和北大数院高考数学研究联合课题组成员；  
网校高中创新产品部负责人；  
荣获学而思网校“桃李满天下奖”“出类拔萃奖”等；腾讯网中国好老师；  
青少年教育导师认证；  
科学家长观体系的创立者

![](images/84d8605235fabe092afe2dd9c16141de6101d8f0b4f6f5a3dac2036290cf9cb5.jpg)

# 王侃老师

毕业于北京大学数学系  
学而思网校高中数学教研奠基人；  
学而思网校高中数学S级教师；  
荣获学而思网校“突出贡献奖” “桃李天下奖”等；擅长总结题型特点，提炼思想方法；  
擅长分层教学，因材施教

![](images/f23b9fc2e983400534fd045d08b82b6d6ad632843624197dc45d708a422250d7.jpg)

# 付恒岩 老师

毕业于大连理工大学  
网校高中部理科主讲岗后培训师；  
2020年荣获学而思网校最具魅力奖；  
2019年、2020年荣获学而思网校诲人不倦奖；2020年荣获学而思网校高考优秀评卷人；  
2021年担任新浪教育高考数学直播解析特邀嘉宾；“停课不停学”公益课高中数学主讲老师

![](images/940c12569c9153e0e719c82510d2a7af176a43dfc347d9f284564aa6bd94ae66.jpg)

# 武洪姣 老师

14年线上线下教学经验；  
学而思网校高中理科教研负责人；  
学而思高中数学特级教师;  
在教学的过程中擅长归纳题型，方法和技巧;  
在高中数学模块中最擅长讲解圆锥曲线和导数；  
无论你是从小数学不好，还是数学一直拔尖，都可以在武老师的课堂上收获很多

# 高中数学 思维突破 3

# 第1讲 预习篇

# 数 列

一本一讲，共26讲（建议5个月学习）

6个知识点 $^ { + 8 }$ 个题型 $+ 2 1$ 个视频

每周一讲（建议学习时间90分钟）

![](images/c7765a36e865588d97434ae34f9ae34fcc51f445d165d1073118968257c34f49.jpg)

# 图书在版编目（CIP）数据

学而思秘籍．高中数学思维突破3级/学而思教研中心编写组编著．--北京：现代教育出版社，2022.3ISBN 978-7-5106-8659-7

I. $\textcircled{1}$ 学…Ⅱ. $\textcircled{1}$ 学…Ⅲ. $\textcircled{1}$ 中学数学课－高中－教学参考资料IV. $\textcircled{1}$ G634

中国版本图书馆CIP数据核字（2022）第036148号

编著：学而思教研中心编写组  
出品人：陈琦  
选题策划：王春霞  
责任编辑：魏星顾林  
装帧设计：学而思教研中心设计组·于磊王琳  
出版发行：现代教育出版社  
地 址：北京市东城区鼓楼外大街26号荣宝大厦三层  
邮 编：100120  
电 话：010-64251036（编辑部）010-64256130（发行部）  
印开印字版印 刷：北京世纪恒宇印刷有限公司本： $8 8 9 ~ \mathrm { m m } \times 1 1 9 4 ~ \mathrm { m m }$ 1/16张：34数：694千字次：2022年3月第1版次：2022年3月第1次印刷  
书定 号：ISBN 978-7-5106-8659-7价：239.00（含视频课程）

# Planning Committee

# 学而思图书策划委员会

主 编：学而思教研中心编写组  
副主编：汪玲玲李奎廷  
执行主编：张卡特 乔　巍　郭忠秀  
编 者：武洪姣付恒岩　傅博宇　成文波牛术强 王侃徐强郑赢景肖龙　谭茗心 　刘　坤　董宇喆沈思含宗倩

# 知识点睛

梳理归纳知识，筑牢学科根基提取关键信息，标注重要结论

# 精讲精练

剖析典型例题，提升解题能力总结方法技巧，升华解题思维

# APP扫码观看本模块 讲解视频

全程跟老师，高效学知识！

由学而思资深老师对本模块知识、例题和练习进行系统讲解，并归纳对应考点的解题技巧，总结解题方法，让学生能够举一反三，通过一道题，学会一类题，从而提升学生的解题能力，实现数学思维突破。

![](images/fed8e894df723ba19dc2588bc017d7bcddae1850e21372aee0801b29d14fe53d.jpg)

知识与方法 例题与练习 全程跟老师 高效学知识

# 序言

# 66

# 亲爱的同学：

很开心能见到你，本书将带领你进入全新的领域。在过去的学习中，无论你是卓越还是略有失意，此刻将是新的征途的开始。你是否已准备好谱写新的辉煌？

在这里，你会知道许多著名数学家的趣事，你也会揭开数学神秘的面纱，了解数学之美。

数学家高斯说：“数学是一切科学的皇后。”数学是通往星辰大海的密钥，是国防科技的护盾，是我们脚下这片土地的未来。数学家华罗庚说过：“宇宙之大，粒子之微，火箭之速，化工之巧，地球之变，生物之谜，日用之繁，无处不用数学。”

# 对于本书的学习，给你一些建议：

第一，提前预习。“凡事预则立，不预则废。”提前预习，学习才会更有效。  
第二，APP扫码观看视频。积极思考，认真做笔记，总结解题方法、技巧。  
第三，及时复习。“温故而知新，可以为师矣。”不忘温习，方得始终。  
第四，及时完成测试。今朝有题今朝做，明朝又有明朝事。  
第五，整理错题，学会总结。聪明人知错就改，糊涂人有错就瞒。  
最后，青春的帆已扬起，只待你乘风破浪，勇往直前。

![](images/7f62c0db9c6df7f22192e0e496e4c1888dee3272804d035228312dc0899544ef.jpg)

# 预习篇

第1讲数列 提升篇第2讲等差数列 第11讲等差数列进阶第3讲等比数列 第12讲等比数列进阶第4讲数列求和 第13讲数列求和的方法汇总第5讲椭圆初步 第14讲数列求通项第6讲双曲线初步 第15讲不等式第 $7$ 讲抛物线初步 第16讲数列不等式综合第8讲直线与圆锥曲线的位置关系 第17讲椭圆的基本量与几何性质第 $^ { 9 }$ 讲直线与圆锥曲线的初步应用 第18讲双曲线的基本量与几何性质第10讲空间向量 第19讲抛物线的基本量与几何性质第20讲弦长与面积问题第21讲向量与比例问题第22讲斜率与角度问题  
模块1 数列的基本概念 2  
模块2 数列的表示方法 7 第23讲对称与中垂线问题  
模块3 数列的前 $n$ 项和 13 第24讲单动点问题  
模块4 数列的单调性 15第25讲空间向量与立体几何第26讲立体几何中的动点问题参考答案

# 数列

# 直击课堂

<html><body><table><tr><td>知识模块</td><td>知识点</td><td>对应例题</td><td>星标统计</td></tr><tr><td rowspan="2">数列的基本概念</td><td>数列的概念</td><td>例1,例2</td><td rowspan="9">★2道题 ★★5道题 ★★★8道题 ★★★★2道题</td></tr><tr><td>数列的分类</td><td>例3</td></tr><tr><td rowspan="2">数列的表示方法</td><td>通项公式</td><td>例4,例5</td></tr><tr><td>递推公式</td><td>例6</td></tr><tr><td>数列的前n项和</td><td>数列的前n项和</td><td>例7</td></tr><tr><td>数列的单调性</td><td>数列的单调性</td><td>例8</td></tr></table></body></html>

# 学习目标

$\textcircled{9}$ 了解数列的概念，能根据数列的前几项的规律写出其后的某一项.

$\textcircled{2}$ 体会数列是一种特殊的函数，能根据数列的前几项的规律写出数列的通项公式，能根据递推公式求出前几项.

$\textcircled{6}$ 掌握判断数列单调性的两种方法，会利用数列的周期性求数列中的某一项.

# 模块1数列的基本概念

# APP扫码观看本模块讲解视频

1知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# $\textcircled{8}$ 数列的概念

搖照一定次序排列的一列数称为数列.数列中的每一个数都叫做这个数列的项，各项依次叫做这个数列的第1项(或首项)，第2项,…,第 $n$ 项,…;所以数列的一般形式可以写成： $a _ { \scriptscriptstyle 1 }$ ，$a _ { 2 } , \cdots , a _ { n }$ ,…,简记为 $\left\{ a _ { n } \right\}$ ：

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例1★

下列叙述中正确的是(

A．数列1,3,5,7和数列3,1,5,7是相同的数列B.数列1,3,5,7,9,的第五项是确定的C.数列1,3,5,7可表示为{1,3,5,7}D.同一个数在数列中不可能重复出现

# 学习笔记

# 例2

# （1）★

数列1,3,6,10,x,21,28，中，按照给出的数之间的规律，可知 $x$ 的值是

A. 12 B.15 C.17 D.18

# 学习笔记

# （2）★

观察下列数列的特点 $\cdot 4 , 3 , 7 , 1 0 , 1 7 , 2 7 , x , \cdots$ ,则其中 $_ x$ 是（

A.33 B.34   
C.44 D.53

# 学习笔记

# 变式1★★★

观察数列:1,2,3,3,2,1,2,3,4,4,3,2,3,4,5,5,4,3,4,5,6,6,5,4,, 则数列的2019项是（）

A. 335 B. 336 C.337 D. 339

# 学习笔记

# 知识点睛

# ②数列的分类

(1)按照数列的项数的多少可分为：有穷数列与无穷数列.项数有限的数列叫有穷数列，项数无限的数列叫无穷数列.(2)按照数列的每一项随序号变化的情况可分为：递增数列、递减数列、常数列、摆动数列.从第2项起,每一项都大于它的前一项的数列叫做递增数列;从第2项起，每一项都小于它的前一项的数列叫做递减数列；各项相等的数列叫做常数列;从第2项起,有些项大于它的前一项,有些项小于它的前一项的数列叫做摆动数列.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例3★

以下几个数列中，哪个是递增的无穷数列？（ ）

A. 1,1,2,3,5,8,13,21,… B. 1,2,3,1,2,3,1,2,3,… C $\frac { 1 } { 1 0 } , \frac { 1 } { 9 } , \frac { 1 } { 8 } , \frac { 1 } { 7 } , \frac { 1 } { 6 } , \frac { 1 } { 5 } , \frac { 1 } { 4 } , \frac { 1 } { 3 } , \frac { 1 } { 2 } , 1$ D.-1,-2，- 1 _1 1 3， 4 5

# 学习笔记

# 模块2数列的表示方法

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# $\textcircled{9}$ 数列与函数的关系

数列可以看成是以正整数集 $\mathbf { N } ^ { * }$ （或它的有限子集 $\{ 1 , 2 , 3 , \cdots$ ，$n \}$ )为定义域的函数 $a _ { n } = f ( n )$ ,当自变量从小到大依次取值时对应的一列函数值.

反过来,对于函数 $\scriptstyle y = f ( x )$ ，如果 $f ( i ) ( i = 1 , 2 , 3 , \cdots )$ 有意义，那么我们可以得到一个数列 $f ( 1 ) , f ( 2 ) , f ( 3 ) , \cdots$

# 思考探究

已知数列 $\left\{ a _ { n } \right\} : 2 , 4 , 8 , 1 6 , 3 2 , \cdots .$ 请写出 $a _ { n }$ 与 $n$ 之间的关系式，$a _ { n }$ 与 $n$ 是什么函数关系？

# 2 数列的表示方法

(1)列表法：与函数一样,数列也可以用列表的方法表示.

如：全体正偶数按从小到大的顺序构成的数列2,4,6,8,用列表法可表示为

<html><body><table><tr><td>n</td><td>1</td><td>2</td><td>3</td><td></td><td>k</td><td>·</td></tr><tr><td>an</td><td>2</td><td>4</td><td>6</td><td>：</td><td>2k</td><td></td></tr></table></body></html>

(2)图象法：数列可以看作特殊的函数.所以，可以以序号为横坐标,相应的项为纵坐标,描点作图来表示这个数列.例如全体正偶数组

成的数列2,4,6,8,用图象法表示如图所示：

![](images/0caa60d2c2a47dbd4d65d4cdfe047b439dfdf3c2cf615307db41d296e35aafdb.jpg)

(3）通项公式法：数列 $\left\{ a _ { n } \right\}$ 的第 $n$ 项 $a _ { n }$ 也叫做数列的通项.如果数列 $\left\{ a _ { n } \right\}$ 的第 $n$ 项 ${ \boldsymbol { a } } _ { n }$ 与 $n$ 之间的关系可以用一个函数解析式 $a _ { n } = f ( n )$ 来表示，这个公式就叫做这个数列的通项公式.如：数列2,4,8,16,32,用通项公式可表示为： $a _ { n } = 2 ^ { n } , n \in \mathbf { N } ^ { * }$

# 思考探究

数列的通项公式唯一吗？

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例4★★★

观察下面数列的前几项,写出它的一个通项公式.

（1）-2，4，-.8 35，7'9，   
(2)1, 315 '4'2'16''   
(3)0,1,0,1,0,1,….   
(4）-1,1 11 2'4'8'16'

# 学习笔记

# 例5★★★★

已知数列 $\left\{ a _ { n } \right\}$ 的通项公式 $a _ { n } = \frac { ( \mathbf { \varepsilon } - 1 ) ^ { n } \mathbf { \varepsilon } \cdot \mathbf { \varepsilon } n } { ( n + 2 ) ( n + 4 ) } .$

(1)写出它的第5项.  
(2）判断 $\frac { 1 } { 1 5 }$ 是不是该数列中的项,如果是,写出它是第几项.

# 学习笔记

# 知识点睛

# $\textcircled{8}$ 数列的递推公式

递推公式也是表示数列的一种重要形式.如果已知数列 $\left\{ a _ { n } \right\}$ 的第1项(或前几项)，且从第2 项起的任意一项与它的前一项 $a _ { n - 1 }$ (或前几项)间的关系可以用一个公式来表示,那么这个公式就叫做数列的递推公式.

如:数列2,4,8,16,32,用递推公式可这样表示： $a _ { 1 } = 2 , a _ { n + 1 } = 2 a _ { n } , n \in \mathbf { N } ^ { * } .$

# 思考探究

斐波那契数列（兔子数列):1,1,2,3,5,8,13,21，的递推公式是什么？

# 注意

完整的递推公式包含初始条件(首项或前几项)和递推关系式.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例6

# （1）★★

已知数列 $\left\{ a _ { n } \right\}$ 的第1项是 $1 , a _ { n } = \frac { a _ { n - 1 } } { 1 + a _ { n - 1 } } ( n \geq 2 )$ ,则 $a _ { 3 }$ 的值为(

A. $\frac 1 4$ B. $\frac 1 3$ C. $\frac 1 2$ D.1

# 学习笔记

# （2）★★

若数列 $\left\{ a _ { n } \right\}$ 满足 $a _ { 1 } = 1 , a _ { n + 1 } = n a _ { n } + 1$ ,则第5项 $a _ { 5 } = \left( \begin{array} { l l l } { \mathrm { ~ } } & { } & { } \end{array} \right)$

A.5 B.65 C.89 D.206

# 学习笔记

# （3）★★

在数列 $\left\{ a _ { n } \right\}$ 中， $a _ { 1 } = - 2 , a _ { n + 1 } = 3 a _ { n } + n$ ，则 $a _ { 4 } = ( \mathrm { ~ \small ~ \alpha ~ } )$

A.-13 B. $- 3 6$ （204   
C.-14 D.-41

# 学习笔记

# 变式2★★★

在数列 $\left\{ a _ { n } \right\}$ 中,已知 $a _ { 1 } = - \frac { 1 } { 4 } , a _ { n } = 1 - \frac { 1 } { a _ { n - 1 } } ( n \geqslant 2 )$ ，则 $a _ { 2 0 1 9 }$ 的值为(

A.2018 B.-1 C. $\frac { 4 } { 5 }$ D.6

# 学习笔记

# 模块3数列的前n项和

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# 数列的前n项和

一般地,我们把数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项的和 $a _ { 1 } + a _ { 2 } + \cdots + a _ { n }$ 称为数列 $\{  a _ { n } \}$ 的前 $n$ 项和,记作$S _ { n }$ ，即 ${ { S } _ { n } } = { { a } _ { 1 } } + { { a } _ { 2 } } + \cdots + { { a } _ { n } } ( n \in \bf { N } ^ { \ast } )$ .根据以上定义，我们得到了新数列 $\{ S _ { n } \}$

# 思考探究

已知数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和 $S _ { n }$ ，怎么求 $\left\{ a _ { n } \right\}$ 的通项公式 $a _ { n }$ ？

# 注意

用数列 $\left\{ a _ { n } \right\}$ 的前 $\boldsymbol { n }$ 项和 $S _ { n }$ 求通项公式时，对 $n = 1$ 的情况要单独讨论。

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例7

# （1）★★★

已知数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和 $S _ { n } = n ^ { 2 } - n + 1$ ，则 $a _ { 1 } = \_ { \_ } { a _ { 6 } } =$ an

# 学习笔记

# （2）★★★

已知数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和 =n1,则n=

# 学习笔记

# 变式3★★★

数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和为 $S _ { n }$ ，若 $S _ { n } = 3 n ^ { 2 } - 2 n - 1$ ，则 $a _ { n } = \left( \begin{array} { l l l } { \begin{array} { r l } \end{array} } & { \begin{array} { r l } \end{array} } \end{array} \right)$

A. 6n-5 $\mathrm { B } . \left\{ { \begin{array} { l } { 0 , n = 1 } \\ { { \it 6 n - 5 , n \geq 2 } } \end{array} } \right.$   
C.3n-2 $\operatorname { D } . \ \left\{ { \begin{array} { l } { 0 , n = 1 } \\ { 3 n - 2 , n \geq 2 } \end{array} } \right.$

# 学习笔记

# 模块4数列的单调性

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# 数列单调性的判断

(1)函数特性法(函数方法):数列是一个特殊的函数,因此判断函数单调性的方法同样适用于数列.我们可以先判断相应函数的单调性,进而得到数列的单调性.

(2)邻项比较法(数列方法)：比较 $a _ { n }$ 与 $a _ { n + 1 }$ 的大小.

对于任意的 $\ b { n } \in \mathbf { N } ^ { * }$ ，若 $a _ { n + 1 } > a _ { n }$ 恒成立,则 $\left\{ a _ { n } \right\}$ 单调递增;若 $a _ { n + 1 } < a _ { n }$ 恒成立,则 $\left\{ a _ { n } \right\}$ 单调递减.

# 思考探究

函数有单调性、周期性和奇偶性,数列是特殊的函数,那么数列是否也有上述性质?

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例8★★★

判断下列数列的单调性，并求最大或最小项.

$( 1 ) a _ { n } = { \frac { n } { n + 2 } } .$ $( 2 ) a _ { n } = n \cdot ( { \frac { 1 } { 3 } } ) ^ { n } .$

# 学习笔记

# 变式4

# （1）★★★

已知 $a _ { n } = { \frac { 1 } { n + 2 } } - { \frac { 1 } { n + 4 } }$ ，则数列 $\left\{ a _ { n } \right\}$ 为递 数列(填“增"或“减”).

# 学习笔记

# （2）★★★★

已知 $a _ { n } = 3 ^ { n } - 2 ^ { n }$ ,则数列 $\left\{ a _ { n } \right\}$ 为递 数列(填“增”或"减”).

# 学习笔记

# 学习总结

![](images/160307b4c763b248eed4c8fc56d1eb0f13f67b227403eb6e3fc22a0ac5f61aac.jpg)

# 提升篇你会遇见

在数列 $\left\{ a _ { n } \right\}$ 中 $a _ { 1 } = 3 , a _ { n } = \frac 1 3 a _ { n - 1 } - 2 ( n \geqslant 2 )$ 则 ${ a _ { n } } = .$

【点石成金】预习篇我们学习了简单的递推公式，可以由递推公式来计算数列中的某一项，提升篇将学习由递推公式来计算数列的通项公式，就需要构造新的数列来解题，期待我们提升篇的学习吧！

# 学而思秘籍系列图书数学

# 思维培养

# 思维提升

# 思维突破

![](images/40705a7aa397b0206081ca2e576de88c3c8f57af7aeffaee3671153646d36c33.jpg)

学而思 8秘籍 CDR有理数数轴基初中数学思维提升-5-7.101++

学而思 8秘籍 集合的护95高中数学思维突破++ .

# 小学秘籍系列

学而思积淀近20年教研经验，培养受益一生的能力。

# 初中秘籍系列

全面覆盖初中基础知识和重难点，帮助学生夯实基础，拓展认知。

# 高中秘籍系列

全面覆盖高中基础知识和重难点，帮助学生提升能力，突破思维。

# 学而思秘籍系列图书|语文

# 提升素养

能力训练

![](images/ca5894a092309b84bf9a707f156f7b75aedb251ad060bb97d55c49aedf1b15af.jpg)

# 小学秘籍系列

5大模块+2条主线，能力与素养双向提升。

![](images/4bd445e6a43d71c034e5f2cef644bddb862b3ee3743fcc01234cd57773b8d473.jpg)

# 初中秘籍系列

融合课改四大核心素养，培养爱阅读、 善写作、勤思考、会学习的学生。

# 创新体系|真题研习

![](images/97a64fb685d42fbd3d72fffe16bf0516bc8ac9e1357dab666b79da3abf9e1b45.jpg)

# 思维创新大通关数学

攻克数学思维难题，通向理想中学。

# 大家一起来“升级”

# 参与方式

您在使用本书时，如有任何疑问或对图书有任何建议，请扫码进行反馈，并查看反馈采纳结果。

![](images/a293a7205c67615a128217d6539093d95fba5c743bf47e14e62a8123f9fb5e94.jpg)

# 奖励

您的反馈一经采纳，我们将会送出总价值35元的图书抵扣券（相同内容的反馈，依据反馈时间，奖励前三位）。请扫码关注公众号，并在对话框中发送反馈时填写的手机号，领取抵扣券。

# 合理规划学习时间

先自己定一个目标，即制定半年学习规划。

2 再将目标细化到每一周，每周学习一本（平均5个考点）。

3 配套课堂巩固的练习， 让学习更有效！

![](images/279cae308b6c9573ce3b867056af6fe7a53986076e9e885f140b2cc9880a651b.jpg)

![](images/3af689b97c035f82b40b95f191d324663caf271bc41559256ac4b6c3f823a3df.jpg)  
·共6级·每级17-26讲