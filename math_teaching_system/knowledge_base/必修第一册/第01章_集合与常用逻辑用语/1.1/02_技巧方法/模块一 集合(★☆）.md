---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 28
source_file: 第1节 集合：方法册+练习册.md
title: 第1节 集合：方法册+练习册
type: skill
---

# 模块一 集合(★☆）

# 内容提要

在全国高考中，本节主要考查集合的概念、关系、运算等，下面梳理一些常考知识点.

1．集合中元素的性质：确定性，互异性，无序性.

2．集合间的基本关系

<html><body><table><tr><td>关系</td><td>自然语言</td><td>符号语言</td><td>Venn图</td></tr><tr><td>子集</td><td>集合A中所有元素都在集合B中</td><td>AnB</td><td>A B 或 A(B)</td></tr><tr><td>真子集</td><td>集合A是集合B的子集，且集 合B 中至少有一个元素不在集合A中</td><td>A B</td><td>A B</td></tr><tr><td>集合相等</td><td>集合A，B中的元素相同 或集合A，B互为子集</td><td>A=B</td><td>A(B)</td></tr></table></body></html>

3．子集个数：含有 $n$ 个元素的集合的子集有 $2 ^ { n }$ 个，非空子集有 $2 ^ { n } - 1$ 个，真子集有 $2 ^ { n } - 1$ 个， 非空真子集有 $2 ^ { n } - 2 ( n \geq 1 )$ 个.

4．集合的基本运算

<html><body><table><tr><td>运算</td><td>自然语言</td><td>符号语言</td><td>Venn图</td></tr><tr><td>并集</td><td>集有集属</td><td>AUB={x|x∈A或x∈B}</td><td>A B</td></tr><tr><td>交集</td><td>由属于集合A且属于集 合B 的所有元素组成的集合</td><td>A∩B={x|x∈A且x∈B}</td><td>A B</td></tr><tr><td>补集</td><td>合全有元素组属于的集合</td><td>CuA={x|x∈U且xε A}</td><td>U A</td></tr></table></body></html>

# 类型Ⅰ：集合的概念、集合中元素的性质

【例1】设集合 $A = \{ 2 , a ^ { 2 } - a + 2 , 1 - a \}$ ，若 $4 \in A$ ，则 $a$ 的值为 ·

解析： $4 \in A$ 意味着 $A$ 中有元素4，那谁是4？ $a ^ { 2 } - a + 2$ 和 $1 - a$ 都有可能，可先由此分别求出 $a ,$ （204因为 $4 \in A$ ，所以 $a ^ { 2 } - a + 2 = 4$ 或 $1 - a = 4$ ，解得： $a = 2$ 或 $^ { - 1 }$ 或 $^ { - 3 }$   
上述三个解都可取吗？不一定，还需代回去检验集合 $A$ 是否满足元素互异，  
当 $a = 2$ 时， $A = \{ 2 , 4 , - 1 \}$ ，满足题意；当 $a = - 1$ 时， $1 - a = 2$ ，不满足元素互异，舍去；  
当 $a = - 3$ 时， $A = \{ 2 , 1 4 , 4 \}$ ，满足题意；综上所述， $^ { a }$ 的值为2或 $- 3$ ：  
答案：2或 $^ { - 3 }$

【反思】如果集合中元素含参，我们在求出参数后，请务必检验是否满足集合中元素的互异性，这类问题在后面还会看到.

# 类型ⅡI：根据集合相等求参

【例2】已知集合 $A = \{ a , b , 1 \}$ ， $B = \{ - 1 , 2 , a ^ { 2 } \}$ ，若 $A = B$ ，则 $a ^ { b } = \mathrm { ~ ( ~  ~ { ~ \mu ~ } ~ ) ~ }$

A.1 B. $\frac { 1 } { 2 }$ C.-1 D.1或} 2

解析：集合 $B$ 中只有1个待定元素 $a ^ { 2 }$ ，故先考虑它是集合 $A$ 中的谁，观察发现只能是1,  
因为 $1 \in A$ ，且 $A = B$ ，所以 $1 \in B$ ，故 $a ^ { 2 } = 1$ ，解得： $a = \pm 1$ ，  
求出两个值，还需检验是否满足元素互异，当 $a = 1$ 时，集合 $A$ 中有相同元素，舍去，所以 $a = - 1$ ，  
此时 $A = \{ - 1 , b , 1 \}$ ， $B = \{ - 1 , 2 , 1 \}$ ，对比可得 $b = 2$ ，所以 $a ^ { b } = ( - 1 ) ^ { 2 } = 1$ ：

答案：A

【变式】已知集合 $A = \left\{ x \in \mathbf { R } \mid x ^ { 2 } + a x + 1 = 0 \right\}$ ， $B = \{ x \in \mathbf { R } \mid x ^ { 2 } + 2 x - a + 3 = 0 \}$ ，若 $A = B$ ，则实数 $a$ 的取值范围是 ·

解析： $A , \ B$ 都是一元二次方程的解集， $A = B$ 意味着两个方程同解，先考虑它们都无解的情况，若 $A = B = \emptyset$ ，则 $\left\{ \begin{array} { l l } { \Delta _ { 1 } = a ^ { 2 } - 4 < 0 } \\ { \Delta _ { 2 } = 2 ^ { 2 } - 4 ( - a + 3 ) < 0 } \end{array} \right.$ 解得： $- 2 < a < 2$ ;  
再考虑 $A$ ， $B$ 不是空集的情形，此时两个一元二次方程应同解，直接算出解显然偏麻烦，所以考虑由韦达定理建立方程求 $^ { a , }$ 若 $A = B \neq \emptyset$ ，设方程 $x ^ { 2 } + a x + 1 = 0$ 和 $x ^ { 2 } + 2 x - a + 3 = 0$ 的解分别为 $x _ { 1 }$ ， $x _ { 2 }$ ，  
则首先应有 $\left\{ \begin{array} { l l } { \Delta _ { 1 } = a ^ { 2 } - 4 \geq 0 } \\ { \Delta _ { 2 } = 2 ^ { 2 } - 4 ( - a + 3 ) \geq 0 } \end{array} \right.$ 解得： $a \ge 2$ ，其次，由韦达定理, $\left\{ \begin{array} { l l } { x _ { 1 } + x _ { 2 } = - a = - 2 } \\ { x _ { 1 } x _ { 2 } = 1 = - a + 3 } \end{array} \right.$   
解得： $a = 2$ ，满足 $a \ge 2$ ；综上所述，实数 $a$ 的取值范围是(-2,2].  
答案：(-2,2]

【反思】在分析含参方程的解集时，一定要考虑无解的情况，此时对应集合为空集，且空集是可能满足题意的.

类型IⅢI：根据集合间的包含关系求参

【例3】若集合 $A = \{ 1 , 2 , 3 , m \}$ ， $B = \{ 2 , 3 , m ^ { 2 } \}$ ，若 $B \subseteq A$ ，则实数 $m$ 的值为解析：集合 $B$ 中2，3这两个元素 $A$ 中已经有了，故只需考虑元素 $m ^ { 2 }$ 是 $A$ 中的谁即可，因为 $B \subseteq A$ 且 $m ^ { 2 } \in B$ ，所以 $m ^ { 2 } \in A$ ，故 $m ^ { 2 } = 1$ 或 $m ^ { 2 } = m$ ，解得： $m = - 1$ ，1或0,别忘了检验是否满足集合中元素互异，经检验，当 $m = 1$ 时，集合 $A$ 中有相同元素，舍去；当 $m = - 1$ 或0时，集合 $A$ ， $B$ 均满足元素互异；所以实数 $m$ 的值为 $^ { - 1 }$ 或0.

答案： $^ { - 1 }$ 或0

【变式】设 $A = \{ x \mid x < - 1$ 或 $x \geq 3 \}$ ， $B = \{ x \vert a x + 1 \le 0 \}$ ，若 $B \subseteq A$ ，则实数 $a$ 的取值范围是

解析：先解 $B$ 中的不等式 $a x + 1 \leq 0$ ，再通过数轴分析 $A$ 和 $B$ 的包含关系会比较直观，解此不等式可能要i端同除以α，需先讨论 $a$ 的正负，  
当 $a = 0$ 时，不等式 $a x + 1 \leq 0$ 无解，所以 $B = \emptyset$ ，满足 $B \subseteq A$ ;  
当 $a > 0$ 时，由 $a x + 1 \leq 0$ 可得 $x \leq - { \frac { 1 } { a } }$ 所以 $B = ( - \infty , - \frac { 1 } { a } ]$   
要分析怎样能使 $B \subseteq A$ ，可画数轴来看，注意单独考虑端点能否重合，  
$B \subseteq A$ 的情形如图1，应有 $- \frac { 1 } { a } < - 1$ ，解得： $0 < a < 1$ ;  
当 $a < 0$ 时，由 $a x + 1 \leq 0$ 得 $x \geq - \frac { 1 } { a } { \Rightarrow B } = [ - \frac { 1 } { a } , + \infty )$ ， $B \subseteq A$ 的情形如图2，应有 $- { \frac { 1 } { a } } \geq 3$ ，故 $- \frac { 1 } { 3 } \leq a < 0$ ；综上所述，实数 $^ { a }$ 的取值范围是 $[ - \frac { 1 } { 3 } , 1 )$ ：

答案： $[ - \frac { 1 } { 3 } , 1 )$

![](images/889725d9dfe9a44b4253c302c253b9d782c4b19578c0e629c8d93eb603a5903c.jpg)  
图1

![](images/93d559b0d8c8db0ad0b61ae85214724db77ca4b75c85bb13cc23302f6f712c6d.jpg)  
图2

【总结】分析列举法表示的集合间的包含关系，对比两个集合中的元素即可；而对于连续取值的集合间的包含关系，常画数轴分析，需重点关注端点能否重合；另外，当子集含参时，一定注意讨论子集为空集的情况.

类型IV：子集个数

【例4】若集合 $A = \{ 1 , 2 , 3 \}$ ，集合 $B = \{ ( x , y ) | x \in A , y \in A , \left| x - y \right| \in A \}$ ，则 $B$ 的子集个数为解析：求子集个数，需先求集合中元素的个数，观察发现 $x , \ y$ 各自都只有3种取值，可列表来看，由上表可知集合 $B$ 的元素有(1,2)，(1,3)，(2,1)，(2,3)，(3,1)，(3,2)，共6个，故 $B$ 的子集有 $2 ^ { 6 } = 6 4$ 个.答案：64

<html><body><table><tr><td>X</td><td>111222333</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>y</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td>123123123</td></tr><tr><td>Ix-y012101210</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

【总结】求集合的子集个数，需先分析集合中有几个元素，再代结论即可（见内容提要第3点）

类型V：集合的基本运算

【例5】（2022·浙江卷）设集合 $A = \{ 1 , 2 \}$ ， $B = \{ 2 , 4 , 6 \}$ ，则 $A \cup B =$ （）

A.{2}B.{1,2)C. {2,4,6} D.{1,2,4,6}解析：求并集，把两集合的元素合在一起即可，需注意相同元素只计一次，由题意， $\begin{array} { r } { A \bigcup B = \{ 1 , 2 , 4 , 6 \} . } \end{array}$ 答案：D

【变式】已知集合 $A = \{ x | a - 2 < x < a + 3 \}$ ， $B = \{ x \vert x ^ { 2 } - 5 x + 4 > 0 \}$ ，若 $A \bigcup B = \mathbf { R }$ ，则 $a$ 的取值范围是（）

A. $( - \infty , 1 )$ B.(1,3）C．[1,3]D. $[ 3 , + \infty )$

解析： $x ^ { 2 } - 5 x + 4 > 0 \Leftrightarrow ( x - 1 ) ( x - 4 ) > 0 \Leftrightarrow x < 1$ 或 $x > 4$ ，所以 $B = ( - \infty , 1 ) \bigcup ( 4 , + \infty )$ ，分析连续取值集合的集合关系，常画数轴来看，其中端点能否重合需重点关注，如图，要使 $A \bigcup B = \mathbf { R }$ ， $a - 2$ 与1， $a + 3$ 与4都不能重合，否则并集就取不到端点处的元素，所以应有 $\left\{ { \begin{array} { l } { a - 2 < 1 } \\ { a + 3 > 4 } \end{array} } \right.$ （ 解得： $1 < a < 3$ ，故 $a$ 的取值范围是(1,3).

答案：B

. →x α-2 1 4 a+3

【反思】对于连续取值的集合的包含关系或交并补运算，一般优先考虑画数轴分析，需要尤其注意端点取值哦.

【例6】（2022·新高考Ⅰ卷）若集合 $M = \{ x \vert \sqrt { x } < 4 \}$ ， $N = \{ x \vert 3 x \ge 1 \}$ ，则 $M \cap N =$ （）

$$
\{ x \mid 0 \leq x < 2 \} \mathrm { ~ \textit ~ { ~ B . ~ } ~ } \{ x \mid \frac { 1 } { 3 } \leq x < 2 \} \mathrm { ~ \textit ~ { ~ C . ~ } ~ } \{ x \mid 3 \leq x < 1 6 \} \mathrm { ~ \textit ~ { ~ D . ~ } ~ } \{ x \mid \frac { 1 } { 3 } \leq x < 1 6 \}
$$

解析： $\sqrt { x } < 4 \Longleftrightarrow 0 \leq x < 1 6$ ，所以 $M = \{ x | 0 \leq x < 1 6 \}$ ， $3 x \geq 1 \Leftrightarrow x \geq { \frac { 1 } { 3 } }$ 所以 $N = \{ x \mid x \geq { \frac { 1 } { 3 } } \}$ 求连续取值的集合的交集，可画数轴来看，如图， $M \cap N = \{ x | \frac { 1 } { 3 } \leq x < 1 6 \}$ ：

答案：D

![](images/f4f866e6a602f951c9dea946cf796f92e578650fdc6e2f70333dc7473d108a65.jpg)

【变式】已知集合 $A = \left\{ x \mid x ^ { 2 } - x - 2 \leq 0 \right\}$ ， $B = \{ x | 2 a < x < a ^ { 2 } \}$ ，若 $A \cap B = \emptyset$ ，则实数 $a$ 的取值范围 是

解析： $x ^ { 2 } - x - 2 \leq 0 \Longleftrightarrow ( x + 1 ) ( x - 2 ) \leq 0 \Longleftrightarrow - 1 \leq x \leq 2$ ，所以 $A = [ - 1 , 2 ]$ ，  
怎样能使 $A \cap B = \emptyset \ ?$ 观察发现集合 $B$ 的左右端点含参，大小不定，可能为空集，先讨论这种情况,当 $B = \emptyset$ 时， $2 a \geq a ^ { 2 }$ ，解得： $0 \leq a \leq 2$ ，此时满足 $A \cap B = \emptyset$ ;  
再考虑 $B$ 非空的情形，此时可画数轴来看，当 $B \neq \emptyset$ 时，首先应有 $2 a < a ^ { 2 }$ ，解得： $a < 0$ 或 $a > 2$ $\textcircled{1}$ 其次，图形应为图1或图2所示的情形，若为图1，则 $a ^ { 2 } \leq - 1$ ，无解；  
若为图2，则 $2 a \ge 2$ ，解得： $a \geq 1$ ，结合 $\textcircled{1}$ 可得 $a > 2$ ;  
综上所述，实数 $a$ 的取值范围是 $[ 0 , + \infty )$ ：

答案： $[ 0 , + \infty )$

![](images/8f30b41f9096238cf430e0a239054a28477b2a32a2e9eab083db3630d35a3f4e.jpg)  
图1

![](images/eb1d397cd0d59cce177eb305fbe000b322ffdd1aa55cc1b3694487c65f511644.jpg)  
图2

【反思】根据交集为空集求参，一定要考虑含参集合本身为空集的情况.

【例7】(2023·全国甲卷）设集合 $A = \{ x \mid x = 3 k + 1 , k \in \mathbf { Z } \}$ ， $B = \left\{ x \vert x = 3 k + 2 , k \in \mathbf { Z } \right\}$ ， $U$ 为整数集,则 $\complement _ { U } ( A \cup B ) =$ （）

A. $\{ x \vert x = 3 k , k \in \mathbf { Z } \}$ B $ \emptyset : \left\{ { x = 3 k - 1 , k \in \mathbf { Z } } \right\} \qquad \mathbb { C } . \quad \left\{ { x \mid x = 3 k - 2 , k \in \mathbf { Z } } \right\} \qquad \mathrm { ~ D } . \quad \emptyset \qquad $ 解法1： $\ A \bigcup B = \{ x \vert x = 3 k + 1$ 或 $x = 3 k + 2 , k \in \mathbf { Z } \}$ ，所以它包括除以3后余数为1或2的整数，整数除以3余数只能是0，1，2，所以取补集后剩余的即为余数为0的，故 $\complement _ { \scriptscriptstyle U } ( A \cup B ) = \{ x \mid x = 3 k , k \in \mathbf { Z } \}$ 解法2：若想象不出 $\complement _ { U } ( A \cup B )$ 中的元素有哪些，也可通过罗列部分元素来观察规律，小题可用此技巧，由题意，集合 $A = \{ \cdots , - 5 , - 2 , 1 , 4 , 7 , \cdots \}$ ，集合 $B = \{ \cdots , - 4 , - 1 , 2 , 5 , 8 , \cdots \}$ ，所以 $A \bigcup B = \{ \cdots , - 5 , - 4 , - 2 , - 1 , 1 , 2 , 4 , 5 , 7 , 8 , \cdots \}$ ，故 $\complement _ { \scriptscriptstyle U } ( A \bigcup B ) = \{ \cdots , - 3 , 0 , 3 , 6 , \cdots \} = \left\{ x \mid x = 3 k , k \in \mathbf { Z } \right\} .$

答案：A

【变式】若全集 $U = \mathbf { R }$ ，集合 $A = \{ x \mid x ^ { 2 } - ( 2 m + 1 ) x + m ^ { 2 } + m < 0 \}$ ，集合 $B = \{ x | - 2 < x < 1 \}$ ，若集合 $( \complement _ { U } A ) \cap B$ 中有且仅有1个整数，则实数 $m$ 的取值范围是

解法1: $x ^ { 2 } - ( 2 m + 1 ) x + m ^ { 2 } + m < 0 \Leftrightarrow ( x - m ) ( x - m - 1 ) < 0 \Leftrightarrow m < x < m + 1 ,$   
所以 $A = \left( m , m + 1 \right)$ ，故 $\complement _ { \scriptscriptstyle U } A = ( - \infty , m ] \cup [ m + 1 , + \infty )$ ，  
再分析 $\complement _ { U } A$ 与 $B$ 的交集，可画数轴来看，尤其需要关注端点能否重合，  
要使 $( \complement _ { U } A ) \cap B$ 中有且仅有1个整数，则可能的情形如图1和图2所示，  
若为图1， $m$ 不能与 $^ { - 1 }$ ，0重合，否则交集中有2个整数，所以 $- 1 < m < 0 < m + 1 < 1$ ，故 $- 1 < m < 0$ ;

若为图2， $m$ 不能与 $^ { - 2 }$ ， $^ { - 1 }$ 重合，否则交集中有2个整数，所以 $- 2 < m < - 1 < m + 1 < 0$ ，故 $- 2 < m < - 1$ ：综上所述，实数 $m$ 的取值范围是 $( - 2 , - 1 ) \cup ( - 1 , 0 )$

![](images/38e72a6d603086e2cc10b6e1e587711696286d3d679c8ba677257b4c6f57e8f7.jpg)  
图1

![](images/927361b29791e5ba82288281da61f2b991564db820affd7da243ab386cccf881.jpg)  
图2

解法2：求出 $\complement _ { \scriptscriptstyle U } A = ( - \infty , m ] \cup [ m + 1 , + \infty )$ 后，由于 $B$ 中的整数只有-1，0，故也可讨论它们谁在 $\complement _ { { \scriptscriptstyle I } { \scriptscriptstyle I } } A$ 中，若 $\left\{ { \begin{array} { l } { - 1 \in \complement _ { \mathit { v } } A } \\ { 0 \not \in \complement _ { \mathit { v } } A } \end{array} } \right.$ 则 $\begin{array} { l } { \left\{ - 1 \leq m \underline { { \partial } } \right\} - 1 \geq m + 1 } \\ { m < 0 < m + 1 } \end{array}$ 解得： $- 1 < m < 0$ ;  
若 $\left\{ \begin{array} { l l } { - 1 \notin \complement _ { \mathit { v } } A } \\ { 0 \in \complement _ { \mathit { v } } A } \end{array} \right.$ 则 $\begin{array} { l } { \left\{ { m < - 1 < m + 1 \atop 0 \leq m \not \equiv \not \backslash 0 \geq m + 1 } \right\} } \end{array}$ 解得： $- 2 < m < - 1$ ;  
综上所述，实数 $m$ 的取值范围是 $( - 2 , - 1 ) \cup ( - 1 , 0 )$ ：  
答案： $( - 2 , - 1 ) \cup ( - 1 , 0 )$

【总结】从上面两道题可以看出，分析列举法表示的集合的并集、交集、补集，直接从元素来看即可；而对于连续取值的集合，则常画数轴来分析，且往往需要重点关注端点.

类型VI：Venn 图

【例8】设集合 $U = \{ 1 , 2 , 3 , 4 , 5 \}$ ，若 $( \complement _ { \scriptscriptstyle U } A ) \cup ( \complement _ { \scriptscriptstyle U } B ) = \{ 1 , 2 , 3 \}$ ，则 $A \cap B =$ （）

A.{4,5}B. {3,4,5}C. {1,2,5}D. {5}解析：直接由 $( \complement _ { \scriptscriptstyle U } A ) \cup ( \complement _ { \scriptscriptstyle U } B ) = \{ 1 , 2 , 3 \}$ 分析 $A , \ B$ 的情况比较抽象，这时常考虑画Venn 图来观察，如图， $( \complement _ { U } A ) \cup ( \complement _ { U } B )$ 表示在全集 $U$ 中，把 $A \cap B$ 的部分去掉，余下的部分，即 $( \complement _ { \scriptscriptstyle U } A ) \bigcup ( \complement _ { \scriptscriptstyle U } B ) = \complement _ { \scriptscriptstyle U } ( A \bigcap B )$ ，因为 $( \complement _ { \scriptscriptstyle U } A ) \cup ( \complement _ { \scriptscriptstyle U } B ) = \{ 1 , 2 , 3 \}$ ，所以 $\complement _ { \scriptscriptstyle U } ( A \cap B ) = \{ 1 , 2 , 3 \}$ ，故 $A \cap B = \{ 4 , 5 \}$ ：

答案：A

【反思】当集合间的运算较抽象时，不妨画 Venn 图来分析，往往可使问题明朗化.

【例9】学校举办运动会时，高一1班共有28名同学参加比赛，有15人参加游泳比赛，8人参加田径比赛，14人参加球类比赛，同时参加游泳和田径比赛的有3人，同时参加游泳和球类比赛的有3人，没有人同时参加三项比赛，那么只参加游泳一项比赛的有_ 人；同时参加田径和球类比赛的有_ 人.

解析：题干的信息较复杂，不妨画出图形，并根据题意把各部分的人数标注出来，  
如图，图中的15，14，8是参加三项运动各自的总人数，所以只参加游泳一项比赛的有 $1 5 - 3 - 3 = 9$ 人；再求同时参加田径和球类比赛的人数，观察图形发现只有这部分不知道了，于是只需将其设为 $x _ { i }$ ，就能表示出图中各小块的人数，由共28人参赛来建立方程求 $x$   
由图可知只参加田径比赛的有 $8 - 3 - x = 5 - x$ 人，  
只参加球类比赛的有 $1 4 - 3 - x = 1 1 - x$ 人，  
所以 $9 + ( 5 - x ) + ( 1 1 - x ) + 3 + 3 + x = 2 8$ ，解得： $x = 3$ ，  
故同时参加田径和球类比赛的有3人.  
答案：9，3

![](images/0b1a84ebf0d71abb387db52cdf32d5f8bfe947b32af204698c4c728e4fc080e1.jpg)

【反思】涉及多个集合关系的文字题目中，常常通过画图将文字信息直观地呈现出来，使问题明朗化.

# 类型VII：集合综合题

【例10】在数学漫长的发展过程中，数学家发现在数学中存在着神秘的“黑洞”现象．数学黑洞：无论怎样设值，最终都将得到固定的一个值，再也跳不出去，就像宇宙中的黑洞一样．目前已发现的数字黑洞有“123 黑洞”“卡普雷卡尔黑洞”、“自恋性数字黑洞”等．定义：若一个 $n$ 位正整数的所有数位上数字的 $n$ 次方和等于这个数本身，则称这个数是自恋数．已知所有一位正整数的自恋数组成集合 $A$ ，集合 $B = \left\{ x \vert - 3 < x < 4 , x \in \mathbf { Z } \right\}$ ，则 $A \cap B$ 的子集个数为（）

A.3 B.4 C.7 D.8

解析：已知B，求 $A \cap B$ 只差 $A$ ，而求 $A$ 需从题干的信息弄懂什么是一位正整数自恋数，一位正整数有1，2，3，…，9，它们都满足所有数位上数字的1次方和等于这个数本身，所以它们都是自恋数，故 $A = \{ 1 , 2 , 3 , 4 , 5 , 6 , 7 , 8 , 9 \}$ ，又 $B = \{ x | - 3 < x < 4 , x \in \mathbf { Z } \} = \{ - 2 , - 1 , 0 , 1 , 2 , 3 \}$ ，所以 $A \cap B = \{ 1 , 2 , 3 \}$ ，故 $A \cap B$ 的子集个数为 $2 ^ { 3 } = 8$ 些数学核心方法

答案：D

【例11】设 $x _ { 1 } , x _ { 2 } , x _ { 3 } , x _ { 4 } \in \mathbf { R }$ ，集合 $A = \{ x _ { i } x _ { j } | 1 \leq i < j \leq 4$ 且 $i , j \in \mathbf { N } \}$ ，若 ${ \cal A } = \{ - 1 8 , - 3 , - 1 , - \frac { 1 } { 6 } , \frac { 1 } { 2 } , 6 \}$ 则xx2xx4=

解析：直接观察集合 $\{ x _ { i } x _ { j } | 1 \leq i < j \leq 4$ 且 $i , j \in  { \mathbf { N } } _ { j }$ 中的元素显然较困难，所以我们不妨按 $i , \ j$ 的可能取值将其所有元素都罗列出来，再分析怎么算 $x _ { 1 } x _ { 2 } x _ { 3 } x _ { 4 }$ ，由题意，集合 $A$ 中的元素 $x _ { i } x _ { j }$ 所有可能的情况如下表：

<html><body><table><tr><td>i</td><td>1</td><td>1</td><td>1</td><td>2</td><td>2</td><td>3</td></tr><tr><td>j</td><td>2</td><td>3</td><td>4</td><td>3</td><td>4</td><td>4</td></tr><tr><td></td><td></td><td>xxxxxxxxxxxx4</td><td></td><td></td><td></td><td>X3X4</td></tr></table></body></html>

由所给条件知集合 $A$ 中有6个元素，所以上表中所列的 $x _ { i } x _ { j }$ 的6种情况即为该6个元素，分析上表中的 $x _ { i } x _ { j }$ 与所给6个元素是如何对应的显然偏麻烦，但考虑到要求的是 $x _ { 1 } x _ { 2 } x _ { 3 } x _ { 4 }$ ，观察发现只要将上表 $x _ { i } x _ { j }$ 全部相乘，就可以出现目标式，因为集合 ${ \cal A } = \{ - 1 8 , - 3 , - 1 , - \frac { 1 } { 6 } , \frac { 1 } { 2 } , 6 \}$ ，所以 $\mathfrak { c } _ { 1 } x _ { 3 } ) \cdot ( x _ { 1 } x _ { 4 } ) \cdot ( x _ { 2 } x _ { 3 } ) \cdot ( x _ { 2 } x _ { 4 } ) \cdot ( x _ { 3 } x _ { 4 } ) = ( x _ { 1 } x _ { 2 } x _ { 3 } x _ { 4 } ) ^ { 3 } = - 1 8 \times ( - 3 ) \times ( - 1 ) \times ( - \frac { 1 } { 6 } ) \times \frac { 1 } { 2 } \times 6 = 2 7$ 故 $x _ { 1 } x _ { 2 } x _ { 3 } x _ { 4 } = 3$ ：

答案：3

【例12】设 $S$ 为实数集 $\mathbf { R }$ 上的非空子集，若对任意的 $x , y \in S$ ，都有 $x + y \ , x - y \ , x y \in S$ ，则称$S$ 为封闭集．下面是关于封闭集的4个判断：

$\textcircled{1}$ 自然数集 $\mathbf { N }$ 为封闭集;   
$\textcircled{2}$ 整数集 $\mathbf { Z }$ 为封闭集;   
$\textcircled{3}$ 若 $S$ 为封闭集，则一定有 $0 \in S$ ;   
$\textcircled{4}$ 封闭集一定是无限集.

其中正确的判断是（）

A. $\textcircled{2} \textcircled{3}$ B. $\textcircled{2} \textcircled{4}$ C. $\textcircled{3} \textcircled{4}$ D. $\textcircled{1} \textcircled{2}$

解析： $\textcircled{1}$ 项，自然数之差不一定是自然数，所以结合封闭集的定义知 $\mathbf { N }$ 不是封闭集，下面举个反例，因为 $2 \in \mathbf { N }$ ， $3 \in \mathbf { N }$ ，但 $2 - 3 = - 1 \notin \mathbf { N }$ ，所以自然数集 $\mathbf { N }$ 不是封闭集，故 $\textcircled{1}$ 项错误;  
$\textcircled{2}$ 项，对任意的 $x , y \in \mathbf { Z }$ ， $x + y$ ， $x - y$ ， $x y$ 都为整数，所以整数集 $\mathbf { Z }$ 是封闭集，故 $\textcircled{2}$ 项正确；$\textcircled{3}$ 项，要判断 $0 \in S$ 是否正确，可尝试结合已知条件构造出0，观察发现由 $x - y \in S$ 容易产生0,若 $S$ 为封闭集，取 $x = y \in S$ ，则 $x - y = 0 \in S$ ，故 $\textcircled{3}$ 项正确;  
$\textcircled{4}$ 项，正面推证结论不易，尝试寻找反例，受 $\textcircled{3}$ 的启发，我们考虑只有0的单元素集合，  
设 $S = \{ 0 \}$ ，则对任意的 $x , y \in S$ ，必有 $x = y = 0$ ，此时， $x + y = x - y = x y = 0 \in S$ ，  
所以 $S$ 为封闭集，但 $S$ 不是无限集，故 $\textcircled{4}$ 项错误.

答案：A

【反思】集合综合题千变万化，各种新定义层出不穷，我们主要通过上述3道题，让大家简单体悟它的分析思路.

# 强化训练

1．（2023·重庆模拟·★）

若集合 $A = \{ x \in \mathbf { Q } \mid ( x - 1 ) ( x ^ { 2 } - 2 ) = 0 \} , \quad B = \{ x \in \mathbf { R } \mid ( x - 1 ) ( x ^ { 2 } - 2 ) = 0 \}$ ，则下列关系正确的是（）

A. $A = B$ （204 B. $A \subseteq B$   
C. $B \subseteq A$ D. $A \cap B = \emptyset$

2．(2022·广州模拟·★）

已知集合 $A = \{ a - 2 , a ^ { 2 } + 4 a , 1 2 \}$ ，且 $- 3 \in A$ ，则 $a$ 的值为（）

A. $^ { - 3 }$ 或-1B. $^ { - 1 }$ C.3D.-3

3．(2022·忻州月考·★★）

已知 $m \in \mathbf { R }$ ， $n \in \mathbf { R }$ ，若集合 $\{ m , \frac { n } { m } , 1 \} = \{ m ^ { 2 } , m + n , 0 \}$ ，则 $m ^ { 2 0 2 3 } + n ^ { 2 0 2 3 } = { \mathrm { ( ) } }$

A.-2 B.-1 C.1 D.2

4．（2022·安徽模拟·★）

已知集合 $A = \{ 1 , 2 , m ^ { 2 } \}$ ， $B = \{ 1 , m \}$ ，若 $A \bigcup B = A$ ，则实数 $m$ 的值为

5．（2023·山西模拟·★）

已知集合 $A = \left\{ x \in \mathbf { Z } \mid x ^ { 2 } - x - 6 < 0 \right\}$ ， $B = \{ y | y = x ^ { 2 } \}$ ，则 $A \cap B$ 的子集有（）

A.2个 B.4个 C.8个 D.16个

6．（2024·重庆模拟·★☆）（多选）

已知全集 $U = \mathbf { Z }$ ，集合 $A = \{ 1 , 2 , 3 \}$ ， $B = \{ a + b \mid a , b \in A \}$ ，则下列结论正确的是（）

A．集合 $B$ 中有6个元素 B. $A \cup B = \{ 1 , 2 , 3 , 4 , 5 , 6 \}$ C. $( \complement _ { U } A ) \bigcap B = \{ 4 , 5 , 6 \}$ D. $A \cap B$ 的真子集个数是3

# 一数·高考数学核心方法

7．（2024·新课标Ⅰ卷·★）

已知集合 $A = \{ x \mid - 5 < x ^ { 3 } < 5 \}$ ， $B = \{ - 3 , - 1 , 0 , 2 , 3 \}$ ，则 $A \cap B =$ （）

A. $\{ - 1 , 0 \}$ B.{2,3} C. $\{ - 3 , - 1 , 0 \}$ D.{-1,0,2}

8．(2021·全国乙卷·★☆）

已知集合 $S = \left\{ s \mid s = 2 n + 1 , n \in \mathbf { Z } \right\}$ ， $T = \left\{ t \mid t = 4 n + 1 , n \in \mathbf { Z } \right\}$ ，则 $S \cap T = \mathbf { \Omega } ( \mathbf { \Omega } )$

A.ØB.S C. $T$ D. Z

9．(2023·福建模拟·★★）

已知全集 $U = \mathbf { R }$ ，集合 $A = \{ x \mid \left| x - 1 \right| \leq 1 \}$ ， $B = \{ x \ | \frac { x - 4 } { x - 1 } > 0 \}$ ，则 $A \cap ( \complement _ { \mathit { w } } B ) = \textrm { ( ) }$

A．[1,2]B.(1,2)C. [0,1)D．(1,2]

10．（2024·全国甲卷·★★）

集合 $A = \{ 1 , 2 , 3 , 4 , 5 , 9 \}$ ， $B = \{ x \mid { \sqrt { x } } \in A \}$ ，则 $\complement _ { _ A } ( A \cap B ) =$ （）

A. {1,4,9} B. {3,4,9} C. {1,2,3} D. {2,3,5}

11．(2023·苏州模拟·★★☆）

已知 $f ( x ) = { \sqrt { x ^ { 2 } - 1 } }$ 的定义域为 $A$ ，集合 $B = \{ x \in \mathbf { R } \mid 1 < a x < 2 \}$ ，若 $B \subseteq A$ ，则实数 $a$ 的取值范围是（)

A.[-2,1]   
B．[-1,1]   
C. $( - \infty , - 2 ] \cup [ 1 , + \infty )$ D. $( - \infty , - 1 ] \cup [ 1 , + \infty )$

12．(2023·扬州期末·★★★）

已知集合 χ4≥}，B=x|x²-(a+1)²x+2a(a²+1)<O若A∩B=，则实数a的取值范围(

A. $( 2 , + \infty )$ B. $\{ 1 \} \bigcup ( 2 , + \infty )$ C. $\{ 1 \} \bigcup [ 2 , + \infty )$ D. $[ 2 , + \infty )$ （204

13．（2024·全国模拟 $\cdot \star$ ）（多选）关于下图说法正确的是（）

A．集合 $A$ 中的元素既是集合 $B$ 中的元素也是集合 $U$ 中的元素  
B．集合 $A$ ， $B$ ， $U$ 中有相同的元素  
C．集合 $U$ 中有元素不在集合 $B$ 中

D．集合 $A$ ， $B$ ， $U$ 中的元素相同

14．(2023·广州一模·★★)

已知集合 $M = \{ x \mid x ( x - 2 ) < 0 \}$ ， $N = \{ x \vert x - 1 < 0 \}$ ，则下列Venn 图中，阴影部分可以表示集合 $\{ x \mid 1 \leq x < 2 \}$ 的是（ ）

![](images/e2e0b18854523d5d467f3ea2a02356a59fcfcfdfe21eb5d23cf69f7741f4f7c8.jpg)

15．（2023·重庆模拟·★★）

# 一数·高考数学核心方法

某班有40名同学参加数学、物理、化学课外研究小组，每名同学至多参加两个小组．已知参加数学、物理、化学小组的人数分别为26、15、13，同时参加数学和化学小组的有6人，同时参加物理和化学小组的有4人，则同时参加数学和物理小组的人数为

16．(2023·济南二模·★★☆)

已知集合 $A = \{ ( x , y ) | y = x \}$ ， $B = \{ ( x , y ) | \left| x \right| + \left| y \right| = 1 \}$ ，则 $A \cap B$ 中元素的个数为（）

A.0 B.1 C. 2 D.3

17．(2022·长沙模拟·★★★）

设 $S$ 是实数集 $\mathbf { R }$ 的一个非空子集，若对任意的 $a , b \in S$ （ ${ \mathit { a } } , { \mathit { b } }$ 可以相等，也可以不相等)， $a + b \in S$ 且 $a - b \in S$ ，则称 $S$ 是“和谐集”．则下列说法中错误的是（）

A．存在一个集合 $S$ ，它既是“和谐集”，又是有限集B．集合 $\left\{ x \mid x = { \sqrt { 2 } } k , k \in \mathbf { Z } \right\}$ 是“和谐集”

C.若 $S _ { \mathrm { { \scriptscriptstyle 1 } } }$ ， $S _ { 2 }$ 都是“和谐集”，则 $S _ { 1 } \cap S _ { 2 } \neq \emptyset$ D．对任意两个不同的“和谐集” $S _ { 1 }$ ， $S _ { 2 }$ ，总有 $S _ { 1 } \cup S _ { 2 } = \mathbf { R }$

18．(2022·南京模拟·★★★☆)

对于集合 $A$ ， $B$ ，我们把集合 $\{ ( a , b ) | a \in A , b \in B \}$ 记作 $A \times B$ ．例如， $A = \{ 1 , 2 \}$ ， $B = \{ 3 , 4 \}$ ， $C = \{ 1 , 3 \}$ ，则$A \times B = \{ ( 1 , 3 ) , ( 1 , 4 ) , ( 2 , 3 ) , ( 2 , 4 ) \} ~ , ~ A \times C = \{ ( 1 , 1 ) , ~ ( 1 , 3 ) , ( 2 , 1 ) , ( 2 , 3 ) \} ~ .$ 现已知 $M = \{ 0 , 1 , 2 , 3 , 4 , 5 , 6 , 7 , 8 , 9 \}$ ，集合 $A , \ B$ 是 $M$ 的子集，且当 $( a , b ) \in A \times B$ 时， $( b , a ) \notin A \times B$ ，则 $A \times B$ 内的元素最多有（）个.

A.20　B.25C.50 D.75