---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 基础
estimated_study_time: 29
source_file: 第1单元 集合与常用逻辑用语（基础篇）（解析版）.md
title: 第1单元 集合与常用逻辑用语（基础篇）（解析版）
type: concept
---

# 第 1 单元 集合与常用逻辑用语（基础篇）

# 基础知识讲解

# 一．子集与真子集

1.真子集是对于子集来说的

真子集定义：如果集合 $A \subseteq B$ ，但存在元素 $x \in B$ ，且元素 $x$ 不属于集合 A，我们称集合$A$ 是集合 B 的真子集也就是说如果集合 $A$ 的所有元素同时都是集合 $B$ 的元素，则称 $A$ 是 $B$ 的子集，若 B 中有一个元素，而 A 中没有，且 $A$ 是 $B$ 的子集，则称 $A$ 是 $B$ 的真子集，

注： $\textcircled{1}$ 空集是所有集合的子集；

$\textcircled{2}$ 所有集合都是其本身的子集；$\textcircled{3}$ 空集是任何非空集合的真子集

# 2、真子集和子集的区别

子集就是一个集合中的全部元素是另一个集合中的元素，有可能与另一个集合相等；真子集就是一个集合中的元素全部是另一个集合中的元素，但不存在相等；注意集合的元素是要用大括号括起来的“{}”，如{1，2}，{a，b，g}；另外，{1，2}的子集有：空集，{1}，{2}，{1，2}．真子集有：空集， $\{ 1 \}$ ，{2}．一般来说，真子集是在所有子集中去掉它本身，所以对于含有 $n$ 个（ $\overset { \cdot } { n }$ 不等于 0）元素的集合而言，它的子集就有 $2 ^ { n }$ 个；真子集就有 $2 ^ { n } - 1$ ．但空集属特殊情况，它只有一个子集，没有真子集．

【技巧点拨】

注意真子集和子集的区别，不可混为一谈， $A \subseteq B$ ，并且 B⊆A 时，有 $A = B$ ，但是A⊂B，并且 B⊂A，是不能同时成立的；子集个数的求法，空集与自身是不可忽视的

# 二．集合的包含关系判断及应用

# 【技巧点拨】

1．按照子集包含元素个数从少到多排列  
2．注意观察两个集合的公共元素，以及各自的特殊元素  
3．可以利用集合的特征性质来判断两个集合之间的关系

4．有时借助数轴，平面直角坐标系，韦恩图等数形结合等方法

# 三．空集的定义、性质及运算

1.空集不是没有；它是内部没有元素的集合，而集合是存在的．这通常是初学者的一个难理解点  
例如： $\{ x | x ^ { 2 } + 1 { = } 0 , x { \in } \mathbf { R } \} { = } 0$ ．虽然有 $x$ 的表达式，但方程中根本就没有这样的实数 $x$ 使得方程成立，所以方程的解集是空集

2、空集是任何集合的子集，是任何非空集合的真子集

# 【技巧点拨】

解答与空集有关的问题，例如集合 $A \cap B { = } B \Leftrightarrow B { \subseteq } A$ ，实际上包含 3 种情况：$\textcircled{1} B = \textcircled { 2 }$ ；$\textcircled{2} B \subset A$ 且 $B { \neq } Q$ ；$\textcircled{3} B = A$ ；往往遗漏 $B$ 是 $\emptyset$ 的情形

# 三．并集及其运算

# 【基础知识】

由所有属于集合 $A$ 或属于集合 B 的元素的组成的集合叫做 $A$ 与 $B$ 的并集，记作 $A \cup B$

# 符号语言：

$A \cup B { = } \{ x | x { \in } A$ 或 $\scriptstyle x \in B \}$

![](images/03ade95ed426d4e85cd0e4838ba47fe2ad4babd8e76c385806e8f10605210c59_1.jpg)

图形语言：

运算形状：

$\textcircled { 1 } A \cup B { = } B \cup A$ ． $\textcircled { 2 } A \cup \varnothing = A$ ． ${ \textcircled { 3 } } A \cup A { = } A$ ． $\textcircled{4}$ A∪B⊇A，A∪B⊇B． $\textcircled{5} A \cup B =$ $B { \Leftrightarrow } A { \subseteq } B$ ． $\textcircled { 6 } A \cup B \mathop { = } \theta$ ，两个集合都是空集． $\textcircled{7}$ A∪（ $\mathbf { \bar { C } _ { U } } \mathbf { A } ) \mathbf { \Psi } = U .$ ． $\textcircled { 8 } \mathsf { C } _ { \mathrm { U } } \mathsf { \Gamma } \left( A \cup B \right) \ =$ （CUA）∩（CUB）

# 【技巧方法】

解答并集问题，需要注意并集中：“或”与“所有”的理解．不能把“或”与“且”混用；注意并集中元素的互异性．不能重复

# 四．交集及其运算

# 【基础知识】

由所有属于集合 $A$ 且属于集合 $B$ 的元素组成的集合叫做 $A$ 与 $B$ 的交集，记作 $A \cap B$   
符号语言：  
$A \cap B = \{ x | x \in A$ ，且 $\scriptstyle x \in B \}$   
$A \cap B$ 实际理解为： $x$ 是 $A$ 且是 $B$ 中的相同的所有元素  
当两个集合没有公共元素时，两个集合的交集是空集，而不能说两个集合没有交集

# 运算形状：

$\textcircled { 1 } A \cap B { = } B \cap A$ ． $\textcircled { 2 } A \cap \emptyset = \emptyset$ ． ${ \textcircled { 3 } } A \cap A = A$ ． $\textcircled{4}$ A∩B⊆A，A∩B⊆B． $( 5 ) A \cap B { = } A \Leftrightarrow A \subseteq B .$ $\textcircled{6} A \cap B = \emptyset$ ，两个集合没有相同元素． $\textcircled{7}$ A∩（ $\mathit { \Omega } ( \mathrm { C u } A ) \ = \emptyset$ ． $\textcircled{8} \textcircled{8}$ （A∩B）＝（∁UA）∪（∁UB）

# 【技巧方法】

解答交集问题，需要注意交集中：“且”与“所有”的理解．不能把“或”与“且”混用；求交集的方法是： $\textcircled{1}$ 有限集找相同； $\textcircled{2}$ 无限集用数轴、韦恩图

# 五．补集及其运算

# 【基础知识】

一般地，如果一个集合含有我们所研究问题中所涉及的所有元素，那么就称这个集合为全集，通常记作 U．（通常把给定的集合作为全集）对于一个集合 A，由全集 $U$ 中不属于集合 A 的所有元素组成的集合称为集合 $A$ 相对于全集$U$ 的补集，简称为集合 A 的补集，记作 $\complement _ { \mathrm { U } } A$ ，即 $\complement _ { \mathrm { U } } A = \{ x | x \in U$ ，且 $x { \notin A } \}$ ．其图形表示如图

![](images/d1d75da073f3a2306123b57d450755949d1b04bd3fa39a37e2927b4230a077bc_1.jpg)

所示的 Venn 图

# 【技巧方法】

常用数轴以及韦恩图帮助分析解答，补集常用于对立事件，否命题，反证法

# 六．全集及其运算

# 【基础知识】

一般地，如果一个集合含有我们所研究问题中所涉及的所有元素，那么就称这个集合为全集，通常记作 U．（通常把给定的集合作为全集）．全集是相对概念，元素个数可以是有限的，也可以是无限的．例如{1，2}；R； $Q$ 等等

# 七．交、并、补集的混合运算

# 【基础知识】

集合交换律 $A \cap B { = } B \cap A$ ， $A \cup B { = } B \cup A$   
集合结合律 $( A \cap B ) \cap C = A \cap ( B \cap C ) , \quad ( A \cup B ) \cup C = A \cup ( B \cup C ) .$   
集合分配律 　A∩（B∪C）＝（A∩B）∪（A∩C），A∪（B∩C）＝（A∪B）∩（A∪C）  
集合的摩根律 $C u ~ \left( A \cap B \right) ~ = C u A \cup C u B , ~ C u ~ \left( A \cup B \right) ~ = C u A \cap C u B .$   
集合吸收律 $A \cup \ ( A \cap B ) \ = A , \ A \cap \ ( A \cup B ) \ = A .$   
集合求补律 $A \cup C u A { = } U ,$ ， $A \cap C u A { = } \Phi$

# 八．Venn 图表达集合的关系及运算

# 【基础知识】

用平面上一条封闭曲线的内部来代表集合，这个图形就叫做 Venn 图（韦恩图）．集合中图形语言具有直观形象的特点，将集合问题图形化，利用Venn图的直观性，可以深刻理解集合的有关概念、运算公式，而且有助于显示集合间的关系运算公式：card（ $\left( A \cup B \right) \ =$ card（A）+card（B）﹣card（A∩B）的推广形式：card（A∪B∪C） $=$ card（A） $^ +$ card（B） $^ +$ card（C）﹣card（A∩B）﹣card（B∩C）﹣card（A∩C）+card（A∩B∩C），或利用 Venn 图解决．公式不易记住，用 Venn 图来解决比较简洁、直观、明了

# 【技巧方法】

在解题时，弄清元素与集合的隶属关系以及集合之间的包含关系，结合题目应很好地使用Venn 图表达集合的关系及运算，利用直观图示帮助我们理解抽象概念．Venn 图解题，就必须能正确理解题目中的集合之间的运算及关系并用图形准确表示出来

# 九．充分条件、必要条件、充要条件

# 【基础知识】

1、判断：当命题“若 $p$ 则 $q ^ { , , }$ 为真时，可表示为 $p { \Rightarrow } q$ ，称 $p$ 为 $q$ 的充分条件， $q$ 是 $p$ 的必要条件．事实上，与“ $\cdot \vec { p } \vec {  } q ^ { \prime }$ 等价的逆否命题是“ $\cdot \lnot q \Rightarrow \lnot p ^ { \ l , * }$ ．它的意义是：若 $q$ 不成立，则 $p$ 一定不成立．这就是说， $q$ 对于 $p$ 是必不可少的，所以说 $q$ 是 $p$ 的必要条件．例如： $p \colon x >$ 2； $q$ ： $x { > } 0$ ．显然 $x \in p$ ，则 $x { \in } q$ ．等价于 $x \notin q$ ，则 $x \notin p$ 一定成立  
2、充要条件：如果既有“ $\cdot \boldsymbol { p } { \Rightarrow } q ^ { , , }$ ，又有“ $\langle q { \Rightarrow } p ^ { \prime \prime }$ ，则称条件 $p$ 是 $q$ 成立的充要条件，或称条件 $q$ 是 $p$ 成立的充要条件，记作“ ${ \bf \nabla } ^ { \epsilon } p { \bf \Leftrightarrow } q ^ { \bf , \curlyeq }$ ． $p$ 与 $q$ 互为充要条件

# 【技巧方法】

判断充要条件的方法是：

$\textcircled{1}$ 若 $p { \Rightarrow } q$ 为真命题且 $q { \Rightarrow } p$ 为假命题，则命题 $p$ 是命题 $q$ 的充分不必要条件；$\textcircled{2}$ 若 $p { \Rightarrow } q$ 为假命题且 $q { \Rightarrow } p$ 为真命题，则命题 $p$ 是命题 $q$ 的必要不充分条件；$\textcircled{3}$ 若 $p { \Rightarrow } q$ 为真命题且 $q { \Rightarrow } p$ 为真命题，则命题 $p$ 是命题 $q$ 的充要条件；$\textcircled{4}$ 若 $p { \Rightarrow } q$ 为假命题且 $q { \Rightarrow } p$ 为假命题，则命题 $p$ 是命题 $q$ 的即不充分也不必要条件$\textcircled{5}$ 判断命题 $p$ 与命题 $q$ 所表示的范围，再根据“谁大谁必要，谁小谁充分”的原则，判断命题 $p$ 与命题 $q$ 的关系

# 十．全称量词和全称命题

# 【基础知识】

<html><body><table><tr><td>命题</td><td>全称命题 xM，p (x)</td><td>特称命题 xM，p (x)</td></tr><tr><td rowspan="5">表述 方法</td><td>①所有的xM，使p（x）成立</td><td>①存在xM，使p(x）成立</td></tr><tr><td>②对一切xM，使p(x）成立</td><td>②至少有一个xM，使p（x）成立</td></tr><tr><td>③对每一个xM，使p(x）成立</td><td>③对有些xM，使p（x）成立</td></tr><tr><td>④任给一个xM，使p（x）成立</td><td>④对某个xM，使p（x）成立</td></tr><tr><td>⑤若xM，则p（x）成立</td><td>⑤有一个xM，使p（x）成立</td></tr></table></body></html>

# 【技巧方法】

要求我们会判断含有一个量词的全称命题和一个量词的特称命题的真假；正确理解含有一个量词的全称命题的否定是特称命题和含有一个量词的特称命题的否定是全称命题，并能利用数学符号加以表示．应熟练掌握全称命题与特称命题的判定方法

# 十一．存在量词和特称命题

# 【基础知识】

<html><body><table><tr><td>命题</td><td>全称命题x∈M，p (x)</td><td>特称命题x∈M，p （xo)</td></tr><tr><td rowspan="5">表述方 法</td><td>①所有的x∈M，使p（x）成立</td><td>①存在x∈M，使p（x）成立</td></tr><tr><td>②对一切x∈M，使p(x）成立</td><td>②至少有一个x∈M，使p（xo）成立</td></tr><tr><td>③对每一个x∈M，使p(x）成立</td><td>③某些x∈M，使p (x）成立</td></tr><tr><td>④对任给一个x∈M，使p（x）成立</td><td>④存在某一个xo∈M，使p（xo）成立</td></tr><tr><td>⑤若x∈M，则p (x）成立</td><td>⑤有一个x∈M，使p（xo）成立</td></tr></table></body></html>

# 【技巧方法】

短语“存在一个”“至少有一个”在逻辑中通常叫做存在量词．符号：∃  
特称命题：含有存在量词的命题．符号：“∃”  
存在量词：对应日常语言中的“存在一个”、“至少有一个”、“有个”、“某个”、  
“有些”、“有的”等词，用符号“∃”表示．

# 习题演练

# 一．选择题（共12小题）

1．设全集 $U = \{ \cdot 3 , \cdot 2 , \cdot 1 , 0 , 1 , 2 , 3 \}$ ，集合 $A = \{ - 1 , 0 , 1 , 2 \} , B = \{ - 3 , 0 , 2 , 3 \}$ ，则（ ）

A． $\{ - 3 , 3 \}$ {0,2} C． {- 3,- 2,- 1,1,3} B． D．

【答案】C【解析】

由题意结合补集的定义可知： $\pmb { \hat { \eta } } B = \{ - 2 , - 1 , 1 \}$ 则 $A \cap \left( \Leftrightarrow B \right) = \left\{ - 1 , 1 \right\} .$ 故选：C.

2．设集合 $A { = } \{ x | x ^ { 2 } { - } 4 { \leq } 0 \}$ ， $B { = } \{ x | 2 x { + } a { \leq } 0 \}$ ，且 $A \cap B { = } \{ x | { - } 2 { \leq } x { \leq } 1 \}$ ，则 $a = \mathrm { ~ \left( ~ \begin{array} { l } { \mathrm { ~ \pi ~ } } \end{array} \right) ~ }$

A．–4 B．–2 C．2 D．4

【答案】B

【解析】  
求解二次不等式 $\chi ^ { 2 } - 4 \leq 0$ 可得： $A = \left\{ x | - 2 \leq x \leq 2 \right\}$ 1  
求解一次不等式 $2 x + a \leq 0$ 可得： $B = \left\{ x \mid x \leq - { \frac { a } { 2 } } \right\} .$   
由于 $A \cap B = \left\{ x | - 2 \leq x \leq 1 \right\}$ ， 故： $\displaystyle { - \frac { a } { 2 } = 1 }$ ， 解得： $a = - 2$ .  
故选：B.

3．设集合 $A = \left\{ - 1 , 1 , 2 , 3 , 5 \right\}$ ， $B = \{ 2 , 3 , 4 \}$ ， $C = \{ x \in R \mid 1 \bullet \llcorner < 3 \}$ ，则 $( A \cap C ) \cup B =$

A．{2} B．{2，3} C．{-1，2，3} D．{1，2，3，4}

【答案】D

【解析】$A \cap C = \{ 1 , 2 \}$   
因为 ，$( A \cap C ) \cup B = \{ 1 , 2 , 3 , 4 \}$   
所以  
故选 D

4．已知集合 $M = \{ - 1 , 0 \}$ ，则满足 $M \cup N { = } \{ { - } 1 , 0 , 1 \}$ 的集合 $N$ 的个数是(　　)

A．2 B．3   
C．4 D．8

【答案】C【解析】

因为由 $\mathbf { M } \cup \mathbf { N } { = } \{ - 1$ ，0，1}，得到集合 $\mathrm { \mathbf { M } } { \subseteq } \mathrm { \mathbf { M } } \cup \mathrm { \mathbf { N } }$ ，且集合 $\mathrm { \mathbf { N } } { \subseteq } \mathrm { \mathbf { M } } \cup \mathrm { \mathbf { N } }$ ，又 $\mathrm { M } = \{ 0 , \tau { - } 1 \}$ ，所以元素 $\cdot$ ，则集合 N 可以为{1}或{0，1}或{-1，1}或{0，-1，1}，共 4 个．故选 C

5.设 $x \in \mathbf { R }$ ，则“ $\boldsymbol { x } ^ { 3 } > 8 ,$ ”是“ ” 的

A．充分而不必要条件 B．必要而不充分条件C．充要条件 D．既不充分也不必要条件

【答案】A

【解析】  
分析：求解三次不等式和绝对值不等式，据此即可确定两条件的充分性和必要性是否成立即可.  
详解：求解不等式 $ { \boldsymbol { X } } ^ { 3 } > 8$ 可得 $\chi > 2$ ，  
求解绝对值不等式 $\left| x \right| > 2$ 可得 或 ，  
$ { \boldsymbol { x } } ^ { 3 } > 8$ $\left| x \right| > 2$   
据此可知： 是 的充分而不必要条件.  
本题选择A选项.

$a \in \mathbf { R }$ ，则“ ”是“ $a ^ { 2 } > a ,$

A．充分不必要条件 B．必要不充分条件C．充要条件 D．既不充分也不必要条件

【答案】A

【解析】  
求解二次不等式 $a ^ { 2 } > a$ 可得： $a > 1 _ {  } a < 0$ ，据此可知： $a > 1 _ { \frac { \Xi } { \mathcal { N } } } a ^ { 2 } > a$ 的充分不必要条件.故选：A.

7．已知集合 $A = \left\{ ( x , y ) { \big | } x ^ { 2 } + y ^ { 2 } \leq 3 , x \in Z , y \in Z \right\}$ ，则 $A$ 中元素的个数为（ ）

A．9 B．8 C．5 D．4

【答案】A【解析】

$\because x ^ { 2 } + y ^ { 2 } \leq 3$   
: x∈Z   
..x =- 1,0,1   
${ \underline { { \underline { { \mathbf { \Pi } } } } } } x = - 1 _ { \sharp \mathcal { \dagger } } , y = - 1 , 0 , 1 _ { ; }$ ${ \bf \Pi } _ { \stackrel { \_ } { \pm } } x = 0 { \bf \Pi } _ { \not \exists \cdot \nmid , } \quad y = - 1 , 0 , 1 { \bf \Pi } _ { ; }$ $\scriptstyle x = 1 \qquad y = - 1 , 0 , 1$ 所以共有 9 个，   
故选：A.

8．下列命题错误的是（ ）

A．命题“若 ，则 ” 的逆否命题为“若 ，则 ”   
B．命题“ $\forall x \in R$ ， $\chi ^ { 2 } - \chi + 2 > 0$ ”的否定是“ $\exists { x } _ { 0 } \in R$ ， $x _ { 0 } ^ { 2 } - x _ { 0 } + 2 < 0$ ”   
C．若“ $p$ 且 $q$ ”为真命题，则 $p$ ， $q$ 均为真命题   
D．“ $x > - 1$ ”是“ $\chi ^ { 2 } + 4 \chi + 3 > 0$ ”的充分不必要条件

【答案】B【解析】

对于A 中，根据逆否命题的概念，可得命题“若 $\scriptstyle x ^ { 2 } - 4 x + 3 = 0$ ， 则 ” 的逆否命题为x≠3 $x ^ { 2 } - 4 x + 3 \neq 0$   
“士 则 ” 所以 A 正确的；若  
对于B 中，根据全称命题与存在性命题的关系，可得命题 $\forall x \in R _ { , } x ^ { 2 } - x + 2 > 0 ,$ ”的否定是 “ $\exists { x } _ { 0 } \in R$ ， ”，所以B不正确；  
对于 C 中，根据复合命题的真假判定方法，若“ $p$ 且 $q$ ”为真命题，则 $p$ ， $q$ 均为真命题，所以C 是正确的；  
对于D中，不等式 $\chi ^ { 2 } + 4 \chi + 3 > 0$ ， 解得 $\chi < - 3 ~ \chi > - 1$ ， 所以 “X>-1, 是 “  
$\chi ^ { 2 } + 4 \chi + 3 > 0$ ”的充分不必要条件，所以D正确.  
综上可得，命题错误为选项B.  
故选：B.

9．已知集合 $A { = } \{ x | x { - } 1 { \geq } 0 \}$ ， $B { = } \{ 0 , ~ 1 , ~ 2 \}$ ，则 $A \cap B =$ （ ）

A．{0} B．{1} C．{1，2} D．{0，1，2}

【答案】C

【解析】  
由题意得 $A { = } \{ x | x { \geq } 1 \}$ ， $\cdot$ ，1，2}，  
$\_$   
故选：C

10．已知集合 $A = \left\{ x \vert x ^ { 2 } - 3 x - 4 < 0 \right\}$ ， $B = \{ x | ( x - m ) [ x - ( m + 2 ) ] > 0 \}$ ，若 $A \cup B { = } \mathbf { R }$ ， 则实数 $m$ 的取值范围是（ ）

A． $( - 1 , + \infty )$ B． C． [-1,2] D．

【答案】C【解析】

集合 $A = \left\{ \left. x \right| x ^ { 2 } - 3 x - 4 < 0 \right\} = \left( - 1 , 4 \right)$ ，   
集合 $\begin{array}{c} B = \{ x | ( x - m ) [ x - ( m + 2 ) ] > 0 \} = ( - \infty , m ) \cup ( m + 2 , + \infty )  \\ { , } \end{array}$ (m>-1   
若 $A \cup B { = } \mathbf { R }$ ，则 $\lfloor m + 2 < 4$ ，解得 $m \in ( - 1 , 2 )$ ，故选 C.

11．已知全集 $U = \{ - 1 , 0 , 1 , 2 , 3 \}$ ，集合 $A = \{ 0 , 1 , 2 \}$ ， $B = \{ - 1 , 0 , 1 \}$ ，则 $\left( \bullet A \right) \cap B = \mathbf { \Phi } _ { ( }$ ）

A． B． C． D．

【答案】A【解析】

$$
C _ { U } A \mathrm { = } \{ - 1 , 3 \} , \mathbb { M } ^ { } ( C _ { U } A ) \cap B = \{ - 1 \}
$$

故选：A

12．设全集为 $\mathrm { R }$ ，集合 $\operatorname { A } = \left\{ x \mid x - 1 > 0 \right\}$ ， $\mathrm { B } = \left\{ x \| x | > 2 \right\}$ ，则集合 $\scriptstyle ( \bigoplus \mathrm { A } ) \cup \mathrm { B } = ( \quad \quad )$

A． {x|x≤1} B $\{ x \vert x < - 2 \frac { x > 1 \} { \not \Xi \not \{ X } }$ {x|1≤x<2} $_ { \mathrm { ~ D ~ . ~ } } \left\{ x \mid x \leq 1 \atop \exists \right\} x > 2 \}$ C

【答案】D

【解析】

$$
\begin{array} { r l } & { \tt { \tt { E } } | { \tt { X } } | = \left\{ \alpha \mid x > 1 \right\} , ~ \tt { B } = \{ x \mid x < - 2 _ { \tt { \overrightarrow { E } } \mid } x > 2 \} , } \\ & { \tt { \tt { \theta } } } \\ & { \tt { \tt { \theta } } . \theta \tt { A } = \{ x \mid x \leq 1 \} _ { \alpha } . : \left( \phi { \tt { A } } \right) \cup \tt { B } = \{ x \mid x \leq 1 _ { \overrightarrow { E } \mid } x > 2 \} . } \end{array}
$$

# 故选 D

# 二．填空题（共6小题）

A={- 1,0,1,6} $B = \{ x \mid x > 0 , x \in \mathbf { R } \}$ AnB=13．已知集合 ， ，则{1,6}【答案】

【解析】AnB={1,6}  
由题知，

14．若命题“ $\exists x \in R$ 使 $x ^ { 2 } + ( a - 1 ) x + 1 < 0$ ”是假命题，则实数 $a$ 的取值范围为_【答案】

【解析】

由题意得若命题 x∈R, $x ^ { 2 } + ( a - 1 ) x + 1 < 0 .$ 是假命题，则命题 $\forall x \in \mathrm { R }$ $x ^ { 2 } + ( a - 1 ) x + 1 \geq 0$ ” 是真命题，，则需 $\Delta \leq 0 \Rightarrow \left( a - 1 \right) ^ { 2 } - 4 \leq 0 \Rightarrow - 1 \leq a \leq 3$ ,故本题正确答案为 ．

15．已知命题 $p : x < - 1$ 或 $x > 3$ ， 命题 $q : x < 3 m + 1$ 或 $x > m + 2$ ， 若 $p$ 是 的充分非必   
要   
条件，则实数 $m$ 的取值范围是

【答案】 $\left[ - \frac { 2 } { 3 } , \frac { 1 } { 2 } \right]$

【解析】

因为 $p$ 是 $q$ 的充分非必要条件，所以 $( - \infty , - 1 ) \cup ( 3 , + \infty ) _ { \underset { \prime \in } {  } } ( - \infty , 3 m + 1 ) \cup ( m + 2 , + \infty ) _ { \underset { \prime \in } {  } }$ 真子集，故 $\left\{ \begin{array} { l } { 3 m + 1 \ge - 1 } \\ { m + 2 \le 3 } \end{array} \right.$ 解得： $- { \frac { 2 } { 3 } } \leq m \leq 1$ ， 又因为 $3 m + 1 \leq m + 2$ ，所以 $m \leq \frac 1 2$ ， 综上可知 $- \frac { 2 } { 3 } \leq m \leq \frac { 1 } { 2 }$ 故填 $\left[ { \tt - \frac { 2 } { 3 } , \frac { 1 } { 2 } } \right] .$

# 

16．设集合 $U = \{ 0 , 1 , 2 , 3 \}$ ，集合 $A = \left\{ x \in U \mid x ^ { 2 } + m x = 0 \right\}$ ，若 $C _ { v } A = \left\{ 1 , 2 \right\}$ ，则实数 m=

【答案】-3【解析】

因为集合 $U = \{ 0 , 1 , 2 , 3 \}$ ， $C _ { v } A = \left\{ 1 , 2 \right\}$ ， $\scriptstyle \cdot A = \{ 0 , 3 \}$ ，故 $\cdot$ .

17．已知命题 “ 不等式 ”为真命题，则 的取值范围为_(-8,4]  
【答案】

【解析】

解：令 $f \left( x \right) = x ^ { 2 } - a x + 4$ ， 则对称轴为 $x = \frac { a } { 2 }$   
要使 $\forall x \in [ 1 , 3 ] ,$ 不等式 $x ^ { 2 } - a x + 4 \geq 0$ 恒成立，即 $\forall x \in [ 1 , 3 ]$ ， $f \left( x \right) = x ^ { 2 } - a x + 4 \geq 0$   
当 $x = \frac { a } { 2 } \leq 1$ 时 解得 ；  
当 $1 < x = \frac { a } { 2 } < 3$ 时 $f \left( { \frac { a } { 2 } } \right) = \left( { \frac { a } { 2 } } \right) ^ { 2 } \cdot a \times { \frac { a } { 2 } } + 4 \geq 0$ 解得 ；  
当 $x = \frac { a } { 2 } \geq 3$ 时 寸 $f \left( 3 \right) = 3 ^ { 2 } - 3 a + 4 \geq 0$ 解得 $a \in \emptyset$ ；$a \in ( - \infty , 4 ]$   
综上可得：(-8,4]  
故答案为：

18．命题 “ $\forall x \in R , ( x - 1 ) ^ { 2 } > 0$ ”的否定是_【答案】 $\exists x _ { \mathrm { o } } \in R , ( x _ { \mathrm { o } } - 1 ) ^ { 2 } \leq 0$

【解析】  
命题 （ $\forall x \in R , ( x - 1 ) ^ { 2 } > 0$ 的否定是 $\exists x _ { \circ } \in R , ( x _ { \circ } - 1 ) ^ { 2 } \ \leq 0 \ ,$   
故答案为： $\exists x _ { \mathrm { o } } \in R , ( x _ { \mathrm { o } } - 1 ) ^ { 2 } \leq 0$

# 三．解析题（共 6 小题）

19．设全集为 $R$ ，集合 $A = \left\{ x { \left| x \leq 3 _ { \ttni \tt G P } x \geq 6 \right. } \right\} B = \left\{ x { \left| - 2 < x < 9 \right. } \right\} .$

（1） $\ l _ { \sharp } A \cup B _ { , } \ ^ { ( \bullet A ) } \cap B _ { ; }$

（2）已知 $C = \left\{ x { \big | } a < x < a + 1 \right\}$ ，若 $C \subseteq B$ ，求实数 $a$ 的取值范围.

$A \cup B = R , ~ \left( \bigoplus A \right) \cap B = \left( 3 , 6 \right) , ~ \left( 2 \right) ~ \left[ - 2 , 8 \right] .$

【解析】

（1）因为全集为 $R$ ，集合 $A = \left\{ x | x \leq 3 \quad x \geq 6 \right\} B = \left\{ x | - 2 < x < 9 \right\}$ ， 所以 $\pmb { \hat { \varrho } } A = ( 3 , 6 )$ ，利用数轴法得 ， ；  
（2）因为 $C = \left\{ x { \big | } a < x < a + 1 { \big | } \ \subseteq B = \left\{ x { \big | } - 2 < x < 9 \right\} \right.$ 所以 $a \ge - 2$ 且 $a + 1 \leq 9$ ，即 $- 2 \leq a \leq 8$ ，所以实数 $a$ 的取值范围为 $\left[ - 2 , 8 \right]$ .

$$
p : x ^ { 2 } - 7 x + 1 0 < 0  , q : x ^ { 2 } - 4 m x + 3 m ^ { 2 } < 0 , \sharp \sharp \ : m > 0 .
$$

若 ，且 为真，求 x 的取值范围；

若 $\neg q$ 是 $\neg p$ 的充分不必要条件，求实数 $m$ 的取值范围．

【答案】 （1）（2） $\frac { 5 } { 3 } \leq m \leq 2$

【解析】

解：由 $x ^ { 2 } - 7 x + 1 0 < 0$ ， 解得 $2 < x < 5$ ， 所以 $p : 2 < x < 5$ ；$x ^ { 2 } - 4 m x + 3 m ^ { 2 } < 0$ 因为 $m > 0$ ， 解得 $m < x < 3 m$ ， 所以 $q : m < x < 3 m$

（1）当 $m = 3$ 时， $q : 3 < x < 9$ ，

又 $p ^ { \prime } ~ q$ 为真， $p$ ， $q$ 都为真， $\therefore { \left\{ \begin{array} { l l } { 2 < x < 5 } \\ { 3 < x < 9 } \end{array} \right. }$   
解得 $3 < x < 5$   
所以 的 取值范围为 (3,5)  
（2） 由 $\neg q$ 是 的充分不必要条件，即 ， ， 表示“推不出”  
其逆否命题为 $p \Rightarrow q , q \Rightarrow p$ ，  
由于 $p ! 2 < x < 5 , q : m < x < 3 m ,$   
所以 $\left\{ \begin{array} { l l } { m { \bullet } 2 } \\ { 3 m { \bullet } 5 } \\ { m > 0 } \\ { m > 0 } \end{array} \right. \quad \underbrace { 5 } _ { \therefore 3 } \underline { { { \sf s } } } \underline { { { \sf s } } } \underline { { { \sf i } } } h \quad 2$   
实数 $m$ 的取值范围为 $\left[ { \frac { 5 } { 3 } } , 2 \right]$

21．己知

（1）若 $p$ 是真命题，求对应 $\chi$ 的取值范围；  
（2）若 $p$ 是 $q$ 的必要不充分条件，求 $a$ 的取值范围.

【答案】 （1） ； （2）

【解析】

（1） 为真命题，即 $\left| 2 x - 5 \right| \leq 3$ ， 解得1≤x≤4  
（2）根据（1）知： ，$p$ q  
是 的必要不充分条件  
当 时， $q : 2 \leq x \leq a$ ， 故满足 $a \le 4$ ， 即 ；

a=2 q:x=2当 时， ， 满足条件；当a<2 时， q:a≤x≤2 故满足 $a \ge 1$ ， 即 .综上所述： $a \in \left[ 1 , 4 \right]$

22．设集合 $\mathtt { A = \{ x \mid a - 1 < x < 2 a , a \in R \} }$ ，不等式 $\mathrm { ~ x ~ } ^ { 2 } - 2 \mathrm { x } - 8 < 0$ 的解集为 B．

当 时，求集合 A，B；

当 时，求实数 a 的取值范围．【答案】（1） $-$ ；（2）a≤2.

【解析】

（1）当 $a = 0$ 时， $A = \left\{ \left. x \right| - 1 < x < 0 \right\}$ （2）若 $A \subseteq B$ ，则有：

$\cdot$ 当 $A = \emptyset$ ，即 $2 a \leq a - 1$ ，即 $a \leq - 1$ 时，符合题意，$\textcircled{2}$ 当 $A \neq \emptyset$ ， 即 $2 a > a - 1$ ， 即 $a > - 1$ 时，有 $\left\{ \begin{array} { l l } { { a - 1 \geq - 2 } } \\ { { } } \\ { { 2 a \leq 4 } } \end{array} \right. \Rightarrow \left\{ \begin{array} { l } { { a \geq - 1 } } \\ { { } } \\ { { a \leq 2 } } \end{array} \right.$

解得：

综合 $\cdot$ 得： $a \le 2$

23．设集合 $M = \left\{ x \left| { ( x + a ) ( x - 1 ) \leq 0 } \right. \right\} ( a > 0 ) , \mathrm { ~ } N = \left\{ x \left| { 4 x ^ { 2 } - 4 x - 3 < 0 } \right. \right\} .$

（Ⅰ）若 $M \cup N = \left\{ x { \Big | } - 2 \leq x < { \frac { 3 } { 2 } } \right\}$ ，求实数 $a$ （Ⅱ）若 $( \ L _ { \ A M } ) \cup N = \mathbf { R }$ ，求实数 $a$ 的取值范围．【答案】（Ⅰ） $a = 2$ ；（Ⅱ） $\left( 0 , { \frac { 1 } { 2 } } \right) .$

【解析】

（Ⅰ） $\because a > 0 , M = \left\{ x \left| { ( x + a ) } ( x - 1 ) \leq 0 \right. \right\} = \left\{ x { \left| { - a \leq x \leq 1 } \right. } \right\} ,$   
$N = \{ x | 4 x ^ { 2 } - 4 x - 3 < 0  \} = \{ x | - { \frac { 1 } { 2 } } < x < { \frac { 3 } { 2 } } \} \qquad M \cup N = \{ x | - 2 \leq x < { \frac { 3 } { 2 } }  \}$ ，所以， $\overline { { \bullet } } a = \overline { { \bullet } } 2$ ，解得 $a = 2$ ；  
${ \bf \Phi } \because { \bf \Phi } _ { a > 0 , } M = \left\{ { x | - a \leq x \leq 1 } \right\} , { \bf { \Phi } } _ { \mathrm { { J i l } } } \oplus { M } = \left\{ { x | x < - a _ { \frac { \pi } { \boxtimes \widetilde { \chi } } } x > 1 } \right\} ,$   
又 ， 所以 $\left\{ { \begin{array} { l } { \displaystyle a > - { \frac { 1 } { 2 } } } \\ { \displaystyle a > 0 } \end{array} } \right.$ 解得  
因此，实数 $a$ 的取值范围是 $\left( 0 , { \frac { 1 } { 2 } } \right)$

24．已知命题：“ $\forall x \in \left( x | - 1 \leq x \leq 1 \right)$ ，都有不等式 $\scriptstyle x ^ { 2 } - x - m < 0$ 成立”是真命题.

（1）求实数 $m$ 的取值集合 $B$ ；（2）设不等式 $( x - 3 a ) ( x - a - 2 ) < 0$ 的解集为 若 $x \in A$ 是 的充分不必要条件，求实数 $a$ 的取值范围.

,+∞）【答案】（1） $( 2 , + \infty )$ ； （2）

【解析】

（1）命题： “ $\forall x \in \left( x | - 1 \leq x \leq 1 \right)$ ， 都有不等式 $\scriptstyle x ^ { 2 } - x - m < 0$ 成立”是真命题，得 $\scriptstyle x ^ { 2 } - x - m < 0$ 在 时恒成立，

∴ ，得 ，即 $B = \{ m | m > 2 \} = ( 2 , + \infty )$ .$( x - 3 a ) ( x - a - 2 ) < 0$   
（2）不等式 ，$\cdot$ 当 $3 a > 2 + a$ ，即 $a > 1$ 时，解集 $A = \left\{ x { \big | } 2 + a < x < 3 a \right\}$ 若 $x \in A$ 是 $x \in B$ 的充分不必要条件，则 $A$ 是 $B$ 的真子集，$\cdot 2 + a \geq 2$ ，此时 $a > 1$ ；  
$\cdot$ 当 $3 a = 2 + a$ ， 即 时，解集 ， 满足题设条件；$\textcircled{3}$ 当 $3 a < a + 2$ ，即 $a < 1$ 时，解集 $A = \left\{ x { \big | } 3 a < x < 2 + a \right\}$ ，若 $x \in A$ 是 $x \in B$ 的充分不必要条件，则 $A$ 是 $B$ 的真子集，$. 3 a \ge 2$ ，此时 $\frac { 2 } { 3 } \leq a < 1$   
综上 $\textcircled{1} \textcircled{2} \textcircled{3}$ 可得 $a \in [ \frac { 2 } { 3 } , + \infty )$