---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 13
source_file: L1-16-函数的奇偶性（二）.md
title: L1-16-函数的奇偶性（二）
type: problem_type
---

第16讲 提升篇

# 函数的奇偶性（二）

一本一讲，共26讲（建议5个月学习）

5个考点+7个题型+20个视频

每周一讲（建议学习时间90分钟）

![](images/78ba90b4ea57ab11a5abd2aaee4df68a4ab39a002514b0be53b915ffcd913b37.jpg)

# 视频内容研发团队

学而思优秀老师

学而思优秀老师和一线高级教师联合创作本书试题，并精心录制讲解视频学而思图书APP扫码即可观看

![](images/0f711396030d723bbc890b56fafca616d76d939e202eaea419cd4167081562de.jpg)

# 傅博宇 老师

毕业于北京大学元培学院  
网校和北大数院高考数学研究联合课题组成员；  
网校高中创新产品部负责人；  
荣获学而思网校“桃李满天下奖”“出类拔萃奖”等；腾讯网中国好老师；  
青少年教育导师认证；  
科学家长观体系的创立者

# 王侃老师

![](images/1bc1cf56d45d7829139788b0027e3886425a0a9ebc89bd9349abdf9a2e65403e.jpg)

毕业于北京大学数学系  
学而思网校高中数学教研奠基人；  
学而思网校高中数学S级教师；  
荣获学而思网校“突出贡献奖”“桃李天下奖”等；擅长总结题型特点，提炼思想方法；  
擅长分层教学，因材施教

# 付恒岩 老师

![](images/6cb554e2fed0447fa8e5a1edb80225220323139bc0b145a9e25a9265b42b5b64.jpg)

毕业于大连理工大学  
网校高中部理科主讲岗后培训师；  
2020年荣获学而思网校最具魅力奖；  
2019年、2020年荣获学而思网校诲人不倦奖；2020年荣获学而思网校高考优秀评卷人；  
2021年担任新浪教育高考数学直播解析特邀嘉宾；“停课不停学”公益课高中数学主讲老师

# 武洪姣 老师

![](images/317575d8c9ca56241619526f1206fcfd5152f872c6fbc62a1b85453eff6b2bb8.jpg)

14年线上线下教学经验；  
学而思网校高中理科教研负责人；  
学而思高中数学特级教师;  
在教学的过程中擅长归纳题型，方法和技巧;  
在高中数学模块中最擅长讲解圆锥曲线和导数；  
无论你是从小数学不好，还是数学一直拔尖，都可以在武老师的课堂上收获很多

1级

# 函数的奇偶性(二)

一本一讲，共26讲（建议5个月学习）

5个考点 $+ 7$ 个题型 $+ z 0$ 个视频每周一讲（建议学习时间90分钟）

16210g9-eln+3²= 2×2-e°+(8³)²二 4-1+4=7

# 预习篇

矛木口い心衣小 提升篇第2讲集合的关系与运算第11讲集合重难点专题第3讲解不等式第12讲常用逻辑用语第4讲函数的概念与三要素第13讲基本不等式第5讲函数的单调性（一）第14讲函数三要素专题第6讲函数的奇偶性（一）第15讲函数的单调性（二）第 $7$ 讲指数幂运算与幂函数第8讲指数函数 第16讲函数的奇偶性（二）第9讲对数运算 第17讲抽象函数第10讲对数函数第18讲指数幂运算与指数函数  
6第19讲对数运算与对数函数第20讲函数与方程第21讲恒成立与存在性问题第22讲三角函数基本概念与诱导公式  
模块1 奇偶性的判断 2第23讲三角函数的图象与性质  
模块2 单调性与奇偶性综合 9第24讲三角公式的应用技巧第25讲三角恒等变换重点题型第26讲正弦型函数的图象与性质叁老签安

# 函数的奇偶性（二）

# 直击课堂

<html><body><table><tr><td>知识模块</td><td>考点</td><td>对应例题</td><td>星标统计</td></tr><tr><td rowspan="5">奇偶性的判断</td><td rowspan="2">判断函数的奇偶性</td><td>例1</td><td rowspan="9">★★7道题 ★★★14道题 ★★★★3道题</td></tr><tr><td>例2</td></tr><tr><td>已知函数奇偶性求参数</td><td>例3</td></tr><tr><td>已知函数奇偶性求解析式</td><td>例4</td></tr><tr><td>构造奇偶函数求值</td><td>例5</td></tr><tr><td rowspan="2">单调性与奇偶性综合</td><td rowspan="2">单调性与奇偶性综合</td><td>例6</td></tr><tr><td>例7</td></tr></table></body></html>

# 学习目标

$\textcircled{1}$ 会判断函数的奇偶性.

$\textcircled{2}$ 会利用奇偶性求解析式，并会构造奇偶函数，结合函数奇偶性求值.

$\textcircled{5}$ 会结合单调性与奇偶性解决比较大小、解不等式等综合问题.

# 模块1奇偶性的判断

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# 判断函数奇偶性的常用方法

(1)定义法： $f ( x )$ 的定义域关于原点对称  
$\textcircled{1}$ 若 $f ( x ) = f ( \ - x )$ ，则 $f ( x )$ 是偶函数.  
$\textcircled{2}$ 若 $f ( x ) = - f ( - x )$ ，则 $f ( x )$ 是奇函数.  
(2)图象法：图象关于坐标原点对称的函数为奇函数，图象关于 $y$ 轴对称的函数为偶函数.(3)四则运算：±  
奇土奇=奇；偶土偶=偶；  
奇×奇=偶；偶×偶=偶；奇×偶=奇；  
奇÷奇=偶；奇÷偶=奇；偶÷偶=偶；偶÷奇=奇。

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点1：判断函数的奇偶性

例1 判断下列函数的奇偶性：

（1）★★ $f ( x ) = x ^ { 4 } + 4 x ^ { 2 } + 3$   
（2）★★ $f ( x ) = x ^ { 3 } + { \frac { 1 } { x ^ { 3 } } }$   
（3）★★ $f ( x ) = 0 , x \in \left[ \begin{array} { l } { - 1 , 2 } \end{array} \right]$   
（4）★★ $f ( x ) = { \frac { x ^ { 2 } + x } { x + 1 } }$   
（5）★★★ $f ( x ) = { \frac { x \mid x \mid } { x ^ { 2 } + 1 } } ;$   
（6）★★ $f ( x ) = \left\{ { \begin{array} { l l } { x - 1 , x > 0 , } \\ { } \\ { x + 1 , x < 0 ; } \end{array} } \right.$   
（7）★★★ $f ( x ) = \left\{ { \begin{array} { l l } { { x \left( x - 1 \right) , x > 0 , } } \\ { { - x \left( x + 1 \right) , x < 0 . } } \end{array} } \right.$

# 学习笔记

# 例2★★

设函数 $f ( x )$ 和 $g ( x )$ 分别是 $\mathbf { R }$ 上的偶函数和奇函数,则下列结论恒成立的是（）

A. $f ( x ) + \vert g ( x ) \vert$ 是偶函数 B. $f ( x ) - \vert g ( x ) \vert$ 是奇函数C. $f ( x ) \mid + g ( x )$ 是偶函数 D. $f ( x ) \mid - g ( x )$ 是奇函数

# 学习笔记

# 达标检测1★★★

如果 $f ( x )$ 是定义在 $\mathbf { R }$ 上的奇函数，那么下列函数中，一定为偶函数的是（ ）

$$
- f ( x ) \mathrm { B . } y = \frac { f ( x ) } { x ^ { 2 } + 1 } \mathrm { C . } y = x ^ { 2 } + f ( x ) \mathrm { D . } y = x f ( x )
$$

# 学习笔记

# 考点2：已知函数奇偶性求参数

# 例3

# （1）★★★

已知 $f ( x ) = a x ^ { 2 } + 2 b x + a - 3 b$ 是偶函数，定义域为 $[ a , 2 a + 1 ]$ ，则 $a =$ $, b = \_ .$

# 学习笔记

# （2）★★★

已知函数 $f ( x ) = { \frac { 1 } { x + 2 } } + { \frac { a } { x - 2 } }$ 是奇函数，则 $a =$

# 学习笔记

# （3）★★★

若函数 $\frac { x } { ( 4 x + 3 ) ( x - a ) }$ 为奇函数，则 $a = \left( \begin{array} { l l l } \end{array} \right)$

A. $\frac 1 2$ B. $\frac { 2 } { 3 }$ C $\frac { 3 } { 4 }$ D.1

# 学习笔记

# 达标检测2★★★

若函数 $f ( x ) = a x ^ { 3 } + x - b$ 是定义在 $( \mathbf { \partial } - 1 \mathbf { \partial } - a \mathbf { \partial } , 2 a )$ 上的奇函数,则 $a ^ { 2 \ : 0 1 7 } \ : +$ $b ^ { 2 ~ 0 1 7 }$ 的值是（ ）

A.1 B.0 C.-1 D.2

# 学习笔记

# 考点3：已知函数奇偶性求解析式

# 例4★★★

已知定义在 $\mathbf { R }$ 上的函数 $f ( x )$ 是奇函数，当 $x > 0$ 时， $f ( x ) = { \sqrt { x } } + x + 1$ ，求$f \left( x \right)$ 的解析式.

# 学习笔记

# 达标检测3★★★

若 $f ( x )$ 是偶函数，且 $x > 0$ 时， $f ( x ) = x ^ { 2 } - 7 x + 3$ ，则 $x < 0$ 时， $f ( x ) =$

A. $x ^ { 2 } + 7 x + 3$ B. $x ^ { 2 } - 7 x + 3$   
C. $x ^ { 2 } + 7 x - 3$ D. $- x ^ { 2 } + 7 x + 3$

# 学习笔记

# 考点4：构造奇偶函数求值

# 例5

# （1）★★★

已知函数 $f ( x ) = a x ^ { 3 } + b x + 8$ ，且 $f ( - 2 ) = 1 0$ ，则 $f ( 2 )$ 的值是( ）

A.-10 B.-6 C.6 D.10

# 学习笔记

# （2）★★★

已知 $y = f ( \boldsymbol { x } ) + \boldsymbol { x } ^ { 2 }$ 是奇函数,且 $f ( 1 ) = 1$ ，若 $g ( x ) = f ( x ) + 2$ ，则 $g ( \ - 1 ) =$

# 学习笔记

# 达标检测4★★★★

已知 $f ( x )$ 和 $g ( x )$ 都是定义在 $\mathbf { R }$ 上的奇函数,若 $F ( x ) = a f ( x ) + b g ( x )$ $+ { x ^ { 3 } } + 1$ ，在 $( \ : 0 , \ : + \infty \ : )$ 上有最大值5,则 $F \left( x \right)$ 在 $( \mathrm { ~  ~ { ~ - ~ } ~ } \infty \mathrm { ~ , ~ } 0 \mathrm { ~ } )$ 上的最小值为

# 学习笔记

# 模块2单调性与奇偶性综合

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 精讲精练

# 拍照批改秒判对错

# 考点5：单调性与奇偶性综合

# 例6

# （1）★★

设偶函数 $f ( x )$ 的定义域为 $\mathbf { R }$ ，当 $x \in [ 0 , + \infty )$ 时函数 $f ( x )$ 是减函数，则$f ( \mathbf { \varepsilon } - 3 \mathbf { \varepsilon } ) , f ( \pi ) , f ( \mathbf { \varepsilon } - 3 . 1 4 )$ 的大小关系为（ ）

A. $f ( \pi ) = f ( \ - 3 . 1 4 ) > f ( \ - 3 )$ $8 . ~ f ( \pi ) ~ < f ( ~ - 3 . ~ 1 4 ) ~ < f ( ~ - 3 )$   
C. $f ( \pi ) > f ( \ - 3 . 1 4 ) > f ( \ - 3 )$ $\mathrm { D . ~ } f ( \pi ) < f ( \mathit { \Pi } - 3 ) < f ( \mathit { \Pi } - 3 . 1 4 )$

# 学习笔记

# （2）★★★

已知函数 $f ( x ) = x ^ { 3 } + x$ ，且 $f ( 3 a - 2 ) + f ( a - 1 ) < 0$ ,则实数 $a$ 的取值范围是

# 学习笔记

# 进阶1★★★★

设函数 $\scriptstyle { y = f ( x ) }$ 是定义在 $[ \mathbf { \Pi } - 1 \mathbf { \Pi } , 1 \mathbf { \Pi } ]$ 上的偶函数,且 $f ( x )$ 在[0,1]上单调递减,若 $f ( 1 - a ) < f ( a )$ ,则实数 $a$ 的取值范围是

# 学习笔记

# 例7★★★

奇函数 $f \left( x \right)$ 在 $( \mathrm { ~ 0 ~ , ~ + ~ } \infty \mathrm { ~ ) ~ }$ 上为增函数，且 $f ( 2 ) = 0$ ，则不等式f（x）-f（-x）<0的解集为

# 学习笔记

# 达标检测5★★★

若函数 $f ( x )$ 是定义在R上的偶函数，在 $( \mathrm { ~  ~ { ~ - ~ } ~ } \infty \mathrm { ~  ~ { ~ , ~ } ~ } 0 \mathrm { ~ ] ~ }$ 上是减函数，且$f ( 2 ) = 0$ ,则使得 $f ( x ) < 0$ 的 $x$ 的取值范围是（ ）

A. $( \mathbf { \partial } - \infty \mathbf { \partial } , 2 )$ B. $( 2 , + \infty )$ C. $( \ - \infty \ , - 2 ) \cup ( 2 , + \infty )$ D. (-2,2)

# 学习笔记

# 进阶2★★★★

设 $f ( x )$ 是偶函数，且在 $[ 0 , + \infty )$ 内是减函数，又 $f ( \ - 2 ) = 0$ ，则$( x - 2 ) f ( x ) < 0$ 的解集是( ）

A $\left\{ x \mid x < - 2 \right.$ 或 $0 < x < 2 \}$ B. $\{ x \mid - 2 < x < 0$ 或 $x > 2 \}$ C. $\left\{ x \mid x > - 2 \right\}$ D. $\left\{ x \mid x > - 2 \right.$ 且 $x \neq 2$

# 学习笔记

![](images/9d8e857847778bb128cb8fc275af2451a200b37f3883323f0fae2615560c7494.jpg)

# 学习总结

![](images/5c9312970524c73044ba436ac276a328b819c6f15ae980281ac5713dffc04192.jpg)

# 直击高考

函数 $f ( x )$ 在 $( \mathbf { \nabla } - \infty , + \infty )$ 上单调递减,且为奇函数.若 $f ( 1 ) = - 1$ ，则满足$- 1 \leqslant f ( x - 2 ) \leqslant 1$ 的 $x$ 的取值范围是（ ）

A $[ \ - 2 , 2 ]$ B.[-1,1] C.[0,4] D.[1,3]

# 学而思秘籍系列图书数学

# 思维培养

# 思维提升

# 小学秘籍系列

![](images/257d4ebb5f5a3c3f4b04d6a1ae1a6062d82a9ab41b12ef9c3223d31ce738ecfd.jpg)

学而思积淀近20年教研经验，培养受益一生的能力。

# 思维突破

![](images/180731949ef49d341860d3b1caca1f6762033e058569efa1aebc8aa041d19c95.jpg)

# 初中秘籍系列

全面覆盖初中基础知识和重难点，帮助学生夯实基础，拓展认知。

![](images/7a2eb23a369ada319f2dbab52aa9535f635bbee43431d74b89a512ef9d508ff4.jpg)

# 高中秘籍系列

全面覆盖高中基础知识和重难点，帮助学生提升能力，突破思维。

# 学而思秘籍系列图书|语文

# 提升素养

![](images/7a380d912abdfd3ac32a7098e76ad994a6f2119c0245a9ae91ca6c24fdbd29ad.jpg)

# 小学秘籍系列

5大模块+2条主线，能力与素养双向提升。

# 能力训练

![](images/2abdef4db0e1eb3bf56aad328f531fbb329eebcaf06c68d3fe90fa1dc92b31bc.jpg)

# 初中秘籍系列

融合课改四大核心素养，培养爱阅读、 善写作、勤思考、会学习的学生。

# 创新体系|真题研习

![](images/ce1cbef69e73a1047137b006e7c8514160167c6156a382bf4206937b6aa6d6a7.jpg)

# 思维创新大通关数学

攻克数学思维难题，通向理想中学。

# 大家一起来“升级

# 参与方式

您在使用本书时，如有任何疑问或对图书有任何建议，请扫码进行反馈，并查看反馈采纳结果。

![](images/034505912fe445dc99bb69d9fba3416f8856fa351ef0ae8173c0c08828976895.jpg)

# 奖励

您的反馈一经采纳，我们将会送出总价值35元的图书抵扣券（相同内容的反馈，依据反馈时间，奖励前三位）。请扫码关注公众号，并在对话框中发送反馈时填写的手机号，领取抵扣券。

![](images/2c5e32d324a1219c3311e3dd9b4a5dbcc05033bd3f2c3d6f8da8ef95afa0a8ef.jpg)

# 合理规划学习时间

先自己定一个目标，即制定半年学习规划。

2 再将目标细化到每一周，每周学习一本（平均5个考点）。

3 配套课堂巩固的练习， 让学习更有效！

![](images/d692dfee782ae8fdd3e03637039065d6a0456b553b3fb957de9a74aa5ecec3a2.jpg)

![](images/2998e44759919bf374ae3bfcd69ddd3fb2198beea14b0c0e97cc2c92984c052f.jpg)  
·共6级·每级17-26讲