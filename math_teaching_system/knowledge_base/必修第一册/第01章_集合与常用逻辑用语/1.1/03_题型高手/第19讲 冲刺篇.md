---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 17
source_file: L5-19-数列（二）.md
title: L5-19-数列（二）
type: problem_type
---

第19讲 冲刺篇

# 数列（二）

一本一讲，共26讲（建议5个月学习）

3个考点+9个题型+13个视频

每周一讲（建议学习时间90分钟）

![](images/281c3fcca6ce825136cd40c9531ff198909a71a81a0d9c231ac8208b33c8889f.jpg)

# 视频内容研发团队

学而思优秀老师

学而思优秀老师和一线高级教师联合创作本书试题，并精心录制讲解视频学而思图书APP扫码即可观看

![](images/569ebb76d1d5d7c92167465b4c93ff3612b3ba26d67d2fabcaa473956e41cefb.jpg)

# 傅博宇 老师

毕业于北京大学元培学院  
网校和北大数院高考数学研究联合课题组成员；  
网校高中创新产品部负责人；  
荣获学而思网校“桃李满天下奖”“出类拔萃奖”等；腾讯网中国好老师；  
青少年教育导师认证；  
科学家长观体系的创立者

![](images/d8f1e34493f7d11d410831a08e3c1a2ec2bf88589116d342b8aad3cc3d8cf674.jpg)

# 王侃老师

毕业于北京大学数学系  
学而思网校高中数学教研奠基人；  
学而思网校高中数学S级教师；  
荣获学而思网校“突出贡献奖”“桃李天下奖”等；擅长总结题型特点，提炼思想方法；  
擅长分层教学，因材施教

![](images/64a7a69a6fb8e0a54111ed950893cdcb0a8d72511d99ea9676173e5bf62ccc97.jpg)

# 付恒岩 老师

毕业于大连理工大学  
网校高中部理科主讲岗后培训师；  
2020年荣获学而思网校最具魅力奖；  
2019年、2020年荣获学而思网校海人不倦奖；2020年荣获学而思网校高考优秀评卷人；  
2021年担任新浪教育高考数学直播解析特邀嘉宾；“停课不停学”公益课高中数学主讲老师

# 武洪姣 老师

![](images/e3e851d282a19ff5a1b15d22d350eb9c9064aedaf0750d1fc7c0d5eb19c5ef7d.jpg)

14年线上线下教学经验；  
学而思网校高中理科教研负责人；  
学而思高中数学特级教师；  
在教学的过程中擅长归纳题型，方法和技巧；  
在高中数学模块中最擅长讲解圆锥曲线和导数；  
无论你是从小数学不好，还是数学一直拔尖，都可以在武老师的课堂上收获很多

5级

# 数列（

一本一讲，共26讲（建议5个月学习）

3个考点 $+ 9$ 个题型 $+ 1 3$ 个视频

每周一讲（建议学习时间90分钟）

![](images/b30ea5a874d9608bbe6b851a45181e84ddc9562aea84ea7057f50dc7f3e68d97.jpg)

# 提升篇

第1讲集合、逻辑、函数初步第2讲基本初等函数与函数 冲刺篇的性质第11讲函数性质的应用第3讲不等式第12讲函数零点与恒成立问题第4讲导数（一）第13讲导数的应用一单调性与极值第5讲导数（二）最值第6讲三角函数第14讲导数的应用 恒成立问题第7讲平面向量与解三角形第15讲导数的应用—零点个数与第8讲数列（一）隐零点问题第9讲立体几何第16讲导数的应用一一极值点偏移第10讲解析几何第17讲三角函数与平面向量第18讲解三角形第19讲数列（二）  
模块1 数列求通项 2第20讲三视图与球的切接问题  
模块2 数列求和 9第21讲空间中的角第22讲统计概率第23讲圆锥曲线的几何性质第24讲几何条件的代数转化第25讲圆锥曲线中的最值范围问题第26讲定点定值问题与轨迹方程问题参考答案

# 数列（二）

# 直击课堂

<html><body><table><tr><td>知识模块</td><td>考点</td><td>对应例题</td><td>考查概率</td></tr><tr><td>数列求通项</td><td>由推求通项</td><td>例1例2、例3</td><td>★★★★★</td></tr><tr><td>数列求和</td><td>数列求和</td><td>例6、例</td><td>★★★★★</td></tr></table></body></html>

# 本讲解读

# ①数列的概念和简单表示法

(1)了解数列的概念和几种简单的表示方法(列表、图象、通项公式).  
(2)了解数列是自变量为正整数的一类函数.

# ②等差数列、等比数列

(1)理解等差数列、等比数列的概念.(2)掌握等差数列、等比数列的通项公式与前 $n$ 项和公式.(3)能在具体的问题情境中识别数列的等差关系或等比关系，并能用有关知识解决相应的问题.（4）了解等差数列与一次函数、等比数列与指数函数的关系.

# 模块1数列求通项

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# ①累加法与累乘法

(1)累加法

$a _ { n } - a _ { n - 1 } = f ( n ) \left( n \geqslant 2 , n \in \mathbf { N } ^ { * } \right)$ $a _ { 1 }$ 与 $f ( n )$ 已知，则 $a _ { n } - a _ { 1 } = f ( 2 )$ $+ f ( 3 ) + \cdots + f ( n ) .$

(2)累乘法

$\frac { a _ { n } } { a _ { n - 1 } } = f ( n ) \left( n \geqslant 2 , n \in \mathbf { N } ^ { * } \right)$ ）， $a _ { 1 }$ 与 $f ( n )$ 已知，则 $\frac { a _ { n } } { a _ { 1 } } = f ( 2 ) \cdot f ( 3 )$ $\cdots \cdot f ( n )$

# ②一阶线性递推

$a _ { n + 1 } = p a _ { n } + q \left( p \neq 0 , 1 , q \neq 0 \right) , a$ 已知,构造新数列， $a _ { n + 1 } + \lambda =$ $p ( a _ { n } + \lambda )$ ，其中 $\lambda = \frac { q } { p - 1 }$ ，若数列 $\{ a _ { n } + \lambda \}$ 的首项 $a _ { 1 } + \lambda \neq 0$ ，则 $\{ a _ { n } \ +$ $\lambda \ : \backslash$ 为等比数列，进而求出 $\left\{ a _ { n } \right\}$ 的通项公式.

# $\circledcirc$ 其他递推类型

（1)an+1=pan+rq（pqr≠0），递推公式除以qn+1， 则 ${ \frac { a _ { n + 1 } } { q ^ { n + 1 } } } = { \frac { p } { q } } \cdot { \frac { a _ { n } } { q ^ { n } } }$ $+ \frac { r } { q }$ ,转化为一阶线性递推,用上述方法逐渐求出 $\left\{ a _ { n } \right\}$ 的通项公式.

$2 ) a _ { n + 1 } = { \frac { p a _ { n } } { q a _ { n } + r } } ( p q r \not = 0$ ),递推公式取倒数， $\frac { 1 } { a _ { n + 1 } } = \frac { r } { p } \cdot \frac { 1 } { a _ { n } } + \frac { q } { p }$ 转化为一阶线性递推，用上述方法逐渐求出 $\left\{ \left. a _ { n } \right\} \right.$ 的通项公式.

$( 3 ) a _ { n + 1 } - a _ { n } = m a _ { n + 1 } a _ { n }$ ，递推公式除以 $a _ { n + 1 } a _ { n }$ ，化为 $\frac { 1 } { a _ { n + 1 } } - \frac { 1 } { a _ { n } } = - m , \left\{ \frac { 1 } { a _ { n } } \right\}$ 是等差数列,求出$\left\{ a _ { n } \right\}$ 的通项公式.

# $\textcircled { 4 5 }$ 由 $S _ { n }$ 与 $a _ { n }$ 的关系求通项

(1)已知 $S _ { n }$ 求通项

$$
a _ { n } = \left\{ { \atop S _ { n } } , n = 1 \right.
$$

(2)已知 $S _ { n }$ 与 $a _ { n }$ 的关系求通项

$\textcircled{1}$ 分别写出 $S _ { n }$ 与 $S _ { n - 1 }$ 的表达式,两式相减,建立 $a _ { n }$ 的递推关系，再求解；$\textcircled{2}$ 用 $S _ { n } - S _ { n - 1 }$ 替换递推公式中的 $a _ { n }$ ，建立 $S _ { n }$ 的递推关系,求出 $S _ { n }$ 后再求 $a _ { n }$

# 直击高考

全国理数的数列解答题和三角函数解答题每年只考一个，考解答题时一般不再考小题，不考解答题时，就考两个小题，交错考法不一定分奇数年或偶数年;数列专题在高考中侧重于基础知识和通法的考查，常从基本量的计算、数列求和等方面选题,此外也需要注意复习回顾有关等差、等比数列定义法判断的题型.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点1：由递推求通项

# 例1

(1)在数列 $\left\{ \boldsymbol { a } _ { n } \right\}$ 中 $a _ { 1 } = 3 , a _ { n } = { \frac { 1 } { 3 } } a _ { n - 1 } - 2 ( n \geq 2 )$ ，则 $a _ { n } =$

# 学习笔记

(2)已知数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 满足 $a _ { n + 1 } = 2 a _ { n } + 3 \cdot 2 ^ { n } , a _ { 1 } = 2$ ，求数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 的通项公式

# 学习笔记

# 例2

已知数列 $\left\{ a _ { n } \right\}$ 满足 $a _ { 1 } = 1 , a _ { n + 1 } = 2 a _ { n } + \lambda ( \lambda$ 为常数).试探究数列 $\{ a _ { n } ~ +$ $\lambda \left\{ \begin{array} { r l } \end{array} \right.$ 是不是等比数列,并求 $a _ { n }$

# 学习笔记

![](images/727705eff75ba3c3e9c712fd7ff9de993427dac368c7b8355c6ecc7bebe72d69.jpg)

# 例3

已知数列 $\{ a _ { n } \}$ 满足 $n a _ { n } - ( n + 1 ) a _ { n - 1 } = 2 n ^ { 2 } + 2 n ( n = 2 , 3 , 4 , \cdots ) , a _ { 1 } = 6 .$ 求证 $\left\{ { \frac { a _ { n } } { n + 1 } } \right\}$ 为等差数列，并求出 $\{ a _ { n } \}$ 的通项公式.

# 学习笔记

# 考点2：由S求通项

# 例4

(1)已知数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和是 $S _ { n }$ ，且 $a _ { n } + S _ { n } = 3 n - 1$ ，则数列 $\left\{ a _ { n } \right\}$ 的通项公式 $a _ { n } =$

# 学习笔记

(2)已知数列 $\left\{ a _ { n } \right\}$ 中， $a _ { n } > 0$ ,且对于任意正整数 $n$ 有 $S _ { n } = { \frac { 1 } { 2 } } { \bigg ( } a _ { n } + { \frac { 1 } { a _ { n } } } { \bigg ) }$ 求通项公式 $a _ { n }$ ：

# 学习笔记

# 例5

已知数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和 $S _ { n } = 2 a _ { n } - 2 ^ { n }$

(1)证明 $\{ a _ { n + 1 } - 2 a _ { n } \}$ 为等比数列；

(2)求数列 $\left\{ \begin{array} { l } { a _ { n } \rule { 0 ex } { 5 ex } } \end{array} \right\}$ 的通项公式.

# 学习笔记

# 模块2数列求和

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师高效学知识

# 知识点睛

# $\textcircled{1}$ 裂项相消法

裂项相消法的基本思想是设法将数列的每一项拆成两项或若干项，并使它们在相加时除了首尾各有一项或少数几项外，其余各项都能前后正负相消，进而求出数列的前 $n$ 项和.

常见的裂项技巧：

(1)分式裂项 ${ \frac { 1 } { n ( n + k ) } } = { \frac { 1 } { k } } \left( { \frac { 1 } { n } } - { \frac { 1 } { n + k } } \right) , { \frac { 1 } { 4 n ^ { 2 } - 1 } } = { \frac { 1 } { ( 2 n - 1 ) ( 2 n + 1 ) } } =$ ${ \frac { 1 } { 2 } } \left( { \frac { 1 } { 2 n - 1 } } - { \frac { 1 } { 2 n + 1 } } \right) ;$

(2）无理式裂项 $: \frac { 1 } { \sqrt { n } + \sqrt { n + k } } = \frac { 1 } { k } \big ( \ \sqrt { n + k } \ - \sqrt { n } \big ) \ ;$

（3）对数裂项 $\operatorname { l n } { \frac { n + 1 } { n } } = \ln ( n + 1 ) - \ln n$

(4)等差数列构造的裂项：等差数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 的公差为 $d$ ，前 $n$ 项和 $S _ { n }$ $= A n ( n + k )$ ，则

$$
\textcircled { 1 } \frac { 1 } { a _ { n } a _ { n + 1 } } = \frac { 1 } { d } \bigg ( \frac { 1 } { a _ { n } } - \frac { 1 } { a _ { n + 1 } } \bigg ) , \textcircled { 2 } \frac { 1 } { S _ { n } } = \frac { 1 } { A k } \bigg ( \frac { 1 } { n } - \frac { 1 } { n + k } \bigg ) .
$$

# 2 错位相减法

错位相减法适用于求数列 $\{ a _ { n } b _ { n } \}$ 的前 $n$ 项和,其中 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 是公差 $d$ ≠0的等差数列,{b}是公比q≠1的等比数列，即an+1-an=d,bn+1=$b _ { n } q$ ：

# 具体步骤：

（1）和式： $S _ { n } = a _ { 1 } b _ { 1 } + a _ { 2 } b _ { 2 } + a _ { 3 } b _ { 3 } + \cdots + a _ { n } b _ { n } \textcircled { 1 } ;$   
(2）错位： $q S _ { n } = a _ { 1 } b _ { 2 } + a _ { 2 } b _ { 3 } + \cdots + a _ { n - 1 } b _ { n } + a _ { n } b _ { n } q \textcircled { 2 } ;$   
（3）相减：①- $\textcircled{2}$ ，（1-q)Sn=ab+d（b+b++bn)-anbnq；（4）求和：n-1项等比数列求和（ $b _ { 2 }$ +b+··+bn）；  
（5）整理：等式左古两边同除以1-q，和式藝理成简洁的孤式；（6）检验：用 $a _ { n } b _ { n }$ 的前几项检验

# $\textcircled { 8 }$ 其他求和方法

(1)公式法$\textcircled{1}$ 等差数列 $; S _ { n } = \frac { n ( a _ { 1 } + a _ { n } ) } { 2 } = n a _ { 1 } + \frac { n ( n - 1 ) } { 2 } d ;$ $: S _ { n } = \left\{ \begin{array} { l l } { n a _ { 1 } , q = 1 , } \\ { \qquad \quad } \\ { \displaystyle \frac { a _ { 1 } \left( 1 - q ^ { n } \right) } { 1 - q } = \frac { a _ { 1 } - a _ { n } q } { 1 - q } , q \neq 1 , } \end{array} \right.$ $\begin{array} { l } { \displaystyle \mathfrak { D } 1 + 2 + \dots + n = \frac { n ( n + 1 ) } { 2 } ; } \\ { \displaystyle \bigoplus _ { \mathbf { \alpha } \geq 1 ^ { 2 } + 2 ^ { 2 } + \dots + n ^ { 2 } = \frac { n ( n + 1 ) } { 6 } ( 2 n + 1 ) } ; } \\ { \displaystyle \bigoplus _ { \mathbf { \alpha } \geq 3 ^ { 3 } + 2 ^ { 3 } + \dots + n ^ { 3 } = \left[ \frac { n ( n + 1 ) } { 2 } \right] ^ { 2 } . } } \end{array}$

# 重点笔记

(2)分组求和

数列的通项公式较复杂时，把数列中的每一项拆分成多项的和，从而将原数列拆分成多个简单可直接求和的数列的和，再分别对各部分求和.

(3)并项求和

主要解决周期数列求和与相邻奇偶项求和的问题.

$\textcircled{1}$ 周期数列：先求一个周期内各项的和,再看整周期数和最后构不成整周期的项,最后求和；

$\textcircled{2}$ 分奇偶求和： $\left\{ { { a } _ { n } } \right\}$ 的奇数项和偶数项规律不同，讨论 $n$ 分别是奇数、偶数时，数列前 $n$ 项共有多少奇数项、多少偶数项，分别求和再相加.

(4）倒序相加

常见于特殊函数构造的数列， $a _ { k } + a _ { n + 1 } - k$ 为定值,可用倒序相加法.

常见的函数形式有 $f ( x ) + f ( 1 - x )$ 为定值， $f ( x ) \ + f { \left( \frac { 1 } { x } \right) }$ 为定值.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点3：数列求和

# 例6

已知等差数列 $\left\{ \left. a _ { n } \right\} \right.$ 的前 $n$ 项和为 $S _ { n }$ ，且 $a _ { \scriptscriptstyle 4 } = 5 , S _ { \scriptscriptstyle 9 } = 5 4 .$ 若 $b _ { n } = \frac { 1 } { S _ { n } }$ ,求数列$\left\{ \begin{array} { l } { { b _ { n } } } \end{array} \right\}$ 的前 $n$ 项和.

# 学习笔记

# 例7

已知 $\left\{ a _ { n } \right\}$ 是首项为1，公差为2的等差数列.求 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 的通项公式及$\left\{ { \frac { 1 } { a _ { n } a _ { n + 1 } } } \right\}$ 的前 $n$ 项和.

# 学习笔记

# 例8

求数列 $\left\{ { \frac { 1 - 2 n } { 3 ^ { n - 1 } } } \right\}$ $n$ $S _ { n }$

# 学习笔记

# 例9

设数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和为 $S _ { n }$ ，且 $a _ { 1 } = 1 , a _ { n + 1 } = 2 S _ { n } + 1$ ，数列 $\left\{ \begin{array} { l }  \displaystyle b _ { n } \right\} \end{array}$ 满足 $a _ { \scriptscriptstyle 1 }$ $\mathbf { \epsilon } = \boldsymbol { b } _ { 1 }$ ，点 $P ( b _ { n } , b _ { n + 1 } )$ 在直线 $x - y + 2 = 0$ 上， $n \in \mathbf { N } \mathbf { \Lambda } ^ { * }$

(1)求数列 $\left\{ a _ { n } \right\} , \left\{ b _ { n } \right\}$ 的通项公式；

（2）设 $c _ { n } = { \frac { b _ { n } } { a _ { n } } }$ ，求数列 $\left\{ c _ { n } \right\}$ 的前 $n$ 项和 $T _ { n }$

# 学习笔记

# 学而思秘籍系列图书|数学

# 思维培养

![](images/ae241ec26dca837e4d16a7ff552c96d17ca9198c87238a4491f52f8591ddadc5.jpg)

# 小学秘籍系列

学而思积淀近20年教研经验，培养受益一生的能力。

思维提升

![](images/55b5dd4aa36b8d251464939e44d5ee2c1d2b605eea3fdb7d6a024db2d7d8bc75.jpg)

# 初中秘籍系列

全面覆盖初中基础知识和重难点，帮助学生夯实基础，拓展认知。

# 思维突破

![](images/1cdd1466d1b880d9a07070474aa99ea768f6d2df21f5e8afcb75fc7738b6d726.jpg)

# 高中秘籍系列

全面覆盖高中基础知识和重难点，帮助学生提升能力，突破思维。

# 学而思秘籍系列图书|语文

# 提升素养

![](images/a5eb21c8e250069f1114a9bf0c1a0feb5b06e8973eaf0d1c561195183b01de27.jpg)

# 小学秘籍系列

5大模块+2条主线，能力与素养双向提升。

能力训练

![](images/afa637cd5600fa50c1c5e70078bb682a93e0ba346c691b0bb3298235cec69a9e.jpg)

# 初中秘籍系列

融合课改四大核心素养，培养爱阅读， 善写作、勤思考、会学习的学生。

# 创新体系|真题研习

![](images/81532bca01e8464736dee83b9ffc17e90bfe9937220b376bd7ccda213278ba7b.jpg)

# 思维创新大通关数学

攻克数学思维难题，通向理想中学。

# 大家一起来“升级”

# 参与方式

您在使用本书时，如有任何疑问或对图书有任何建议，请扫码进行反馈，并查看反馈采纳结果。

![](images/51764a23b16d0fa5dfb12d0532b93ed24183a3a9c6f070ce62bc4867987dc847.jpg)

# 奖励

您的反馈一经采纳，我们将会送出总价值35元的图书抵扣券（相同内容的反馈，依据反馈时间，奖励前三位）。请扫码关注公众号，并在对话框中发送反馈时填写的手机号，领取抵扣券。

![](images/1ccd7a996dba0ac5a2fdfa07950a6625c77ee5554c51bf313ad8a4c94b0fc273.jpg)

# 合理规划学习时间

![](images/b787c866a61678ca94d6e1c4c55c15efafc6a9c7e68e77465c743785460c2c9e.jpg)

先自己定一个目标，即制定半年学习规划。再将目标细化到每一周，每周学习一本（平均5个考点）。3 配套课堂巩固的练习，让学习更有效！

![](images/eaa2971ea079cb5fc0ae57dc1d8125a0e4c260b094025dd3cc62b5bff7b9bf4a.jpg)  
·共6级·每级17-26讲