---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 中等
estimated_study_time: 120
source_file: 新高考高中数学题型总结-选修3试题册.md
title: 新高考高中数学题型总结-选修3试题册
type: problem_type
---

# 目录

第1讲分类加法计数原理与分步乘法计数原理题型一：分类加法计数原理. 4题型二：分步乘法计数原.. ...6题型三：两个原理的合应用

第2.排列及排数.  
题型一：排列的概念… 11  
题型二：排列数的计算.. 1  
题型三：解排列数方程和不等4  
题型四：证明排列数恒等式15  
题型五：排列的单用.6  
第3讲组合及组合数 19  
题型一：组合的概念 2  
题型二：组合数的计算21  
题型三：解组合数方程和不等式.. .22  
题型四：组合数的性质及恒等式.22  
题型五：组合的单应用24  
第4讲排列组合常见题型总结分析.. ….28  
题型一：特殊元素与特殊位置优待法.  
题型二：分类讨论思想.. 29  
题型三：空法（不相邻问题）  
题型四：捆绑法（相邻问题） …………………………………………….31  
题型五：均分组问题除法策3  
题型六：分配问题先分组再分配33  
题型七：正难则反及交叉问.. ..353  
题型八：定序问题（消序法） 36  
题型九：隔法（元素相同问题隔板法）.8  
题型十：排列组合中的涂色问题.  
题型十一：与几何有关的组合应用题. .42  
第5讲二项式定理常考考点44  
题型一： $( a + b ) ^ { n }$ 展开式45  
题型二：开的系5  
题型三：二式系数和和各系数和47  
题型四：求两个二式积的展开式指定幂的系数48  
题型五：求三项展开式中指定幂的系数.. .00  
题型六：有理现项问是. 51  
题型七：求系数最大项问题 52  
题型八：利用“赋值法"及二项式性质，求部分项系数，二项式系数和.. ..53  
题型九：利用二项式定理求余数.. .55  
题型十：利用二项式定理求近似值.. ….56  
题型十一：二项式定理与杨辉三角57  
专题一：新高考计数原理压轴题精选61  
专题二新高考计数原理压新文化试题精选67  
第6讲古典概型中的排列组合问题.73  
第7讲条件概率合全概率公及应用 .76  
题型一：条件概率的计算. 77

#

选择性必修第三册 考点题型方法总结 申老师高考内部资料  
题型二：条件概率的证明 .80  
题型三：全概率公式及应用  
第8讲离散型随机量的分布列及其性质 ...85  
题型一：随机变量．念.… .85  
题型二：离散型随机变量与连续型随机变量.. ....86  
题型三：离散型随机变量分布 …87  
题型四：离散型随机变量分布列的性质.  
第9讲离散型随机变量的期望方差及其性质93  
题型一：离散型随机变量的期. …94  
题型二：离散型随机变量的方差  
题型三：离散型随机变量的期望方差的性质. 10  
第10讲两点分布，二项分布及超几何分布的区别与联系. …04  
题型一：两点分布的概念及分布列.. …05  
题型二：两点分布的期望方差..  
题型三：独重复实验发次的概率… ....107  
题型四：二项分布的概念  
题型五：二项分布的期望方差.  
题型六：超几何分布的概率 16  
题型七：超几何分布的期望方差， 17  
第11讲分布列概率中的三大最值问题 .121  
题型一：二项分布的转化为数列问题求最值121  
题型二：二项分布的转化为导数问题求最值.. ....124  
题型三：超几何分布的概率最值. 26  
第12讲数列递推方法计算概率与一维马尔科夫过程.. 127  
第13讲期望方差的实际应用. .133  
第14讲正态分布常考考点. 145  
题型一：正态曲线. …146  
题型二利用正态分布求概率.  
题型三：正态分布的实际应用. ..5  
第15讲正态分布中的最值范围问题 ……………………………………… ...57  
专题三新高考离散型随机变量压轴培优试题精选. ...159  
专题四新高考离散型随机变量新文化试题精选. 164  
第16讲变量间的相关关系.. .68  
题型一：相关关系的判断.. 169  
题型二：求回归直线的方… 171  
题型三：两个变量间的相关性分析. …174  
题型四：相关系数的计算. 中78  
题型五：线性归方程恒样本点的应用82  
第17讲非线性回归方程. 186  
第18讲独立性检.. 192  
题型一： $2 \times 2$ 列联表. 192  
题型二：等高条形图.. …194  
题型三：独立性检验解决实际问题. ….197

# 第1讲分类加法计数原理与分步乘法计数原理

# 【考点分析】

# 考点一：分类加法计数原理

完成一件事有两类不同方案，在第1类方案中有 $m$ 种不同的方法，在第2类方案中有 $n$ 种不同的方法，那么完成这件事共有 $N { = } m + n$ 种不同的方法.

# 考点二：分步乘法计数原理

完成一件事需要两个步骤，做第1步有 $m$ 种不同的方法，做第2步有 $n$ 种不同的方法，那么完成这件事共有 $N { = } m \times n$ 种不同的方法.

考点三：两个计数原理的区别和联系  

<html><body><table><tr><td></td><td>分类加法计数原理</td><td>分步乘法计数原理</td></tr><tr><td>联系</td><td colspan="2">回答的都是关于完成一件事情的不同方法的种数的问题</td></tr><tr><td>区别一</td><td>针对的是“分类”问题</td><td>针对的是“分布”问题</td></tr><tr><td>区别二</td><td>各种方法相互“独立”</td><td>各个步骤中的方法互相“依存”</td></tr><tr><td>区别三</td><td>任何一种方法都可以做完这件事</td><td>只有各个步骤都完成才算做完这件事</td></tr></table></body></html>

考点四：分类加法计数原理的推广

完成一件事有 $_ n$ 类不同的方案，在第1类方案中有 $m _ { 1 }$ 种不同的方法，在第2类方案中有 $m _ { 2 }$ 种不同的方法，...  
在第 $n$ 类方案中有 $m _ { n }$ 种不同的方法，那么完成这件事共有 $N = m _ { 1 } + m _ { 2 } + \cdots + m _ { n }$ 种不同的方法.

# 考点五：分步乘法计数原理的推广

完成一件事需要分成 $n$ 个步骤，做第1步有 $m _ { 1 }$ 种不同的方法，做第2步有 $m _ { 2 }$ 种不同的方法，.，做第 $_ n$ 步有 $m _ { n }$ 种不同的方法，那么完成这件事共有 $N { = } m _ { 1 } \times m _ { 2 } \times \cdots \times m _ { n }$ 种不同的方法.

# 考点六：注意的问题

1.应用分类加法计数原理应注意的问题

$\textcircled{1}$ 明确题目中所指的“完成一件事"是什么事，完成这件事可以有哪些办法，怎样才算是完成这件事.  
$\textcircled{2}$ 完成这件事的 $n$ 类方法是相互独立的，无论哪种方案中的哪种方法都可以独立完成这件事，而不需要再用到其他的方法.  
$\textcircled{3}$ 确定恰当的分类标准，准确地对“这件事"进行分类，要求每一种方法必属于某一类方案，不同方案的任意两种方法是不同的方法，也就是分类时必须做到既“不重复”也“不遗漏”.

2.应用分步乘法计数原理要注意的问题

$\textcircled{1}$ 明确题目中所指的“完成一件事"是什么事，单独用题目中所给的某种方法是不是能完成这件事，也就是说是否必须要经过几步才能完成这件事.  
$\textcircled{2}$ 完成这件事要分若干个步骤，只有每个步骤都完成了，才算完成这件事，缺少哪一步，这件事都不可能完成.$\textcircled{3}$ 根据题意正确分步，要求各步之间必须连续，只有按照这几步逐步地去做，才能完成这件事，各步骤之间不能重复，也不能遗漏.

选择性必修第三册题型一：分类加法计数原理

解题思路：用分类加法计数原理计数，关键在于根据问题的特点确定一个适合它的分类标准在这个分类标准下，完成这件事的任何一种方法只属于某一类，并且分别属于不同种类的两种方法是不同的。

# 【精选例题】

【例1】现有5幅不同的油画，2幅不同的国画，7幅不同的水彩画，从这些画中选一幅布置房间，则不同的选法共有（）

A.7种 B.9种 C.14种 D.70种【例2】我们把各位数字之和为6的四位数称为"六合数”（如2130是“六合数"），则其中首位为2的"六合数”共有（）.

A.18个 B.15个 C.12个 D.9个【例3】某校高二年级举行健康杯篮球赛，共20个班级，其中1、3、4班组成联盟队，2、5、6班组成联盟队，一共有16支篮球队伍，先分成4个小组进行循环赛，决出8强（每队与本组其他队赛一场)，即每个组取前两名（按获胜场次排名，如果获胜场次相同的就按净胜分排名）；然后晋级的8支队伍按照确定的程序进行淘汰赛，淘汰赛第一轮先决出4强，晋级的4支队伍要决出冠亚军和第三、四名，同时后面的4支队伍要决出第五至八名，则总共要进行篮球赛的场次为（）

A.32 B.34 C.36 D.38【例4】若 $m$ $n \in \mathrm N$ ， $m > 0$ ， $n > 0$ ，且 $m + n \leq 8$ ，则平面上的点 $\scriptstyle ( m , n )$ 共有（）.

A.21个 B.20个 C.28个 D.30个【例5】某日，甲、乙、丙三个单位被系统随机预约到 $A$ ， $B$ ， $C$ 三家医院接种疫苗且每个单位只能被随机预约到一家医院，每家医院每日至多接待两个单位．已知 $A$ 医院接种的是只需要打一针的腺病毒载体疫苗， $B$ 医院接种的是需要打两针的灭活疫苗， $C$ 医院接种的是需要打三针的重组蛋白疫苗，则甲单位不接种需要打三针的重组蛋白疫苗的预约方案种数为（）

A.27 B.24 C.18 D.16【例6】如图，将钢琴上的12个键依次记为 $a _ { 1 } , a _ { 2 } , \cdots , a _ { 1 2 }$ ，设 $1 \leq i < j < k \leq 1 2$ .若 $k - j = 3$ 且 $j - i = 4$ ，则称 $a _ { i } , a _ { j } , a _ { k }$ 为原位大三和弦；若 $k - j = 4$ 且 $j - i = 3$ ，则称 $a _ { i } , a _ { j } , a _ { k }$ 为原位小三和弦．用这12个键可以构成的原位大三和弦与原位小三和弦的个数之和为

![](images/5e2054d2ccfbe3bf2ef1408e7a2185ea62b17cb07cf4bfe3f251646038bb19c5.jpg)

# 【题型专练】

1.为了方便广大市民接种新冠疫苗，提高新冠疫苗接种率，某区卫健委在城区设立了11个接种点，在乡镇设立了19个接种点．某市民为了在同一接种点顺利完成新冠疫苗接种，则不同接种点的选法共有（）

A.11种 B.19种 C.30种 D.209种

2．从甲地出发前往乙地，一天中有4趟汽车、3火车和1趟航班可供选择．某人某天要从甲地出发，去乙地旅游，则所有不同走法的种数是（）

A.16 B.15 C.12 D.8

3．某高中为高一学生提供四门课外选修课：数学史、物理模型化思维、英语经典阅读、《红楼梦》人物角色分析.要求每个学生选且只能选一门课程.若甲只选英语经典阅读，乙只选数学史或物理模型化思维，学生丙、丁任意选，这四名学生选择后，恰好选了其中三门课程，则他们选课方式的可能情况有 种.

4．书架的第1层放有4本不同的计算机书，第2层放有3本不同的文艺书，第3层放有2本不同的体育书.从书架上任取1本书，不同的取法有 种.

5.从数字1，2，3，4中取出3个数字（允许重复），组成三位数，各位数字之和等于6，则这样的三位数的个数为（）

A.7 B.9 C.10 D.13

6．已知集合 $A = \left\{ 2 , 4 , 6 , 8 \right\}$ ， $B = \left\{ 1 , 3 , 5 , 7 , 9 \right\}$ ，从 $A$ 中取一个数作为十位数字，从 $B$ 中取一个数作为个位数字，能组成_个不同的两位数，能组成_个十位数字小于个位数字的两位数.

选择性必修第三册

题型二：分步乘法计数原理

解题思路：用分步乘法计数原理计数，关键在于分清楚完成一件事情，需要分几个步骤，只有完成了所有步骤才完成了这件事情。

# 【精选例题】

【例1】仅有甲、乙、丙三人参加四项比赛，所有比赛均无并列名次，则不同的夺冠情况共有（）种.

A. ${ \mathsf { A } } _ { 4 } ^ { 3 }$ B.4 C.34 D. $\mathrm { C } _ { 4 } ^ { 3 }$ 【例2】洛书，古称龟书，是阴阳五行术数之源，在古代传说中有神龟出于洛水，其甲壳上有此图象，如图，结构是戴九履一，左三右七，二四为肩，六八为足，以五居中，五方白圈皆阳数，四隅黑点为阴数（图中白圈为阳数，黑点为阴数）.现利用阴数和阳数构成一个四位数，规则如下：（从左往右数）第一位数是阳数，第二位数是阴数，第三位数和第四位数一阴一阳和为7，则这样的四位数的个数有（）

0-0-0-0-0-0-0-0-0 0101010101010

A.120 B.90 C.48 D.12【例3】某学校举行校庆文艺晚会，已知节目单中共有七个节目，为了活跃现场气氛，主办方特地邀请了三位老校友演唱经典歌曲，并要将这三个不同节目添入节目单，而不改变原来的节目顺序，则不同的安排方式有种.

【例4】如图所示，用3种不同的颜色涂入图中的矩形A，B， $C$ 中，要求相邻的矩形不能使用同一种颜色，则不同的涂法数为

<html><body><table><tr><td>A</td><td>B</td><td>C</td></tr></table></body></html>

【例5】乘积式 ${ \left( a _ { 1 } + a _ { 2 } + a _ { 3 } \right) } { \left( b _ { 1 } + b _ { 2 } \right) } { \left( c _ { 1 } + c _ { 2 } + c _ { 3 } \right) }$ 展开后的项数是【例6】从0，1，2，3这四个数中选三个不同的数作为函数 $f ( x ) = a x ^ { 2 } + b x + c$ 的系数，可组成不同的一次函数共有 个，不同的二次函数共有 个，（用数字作答）

【例7】正整数2160的不同正因数的个数为（）.

A.20 B.28 C.40 D.50

# 【题型专练】

1．五名高中生报考三所高等院校，每人报且只报一所，不同的报名方法有 种.

2.（1）将4封信投入3个信箱中，共有 种不同的投法；

（2）某外语组有9人，每人至少会英语和日语中的一门，其中7人会英语，3人会日语，从中选出会英语和日语的各一人，有 种不同的选法.

3.如图所示，用不同的五种颜色分别为 $A$ ， $B$ ， $C$ ， $D$ ， $E$ 五部分着色，相邻部分不能用同一种颜色，但同一种颜色可以反复使用，也可不使用，则复合这些要求的不同着色的方法共有（）

<html><body><table><tr><td rowspan="3">A</td><td colspan="2">B</td></tr><tr><td rowspan="2">C</td><td>D</td></tr><tr><td>E</td></tr></table></body></html>

A.500种 B.520种 C.540种 D.560种

4．为了丰富学生的课余生活，某学校开设了篮球、书法、美术、吉他、舞蹈、击剑共六门活动课程，甲、乙、丙3名同学从中各自任选一门活动课程参加，则这3名学生所选活动课程不全相同的选法有（）

A.120种 B.150种 C.210种 D.216种

3．某城市在中心广场建造一个花圃，花圃分为6个部分，如图所示．现要栽种4种不同颜色的花，每部分栽种一种且相邻部分不能栽种同样颜色的花，则不同的栽种方法有（）.

![](images/a0d1e421b1ba8ba80f3d8391d3f58be0075a722f05d2866eef7b336f0c3b22d4.jpg)

A.80种 B.120种 C.160种 D.240种

4.核糖核酸RNA是存在于生物细胞以及部分病毒、类病毒中的遗传信息载体，参与形成RNA的碱基有4种，分别用 $A$ ，C， $G$ ， $U$ 表示．在一个RNA分子中，各种碱基能够以任意次序出现，假设某一RNA分子由100个碱基组成，则不同的RNA分子的种数为（）

A. $1 0 0 ^ { 4 }$ B.4100 C. $2 ^ { 1 0 0 }$ D.410

5．从-1，0，1，2这四个数中选三个不同的数作为函数 $f ( x ) = a x ^ { 2 } + b x + c$ 的系数，可组成不同的二次函数共有个，其中不同的偶函数共有 个．（用数字作答）

6.（多选题）用 $n$ 种不同的颜色涂图中的矩形 $A , B , C , D$ ，要求相邻的矩形涂色不同，不同的涂色方法总种数记为 $s ( n )$ ，则（）

<html><body><table><tr><td>A</td><td>B</td></tr><tr><td>C</td><td rowspan="3"></td></tr><tr><td>D</td></tr><tr><td></td></tr></table></body></html>

A $s \left( 3 \right) = 1 2$ B. $s ( 4 ) = 3 6$ C. $s \left( 5 \right) = 1 2 0$ D. $s ( 6 ) = 6 0 0$

题型三：两个原理的综合应用

【例1】用4种不同颜色给如图所示的地图上色，要求相邻两块涂不同的颜色，不同的涂色方法共有（）

![](images/c4e1f2ea789427f5674fafe808b323225b763bfee830fe1eb4bfc509a3f9556e.jpg)

A.24种 B.36种 C.48种 D.72种

# 选择性必修第三册

【例2】某学校为落实“双减政策，在每天放学后开设拓展课程供学生自愿选择，开学第一周的安排如下表.小明同学要在这一周内选择编程、书法、足球三门课，不同的选课方案共有（）

<html><body><table><tr><td>周一</td><td>周二</td><td>周三</td><td>周四</td><td>周五</td></tr><tr><td>演讲、绘画、舞蹈、</td><td>编程、绘画、舞蹈、 足球</td><td>编程、书法、舞蹈、 足球</td><td>书法、演讲、舞蹈、 足球</td><td>书法、演讲、舞蹈、 足球</td></tr></table></body></html>

注：每位同学每天最多选一门课，每门课一周内最多选一次.

A.15种 B.10种 C.8种 D.5种【例3】回文联是我国对联中的一种，它是用回文形式写成的对联，既可顺读，也可倒读，不仅意思不变，而且颇具趣味．相传，清代北京城里有一家饭馆叫“天然居”，曾有一副有名的回文联：“客上天然居，居然天上客；人过大佛寺，寺佛大过人."在数学中也有这样一类顺读与倒读都是同一个数的正整数，被称为"回文数”，如22，575，1661等．那么用数字1，2，3，4，5可以组成4位"回文数"的个数为（）

A.25 B.20 C.30 D.36【例4】已知集合 $A = \left\{ 0 , 1 , 2 , 3 , 4 \right\}$ ，且 $a , b , c \in A$ ，用 $a , b , c$ 组成一个三位数，这个三位数满足“十位上的数字比其它两个数位上的数字都大”，则这样的三位数的个数为（）

A.14 B.17 C.20 D.23【例5】为提高学生的身体素质，某校开设了游泳、武术和篮球课程，甲、乙、丙、丁4位同学每人从中任选1门课程参加，则不同的选法共有（）

A.12种 B.64种 C.81种 D.96种

# 【题型专练】

1．某学校举行秋季运动会，酷爱运动的小明同学准备在某七个比赛项目中，选择参加其中四个项目的比赛.根据赛程安排，在这七个比赛项目中，100米赛跑与200米赛跑不能同时参加，且跳高与跳远也不能同时参加.则不同的报名方法数为 （用数字作答）

2．由0、1、2、3、4、5这6个数字可以组成 个没有重复数字的三位偶数.

3.在正方形ABCD的每一个顶点处分别标上1,2,3,4中的某一个数字（可以重复），则顶点 $A , B$ 处的数字都大于顶点 $C , D$ 处的数字的标注方法有（）

A.36种 B.48种 C.24种 D.26种

4．用0.1.2.3.4这五个数字，可以组成没有重复数字的三位数的个数为（）

A.18 B.24 C.30 D.48

5．如图，从左到右共有5个空格.

![](images/c0883f76802ab97221f52db73680a993032c1da690fa8786919d29d8e6d85665.jpg)

(1)向5个空格中分别放入0，1，2，3，4这5个数字，一共可组成多少个不同的五位数的奇数？

(2)用红、黄、蓝这3种颜色给5个空格涂色，要求相邻空格用不同的颜色涂色，一共有多少种涂色方案？

6．某校数学课外活动小组有高一学生10人，高二学生8人，高三学生7人.

(1)选其中1人为总负责人，有多少种不同的选法？

(2)每一年级各选1名组长，有多少种不同的选法？

(3)推选出其中2人去外校参观学习，要求这2人来自不同年级，有多少种不同的选法？

# 【考点分析】

# 考点一：排列的有关概念

$\textcircled{1}$ 定义：一般地，从 $_ n$ 个不同元素中取出 $m ( m { \leq } n )$ 个元素，按照一定的顺序排成一列，叫做从 $_ n$ 个不同的元素中取出 $m$ 个元素的一个排列.$\textcircled{2}$ 相同排列：两个排列相同，当且仅当两个排列的元素相同，且元素的排列顺序也相同.

# 考点二：排列数与排列数公式

$\textcircled{1}$ 排列数：从 $n$ 个不同元素中取出 $m ( m { \leq } n )$ 个元素的所有不同排列的个数叫做从 $n$ 个不同元素中取出 $m$ 个元素的排列数，用符号 $\textit { A } _ { n } ^ { m }$ 表示.  
$\textcircled{2}$ 排列数公式： $A _ { n } ^ { m } = n { \bigl ( } n - 1 { \bigr ) } { \bigl ( } n - 2 { \bigr ) } \cdots { \bigl ( } n - m + 1 { \bigr ) } = { \frac { n ! } { { \bigl ( } n - m { \bigr ) } ! } }$ 特别地， $A _ { n } ^ { n } = n ( n - 1 ) ( n - 2 ) \times \cdots 2 \times 1 = n !$ （m， $n \in N ^ { * }$ 且 $m { \le } n )$ ，规定： $0 : = 1$ ：

【考点解读】$\textcircled{1}$ 关于排列的概念

给出的 $n$ 个元素是互不相同的，且抽取的 $m$ 个元素是没有重复抽取的；排列的定义中包含两个基本内容：一是“取元素”，二是“按照一定顺序排列”．注意在解题时应细心观察：一“抽取”是否“重复”，二是否与顺序有关.$\textcircled{2}$ 排列数公式的特征

1.m个连续自然数之积；2.最大数是 $n$ ，最小的是 $( n - m + 1 )$

题型一：排列的概念

解题思路：排列的核心是“顺序”，有“顺序"就是排列问题，那么如何判断是否有顺序呢？最常用的办法是把得到的结果变换元素的位置，如果结果变了，就是有“顺序”，若结果不变，就是无“顺序”.

# 【精选例题】

【例1】下列问题是排列问题的是（）

A．10个朋友聚会，每两人握手一次，一共握手多少次？  
B．平面上有2022个不同的点，且任意三点不共线，连接任意两点可以构成多少条线段？  
C.集合 $\left\{ a _ { 1 } , a _ { 2 } , a _ { 3 } , \cdots , a _ { n } \right\}$ 的含有三个元素的子集有多少个？  
D．从高三（19）班的54名学生中选出2名学生分别参加校庆晚会的独唱、独舞节目，有多少种选法？

【例2】从集合{3,5,7,9,11}中任取两个元素， $\textcircled{1}$ 相加可得多少个不同的和？ $\textcircled{2}$ 相除可得多少个不同的商？ $\textcircled{3}$ 作为椭圆 $\frac { x ^ { 2 } } { a ^ { 2 } } + \frac { y ^ { 2 } } { b ^ { 2 } } = 1 \left( a > 0 , b > 0 \right)$ 中的 $a , ~ b$ ，可以得到多少个焦点在 $x$ 轴上的椭圆方程？ $\textcircled{4}$ 作为双曲线$\frac { x ^ { 2 } } { a ^ { 2 } } - \frac { y ^ { 2 } } { b ^ { 2 } } = 1 \left( a > 0 , b > 0 \right)$ 中的 $a$ ， $b$ ，可以得到多少个焦点在 $x$ 轴上的双曲线方程？

上面四个问题属于排列问题的是（）

A. $\textcircled{1} \textcircled{2} \textcircled{3} \textcircled{4}$ B. $\textcircled{2} \textcircled{4}$ c.②③ D. $\textcircled{1} \textcircled{4}$ 【例3】（多选题）下列问题中，属于排列问题的有（）

A．从甲、乙、丙三名同学中选出两名分别担任正、副班长，共有多少种不同的选取方法B．从甲、乙、丙三名同学中选出两名同学参加志愿者活动，共有多少种不同的选取方法C．平面上有五个点，任意三点不共线，这五个点最多可确定多少条直线D．从1，2，3，4四个数字中任选两个组成一个两位数，共有多少个不同的两位数

【例4】（多选题）下列问题中，属于排列问题的是（）

A．有10个车站，共有多少种不同的车票  
B．有10个车站，共有多少种不同的票价  
C．平面内有10个点，共可作出多少条不同的有向线段  
D．从10名同学中选出2名分别参加数学和物理竞赛，有多少种选派方法

# 【题型专练】

1．下面问题中，是排列问题的是（）

A．由1，2，3三个数字组成无重复数字的三位数B．从40人中选5人组成篮球队C．从100人中选2人抽样调查 D．从1，2，3，4，5中选2个数组成集合

2．下列问题属于排列问题的是（）

$\textcircled{1}$ 从10个人中选2人分别去种树和扫地；  
$\textcircled{2}$ 从10个人中选2人去扫地；  
$\textcircled{3}$ 从班上30名男生中选出5人组成一个篮球队；  
$\textcircled{4}$ 从数字5，6，7，8中任取两个不同的数作幂运算.

A. $\textcircled{1} \textcircled{4}$ B. $\textcircled{1} \textcircled{2}$ C. $\textcircled{4}$ D.①③④

3．（多选题）从集合{3，5，7，9，11}中任取两个元素，下列四个问题属于排列问题的是（），

A．相加可得多少个不同的和  
B.相除可得多少个不同的商  
C.作为椭圆 $\frac { x ^ { 2 } } { a ^ { 2 } } + \frac { y ^ { 2 } } { b ^ { 2 } } = 1$ 中的 $a , \ b$ ，可以得到多少个焦点为 $x$ 轴上的椭圆方程  
D.作为双曲线 $\frac { x ^ { 2 } } { a ^ { 2 } } - \frac { y ^ { 2 } } { b ^ { 2 } } = 1$ 中的 $a , \ b$ ，可以得到多少个焦点在 $x$ 轴上的双曲线方程

4．（多选题）下列问题是排列问题的是（）

A．求从甲、乙、丙三名同学中选出两名分别参加数学、物理兴趣小组的方法种数B．求从甲、乙、丙三名同学中选出两名参加一项活动的方法种数C.求从 $a$ ， $b$ ， $c$ ， $d$ 中选出3个字母的方法种数D．求从1，2，3，4，5中取出2个数字组成两位数的个数

# 题型二：排列数的计算

【例1】 ${ \bf A } _ { 1 0 } ^ { 1 0 } - 8 9 { \bf A } _ { 8 } ^ { 8 } - 8 { \bf A } _ { 7 } ^ { 7 } =$

【例2】计算： $\frac { 4 \mathrm { A } _ { 8 } ^ { 4 } + 2 \mathrm { A } _ { 8 } ^ { 5 } } { \mathrm { A } _ { 8 } ^ { 8 } - \mathrm { A } _ { 9 } ^ { 5 } } = -$

【例3】阶乘是基斯顿·卡曼（ChristianKramp）于1808年发明的一种运算，正整数 $n$ 的阶乘记为 $n !$ ，它的值为所有小于或等于 $n$ 的正整数的积，即 $n ! = 1 \times 2 \times 3 \times \cdots \times ( n - 1 ) \times n$ ．根据上述材料，以下说法错误的是（）

2！31 n！ A. $4 ! = 2 4$ B. $8 ! = 4 0 3 2 0$ C. $1 2 ! = 1 2 \times 1 1 !$ D.+…·+-1=n！

【例4】对任意正整数 $n$ ，定义 $_ n$ 的双阶乘 $n ! !$ ：当 $n$ 为偶数时， $n ! ! = n \times ( n - 2 ) \times ( n - 4 ) \times \cdots \times 6 \times 4 \times 2$ ；当 $n$ 为奇数时， $n ! ! = n \times ( n - 2 ) \times ( n - 4 ) \times \cdots \times 5 \times 3 \times 1$ ，则下列四个命题中错误的是（）

A. $2 0 9 ! ! \times 2 0 8 ! ! = 2 0 9 !$ B. $2 0 8 ! ! = 2 \times 1 0 4 !$ C．208!!的个位数字为0D．209!!的个位数字为5

【例5】 $\left( n - 1 9 9 8 \right) \left( n - 1 9 9 9 \right) \cdots \left( n - 2 0 2 1 \right) \left( n - 2 0 2 2 \right) \left( n \in N , n > 2 0 2 2 \right)$ 可表示为（）

$\mathrm { A } _ { n - 1 9 9 8 } ^ { 2 4 }$ B $\mathrm { A } _ { n - 1 9 9 8 } ^ { 2 5 }$ $\mathrm { A } _ { n - 2 0 2 2 } ^ { 2 4 }$ D. $\mathrm { A } _ { n - 2 0 2 2 } ^ { 2 5 }$

# 【题型专练】

1．若“”是一种运算符号，并定义： $1 ! = 1 , 2 ! = 2 \times 1 = 2 , 3 ! = 3 \times 2 \times 1 = 6 , \cdots \cdots$ ， 则 $\frac { 1 0 0 ! } { 9 8 ! }$ 的值为（）

$\frac { 5 0 } { 4 9 }$ B.99！ C.9900 D.2！

2.计算： $\frac { { \sf A } _ { 7 } ^ { 7 } } { { \sf A } _ { 3 } ^ { 3 } } =$

A $\mathrm { A } _ { 4 } ^ { 4 }$ B. ${ \sf A } _ { 7 } ^ { 4 }$ C. ${ \mathrm { C } } _ { 7 } ^ { 4 }$ D.A

3.（多选题） $\mathrm { A } _ { 1 5 } ^ { 5 } - 1 5 \mathrm { A } _ { 1 4 } ^ { 4 } + 1 4 \mathrm { A } _ { 1 3 } ^ { 3 } = \mathrm { ~ ( ~ ) ~ }$

A ${ \mathrm { A } } _ { 1 4 } ^ { 4 }$ B. $1 4 \mathrm { A } _ { 1 3 } ^ { 3 }$ C. ${ \mathbb A } _ { 1 3 } ^ { 3 }$ D. 14！ 10

4. $( n - 3 ) ( n - 4 ) \cdots ( n - 9 ) ( n - 1 0 )$ （ $\boldsymbol { n } \in \mathbf { N } ^ { * }$ ， $n > 1 0$ ）可以表示为（）

$\mathrm { A } _ { n - 1 0 } ^ { 8 }$ B. $\ A _ { n - 3 } ^ { 8 }$ C. $\mathbf { A } _ { n - 3 } ^ { 7 }$ D. $\mathbf { C } _ { n - 3 } ^ { 8 }$

# 题型三：解排列数方程和不等式

【例1】若 $\mathbf { A } _ { m } ^ { 3 } = 2 \mathbf { A } _ { m + 1 } ^ { 2 }$ ，则 $m = { \mathrm { ~ ( ~ ) ~ } }$

A.3 B.4 C.5 D.6

【例2】已知自然数 $x$ 满足 $3 A _ { x + 1 } ^ { 3 } = 2 A _ { x + 2 } ^ { 2 } + 6 A _ { x + 1 } ^ { 2 }$ ，则 $x = ~ ( ~ )$

A.2 B.3 C.4 D.5

【例3】（1）解不等式： $3 A _ { x } ^ { 3 } \leq 2 A _ { x + 1 } ^ { 2 } + 6 A _ { x } ^ { 2 }$ （2）解方程： $A _ { 2 x + 1 } ^ { 4 } = 1 4 0 A ^ { 3 } _ { x }$

【例4】不等式 ${ \sf A } _ { 8 } ^ { x } < 6 \times { \sf A } _ { 8 } ^ { x - 2 }$ 的解集为（）

A.[2.8] B.(7,12) $\mathrm { C } . \quad \{ x \vert 7 < x < 1 2 , x \in N \} \ \mathrm { D } . \quad \left\{ 8 \right\}$

# 【题型专练】

1.若 $\mathsf { A } _ { 2 n } ^ { 3 } = 1 0 \mathsf { A } _ { n } ^ { 3 }$ ，则 $n =$ （）

A.7 B.8 C.9 D.10

2.（多选题）已知 $A _ { 3 } ^ { m } - \frac { 1 } { 2 } A _ { 3 } ^ { 2 } + 0 ! = 4$ ，则 $m$ 的可能取值是（）

A.0 B.1 C.2 D.3

3.不等式 $A _ { 8 } ^ { x } < 6 A _ { 8 } ^ { x - 2 }$ 的解集为（）.

A.{2,3,4,5,6,7,8} B.{2,3,4,5,6} C.{8,9,10,11} D.{8

4.（1）解方程： $\mathrm { A } _ { 2 x + 1 } ^ { 4 } = 1 4 0 \mathrm { A } _ { x } ^ { 3 }$ ：

（2）解不等式： $A _ { 9 } ^ { x } > 6 A _ { 9 } ^ { x - 2 }$

5．解不等式： $3 A _ { x + 1 } ^ { 3 } \leq 2 A _ { x + 2 } ^ { 2 } + 6 A _ { x + 1 } ^ { 2 }$

题型四：证明排列数恒等式

【例1】（多选题）下列各式中，等于 $n !$ 的是（）

A $m ! { \mathrm { A } _ { n } ^ { m } }$ B. $\mathsf { A } _ { n + 1 } ^ { n }$ C. $\mathsf { A } _ { \mathfrak { n } } ^ { \mathfrak { n } - 1 }$ D. $n \mathsf { A } _ { n - 1 } ^ { n - 1 }$

【例2】（1）求证： $\left( n + 1 \right) ! - n ! = n \cdot n !$

(2）求证： $\frac { n } { \Big ( n + 1 \Big ) ! } = \frac { 1 } { n ! } - \frac { 1 } { \Big ( n + 1 \Big ) ! }$ （

（3）求和： $\frac { 1 } { 2 ! } + \frac { 2 } { 3 ! } + \frac { 3 } { 4 ! } + \mathrm { L } + \frac { n } { ( n + 1 ) ! }$

【例3】求证：（1） $A _ { n + 1 } ^ { \prime \prime + 1 } - A _ { n } ^ { \prime \prime } = n ^ { 2 } A _ { n - 1 } ^ { \prime \prime - 1 }$

$$
{ \frac { ( n + 1 ) ! } { k ! } } - { \frac { n ! } { ( k - 1 ) ! } } = { \frac { ( n - k + 1 ) \cdot n ! } { k ! } } ( k \leq n ) .
$$

# 【题型专练】

1．（多选题）下列等式正确的是（）

A $\left( n + 1 \right) \mathbf { A } _ { n } ^ { m } = \mathbf { A } _ { n + 1 } ^ { m + 1 }$ $= \mathbf { A } _ { n + 1 } ^ { m + 1 } \qquad \mathbf { B . } \quad \mathbf { A } _ { n } ^ { m - 1 } = \frac { n ! } { \left( n - m - 1 \right) ! } \qquad \mathsf { C . } \quad \frac { n ! } { n \left( n - 1 \right) } = \left( n - 2 \right) ! \qquad \mathsf { D . } \quad \frac { 1 } { n - m } \mathbf { A } _ { n } ^ { m + 1 } = \mathbf { A } _ { n } ^ { m - 1 } ,$

2.证明 ${ \frac { n } { \left( n + 1 \right) ! } } = { \frac { 1 } { n ! } } - { \frac { 1 } { \left( n + 1 \right) ! } }$ ，并利用这一结果化简： $( 1 ) { \frac { 1 } { 2 ! } } + { \frac { 2 } { 3 ! } } + { \frac { 3 } { 4 ! } } + \cdots + { \frac { 9 } { 1 0 ! } } ; ( 2 ) { \frac { 1 } { 2 ! } } + { \frac { 2 } { 3 ! } } + { \frac { 3 } { 4 ! } } + \cdots + { \frac { n } { ( n + 1 ) ! } } .$

【例1】从5本不同的书中选出3本分别送3位同学每人一本，不同的方法总数是（）

A.10 B.60 C.243 D.15【例2】从6名员工中选出3人分别从事教育、培训、管理三项不同的工作，则选派方案共有（）

A.60种 B.80种 C.100种 D.120种【例3】某班新年联欢会原定的5个节目已排成节目单，开演前又增加了两个新节目，如果将这两个节目插入原节目单中，且两个新节目不相邻，那么不同插法的种数为（）

A.6 B.12 C.15 D.30【例4】若一个三位正整数的十位数字比个位数字和百位数字都大，则称这个数为“伞数”，现从1,2,3,4,5这5个数字中任取3个数字，组成没有重复数字的三位数，其中“伞数"共有（）个.

A.60 B.5 C.20 D.

【例5】某诗词大会共设有十场比赛，每场比赛都有一首特别设计的开场诗词.若将《将进酒》《山居秋暝》《望岳》《送杜少府之任蜀州》和另外确定的两首诗词排在后六场，并要求《将进酒》与《望岳》相邻，且《将进酒》排在《望岳》的前面，《山居秋暝》与《送杜少府之任蜀州》不相邻，且均不排在最后，则后六场开场诗词的排法有（）

A.144种 B.48种 C.36种 D.72种【例6】（多选题）甲、乙、丙、丁、戊五人站成一排．（）

A．若甲、乙必须相邻且乙在甲的右边，则不同的排法有24种B．若最左端只能排甲或乙，最右端不能排甲，则不同的排法有42种C．甲、乙不相邻的排法有82种D．甲、乙、丙按从左到右的顺序排列的排法有20种

# 选择性必修第三册

【例7】现有8个人（5男3女）站成一排.

(1)其中甲必须站在排头有多少种不同排法？(2)女生必须排在一起，共有多少种不同的排法？(3)其中甲、乙两人不能排在两端有多少种不同的排法？(4)其中甲在乙的左边有多少种不同的排法？(5)甲、乙不能排在前3位，有多少种不同排法？(6)女生两旁必须有男生，有多少种不同排法？

# 【题型专练】

1．在2022年北京冬奥会和冬残奥会城市志愿者的招募项目中有一个“国际服务项目”，截止到2022年1月25日还有8个名额空缺，需要分配给3个单位，则每个单位至少一个名额且各单位名额互不相同的方法种数是（）

A.14 B.12 C.10 D.8

2．将诗集《诗经》、《唐诗三百首》，戏剧《牡丹亭》，四大名著《红楼梦》、《西游记》、《三国演义》、《水浒传》  
7本书放在一排，下面结论成立的是（）

A.戏剧放在中间的不同放法有7！种 B．诗集相邻的不同放法有6！种C.四大名著互不相邻的不同放法有 $4 ! \times 3 !$ 种D．四大名著不放在两端的不同放法有 $6 \times 4 !$ 种

3．2021年是中国共产党百年华诞．某学校社团将举办庆祝中国共产党成立100周年革命歌曲展演．现从《歌唱祖国》《英雄赞歌》《南泥湾》《没有共产党就没有新中国》4首独唱歌曲和《保卫黄河》《唱支山歌给党听》《我和我的祖国》3首合唱歌曲中共选出4首歌曲安排演出，要求最后一首歌曲必须是合唱，则不同的安排方法共有（）

A.40 B.240 C.120 D.360

4．中国古代中的"礼、乐、射、御、书、数"合称"六艺”．为传承和弘扬中华优秀传统文化，某校国学社团开展“六艺"讲座活动，每艺安排一次讲座，共讲六次，讲座次序要求“礼”在第一次，“数”不在最后，“射"和“御"两次相邻，则"六艺"讲座不同的次序共有（）

A.48种 B.36种 C.24种 D.20种

5．六个人站成一排照相，其中甲乙要相邻的站法种数有（）

A.720 B.120 C.240 D.360

6．甲乙丙丁戊5名同学站成一排参加文艺汇演，若甲不站在两端，丙和丁相邻的不同排列方式有 种.

7．七名学生站成一排，其中甲不站在两端且乙不站在中间的排法共有 种.

8.排一张5个独唱和3个合唱的节目单，如果合唱节目不排两头，且任何两个合唱不相邻，符合条件的排法共有 种.

9．六人按下列要求站一横排，分别有多少种不同的站法？

(1)甲不站两端  
(2)甲、乙必须相邻；  
(3)甲、乙不相邻；  
(4)甲、乙之间间隔两人；

10．快毕业了，7名师生站成一排照相留念，其中老师1人，男生4人，女生2人，在下列情况下，各有多少种不同站法？（每题都要用数字作答）

(1)两名女生必须相邻而站；   
(2)4名男生互不相邻；   
(3)若4名男生身高都不等，按从高到低的顺序站.

# 考点题型方法总结第3讲组合及组合数

# 【考点分析】

考点一：组合及组合数的概念

$\textcircled{1}$ 组合：一般地，从 $n$ 个不同元素中取出 $m ( m { \leq } n )$ 个元素合成一组，叫做从 $n$ 个不同元素中取出 $m$ 个元素的一个组合.  
$\textcircled{2}$ 组合数：从 $n$ 个不同元素中取出 $m ( m { \leq } n )$ 个元素的所有不同 组合的个数，叫做从 $n$ 个不同元素中取出 $m$ 个元素的组合数，用符号 $C _ { n } ^ { m }$ 表示.

考点二：组合数公式及其性质

$$
C _ { n } ^ { m } = \frac { A _ { n } ^ { m } } { A _ { m } ^ { m } } = \frac { n ( n - 1 ) ( n - 2 ) \cdots ( n - m + 1 ) } { m ! } = \frac { n ! } { m ! ( n - m ) ! }
$$

性质：性质1: $C _ { n } ^ { m } = C _ { n } ^ { n - m }$ 性质2： $C _ { n + 1 } ^ { m } = C _ { n } ^ { m } + C _ { n } ^ { m - 1 }$ 规定： $C _ { n } ^ { 0 } = 1$

# 【考点解读】

1.组合的概念

$\textcircled{1}$ 如果两个组合中的元素完全相同，不管它们是顺序如何都是相同的组合，组合的定义中包含两个基本内容：一是“取出元素”；二是“合成一组”，“合成一组”即表示与顺序无关.  
$\textcircled{2}$ 若两个组合中的元素不完全相同(即使只有一个元素不同)，则是不同的组合，例如从a、b、 $c$ 三个不同的元素中取出两个元素的所有组合有3个，它们分别是ab、ac、bc.ba、ab是相同的组合，而ab、 $^ { a c }$ 是不同的组合.$\textcircled{3}$ 组合与排列问题的共同点是都要“从 $_ n$ 个不同元素中，任取 $m$ 个元素”；不同点是前者是“不管顺序合成一组”，而后都者要“按照一定顺序排成一列”.

2.区分排列问题、组合问题

前者是从 $n$ 个不同元素中选取 $m$ 个不同元素后，还要按照一定的顺序排成一列，而后者只要从 $n$ 个不同元素中选取 $m$ 个不同的元素合成一组，所以区分某一问题是排列还是组合，关键看选出的元素与顺序是否有关，若交换某两个元素的位置对结果产生影响，则是排列问题；若交换任意两个元素的位置对结果都没有影响，则是组合问题.

3.组合数、组合数公式及组合数性质

$\textcircled{1}$ 组合数符号表示为 $C _ { n } ^ { m }$ ，如从4个不同元素取出3个元素的组合数为 $C \hat { \mathfrak { z } }$   
$\textcircled{2}$ “组合”与“组合数"是两个不同的概念，组合是指“从 $n$ 个不同的元素中，任取 $m ( m { \leq } n )$ 个元素合成一组”，它不是一个数，而是具体的一件事；组合数是指从 $_ n$ 个不同元素中取出 $m$ 个元素的所有组合的个数，它是一个数；$\textcircled{3}$ 组合数公式是在 $m$ ， $n \in N ^ { \star }$ ，且 $m { \le } n$ 时成立；  
$\textcircled{4}$ 当 $m$ $n$ 数值较大或含有字母的组合数式进行变形论证时，利用公式 $C _ { n } ^ { m } = \frac { n ! } { m ! ( n - m ) ! }$ 解题较方便；$\textcircled{5} C _ { n } ^ { 0 } = 1$ 是一种规定，不能用组合数定义解释；  
$\textcircled{6}$ 计算组合数时，特别是 $m$ 较大时，注意利用公式 $C _ { n } ^ { m } = C _ { n } ^ { n - m }$ 转化.

# 选择性必修第三册

题型一：组合的概念

【例1】（多选题）下面问题中，是组合问题的是（）

A．由1，2，3三个数字组成无重复数字的三位数  
B．从40人中选5人组成篮球队  
C．从100人中选2人抽样调查  
D．从1，2，3，4，5中选5个数组成集合

【例2】下列问题中，组合问题的个数是（）

$\textcircled{1}$ 从全班50人中选出5人组成班委会；  
$\textcircled{2}$ 从全班50人中选出5人分别担任班长、副班长、团支部书记、学习委员、生活委员；  
$\textcircled{3}$ 从1，2，3，..，9中任取出两个数求积；  
$\textcircled{4}$ 从1，2，3，..，9中任取出两个数求差或商.

A.1 B.2 C.3 D.4【例3】（多选题）给出下列问题，属于组合问题的有（）

A．从甲、乙、丙3名同学中选出2名分别去参加两个乡镇的社会调查，有多少种不同的选法B．有4张电影票，要在7人中确定4人去观看，有多少种不同的选法C．某人射击8枪，击中4枪，且命中的4枪均为2枪连中，则不同的结果有多少种D．从2，3，5，7，11中任选两个数相乘，可以得到多少个不同的积

# 【题型专练】

1．以下四个问题，属于组合问题的是（）

A．从3个不同的小球中，取出2个排成一列B．老师在排座次时将甲、乙两位同学安排为同桌C.在电视节目中，主持人从100位幸运观众中选出2名幸运之星D．从13位司机中任选出两位开同一辆车往返甲、乙两地

2.（多选题）下列问题中，属于组合问题的是（）

A．10支战队以单循环进行比赛（每两队比赛一次），共进行多少次比赛B．10支战队以单循环进行比赛，这次比赛的冠、亚军获得者有多少种可能C．从10名员工中选出3名参加同一种的娱乐活动，有多少种选派方法D．从10名员工中选出3名分别参加不同的娱乐活动，有多少种选派方法

3．（多选题）下列问题中是组合问题的有（）.

A．某铁路线上有4个车站，则这条铁路线上需准备多少种车票B．从7本不同的书中取出5本给某同学C．3个人去做5种不同的工作，每人做一种，有多少种分工方法D．把3本相同的书分给5个学生，每人最多得一本，有多少种分配方法

4．（多选题）给出下列问题，其中是组合问题的是（）

A．由1，2，3，4构成的含3个元素的集合 B.从7名班委中选2人担任班长和团支书C．从数学组的10名教师中选3人去参加市里新课程研讨会D．由1，2，3，4组成无重复数字的两位数

# 题型二：组合数的计算

【例1】 $C _ { 5 } ^ { 2 } + C _ { 6 } ^ { 3 } =$ （）

A.25 B.30 C.35 D.40

【例2】已知 $n$ ， $m$ 为正整数，且 $n \geq m$ ，则在下列各式中错误的是（）

A. ${ \sf A } _ { 6 } ^ { 3 } = 1 2 0$ ； B. ${ \bf A } _ { 1 2 } ^ { 7 } = { \bf C } _ { 1 2 } ^ { 7 } \cdot { \bf A } _ { 7 } ^ { 7 }$ ；c. $\mathbf { C } _ { n } ^ { m } + \mathbf { C } _ { n + 1 } ^ { m } = \mathbf { C } _ { n + 1 } ^ { m + 1 }$ ；D. $\mathbf { C } _ { n } ^ { m } = \mathbf { C } _ { n } ^ { n - m }$

【例3】 $\mathbf { C } _ { 7 } ^ { 1 } + \mathbf { C } _ { 7 } ^ { 3 } + \mathbf { C } _ { 7 } ^ { 5 } + \mathbf { C } _ { 7 } ^ { 7 } =$ 1

【例4】设 $n$ 为正整数，则关于 $\mathbf { C } _ { 2 n - 3 } ^ { n - 1 } + \mathbf { C } _ { n + 1 } ^ { 2 n - 3 }$ ，下列说法正确的是（）

A．该代数式的值唯一确定 B．该代数式的值有两种情况 C．该代数式的值有三种情况 D．该代数式的值有无数种情况

# 【题型专练】

1.求值： $7 C _ { 6 } ^ { 3 } - 4 C _ { 7 } ^ { 4 } =$ $2 . \mathrm { A } _ { 7 } ^ { 3 } - 1 0 \mathrm { C } _ { 9 } ^ { 4 } = \_$ （用数字作答）

3. $\mathrm { C } _ { 3 } ^ { 3 } + \mathrm { C } _ { 3 } ^ { 2 } + \mathrm { C } _ { 4 } ^ { 2 } =$ （）

A ${ \bf C } _ { 6 } ^ { 3 }$ B. ${ C } _ { 5 } ^ { 3 }$ C. ${ \mathrm { C } } _ { 6 } ^ { 2 }$ D.C

选择性必修第三册

题型三：解组合数方程和不等式

【例1】已知 $\frac { C _ { 4 } ^ { m } } { 2 } + \frac { C _ { 5 } ^ { m } } { 5 } = \frac { C _ { 6 } ^ { m } } { 3 }$ 则 $m =$

【例2】若 $x { \bf C } _ { x } ^ { x - 1 } + { \bf A } _ { x } ^ { 3 } = 4 { \bf C } _ { x + 1 } ^ { 3 }$ ，则 $x$ 的值为

【例3】若 $3 \mathrm { C } _ { 2 n } ^ { 3 } = 5 \mathrm { A } _ { n } ^ { 3 }$ ，则正整数 $n =$

【例4】已知 $\left\{ \begin{array} { l l } { \displaystyle \mathrm { C } _ { n } ^ { x } = \mathrm { C } _ { n } ^ { 2 x } , } \\ { \displaystyle \mathrm { C } _ { n } ^ { x + 1 } = \frac { 1 1 } { 3 } \mathrm { C } _ { n } ^ { x - 1 } , } \end{array} \right.$ 则 $x =$

# 【题型专练】

1.已知 $C _ { n } ^ { 4 }$ ， $C _ { n } ^ { 5 }$ ， $C _ { n } ^ { 6 }$ 成等差数列，则 $C _ { \eta } ^ { 1 2 } =$

2.已知 $\frac { C _ { n - 1 } ^ { 5 } + C _ { n - 3 } ^ { 3 } } { C _ { n - 3 } ^ { 3 } } = \frac { 1 9 } { 5 }$ 则 $n$ 的值是（）

A.9 B.7 C.9或-6 D.8

3.（1）若 $\mathrm { C } _ { \eta } ^ { 4 } > \mathrm { C } _ { n } ^ { 6 }$ ，则 $_ n$ 的取值集合是 (2) $\mathrm { C } _ { 2 } ^ { 2 } + \mathrm { C } _ { 3 } ^ { 2 } + \mathrm { C } _ { 4 } ^ { 2 } + \cdots + \mathrm { C } _ { 1 0 } ^ { 2 } =$

题型四：组合数的性质及恒等式

【例1】已知 $\boldsymbol { x } \in \mathbf { N }$ ，则方程 $\mathrm { C } _ { 5 } ^ { x } = \mathrm { C } _ { 5 } ^ { 2 x - 1 }$ 的解是

【例2】已知 $C _ { m } ^ { 3 } = C _ { m } ^ { 4 }$ ，则 $C _ { 8 } ^ { m - 2 } + C _ { 8 } ^ { m - 1 } + C _ { 9 } ^ { m } =$

【例3】下列等式不正确的是（）

A $C _ { n } ^ { m } = \frac { m + 1 } { n + 1 } C _ { n + 1 } ^ { m }$ B. $A _ { n + 1 } ^ { m + 1 } - A _ { n } ^ { m } = n ^ { 2 } A _ { n - 1 } ^ { m - 1 }$ C. $A _ { n } ^ { m } = n A _ { n - 1 } ^ { m - 1 }$ D. $n C _ { n } ^ { k } = ( k + 1 ) C _ { n } ^ { k + 1 } + k C _ { n } ^ { k }$

【例4】（多选题）对于 $m \leq n$ ， $m$ ， $n \in \mathrm { N } ^ { * }$ ，关于下列排列组合数，结论正确的是（）

$$
C _ { n } ^ { m } = \frac { m : } { \eta 1 | { \cal J } _ { n - m } ) | } \mathrm { ~ { ~  ~ { ~ \cal ~ B ~ . ~ } ~ } ~ } \ : \ : C _ { n } ^ { m } = C _ { n } ^ { n - m } { { ~ \cal ~ C } _ { \cdot } ^ { m } { } ~ } \ : \ : \ : C _ { n + 1 } ^ { m } = C _ { n } ^ { m - 1 } + C _ { n } ^ { m } { { ~ \mathrm { ~ ~  ~ { ~ \cal ~ D ~ . ~ } ~ } ~ } } \ : \ : \ : \ : \mathrm { ~ { ~  ~ { ~ \cal ~ D ~ . ~ } ~ } ~ } \ : \ : \ : \ : \ : \ : \ : \ : \ : \mathrm { ~ { ~ \cal ~ D ~ . ~ } ~ } \ : \ : C _ { n } ^ { m } = m { \cal A } _ { n - 1 } ^ { m - 1 } .
$$

【例5】（多选题） $\mathrm { C } _ { 9 8 } ^ { 9 6 } + 2 \mathrm { C } _ { 9 8 } ^ { 9 5 } + \mathrm { C } _ { 9 8 } ^ { 9 4 } =$ （）

$\mathbb { C } _ { 9 9 } ^ { 9 7 }$ B. $C _ { 9 9 } ^ { 2 }$ c. $C _ { 1 0 0 } ^ { 9 6 }$ D. $\mathrm { C } _ { 1 0 0 } ^ { + }$

【例6】计算： $\mathrm { C } _ { 5 } ^ { 2 } + \mathrm { C } _ { 6 } ^ { 3 } + \mathrm { C } _ { 7 } ^ { 4 } + \mathrm { C } _ { 8 } ^ { 5 } + \mathrm { C } _ { 9 } ^ { 6 } + \mathrm { C } _ { 1 0 } ^ { 7 } + \mathrm { C } _ { 1 1 } ^ { 8 }$

# 【题型专练】

1.已知 ${ \bf C } _ { 1 3 } ^ { 2 x - 1 } = { \bf C } _ { 1 3 } ^ { x + 2 }$ ，则 $x$ 的值为（）

A.3 B.3或4 C.4 D.4或5

2.已知 $C _ { 1 6 } ^ { x ^ { 2 } - x } = C _ { 1 6 } ^ { 5 x - 5 }$ $( x \in Z )$ ，则 $x =$

3．（多选题）下列四个关系式中，一定成立的是（）

A $\mathbf { A } _ { n - 1 } ^ { m - 1 } = { \frac { { \big ( } n - 1 { \big ) } ! } { { \big ( } m - n { \big ) } ! } }$ B. $\mathbf { A } _ { n } ^ { m } = n \mathbf { A } _ { n - 1 } ^ { m - 1 }$ C. $3 C _ { 8 } ^ { 3 } - 2 C _ { 5 } ^ { 2 } = 1 4 8$ D. $\mathrm { C } _ { 4 } ^ { 3 } + \mathrm { C } _ { 5 } ^ { 3 } + \mathrm { C } _ { 6 } ^ { 3 } + \cdots + \mathrm { C } _ { 1 0 } ^ { 3 } = 3 2 8$

4.（多选题）下列有关排列数、组合数计算正确的有（）

A. $C _ { 5 } ^ { 3 } = 5 \times 4 \times 3 = 6 0$   
B.从2,3,5,7中任取两个数相乘可得 $\mathrm { C } _ { 4 } ^ { 2 }$ 个积  
C. $\mathrm { C } _ { 3 } ^ { 2 } + \mathrm { C } _ { 4 } ^ { 2 } + \mathrm { C } _ { 5 } ^ { 2 } + \cdots + \mathrm { C } _ { 1 0 0 } ^ { 2 } = \mathrm { C } _ { 1 0 1 } ^ { 3 }$ $\frac { n \big ( n + 1 \big ) \big ( n + 2 \big ) \cdots \big ( n + 1 0 0 \big ) } { 1 0 0 ! } = 1 0 1 C _ { n + 1 0 0 } ^ { 1 0 1 }$

5.（多选题）对 $\forall n \geq m$ 且 $m , n \in \mathbf { N } ^ { * }$ ，下列等式一定恒成立的是（）.

A $\mathbf { C } _ { n } ^ { m } = \mathbf { C } _ { n } ^ { n - m }$ B. $\mathbf { C } _ { n } ^ { m } = \frac { \mathbf { A } _ { n } ^ { m } } { \mathbf { A } _ { m } ^ { m } }$ C. $\mathbf { C } _ { n + 1 } ^ { m } = \mathbf { C } _ { n } ^ { m } + \mathbf { C } _ { n } ^ { m - 1 }$ D. $\boldsymbol { \mathrm { A } } _ { n + 1 } ^ { m } = \boldsymbol { \mathrm { A } } _ { n } ^ { m } + \boldsymbol { \mathrm { A } } _ { n } ^ { n - 1 }$

6.（多选题）下列有关排列数、组合数的计算，正确的是（）

$\mathbb { A } _ { n } ^ { m } = \frac { m ! } { n ! }$ $\left( n + 2 \right) \left( n + 1 \right) { \bf A } _ { n } ^ { m } = { \bf A } _ { n + 2 } ^ { m + 2 } \quad \mathrm { ~ C . ~ } ~ \mathrm { C } _ { 3 } ^ { 2 } + \mathrm { C } _ { 4 } ^ { 2 } + \mathrm { C } _ { 5 } ^ { 2 } + \dots + \mathrm { C } _ { 1 0 0 } ^ { 2 } = \mathrm { C } _ { 1 0 1 } ^ { 3 } \quad \mathrm { ~ D . ~ } ~ \mathrm { C } _ { 2 } ^ { m + 2 } = \mathrm { C } _ { 1 0 1 } ^ { 2 }$ $\mathbf { C } _ { 2 n - 1 } ^ { n - 2 } + \mathbf { C } _ { n + 1 } ^ { 2 n - 1 }$

7.计算： $\mathsf C _ { 1 3 + n } ^ { 3 n } + \mathsf C _ { 1 2 + n } ^ { 3 n - 1 } + \mathsf C _ { 1 1 + n } ^ { 3 n - 2 } + \cdots + \mathsf C _ { 2 n } ^ { 1 7 - n } =$

# 选择性必修第三册

题型五：组合的简单应用

【例1】如图，某城市的街区由12个全等的矩形组成（实线表示马路），CD段马路由于正在维修，暂时不通，则从 $A$ 到 $B$ 的最短路径有（）

![](images/922ba4ae40ca8d0c1f2f6e1592518328d3443560fc892d41dc62f2b5c82e3cca.jpg)

A.20条 B.21条 C.22条 D.23条

【例2】绿水青山就是金山银山，浙江省对"五水共治"工作落实很到位，效果非常好．现从含有甲的5位志愿者中选出4位到江西，湖北和安徽三个省市宣传，每个省市至少一个志愿者．若甲不去安徽，其余志愿者没有条件限制，共有多少种不同的安排方法（）

A.228 B.132 C.180 D.96

【例3】新课程改革后，普通高校招生方案规定：每位考生从物理、化学、生物、地理、政治、历史六门学科中随机选三门参加考试，某省份规定物理或历史至少选一门，那么该省份每位考生的选法共有（）

A.14种 B.15种 C.16种 D.17种

【例4】8名医生去甲、乙、丙三个单位做核酸检测，甲、乙两个单位各需三名医生，丙需两名医生，其中医生$a$ 不能去甲医院，则不同的选派方式共有（）

A.280种 B.350种 C.70种 D.80种

【例5】平面上，四条平行直线与另外五条平行直线互相垂直，则它的矩形共有个（结果用数值表示）.

【例6】将某商场某区域的行走路线图抽象为一个 $2 \times 2 \times 3$ 的长方体框架（如图)，小红欲从 $A$ 处行走至 $B$ 处，则小红行走路程最近的路线共有 ·（结果用数字作答）

![](images/f38295d83944f781f485f7df73b0ad8f1cec5bc623e46f3d113e1c197cba647d.jpg)

【例7】我国古代典籍《周易》用“卦”描述万物的变化.每一“重卦”由从下到上排列的6个爻组成，爻分为阳爻"和阴爻“”，如图就是一重卦.如果某重卦中恰有3个阴爻，则该重卦可以有 种.（用数字作答)

![](images/20fcd78733fbdeaf39e712b679c0a713528248de0f28dd5c1f4b596843e5b631.jpg)

【例8】2022年4月，新型冠状病毒疫情牵动着全国人民的心，某市根据上级要求，在本市某人民医院要选出护理外科、心理治疗方面的专家4人与省专家组一起赶赴上海参加救助工作，该医院现有3名护理专家 $A _ { 1 }$ ， $A _ { 2 }$ ，$A _ { 3 }$ ，5名外科专家 $B _ { \imath }$ ， $B _ { 2 }$ ， $B _ { 3 }$ ， $B _ { 4 }$ ， $B _ { 5 }$ ，2名心理治疗专家 $C _ { \parallel }$ ， $C _ { 2 }$ ：

(1)求4人中有1位外科专家，1位心理治疗师的选法有多少种？

(2)求至少含有2位外科专家，且外科专家 $B _ { \imath }$ 和护理专家 $A _ { \mathfrak { l } }$ 不能同时被选的选法有多少种？

# 选择性必修第三册

# 【题型专练】

1.教育部于2022年开展全国高校书记校长访企拓岗促就业专项行动，某市4所高校的校长计划拜访当地的甲、乙两家企业，若每名校长拜访1家企业，每家企业至少接待1名校长，则不同的安排方法共有（）

A.8种 B.10种 C.14种 D.20种

2.为了宣传2022年北京冬奥会和冬残奥会，某学校决定派小明和小李等5名志愿者将两个吉祥物“冰墩墩"和"雪容融"安装在学校的体育广场，每人参与且只参与一个吉祥物的安装，每个吉祥物都至少由两名志愿者安装，若小明和小李必须安装不同的吉祥物，则不同的分配方案种数为（）

A.8 B.10 C.12 D.14

3．北京2022年冬奥会吉祥物“冰墩墩"和冬残奥会吉祥物“雪容融”一亮相，好评不断，这是一次中国文化与奥林匹克精神的完美结合，是一次现代设计理念的传承与突破．为了宣传2022年北京冬奥会和冬残奥会，某学校决定派小明和小李等5名志愿者将两个吉祥物安装在学校的体育广场，若小明和小李必须安装不同的吉祥物，且每个吉祥物都至少由两名志愿者安装，则不同的安装方案种数为（）

A.8 B.10 C.12 D.14

4.（多选题）某学生想在物理、化学、生物、政治、历史、地理、技术这七门课程中选三门作为选考科目，下列说法正确的是（）

A．若任意选择三门课程，则选法种数为35  
B．若物理和化学至少选一门，则选法种数为30  
C．若物理和历史不能同时选，则选法种数为30  
D．若物理和化学至少选一门，且物理和历史不能同时选，则选法种数为20

5．从1，2，...，10这十个数中取出四个数，使它们的和为奇数，共有_ 种取法（用数字作答）.

6.8名世界网球顶级选手在上海大师赛上分成两组，每组各4人，分别进行单循环赛，每组决出前两名，再由每组的第一名与另一组的第二名进行淘汰赛，获胜者角逐冠、亚军，败者角逐第三、四名，则该大师赛共有场比赛.

7．某龙舟队有9名队员，其中3人只会划左舷，4人只会划右舷，2人既会划左舷又会划右舷，现要选派划左舷的3人、右舷的3人共6人去参加比赛，则不同的选派方法共有

8.从4男2女共6名学生中选出1人吃原味薯片，2人吃黄瓜味薯片，剩下3人吃番茄味薯片，共有种选法；如果男生不吃原味薯片，共有 种选法：（用数字作答）

9．有4个不同的球，4个不同的盒子，把球全部放入盒内.

（1）共有多少种放法？（2）恰有一个盒子不放球，有多少种放法？（3）恰有两个盒不放球，有多少种放法？

10.如图，在某城市中， $M , N$ 两地之间有整齐的 $6 \times 6$ 方格形道路网，其中A是道路网中的一点.今在道路网 $M , N$ 处的甲、乙两人分别要到 $N , M$ 处，其中甲每步只能向右走或者向上走，乙每步只能向下或者向左走.

![](images/423c3419ebf478840d9d81a208d9d440ea05265b4b1d9065a86f4403a5a96c0b.jpg)

(1）求甲从 $M$ 到达 $N$ 处的走法总数；  
(2)求甲乙两人在A相遇的方法数.

# 考点题型方法总结第4讲排列组合常见题型总结分析

题型一：特殊元素与特殊位置优待法

解题思路：对于有附加条件的排列组合问题，一般采用：先考虑满足特殊的元素和位置，再考虑其它元素和位置。

# 【精选例题】

【例1】从6名志愿者中选出4人分别从事翻译、导游、导购、保洁四项不同的工作，若其中甲、乙两名志愿者都不能从事翻译工作，则不同的选派方案共有（）

（A）280种 （B）240种 （C）180种 （D）96种【例2】某城市的汽车牌照号码由2个英文字母后接4个数字组成，其中4个数字互不相同的牌照号码共有（）个

A. $A _ { 2 6 } ^ { 2 } 1 0 ^ { 4 }$ （204 B.AA C.(C2）²10 D.(C2）²A 【例3】将甲、乙、丙等六位同学排成一排，且甲、乙在丙的两侧，则不同的排法有 种.

【例4】用0、1、2、3、4五个数字：

（1）可组成多少个五位数；  
(2)可组成多少个无重复数字的五位数；  
(3)可组成多少个无重复数字的且是3的倍数的三位数；  
(4)可组成多少个无重复数字的五位奇数.

# 【题型专练】

1．某校从8名教师中选派4名教师到4个边远地区支教（每地1人），要求甲、乙不同去，甲、丙只能同去或同不去，则不同的选派方案有_ 种.

2.某化工厂生产中需依次投放2种化工原料，现已知有5种原料可用，但甲、乙两种原料不能同时使用，且依次投料时，若使用甲原料，则甲必须先投放，则不同的投放方案有（）.

A.10种 B.12种 C.15种 D.16种

3．4张卡片的正、反面分别写有数字1，2；1，3；4，5；6，7．将这4张卡片排成一排，可构成不同的四位数的个数为（）

A.288 B.336 C.368 D.412

4．用0，2，4，5，6，8组成无重复数字的四位数，则这样的四位数中偶数共有（）

A.120个 B.192个 C.252个 D.300个

题型二：分类讨论思想

解题思路：遇到情况比较复杂，我们可以通过分类讨论，分出几种情况，再用分类加法原理进行计算【精选例题】

【例1】（2023全国卷乙卷真题）现有5名志愿者报名参加公益活动，在某一星期的星期六、星期日两天，每天从这5人中安排2人参加公益活动，则恰有1人在这两天都参加的不同安排方式共有（）

A.120 B.60 C.30 D.20

【例2】（2023全国卷甲卷真题）某学校开设了4门体育类选修课和4门艺术类选修课，学生需从这8门课中选修2门或3门课，并且每类选修课至少选修1门，则不同的选课方案共有 种（用数字作答）.

【例3】在8张奖券中有一、二、三等奖各1张，其余5张无奖，将这8张奖券分配给4个人，每人2张，不同的获奖情况数（）

A.60 B.40 C.30 D.80

【例4】中国古代十进制的算筹计数法，在数学史上是一个伟大的创造，算筹实际上是一根根同长短的小木棍.如图，是利用算筹表示数1\~9的一种方法.例如：3可以表示为“=”，26可以表示为“ ${ = } \bot$ ”现有6根算筹，据此表示方法，若算筹不能剩余，则可以用1\~9这9个数字表示两位数的个数为

123456789

【例5】将1，2，3填入 $3 \times 3$ 的方格中，要求每行、每列都没有重复数字，下面是一种填法，则不同的填写方法共有（）

1 2 3   
3 1 2   
2 3 1

A.6种 B.12种 C.24种 D.48种

# 选择性必修第三册

# 【题型专练】

1.6名同学到甲、乙、丙三个场馆做志愿者，每名同学只去1个场馆，甲场馆安排1名，乙场馆安排2名，丙场馆安排3名，则不同的安排方法共有（）

A.120种 B.90种 C.60种 D.30种

2.某公司安排甲乙丙等7人完成7天的值班任务，每人负责一天，已知甲不安排在第一天，乙不安排在第二天，甲和丙在相邻两天，则不同的安排方式有_种.

# 题型三：插空法（不相邻问题）

解题思路：对于某几个元素不相邻的排列问题，可先将其他元素排好，再将不相邻元素在已排好的元素之间及两端空隙中插入即可

【例1】黄金分割最早见于古希腊和古埃及.黄金分割又称黄金率、中外比，即把一条线段分成长短不等的a，b两段，使得长线段 $a$ 与原线段 $a + b$ 的比等于短线段 $^ { b }$ 与长线段 $a$ 的比，即 $a : \left( a + b \right) = b : a$ ，其比值约为0.618339...小王酷爱数学，他选了其中的6，1，8，3，3，9这六个数字组成了手机开机密码，如果两个3不相邻，则小王可以设置的不同密码个数为（）

A.180 B.210 C.240 D.360【例2】（多选题）把5件不同产品 $A$ ， $B$ ，C， $D$ ， $E$ 摆成一排，则（）

A. $A$ 与 $B$ 相邻有48种摆法 B. $A$ 与 $C$ 相邻有48种摆法C. $A$ ， $B$ 相邻又 $A$ ， $C$ 相邻，有12种摆法 D. $A$ 与 $B$ 相邻，且 $A$ 与 $C$ 不相邻有24种摆法【例3】有5本不同的教科书，其中语文书2本，数学书2本，物理书1本．若将其并排摆放在书架的同一层上，则同一科目书都不相邻的放法种数是（）

A.12 B.48 C.72 D.96

# 【题型专练】

1．有互不相同的5盆菊花，其中2盆为白色，2盆为黄色，1盆为红色，现要摆成一排，要求红色菊花摆放在正中间，白色菊花不相邻，黄色菊花也不相邻，则共有摆放方法（）

A.120种 B.32种 C.24种 D.16种

2．现有2名学生代表，2名教师代表和3名家长代表合影，则同类代表互不相邻的排法共有（）种.

A.552 B.864 C.912 D.1008

3.某种产品的加工需要经过 $A , B , C , D , E , 5$ 道工序，如果工序 $C , D$ 必须不能相邻，那么有_ 种加工顺序（数字作答）

题型四：捆绑法（相邻问题）

解题思路：对于某几个元素相邻的排列问题，可先将相邻的元素捆绑，再将它与其它元素在一起排列，注意捆绑部分的内部顺序。

【例1】2023年杭州亚运会期间，甲、乙、丙3名运动员与5名志愿者站成一排拍照留念，若甲与乙相邻、丙不排在两端，则不同的排法种数有（）

A.1120 B.7200 C.8640 D.14400

【例2】有甲、乙、丙、丁、戊5名同学站成一排参加文艺汇演，若甲不站在两端，丙和丁相邻，则不同排列方式共有（）

A.12种 B.24种 C.36种 D.48种

【例3】（多选题）3个人坐在一排5个座位上，则下列说法正确的是（）

A．共有60种不同的坐法 B．空位不相邻的坐法有72种C.空位相邻的坐法有24种 D．两端不是空位的坐法有27种

【例4】中国古代儒家提出的"六艺"指：礼、乐、射、御、书、数.某校国学社团预在周六开展“六艺"课程讲座活动，周六这天准备排课六节，每艺一节，排课有如下要求：“礼”与“乐"不能相邻，“射”和"御"要相邻，则针对“六艺”课程讲座活动的不同排课顺序共有（）

A.18种 B.36种 C.72种 D.144种

【例5】某办公楼前有7个连成一排的车位，现有三辆不同型号的车辆停放，恰有两辆车停放在相邻车位的方法有 种.

# 选择性必修第三册

# 【题型专练】

1.把5件不同产品摆成一排，若产品 $A$ 与产品 $B$ 相邻，且产品 $A$ 与产品 $C$ 不相邻，则不同的摆法有 种.

2.甲、乙、丙等七人相约到电影院看电影《长津湖》，恰好买到了七张连号的电影票，若甲、乙两人必须相邻，且丙坐在七人的正中间，则不同的坐法的种数为（）

A.240 B.192 C.96 D.48

3．2名老师和3名学生站成一排照相，则3名学生中有且仅有2人相邻的站法有 种.

4.（多选题）为弘扬我国古代的“六艺文化”，某夏令营主办单位计划利用暑期开设“礼”、“乐”、“射"御"书"数”六门体验课程，每周一门，连续开设六周，则下列说法正确的是（）

A．某学生从中选2门课程学习，共有15种选法  
B．课程"乐"射"排在相邻的两周，共有240种排法  
C.课程“御"书数"排在不相邻的三周，共有144种排法  
D．课程“礼"不排在第一周，课程“数”不排在最后一周，共有480种排法

5．如图,某手链由10颗较小的珠子(每颗珠子相同)和11颗较大的珠子(每颗珠子均不相同)串成,若10颗小珠子必须相邻,大珠子的位置任意,则该手链不同的串法有（）

![](images/ec005f0d82d7b48893204be75bc57edb6666fcafce896a0f57192ae262c11066.jpg)

A $\boldsymbol { A } _ { 1 1 } ^ { 1 1 }$ 种 B.种 C. $A _ { 1 2 } ^ { 1 2 }$ 种 D $\frac { A _ { 1 2 } ^ { 1 2 } } { 2 }$ 种

6．中国书法一般分为篆书、隶书、行书、楷书和草书这5种字体，其中篆书分大篆和小篆，隶书分古隶和汉隶，草书分章草、今草和狂草，行书分行草和行楷，楷书分魏碑和唐楷.为了弘扬传统文化，某书法协会采用楷书、隶书和草书3种字体书写6个福字，其中隶书字体的福字分别用古隶和汉隶书写，草书字体的福字分别用章草、今草和狂草书写，楷书字体的福字用唐楷书写.将这6个福字排成一排，要求相同类型字体的福字相邻，则不同的排法种数为 种.

选择性必修第三册

题型五：平均分组问题除法策略解决此类问题，平均分了 $n$ 组，就要除以组数的排序 $\boldsymbol { A } _ { n } ^ { n }$

# 【精选例题】

【例1】已知有6本不同的书.分成三堆，每堆2本，有 种不同的分堆方法？

【例2】12个篮球队中有3个强队，将这12个队任意分成3个组（每组4个队），则3个强队恰好被分在同一组的概率为

A. B. 35 C. 1-4 D. 1-3

【例3】6本不同的书，分成三份，1份4本，另外两份每份1本，共有 种不同的分配方式

# 【跟踪训练】

1.奥运会足球预选赛亚洲区决赛（俗称九强赛)，中国队和韩国队是其中的两支球队，现要将9支球队随机平均分成3组进行比赛，则中国队与韩国队分在同一组的概率是（）.

A1 B c D

2.6本不同的书，分成三堆，一堆1本，一堆2本，一堆3本，有 种分法

3.“全员检测，阻断清零"的新冠防疫政策，使得我国成为全球最安全的国家.现某处需要三组全民核酸检测人员，其中有3名医生和6名社会志愿者组成，每组人员由1名医生和2名志愿者组成.根据需要，志愿者甲与乙要分配在同一组，则这9名检测人员分组方法种数为

题型六：分配问题先分组再分配

遇到分配问题，切记一定要先分组，再去分配，这样就比较容易理解

# 【精选例题】

【例1】为了响应全国创文明城活动，某单位计划安排五名员工分别去三个小区 $A$ ， $B$ ， $C$ 参加志愿者服务，每个员工只去一个小区，每个小区至少安排1人，员工甲不去小区 $A$ ，则不同的安排方法种数共有（）种

A.100 B.110 C.140 D.260

【例2】某市新冠疫情封闭管理期间，为了更好的保障社区居民的日常生活，选派6名志愿者到甲、乙、丙三个社区进行服务，每人只能去一个地方，每地至少派一人，则不同的选派方案共有（）

A.540种 B.180种 C.360种 D.630种

# 选择性必修第三册

【例3】6名志愿者要到A，B， $C$ 三个社区进行志愿服务，每个志愿者只去一个社区，每个社区至少安排1名志愿者，若要2名志愿者去A社区，则不同的安排方法共有（）

A.105种 B.144种 C.150种 D.210种

【例4】某班9名同学参加植树活动，若将9名同学分成挖土、植树、浇水3个小组，每组3人，则甲、乙、丙任何2人在不同小组的安排方法的种数为（）

A.90 B.180 C.540 D.3240

【例5】2022年9月30日至10月9日，第56届国际乒联世界乒乓球团体锦标赛在成都举行，组委会安排甲、乙等6名工作人员去4个不同的岗位工作，其中每个岗位至少一人，且甲、乙2人必须在一起，则不同的安排方法的种数为（）

A.240 B.180 C.156 D.144

【例6】我国古代有辉煌的数学研究成果，其中《周髀算经》，《九章算术》，《海岛算经》，《孙子算经》均有着十分丰富的内容.某中学计划将这4本专著作为高中阶段“数学文化"校本课程选修内容，要求每学年至少选一科，三学年必须将4门选完，则小南同学的不同选修方式有（）种.

A.12 B.24 C.36 D.72

【例7】为促进援疆教育事业的发展，某省重点高中选派了3名男教师和2名女教师去支援边疆工作，分配到3所学校，每所学校至少一人，每人只去一所学校，则两名女教师分到同一所学校的情况种数为 ·

# 【精选例题】

1.某地为遏制新冠肺炎病毒传播，要安排3个核酸采样队到2个中风险小区做核酸采样，每个核酸采样队只能选择去一个中风险小区，每个中风险小区里至少有一个核酸采样队，则不同的安排方法共有（）

A.2种 B.3种 C.6种 D.8种

2.某社区服务站将5名志愿者分到3个不同的社区参加活动，要求每个社区至少1人，不同的分配方案有（）

A.360种 B.300种 C.90种 D.150种

3．6名同学到甲、乙、丙三个场馆做志愿者，每名同学只去1个场馆，甲场馆安排1名，乙场馆安排2名，丙场馆安排3名，则不同的安排方法共有（）

A.120种 B.90种 C.60种 D.30种

4．有编号分别为1，2，3，4的四个盒子和四个小球，把小球全部放入盒子，恰有一个空盒，有 种放法

5．某市教育局人事部门打算将甲、乙、丙、丁、戊这5名应届大学毕业生安排到该市4所不同的学校任教，每所学校至少安排一名，每名学生只去一所学校，则不同的安排方法种数是

6．某班将5名同学分配到甲、乙、丙三个社区参加劳动锻炼，每个社区至少分配一名同学，则甲社区恰好分配  
2名同学共有 种不同的方法.

7．甲、乙、丙三名志愿者需要完成A，B，C，D， $E$ 五项不同的工作，每项工作由一人完成，每人至少完成一项，且 $E$ 工作只有乙能完成，则不同的安排方式有_种.

8.某校举行科技文化艺术节活动，学生会准备安排6名同学到两个不同社团开展活动，要求每个社团至少安排两人，其中A， $B$ 两人不能分在同一个社团，则不同的安排方案数是（）

A.56 B.28 C.24 D.12

9.中国空间站的主体结构包括天和核心实验舱、问天实验舱和梦天实验舱，假设空间站要安排甲、乙等5名航天员开展实验，三舱中每个舱至少一人至多二人，则甲乙不在同一实验舱的种数有（）

A.60 B.66 C.72 D.80

题型七：正难则反及交叉问题

【例1】用1，2，3，4，5组成一个没有重复数字的五位数，三个奇数中仅有两个相邻的五位数有

【例2】如图，某城市的街区由12个全等的矩形组成（实线表示马路），CD段马路由于正在维修，暂时不通，则从 $A$ 到 $B$ 的最短路径有（）

![](images/97d58f32145747ff98fbeafd78708ebf10929dc8692c9327d6cef134ab7c5597.jpg)

A.23条 B.24条 C.25条 D.26条

【例3】某老师一天上3个班级的课，每班一节，如果一天共9节课，且老师不能连上3节课（第5节和第6节不算连上)，那么这位老师一天的课表的所有排法有__种.

【例4】从2，4，6，8，10这五个数中，每次取出两个不同的数分别为 $^ { a , b }$ ，共可得到lg $| a - | \mathrm { g } b$ 的不同值的个数是（）

A.20 B.18 C.10 D.9

【例5】某校以劳动周的形式开展劳育工作的创新实践.学生可以参加“民俗文化"茶艺文化”茶壶制作水果栽培“蔬菜种植"3D打印"这六门劳动课中的两门.则甲、乙、丙这3名学生至少有2名学生所选劳动课全不相同的方法种数共有（）

A.2080 B.2520 C.3375 D.3870

# 【题型专练】

1．从6位女学生和5位男学生中选出3位学生，分别担任数学、信息技术、通用技术科代表，要求这3位科代表中男、女学生都要有，则不同的选法共有.

A.810种 B.840种 C.1620种 D.1680种

2．设直线的方程是 $A x + B y = 0$ ，从1，2，3，4，5这五个数中每次取两个不同的数作为 $A$ 、 $B$ 的值，则所得不同直线的条数是

3.从集合 $M = \left\{ 1 , 2 , 3 , 4 , 5 , 6 , 7 , 8 , 9 \right\}$ 中分别取2个不同的数作为对数的底数与真数，一共可得到_ _个不同的对数值.

4．用1，2，3，4，5，0组成数字不重复的六位数，满足1和2不相邻，5和0不相邻，则这样的六位数的个数为

题型八：定序问题（消序法）在排列问题中限制某几个元素必须保持一定顺序称为定序问题，这类问题用消序法求解比较方便快捷

【例1】甲、乙、丙、丁、戊、己六人按一定的顺序依次抽奖，要求甲排在乙前面，丙与丁不相邻且均不排在最后，则抽奖的顺序有（）

A.72种 B．144种 C.360种 D.720种

# 选择性必修第三册

【例2】按照编码特点来分，条形码可分为宽度调节法编码和模块组合法编码．最常见的宽度调节法编码的条形码是“标准25码”，“标准25码"中的每个数字编码由五个条组成，其中两个为相同的宽条，三个为相同的窄条，如图就是一个数字的编码，则共有多少（）种不同的编码.

![](images/793455f26307d70b4f25091f13b63cc18327aad65fb411b07a251b71d7cc069b.jpg)

A.120 B.60 C.40 D.10【例3】DNA是形成所有生物体中染色体的一种双股螺旋线分子，由称为碱基的化学成分组成它看上去就像是两条长长的平行螺旋状链，两条链上的碱基之间由氢键相结合．在 DNA中只有4种类型的碱基，分别用A、C、$G$ 和 $T$ 表示，DNA中的碱基能够以任意顺序出现两条链之间能形成氢键的碱基或者是A-T，或者是C-G，不会出现其他的联系因此，如果我们知道了两条链中一条链上碱基的顺序，那么我们也就知道了另一条链上碱基的顺序．如图所示为一条DNA单链模型示意图，现在某同学想在碱基 $T$ 和碱基 $C$ 之间插入3个碱基A，2个碱基$C$ 和1个碱基T，则不同的插入方式的种数为（）

...AGGATCGG...

A.20 B.40 C.60 D.120【例4】有 $6 \times 6$ 的方格中停放三辆完全相同的红色车和三辆完全相同的黑色车，每一行每一列只有一辆车，每辆车占一格，则停放的方法数为（）

A.720 B.2160 C.8400 D.14400【例5】花灯，又名“彩灯"灯笼”，是中国传统农业时代的文化产物，兼具生活功能与艺术特色.如图，现有悬挂着的8盏不同的花灯需要取下，每次取1盏，则不同取法总数为（）

888

A.2520 B.5040 C.7560 D.10080【例6】如图所示，某货场有三堆集装箱，每堆2个，现需要全部装运，每次只能从其中一堆取最上面的一个集装箱，则在装运的过程中不同取法的种数是 （用数字作答）：

![](images/e1e084b445c316797a7d4ac4359db2c4d88061a05b6c8adea1656f589f270296.jpg)

# 【题型专练】

1.某次灯谜大会共设置6个不同的谜题，分别藏在如图所示的6只灯笼里，每只灯笼里仅放一个谜题.并规定一名参与者每次只能取其中一串最下面的一只灯笼并解答里面的谜题，直到答完全部6个谜题，则一名参与者一共有 种不同的答题顺序.

![](images/618276ef5bb15609058c765d4dbc017a0719cd2750bec86fffcef178385e2338.jpg)

2．五个人并排站在一排，如果甲必须站在乙的右边(甲乙可不相邻)，则不同的排法有 种.

3．由数字0,1组成的一串数字代码，其中恰好有7个1，3个0，则这样的不同数字代码共有 个.

4．2021年07月01日是中国共产党成立100周年，习近平总书记代表党和人民庄严宣告，经过全党全国各族人民持续奋斗，我们实现了第一个百年奋斗目标，在中华大地上全面建成了小康社会，历史性地解决了绝对贫困问题.某数学兴趣小组把三个0.两个2、两个1与一个7组成一个八位数（如20001217)，若其中三个0均不相邻，则这个八位数的个数为（）

A.200 B.240 C.300 D.600

5．英文单词"sentence"由8个字母构成，将这8个字母组合排列，且两个 $_ n$ 不相邻一共可以得到英文单词的个数为（）（可以认为每个组合都是一个有意义的单词）

A.2520 B.3360 C.25200 D.4530题型九：隔板法（元素相同问题隔板法）

# 【精选例题】

【例1】把6个相同的小球放入4个不同的箱子中，每个箱子都不空，共有多少种放法（）

A.10种 B.24种 C.36种 D.60种

【例2】某学校为增进学生体质，拟举办长跑比赛，该学校高一年级共有6个班，现将8个参赛名额分配给这6个班，每班至少1个参赛名额，则不同的分配方法共有（）

A.15种 B.21种 C.30种 D.35种

【例3】马路上亮着一排编号为1，2，3，4，5，6，7，8，9，10的10盏路灯．为节约用电，现要求把其中的两盏灯关掉，但不能同时关掉相邻的两盏，也不能关掉两端的路灯，则满足条件的关灯方法种数为（）

A.12 B.18 C.21 D.24

# 【题型专练】

1．把9个完全相同的口罩分给6名同学，每人至少一个，不同的分法有（）种

A.41 B.56 C.156 D.252

2．（多选题）将12支完全相同的圆珠笔分给4位小朋友.（）

A．若每位小朋友至少分得1支，则有 $C _ { 1 1 } ^ { 4 }$ 种分法B．若每位小朋友至少分得1支，则有 ${ \bf C } _ { 1 1 } ^ { 3 }$ 种分法C．若每位小朋友至少分得2支，则有 ${ \mathrm { C } } _ { 7 } ^ { 3 }$ 种分法D．若每位小朋友至少分得2支，则有 ${ \sf C } _ { 8 } ^ { 3 }$ 种分法

3．从某校4个班级的学生中选出7名学生作为代表参加志愿者服务活动，若每个班级至少有一名代表，则有种不同的选法.

题型十：排列组合中的涂色问题

【例1】用红、黄、蓝3种颜色给如图所示的6个相连的圆涂色，若每种颜色只能涂2个圆，且相邻2个圆所涂颜色不能相同，则不同的涂法种数为（）

OO00OO

A.24 B.30 C.36 D.42

【例2】如图，“赵爽弦图"是我国古代数学的瑰宝，它是由四个全等的直角三角形和一个正方形构成，现从给出的5种不同的颜色中最多可以选择4种不同的颜色给这5个区域涂色；要求相邻的区域不能涂同一种颜色，每个区域只涂一种颜色．则不同的涂色方案有（）种

![](images/d27a1afe7222bd7df59449cdf86b0009610dd2701aac3cb45bb83d9c75416c08.jpg)

A.120 B.240 C.300 D.360

【例3】用红、黄、蓝三种颜色填涂如图所示的六个方格，要求有公共边的两个方格不同色，则不同的填涂方法有（）

![](images/2d83dbbcac7525969302699551c8c038f179e9f83796507875ea88d87bc473ca.jpg)

A.96种 B.48种 C.144种 D.72种

【例4】用6种不同的颜色给图中的“笑脸"涂色，要求“眼睛”（即图中A、B所示区域）用相同颜色，则不同的涂法共有 种．（用数字作答）

![](images/38a0f0bd0497b17b618ea8f13360eabd65fc868be1023423e67dd91c4f21e10b.jpg)

△ <

【例5】学习涂色能锻炼手眼协调能力，更能提高审美能力.现有四种不同的颜色：湖蓝色、米白色、橄榄绿、薄荷绿，欲给小房子中的四个区域涂色，要求相邻区域不涂同一颜色，且橄榄绿与薄荷绿也不涂在相邻的区域内，则共有 种不同的涂色方法.

![](images/b7330085e34009b010ae0c1774abc74606a28fa672d539f22cf4a6871c0bd05f.jpg)

# 选择性必修第三册

# 【题型专练】

1.如图，节日花坛中有5个区域，现有4种不同颜色的花卉可供选择，要求相同颜色的花不能相邻栽种，则符合条件的种植方案有 种.

![](images/3e336450ff750a5fde5eb1a57b65331abe11fcc370e6a59587019229173718df.jpg)

2．如图，用四种不同颜色给图中的A,B.C,D,E.F六个点涂色，要求每个点涂一种颜色，且图中每条线段的两个端点涂不同颜色，则不同的涂色方法用

![](images/ce51dde3592f400159e1d6738f167f4986a4761fe65e22aba7cf15b67a6eb3ed.jpg)

A.288种 B.264种 C.240种 D.168种

3.如图，用5种不同的颜色给图中 $A$ ， $B$ ，C， $D$ 四块区域涂色，若相邻区域不能涂同一种颜色，则不同的涂法共有 种·

![](images/0438c830e7936e046bcf114734661f4c019a51220e1d10a2afc851a37ba221c7.jpg)

4．某城市在中心广场建造一个花圃，花圃分为6个部分.现要栽种4种不同颜色的花，每部分栽种一种且相邻部分不能栽种同样颜色的花，则不同的栽种方法有_ 种.（用数字作答）

![](images/3030ad32d1e66030cb6312338305417670a53d722f33ebdd8f322570ced4a7c4.jpg)

5.如图，用6种不同的颜色给图中的4个格子涂色，每个格子涂一种颜色，要求相邻的两个格子颜色不同，且两端的格子的颜色也不同，则不同的涂色方法共有 种（用数字作答）.

![](images/e7ebfbc3fb7fa2aaa04d5d4be81092df02ca9d5fe02a83db133c3225159ab825.jpg)

# 题型十一：与几何有关的组合应用题

【例1】以一个正三棱柱的顶点为顶点的四面体共有（）

A.6个 B.12个 C.18个 D.30个

【例2】8个点将半圆分成9段弧，以10个点（包括2个端点）为顶点的三角形中钝角三角形有（）个

A.55 B.112 C.156 D.120

【例3】图中的矩形的个数为（）

![](images/0c906c8069a5eb81abe387fb8f98745bb533c48bb671d18b6efafb7d6ff46e1a.jpg)

A.12 B.30 C.60 D.120

【例4】如图，∠MON的边 $O M$ 上有四点 $A _ { 1 }$ $A _ { 2 }$ $A _ { 3 }$ $A _ { 4 }$ ， $O N$ 上有三点 $B _ { 1 }$ 、 $B _ { 2 }$ $B _ { 3 }$ ，则以 $O$ $A _ { 1 }$ $A _ { 2 }$ $A _ { 3 }$ $A _ { 4 }$ 、 $B _ { 1 }$ 、 $B _ { 2 }$ 、 $B _ { 3 }$ 中三点为顶点的三角形的个数为（）

![](images/1587729c647fd63da8f552c86a283cf10bfa646d918362b5f5aadc8f80a9ad00.jpg)

A.30 B.42 C.54 D.56

【例5】（多选题） $n$ 边形对角线的条数为（）

A $n ( n - 3 )$ B. $C _ { n } ^ { 2 }$ C. $C _ { n } ^ { 2 } - n$ D. $\frac { n ( n - 3 ) } { 2 }$

【例6】（1）以正方体的顶点为顶点，可以确定多少个四面体？

（2）以正方体的顶点为顶点，可以确定多少个四棱锥？

# 【跟踪训练】

1．四面体的一个顶点为 $A$ ，从其他顶点与棱的中点中取3个点，使它们和点 $A$ 在同一平面上，不同的取法共有（）

A.30种 B.33种 C.36种 D.39种

2．在平面直角坐标系 $x O y$ 上，平行直线 $x = n \big ( n = 0 , 1 , 2 , \ L \ , 5 \big )$ 与平行直线 $y = n \big ( n = 0 , 1 , 2 , \mathrm { L } \ , 5 \big )$ 组成的图形中，矩形共有（）

A.25个 B.36个 C.100个 D.225个

3．如图为一个直角三角形工业部件的示意图，现在 $A B$ 边内侧钻5个孔，在BC边内侧钻4个孔， $A B$ 边内侧的5个孔和BC边内侧的4个孔可连成20条线段，在这些线段的交点处各钻一个孔，则这个部件上最多可以钻的孔数为（）.

A.190 B.199 C.69 D.60

![](images/93c66f13754cf2de5e1d67b090a06919a5d330beb092342c02cc2cc660dc877f.jpg)

4．从正方体的8个顶点中选取4个作为顶点，可得到四面体的个数为（）

A. $C _ { 8 } ^ { 4 } - 1 2$ B. $C _ { 8 } ^ { 4 } - 8$ （204 C. $C _ { 8 } ^ { + } - 6$ D. $C _ { 8 } ^ { 4 } - 4$

5.一空间有10个点，其中5个点在同一平面上，其余没有4点共面，则10个点可以确定不同平面的个数是  
6.如图，已知图形ABCDEF，内部连有线段.（用数字作答）

![](images/0e6a71f3a8730d055c79fe2c3cb43d221920e1c392a8acfa8c2949b5db5ef8df.jpg)

(1）由点 $A$ 沿着图中的线段到达点 $E$ 的最近路线有多少条？

(2）由点 $A$ 沿着图中的线段到达点 $C$ 的最近路线有多少条？

(3)求出图中总计有多少个矩形？

# 考点题型方法总结第5讲二项式定理常考考点

# 【考点分析】

考点一：二项式定理

$\textcircled{1}$ 二项式定理： $( a + b ) ^ { n } { = } C _ { n } ^ { 0 } a ^ { n } { + } C _ { n } ^ { 1 } a ^ { n ^ { - } 1 } b { + } { \ldots } { + } ~ C _ { n } ^ { k } a ^ { n ^ { - } k } b ^ { k } { + } { \ldots } { + } C _ { n } ^ { n } b ^ { n } ( n { \in } \mathrm { N } ^ { * } )$   
$\textcircled{2}$ 通项公式： $\begin{array} { r } { T _ { k + 1 } { = } C _ { n } ^ { k } a ^ { n - k } b ^ { k } } \end{array}$ ，它表示第 $k { + 1 }$ 项  
$\textcircled{3}$ 二项式系数：二项展开式中各项的系数为 $C _ { n } ^ { 0 }$ ， $C _ { n } ^ { 1 }$ ，.， ${ \cal { C } } _ { n } ^ { n }$   
$\textcircled{4}$ 项数为 $n { \mathrel { + { 1 } } }$ ，且各项的次数都等于二项式的幂指数 $n$ ，即 $a$ 与 $b$ 的指数的和为 $_ { n }$

考点二：二项式系数的性质

对称性 与首末等距的两个二项式系数相等，即 ${ \widehat { \mathrm { C } _ { n } ^ { k } } } { = } \mathrm { C } _ { n } ^ { n - k }$ 当 $k < \frac { n + 1 } { 2 }$ 时，二项式系数是递增的性质 增减性 当 $k > \frac { n + 1 } { 2 }$ 时，二项式系数是递减的与最大值 当 $\pmb { n }$ 为偶数时，中间一项的二项式系数最大当 $\pmb { n }$ 为奇数时，中间两项的二项式系数相等且最大二项式 C+C+C²+…+C=2系数的和 $\scriptstyle { \left( \mathrm { C } _ { n } ^ { 0 } + \mathrm { C } _ { n } ^ { 2 } + \mathrm { C } _ { n } ^ { 4 } + \dots { } = \mathrm { C } _ { n } ^ { 1 } + \mathrm { C } _ { n } ^ { 3 } + \mathrm { C } _ { n } ^ { 5 } + \dots { } = 2 ^ { n - 1 } \right.}  $

# 考点三：指定项的系数或二项式系数

1.解题思路：通项公式

2.常见指定项：若二项展开式的通项为 $T _ { r + 1 } { = } g ( r ) { \cdot } x ^ { h ( r ) } ( r { = } 0 , 1 , 2 , \ . . . , \ n )$ ， $g ( r ) { \neq } 0$ ，则有以下常见结论：

$\textcircled { 1 } h ( r ) { = } 0 \Leftrightarrow T _ { r + 1 }$ 是常数项$\textcircled{2} h ( r )$ 是非负整数 $\Leftrightarrow T _ { r + 1 }$ 是整式项$\textcircled{3} h ( r )$ 是负整数 $\Leftrightarrow T _ { r + 1 }$ 是分式项$\textcircled{4} h ( r )$ 是整数 $\Leftrightarrow T _ { r + 1 }$ 是有理项

考点四：系数和---赋值法

1.赋值法的应用

$\textcircled{1}$ 形如 $( a x + b ) ^ { n }$ ， $( a x ^ { 2 } + b x + c ) ^ { m } ( a , b \in \mathbb { R } )$ 的式子，求其展开式的各项系数之和，只需令 $x { = } 1$ 即可.   
$\textcircled{2}$ 形如 $( a x + b y ) ^ { n } ( a , \ b \in \mathbb { R } )$ 的式子，求其展开式各项系数之和，只需令 $x = y = 1$ 即可.

2.二项式系数最大项的确定方法

$\textcircled{1}$ 如果 $n$ 是偶数，则中间一项第 +1项的二项式系数最大；  
$\textcircled{2}$ 如果 $n$ 是奇数，则中间两项第 $\frac { n } { 2 } + 1$ 项与第 ${ \frac { n + 1 } { 2 } } + 1$ 项的二项式系数相等并最大.考点五：求系数最大项问题  
写出通向 $T _ { r + 1 } = C _ { n } ^ { r } a ^ { n - r } b ^ { r }$ ，利用 $\begin{array} { r } { \left\{ { T _ { r } \geq T _ { r + 1 } } \right. } \\ { \left. { T _ { r } \geq T _ { r - 1 } } \right. } \end{array}$ 解出 $r$ 范围即可

题型一： $( a + b ) ^ { n }$ 展开式【例1款 $( 3 \sqrt { x } + \frac { 1 } { \sqrt { x } } ) ^ { . }$ 的展开式

【例2】设 $\boldsymbol { n } \in \mathbf { N } ^ { * }$ ，化简 $1 + C _ { n } ^ { 1 } \cdot 1 0 + C _ { n } ^ { 2 } \cdot 1 0 ^ { 2 } + C _ { n } ^ { 3 } \cdot 1 0 ^ { 3 } + \cdots + C _ { n } ^ { n } \cdot 1 0 ^ { n } =$ 【例3】求值： $1 - 2 C _ { 2 0 1 9 } ^ { 1 } + 4 C _ { 2 0 1 9 } ^ { 2 } - \cdots + ( - 2 ) ^ { 2 0 1 9 } C _ { 2 0 1 9 } ^ { 2 0 1 9 } =$ 【例4】化简多项式 $\left( 2 x + 1 \right) ^ { 5 } - 5 \left( 2 x + 1 \right) ^ { 4 } + 1 0 \left( 2 x + 1 \right) ^ { 3 } - 1 0 \left( 2 x + 1 \right) ^ { 2 } + 5 \left( 2 x + 1 \right) - 1$ 的结果是（）

A. $\left( 2 x + 1 \right) ^ { 5 }$ B. $2 x ^ { 5 }$ C. $\left( 2 x - 1 \right) ^ { 5 }$ D. $3 2 x ^ { 5 }$

# 【题型专练】

$\left( x - { \frac { 1 } { 2 } } \right) ^ { 3 }$ 的展开式.

A. $2 ^ { 2 0 2 1 } - 1$ B. $2 ^ { 2 0 2 4 } - 1$ C. $1 0 1 1 \times 2 ^ { 2 0 2 1 }$ D. $1 0 1 1 \times 2 ^ { 2 0 2 2 }$

3.化简 ${ \bigl ( } x + 1 { \bigr ) } ^ { 4 } - 4 { \bigl ( } x + 1 { \bigr ) } ^ { 3 } + 6 { \bigl ( } x + 1 { \bigr ) } ^ { 2 } - 4 { \bigl ( } x + 1 { \bigr ) } +$ 1的结果为（）

A. $x ^ { \mathcal { I } }$ B. $\left( x - 1 \right) ^ { 4 }$ C. $\left( x + 1 \right) ^ { 4 }$ D.x4-1

# 题型二：二项展开式中的系数

解题思路：利用二项式展开式的通向 $T _ { r + 1 } = C _ { n } ^ { r } a ^ { n - r } b ^ { r }$ ，合并同类项，按照题目要求找出相应 $r$ 的值

[1茶 $\left( x - { \frac { 2 } { x } } \right) ^ { \gamma }$ 的展开式中， $\frac { 1 } { x }$ 的系数是（）

A.35 B.-35 C.560 D.-560

【例2】若4是一组数据0,2.2的方差，则2 的展开式的常数项为（）

A.-210 B.3360 C.210 D.16

【例3】已知二项式 $\left( x + { \frac { 1 } { x ^ { 5 } } } \right) ^ { n } \left( n \in \mathbf { N } ^ { * } \right)$ 展开式中含有常数项，则 $n$ 的最小值为

【例4 $1 ~ { \bigg ( } { \sqrt [ 3 ] { x } } - { \frac { 1 } { 2 } } x { \bigg ) } ^ { 6 }$ 的展开式中含 $x ^ { + }$ 项的系数为 （用数字作答）

【例5】 $\left( 3 x - \frac { 1 } { \sqrt { x } } \right) ^ { n }$ 的展开式中各二项式系数之和为64，则展开式中的常数项为

# 【题型专练】

$\left( 4 x + { \frac { 1 } { 2 { \sqrt { x } } } } \right) ^ { 9 }$ 的展开式中的常数项为

2.写出一个正整数 $n$ ，使 $\left( x ^ { 2 } - \frac { 1 } { 2 \sqrt [ 3 ] { x } } \right) ^ { n }$ 的展开式中含有常数项，则 $n ^ { = }$ （答案不唯一，写出一个符合题意的即可）

3.若（x²- $( x ^ { 2 } - \frac { 1 } { x \sqrt { x } } ) ^ { n }$ 展开式中第5项为常数项，则 $n =$ （20114号

$\left( x ^ { 3 } - { \frac { 1 } { 2 { \sqrt { x } } } } \right) ^ { n }$ 的展开式共有8项，则常数项为

5.已知 $\left( 2 x ^ { 2 } + y \right) ^ { 6 }$ 的展开式中 $x ^ { 8 } y ^ { 2 }$ 的系数为

6.在 $\left( \sqrt [ 3 ] { x } - \frac { 1 } { 2 \sqrt [ 3 ] { x } } \right) ^ { 1 2 }$ 的二项展开式中，第 项为常数项.

$a > 0$ ， $\left( a x ^ { 2 } + \frac { 1 } { \sqrt { x } } \right) ^ { 4 }$ 概开式中 $x ^ { 3 }$ 的系数为 $\frac { 3 } { 2 }$ $a =$

# 题型三：二项式系数和和各项系数和

【例1】已知 $\left( { \frac { 1 } { x } } - x \right) ^ { n }$ 的展开式中二项式系数的和是1024，则它的展开式中的常数项是（）

A.252 B.-252 C.210 D.-210

【例2】在 $\left( 3 x + { \frac { 1 } { \sqrt { x } } } \right) ^ { \prime \prime }$ 的展开式中，各项系数与二项式系数和之比为64，则该展开式中的常数项为（）

A.15 B.45 C.135 D.405

【例3】（多选题）已知 $\left( 1 + { \frac { a } { x } } \right) \left( 2 x - { \frac { 1 } { x } } \right) ^ { 6 }$ 的展开式中各项系数的和为2，则下列结论正确的有（）

A $a = 1$ B．展开式中常数项为160   
C．展开式系数的绝对值的和1458 D．展开式中含 $x ^ { 2 }$ 项的系数为240

【例4】在二项式 $\left( 2 x ^ { 3 } + { \frac { 1 } { x } } \right) ^ { n }$ 的展开式中，所有的二项式系数之和为64，则该展开式中的 $x ^ { 6 }$ 的系数是

【例5】在 $\left( 1 + 3 x \right) ^ { 4 }$ 的展开式中，二项式系数之和为 ；各项系数之和为 （用数字作答）

# 【题型专练】

$( x ^ { 2 } - \frac { 3 } { x } ) ^ { n }$ 的墨开式中，二项式系教的和是16，则展开式中各项系的和为

A.16 B.32 C.1 D.-32

2.（多选题）已知 $\left( { \frac { 1 } { x } } - 2 x \right) ^ { 2 n + 1 }$ 的展开式中第二项与第三项的系数的绝对值之比为1:8，则（）

A. $n { = } 4$ B．展开式中所有项的系数和为1C．展开式中二项式系数和为 $2 ^ { + }$ D.展开式中不含常数项

3.若 $\left( 3 x + { \sqrt { x } } \right) ^ { n }$ 的展开式的所有项的系数和与二项式系数和的比值是32,则展开式中 $x ^ { 3 }$ 项的系数是

$2 { \bigg ( } 2 x - { \frac { 1 } { x } } { \bigg ) } ^ { n }$ 的展开式中二项式系数的和为64，测该展升式中的常数项是

5.在 $( x + \frac { 3 } { \sqrt { x } } ) ^ { n }$ 的展开式中，各项系数和与二项式系数和之比为32，则 $x ^ { 2 }$ 的系数为

6.二项式 $\left( x - { \frac { 2 } { x } } \right) ^ { 6 }$ 的展开式中，常致项是 ，各项二项式系数之和是 （本题用数字作答）

题型四：求两个二项式乘积的展开式指定幂的系数

【例1】 $( x ^ { 3 } + 1 ) \cdot ( 2 x - \frac { 1 } { x ^ { 2 } } ) ^ { 6 }$ 的展开式中的常数项为（）

A.240 B.-240 C.400 D.80

【例2】二项式 $( 1 + x + x ^ { 2 } ) ( 1 - x ) ^ { 1 0 }$ 展开式中 $x ^ { 4 }$ 的系数为（）

A.120 B.135 C.140 D.100

【例3】已知 ${ \bigl ( } x + 1 { \bigr ) } { \bigl ( } x - 1 { \bigr ) } ^ { 5 } = a _ { 0 } + a _ { 1 } x + a _ { 2 } x ^ { 2 } + a _ { 3 } x ^ { 3 } + a _ { 4 } x ^ { 4 } + a _ { 5 } x ^ { 5 } + a _ { 6 } x ^ { 6 }$ ，则 $a _ { 3 }$ 的值为（）

A.-1 B.0 C.1 D.2

【例4】（多选题）已知 $\left( 1 + { \frac { a } { x } } \right) \left( 2 x - { \frac { 1 } { x } } \right) ^ { 6 }$ 的展开式中各项系数的和为2，则下列结论正确的有（）

A. $a = 1$ B．展开式中常数项为160   
C．展开式系数的绝对值的和1458 D．展开式中含 $x ^ { 2 }$ 项的系数为240

【例5】 $\left( x - 1 \right) ^ { 5 } ( 2 + \frac { y } { x } ) ^ { 6 }$ 的展开式中 $x y ^ { 3 }$ 的系数为 （用数字作答）.

【例6】 $\left( 1 + x \right) ^ { 5 } \left( 1 + 2 y \right) ^ { 3 }$ 的展开式中，记 $x ^ { m } y ^ { n }$ 项的系数为 $f ( m , n )$ ，则 $f ( 3 , 0 ) + f ( 2 , 1 ) + f ( 1 , 2 ) + f ( 0 , 3 ) =$

# 选择性必修第三册

# 【题型专练】

1.在 ${ \Bigl ( } x ^ { 2 } + x + 1 { \Bigr ) } { \Biggl ( } { \frac { 1 } { x } } - 1 { \Biggr ) } ^ { 5 }$ 的展开式中常数项为（）

A.14 B.-14 C.6 D.-6

2.已知 $( x ^ { 2 } + a ) ( x - \frac { 2 } { x } ) ^ { 5 }$ 的展开式中各项系数的和为-3，则该展开式中 $x$ 的系数为（）

A.0 B.-120 C.120 D.-160

3 $( 1 + 2 x ^ { 2 } ) ( x + \frac { 1 } { x } ) ^ { 2 }$ （21 的展开式中常数项为 （用数字作答）

4.已知 ${ \big ( } x + 1 { \big ) } ( x - 1 ) ^ { 5 } = a _ { 0 } + a _ { 1 } x + a _ { 2 } x ^ { 2 } + a _ { 3 } x ^ { 3 } + a _ { 4 } x ^ { 4 } + a _ { 5 } x ^ { 5 } + a _ { 6 } x ^ { 6 }$ ，则 $a _ { 0 } + a _ { 3 }$ 的值为

5.已知 ${ \Biggl ( } x + { \frac { 1 } { x } } { \Biggr ) } { \Bigl ( } a x + 1 { \Bigr ) } ^ { \xi }$ 的所有项的系数的和为64，展开式中 $x ^ { 2 }$ 项的系数为

6.在 $( 1 + x ) ^ { 7 } ( 1 + y ) ^ { 3 }$ 的展开式中，记 $x ^ { m } y ^ { n }$ 项的系数为 $f ( m , n )$ ，则 $f ( 3 , 0 ) + f ( 2 , 1 ) + f ( 1 , 2 ) + f ( 0 , 3 ) = \mathrm { ~ ( ~ ) ~ }$

A.45 B.60 C.120 D.210

7. $\left( x + 2 y \right) ^ { 5 } \left( x - 2 y \right) ^ { 7 }$ 的展开式中 $x ^ { 9 } y ^ { 3 }$ 的系数为（）

A.-160 B.-80 C.160 D.80

8 $\left( 1 + \sqrt [ 3 ] { x } \right) ^ { 6 } \left( 1 + \frac { 1 } { \sqrt [ 4 ] { x } } \right) ^ { 1 }$ 展开式中的常数项为

9.已知多项式 $( 2 - x ) ^ { 5 } ( 1 + x ) ^ { 4 } = a _ { 0 } + a _ { 1 } x + a _ { 2 } x ^ { 2 } + \cdots + a _ { 9 } x ^ { 9 }$ ，则 $a _ { 0 } + a _ { 1 } + a _ { 2 } + \cdots + a _ { 9 } =$ ， $a _ { \scriptscriptstyle 1 } =$ 1 ·

选择性必修第三册

题型五：求三项展开式中指定幂的系数

【侧1 $1 ( x - \frac { 1 } { x } + 1 ) ^ { 8 }$ 的展开式中 $x$ 项的系毅为（）

A.568 B.-160 C.400 D.120

$2 \Im \left( x + \frac { 2 } { x } - y \right) ^ { \prime }$ 展开式中 $x ^ { 2 } y ^ { 5 }$ 的家数为（）

A.-21 B.21 C.-35 D.35

【例3】 $\left( 1 + 2 x - x ^ { 2 } \right) ^ { n }$ 展开式中各项系数的和为64，则该展开式中的 $x ^ { 3 }$ 项的系数为（）

A.-60 B.-30 C.100 D.160

【例4】 $( x + 2 y - 3 z ) ^ { 4 }$ 的展开式中，所有不含 $z$ 的项的系数之和为（）

A.16 B.32 C.27 D.81

【例5】 $\left( x ^ { 2 } + \frac { 1 } { x } + y \right) ^ { 4 }$ 的展开式中 $\frac { y ^ { 2 } } { x ^ { 2 } }$ 的系数为（）

A.4 B.6 C.8 D.12

【例6】 $( x + 2 y + 3 z ) ^ { 6 }$ 的展开式中， $x y ^ { 3 } z ^ { 2 }$ 的系数为

# 【题型专练】

1.在 $( 2 + x - x ^ { 2 } ) ^ { 5 }$ 的展开式中，含 $x ^ { 4 }$ 的项的系数为（）

A.-120 B.-40 C.-30 D.200

$\left( 1 + { \frac { 1 } { x } } - x \right) ^ { \cdot }$ 展开式中， $x ^ { 3 }$ 项的系数为（

A.5 B.-5 C.15 D.-15

3.在 $\left( 3 x ^ { 3 } - 5 x ^ { 2 } + 1 \right) ^ { 5 }$ 的展开式中，除 $x ^ { 5 }$ 项之外，剩下所有项的系数之和为（）

A.299 B.-301 C.300 D.-302

4.在 $\left( x + { \frac { 1 } { x } } - y \right) ^ { 9 }$ 的展开式中， $x ^ { 4 } y ^ { 3 }$ 的系致为

5. $( x ^ { 2 } + x + 1 ) ^ { n }$ 的展开式的所有项的系数和为243，则展开式中 $x ^ { 5 }$ 的系数为

题型六：有理项问题

【例1】若二项式 $\left( 2 x + { \frac { 1 } { \sqrt { x } } } \right) ^ { n } \left( n \in \mathbf { N } ^ { * } \right)$ 的展开式中只有第7项的二项式系数最大，若展开式的有理项中第 $k$ 项的系数最大，则 $k =$ （）

A.5 B.6 C.7 D.8

【例2】二项式 $( \sqrt { 2 } + \sqrt [ 3 ] { 3 } x ) ^ { 5 0 }$ 的展开式中系数为有理数的项共有（）

A.6项 B.7项 C.8项 D.9项

【例3】已知 $\left( x - { \frac { 1 } { \sqrt [ { 3 } ] { x } } } \right) ^ { \prime \prime }$ 的展开式共有13项，则下列说法中正确的有（）

A．展开式所有项的系数和为 $2 ^ { 1 2 }$ B．展开式二项式系数最大为 ${ \bf C } _ { 1 2 } ^ { 7 }$ C.展开式中没有常数项 D．展开式中有理项共有5项

【例4】（多选题）在 $( \sqrt [ 3 ] { x } + \frac { 1 } { \sqrt { x } } ) ^ { n } ( n \in \mathbf { N } ^ { * } )$ 的展开式中，有理项恰有两项，则 $_ { n }$ 的可能取值为（）

A.8 B.12 C.13 D.15

【例 $5 \mathbf { 1 } ( x - \frac { 1 } { \sqrt [ 3 ] { x } } ) ^ { 3 }$ 的展开式中所有有理项的系数和为（）

A.85 B.29 C.-27 D.-84

# 【题型专练】

1.在 $( \sqrt { x } - \frac { 1 } { \sqrt [ 3 ] { x } } ) ^ { 2 }$ 4 的展开式中， $x$ 的幂的指数是整数的项共有（）

A.3项 B.4项 C.5项 D.9项

2. $\left( x - { \frac { 1 } { \sqrt [ { 3 } ] { x } } } \right) ^ { n }$ 展开式的二项式系数和64，则展开式中的有理项个数为（）

A.0 B.1 C.2 D.3

3.（多选题）已知（x² $( x ^ { 2 } - \frac { 1 } { \sqrt { x } } ) ^ { n }$ 的展开式中第3项与第5项的系数之比为 $\frac { 3 } { 1 4 }$ 则下列结论成立的是（）

A. $n = 1 0$ B．展开式中的常数项为45C.含 $x ^ { 5 }$ 的项的系数为210 D.展开式中的有理项有5项

4、已知 $\left( { \frac { 1 } { \sqrt [ 3 ] { x } } } - 2 { \sqrt [ 5 ] { x } } \right) ^ { \prime }$ 的展开式中，仅有第5项的二项式系数最大，则展开式中有理项的个数为

5.如果 $\left( { \sqrt { x } } + { \frac { 1 } { \sqrt [ { 3 } ] { x } } } \right) ^ { n }$ 的展开式中第3项与第2项系数的比是4，那么展开式里 $x$ 的有理项有 项.（填个数）

题型七：求系数最大小项问题

【例1】已知 $( \sqrt { x } - \frac { 2 } { x } ) ^ { n }$ 的展开式中只有第5项是二项式系数最大，则该展开式中各项系数的最小值为（）

A.-448 B.-1024 C.-1792 D.-5376

【例2】 $( 1 + 1 2 x ) ^ { 2 0 2 2 }$ 的二项展开式中，系数最大的是第 项

【例3】在二项式 $\left( x + { \frac { 1 } { 2 { \sqrt [ { 3 } ] { x } } } } \right) ^ { n }$ 展开式中，第3项和第4项的系数比为 $\frac { 3 } { 5 }$ （24号

(1）求 $n$ 的值及展开式中的常数项；  
(2)求展开式中系数最大的项是第几项.

# 选择性必修第三册

# 【题型专练】

1.已知 $n$ 为正偶数，在 $( \sqrt { x } + \frac { 1 } { 2 \sqrt [ 4 ] { x } } ) ^ { \prime }$ 的展开式中，第5项的二项式系数最大.

(1)求展开式中的一次项；  
(2)求展开式中系数最大的项.

2.已知 $\left( \sqrt [ 3 ] { x } + \frac { 1 } { 2 \sqrt [ 3 ] { x } } \right) ^ { \prime }$ 的展开式中，前三项的系数成等差数列.

(1)求展开式中二项式系数最大的项；  
(2)求展开式中系数最大的项.

3.已知 $\left( { \sqrt { x } } - { \frac { a } { \sqrt [ { 4 } ] { x } } } \right) ^ { n }$ 的二项式展开式的各项二项式系数和与各项系数和均为128，

(1)求展开式中所有的有理项；  
(2)求展开式中系数最大的项.

题型八：利用“赋值法"及二项式性质，求部分项系数，二项式系数和

【例1】若 $( x + 2 + m ) ^ { 9 } = a _ { 0 } + a _ { 1 } ( x + 1 ) + a _ { 2 } ( x + 1 ) ^ { 2 } + \cdots + a _ { 9 } ( x + 1 ) ^ { 9 }$ ，且 $\left( a _ { 0 } + a _ { 2 } + \cdots + a _ { 8 } \right) ^ { 2 } - \left( a _ { 1 } + a _ { 3 } + \cdots + a _ { 9 } \right) ^ { 2 } = 3 ^ { 9 } ,$ 则实数 $m$ 的值可以为（）

A.1或-3 B.-1 C.-1或3 D.-3

【例2】设 $\left( 1 - a x \right) ^ { 2 0 2 0 } = a _ { 0 } + a _ { 1 } x + a _ { 2 } x ^ { 2 } + \cdots + a _ { 2 0 2 0 } x ^ { 2 0 2 0 }$ ，若 $a _ { 1 } + 2 a _ { 2 } + 3 a _ { 3 } + \cdots + 2 0 2 0 a _ { 2 0 2 0 } = 2 0 2 0 a$ 则非零实数 $a$ 的值为（）

A.2 B.0 C.1 D.-1

【例3】（多选题）已知 $\left( 2 x - m \right) ^ { 7 } = a _ { 0 } + a _ { 1 } \left( 1 - x \right) + a _ { 2 } \left( 1 - x \right) ^ { 2 } + \cdots + a _ { 7 } \left( 1 - x \right) ^ { 7 }$ ，若 $a _ { 0 } + { \frac { a _ { 1 } } { 2 } } + { \frac { a _ { 2 } } { 2 ^ { 2 } } } + \cdots + { \frac { a _ { 7 } } { 2 ^ { 7 } } } = - 1 2 8$ ，则有（）

A. $m = 2$ B $\begin{array} { r } { \mathrm { ~  ~ { ~ \alpha ~ } ~ } _ { 3 } = - 2 8 0 \qquad \mathrm { ~  ~ { ~ C ~ } ~ } . \quad a _ { 0 } = - 1 \qquad \mathrm { ~  ~ { ~ D ~ } ~ } . \quad - a _ { 1 } + 2 a _ { 2 } - 3 a _ { 3 } + 4 a _ { 4 } - 5 a _ { 5 } + 6 a _ { 6 } - 7 a _ { 7 } = 1 4 } \end{array}$

【例4】（多选题）已知 ${ \bigl ( } 1 + 2 x { \bigr ) } ^ { n } + a { \bigl ( } 3 - x { \bigr ) } ^ { 7 } = a _ { 0 } + a _ { 1 } x + . . . + a _ { 6 } x ^ { 6 } { \bigl ( } a \neq 0 { \bigr ) }$ ，则（）

A. $n = 6$ B. $a = 1 2 8$   
C. ${ \frac { a _ { 0 } } { 3 ^ { 7 } } } + { \frac { a _ { 1 } } { 3 ^ { 6 } } } + \cdots + { \frac { a _ { 6 } } { 3 } } = \left( { \frac { 7 } { 3 } } \right) ^ { 7 }$ $) . a _ { 1 } + 2 a _ { 2 } + \cdots + 6 a _ { 6 } = - 6 4$

【例5】若 $x ^ { 3 } + x ^ { 1 0 } = a _ { 0 } + a _ { 1 } ( 1 + x ) + a _ { 2 } ( 1 + x ) ^ { 2 } + \cdots + a _ { 1 0 } ( 1 + x ) ^ { 1 0 }$ ，则 $a _ { 0 } = \underline { { \underline { { \quad \quad } } } }$ ， $a _ { 9 } =$

【例6】设多项式(x+1)+(x-1)°=χ+ax++ax+a。，则+a+a+a+g+a0=

# 【题型专练】

1.已知 $\left( 1 + x \right) ^ { n } = a _ { 0 } + a _ { 1 } x + a _ { 2 } x ^ { 2 } + \cdots + a _ { n } x ^ { n }$ ，若 $a _ { 1 } + a _ { 2 } + a _ { 3 } + \cdots + a _ { n } = 3 1$ ，则自然数 $n ^ { = }$

2.已知 $p \in R$ 且 $p \neq 0$ ， $q \in N ^ { * }$ ， $( 1 + p x ) ^ { q } = a _ { 0 } + a _ { 1 } x + a _ { 2 } x ^ { 2 } + a _ { 3 } x ^ { 3 } + a _ { 4 } x ^ { 4 } + a _ { 5 } x ^ { 5 }$ ，且 $a _ { 1 } + a _ { 2 } + a _ { 3 } + a _ { 4 } + a _ { 5 } = 3 1$ ，则 $p + q =$

3.已知 $\left( 1 + x \right) ^ { 7 } + k \left( x ^ { 2 } + x + 1 \right) ^ { 3 } = a _ { 0 } + a _ { 1 } x + a _ { 2 } x ^ { 2 } + \cdots + a _ { 7 } x ^ { 7 }$ ，且 $a _ { 2 } + 2 a _ { 3 } + 3 a _ { 4 } + 4 a _ { 5 } + 5 a _ { 6 } + 6 a _ { 7 } = - 9$ ，则 $k = _ { - }$

4.已知 $x + m \Big ) ^ { s } = a _ { 0 } + a _ { 1 } \big ( x - 1 \big ) + a _ { 2 } \big ( x - 1 \big ) ^ { 2 } + \cdots + a _ { s } \big ( x - 1 \big ) ^ { s } \big ( m \in \mathbb { R } \big ) , \quad \frac { \ast \ast } { \sqrt { \pi } } \big ( a _ { 0 } + a _ { 2 } + a _ { 4 } \big ) ^ { 2 } - \big ( a _ { 1 } + a _ { 3 } + a _ { s } \big ) ^ { 2 } = 3 ^ { s } ,$ 则 $m =$ 或

5.（多选题）设 $( 2 x - 1 ) ^ { 5 } = a _ { 0 } + a _ { 1 } x + \cdots + a _ { 5 } x ^ { 5 }$ ，则下列说法正确的是（）

A. $a _ { 0 } = 1$ B $a _ { 1 } + a _ { 2 } + a _ { 3 } + a _ { 4 } + a _ { 5 } = 1$ C. $a _ { 0 } + a _ { 2 } + a _ { 4 } = - 1 2 1$ D. $a _ { 1 } + a _ { 3 } + a _ { 5 } = 1 2 2$

6.（多选题）已知 $\left( 1 - 2 x \right) ^ { 2 0 2 1 } = a _ { 0 } + a _ { 1 } x + a _ { 2 } x ^ { 2 } + \cdots + a _ { 2 0 2 1 } x ^ { 2 0 2 1 }$ ，下列命题中，正确的是（）

A．展开式中所有项的二项式系数的和为 $2 ^ { 2 0 2 1 }$ $\mathtt { B }$ ，展开式中所有奇次项系数的和为 $\frac { 3 ^ { 2 0 2 1 } + 1 } { 2 }$ C．展开式中所有偶次项系数的和为 $\frac { 3 ^ { 2 0 2 1 } - 1 } { 2 }$ ； D. $\frac { a _ { 1 } } { 2 } + \frac { a _ { 2 } } { 2 ^ { 2 } } + \frac { a _ { 3 } } { 2 ^ { 3 } } + \cdots \frac { a _ { 2 0 2 1 } } { 2 ^ { 2 0 2 1 } } = - 1 .$

7.（多选题）已知 $( x + 2 ) ^ { 6 } = \sum _ { i = 0 } ^ { 6 } a _ { i } x ^ { i }$ ，则（）

A. $a _ { 1 } + a _ { 2 } + a _ { 3 } + a _ { 4 } + a _ { 5 } + a _ { 6 } = 6 6 6$ B. $a _ { 3 } = 2 0$   
C. $a _ { 1 } + a _ { 3 } + a _ { 5 } > a _ { 2 } + a _ { 4 } + a _ { 6 }$ D. $a _ { 1 } + 2 a _ { 0 } = a _ { 3 } + 2 a _ { 4 } + 3 a _ { 5 } + 4 a _ { 6 }$

8.已知 $m > 0 , n \in \mathrm { N } ^ { * } , ( 1 + m x ) ^ { n } = a _ { 0 } + a _ { 1 } x + a _ { 2 } x ^ { 2 } + a _ { 3 } x ^ { 3 } + a _ { 4 } x ^ { 4 }$ 且 $a _ { 1 } + a _ { 2 } + a _ { 3 } + a _ { 4 } = 1 5$ ，则 $m + n =$ ，该展开式第3项为

题型九：利用二项式定理求余数

【例1】中国南北朝时期的著作《孙子算经》中，对同余除法有较深的研究．设 $a , \ b$ ， $m \left( m > 0 \right)$ 为整数，若 $a$ 和 $b$ 被 $m$ 除得的余数相同，则称 $a$ 和 $b$ 对模 $m$ 同余，记为 $a \equiv b { \bigl ( } { \bmod { m } } { \bigr ) }$ .若 $a = \mathrm { C } _ { 2 0 } ^ { 0 } + \mathrm { C } _ { 2 0 } ^ { 1 } \times 3 + \mathrm { C } _ { 2 0 } ^ { 2 } \times 3 ^ { 2 } + \dots + \mathrm { C } _ { 2 0 } ^ { 2 0 } \times 3 ^ { 2 0 }$ ，$a \equiv b = \left( { \mathrm { m o d } } 5 \right)$ ，则 $b$ 的值可以是（）

A.2004 B.2005 C.2025 D.2026

【例2】（多选题）已知 $( x ^ { 2 } + \frac { a } { \sqrt { x } } ) ^ { n }$ 的展开式中第4项与第7项的二项式系数相等，且展开式的各项系数之和为0，则（）

A. $\mathbf { \nabla } _ { \mathbf { \mathcal { n } } } = \mathbf { 9 }$ B. $( x ^ { 2 } + \frac { a } { \sqrt { x } } ) ^ { n }$ 的展开式中有理项有5项 C. $( x ^ { 2 } + \frac { a } { \sqrt { x } } ) ^ { n }$ 的展开式中偶数项的二项式系数和为512D. $( 7 - a ) ^ { n }$ 除以9余8

【例3】设 $n { \in }  { \mathbb { N } } _ { + }$ ，且 $1 9 ^ { 2 0 2 2 } + n$ 能被6整除，则 $n$ 的值可以为 写出一个满足条件的 $n$ 的值即可)

【例4】已知 $\boldsymbol { a } \in \mathbb { N }$ 且满足 $3 ^ { 2 0 2 3 } + a$ 能被8整除，则符合条件的一个 $a$ 的值为

# 【题型专练】

1.设 $a = 3 ^ { n } + \textstyle \mathrm { C } _ { n } ^ { 1 } 3 ^ { n - 1 } + \textstyle \mathrm { C } _ { n } ^ { 2 } 3 ^ { n - 2 } + \dots + \textstyle \mathrm { C } _ { n } ^ { n - 1 } 3$ ，则当 $n = 2 0 2 3$ 时， $^ { a }$ 除以15所得余数为（）

A.3 B.4 C.7 D.8

2.（多选题）设 $a \in \mathbf { N }$ ，且 $ { 0 } \leq a < 2 6$ ，若 $5 1 ^ { 2 0 } + a$ 能被13整除，则 $a$ 的值可以为（）

A.0 B.11 C.12 D.25

3. $9 1 ^ { 9 2 }$ 除以100的余数是

4.若 $5 1 ^ { 2 0 2 0 } + a$ 能被13整除，则实数 $a$ 的值可以为 .（填序号）

$\textcircled{1} 0$ $\textcircled{2} 1 1$ $\textcircled{3}$ 12； $\textcircled{4} 2 5$

题型十：利用二项式定理求近似值

【例1】 $0 . 9 9 ^ { 7 }$ 的计算结果精确到0.001的近似值是（）

A.0.930 B.0.931 C.0.932 D.0.933

【例2】估算 $\mathrm { C } _ { 5 } ^ { 1 } 0 . 9 9 8 + \mathrm { C } _ { 5 } ^ { 2 } 0 . 9 9 8 ^ { 2 } + \mathrm { C } _ { 5 } ^ { 3 } 0 . 9 9 8 ^ { 3 } + \mathrm { C } _ { 5 } ^ { 4 } 0 . 9 9 8 ^ { 4 } + \mathrm { C } _ { 5 } ^ { 5 } 0 . 9 9 8 ^ { 5 }$ 的结果，精确到0.01的近似值为（）

A.30.84 B.31.84 C.30.40 D. 32.16

# 【题型专练】

1.已知 $_ n$ 为正整数，若 $1 . 1 5 ^ { 1 0 } \in \left[ n , n + 1 \right)$ ，则 $n$ 的值为（）

A.2 B.3 C.4 D.5

2. $1 . 0 2 ^ { 8 }$ 的近似值是 (精确到小数点后三位）

题型十一：二项式定理与杨辉三角

【例1】当 $n \in \mathrm { N }$ 时，将三项式 $( x ^ { 2 } + x + 1 ) ^ { n }$ 展开，可得到如图所示的三项展开式和“广义杨辉三角形”：

$$
{ \begin{array} { r l } & { \left( x ^ { 2 } + x + 1 \right) ^ { 0 } = 1 } \\ & { \left( x ^ { 2 } + x + 1 \right) ^ { 1 } = x ^ { 2 } + x + 1 } \\ & { \left( x ^ { 2 } + x + 1 \right) ^ { 2 } = x ^ { 4 } + 2 x ^ { 3 } + 3 x ^ { 2 } + 2 x + 1 } \\ & { \left( x ^ { 2 } + x + 1 \right) ^ { 3 } = x ^ { 6 } + 3 x ^ { 3 } + 6 x ^ { 4 } + 7 x ^ { 3 } + 6 x ^ { 2 } + 3 x + 1 } \\ & { \left( x ^ { 2 } + x + 1 \right) ^ { 4 } = x ^ { 8 } + 4 x ^ { 7 } + 1 0 x ^ { 6 } + 1 6 x ^ { 3 } + 1 9 x ^ { 4 } + 1 6 x ^ { 3 } + 1 0 x ^ { 2 } + 4 x + 1 } \end{array} }
$$

# 广义杨辉三角形

第0行 1  
第1行 111  
第2行 12321  
第3行 1367631  
第4行14101619161041

若在 $( 1 + a x ) ( x ^ { 2 } + x + 1 ) ^ { 5 }$ 的展开式中， $x ^ { 8 }$ 的系数为75，则实数 $a$ 的值为（）

A.1 B.-1 C.2 D.-2

【例2】杨辉是我国南宋末年的一位杰出的数学家．他在《详解九章算法》一书中，画了一个由二项式$\left( a + b \right) ^ { n } \left( n = 1 , 2 , 3 , \cdots \right)$ 展开式的系数构成的三角形数阵，称作“开方作法本源”，这就是著名的“杨辉三角”．在"杨辉三角"中，从第2行开始，除1以外，其他每一个数值都是它上面的两个数值之和，每一行第 $k \left( k \leq n , k \in \mathbf { N } ^ { * } \right)$ 个数组成的数列称为第 $k$ 斜列．该三角形数阵前5行如图所示，则该三角形数阵前2022行第 $k$ 斜列与第 $k + 1$ 斜列各项之和最大时， $k$ 的值为（）

第1行 11第2行 121第3行 1331第4行 14641第5行15101051

A.1009 B.1010 C.1011 D.1012【例3】（多选题）杨辉三角形，又称贾宪三角形，是二项式系数 $\mathbf { C } _ { n } ^ { r - 1 }$ （ $n \in \mathbf { N } ^ { * }$ ， $\boldsymbol { r } \in \mathbf { N } ^ { * }$ 且 $r$ $n + 1$ ）在三角形中的一种几何排列，北宋人贾宪约1050年首先使用“贾宪三角"进行高次开方运算，南宋时期杭州人杨辉在他1261年所著的《详解九章算法》一书中，辑录了如下图所示的三角形数表，称之为“开方作法本源"图，并说明此表引自11世纪前半贾宪的《释锁算术》，并绘画了“古法七乘方图”，故此，杨辉三角又被称为“贾宪三角”，杨辉三角形的构造法则为：三角形的两个腰都是由数字1组成的，其余的数都等于它肩上的两个数字相加.根据以上信息及二项式定理的相关知识分析，下列说法中正确的是（）

A $\mathbf { C } _ { n + 1 } ^ { r } = \mathbf { C } _ { n } ^ { r } + \mathbf { C } _ { n } ^ { r - 1 }$   
B.当 $k \in \mathbf { N } ^ { * }$ 且 $k \leq n$ 时， $\mathbf { C } _ { n } ^ { k } < \mathbf { C } _ { n + 1 } ^ { k }$   
C. $\left\{ \mathbf { C } _ { n } ^ { 2 } \right\}$ 为等差数列  
D.存在 $k \in \mathbf { N } ^ { * }$ ，使得 $\left\{ \mathbf { C } _ { n + 1 } ^ { k } - \mathbf { C } _ { n } ^ { k } \right\}$ 为等差数列

【例4】如图所示的杨辉三角中，从第2行开始，每一行除两端的数字是1以外，其他每一个数字都是它肩上两个数字之和在此数阵中，若对于正整数 $n$ ，第 $2 n$ 行中最大的数为 $x$ ，第 $2 n + 1$ 行中最大的数为 $y$ ，且 $1 3 x = 7 y$ ，则 $n$ 的值为

第0行 1  
第1行 1 1  
第2行 1 2 1  
第3行 1 3 3 1  
第4行 1 4 6 4 一  
第5行 1 5 10 10 5 1  
第6行1 6 15 20 15 6 1…

【例5】如图，在杨辉三角形中，斜线1的上方从1按箭头所示方向可以构成一个“锯齿形”的数列：1,3,3,4，6,5,10.,，记此数列的前 $n$ 项之和为 $S _ { n }$ ，则 $S _ { 2 3 }$ 的值为

1   
11   
小   
14641   
151010 1

# 【题型专练】

1.“杨辉三角"是中国古代数学文化的瑰宝之一，最早在中国南宋数学家杨辉1261年所著的《详解九章算法》一书中出现，欧洲数学家帕斯卡在1654年才发现这一规律，比杨辉要晚近四百年.在由二项式系数所构成的“杨辉三角"中(如图)，记第2行的第3个数字为 $a _ { \scriptscriptstyle 1 }$ ，第3行的第3个数字为 $a _ { 2 } , \cdots$ ，第 $n \left( n \geq 2 \right)$ 行的第3个数字为 $a _ { n - 1 }$ ，则 $a _ { 1 } + a _ { 2 } + a _ { 3 } + \cdots + a _ { 9 } = ( \quad )$

第0行 1  
第1行 1 1  
第2行 1 2 1  
第3行 1 3 3 1  
第4行 1 4 6 4 1  
第5行1 5 10 10 5 1  
.·.·..

A.165 B.180 C.220 D.236

2.（多选题）“杨辉三角"是二项式系数在三角形中的一种几何排列，在中国南宋数学家杨辉1261年所著的《详解九章算法》一书中就有出现.如图所示，在“杨辉三角"中，除每行两边的数都是1外，其余每个数都是其“肩上”的两个数之和，例如第4行的6为第3行中两个3的和.则下列命题中正确的是（）

第第第第第第 012345 行行行行行行 111 2 11 3 3 11 4 6 4 11 5101051：  
第 $\pmb { n }$ 行  
：A．在“杨辉三角"第9行中，从左到右第7个数是84  
B.在“杨辉三角”中，当 $n = 1 2$ 时，从第1行起，每一行的第2列的数字之和为66C.在"杨辉三角”中，第 $n$ 行所有数字的平方和恰好是第 $2 n$ 行的中间一项的数字D.记“杨辉三角”第 $_ { n }$ 行的第 $\mathrm { i }$ 个数为 $a _ { i }$ ，则 $\sum _ { i = 1 } ^ { n + 1 } 2 ^ { i - 1 } \cdot a _ { i } = 2 ^ { n }$

3.（多选题）我国南宋数学家杨辉1261年所著的《详解九章算术》就给出了著名的杨辉三角，由此可见我国古代数学的成就是非常值得中华民族自豪的，以下关于杨辉三角的叙述证确的是（）

A．第9行中从左到右第6个数是126 B. $\mathbf { C } _ { n - 1 } ^ { r - 1 } + \mathbf { C } _ { n - 1 } ^ { r } = \mathbf { C } _ { n } ^ { r }$   
C. $\mathbf { C } _ { n } ^ { 1 } + \mathbf { C } _ { n } ^ { 2 } + \cdots + \mathbf { C } _ { n } ^ { n } = 2 ^ { n }$ $\mathrm { D . } \mathrm { C } _ { 3 } ^ { 3 } + \mathrm { C } _ { 4 } ^ { 3 } + \mathrm { C } _ { 5 } ^ { 3 } + \cdots + \mathrm { C } _ { 1 0 } ^ { 3 } = 3 3 0$

4.（多选题）杨辉三角把二项式系数图形化，把组合数内在的一些代数性质直观地从图形中体现出来，是一种离散型的数与形的结合．根据杨辉三角判断下列说法正确的是（）

A. $\left( x - 1 \right) ^ { 6 } = { x ^ { 6 } } - 6 { x ^ { 5 } } + 1 5 { x ^ { 4 } } - 2 0 { x ^ { 3 } } + 1 5 { x ^ { 2 } } - 6 x + 1$   
B. $\mathrm { C } _ { 7 } ^ { 2 } + 4 \mathrm { C } _ { 7 } ^ { 3 } + 6 \mathrm { C } _ { 7 } ^ { 4 } + 4 \mathrm { C } _ { 7 } ^ { 5 } + \mathrm { C } _ { 7 } ^ { 6 } = \mathrm { C } _ { 1 1 } ^ { 6 }$   
C.已知 $\left( 1 - 3 x \right) ^ { n }$ 的展开式中第3项与第9项的二项式系数相等，则所有项的系数和为 $2 ^ { 1 2 }$ D.已知 $\left( x + 2 \right) ^ { 5 } = a _ { 0 } + a _ { 1 } \left( x + 1 \right) + a _ { 2 } \left( x + 1 \right) ^ { 2 } + \cdots + a _ { s } \left( x + 1 \right) ^ { 5 }$ ，则 $a _ { 1 } + a _ { 2 } + a _ { 3 } + a _ { 4 } = 3 1$

5．如图，在由二项式系数所构成的杨辉三角形中，若第 $n$ 行中从左至右第14与第15个数的比为2:3，则 $n$ 的值为

第0行 第1行 $\begin{array} { c } { 1 } \\ { 1 \quad 1 } \\ { 1 \quad 2 \quad 1 } \\ { 1 \quad 3 \quad 3 \quad 1 } \\ { 1 \quad 4 \quad 6 \quad 4 \quad 1 } \\ { 1 \quad 5 \quad 1 0 \quad 1 0 \quad 5 \quad 1 } \\ { \quad \quad \cdots \quad } \end{array}$   
第2行  
第3行  
第4行  
第5行

6.在我国南宋数学家杨辉所著作的《详解九章算法》一书中，用如图所示的三角形（杨辉三角）解释了二项和的乘方规律，下面的数字三角形可以看做当 $n$ 依次取0、1、2、3、L时 ${ \left( a + b \right) } ^ { n }$ 展开式的二项式系数，相邻两斜线间各数的和组成数列 $\left\{ a _ { n } \right\}$ ，例 $a _ { \scriptscriptstyle 1 } = 1$ ， $a _ { 2 } = 1 + 1$ ， $a _ { 3 } = 1 + 2$ ，L，设数列 $\left\{ a _ { n } \right\}$ 的前 $_ n$ 项和为 $S _ { n }$ .若 $a _ { 2 0 2 4 } = m + 3$ ，则 $S _ { 2 0 2 2 } =$

![](images/717c457c61c99ce52b582dba69ea4b5fa630088650c14ecf5b09fb9560a2df1e.jpg)

1．五行是华夏民族创造的哲学思想，多用于哲学、中医学和占卜方面，五行学说是华夏文明重要组成部分.古代先民认为，天下万物皆由五类元素组成，分别是金、木、水、火、土，彼此之间存在相生相克的关系.下图是五行图，现有5种颜色可供选择给五"行"涂色，要求五行相生不能用同一种颜色（例如金生火，水生木，不能同色），五行相克可以用同一种颜色（例如水克火，木克土，可以用同一种颜色)，则不同的涂色方法种数有（）

![](images/d54da327c7b77fe9bb4e3c5340013a019c385a4a68f04f843ced595b8f8847f2.jpg)

A.3125 B.1000 C.1040 D.1020

2.已知空间直角坐标系中， $O ( 0 , 0 , 0 ) , A ( 8 , 0 , 0 ) , B ( 0 , 8 , 0 ) , C ( 0 , 0 , 8 )$ ，三棱锥 $O - A B C$ 内部整数点（所有坐标均为整数的点，不包括边界）的个数为（）

A $\mathrm { C } _ { 7 } ^ { 2 }$ B. ${ \bf C } _ { 7 } ^ { 3 }$ C. ${ \mathrm { C } } _ { 8 } ^ { 2 }$ D. $C _ { 8 } ^ { 3 }$

3．如图，在某城市中， $M$ $N$ 两地之间有整齐的方格形道路网，其中 $A _ { \mathrm { l } }$ $A _ { 2 }$ $A _ { 3 }$ $A _ { 4 }$ 是道路网中位于一条对角线上的4个交汇处，今在道路网 $M$ $N$ 处的甲、乙两人分别要到 $N$ $M$ 处，他们分别随机地选择一条沿街的最短路径，以相同的速度同时出发，直到到达 $N$ $M$ 处为止．则下列说法正确的是（）

![](images/5d353c3364d7bba1fd5efc2dc3f833588a322410f1d81f71c7cc79f35482ed30.jpg)

A.甲从 $M$ 到达 $N$ 处的方法有120种B.甲从 $M$ 必须经过 $A _ { 2 }$ 到达 $N$ 处的方法有64种C.甲、乙两人在 $A _ { 2 }$ 处相遇的概率为 $\frac { 8 1 } { 4 0 0 }$ D．甲、乙两人相遇的概率为 $\frac { 1 } { 2 }$

4.6名研究人员在3个无菌研究舱同时进行工作，由于空间限制，每个舱至少1人，至多3人，则不同的安排方案共有（）

A.360种 B.180种 C.720种 D.450种

5．将六枚棋子 $A$ ，B，C， $D$ ，E， $F$ 放置在 $2 \times 3$ 的棋盘中，并用红、黄、蓝三种颜色的油漆对其进行上色（颜色不必全部选用），要求相邻棋子的颜色不能相同，且棋子 $A$ ， $B$ 的颜色必须相同，则一共有（）种不同的放置与上色方式

A.11232 B.10483 C.10368 D.5616

6．因演出需要，身高互不相等的9名演员要排成一排成一个“波浪形”，即演员们的身高从最左边数起：第一个到第三个依次递增，第三个到第七个依次递减，第七、八、九个依次递增，则不同的排列方式有（）种。

A.379 B.360 C.243 D.217

7.“杨辉三角"是中国古代数学杰出的研究成果之一.如图所示，由杨辉三角的左腰上的各数出发，引一组平行线，从上往下每条线上各数之和依次为1，1，2，3，5，8，13，L，则下列选项不正确的是（）

---..........----2 --2--.--. ---?. .---3-.....- .--4 4 1------- -1.-. 10 10 5 16 15 20 15 6 1

A．在第9条斜线上，各数之和为55  
B.在第 $n \left( n \geq 5 \right)$ 条斜线上，各数自左往右先增大后减小  
C.在第n条斜线上，共有2n+1-(-1)" 个数  
D．在第11条斜线上，最大的数是 ${ \bf C } _ { 7 } ^ { 3 }$

# 选择性必修第三册

# 二、多选题

8、对于1,2，. $n$ ，的全部排列，定义Euler数 $\binom { n } { k }$ （其中 $n \in  { \mathbb { N } } ^ { * }$ ， $k = 0 , 1 , \cdots , n$ ）表示其中恰有 $k$ 次升高的排列的个数（注： $k$ 次升高是指在排列 $a _ { 1 } a _ { 2 } \cdots a _ { n }$ 中有 $k$ 处 $a _ { i } < a _ { i + 1 }$ ， $i = 1 , \cdots , n - 1$ ）.例如：1，2，3的排列共有：123，132，213，231，312，321六个，恰有1处升高的排列有如下四个：132，213，231，312，因此： $\binom { 3 } { 1 } = 4$ 则下列结论正确的有（）

A. $\scriptstyle { \binom { 4 } { 3 } } = 3$ $\begin{array} { r l } { \mathsf { B . } } & { \binom { 4 } { 2 } { = } 1 1 } \\ { \mathsf { D . } } & { \binom { n } { k } { = } k \binom { n - 1 } { k } { + } n \binom { n - 1 } { k - 1 } } \end{array}$   
C. $\binom { n } { k } = \binom { n } { n - k - 1 }$

9．商场某区域的行走路线图可以抽象为一个 $2 \times 2$ 的正方体道路网（如图，图中线段均为可行走的通道），甲、乙两人分别从A， $B$ 两点出发，随机地选择一条最短路径，以相同的速度同时出发，直到到达 $B$ ，A为止，下列说法正确的是（）

A．甲从A必须经过 $C _ { \mathrm { { I } } }$ 到达 $B$ 的方法数共有9种B.甲从A到 $\boldsymbol { B }$ 的方法数共有180种C.甲、乙两人在 $C _ { 2 }$ 处相遇的概率为 $\frac { 4 } { 2 5 }$ D．甲、乙两人相遇的概率为 $\frac { 1 1 } { 5 0 }$

![](images/5802086ed5522e7c7e6f6fcaa5d83bc2b038ec3d82a06ab5b7f791ab627210e3.jpg)

10．将1，2，3，4，5，6，7这七个数随机地排成一个数列，记第 $i$ 项为 $a _ { i } \left( i = 1 , 2 , \cdots , 7 \right)$ ，则下列说法正确的是（）

A.若 $a _ { 4 } = 7 , a _ { 1 } + a _ { 2 } + a _ { 3 } < a _ { 5 } + a _ { 6 } + a _ { 7 }$ ，则这样的数列共有360个 B．若所有的奇数不相邻，所有的偶数也不相邻，则这样的数列共有288个 C．若该数列恰好先减后增，则这样的数列共有50个 D.若 $a _ { 1 } < a _ { 2 } < a _ { 3 } , a _ { 3 } > a _ { 4 } > a _ { 5 } , a _ { 5 } < a _ { 6 } < a _ { 7 }$ ，则这样的数列共有71个

11．跳格游戏：如图，人从格子外只能进入第1个格子，在格子中每次可向前跳1格或2格，那么下面说法正确的是（）

<html><body><table><tr><td>12345678</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

A．进入第二个格子走法有2种 B．进入第二个格子走法有1种C.进入第三个格子走法有2种 D．进入第八个格子走法有21种

12.若 $( 1 - 2 x ) ^ { 2 n } = a _ { 0 } + a _ { 1 } x + a _ { 2 } x ^ { 2 } + \cdots + a _ { 2 n } x ^ { 2 n }$ ，则（）

A. $a _ { 0 } = 1$ $\begin{array} { l } { { \mathrm { ~  ~ { ~ \cal ~ B ~ } ~ } . ~ \displaystyle \sum _ { i = 0 } ^ { 2 n } \left. a _ { i } \right. = 1 } } \\ { { \mathrm { ~  ~ { ~ \cal ~ D ~ } ~ } . ~ \displaystyle \sum _ { i = 1 } ^ { n } a _ { 2 i } = \frac { 9 ^ { n } + 1 } { 2 } } } \end{array}$   
C. $\sum _ { i = 0 } ^ { n } ( \mathbf { C } _ { n } ^ { i } ) ^ { 2 } = \mathbf { C } _ { 2 n } ^ { n }$

13．下列各式正确的是（ ）

A $\begin{array} { r } { 1 \times 2 + 2 \times 3 + 3 \times 4 + \cdots + 9 9 \times 1 0 0 = 2 C _ { 1 0 1 } ^ { 3 } \mathrm { B } . C _ { 1 0 0 } ^ { 0 } + 3 C _ { 1 0 0 } ^ { 1 } + 5 C _ { 1 0 0 } ^ { 2 } + \cdots + 2 0 1 C _ { 1 0 0 } ^ { 1 0 0 } = 1 0 0 \times 2 ^ { 1 0 0 } } \end{array}$ C. $\begin{array} { r } { \mathrm { C } _ { 1 0 0 } ^ { 1 } + 2 \mathrm { C } _ { 1 0 0 } ^ { 2 } + 3 \mathrm { C } _ { 1 0 0 } ^ { 3 } + \cdots + 5 0 \mathrm { C } _ { 1 0 0 } ^ { 5 0 } = 2 5 \times 2 ^ { 1 0 0 } \quad \mathrm { ~ D . ~ } \quad \mathrm { C } _ { 9 0 } ^ { 2 0 } \mathrm { C } _ { 1 0 } ^ { 0 } + \mathrm { C } _ { 9 0 } ^ { 1 9 } \mathrm { C } _ { 1 0 } ^ { 1 } + \mathrm { C } _ { 9 0 } ^ { 1 8 } \mathrm { C } _ { 1 0 } ^ { 2 } + \cdots + \mathrm { C } _ { 9 0 } ^ { 1 0 } \mathrm { C } _ { 1 0 } ^ { 1 0 } = \mathrm { C } _ { 1 0 0 } ^ { 2 0 } } \end{array}$

14.下列关系式成立的是（）

A $\sum _ { k = 0 } ^ { n } 2 ^ { k } \mathbf { C } _ { n } ^ { k } = 3 ^ { n } { \Big ( } n \in \mathbf { N } ^ { * } { \Big ) }$   
B. $2 \mathrm { C } _ { 2 n } ^ { 0 } + \mathrm { C } _ { 2 n } ^ { 1 } + 2 \mathrm { C } _ { 2 n } ^ { 2 } + \mathrm { C } _ { 2 n } ^ { 3 } + \cdots + \mathrm { C } _ { 2 n } ^ { 2 n + } + 2 \mathrm { C } _ { 2 n } ^ { 2 n } = 3 \ \hat { \mathrm { \bf ~ z } } ^ { 2 \ n + } \ \left( n \in \mathrm { \bf ~ N } \ \right)$ $2 < \left( 1 + { \frac { 1 } { n } } \right) ^ { n } < 3 \left( n \in \mathbf { N } ^ { * } \right)$   
D. $\operatorname { C } _ { n } ^ { 1 } \bullet ! ^ { 2 } + \operatorname { C } _ { n } ^ { 2 } \bullet 2 ^ { 2 } + \dots + \operatorname { C } _ { n } ^ { n } \bullet n ^ { 2 } = n ( n + 1 ) 2 ^ { n - 2 }$

15.已知当 $x > 0$ 时， $\frac { 1 } { 1 + x } < \ln ( 1 + \frac { 1 } { x } ) < \frac { 1 } { x }$ ，则（）

C. A $\frac { 1 0 } { 9 } < e ^ { \frac { 1 } { 9 } } < \frac { 9 } { 8 }$ $( \frac { 1 0 } { \mathrm { ~ e ~ } } ) ^ { 9 } < 9 !$ $\begin{array} { l } { { { \bf B . } \ln 9 < 1 + \displaystyle \frac { 1 } { 2 } + \cdots + \displaystyle \frac { 1 } { 9 } < \ln 1 0 } } \\ { { { \bf D . } ( \displaystyle \frac { { \bf C } _ { 9 } ^ { 0 } } { 9 ^ { 0 } } ) ^ { 2 } + ( \displaystyle \frac { { \bf C } _ { 9 } ^ { 1 } } { 9 ^ { 1 } } ) ^ { 2 } + \cdots + ( \displaystyle \frac { { \bf C } _ { 9 } ^ { 9 } } { 9 ^ { 9 } } ) ^ { 2 } < { \bf e } } } \end{array}$

# 三、填空题

16．中秋节假期间，某医院要安排某科室的2名男职工和2名女职工进行3天值班（分白班和夜班，每班1名职工)，其中女职工不值夜班，且每个人至少要值班一次，则不同的安排方法共有 种（用数字作答）.

17．我们称 $n \big ( n \in \mathbf { N } ^ { \star } \big )$ 元有序实数组 $\left( x _ { 1 } , x _ { 2 } , \cdots , x _ { n } \right)$ 为 $n$ 维向量， $\left| x _ { 1 } \right| + \left| x _ { 2 } \right| + \cdots + \left| x _ { n } \right|$ 为该向量的范数.已知 $n$ 维向量$\vec { a } = \left( x _ { 1 } , x _ { 2 } , \cdots , x _ { n } \right)$ ，其中 $x _ { i } \in \left\{ - 1 , 0 , 1 \right\} \left( i = 1 , 2 , \cdots n \right)$ ，记范数为奇数的 $\overset {  } { a }$ 的个数为 $A _ { n }$ ，则 $A _ { 3 } =$ ； $A _ { 2 n } = \_$ （用含 $n$ 的式子表示， $\boldsymbol { n } \in \mathbf { N } ^ { * }$ ）

18.已知正整数m，n满足m<n≤24,若关于x的方程2-sin(mx)+2-sin(mx) 有实数解，则符合条件的 $\scriptstyle ( m , n )$ 共有_ 对.

19.对于 $n \in \mathbf { N } ^ { * }$ ，将 $n$ 表示为 $n = a _ { 0 } \times 2 ^ { k } + a _ { 1 } \times 2 ^ { k - 1 } + a _ { 2 } \times 2 ^ { k - 1 } \mathrm { ~ + ~ } + a _ { k - 1 } \times 2 ^ { 1 } + a _ { k } \times 2 ^ { 0 }$ ，当 $i = 0$ 时， $a _ { i } = 1$ .当 $1 \leq i \leq k$ 时，$a _ { i }$ 为0或1.记 $I ( n )$ 为上述表示中 $a _ { i }$ 为0的个数，（例如 $1 = 1 \times 2 ^ { 0 }$ ， $4 = 1 \times 2 ^ { 2 } + 0 \times 2 ^ { 1 } + 0 \times 2 ^ { 0 }$ ，故 $I \left( 1 \right) = 0$ ， $I \left( 4 \right) = 2 )$ 若 $\sum _ { n = 1 } ^ { i } a _ { n } = a _ { 1 } + a _ { 2 } + \mathrm { L } + a _ { i }$ ，则 $\sum _ { n = 1 } ^ { 1 2 7 } 2 ^ { I ( n ) } = \_$

20.5320的因数有 个，从小到大排列后，第24个因数为

21.若一个三位数同时满足： $\textcircled{1}$ 各数位的数字互不相同； $\textcircled{2}$ 任意两个数位的数字之和不等于9，则这样的三位数共有 个．(结果用数字作答）

22．已知集合 $H = \left\{ 2 0 1 9 , 1 2 , 6 , - 1 0 , - 5 , - 1 , 0 , 1 , 8 , 1 5 \right\}$ ，记集合 $H$ 的非空子集为 $M _ { 1 }$ 、 $M _ { 2 }$ 、L、 $M _ { 1 0 2 3 }$ ，且记每个子集中各元素的乘积依次为 $m _ { 1 } , m _ { 2 } , L , m _ { 1 0 2 3 }$ ，则 $m _ { 1 } + m _ { 2 } + \cdots + m _ { 1 0 2 3 }$ 的值为

23.已知 $( 1 + x ) ^ { 2 0 2 3 } = a _ { 0 } + a _ { 1 } x + a _ { 2 } x ^ { 2 } + a _ { 3 } x ^ { 3 } + \cdots + a _ { 2 0 2 3 } x ^ { 2 0 2 3 }$ ，则（1） $a _ { 0 } + a _ { 2 } + a _ { 4 } + \cdots + a _ { 2 0 2 0 } + a _ { 2 0 2 2 }$ 被3除的余数是_；(2） $a _ { 2 0 2 2 } + 2 a _ { 2 0 2 1 } + 3 a _ { 2 0 2 0 } + 4 a _ { 2 0 1 9 } + \cdots + 2 0 2 2 a _ { 1 } + 2 0 2 3 a _ { 0 } = \qquad \cdot$

24.类比排列数公式 $\operatorname { A } _ { n } ^ { r } = n { \big ( } n - 1 { \big ) } { \big ( } n - 2 { \big ) } \cdots { \big ( } n - r + 1 { \big ) }$ ，定义 $\mathbf { B } _ { x } ^ { n } = x { \bigl ( } x - 1 { \bigr ) } { \bigl ( } x - 2 { \bigr ) } \cdots { \bigl ( } x - n + 1 { \bigr ) }$ （其中 $n \in  { \mathbb { N } } ^ { * }$ ， $x \in \mathbb { R }$ ）将右边展开并用符号 $S \left( n , k \right)$ 表示 $x ^ { k }$ （ $1 \leq k \leq n$ ， $k \in \mathbb { N } ^ { * } )$ 的系数，得 $\mathrm { B } _ { x } ^ { n } = S \left( n , n \right) x ^ { n } + S \left( n , n - 1 \right) x ^ { n - 1 } + \cdots + S \left( n , \mathbb { 1 } \right) x ,$ 则：

(1） $S \left( n , 1 \right) = \qquad \quad :$ (2）若 $S \left( n , r \right) = a$ ， $S \left( n , r + 1 \right) = b$ （ $r \in \mathbb { N } ^ { * }$ ， $r + 1 \leq n \ )$ ，则 $S \left( n + 1 , r + 1 \right) = .$

25.设 $a$ ， $b$ $m \left( m > 0 \right)$ 为整数，若 $a$ 和 $b$ 被 $m$ 除得的余数相同，则称 $^ { a }$ 和 $b$ 对模 $m$ 同余，记为 $a \equiv b { \bigl ( } { \bmod { m } } { \bigr ) }$ 已知 $a = \mathrm { C } _ { 9 } ^ { 0 } + \frac { 1 } { 2 } \mathrm { C } _ { 9 } ^ { 1 } + \frac { 1 } { 3 } \mathrm { C } _ { 9 } ^ { 2 } + \cdots + \frac { 1 } { 1 0 } \mathrm { C } _ { 9 } ^ { 9 } + \frac { 7 } { 1 0 }$ $b \equiv a \left( { \bmod { 1 0 } } \right)$ ，则满足条件的正整数 $b$ 中，最小的两位数是

26.如图，由 $6 \times 6 = 3 6$ 个边长为1个单位的小正方形组成一个大正方形，某机器人从 $C$ 点出发，沿若小正方形的边走到 $D$ 点，每次可以向右走一个单位或者向上走一个单位，如果要求机器人不能接触到线段 $A B$ ，那么不同的走法共有_ 种.

![](images/f14825139f605dbdb29edf8a909d75da68717351708dfd11920ff5bdd1e988c7.jpg)

27.组合数 $C _ { 3 4 } ^ { 0 } + C _ { 3 4 } ^ { 2 } + C _ { 3 4 } ^ { 4 } + \cdots + C _ { 3 4 } ^ { 3 4 }$ 被9除的余数是

1．中国南北朝时期的著作《孙子算经》中，对同余除法有较深的研究.设 $a$ ， $b$ ， $m ( m > 0 )$ 为整数，若 $a$ 和 $^ { b }$ 被 $m$ 除得的余数相同，则称 $a$ 和 $b$ 对模 $m$ 同余，记为 $a = b { \bigl ( } { \mathrm { m o d } } m { \bigr ) }$ .若 $a = \mathrm { C } _ { 1 8 } ^ { 1 } \cdot 2 + \mathrm { C } _ { 1 8 } ^ { 2 } \cdot 2 ^ { 2 } + . . . + \mathrm { C } _ { 1 8 } ^ { 1 8 } \cdot 2 ^ { 1 8 } , a = b \big ( \mathrm { m o d } 1 0 \big ) ,$ 则 $b$ 的值可以是（）

A.2018 B.2020 C.2022 D.2024

2．回文联是我国对联中的一种，用回文形式写成的对联，既可顺读，也可倒读，不仅意思不变，而且颇具趣味.相传，清代北京城里有一家酒楼叫“天然居”，一次乾隆路过这家酒楼，称赞楼名的高雅，遂以楼名为题作对联，上联是：“客上天然居，居然天上客”纪晓岚对曰：“人过大佛寺，寺佛大过人”，乾隆微笑颔首，后“天然居”以此为门联，遂声名大噪.在数学中也有这样一类顺读与倒读都是同一个数的自然数，称之为：“回文数”如66,787，4334等，那么用数字1，2，3，4，5，6，7，8，9可以组成4位“回文数"的个数为（）

A.56个 B.64个 C.81个 D.90个

3．2013年华人数学家张益唐证明了孪生素数猜想的一个弱化形式．李生素数猜想是希尔伯特在1900年提出的23个问题之一，可以这样描述：存在无穷多个素数 $p$ ，使得 $p + 2$ 是素数，素数对 $( p , p + 2 )$ 称为孪生素数．在不超过26的素数中，随机选取两个不同的数，其中能够组成孪生素数的概率是（）

A.i18 B.4 c. D.1

4．欧几里得在《几何原本》中证明了算术基本定理：任何一个大于1的自然数 $N$ ，可以唯一分解成有限个素数的乘积，如果不考虑这些素数在乘积中的顺序，那么这个乘积形式是唯一的.记 $N = p _ { 1 } ^ { a _ { 1 } } \cdot p _ { 2 } ^ { a _ { 2 } } \cdots p _ { k } ^ { a _ { k } }$ （其中 $p _ { i }$ 是素数， $a _ { i }$ 是正整数， $1 \leq i \leq k$ ， $p _ { 1 } < p _ { 2 } < \cdots < p _ { k } \ \rangle$ ，这样的分解称为自然数 $N$ 的标准素数分解式.若 $N$ 的标准素数分解式为 $N = p _ { 1 } ^ { a _ { 1 } } \cdot p _ { 2 } ^ { a _ { 2 } } \cdots p _ { k } ^ { a _ { k } }$ ，则 $N$ 的正因子有 ${ \bigl ( } a _ { 1 } + 1 { \bigr ) } { \bigl ( } a _ { 2 } + 1 { \bigr ) } \cdots { \bigl ( } a _ { k } + 1 { \bigr ) }$ 个，根据以上信息，180的正因子个数为（）

A.6 B.12 C.13 D.18

5.如图为我国数学家赵爽（约3世纪初）在为《周髀算经》作注时验证勾股定理的示意图，现在替工5种颜色给其中5个小区域涂色，规定每个区域只涂一种颜色，相邻颜色不同，则不同的涂色方法种数为（）

![](images/22e8e452c603135514fd774c223f8776ad286302f1c07383fbb3b87cbec1085e.jpg)

A.120 B.420 C.300 D．以上都不对

6．根据历史记载，早在春秋战国时期，我国劳动人民就普遍使用算筹进行计数.算筹计数法就是用一根根同样长短和粗细的小棍子以不同的排列方式来表示数字，如图所示.如果用算筹随机摆出一个不含数字0的两位数，个位用纵式，十位用横式，则个位和十位上的算筹不一样多的概率为（）

纵式：丨II 式=三 123456789

A. 89 B. 6418 c. D.

7.我国古代典籍《周易》用“卦"描述万物的变化，每一“重卦”由从下到上排列的6个爻组成，爻分为阳爻“和阴爻“-一”，如图就是一重卦，在所有重卦中随机取一重卦，则该重卦恰有2个阳爻且2个阳爻不相邻的概率是（）

![](images/42cd55e8d8eb874229fb3e267114dd8145c9695064b0f2f03a368aacd54766ca.jpg)

A. $\frac { 1 } { 7 2 }$ B. C. 5-6 D. 23

8．上古时代神话传说中，伏羲通过黄河中浮出龙马身上的图案，与自己的观察，画出了“八卦”，而龙马身上的图案就叫作"河图”（如图1)，河图把一到十这十个数字分成五组，其口诀为：一六共宗，为水居北；二七同道，为火居南；三八为朋，为木居东；四九为友，为金居西；五十同途，为土居中，现从这十个数中随机抽取六个数，则能成为三组的概率是（）

![](images/e6fab0222f7bbf58871f7b85006bc5b19bc945a3ad07e6f1469f54c143368a66.jpg)  
图1

A. 1-5 B.1 C. 1 D. $\frac { 1 } { 2 5 2 }$ 10

9．算盘起源于中国，迄今已有 2600多年的历史，是中国古代的一项伟大的发明.在阿拉伯数字出现前，算盘是世界广为使用的计算工具.下图一展示的是一把算盘的初始状态，自右向左分别表示个位、十位、百位、千位…，上面的一粒珠子（简称上珠）代表5，下面的一粒珠子（简称下珠）代表1，五粒下珠的大小等同于一粒上珠的大小.例如，如图二，个位上拨动一粒上珠、两粒下珠，十位上拨动一粒下珠至梁上，代表数字17.现将算盘的个位、十位、百位、千位、万位分别随机拨动一粒珠子至梁上，则表示的五位数至多含3个5的情况有（）

![](images/37f7f999a9932083a1050839182af705d142839d37b5257544c3ea89b19d8646.jpg)

图一 图二

A.10种 B.25种 C.26种 D.27种

10.“杨辉三角”是中国古代数学文化的瑰宝之一，最早出现在南宋数学家杨辉于1261年所著的《详解九章算法》一书中，“杨辉三角"揭示了二项式系数在三角形数表中的一种几何排列规律，如图所示．下列关于“杨辉三角”的结论正确的是（）

![](images/ff557d4e3a68f3be2ae8c809bdfce7cf0784c44bc52efdc3e45a734afca6aa9a.jpg)

A. $\mathbf { C } _ { 3 } ^ { 2 } + \mathbf { C } _ { 4 } ^ { 2 } + \mathbf { C } _ { 5 } ^ { 2 } + \cdots + \mathbf { C } _ { 1 1 } ^ { 2 } = 2 2 0$ B．第2023行中从左往右第1011个数与第1012个数相等C.记第 $n$ 行的第 $i$ 个数为 $a _ { i }$ ，则 $\sum _ { i = 1 } ^ { n + 1 } 3 ^ { i - 1 } a _ { i } = 4 ^ { n }$ D.第30行中第12个数与第13个数之比为13：18

11.“宫、商、角、徵、羽”（读音为gong shangjuezhiyu）是我国五声音调中五个不同音的名称，类似现在简谱中的1，2，3，5，6，即宫等于1（Do)，商等于2（Re)，角等于3（Mi），徵等于5（So)，羽等于6（La)，亦称作五音．现在我们有三个徵，两个宫，两个羽，一共7个音符，把它们任意排列，恰好能组成《小星星》的旋律"宫宫徵徵羽羽徵”（即1155665）的概率是（）

A. 1-35 B. C. 210 D. 2

12．二项式定理，又称牛顿二项式定理，由艾萨克·牛顿提出．二项式定理可以推广到任意实数次幂，即广义二项式定理： $\vec { \lambda } \dag \mp \langle \ddagger { \hat { \mathcal { L } } } _ { \hat { \mathcal { L } } } { \hat { \mathcal { L } } } _ { \hat { \mathcal { S } } } { \hat { \mathcal { H } } } _ { \mathcal { L } } ( \mathbf { \mathcal { L } } , \mathbf { \Lambda } ( 1 + x ) ^ { \alpha } = 1 + \frac { \alpha } { 1 } \cdot x + \frac { \alpha ( \alpha - 1 ) } { 2 \times 1 } x ^ { 2 } + \cdots + \frac { \alpha ( \alpha - 1 ) \cdot \cdot ( \alpha - k + 1 ) } { k \times ( k - 1 ) \times \cdot \cdot \times 2 \times 1 } x ^ { \textbf {  { k } } _ { + } } \cdots ,$ (-1)(α-k+1）x+.，当x比较小的时候，取广义二项式定理的展开式的前两项可得： $( 1 + x ) ^ { \alpha } \approx 1 + \alpha \cdot x$ ，并且 $| x |$ 的值越小，所得结果就越接近真实数据，用这个方法计算 $\sqrt { 5 }$ 的近似值，可以这样操作： ${ \sqrt { 5 } } = { \sqrt { 4 + 1 } } = { \sqrt { 4 { \left( 1 + { \frac { 1 } { 4 } } \right) } } } = 2 { \sqrt { 1 + { \frac { 1 } { 4 } } } } \approx 2 \times ( 1 + { \frac { 1 } { 2 } } \times { \frac { 1 } { 4 } } ) = 2 . 2 5$ ，用这样的方法，估计 $\sqrt [ 3 ] { 2 5 }$ 的近似值约为（）

A.2.922 B.2.928 C. 2.926 D.2.930

# 二、多选题

13．“杨辉三角"是二项式系数在三角形中的一种几何排列，在中国南宋数学家杨辉1261年所著的《详解九章算法》一书中就有出现．如图所示，在“杨辉三角"中，除每行两边的数都是1外，其余每个数都是其"肩上"的两个数之和，例如第4行的6为第3行中两个3的和．则下列命题中正确的是（）

第0行 1  
第1行 11  
第2行 121  
第3行 1331  
第4行 14641  
第5行15101051

A．在“杨辉三角"第9行中，从左到右第7个数是84B.由“第 $n$ 行所有数之和为 $2 ^ { n }$ ”猜想： $\mathbf { C } _ { n } ^ { 0 } + \mathbf { C } _ { n } ^ { 1 } + \mathbf { C } _ { n } ^ { 2 } + \cdots + \mathbf { C } _ { n } ^ { n } = 2 ^ { n }$ C.在“杨辉三角”中，当 $n = 1 2$ 时，从第1行起，每一行的第2列的数字之和为66D．在"杨辉三角"中，第3行所有数字的平方和恰好是第6行的中间一项的数字

14．算盘起源于中国，迄今已有2600多年的历史，在电子计算机发明以前，算盘是广为使用的计算工具.图（1）展示的是一把算盘的初始状态，自右向左每一档分别表示个位、十位、百位、千位...上面的一粒珠子表示5，下面的一粒珠子表示1.例如图（2）中个位上拨动一粒上珠、两粒下珠，十位上拨动一粒下珠靠梁，表示数字17.现将初始状态的算盘上个位、十位、百位、千位、万位、十万位分别随机拨动一粒珠子靠梁，则可以表示能被3整除的六位数的个数为

![](images/fcb2b3cbb5fb25fa63f70c65d6ee004636523bf024609f4ab20fac909e98b001.jpg)  
图(1)

![](images/76541ed7dd771576b37804d03963b5be73bace729e83341417fe1939021b8a89.jpg)  
图（2)

15．龙马负图、神龟载书图象如图甲所示，数千年来被认为是中华传统文化的源头；其中洛书有云，神龟出于洛水，甲壳上的图象如图乙所示，其结构是戴九履一，左三右七，二四为肩，六八为足，以五居中，五方白圈皆阳数，四角黑点为阴数；若从阳数和阴数中分别随机抽出2个和1个，则不同选法的种数是 （用数字作答）.

![](images/3348fe4f7071bd8de0b895e3f678b5b9c789922cc47368311b2a731041adb52c.jpg)  
图甲  
图乙

16．费马大定理又被称为“费马最后的定理”，由17世纪法国数学家皮耶·德·费马提出．他断言当整数 $n > 2$ 时，关于 $x , \ y , \ z$ 的方程 $\ x ^ { n } + y ^ { n } = z ^ { n }$ 没有正整数解．他提出后，历经多人猜想辩证，最终在1995年被英国数学家安德鲁·怀尔斯彻底证明．甲同学对这个问题很感兴趣，他决定从集合 $A = \{ 1 , 2 , 3 , 4 , 5 \}$ 中的5个自然数中随机选两个数字分别作为方程 $x ^ { n } + y ^ { n } = z ^ { n }$ 中 $n$ 的指数，则方程 $x ^ { n } + y ^ { n } = z ^ { n }$ 存在正整数解的概率为

17．数论领域的四平方和定理最早由欧拉提出，后被拉格朗日等数学家证明．四平方和定理的内容是：任意正整数都可以表示为不超过四个自然数的平方和，例如正整数 $1 2 = 3 ^ { 2 } + 1 ^ { 2 } + 1 ^ { 2 } + 1 ^ { 2 } = 2 ^ { 2 } + 2 ^ { 2 } + 2 ^ { 2 } + 0 ^ { 2 }$ 设$2 5 = a ^ { 2 } + b ^ { 2 } + c ^ { 2 } + d ^ { 2 }$ ，其中 $a$ ，b， $^ { c }$ ， $d$ 均为自然数，则满足条件的有序数组 $( a , b , c , d )$ 的个数是

18．干支纪年是中国古代的一种纪年法.分别排出十天干与十二地支如下：

天干：甲乙丙丁戊己庚辛壬癸 地支：子丑寅卯辰巳午未申酉戌亥把天干与地支按以下方法依次配对：把第一个天干“甲"与第一个地支“子"配出“甲子”，把第二个天干“乙"与第二个地支“丑"配出“乙丑”，L，若天干用完，则再从第一个天干开始循环使用，若地支用完，则再从第一个地支开始循环使用.已知2022年是壬寅年，则 $1 3 ^ { 8 }$ 年以后是 年.

19．格点是指平面直角坐标系中横纵坐标均为整数的点.一格点沿坐标线到原点的最短路程为该点到原点的"格点距离”（如： $P ( - 2 , 1 )$ ，则点 $P$ 到原点的格点距离为 $2 + 1 = 3$ ）.格点距离为定值的点的轨迹称为“格点圆”，该定值称为格点圆的半径，而每一条最短路程称为一条半径.当格点半径为6时，格点圆的半径有_ 条（用数字作答）

20．卡特兰数是组合数学中一个常在各种计数问题中出现的数列．以比利时的数学家欧仁·查理·卡特兰（1814-1894）命名.历史上，清代数学家明安图（1692年-1763年）在其《割圜密率捷法》最早用到“卡特兰数”，远远早于卡塔兰．有中国学者建议将此数命名为"明安图数"或"明安图-卡特兰数”．卡特兰数是符合以下公式的一个数列： $a _ { n } = a _ { 0 } a _ { n - 1 } + a _ { 1 } a _ { n - 2 } + \dotsb + a _ { n - 1 } a _ { 0 }$ 且 $a _ { \scriptscriptstyle 0 } = 1$ ，如果能把公式化成上面这种形式的数，就是卡特兰数．卡特兰数是一个十分常见的数学规律，于是我们常常用各种例子来理解卡特兰数．比如：在一个无穷网格上，你最开始在 $\left( 0 , 0 \right)$ 上，你每个单位时间可以向上走一格，或者向右走一格，在任意一个时刻，你往右走的次数都不能少于往上走的次数，问走到 $\scriptstyle ( n , n )$ ， $0 { \leq } n$ 有多少种不同的合法路径．记合法路径的总数为 $b _ { n }$

(1）证明 $b _ { n }$ 是卡特兰数；  
(2）求 $b _ { n }$ 的通项公式.

# 考点题型方法总结

# 第6讲古典概型中的排列组合问题

# 【精选例题】

【例1】江南的周庄、同里、角直、西塘、鸟镇、南浔古镇，并称为"江南六大古镇”，是中国江南水乡风貌最具代表的城镇，它们以其深邃的历史文化底蕴、清丽婉约的水乡古镇风貌、古朴的吴侬软语民俗风情，在世界上独树一帜，驰名中外.这六大古镇中，其中在苏州境内的有3处.某家庭计划今年暑假从这6个古镇中挑选2个去旅游，则只选一个苏州古镇的概率为（）

A. 2-5 B. 3-5 C. 1-5 D. 4-5【例2】“村超"是贵州榕江县乡村足球超级联赛的简称，是该县的一项传统乡村体育赛事，“村超"深受当地人民的喜爱，也在2023年开始火爆全网.某体育新闻网站派出含甲、乙在内的4名记者前去A，B， $C$ 三个足球场报道"村超"赛事，要求每个足球场至少1名记者，则甲、乙分在不同足球场的概率为（）

A. B. C. 2-3 D. 5-6【例3】现有4名男生和3名女生计划利用假期到某地景区旅游，由于是旅游的旺季，他们在景区附近订购了一家酒店的5间风格不同的房间，并约定每个房间都要住人，但最多住2人，男女不同住一个房间，则女生甲和女生乙恰好住在同一间房的概率是（）

A4 B. c. D. 3   
10

【例4】6本不同的课本分给甲、乙、丙、丁四位同学，每位同学至少分得1本，则甲、乙分得的课本数量一样的概率是（）

A. 38 B. 5-3 C. 7-55 D. 3-5【例5】3个0和2个1随机排成一行，则2个1不相邻的概率为（）

A. 1-5 B. C. 35 D. 45【例6】2023年10月18日，第三届"一带一路"国际合作高峰论坛在北京举行．在"一带一路"欢迎晚宴上，我国拿出特有的美食、美酒款待大家，让国际贵宾们感受中国饮食文化、茶文化、酒文化．这次晚宴菜单中有"全家福”、“沙葱牛肉”、“北京烤鸭”、“什锦鲜蔬”、“冰花锅贴”、“蟹黄烧麦”、“天鹅酥”、“象形枇杷”．假设在上菜的过程中服务员随机上这8道菜（每次只上一道菜)，则“沙葱牛肉”、“北京烤鸭"相邻上的概率为（）

A. B. C. 1-3 D. 1-4

# 选择性必修第三册

【例7】2013年华人数学家张益唐证明了孪生素数（素数即质数）猜想的一个弱化形式.素数猜想是希尔伯特在1900年提出的23个问题之一，可以这样描述：存在无穷个素数 $p$ ，使得 $p + 2$ 是素数，素数对 $( p , p + 2 )$ 称为孪生素数.则从不超过18的素数中任取两个素数，这两个素数组成孪生素数对的概率为（）

A. 1-4 B. 328 c.1 D. 5-28【例8】（多选题）四位同学各在周六、周日两天中任选一天参加社区公益活动，则（）

A．四位同学选在同一天参加公益活动的概率为 $\frac { 1 } { 8 }$ B．周六两位同学，周日两位同学参加公益活动的概率为 $\frac { 3 } { 8 }$ C．周六、周日都有同学参加公益活动的概率为4D．周六一位同学，周日三位同学参加公益活动的概率为 $\frac { 1 } { 4 }$ 【例9】2023年10月18日，第三届“一带一路"国际合作高峰论坛在北京举行．在“一带一路"欢迎晚宴上，我国拿出特有的美食、美酒款待大家，让国际贵宾们感受中国饮食文化、茶文化、酒文化．这次晚宴菜单中有“全家福沙葱牛肉"北京烤鸭”什锦鲜蔬”冰花锅贴"蟹黄烧麦”天鹅酥"象形枇杷”.假设在上菜的过程中服务员随机上这八道菜（每次只上一道菜)，则“沙葱牛肉"北京烤鸭"相邻的概率为

【例10】某自媒体视频博主准备分5期录播北京烤鸭、上海生煎包、西安肉夹馍、武汉热干面、广州早茶这5种中国美食（每期录播一种)，则上海生煎包不在第一期录播，且北京烤鸭在最后一期录播的概率为

# 【跟踪训练】

1.我校甲、乙两名同学同时从湖心路口总站乘坐801路公交上学，准备在金湖公园、清溪路东和金湾一中这3个站点中的某个站点下车，若他们在这3个站点中的某个站点下车是等可能的，则甲、乙两名同学在不同站点下车的概率为（）

A. 1-4 B. 1-3 C. 2-3 D. 3-4

# 选择性必修第三册

2.2023年6月25日19时，随着最后一场比赛终场哨声响起，历时17天的.2023年凉山州首届"火洛杯"禁毒防艾男子篮球联赛决赛冠军争夺赛在凉山民族体育馆内圆满闭幕，为进一步展现凉山男儿的精神风貌主办方设置一场扣篮表演，分别由西昌市、冕宁县、布拖县、昭觉县4个代表队每队各派1名球员参加扣篮表演，则西昌代表队队员扣篮表演不在第一位且不在最后一位的概率为（）

A. B c.4 D.

3.某人忘记了电话号码的最后一个数字，随意拨号，则拨号不超过两次而接通电话的概率为

A. 910 B.1 C. 1-5 D. 1   
10

4．2023年9月，我国成功地举办了“杭州亚运会”亚运会期间，某场馆要从甲、乙、丙、丁、戊5名音效师中随机选取3人参加该场馆决赛的现场音效控制，则甲、乙至少有一人被选中的概率为

5.为了筹办某运动会开幕式，导演组从某高校的6名男生代表和6名女生代表中选取8人加入开幕式志愿者团队．在志愿者招募的过程中，为了平衡男女比例，要求本次选取的8人中至少有3名女生，则选取的8人中男女生人数均等的概率为

6.“北依长江，南临太湖、江苏之南，明珠无锡.总要来趟无锡吧”某游客从黿头渚、梅园、蠡园、锡惠公园、灵山胜境、拈花湾六个景点中任选三个进行游览，则他选中黿头渚的概率为

7.2023年8月，某志愿者团队从A，B， $C$ ， $D$ ， $E$ ， $F$ 这6名志愿者中随机选取4名志愿者去河北参加抗洪抢险，则A，B， $C$ 中至少有2人被选中的概率为

8．国家鼓励中小学校开展课后服务，某中学为了搞好课后服务工作，教务科组建了一批社团，学生们都能积极选择自己喜欢的社团.目前话剧社团、书法社团、舞蹈社团、朗诵社团分别还可以接收1名学生，恰好甲、乙、丙、丁4名同学前来教务科申请加入，按学校规定每人只能加入一个社团，则甲进朗诵社团，乙进书法社团或舞蹈社团的概率为

9．近日，在2023年全国中学生数学奥林匹克竞赛决赛中，某校有4位学生获得金牌或银牌，破格入围了清华大学与北京大学的强基计划，这4位学生都可以在这2所大学中任选1所填报，则填报这2所大学的人数相同的概率为_

10．甲，乙等5人站成一排，则甲，乙相邻，且甲在乙左侧的概率为

# 第7讲条件概率合全概率公式及应用

# 【考点分析】

# 考点一、条件概率

$\textcircled{1}$ 条件概率  
一般地,当事件 $B$ 发生的概率大于0时（即 $P ( B ) { > } 0 )$ ，已知事件 $B$ 发生的条件下事件 $A$ 发生的概率，称为条件概率，$P ( A | B )$ 阻 $P ( A | B ) { = } \frac { P \big ( A \cap B \big ) } { P \big ( B \big ) } ,$ $\textcircled{2}$ 概率的乘法公式  
由条件概率的定义，对任意两个事件A与B，若P（A） ${ > } 0$ ，则 $\mathrm { \bf P }$ （AB） $\mathrm { \Phi = } \mathrm { P }$ （A）P（BA）.我们称上式为概率的乘法公式（multiplicationformula）.  
$\textcircled{3}$ 条件概率的性质  
条件概率只是缩小了样本空间，因此条件概率同样具有概率的性质.  
设P（A） ${ > } 0$ ，则  
（1）P $( \Omega | \mathrm { A } ) = 1$   
（2）如果B和C是两个互斥事件，则 $\mathrm { P }$ （BUC|A） $\mathrm { \Gamma = } \mathrm { P }$ （B|A) $+ \mathrm { P }$ (C|A）;  
（3）设B和 $\overline { B }$ 互为对立事件，则 $\mathrm { ~ P ~ } ( \overline { { B } } | \mathbf { A } ) = 1 - \mathrm { P }$ （B|A）.

![](images/4dd4eef7c343f30965196b79760ce2723c93c264b71953abb2b91bcc2278ac82.jpg)

# 考点二、全概率公式

$\textcircled{1}$ 全概率公式

一般地,设 $A _ { 1 } , A _ { 2 } , \ldots , A _ { n }$ 是一组两两互斥的事件， $A _ { 1 } \cup A _ { 2 } \cup \ldots \cup A _ { n } { = } \Omega$ ，且 $P ( A _ { i } ) { > } 0$ ， $i = 1 , 2$ ，...，n，则对任意的事$_ { n }$   
件 $B \subseteq { \mathcal { Q } }$ ，有 $\begin{array} { r } { \mathrm { P } ( \mathbf { B } ) { = } \sum P ( A _ { i } ) P ( B | A _ { i } ) } \end{array}$ i1

我们称上面的公式为全概率公式.

![](images/b46d51e72b2d83eacc26bc9b22fe2a2cf8e2744f3c34597438c235f892f86a58.jpg)

$\textcircled { 2 } ^ { * }$ 贝叶斯公式：一般地，设 $A _ { 1 , } A _ { 2 } ,$ …， $A _ { n }$ 是一组两两互斥的事件，有 $A _ { 1 } \cup A _ { 2 } \cup \ldots \cup A _ { n } = \Omega$ ，且P $( A _ { i } ) > 0$ $\mathrm { i } = 1 , 2 , . . . , \Pi ,$ 则对任意的事件 ${ \mathrm { B } } \subseteq { \Omega }$ $P ( B ) > 0$ 有

$$
P ( B \mid \mathrm { A } _ { i } ) = \frac { \mathrm { P } \left( \mathrm { A } _ { i } \right) P ( B \mid \mathrm { A } _ { i } ) } { P ( B ) } { = } \frac { \mathrm { P } \left( \mathrm { A } _ { i } \right) P ( B \mid \mathrm { A } _ { i } ) } { \displaystyle \sum _ { i = 1 } ^ { n } \mathrm { P } \left( \mathrm { A } _ { i } \right) P ( B \mid \mathrm { A } _ { i } ) } , \mathrm { i = 1 , ~ 2 , . . . , n }
$$

# 选择性必修第三册

题型一：条件概率的计算

# 【精选例题】

【例1】小明每天上学途中必须经过2个红绿灯，经过一段时间观察发现如下规律：在第一个红绿灯处遇到红灯的概率是 $\frac 1 3$ ，连续两次遇到红灯的概率是 $\frac { 1 } { 4 }$ 则在第一个红绿灯处小明遇到红灯的条件下，第二个红绿灯处小明也遇到红灯的概率为（）

A. 2-3 B. c.4 D. 113

【例2】某地区气象台统计，该地区下雨的概率是 15 刮风的概率为 $\frac { 2 } { 1 5 }$ ，在下雨天里，刮风的概率为 $\frac { 3 } { 8 }$ 则既刮风又下雨的概率为（）

A. 34 B. 35 c $\frac { 1 } { 1 0 }$ D $\frac { 1 } { 2 0 }$

【例3】从编号为1-20的20张卡片中依次不放回地抽出两张，记A：第一次抽到数字为6的倍数， $B$ ：第二次抽到的数字小于第一次，则 $P ( B \mid A ) =$ （）

A.8 B. 11 c. D.1

【例4】盒子里有1个红球与 $n$ 个白球，随机取球，每次取1个球，取后放回，共取2次.若至少有一次取到红球的条件下，两次取到的都是红球的概率为 $\frac { 1 } { 9 }$ ， 则 $n = \mathrm { ~ ( ~ ) ~ }$

A.3 B.4 C.6 D.8

【例5】在医学生物学试验中，经常以果蝇作为试验对象，一个关有6只果蝇的笼子里，不慎混入了两只苍蝇（此时笼内共有8只蝇子：6只果蝇和2只苍蝇），只好把笼子打开一个小孔，让蝇子一只一只地往外飞，直到两只苍蝇都飞出，再关闭小孔.记事件 $A _ { k }$ 表示“第 $k$ 只飞出笼的是苍蝇”， $k = 1 , 2 , \cdots , 8$ ，则 $P \left( A _ { 5 } \mid A _ { 2 } \right)$ 为（）

A. B. 1-6 c. D.

【例6】（多选题）甲罐中有5个红球，2个白球和3个黑球，乙罐中有4个红球，3个白球和3个黑球．先从甲罐中随机取出一球放入乙罐，分别以 $A _ { 1 }$ ， $A _ { 2 }$ 和 $A _ { 3 }$ 表示由甲罐取出的球是红球，白球和黑球的事件；再从乙罐中随机取出一球，以 $B$ 表示由乙罐取出的球是红球的事件，则下列结论中正确的是（）

A $P ( B ) = \frac { 2 } { 5 }$ B. $P ( B \left| A _ { 1 } \right. ) = \frac { 5 } { 1 1 }$ C.事件 $A _ { 1 }$ 与事件 $B$ 不相互独立 D $P ( B \vert A _ { 2 } ) = P ( B \vert A _ { 3 } ) = \frac { 4 } { 1 1 }$

【例7】已知 $P ( A \mid B ) = P ( B \mid A ) = \frac { 1 } { 2 }$ ， $P ( \overline { A } ) = \frac { 3 } { 4 }$ 则 $P ( B ) =$

【例8】甲、乙和另外5位同学站成两排拍照，前排3人，后排4人.若每个人都随机站队，且前后排不认为相邻，则在甲、乙站在同一排的条件下，两人不相邻的概率为（）

A. 54 B. 4-9 C. 5-9 D. 5-6

【例9】某次抽奖活动共有50张奖券，其中5张写有“中奖”字样，抽完的奖券不再放回.若甲抽完之后乙再抽.

(1)求在甲中奖的条件下，乙中奖的概率；   
(2)证明：甲中奖的概率与乙中奖的概率相等.

【例10】今年春季新型冠状病毒肺炎疫情又有爆发趋势，上海医疗资源和患者需求之间也存在矛盾，海安决定支持上海市.在接到上级通知后，某医院部门马上召开动员会，迅速组织队伍，在报名请战的6名医生（其中男医生4人、女医生2人）中，任选3人奔赴上海新冠肺炎防治一线.

(1)求所选3人中恰有1名女医生的概率； (2)设"男医生甲被选中"为事件 $A$ ，“女医生乙被选中"为事件 $B$ ，求 $P \left( B \right)$ 和 $P \left( B { \big \vert } A \right)$

# 【题型专练】

1.已知抽奖盒中装有大小形状完全相同的奖票12张，其中一等奖2张、二等奖4张、三等奖6张.甲每次从中随机抽取一张奖票且不放回，则在他第一次抽到的是一等奖的前提下，第二次抽到三等奖的概率为（）

A. B. C. 6Ⅱ D.

2．有歌唱道：“江西是个好地方，山清水秀好风光."现有甲、乙两位游客慕名来到江西旅游，准备从庐山、三清山、龙虎山和明月山四个著名旅游景点中随机选择一个景点游玩，记事件A为“甲和乙至少一人选择庐山”，事件 $B$ 为“甲和乙选择的景点不同”，则 $P ( B { | } A ) = ( )$

A. 716 B. 78 c. D. 6-7

3．如图，用 $K$ 、 $A _ { 1 }$ 、 $\boldsymbol { \mathcal { A } } _ { 2 }$ 三类不同的元件连接成一个系统，当 $K$ 正常工作且 $A _ { 1 }$ 、 $A _ { 2 }$ 至少有一个正常工作时，系统正常工作，已知 $K$ 、 $A _ { 1 }$ 、 $A _ { 2 }$ 正常工作的概率依次是 $\frac { 1 } { 2 } 、 \frac { 2 } { 3 } 、 \frac { 2 } { 3 }$ ， 已知在系统正常工作的前提下，求只有 $K$ 和$A _ { \mathfrak { r } }$ 正常工作的概率是（）

![](images/c34f7a88864d4cb9e6e388deade8e9820c929d08f1576fbfe916d4804a0f9342.jpg)

A. 49 B. c.4 D. 1-9

4．某科技公司联欢会进行抽奖活动，袋中装有标号为1，2，3的大小、质地完全相同的3个小球，每次从袋中随机摸出1个球，记下它的号码，放回袋中，这样连续摸三次.规定"三次记下的号码都是2"为一等奖.已知小张摸球“三次记下的号码之和是6”，此时小张能得一等奖的概率为（）

A B. c.1 D

5.（多选题）将甲、乙、丙、丁4名医生随机派往 $\textcircled{1}$ ， $\textcircled{2}$ ， $\textcircled{3}$ 三个村庄进行义诊活动，每个村庄至少派1名医生，A表示事件医生甲派往 $\textcircled{1}$ 村庄”； $B$ 表示事件“医生乙派往 $\textcircled{1}$ 村庄”； $C$ 表示事件“医生乙派往 $\textcircled{2}$ 村庄”，则（）

A.事件A与 $B$ 相互独立 B.事件A与 $C$ 不相互独立C $P \left( B { \mid } A \right) = \frac { 5 } { 1 2 }$ $\mathrm { D } . P \left( C 1 A \right) = \frac { 5 } { 1 2 }$

6．为了给学生树立正确的劳动观，使学生懂得劳动的伟大意义，某班从包含甲、乙的6名学生中选出3名参加学校组织的劳动实践活动，在甲被选中的情况下，乙也被选中的概率为（）

A B. 25 C. 3-5 D. 314

7.（多选题）全球有 $0 . 5 \%$ 的人是高智商，他们当中有 $9 5 \%$ 的人是游戏高手，在非高智商人群中， $9 5 \%$ 的人不是游戏高手．下列说法正确的有（）

A．全球游戏高手占比不超过 $10 \%$ B．某人既是游戏高手，也是高智商的概率低于 $0 . 1 \%$ C．如果某人是游戏高手，那么他也是高智商的概率高于 $8 \%$ D．如果某人是游戏高手，那么他也是高智商的概率低于 $8 . 5 \%$

8．已知一个二胎家庭中有一个男孩，则这个家庭中有女孩的概率为（）

A $\frac { 1 } { 2 }$ B. 1-3 c. D.4

9.足球运动，最早的起源在中国.在春秋战国时期，就出现了“蹴鞠"或名“塌鞠”某足球俱乐部随机调查了该地区  
100位足球爱好者的年龄，得到如下样本数据频率分布直方图.

![](images/ed686616249c4da1da34fe8dac0b8bb4f32ea4b94f0e8818224e994f5882d7db.jpg)

(1)估计该地区足球爱好者的平均年龄：（同一组数据用该区间的中点值作代表）

(2)估计该地区足球爱好者年龄位于区间[20,60)的概率；  
(3)已知该地区足球爱好者占比为 $21 \%$ ，该地区年龄位于区间[10,20）的人口数占该地区总人口数的 $35 \%$ ，从该地区任选1人，若此人的年龄位于区间[10,20)，求此人是足球爱好者的概率.

# 题型二：条件概率的证明

【例1】从有3个红球和4个蓝球的袋中，每次随机摸出1个球，摸出的球不再放回，记A表示事件“第i次摸到红球”， $i = 1 , 2 , \cdots , 7$

(1)求第一次摸到蓝球的条件下第二次摸到红球的概率；  
（2）记 $P \left( A _ { 1 } A _ { 2 } A _ { 3 } \right)$ 表示 $A _ { 1 }$ ， $A _ { 2 }$ ， $A _ { 3 }$ 同时发生的概率， $P { \left( { { A _ { 3 } } | { A _ { 1 } } { A _ { 2 } } } \right) }$ 表示已知 $A _ { 1 }$ 与 $A _ { 2 }$ 都发生时 $A _ { 3 }$ 发生的概率.$\textcircled{1}$ 证明： $P \left( \mathcal { A } _ { 1 } A _ { 2 } A _ { 3 } \right) = P \left( A _ { 1 } \right) P \left( A _ { 2 } \middle | A _ { 1 } \right) P \left( A _ { 3 } \middle | A _ { 1 } A _ { 2 } \right) ;$   
$\textcircled{2}$ 求 $P \left( A _ { 3 } \right)$ ：

【例2】一医疗团队为研究某地的一种地方性疾病与当地居民的卫生习惯（卫生习惯分为良好和不够良好两类）的关系，在已患该疾病的病例中随机调查了100例（称为病例组)，同时在未患该疾病的人群中随机调查了100人（称为对照组），得到如下数据：

<html><body><table><tr><td></td><td>不够良好</td><td>良好</td></tr><tr><td>病例组</td><td>40</td><td>60</td></tr><tr><td>对照组</td><td>10</td><td>90</td></tr></table></body></html>

从该地的人群中任选一人， $A$ 表示事件“选到的人卫生习惯不够良好”， $B$ 表示事件“选到的人患有该疾病” $\frac { P ( B \mid A ) } { P ( \overline { { B } } \mid A ) }$ 与 $\frac { P ( B \mid { \overline { { A } } } ) } { P ( { \overline { { B } } } \mid { \overline { { A } } } ) }$ 的比值是卫生习惯不够良好对患该疾病风险程度的一项度量指标，记该指标为 $R$ （

(1）证明：R= $R = { \frac { P ( A \mid B ) } { P ( { \overline { { A } } } \mid B ) } } \cdot { \frac { P ( { \overline { { A } } } \mid { \overline { { B } } } ) } { P ( A \mid { \overline { { B } } } ) } }$ (2)利用该调查数据，给出 $P ( A | B ) , P ( A | \overline { { B } } )$ 及 $R$ 的估计值.

# 【题型专练】

1.某大学有A， $B$ 两个餐厅为学生提供午餐与晚餐服务，甲、乙两位学生每天午餐和晚餐都在学校就餐，近100天选择餐厅就餐情况统计如下：

<html><body><table><tr><td>选择餐厅情况（午餐，晚餐）</td><td>(AA)</td><td>(AB)</td><td>(B,A)</td><td>(B,B)</td></tr><tr><td>甲</td><td>30天</td><td>20天</td><td>40天</td><td>10天</td></tr><tr><td>乙</td><td>20天</td><td>25天</td><td>15天</td><td>40天</td></tr></table></body></html>

假设甲、乙选择餐厅相互独立，用频率估计概率.

(1)分别估计一天中甲午餐和晚餐都选择 $A$ 餐厅就餐的概率，乙午餐和晚餐都选择B餐厅就餐的概率；(2）假设 $M$ 表示事件 $\spadesuit$ 餐厅推出优惠套餐”， $N$ 表示事件“某学生去 $A$ 餐厅就餐”， $P ( M ) > 0$ ，一般来说在推出优惠套餐的情况下学生去该餐厅就餐的概率会比不推出优惠套餐的情况下去该餐厅就餐的概率要大，证明：$P \left( M \left| N \right. \right) > P \left( M \left| \overline { { N } } \right. \right) .$

选择性必修第三册题型三：全概率公式及应用

【例1】已知 $P \left( B \right) = 0 . 3$ ， $P \left( B \mid A \right) = 0 . 9$ ， $P \left( B | \ { \overline { { A } } } \right) = 0 . 2$ ，则 $P ( A ) =$ （）

A $\frac { 1 } { 7 }$ B C.0.33 D.0.1【例2】盒中有 $a$ 个红球， $b$ 个黑球，今随机地从中取出一个，观察其颜色后放回，并加上同色球c个，再从盒中抽取一球，则第二次抽出的是红球的概率是（）

A. $\frac { a } { a + b + c }$ $\begin{array} { l l l } { \mathrm { B . } } & { \displaystyle \frac { a } { a + b } \qquad } & { \mathrm { C . } \frac { a } { a + c } \qquad } & { \mathrm { D . } \frac { a + c } { a + b + c } } \end{array}$ 【例3】（多选题）一个不透明的纸箱中放有大小、形状均相同的10个小球，其中白球6个、红球4个，现无放回分两次从纸箱中取球，第一次先从箱中随机取出1球，第二次再从箱中随机取出2球，分别用 $A _ { 1 }$ ， $A _ { 2 }$ 表示事件“第一次取出白球，第一次取出红球”；分别用 $B$ ， $C$ 表示事件“第二次取出的都为红球”，“第二次取出两球为一个红球一个白球”则下列结论正确的是（）

$$
. P ( B | A _ { 1 } ) = \frac { 1 } { 6 } \qquad \mathrm {  ~ B . } \quad P ( C | A _ { 2 } ) = \frac { 1 } { 2 } \qquad \mathrm {  ~ C . } \quad P ( B ) = \frac { 1 } { 3 } \qquad \mathrm {  ~ D . } \quad P ( A _ { 1 } C ) = \frac { 1 } { 5 }
$$

【例4】一道单项选择题有4个答案，要求学生将正确答案选择出来.某考生知道正确答案的概率为 $\frac { 1 } { 5 }$ 在乱猜时，4个答案都有机会被他选择，若他答对了，则他确实知道正确答案的概率是

【例5】现有 $n$ ( $n > 2$ ， $\boldsymbol n \in \boldsymbol N ^ { * }$ ）个相同的袋子，里面均装有 $n$ 个除颜色外其他无区别的小球，第 $k$ ( $k { = } 1$ ，2，3，...， $n$ ）个袋中有 $k$ 个红球， $n - k$ 个白球.现将这些袋子混合后，任选其中一个袋子，并且从中连续取出三个球（每个取后不放回)，若第三次取出的球为白球的概率是 $\frac { 7 } { 1 6 }$ 则 $n { = }$

【例6】（1）已知A与 $B$ 独立，且 $P ( { \overline { { A } } } \mid B ) = { \frac { 7 } { 1 0 } }$ 求 $P ( A )$

（2）已知 $P \left( { \overline { { A } } } \right) = { \frac { 1 } { 2 } }$ ， $P \left( { \overline { { B } } } { \big | } A \right) = { \frac { 2 } { 3 } }$ ， $P \left( B { \sqrt { A } } \right) = { \frac { 1 } { 4 } }$ 求 $P ( { \overline { { B } } } )$ ， $P ( { \overline { { A } } } \mid B )$ ：

# 选择性必修第三册

【例7】某学校为了迎接党的二十大召开，增进全体教职工对党史知识的了解，组织开展党史知识竞赛活动并以支部为单位参加比赛.现有两组党史题目放在甲、乙两个纸箱中，甲箱有5个选择题和3个填空题，乙箱中有4个选择题和3个填空题，比赛中要求每个支部在甲或乙两个纸箱中随机抽取两题作答.每个支部先抽取一题作答，答完后题目不放回纸箱中，再抽取第二题作答，两题答题结束后，再将这两个题目放回原纸箱中.

(1)如果第一支部从乙箱中抽取了2个题目，求第2题抽到的是填空题的概率；  
(2)若第二支部从甲箱中抽取了2个题目，答题结束后错将题目放入了乙箱中，接着第三支部答题，第三支部抽取第一题时，从乙箱中抽取了题目.已知第三支部从乙箱中取出的这个题目是选择题，求第二支部从甲箱中取出的是2个选择题的概率.

# 【题型专练】

1．甲口袋中有3个红球，2个白球和5个黑球，乙口袋中有3个红球，3个白球和4个黑球，先从甲口袋中随机取出一球放入乙口袋，分别以 $A _ { 1 } , A _ { 2 }$ 和 $A _ { 3 }$ 表示由甲口袋取出的球是红球，白球和黑球的事件；再从乙口袋中随机取出一球，以 $B$ 表示由乙口袋取出的球是红球的事件，则下列结论中正确的是（）

$P \left( B { \big | } A _ { 2 } \right) = { \frac { 4 } { 1 1 } }$ B.事件 $A _ { 1 }$ 与事件 $B$ 相互独立 C. $P \left( A _ { 3 } \vert B \right) = \frac { 1 } { 2 }$ D. $P ( B ) = \frac { 3 } { 1 0 }$

2.（多选题）甲盒子中有5个红球，2个白球和3个黑球，乙盒子中有4个红球，3个白球和3个黑球.先从甲盒子中随机取出一球放入乙盒子，分别以 $A _ { 1 }$ ， $A _ { 2 }$ 和 $A _ { 3 }$ 表示由甲盒子取出的球是红球，白球和黑球的事件；再从乙盒子中随机取出一球，以 $B$ 表示由乙盒子取出的球是红球的事件，则下列结论中正确的是（）

$A _ { 3 }$ 是两互斥的事件B。 $P \left( B \right) = \frac { 2 } { 5 }$ c，维件 $B$ 与事件 $A _ { 1 }$ 相互独立D. $P \left( B { \big | } A _ { 1 } \right) = { \frac { 5 } { 1 1 } }$

3.（多选题）已知编号为1，2，3的三个盒子，其中1号盒子内装有两个1号球，一个2号球和一个3号球；2号盒子内装有两个1号球，一个3号球；3号盒子内装有三个1号球，两个2号球．若第一次先从1号盒子内随机抽取1个球，将取出的球放入与球同编号的盒子中，第二次从该盒子中任取一个球，则下列说法正确的是

A．在第一次抽到2号球的条件下，第二次抽到1号球的概率为  
B．第二次抽到3号球的概率为 $\frac { 1 1 } { 4 8 }$   
C．如果第二次抽到的是1号球，则它来自2号盒子的概率最大  
D．如果将5个不同的小球放入这三个盒子内，每个盒子至少放1个，则不同的放法有300种

4．盒子中有大小与质地相同的5个红球和4个白球，从中随机取1个球，观察其颜色后放回，并同时放入与其相同颜色的球3个，再从盒子中取1个球.则第二次取出的球是白色的概率为

5．两台车床加工同样的零件，第一台出现废品的概率是0.03，第二台出现废品的概率是0.02.加工出来的零件放在一起，并且已知第一台加工的零件比第二台加工的零件多一倍.

(1)求任意取出1个零件是合格品的概率；  
(2)如果任意取出的1个零件是废品，求它是第二台车床加工的概率.

6．两批同种规格的产品，第一批占 $40 \%$ ，次品率为 $5 \%$ ；第二批占 $60 \%$ ，次品率为 $4 \%$ ，将两批产品混合，从混合产品中任取一件.

(1)求这件产品是次品的概率；  
(2)已知取到的是次品，求它取自第一批产品的概率.

7.“青团"是江南人家在清明节吃的一道传统点心，据考证“青团"之称大约始于唐代，已有100多年的历史．现有甲、乙两个箱子装有大小、外观均相同的“青团”，已知甲箱中有4个蛋黄馅的“青团”和3个肉松馅的“青团”，乙箱中有3个蛋黄馅的“青团”和2个肉松馅的青团”.

（1)若从甲箱中任取2个“青团”，求这2个“青团”馅不同的概率；

(2)若先从甲箱中任取2个“青团"放入乙箱中，然后再从乙箱中任取1个“青团”，求取出的这个“青团”是肉松馅的概率.

# 第8讲离散型随机变量的分布列及其性质

# 【考点分析】

# 考点一离散型随机变量的分布列

$\textcircled{1}$ 随机变量：如果随机试验的结果可以用一个变量来表示，那么这样的变量叫做随机变量.常用希腊字母、n等表示.  
$\textcircled{2}$ 离散型随机变量：对于随机变量可能取的值，可以按一定次序一一列出，这样的随机变量叫做离散型随机变量.  
$\textcircled{3}$ 连续型随机变量：对于随机变量可能取的值，可以取某一区间内的一切值，这样的变量就叫做连续型随机变量.

$\textcircled{4}$ 分布列：设离散型随机变量可能取得值为 $x _ { 1 } , x _ { 2 } , \ldots , x _ { 3 } , \ldots , \xi$ 取每一个值 $x _ { i } ( i { = } 1 , 2 , \ldots )$ 的概率为 $P ( \xi = x _ { i } ) = p _ { i }$ ，则称表

<html><body><table><tr><td></td><td>X1</td><td>x2</td><td></td><td>xi</td><td></td></tr><tr><td>P</td><td>P</td><td>P</td><td></td><td>Pi</td><td>·</td></tr></table></body></html>

为随机变量的概率分布，简称的分布列.

$\textcircled{5}$ 分布列的两个性质：任何随机事件发生的概率都满足： $0 \leq P ( A ) \leq 1$ ，并且不可能事件的概率为0，必然事件的概率为1．由此你可以得出离散型随机变量的分布列都具有下面两个性质：(1) $P _ { i } \ge 0 , i = 1 , 2 , 3 \cdots$ $( 2 ) P _ { 1 } + P _ { 2 } + \cdots + P _ { i } = 1 , i = 1 , 2 , \cdots .$

题型一：随机变量概念

# 【精选例题】

【例1】甲、乙两队在一次对抗赛的某一轮中有3个抢答题，比赛规定：对于每一个题，没有抢到题的队伍得0分，抢到题并回答正确的得1分，抢到题但回答错误的扣1分（即得-1分）.若 $X$ 是甲队在该轮比赛获胜时的得分（分数高者胜），则 $X$ 的所有可能取值之和是（）

A.3 B.4 C.5 D.6

【例2】袋中有3个白球、5个黑球，从中任取2个，则可以作为随机变量的是（）.

A．至少取到1个白球B．取到白球的个数C．至多取到1个白球D．取到的球的个数

【例3】甲、乙两班进行足球对抗赛，每场比赛赢了的队伍得3分，输了的队伍得0分，平局的话，两队各得1分，共进行三场．用 $\xi$ 表示甲的得分，则 $\left\{ \xi = 3 \right\}$ 表示（）.

A．甲赢三场B．甲赢一场、输两场C．甲、乙平局三次D．甲赢一场、输两场或甲、乙平局三次

【例4】袋中装有除颜色外，质地大小完全相同的4个小球，其中有1个红球、3个白球，从中任意取出1个观察颜色，取后不放回，如果取出的球的颜色是红色，则停止取球，如果是白色，则继续取球，直到取到红球时停止，记停止时的取球次数为§，则§所有可能取值的集合为 _， $\xi = 2$ 的意义为

# 【题型专练】

1．先后抛掷一个骰子两次，记随机变量为两次掷出的点数之和，则的取值集合是（）

A.{1，2,3，4,5，6} B.{2，3，4，5，6，7}C.{2,4,6，8，10，12} D.{2，3，4，5，6，7，8，9,10，11，12}

2．袋中装有5个红球、10个黑球.每次随机抽取1个球后，若取得黑球后则另外换1个红球放回袋中，直到取得红球为止.若抽取的次数为 $X$ ，则表示事件“放回3个红球"的是（）

A. $X = 2$ B. $X = 3$ C. $X = 4$ D. $X = 5$

3.（多选题）一副扑克牌共有54张牌，其中52张是正牌，另2张是副牌（大王和小王），从中任取4张，则随机变量可能为（）

A．所取牌数 B．所取正牌和大王的总数C．这副牌中正牌数D．取出的副牌的个数

4．从4名男生和2名女生中任选3人参加演讲比赛，设随机变量 $X$ 表示所选3人中女生的人数，则 $\left\{ X \leq 1 \right\}$ 表示

# 题型二：离散型随机变量与连续型随机变量

【例1】下面给出的四个随机变量中是离散型随机变量的为（）

$\textcircled{1}$ 高速公路上某收费站在半小时内经过的车辆数 $X _ { 1 }$   
$\textcircled{2}$ 一个沿直线 $y = 2 x$ 进行随机运动的质点离坐标原点的距离 $X _ { 2 }$   
$\textcircled{3}$ 某同学射击3次，命中的次数 $X _ { 3 }$   
$\textcircled{4}$ 某电子元件的寿命 $X _ { 4 }$

A. $\textcircled{1} \textcircled{2}$ B. $\textcircled{3} \textcircled{4}$ C. $\textcircled{1} \textcircled{3}$ D. $\textcircled{2} \textcircled{4}$

【例2】下面给出四个随机变量：

$\textcircled{1}$ 一高速公路上某收费站在半小时内经过的车辆数；  
$\textcircled{2}$ 一个沿直线 $y = 2 x$ 进行随机运动的质点，它在该直线上的位置 $\eta$ $\textcircled{3}$ 某指挥台5分钟内接到的雷达电话次数 $X$   
$\textcircled{4}$ 某同学离开哈尔滨市第三中学的距离Y；

其中是离散型随机变量的为（）

A. $\textcircled{1} \textcircled{2}$ B. $\textcircled{3} \textcircled{4}$ C. $\textcircled{1} \textcircled{3}$ D.②④

# 【题型专练】

1．下面是离散型随机变量的是（）

A．电灯炮的使用寿命 $X$   
B．小明射击1次，击中目标的环数 $\chi$   
C.测量一批电阻两端的电压，在 $1 0 \mathrm { { V } } { \sim } 2 0 \mathrm { { V } }$ 之间的电压值 $X$   
D．一个在y轴上随机运动的质点，它在y轴上的位置 $X$

2.（多选题）下列随机变量中属于离散型随机变量的是（）

A．某电话亭内的一部电话1小时内使用的次数记为 $X$ B．测量一个年级所有学生的体重，在 $6 0 k g { \sim } 7 0 k g$ 之间的体重记为 $X$ C.测量全校所有同学的身高，在 $1 7 0 c m { \sim } 1 7 5 c m$ 之间的人数记为 $X$ D．一个数轴上随机运动的质点在数轴上的位置记为 $\chi$

3．（多选题）下列随机变量是离散型随机变量的是（）

A．某景点一天的游客数 $X$ B．某寻呼台一天内收到寻呼次数 $X$ C．水文站观测到江水的水位数 $X$ D．某收费站一天内通过的汽车车辆数 $X$

# 题型三：离散型随机变量分布列

【例1】从装有3个红球，2个白球的袋中随机取出2个球，设其中有5个红球，则随机变量的概率分布为：

<html><body><table><tr><td>5</td><td>0</td><td>1</td><td>2</td></tr><tr><td>P</td><td></td><td></td><td></td></tr></table></body></html>

【例2】袋中装有一些大小相同的球，其中标号为1号的球1个，标号为2号的球2个，标号为3号的球3个，…，标号为 $n$ 号的球 $n$ 个.现从袋中任取一球，所得号数为随机变量 $\chi$ ，若 $P ( X = n ) = 0 . 2$ ，则 $n =$

【例3】在高考结束后，程浩同学回初中母校看望数学老师，顺便帮老师整理初三年级学生期中考试的数学成绩，并进行统计分析，在整个年级中随机抽取了200名学生的数学成绩，将成绩分为[40,50)，[50,60)，[60,70)，[70,80)，[80,90)，[90,100]，共6组，得到如图所示的频率分布直方图，记分数不低于90分为优秀.

![](images/f094ac9e82db47c879daf60138b7b78177eef0e814b93c0d6ba7cc90805487c7.jpg)

(1)从样本中随机选取一名学生，已知这名学生的分数不低于70分，问这名学生数学成绩为优秀的概率；(2)在样本中，采取分层抽样的方法从成绩在[70,100]内的学生中抽取13名，再从这13名学生中随机抽取3名，记这3名学生中成绩为优秀的人数为 $X _ { i }$ 求 $X$ 的分布列.

【例4】已知新高考数学共4道多选题，评分标准是每题满分5分，全部选对得5分，部分选对得2分，有错选或不选的得0分，每道多选题共有4个选项，正确答案往往为2项或3项.为了研究多选题的答题规律，某数学兴趣小组研究发现：多选题正确答案是“选两项"的概率为 $\frac { 1 } { 2 }$ ，正确答案是“选三项"的概率为 $\frac { 1 } { 2 }$ .现有学生甲、乙两人，由于数学基础很差，多选题完全没有思路，只能靠猜.

（1)已知某题正确答案是“选两项”，求学生甲不得0分的概率；

(2)学生甲的答题策略是"猜一个选项”，学生乙的策略是“猜两个选项”，试写出甲、乙两名学生得分的分布列.

# 选择性必修第三册

# 【题型专练】

1．某小组共10人，利用假期参加义工活动．已知参加义工活动次数为1，2，3的人数分别为3，3，4．现从这10人中随机选出2人作为该组代表参加座谈会.

(1）设 $A$ 为事件“选出的2人参加义工活动次数之和为 $4 ^ { \circ }$ ，求事件 $A$ 发生的概率；  
（2）设 $X$ 为选出的2人参加义工活动次数之差的绝对值，求随机变量 $X$ 的分布列.

2．某城市为了加快"两型社会”（资源节约型，环境友好型）的建设，本着健康、低碳的生活理念，租自行车骑游的人越来越多，自行车租车点的收费标准是每车每次租车时间不超过两小时免费，超过两小时的部分每小时收费2元（不足1小时的部分按1小时计算).有甲、乙两人相互独立来该租车点租车骑游（各租一车一次）．设甲、乙不超过两小时还车的概率分别为 $\frac { 1 } { 4 } , \frac { 1 } { 2 }$ ；两小时以上且不超过三小时还车的概率分别为 ${ \frac { 1 } { 2 } } , \ { \frac { 1 } { 4 } }$ 两人租车时间都不会超过四小时.

(1)求甲、乙两人所付的租车费用相同的概率；  
(2)设甲、乙两人所付的租车费用之和为随机变量 $X ,$ 求 $X$ 的分布列.

3.某学院为了调查本校学生2014年9月“健康上网(健康上网是指每天上网不超过两个小时)的天数情况，随机抽取了40名本校学生作为样本，统计他们在该月30天内健康上网的天数，并将所得的数据分成以下六组：[0，5]，(5，10]，(10，15]，..，(25，30]，由此画出样本的频率分布直方图，如图所示.

![](images/24afc271adbd3d1e2e2b7c611cdf4e59641384b78e5e011e53e7e8fa257919c3.jpg)

(1)根据频率分布直方图，求这40名学生中健康上网天数超过20天的人数；

(2)现从这40名学生中任取2名，设Y为取出的2名学生中健康上网天数超过20天的人数，求Y的分布列.

4.学校组织解题能力大赛，比赛规则如下：依次解答一道解析几何题和两道立体几何题，解析几何正确得2分，错误得0分；两道立体几何全部正确得3分，只正确一道题得1分，全部错误得0分；总分是两部分得分之和.小明同学准备参赛，他目前的水平是：解析几何解答正确的概率是 $\frac { 3 } { 5 }$ 每道立体几何解答正确的概率均为 $\frac { 2 } { 3 }$ 假设小明同学每道题的解答相互独立，

(1)求小明同学恰好有两道题解答正确的概率；  
(2)求小明同学获得的总分 $X$ 的分布列.

题型四：离散型随机变量分布列的性质

【例1】设随机安量 $\xi$ 的分布为 $P \left( \xi = \frac { k } { 5 } \right) = a k \left( k = 1 , 2 , 3 , 4 , 5 \right)$ 發则 $P \left( \frac { 1 } { 1 0 } < \xi < \frac { 7 } { 1 0 } \right) = \_$

【例2】已知随机变量 $X$ 的分布列是：

<html><body><table><tr><td>x1</td><td></td><td>23</td><td></td></tr><tr><td>P</td><td>0.25</td><td>ab</td><td></td></tr></table></body></html>

则 $a + b =$ （）

A.0.75 B.1.5 C.1 D.0.25【例3】已知随机变量 $X$ 的概率分布为： $P \left( X = n \right) = \frac { \lambda } { n \left( n + 1 \right) } \left( n = 1 , 2 , 3 \right)$ ，其中 $\lambda$ 是常数，则 $P \left( 1 \leq X < 3 \right)$ 的值为（）

A $\frac { 8 } { 9 }$ B. c $\frac 1 3$ D. 29【例4】随机变量 $X$ 的概率分布满足 $P \left( X = k \right) = \frac { C _ { 1 0 } ^ { k } } { M } \left( \ k = 0 , \ 1 , \ 2 , \ . . . , \ 1 0 \right)$ ，则 $M$ 的值为【例5】两对孪生兄弟共4人随机排成一排，设随机变量 $\xi$ 表示孪生兄弟相邻的对数，则（）

$$
> P \left( \xi = 1 \right) \qquad \mathrm { ~ B . ~ } \ P \left( \xi = 0 \right) = P \left( \xi = 1 \right) \qquad \mathrm { ~ C . ~ } \ P \left( \xi = 0 \right) < P \left( \xi = 1 \right) \qquad \mathrm { ~ D . ~ } \ P \left( \xi = 1 \right) > P \left( \xi = 2 \right)
$$

选择性必修第三册

【例6】（多选题）设随机变量 $\xi$ 的分布列如表：

<html><body><table><tr><td></td><td>51</td><td>2</td><td>3</td><td>：</td><td>2020</td><td>2021</td></tr><tr><td></td><td>Pa</td><td>a</td><td>a</td><td>：</td><td>a2020</td><td>a2021</td></tr></table></body></html>

则下列说法正确的是（）

A.当 $\left\{ a _ { n } \right\}$ 为等差数列时， $a _ { 2 } + a _ { 2 0 2 0 } = \frac { 2 } { 2 0 2 3 }$   
B.数列{a}的通项公式可能为a= $a _ { n } = \frac { 2 0 2 2 } { 2 0 2 1 n ( n + 1 ) }$   
C.当数列 $\left\{ a _ { n } \right\}$ 满足 (n=2时，  
D．当数列 $\left\{ a _ { n } \right\}$ 满足 $P ( \xi \le k ) = k ^ { 2 } a _ { k } ( k = 1 , 2 , \cdots , 2 0 2 1 )$ 时， $( n + 1 ) a _ { n } = ( n - 1 ) a _ { n - 1 } ( n \geq 2 )$

# 【题型专练】

1．随机变量 $X$ 的分布列如下表，则 $P ( | X | = 1 )$ 等于（）

<html><body><table><tr><td>X</td><td>-1</td><td>0</td><td>1</td></tr><tr><td>P</td><td>a</td><td>1-3</td><td>C</td></tr></table></body></html>

A. $\frac { 1 } { 2 }$ B. c. D.

2．已知随机变量 $\chi$ 的分布列如表（其中 $a$ 为常数）：

<html><body><table><tr><td></td><td>x0</td><td></td><td></td><td></td><td>1234</td><td>5</td></tr><tr><td></td><td>P0.10.1a0.3</td><td></td><td></td><td></td><td>0.2</td><td>0.1</td></tr></table></body></html>

则 $P \left( 1 \leq X \leq 3 \right)$ 等于（）A.0.4 B.0.5 C.0.6D.0.7

3.已知离散型随机变量 $X$ 的分布列为 $P \left( X = n \right) = { \frac { a } { \sqrt { n } + { \sqrt { n + 1 } } } } \left( n = 1 , 2 , \cdots , 1 5 \right)$ ，其中 $^ a$ 为常数，则 $P \left( X \leq 8 \right) = ($ ）

A $\frac { 1 } { 2 }$ B. C. 3-4 $\mathrm { D } . \ \frac { 4 } { 5 }$

4．若离散型随机变量 $X$ 的分布列为 $P \left( X = 0 \right) = 0 . 4$ ， $P \left( X = 1 \right) = a , P \left( X = 2 \right) = 2 a$ ，则 $^ { a }$ 的值为（）

A.0.1 B.0.2 C.0.3 D.0.4

5．设随机变量 $\xi$ 的概率分布为 $P \left( \xi = k \right) = { \frac { a } { 2 ^ { k } } }$ ， $a$ 为常数， $k = 1$ ，2，3，4，则 $a =$

6．某同学参加3门课程的考试.假设该同学第一门课程取得优秀成绩的概率为 $\frac { 4 } { 5 }$ 第二、第三门课程取得优秀成绩的概率分别为 $p , q ( p > q )$ ，且不同课程是否取得优秀成绩相互独立.记 $\xi$ 为该生取得优秀成绩的课程数，其分布列为：

<html><body><table><tr><td></td><td>50123</td><td></td><td></td><td></td></tr><tr><td>P</td><td>125</td><td></td><td>ab</td><td>24 125</td></tr></table></body></html>

则 $a + b$ 的值为 _；则 $p + q$ 的值为

7.设随机变量 $X$ 的分布列为 $P \left( X = { \frac { k } { 5 } } \right) = a k \left( k = 1 , 2 , 3 , 4 , 5 \right) ,$

(1)求常数 $a$ 的值； 2 $P \left( X \geq \frac { 3 } { 5 } \right)$ $P \left( \frac { 1 } { 1 0 } < X < \frac { 7 } { 1 0 } \right) .$

# 第9讲离散型随机变量的期望方差及其性质

# 【考点分析】

考点一：离散型随机变量的数字特征$\textcircled{1}$ 期望的含义：一般地，若离散型随机变量的概率分布为则称 $E \xi = x _ { 1 } p _ { 1 } + x _ { 2 } p _ { 2 } + \cdots + x _ { n } p _ { n } + \cdots$ 为的数学期望或平均数、均值.数学期望又简称期望.数学期望反映了离散型随机变量取值的平均水平.

<html><body><table><tr><td></td><td>x</td><td>X2</td><td></td><td>x</td><td></td></tr><tr><td>P</td><td>P</td><td>P</td><td>…</td><td>Pi</td><td></td></tr></table></body></html>

$\textcircled{2} ( 1 )$ 随机变量 $\eta = a \xi + b$ 的数学期望： ${ E } \eta = { E } { ( a \xi + b ) } = a { E } \xi + b$ $\textcircled{1}$ 当 $a = 0$ 时， $E ( b ) = b$ ，即常数的数学期望就是这个常数本身.

$\textcircled{2}$ 当 $a = 1$ 时， $E ( \xi + b ) = E \xi + b$ ，即随机变量5与常数之和的期望等于的期望与这个常数的和.

$\textcircled{3}$ 当 $b = 0$ 时， $E ( a \xi ) = a E \xi$ ，即常数与随机变量乘积的期望等于这个常数与随机变量期望的乘积.

(2)单点分布： $E \xi = c \times 1 = c$ 其分布列为： $P ( \xi = 1 ) = c$ ：

(3)两点分布： $E \xi = 0 \times q + 1 \times p = p$ ，其分布列为： $( { \mathfrak { p } } + { \mathfrak { q } } = 1$ ）

<html><body><table><tr><td></td><td>0</td><td>1</td></tr><tr><td>P</td><td>q</td><td>p</td></tr></table></body></html>

(4)二项分布： $E \xi = \sum k \cdot \frac { n ! } { k ! ( n - k ) ! } p ^ { k } \cdot q ^ { n - k } = n p$ 其分布列为 $\xi \sim B ( n , p )$ .(P为发生$\xi$ 的概率）

（5)几何分布： $E \xi = \frac { 1 } { p }$ 其分布列为 $\xi \sim q ( k , p )$ .（P为发生 $\xi$ 的概率）

$\textcircled{3}$ 方差、标准差的定义：当已知随机变量的分布列为 $P ( \xi = x _ { k } ) = p _ { k } \left( k = 1 , 2 , \cdots \right)$ 时，则称$D \xi = ( x _ { 1 } - E \xi ) ^ { 2 } p _ { 1 } + ( x _ { 2 } - E \xi ) ^ { 2 } p _ { 2 } + \cdots + ( x _ { n } - E \xi ) ^ { 2 } p _ { n } + \cdots$ 为的方差，显然 $D \xi \ge 0$ ，故 $\sigma \xi = \sqrt { D \xi }$ ： $\sigma \xi$ 为的根方差或标准差.随机变量的方差与标准差都反映了随机变量取值的稳定与波动，集中与离散的程度. $D \xi$ 越小，稳定性越高，波动越小：

$\textcircled{4}$ 方差的性质.(1)随机变量 $\eta = a \xi + b$ 的方差 $D ( \eta ) = D ( a \xi + b ) = a ^ { 2 } D \xi$ .（a、b均为常数）

$\textcircled{5}$ 期望与方差的关系.

(1）如果 $E \xi$ 和 $E \eta$ 都存在，则 $E ( \xi \pm \eta ) = E \xi \pm E \eta$ (2)设和 $\eta$ 是互相独立的两个随机变量，则 $E ( \xi \eta ) = E \xi \cdot E \eta , D ( \xi + \eta ) = D \xi + D \eta$ (3)期望与方差的转化： $D \xi = E \xi ^ { 2 } - ( E \xi ) ^ { 2 }$ 4 $\begin{array} { r } { \big ) E ( \xi - E \xi ) = E ( \xi ) - E ( E \xi ) } \end{array}$ （因为 $E \xi$ 为一常数） $= E \xi - E \xi = 0$ ：

# 选择性必修第三册

题型一：离散型随机变量的期望

【例1】在采用五局三胜制（先取得三局胜利的一方，获得最终胜利）的篮球总决赛中，当甲队先胜2场时，因疫情暴发不得不中止比赛.已知甲、乙两队水平相当，每场甲、乙胜的概率都为 $\frac { 1 } { 2 }$ ，总决赛的奖金为80万元，总决赛的胜者获得全部奖金.根据我们所学的概率知识，甲队应分得的奖金为（）万元.

A.80 B.70 C.50 D.40【例2】一个袋子中装有大小相同的5个小球，其中有3个白球，2个红球，小明从中无放回地取出3个小球，摸到一个白球记1分，摸到一个红球记2分，则小明总得分{的数学期望等于（）

A.3.8分 B.4分 C.4.2分 D.4.4分 【例3】从一批含有6件正品和4件次品的10件产品中随机抽取2件产品进行检测，记随机变量 $X$ 为抽检结果 中含有的次品件数，则随机变量 $X$ 的期望 $E \left( X \right) =$

【例4】某公司有5万元资金用于投资开发项目，如果成功，一年后可获利 $12 \%$ ，一旦失败，一年后将丧失全部资金的 $50 \%$ ，下表是过去200例类似项目开发的实施结果：

<html><body><table><tr><td>投资成功</td><td>投资失败</td></tr><tr><td>192次</td><td>8次</td></tr></table></body></html>

则该公司一年后估计可获收益的期望是 （元）.

选择性必修第三册

【例5】电机（或变压器）绕组采用的绝缘材料的耐热等级也叫绝缘等级，电机与变压器中常用的绝缘材料耐热等级分为如下7个级别：

<html><body><table><tr><td>耐热等级</td><td>Y</td><td>A</td><td>E</td><td>B</td><td>F</td><td>H</td><td>C</td></tr><tr><td>绝缘耐温（℃）</td><td></td><td></td><td></td><td></td><td></td><td></td><td>[9005）5）</td></tr></table></body></html>

某绝缘材料生产企业为测试甲、乙两种生产工艺对绝缘耐温的影响，分别从两种工艺生产的产品中各随机抽取50件，测量各件产品的绝缘耐温（单位： $^ { \circ } C ^ { \cdot }$ ，其频率分布直方图如下：

频率/组距 频率/组距  
0.07 0.07  
0.06 1 ： 0.06 1  
0.05 0.05  
0.04 0.04 1  
0.03 0.03  
0.02 0.02180190200210220绝缘耐温（C） 180190200210220绝缘耐温（℃）甲 乙

(1)若10月份该企业采用甲工艺生产的产品为65万件，估计其中耐热等级达到 $C$ 级的产品数；

(2)若从甲、乙两种工艺生产的产品中分别随机选择1件，用频率估计概率，求2件产品中耐热等级达到 $C$ 级的产品数的分布列和数学期望.

【例6】现有甲、乙、丙、丁等6人去参加新冠疫苗的接种排队，有A、B、C、D4个不同的窗口供排队等候接种，每个窗口至少有一位同学等候.

（1)求甲、乙两人在不同窗口等候的概率；  
(2)设随机变量 $X$ 表示在窗口 $A$ 排队等候的人数，求随机变量 $X$ 的期望.

# 【题型专练】

1．甲、乙两人进行乒兵球比赛，约定每局胜者得1分，负者得0分，比赛进行到有一人比对方多2分或打满6局时停止，设甲在每局中获胜的概率为 $\frac { 2 } { 3 }$ 乙在每局中获胜的概率为， 且各局胜负相互独立，则比赛停止时已打局数 $X$ 的期望 $E ( X )$ 为（）

A. $\frac { 1 7 } { 2 }$ B. 266 C. 256 D. 670   
81 81 243

2.某车间打算购买2台设备，该设备有一个易损零件，在购买设备时可以额外购买这种易损零件作为备件，价格为每个120元.在设备使用期间，零件损坏，备件不足再临时购买该零件时，价格为每个280元.在使用期间，每台设备需更换的零件个数 $X$ 的分布列为：

<html><body><table><tr><td>X</td><td>6</td><td>7</td><td>8</td></tr><tr><td>P</td><td>0.4</td><td>0.5</td><td>0.1</td></tr></table></body></html>

若购买2台设备的同时购买易损零件13个，则在使用期间，这2台设备另需购买易损零件所需费用的期望为（）

A.1716.8元 B.206.5元 C.168.6元 D.156.8元

3．袋中装有大小与质地相同的5个红球、 $m$ 个白球，现从中任取2个球，若取出的两球都是红球的概率为 $\frac { 5 } { 1 4 }$ 记取出的红球个数为 $X$ ，则 $E \left( X \right) = \qquad -$

4．农历五月初五是我国的传统节日—端午节，为纪念伟大的爱国诗人屈原，民间有吃粽子的习惯，粽子也就成为了我们生活中的一种美食.设一盘中装有6个粽子，其中豆粽、肉粽、白粽各2个，这三种粽子的外观完全相同，小明从中任取2个吃，吃完这2个，若是吃到了肉粽就不再吃了；若是还没吃到肉粽，就再从剩下的4个中任取1个吃，吃完这个不管是否吃到肉粽都不再吃了.

（1）求小明吃到肉粽的概率；  
（2）设 $X$ 表示取到的肉粽个数，求 $X$ 的分布列与数学期望.

5.某人花了 $a$ 元预定2023年杭州亚运会开幕式门票一张，另外还预定了两张其他门票，根据亚奥理事会的相关规定，从所有预定者中随机抽取相应数量的人，这些人称为预定成功者，他们可以直接购买门票，另外，对于开幕式门票，有自动降级规定，即当这个人预定的 $a$ 元门票未成功时，系统自动使他进入 $b$ 元开幕式门票的预定.假设获得 $a$ 元开幕式门票的概率是0.1，若未成功，仍有0.2的概率获得 $b$ 元开幕式门票的机会，获得其他两张门票中的每一张的概率均是0.5，且获得每张门票之间互不影响.

(1)求这个人可以获得亚运会开幕式门票的概率；(2)假设这个人获得门票总张数是 $X$ ，求 $\chi$ 的分布列及数学期 $E { \big ( } X { \big ) }$

# 选择性必修第三册

6．中国男子篮球职业联赛"简称CBA"半决赛采用"五局三胜制”，具体规则为比赛最多进行五场，当参赛的两方有一方先赢得三场比赛，就由该方获胜而比赛结束，每场比赛都需分出胜负.同时比赛采用主客场制，比赛先在$A$ 队的主场进行两场比赛，再移师 $B$ 队主场进行两场比赛（有必要才进行第二场)，如果需要第五场比赛，则回到 $A$ 队的主场进行，已知 $A$ 队在主场获胜的概率为 $\frac { 2 } { 3 }$ ，在客场获胜的概率为 $\frac { 1 } { 2 }$ ，假设每场比赛的结果相互独立.

(1)第一场比赛 $B$ 队在客场通过全队的努力先赢了一场，赛后 $B$ 队的教练鼓励自己的队员说“胜利的天平已经向我们倾斜”，试从概率大小的角度判断 $B$ 队教练的话是否客观正确；

(2)每一场比赛，会给主办方在门票，饮食，纪念品销售等方面带来综合收益300万元，设整个半决赛主办方综合收益为 $\xi$ ，求 $\xi$ 的分布列与期望，

7.某工厂质检部门要对该厂流水线生产出的一批产品进行检验，如果检查到第4件仍未发现不合格品，则此次检查通过且认为这批产品合格，如果在尚未抽到第4件时已检查到不合格品，则拒绝通过且认为这批产品不合格.且每件产品质检费用为80元.设这批产品的数量足够大，并认为每次检查中查到不合格品的概率都为 $p$ ，即每次抽查的产品是相互独立的.

(1)求这批产品能够通过检查的概率；

(2)记对这批产品的质检个数记作 $X$ ，求 $X$ 的分布列和数学期望；

(3)已知100批此类产品，若 $p \in \left[ 0 . 0 5 , 0 . 1 \right]$ ，则总平均检查费用至少需要多少元？（总平均检查费用 $=$ 每批次平均检查费用 $\times$ 批数）

题型二：离散型随机变量的方差【例1】若随机变量 $X$ 的概率分布表如下：

<html><body><table><tr><td>X</td><td>0</td><td>1</td></tr><tr><td>P</td><td>0.4</td><td>m</td></tr></table></body></html>

则 $D ( X ) =$ （）

A.0.5 B.0.42 C.0.24 D.0.16【例2】已知随机变量 $\xi _ { i } \left( i = 1 , 2 \right)$ 的分布列如下表所示：

<html><body><table><tr><td>y</td><td>0</td><td>1</td><td>2</td></tr><tr><td>P</td><td>1-3</td><td>Pi</td><td>2-P</td></tr></table></body></html>

$0 < p _ { 1 } < \frac { 1 } { 2 } < p _ { 2 } < \frac { 2 } { 3 }$ 则（）

A. $E ( \xi _ { 1 } ) > E ( \xi _ { 2 } )$ ， $D ( \xi _ { 1 } ) > D ( \xi _ { 2 } )$ B. $E ( \xi _ { 1 } ) < E ( \xi _ { 2 } )$ ， $D ( \xi _ { 1 } ) > D ( \xi _ { 2 } )$ C. $E ( \xi _ { 1 } ) > E ( \xi _ { 2 } )$ ， $D ( \xi _ { 1 } ) < D ( \xi _ { 2 } )$ D $E ( \xi _ { 1 } ) < E ( \xi _ { 2 } )$ ， $D ( \xi _ { 1 } ) < D ( \xi _ { 2 } )$

【例3】（多选题）设 $0 < m < 1$ ，随机变量的分布列为：

<html><body><table><tr><td></td><td>0</td><td>m</td><td>1</td></tr><tr><td>P</td><td>a3</td><td>1-3</td><td>2a-1 3</td></tr></table></body></html>

则当 $m$ 在（0，1）上增大时，（）

A. $E \mathopen { } \mathclose \bgroup \left( \xi \aftergroup \egroup \right)$ 减小 B. $E \mathopen { } \mathclose \bgroup \left( \xi \aftergroup \egroup \right)$ 增大C. $D \left( \boldsymbol { \xi } \right)$ 先增后减，最大值为 $\frac { 1 } { 6 }$ D. $D \left( \boldsymbol { \xi } \right)$ 先减后增，最小值为【例4】从4名男生和2名女生中任选3人参加演讲比赛，设随机变量 $X$ 表示所选3人中女生的人数.求：

(1) $X$ 的分布；  
(2) $X$ 的期望与方差.

# 选择性必修第三册

【例5】某种水果按照果径大小可分为四类：标准果、优质果、精品果、礼品果.某采购商从采购的一批水果中随机抽取100个，利用水果的等级分类标准得到的数据如下：

<html><body><table><tr><td>等级</td><td>标准果</td><td>优质果</td><td>精品果</td><td>礼品果</td></tr><tr><td>个数</td><td>10</td><td>30</td><td>40</td><td>20</td></tr></table></body></html>

(1)若将频率视为概率，从这100个水果中有放回地随机抽取5个，求恰好有2个水果是礼品果的概率（结果用分数表示）；

(2)用样本估计总体，果园老板提出两种购销方案给采购商参考，方案1：不分类卖出，单价为21元/kg；  
方案2：分类卖出，分类后的水果售价如下：

<html><body><table><tr><td>等级</td><td>标准果</td><td>优质果</td><td>精品果</td><td>礼品果</td></tr><tr><td>售价（元/kg）</td><td>16</td><td>18</td><td>22</td><td>24</td></tr></table></body></html>

从采购商的角度考虑，应该采用哪种方案？

(3)用分层抽样的方法从这100个水果中抽取10个，再从抽取的10个水果中随机抽取3个， $X$ 表示抽取的是精品果的数量，求 $X$ 的分布列及方差 $D ( X )$ ：

【例6】为迎接2022年北京冬奥会，推广滑雪运动，某滑雪场开展滑雪促销活动，该滑雪场的收费标准是：滑雪时间不超过1小时免费，超过1小时的部分每小时收费标准为40元(不足1小时的部分按1小时计算).有甲、乙两人相互独立地来该滑雪场运动，设甲、乙不超过1小时离开的概率分别为 $\frac { 1 } { 4 } , \frac { 1 } { 6 }$ 1小时以上且不超过2小时离开的概率分别为 ${ \frac { 1 } { 2 } } , { \frac { 2 } { 3 } }$ ；两人滑雪时间都不会超过3小时.

（1)求甲、乙两人所付滑雪费用相同的概率；(2)设甲、乙两人所付的滑雪费用之和为随机变量，求的分布列与均值 $E ( \xi )$ ，方差 $D ( \boldsymbol { \xi } )$

# 选择性必修第三册

# 【题型专练】

1.（多选题）已知下表为离散型随机变量 $X$ 的分布列，其中 $a b \neq 0$ ，下列说法正确的是（）

<html><body><table><tr><td></td><td>x0</td><td>1</td><td>2</td></tr><tr><td></td><td>Pa</td><td>b-2</td><td>b-2</td></tr></table></body></html>

A. $a + b = 1$ B. $E \left( X \right) = 2 b$ C. $D ( X )$ 有最大值 D. $D ( X )$ 有最小值

2.（多选题）2022年冬奥会在北京举办，为了弘扬奥林匹克精神，某市多所中小学开展了冬奥会项目科普活动.为了调查学生对冰壶这个项目的了解情况，在该市中小学中随机抽取了10所学校，10所学校中了解这个项目的人数如图所示：

![](images/d3a2c7764eb86791f97d6fc6b360df08c4e596f4e9de86ec3c193533c2eb3cd4.jpg)

若从这10所学校中随机选取2所学校进行这个项目的科普活动，记 $X$ 为被选中的学校中了解冰壶的人数在30以上的学校所数，则（）

A. $X$ 的可能取值为0，1，2，3 B $P \left( X = 0 \right) = \frac { 1 } { 3 }$ C. $E { \bigl ( } X ) = { \frac { 3 } { 5 } }$ $) . D ( X ) = { \frac { 3 2 } { 7 5 } }$

3．已知一个随机变量 $X$ 的分布为 $\left( \frac { 0 } { 5 } \& \frac { 2 } { 3 } \right)$ ，且 $E [ X ] = 1$ ，则 $D [ X ] =$

4．某网站规定：一个邮箱在一天内出现3次密码尝试错误，该邮箱将被锁定24小时.小王发现自己忘记了邮箱密码，但是可以确定该邮箱的正确密码是他常用的6个密码之一，小王决定从中不重复地随机选择1个进行尝试.若密码正确，则结束尝试；否则继续尝试，直至该邮箱被锁定.

（1)求当天小王的该邮箱被锁定的概率；   
(2)设当天小王尝试该邮箱的密码次数为 $X$ ，求 $X$ 的分布列及 $E [ \boldsymbol { X } ]$ ， $D [ X ]$ 的值.

5.为了响应大学毕业生自主创业的号召，小李毕业后开了水果店，水果店每天以每个5元的价格从农场购进若干西瓜，然后以每个10元的价格出售，如果当天卖不完，剩下的西瓜作赠品处理.

(1)若水果店一天购进16个西瓜，求当天的利润（单位：元）关于当天需求量 $n$ （单位：个， $n \in \mathrm N$ ）的函数解析式；

(2)水果店记录了100天西瓜的日需求量（单位：个)，整理得下表：

<html><body><table><tr><td>日需求量n</td><td>14</td><td>15</td><td>16</td><td>17</td><td>18</td><td>19</td><td>20</td></tr><tr><td>频数</td><td>10</td><td>20</td><td>16</td><td>16</td><td>15</td><td>13</td><td>10</td></tr></table></body></html>

以100天记录的各需求量的频率作为各需求量发生的概率.

$\textcircled{1}$ 若水果店一天购进16个西瓜， $X$ 表示当天的利润（单位：元），求 $X$ 的分布列、数学期望及方差；  
$\textcircled{2}$ 若水果店计划一天购进16个或17个西瓜，你认为应购进16个还是17个？请说明理由.

6．冬奥会志愿者有6名男同学，4名女同学.在这10名志愿者中，三名同学来自北京大学，其余7名同学来自北京邮电大学，北京交通大学等其他互不相同的7所大学.现从这10名志愿者中随机选取4名同学，到机场参加活动（每位同学被选中的可能性相等）.

（1)求选出的4名同学是来自互不相同的大学的概率；  
(2）设 $X$ 为选出的4名同学中女同学的人数，求随机变量 $X$ 的期望和方差.

7．甲、乙、丙进行乒乓球比赛，比赛规则如下：赛前抽签决定先比赛的两人，另一人轮空：每场比赛的胜者与轮空者进行下一场比赛，负者下一场轮空，直至有人累计胜两场，比赛结束.经抽签，甲、乙先比赛，丙轮空.设比赛的场数为 $X$ ，且每场比赛双方获胜的概率都为 $\frac { 1 } { 2 }$

（1）求 $P \left( X = 2 \right)$ 和 $P \left( X = 3 \right)$ （2）求 $X$ 的标准差.

选择性必修第三册

题型三：离散型随机变量的期望方差的性质

【例1】已知随机变量 $X$ 满足 $E ( 2 X + 3 ) = 7$ ， $D ( 2 X + 3 ) = 1 6$ ，则下列选项正确的是（）

A $E ( X ) = \frac { 7 } { 2 } , D ( X ) = \frac { 1 3 } { 2 }$ B. $E ( X ) = 2 , D ( X ) = 4$   
C. $\ E ( X ) = 2 , D ( X ) = 8$ ${ \mathrm { D } } . \quad E ( X ) = { \frac { 7 } { 4 } } , D ( X ) = 8$

【例2】设随机变量 $\xi$ 的分布列为 $P ( \xi = k ) = \frac { a } { k + 1 } ( k = 1 , 2 , 5 ) , a \in R$ ， $E ( \xi ) , D ( \xi )$ 分别为随机变量 $\xi$ 的数学期望与方差，则下列结论正确的是（）

A. $P ( 0 < \xi < 3 . 5 ) = \frac { 2 } { 3 }$ B. $E ( 3 \xi + 2 ) = 7$ C. $D ( \xi ) = 2$ D. $D ( 3 \xi + 1 ) = 6$

【例3】（多选题）设离散型随机变量 $X$ 的分布列为：

<html><body><table><tr><td>x0</td><td></td><td>1</td><td>2</td><td>3</td><td>4</td></tr><tr><td>P</td><td>q</td><td>0.4</td><td>0.1</td><td>0.2</td><td>0.2</td></tr></table></body></html>

若离散型随机变量 $Y$ 满足 $Y = 2 X + 1$ ，则下列结果正确的有（）

A. $q = 0 . 1$ B. $D ( X ) = 1 . 8$ C. $E ( Y ) = 5$ D $D ( Y ) = 3 . 6$

【例4】已知离散型随机变量 $X$ 的取值为有限个， $E { \bigl ( } X { \bigr ) } = { \frac { 7 } { 2 } }$ ， $D \left( X \right) = \frac { 3 5 } { 1 2 }$ 则 $E \left( X ^ { 2 } \right) =$

【例5】某网约车司机统计了自己一天中出车一次的总路程 $X$ （单位： $\mathrm { k m }$ ）的可能取值是20，22，24，26，28，30，它们出现的概率依次是0.1，0.2，0.3，0.1，t，2t.

（1）求 $X$ 的分布列，并求 $X$ 的均值和方差；

(2)若网约车计费细则如下：起步价为5元，行驶路程不超过 $3 \mathrm { k m }$ 时，收费5元，行驶路程超过3km时，则按每超出1km（不足1km也按1km计程）收费3元计费，试计算此人一天中出车一次收入的均值和方差.

# 选择性必修第三册

# 【题型专练】

1．已知随机变量 $X$ 满足 $E { \big ( } 2 - 2 X { \big ) } = 4$ ， $D \left( 2 - 2 X \right) = 4$ ，下列说法正确的是（）

A. $E { \big ( } X { \big ) } = - 1 , D { \big ( } X { \big ) } = - 1$ $\begin{array} { r l } { \boldsymbol { \mathrm { B } } . } & { { } E \left( \boldsymbol { X } \right) = 1 , D \left( \boldsymbol { X } \right) = 1 } \end{array}$   
C. $E { \big ( } X { \big ) } = - 1 , D { \big ( } X { \big ) } = 4$ $\mathrm { D } . \quad E { \bigl ( } X { \bigr ) } = - 1 , D { \bigl ( } X { \bigr ) } = 1$

2．已知随机变量 $\xi ( \xi > 0 )$ 满足 $E \left( 2 - 3 \xi \right) + E ^ { 2 } \left( \xi \right) = 6$ ，则 $E \left( \xi \right) = ( )$

A.-1或4 B.2 C.3 D.4

3.离散型随机变量 $X$ 的分布为：

<html><body><table><tr><td>X</td><td>0</td><td>1</td><td>2</td><td>4</td><td>5</td></tr><tr><td>P</td><td>q</td><td>0.3</td><td>0.2</td><td>0.2</td><td>0.1</td></tr></table></body></html>

若离散型随机变量 $Y$ 满足 $Y = 2 X + 1$ ，则下列结果正确的为

$\textcircled { 1 } E \big ( X \big ) = 2$ ， $\textcircled { 2 } E ( Y ) = 4$ ； $\textcircled { 3 } D \big ( X \big ) = 2 . 8$ $\textcircled { 4 } D \textcircled { Y } = 1 4$ ：

4．对于随机变量 $X$ ，它的数学期望 $E { \big ( } X { \big ) }$ 和方差 $D ( X )$ ，下列所有正确的序号是

$\textcircled { 1 } E ( X )$ 是反映随机变量的平均取值； $\textcircled { 2 } D ( X )$ 越小，说明 $X$ 越集中于 $E \mathopen { } \mathclose \bgroup \left( X \aftergroup \egroup \right)$ $\textcircled { 3 } E { \bigl ( } a X + b { \bigr ) } = a E { \bigl ( } X { \bigr ) } + b ~ ; \qquad \textcircled { 4 } D { \bigl ( } a X + b { \bigr ) } = a ^ { 2 } D { \bigl ( } X { \bigr ) } + b ~ .$

5．某工厂的某种产品成箱包装，每一箱100件.每一箱产品在交付用户之前要对产品作检验，如检验出不合格品，则更换为合格品.检验时，先从这箱产品中任取10件作检验，再根据检验结果决定是否对余下的所有产品作检验，设每件产品是不合格品的概率都为 $x \left( 0 < x < 1 \right)$ ，且各件产品是否为不合格品相互独立.

(1)记10件产品中恰有1件不合格品的概率为 $f ( x )$ ，求 $f ( x )$ 的最大值点 $x _ { 0 }$

(2)现对一箱产品检验了10件，结果恰有1件不合格品，以（1）中确定的 $x _ { 0 }$ 作为 $x$ 的值.已知每件产品的检验费用为2.5元，若有不合格品进入用户手中，则工厂要对每件不合格品支付20元的赔偿费用.

$\textcircled{1}$ 若不对该箱余下的产品作检验，这一箱产品的检验费用与赔偿费用的和记为 $X$ ，求 $E { \big ( } X { \big ) }$

$\textcircled{2}$ 以检验费用与赔偿费用的和的期望值为决策依据是否该对这箱余下的所有产品作检验？

# 第10讲两点分布，二项分布及超几何分布的区别与联系

# 【考点分析】

考点一：两点分布：

<html><body><table><tr><td>X</td><td>0</td><td>1</td></tr><tr><td>P</td><td>1-P</td><td>P</td></tr></table></body></html>

# 考点二：独立重复实验

$\textcircled{1}$ 独立重复实验的定义  
一般地，在相同条件下重复做的 $n$ 次试验称为 $n$ 次独立重复试验.  
$\textcircled{2}$ 独立重复试验中事件 $A$ 恰好发生 $k$ 次的概率  
一般地，如果在1次实验中某事件发生的概率是 $p$ ，那么在 $n$ 次独立重复试验中这个事件恰好发生 $k$ 次的概率$P ( X { = } k ) { = } \mathbb { C } _ { n } ^ { k } p ^ { k } ( 1 { - } p ) ^ { n ^ { - } k } , k { = } 0 , 1 , 2 , . . . , n .$

# 考点三：二项分布

$\textcircled{1}$ 二项式分布

一般地，在 $n$ 次独立重复试验中，设事件 $A$ 发生的次数为 $X$ ，在每次试验中事件 $A$ 发生的概率为 $p$ ，则事件 $A$ 恰好发生 $k$ 次的概率为 $P ( x = k ) = { C _ { n } } ^ { k } p ^ { k } ( 1 - p ) ^ { n - k }$ ， $k = 0 , 1 , 2$ ，..，n.则称随机变量 $X$ 服从二项分布，记作$X \sim B ( n , p )$ ，并称 $p$ 为成功概率.二项分布可以看成是两点分布的一般形式。

$\textcircled{2}$ 判断一个随机变置是否服从二项分布，看两点

1.是否为 $\boldsymbol { \mathsf { n } }$ 次独立重复试验，

2.随机变量是否为某事件在这 $\mathbf { n }$ 次独立重复试验中发生的次数.

# 考点四：超几何分布

一般地，在含有 $M$ 件次品的 $N$ 件产品中，任取 $n$ 件，其中恰有 $X$ 件次品，则 CC=0.2，其中 ${ \mathfrak { m } } = \operatorname* { m i n } \{ M , \ n \}$ ，且 $n { \leq } N$ M≤N，n，M， $\boldsymbol { N } \in \mathbf { N } ^ { * }$ ，则称分布列

<html><body><table><tr><td>X</td><td>0</td><td>1</td><td></td><td>m</td></tr><tr><td>P</td><td>CCN-M C</td><td>C C</td><td>·</td><td>CCN-M C</td></tr></table></body></html>

为超几何分布列.如果随机变量 $X$ 的分布列为超几何分布列，则称随机变量 $X$ 服从超几何分布.

# 考点五：二项式分布与超几何分布的区别和联系

超几何分布和二项分布的相同点为：随机变量均是取连续非负整数值的离散型分布列.

超几何分布和二项分布最明显的区别有两点：

$\textcircled{1}$ 超几何分布是不放回抽取，二项分布是放回抽取，也就是说二项分布中每个事件之间是相互独立的，而超几何分布不是；

$\textcircled{2}$ 超几何分布需要知道总体的容量，也就是总体个数有限；而二项分布不需要知道总体容量，但需要知道"成功

率”

超几何分布和二项分布二者之间也有联系：当总体很大时，超几何分布近似于二项分布，或者说超几何分布的极限就是二项分布.

题型一：两点分布的概念及分布列

# 【精选例题】

【例1】（多选题）下列选项中的随机变量服从两点分布的是（）

A．抛掷一枚骰子，所得点数 $\chi$   
B．某射击手射击一次，击中目标的次数 $X$ $X = { \left\{ \begin{array} { l l } { 1 , } \\ { 0 , } \end{array} \right. }$ 取出白球  
C．从装有除颜色外其余均相同的5个红球、3个白球的袋中任取1个球，设取出红球  
D．某医生做一次手术，手术成功的次数 $X$

【例2】设随机变量 $X$ 服从两点分布，若 $P \big ( X = 1 \big ) - P \big ( X = 0 \big ) = 0 . 3$ ，则成功概率 $P \left( X = 1 \right) =$ （）

A.0.3 B.0.35 C.0.65 D.0.7【例3】对于只有两个可能结果的随机试验，用 $A$ 表示“成功”， $\overline { { A } }$ 表示“失败”.定义 $X = \left\{ \begin{array} { l l } { { 1 , A ^ { y } \tilde { \times } \tilde { \pm } } } \\ { { 0 , \ \overline { { { A } } } ^ { y } \tilde { \times } \tilde { \pm } } } \end{array} \right.$ ，如果 $P ( A ) { = } p$ ，那么 $X$ 的分布为

# 【题型专练】

1.（多选题）下列选项中的随机变量服从两点分布的是（）.

A．抛掷一枚骰子，所得点数 $X$   
B．某射手射击一次，击中目标得2分，未击中目标得0分，射手的得分 $X$   
C．从装有5个红球，3个白球的袋子中取1个球，定义： $\{ X = 1 \} =$ “取出白球”， $\left\{ X = 0 \right\} =$ “取出红球”  
D．某医生做一次手术，手术成功的次数 $X$

2．下列问题中的随机变量不是伯努利型的序号是

$\textcircled{1}$ 某运动员射击一次，击中目标的次数为随机变量 $X _ { \mathrm { { i } } }$   
$\textcircled{2}$ 某医生做一次手术，手术成功的次数为随机变量 $X$   
$\textcircled{3}$ 抛掷一颗骰子，所得点数为随机变量 $X$   
$\textcircled{4}$ 从装有5个红球、3个白球的袋中取1个球，令随机变量 $X = 1$ ，取出白球； $X = 0$ ，取出红球.

3．已知随机变量 $X$ 服从两点分布，且 $P ( X = 1 ) = 0 . 6$ ，设 $\xi = 3 X - 2$ ，那么 $P ( \xi = - 2 ) =$

4．一个袋中有除颜色外其余完全相同的3个白球和4个红球.

$X = { \left\{ \begin{array} { l l } { 0 , } \\ { 1 , } \end{array} \right. }$ 摸出白球，(1)从袋中任意摸出一球，用0表示摸出白球，用1表示摸出红球，则有 求 $X$ 的分布列；摸出红球，

(2)从袋中任意摸出两个球，用“ $Y = 0 ^ { \circ }$ 表示两个球全是白球，用 $Y = 1$ 表示两个球不全是白球，求Y的分布列.

5.已知 $X$ 服从参数为0.3的两点分布，则 $P \left( X = 0 \right) =$ ；若 $Y = 3 X - 2$ ，则 $P \left( \boldsymbol { Y } = 1 \right) =$

# 题型二：两点分布的期望方差

# 【精选例题】

【例1】已知随机变量 $X$ 服从两点分布， $E \left( X \right) = 0 . 7$ ，则其成功概率为（）

A.0 B.1 C.0.3 D.0.7

【例2】若随机变量 $X$ 服从两点分布，其中 $P \left( X = 0 \right) = \frac { 1 } { 3 }$ ，则 $D ( \cal { X } ) = ( \cal { X } )$

A. $\frac { 2 } { 9 }$ B. 1-3 $\mathsf { C } . \mathsf { \Pi } _ { 9 } ^ { 4 }$ D. $\frac { 2 } { 3 }$

【例3】已知离散型随机变量 $X$ 的分布列服从两点分布，满足 $P \left( X = 0 \right) = \frac { 2 } { 9 P \left( X = 1 \right) }$ 且 $P \left( X = 0 \right) < P \left( X = 1 \right)$ 则 $E { \big ( } X { \big ) } =$ （）

A $\frac 1 3$ B. C. $\frac { 2 } { 3 }$ D. $\frac { 1 } { 4 }$

【例4】（多选题）若随机变量 $X$ 服从两点分布，其中 $P \left( \boldsymbol { X } = \boldsymbol { 0 } \right) = \frac { 1 } { 4 } , E \left( \boldsymbol { X } \right) , D \left( \boldsymbol { X } \right)$ 分别为随机变量 $X$ 的均值和方差，则（）

$$
A . \frac { \ d } { \ d t } P ( X = 1 ) = \frac { 3 } { 4 } \qquad \mathrm { ~ B . ~ } \frac { \ d } { \ d t } E ( X ) = \frac { 1 } { 4 } \qquad \mathrm { ~ C . ~ } D ( X ) = \frac { 3 } { 1 6 } \qquad \mathrm { ~ D . ~ } E \big ( 4 X + 1 \big ) = 4
$$

# 选择性必修第三册

# 【题型专练】

1．若随机变量 $X$ 服从两点分布，其中 $P ( X = 0 ) = \frac { 1 } { 3 }$ 则 $E ( 3 X + 2 )$ 和 $D ( 3 X ^ { + } \thinspace 2 )$ 的值分别是（）

A.4和4 B.4和2 C.2和4 D.2和2

2．设随机变量 $X$ 服从两点分布，若 $P \big ( X = 1 \big ) - P \big ( X = 0 \big ) = 0 . 4$ ，则 $E \left( \boldsymbol { X } \right) = \mathrm { ~ \left( ~ \begin{array} { l } { \mathrm { ~ \boldsymbol { ~ \chi } ~ } } \end{array} \right) ~ }$

A.0.3 B.0.4 C.0.6 D.0.7

3．随机变量 $X$ 的概率分布为 $P \left( X = 0 \right) = a$ ， $P \left( X = 1 \right) = b$ .若 $E { \bigl ( } X ) = { \frac { 1 } { 3 } }$ 则 $D \left( X \right) =$ （）

A. B. C $\frac { 1 } { 9 }$ ${ \mathrm { D } } . { \begin{array} { l } { { \frac { 2 } { 9 } } } \end{array} }$

4.（多选题）若随机变量 $X$ 服从两点分布，其中 $P \left( X = 1 \right) = \frac { 1 } { 2 }$ ， $E { \big ( } X { \big ) }$ $D ( X )$ 分别为随机变量 $X$ 的均值与方差，则下列结论正确的是（）

$$
P \left( X = 0 \right) = \frac { 1 } { 2 } \qquad \mathrm { ~ B . ~ } \ E \left( X \right) = \frac { 1 } { 2 } \qquad \mathrm { ~ C . ~ } \ E \left( 3 X \right) = \frac { 1 } { 2 } \qquad \mathrm { ~ D . ~ } \ D \left( 2 X \right) = \frac { 1 } { 4 }
$$

5.（多选题）若随机变量 $X$ 服从两点分布，其中 $P \left( X = 0 \right) = \frac { 1 } { 3 }$ 则下列结论正确的是（）

$$
\overline { { { \bf { \Lambda } } } } \cdot { \bf { \Lambda } } P \big ( X = 1 \big ) = E \big ( X \big ) \qquad \mathrm { ~ B . ~ } { \bf { \Lambda } } E \big ( 3 X + 2 \big ) = 4 \qquad \mathrm { ~ C . ~ } { \bf { \Lambda } } D \big ( 3 X + 2 \big ) = 4 \qquad \mathrm { ~ D . ~ } { \bf { \Lambda } } D \big ( X \big ) = \frac { 4 } { { \bf { \Lambda } } _ { 0 } } \mathrm { ~  ~ \Omega ~ }
$$

6.（多选题）若随机变量 $X$ 服从两点分布，其中 $P \left( X = 0 \right) = \frac { 1 } { 3 }$ ， $E { \big ( } X { \big ) }$ ， $D ( X )$ 分别为随机变量 $X$ 的均值与方差，则下列结论正确的是（）

$$
P \left( X = 1 \right) = E \left( X \right) \qquad \mathrm { ~ B . ~ } \ E \left( 3 X + 2 \right) = 4 \qquad \mathrm { ~ C . ~ } \ D \left( 3 X + 2 \right) = 4 \qquad \mathrm { ~ D . ~ } \ D \left( X \right) = \frac { 2 } { \Omega } \ .
$$

# 题型三：独立重复实验发生 $k$ 次的概率

【例1】若某射手每次射击击中目标的概率均为 $\frac { 2 } { 3 }$ 每次射击的结果相互独立，则在他连续4次射击中，恰好有两次击中目标的概率为（）

A. B. 87 C. 48 D. 88【例2】（多选题）下图是一块改造的高尔顿板的示意图，在一块木板上钉着若干排相互平行但相互错开的圆柱形小木块，小木块之间留有适当的空隙作为通道，前面挡有一块玻璃，将小球从顶端放入，小球从通道口落下，第一次与第2层中间的小木块碰撞，以 $\frac { 1 } { 2 }$ 的概率向左或向右滚下，依次经过7次与小木块碰撞，最后掉入编号为1，2，.，6的球槽内.用 $X$ 表示小球经过第7层通过的空隙编号（从左向右的空隙编号依次为0，1，2，，6)，用Y表示小球最后落入球槽的号码，则下列结论正确的是（）

![](images/2fabb0d81c24fab7f53af8b1232e4e07245e9d4f5a1ca2bbaab46a56c171fdd5.jpg)

A.x\~B(62） B. $P \left( Y = 3 \right) = P \left( X = 2 \right) + P \left( X = 3 \right)$ C. $P \left( Y = 2 \right) = P \left( Y = 5 \right)$ D．若放入80个小球，则落入1号球槽的小球个数 $Z$ 的期望为5 【例3】设甲、乙、丙三人每次射击命中目标的概率分别为0.7、0.6、0.5.

(1)三人各向目标射击一次，求至少有一人命中目标的概率及恰有两人命中目标的概率；   
(2)若甲单独向目标射击三次，求他恰好命中两次的概率.

【例4】甲、乙两台机床相互没有影响地生产某种产品，甲机床产品的正品率是0.9，乙机床产品的正品率是0.95.

(1)从甲机床生产的产品中任取3件，求其中恰有2件正品的概率（用数字作答）；  
(2)从甲、乙两台机床生产的产品中各任取1件，求其中至少有1件正品的概率（用数字作答).

【例5】甲、乙两队进行篮球决赛，采取七场四胜制（当一队赢得四场胜利时，该队获胜，决赛结束）．根据前期比赛成绩，甲队的主客场安排依次为“主主客客主客主”.设甲队主场取胜的概率为0.6，客场取胜的概率为0.5，且各场比赛结果相互独立，求甲队以4：1获胜的概率.

# 选择性必修第三册

# 【题型专练】

1.（多选题）某同学投篮1次，投中的概率是0.8，他连续投篮4次，且他每次投篮互不影响，则下列四个选项中，正确的（）

A．他第3次投中的概率是0.8 B．他恰投中3次的概率是0.8×0.2C．他至少投中1次的概率是1-0.24 D．他恰好有连续2次投中的概率为 $3 \times 0 . 8 ^ { 3 } \times 0 . 2$

2．某校为了增强学生对传统文化的继承和发扬，组织了一场类似《诗词大会》 $P K$ 赛（共4局）， $A , B$ 两队各由4名选手组成，每局两队各派一名选手 $P K$ ，除第三局胜者得2分外，其余各胜者均得1分，每局的负者得0分.假设每局比赛 $A$ 队选手获胜的概率均为 $\frac { 3 } { 4 }$ 且各局比赛结果相互独立，比赛结束时 $A$ 队的得分高于 $B$ 队的得分的概率为

3．一枚均匀的硬币连续抛掷三次，至少出现一次正面朝上的概率为

4．现对一批设备的性能进行抽检，第一次检测每台设备合格的概率是0.5，不合格的设备重新调试后进行第二次检测，第二次检测合格的概率是0.6，如果第二次检测仍不合格，则作报废处理．设每台设备是否合格相互独立，则每台设备报废的概率为 .检测3台设备，则恰有2台合格的概率为

5、设甲、乙两人每次射击命中目标的概率分别为 $\frac { 3 } { 4 } \pi = \frac { 4 } { 5 }$ 且各次射击相互独立.

(1)若甲、乙各射击一次，求甲命中但乙未命中目标的概率；   
(2)若甲、乙各射击两次，求两人命中目标的次数相等的概率.

题型四：二项分布的概念

【例1】已知随机变量 $X$ 服从二项分布 $X - B ( 6 , \frac { 1 } { 3 } )$ 则 $P \left( X = 2 \right)$ 等于（）

A. B. 4 $\mathsf { C } . \frac { 1 3 } { 2 4 3 }$ D. $\frac { 8 0 } { 2 4 3 }$

# 选择性必修第三册

【例2】下列说法正确的个数是（）.

$\textcircled{1}$ 某同学投篮的命中率为0.7，他10次投篮中命中的次数 $X$ 是一个随机变量，且 $X$ 服从二项分布 $B \left( 1 0 , 0 . 7 \right)$ $\textcircled{2}$ 某福彩中奖概率为 $p$ ，某人一次买了20张彩票，中奖张数 $X$ 是一个随机变量，且 $X$ 服从二项分布 $B ( 2 0 , p )$ $\textcircled{3}$ 从装有大小与质地相同的5个红球、5个白球的袋中，有放回地摸球，直到摸出白球为止，则摸球次数 $X$ 是随机变量，且 $X$ 服从二项分布 $B \Bigg ( n , \frac { 1 } { 2 } \Bigg )$

A.0个 B.1个 C.2个 D.3个 【例3】同时抛掷三枚硬币，则抛掷一次时出现两枚正面一枚反面的概率为

A. B. 3-8 c.4 D.

【例4】已知随机变量 $X \sim B \left( 2 , p \right)$ ， $Y$ 服从两点分布，若 $P \left( X \geq 1 \right) = 0 . 6 4$ ， $P \left( { Y = 1 } \right) = p$ ，则 $P \left( { Y = 0 } \right) =$ （）

A.0.2 B.0.4 C.0.6 D.0.8【例5】某高三学生进行考试心理素质测试，场景相同的条件下每次通过测试的概率为 $\frac { 4 } { 5 }$ ，则连续测试4次，至少有3次通过的概率为（）

A. 512 B. 256 C. $\frac { 6 4 } { 6 2 5 }$ D. 64   
625 625 125

# 【题型专练】

1.设 $X \sim B \left( 4 , p \right)$ ，其中 $0 < p < \frac { 1 } { 2 }$ 且 $P \left( X = 2 \right) = \frac { 8 } { 2 7 }$ ，那么 $P \big ( X = 1 \big ) = ( )$

A. $\frac { 8 } { 8 1 }$ B. 1608 C. $\frac { 8 } { 2 7 }$ D.

2.若随机变量 $X \sim B ( 5 , \frac { 1 } { 3 } )$ 则 $P ( X = 3 )$ 等于（）

A. $\frac { 4 0 } { 2 4 3 }$ $\mathrm { B } . \frac { 1 } { 3 }$ C. 107 D. 3-5

3.有8件产品，其中4件是次品，从中有放回地取3次（每次1件），若 $X$ 表示取得次品的次数，则 $P ( X \leq 2 ) =$

A. 3-8 B.1 14 c. D. 78

4．将一枚质地均匀的硬币连续抛掷5次，正面向上的次数为 $X$ ，则

A. $X \sim \mathcal { B } ( 5 , 1 )$ B. $X \sim B ( 0 . 5 , 5 )$ C. $X \sim B ( 2 , 0 . 5 )$ D.X\~B(5,0.5)

5．某公司为招聘新员工设计了一个面试方案：应聘者从6道备选题中一次性随机抽取3道题，按照题目要求独立完成，规定：至少正确完成其中2道题便可通过．已知6道备选题中应聘者甲有4道题能正确完成，2道题不能完成；应聘者乙每题正确完成的概率都是 $\frac { 2 } { 3 }$ 且每题正确完成与否互不影响.

（1)求甲正确完成两个面试题的概率；  
(2)求乙正确完成面试题数的分布列.

# 题型五：二项分布的期望方差

【例1】若离散型随机变量 $X$ ， $X \sim B ( 5 , p )$ ，且 $E ( X ) = \frac { 1 0 } { 3 }$ ， 则 $P \left( X \leq 2 \right)$ 为（）

A. 119 B. 427 c.1 D. 2

【例2】某同学参加学校数学知识竞赛，规定每个同学答题20道，已知该同学每道题答对的概率为0.6，则该同学答对题目数量的数学期望和方差分别为（）

A.16,7.2 B.12,7.2 C.12,4.8 D.16,4.8

【例3】（多选题）某计算机程序每运行一次都随机出现一个五位二进制数 $A = a _ { 1 } a _ { 2 } a _ { 3 } a _ { 4 } a _ { 5 }$ （例如10100），其中 $A$ 的各位数中 $a _ { k }$ $k { = } 2$ ，3，4，5）出现0的概率为 $\frac 1 3$ ，出现1的概率为 $\frac { 2 } { 3 }$ ，记 $X = a _ { 2 } + a _ { 3 } + a _ { 4 } + a _ { 5 }$ ，则当程序运行一次时（）

A. $X$ 服从二项分布 B. $P ( X = 1 ) = \frac { 8 } { 8 1 }$ c $X$ 的均值 $E ( X ) = { \frac { 8 } { 3 } }$ D $X$ 的方差 $D ( X ) = \frac { 8 } { 3 }$

【例4】某地区为下岗人员免费提供财会和计算机培训，以提高下岗人员的再就业能力，每名下岗人员可以选择参加一项培训、参加两项培训或不参加培训，已知参加过财会培训的有 $60 \%$ ，参加过计算机培训的有 $7 5 \%$ .假设每个人对培训项目的选择是相互独立的，且各人的选择相互之间没有影响.

(1)任选1名下岗人员，求该人参加过培训的概率；  
(2)任选3名下岗人员，记5为3人中参加过培训的人数，求的分布列和期望.

【例5】某工厂的某种产品成箱包装,每箱200件,每一箱产品在交付用户之前要对产品作检验,如检验出不合格品，则更换为合格品.检验时，先从这箱产品中任取20件作检验,再根据检验结果决定是否对余下的所有产品作检验，设每件产品为不合格品的概率都为 $p$ $\scriptstyle \left( 0 < p < 1 \right)$ ，且各件产品是否为不合格品相互独立.

(1)记20件产品中恰有2件不合格品的概率为 $f ( p )$ 求 $f ( p )$ 的最大值点 $p _ { \theta }$ （即 $f ( p )$ 取最大值时对应的 $p$ 的值).(2)现对一箱产品检验了20件，结果恰有2件不合格品，以(1)中确定的 $p _ { \theta }$ 作为 $p$ 的值，已知每件产品的检验费用为3元，若有不合格品进入用户手中，则工厂要对每件不合格品支付28元的赔偿费用.

$\textcircled{1}$ 若不对该箱余下的产品作检验,这一箱产品的检验费用与赔偿费用之和记为 $X$ ,求 $E { \big ( } X { \big ) }$

$\textcircled{2}$ 以检验费用与赔偿费用和的期望值为决策依据,是否该对这箱余下的所有产品作检验？

# 【题型专练】

1．已知随机变量 $\chi$ 服从二项分布 $B \left( 1 2 , p \right)$ ，若 $E \left( 2 X - 3 \right) = 5$ ，则 $D \left( 3 X \right)$ 等于（）

B.8 C.12 D.24

2.（多选题）已知随机变量 $X \sim B \Bigg ( 3 , \frac { 1 } { 4 } \Bigg )$ 则（）

A.E(x）=  
B.D（x）=  
C．从装有3个红球、9个黑球的袋中一次性摸出3个球，则 $X$ 可表示摸出的红球个数  
D．桐人和茅场晶彦进行3场决斗，且桐人每场决斗的胜率均为 $\frac { 1 } { 4 }$ （不存在平手），则 $X$ 可

3．已知随机变量 $X + Y = 8$ ，若 $X$ 服从二项分布 $B \big ( 1 0 , 0 . 6 \big )$ ，则 $E \big ( Y \big )$ 、 $D ( Y )$ 分别为

4．2022年，某省启动高考综合改革，改革后，不再分文理科，改为采用是 $3 + 1 + 2$ ”模式，“3"是语文、外语、数学三科必考，“1"是在物理与历史两科中选择一科，“2"是在化学，生物，政治，地理四科中选择两科作为高考科目，某学校为做好选课走班教学，给出三种可供选择的组合进行模拟选课，其中 $A$ 组合：物理、化学、生物， $B$ 组合：历史、政治、地理， $C$ 组合：物理、化学、地理.根据选课数据得到，选择 $A$ 组合的概率为 $\frac { 2 } { 3 }$ ，选择 $B$ 组合的概率为 $\frac { 1 } { 6 }$ 选择 $C$ 组合的概率为 $\frac { 1 } { 6 }$ 甲、乙、丙三位同学每人选课是相互独立的.

(1)求这三位同学恰好有两位同学选择相同组合的概率.  
(2）记 $X$ 表示这三人中选择含地理的组合的人数，求 $X$ 的分布列及数学期望.

5.为保护学生视力，让学生在学校专心学习，促进学生身心健康发展，教育部于2021年1月15日下发文件《关于加强中小学生手机管理工作的通知》，几对中小学生的手机使用和管理作出了相关的规定，某中学研究型学习小组调查研究"中学生每日使用手机的时间”。从该校学生中随机选取了100名学生，调查得到如下表所示的统计数据.

<html><body><table><tr><td>时间t/min</td><td></td><td></td><td></td><td>[0,12）[1,24）[24,36）36,48）[4860）[60,72]</td><td></td><td></td></tr><tr><td>人数</td><td>6</td><td>30</td><td>35</td><td>19</td><td>6</td><td>4</td></tr></table></body></html>

(1)从该校任选1名学生，估计该学生每日使用手机的时间小于 $3 6 \mathrm { { m i n } }$ 的概率；  
(2)估计该校所有学生每日使用手机的时间 $t$ 的中位数；  
(3)以频率估计概率，若在该校学生中随机挑选3人，记这3人每日使用手机的时间在[48,72]的人数为随机变量$X$ ，求 $X$ 的分布列和数学期望 $E { \big ( } X { \big ) }$

6．某公司在年会上举行抽奖活动，有甲，乙两个抽奖方案供员工选择.方案甲：员工最多有两次抽奖机会，每次抽奖的中奖率均为 $\frac { 3 } { 5 }$ ，第一次抽奖，若未中奖，则抽奖结束，若中奖，则通过抛一枚质地均匀的硬币，决定是否继续进行第二次抽奖，规定：若抛出硬币，反面朝上，员工则获得奖金500元，不进行第二次抽奖；若正面朝上，员工则需进行第二次抽奖，且在第二次抽奖中，若中奖，则获得奖金1000元；若未中奖，则所获得奖金为0元.方案乙：员工连续三次抽奖，每次中奖率均为 $\frac { 1 } { 5 }$ ， 每次中奖均可获得奖金500元.

(1)求某员工选择方案甲进行抽奖所获奖金 $X$ （元）的分布列；  
(2)试比较某员工选择方案乙与选择方案甲进行抽奖，哪个方案更划算？请说明理由.

7．为防止风沙危害，某地决定建设防护绿化带，种植杨树、沙柳等植物．某人一次种植了 $n$ 株沙柳，各株沙柳的成活与否是相互独立的，成活率为 $p$ 设 $X$ 为成活沙柳的株数，期望 $E \left( X \right) = 3$ ，方差 $D \left( X \right) = \frac { 3 } { 2 }$

(1）求 $n$ 和 $p$ 的值，并写出 $X$ 的分布列；  
(2)若有3株或3株以上的沙柳未成活，则需要补种，求需要补种沙柳的概率.

8．近两年肆虐全球的新型冠状病毒是以前从未在人体中发现的冠状病毒新毒株.人感染了新型冠状病毒后常见体征有呼吸道症状、发热、咳嗽、气促和呼吸困难等.在较严重病例中，感染可导致肺炎、严重急性呼吸综合征、肾衰竭，甚至死亡.核酸检测是诊断新冠肺炎的重要依据，首先取病人的唾液或咽拭子的样本，再提取唾液或咽拭子样本里的遗传物质，若有病毒，样本检测会呈现阳性，否则为阴性.根据统计发现，疑似病例核酸检测呈阳性的概率为 $\frac 1 3$ .现有4例疑似病例，分别对其取样、检测，多个样本检测时，既可以逐个化验，也可以将若干个样本混合在一起化验.混合样本中只要有病毒，则混合样本化验结果就会呈阳性，若混合样本呈阳性，则将该组中备份的样本再逐个化验；若混合样本呈阴性，则判定该组各个样本均为阴性，无需再检验.现有以下三种方案：

方案一：逐个化验；  
方案二：四个样本混合在一起化验；  
方案三：平均分成两组，分别混合在一起化验.

在新冠肺炎爆发初期，由于检查能力不足，化检次数的期望值越小，则方案越"优”

(1)若按方案一，求4个疑似病例中恰有2例呈阳性的概率；  
(2)现将该4例疑似病例样本进行化验，请问：方案一、二、三中哪个最"优"？并说明理由.

9．2022年8月7日是中国传统二十四节气“立秋”，该日，“秋天的第一杯奶茶”再度出圈，据此，学校社会实践小组随机调查了该地区100位奶茶爱好者的年龄，得到如下样本数据频率分布直方图.

![](images/61ebf34960789216d652fa85aabc6264269e3021a1d234d3b0cb419f1bd488ed.jpg)

(1)估计奶茶爱好者的平均年龄；（同一组数据用该区间的中点值作代表）

(2)估计奶茶爱好者年龄位于区间[20,60)的概率；

(3)以频率替代概率进行计算，若从该地区所有奶茶爱好者中任选3人，求3人中年龄在30岁以下的人数 $\chi$ 的分布列和期望.

10．第24届冬季奥运会将于2022年2月在北京举办，为了普及冬奥知识，某校组织全体学生进行了冬奥知识答题比赛，从全校众多学生中随机选取了10名学生，得到他们的分数统计如下表：

<html><body><table><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td>分数段[30,40）[40,50）[50,60）[6070）[70,80）[80.90）[90,100]</td></tr><tr><td>人数</td><td>1</td><td>1</td><td>1</td><td>3</td><td>2</td><td>1</td><td>1</td></tr></table></body></html>

规定60分以下为不及格；60分及以上至70分以下为及格；70分及以上至80分以下为良好；80分及以上为优秀．将频率视为概率.

(1)此次比赛中该校学生成绩的优秀率是多少？(2)从全校学生中随机抽取2人，以 $X$ 表示这2人中成绩良好和优秀的人数之和，求 $X$ 的分布列和数学期望.

题型六：超几何分布的概率

【例1】设袋中有80个红球，20个白球，若从袋中任取10个球，则其中恰有6个红球的概率为（）

A. $\frac { \mathbf { C } _ { 8 0 } ^ { 4 } \cdot \mathbf { C } _ { 1 0 } ^ { 6 } } { \mathbf { C } _ { 1 0 0 } ^ { 1 0 } }$ $\begin{array} { r } { \mathrm { ~  ~ { ~ \mathsf ~ { ~ B ~ } ~ } ~ } . \begin{array} { l l } { \mathrm { ~  ~ { ~ C ^ 6 _ { 8 0 } } \cdot C ^ { 4 _ { 1 0 } } _ { 1 0 } ~ } } & { \qquad \mathrm { ~  ~ { ~ C . ~ } ~ } \frac { \mathrm { ~  ~ { ~ C ^ 6 _ { 8 0 } } \cdot C ^ 6 _ { 2 0 } ~ } } { \mathrm { ~  ~ { ~ C ^ 6 _ { 1 0 0 } } ~ } } } \end{array} \qquad \mathrm { ~  ~ { ~ D . ~ } ~ } \begin{array} { l } { \mathrm { ~  ~ { ~ C ^ 6 _ { 8 0 } } \cdot C ^ 4 _ { 2 0 } ~ } } \\ { \mathrm { ~  ~ { ~ C ^ 6 _ { 1 0 0 } } ~ } } \end{array} } \end{array}$ 【例2】袋中有6个大小相同的黑球，编号为1,2.3,4,5,6，还有4个同样大小的白球，编号为7,8,9,10，现从中任取4个球，则下列结论中正确的是（）

$\textcircled{1}$ 取出的最大号码 $X$ 服从超几何分布； $\textcircled{2}$ 取出的黑球个数 $Y$ 服从超几何分布；$\textcircled{3}$ 取出2个白球的概率为 $\frac { 1 } { 1 4 }$ $\textcircled{4}$ 若取出一个黑球记2分，取出一个白球记1分，则总得分最大的概率为 $\frac { 1 } { 1 4 }$

A. $\textcircled{1} \textcircled{2}$ B.②④ C. $\textcircled{3} \textcircled{4}$ D. $\textcircled{1} \textcircled{3} \textcircled{4}$ 【例3】厂家在产品出厂前，需对产品做检验，厂家对一批产品发给商家时，商家按规定拾取一定数量的产品做检验，以决定是否验收这批产品：

(1)若厂家库房中的每件产品合格的概率为0.3，从中任意取出4种进行检验，求至少有1件是合格产品的概率；(2)若厂家发给商家20件产品，其中有3件不合格，按合同规定该商家从中任取2件，来进行检验，只有2件产品合格时才接收这些产品，否则拒收，分别求出该商家检验出不合格产品为1件和2件的概率，并求该商家拒收这些产品的概率.

# 选择性必修第三册

# 【题型专练】

1．在含有3件次品的50件产品中，任取2件，则至少取到1件次品的概率为（）

A $\frac { C _ { 3 } ^ { 1 } C _ { 4 7 } ^ { 1 } } { C _ { 5 0 } ^ { 2 } }$ •B $\frac { C _ { 3 } ^ { 2 } C _ { 4 7 } ^ { 0 } } { C _ { 5 0 } ^ { 2 } }$ $\frac { C _ { 3 } ^ { 1 } C _ { 3 } ^ { 2 } } { C _ { 5 0 } ^ { 2 } }$ D. Cc+gc

2.（多选题）在一个袋中装有质地大小一样的6个黑球，4个白球，现从中任取4个小球，设取出的4个小球中白球的个数为 $X$ ，则下列结论正确的是（）

A. $P \left( X = 1 \right) = \frac { 8 } { 2 1 }$ B.随机变量 $X$ 服从二项分布C.随机变量 $X$ 服从超几何分布D. $E { \bigl ( } X { \bigr ) } = { \frac { 8 } { 5 } }$

3．一机床生产了100个汽车零件，其中有40个一等品、50个合格品、10个次品，从中随机地抽出4个零件作为样本.用 $X$ 表示样本中一等品的个数.

（1）若有放回地抽取，求 $X$ 的分布列；

（2）若不放回地抽取，用样本中一等品的比例去估计总体中一等品的比例.

$\textcircled{1}$ 求误差不超过0.2的 $X$ 的值；$\textcircled{2}$ 求误差不超过0.2的概率（结果不用计算，用式子表示即可）

# 题型七：超几何分布的期望方差

【例1】某冷饮店的冰淇淋在一天中销量为200个，三种口味各自销量如表所示：把频率视作概率，从卖出的冰淇淋中随机抽取10个，记其中草莓味的个数为 $X$ ，则 $E { \big ( } X { \big ) } = ( \quad )$

<html><body><table><tr><td>冰淇淋口味</td><td>草莓味</td><td>巧克力味</td><td>原味</td></tr><tr><td>销量（个）</td><td>40</td><td>60</td><td>100</td></tr></table></body></html>

A.5 B.3 C.2 D.1【例2】在一个袋中装有质地大小一样的6个黑球，4个白球，现从中任取4个小球，设取的4个小球中白球的个数为 $X$ ，则下列结论正确的是（）

A $P ( X = 1 ) = \frac { 2 } { 5 }$ B．随机变量 $X$ 服从二项分布C．随机变量 $X$ 服从几何分布 D. $E { \bigl ( } X { \bigr ) } = { \frac { 8 } { 3 } }$

【例3】（多选题）某工厂进行产品质量抽测，两位员工随机从生产线上各抽取数量相同的一批产品，已知在两人抽取的一批产品中均有5件次品，员工 $A$ 从这一批产品中有放回地随机抽取3件产品，员工 $B$ 从这一批产品中无放回地随机抽取3件产品．设员工 $A$ 抽取到的3件产品中次品数量为 $X$ ，员工 $B$ 抽取到的3件产品中次品数量为Y， $k = 0$ ，1，2，3．则下列判断正确的是（）

A．随机变量 $X$ 服从二项分布 B．随机变量 $Y$ 服从超几何分布C. $P \left( X = k \right) < P \left( Y = k \right)$ D. $E \left( X \right) = E \left( Y \right)$

【例4】一个箱子中有6个大小相同产品，其中4个正品、2个次品，从中任取3个产品，记其中正品的个数为随机变量 $X$ ，则 $X$ 的均值 $E { \big ( } X { \big ) } =$

【例5】全国第36届中国化学奥林匹克竞赛已经结束，我校学生取得了优异成绩，为了方便统计，现将学生成绩转化为百分制，从中随机抽取了100名学生的成绩，经统计，这批学生的成绩全部介于40至100之间，将数据按照[40，50），[50，60），[60，70），[70，80），[80，90），[90，100]分成6组，制成了如图所示的频率分布直方图.

![](images/8e279259b65383418beeeaa7015adfbffbab5217d0780a6cfa6d26bd71d13066.jpg)

(1)求频率分布直方图中m的值，并估计这100名学生成绩的中位数；(2)在这100名学生中用分层抽样的方法从成绩在[70，80），[80，90），[90，100]的三组中抽取了10人，再从这10人中随机抽取3人，记 $X$ 为3人中成绩在[80，90）的人数，求 $X$ 的分布列和数学期望；

【例6】北京冬奥会某个项目招募志愿者需进行有关专业、礼仪及服务等方面知识的测试，测试合格者录用为志愿者.现有备选题10道，规定每次测试都从备选题中随机抽出3道题进行测试，至少答对2道题者视为合格，若甲能答对其中的5道题，求：

(1）甲测试合格的概率；  
(2)甲答对的试题数 $X$ 的分布列和数学期望.

# 【题型专练】

1．设50个产品中有10个次品，任取产品20个，取到的次品可能有 $X$ 个，则 $E X = ( \begin{array} { l l l } \end{array} )$

A.4 B.3 C.2 D.1

2.已知6件产品中有2件次品，4件正品，检验员从中随机抽取3件进行检测，记取到的正品数为 $X$ ，则 $E { \big ( } X { \big ) } =$ （）

A.2 B.1 C. 4-3 D.

3．一个袋子中100个大小相同的球，其中有40个黄球，60个白球，从中不放回地随机摸出20个球作为样本，用随机变量 $X$ 表示样本中黄球的个数，则 $X$ 服从（）

A．二项分布，且 $E \left( X \right) = 8$ B．两点分布，且 $E \big ( X \big ) = 1 2$ C．超几何分布，且 $E { \big ( } X { \big ) } = 8$ D．超几何分布，且 $E \big ( X \big ) = 1 2$

4.（多选题）在一个袋中装有大小一样的6个豆沙粽，4个咸肉粽，现从中任取4个粽子，设取出的4个粽子中成肉粽的个数为 $X$ ，则下列结论正确的是（）

A. $P ( X = 2 ) = \frac { 3 } { 7 }$ B. $E X = \frac { 8 } { 3 }$ C．随机变量 $X$ 服从超几何分布 D.P(1<X<4)=35

5．一个口袋中装有7个球，其中有5个红球，2个白球抽到红球得2分，抽到白球得3分．现从中任意取出3个球，则取出3个球的得分 $Y$ 的均值 $E \big ( Y \big )$ 为

6．为进一步做好新冠疫情防控工作，某地组建一只新冠疫苗宣传志愿者服务队，现从2名男志愿者，3名女志愿者中随机抽取2人作为队长，则在“抽取的2人中至少有一名女志愿者"的前提下“抽取的2人全是女志愿者”的概率是 ；若用 $X$ 表示抽取的2人中女志愿者的人数，则 $E \left( X \right) =$

7.某校高三年级有男生360人，女生240人，对高三学生进行问卷调查，采用分层抽样的方法，从这600名学生中抽取5人进行问卷调查，再从这5名学生中随机抽取3人进行数据分析，则这3人中既有男生又有女生的概率是 ，记抽取的男生人数为 $X$ ，则随机变量 $X$ 的数学期望为

8．某校高一，高二年级的学生参加书法比赛集训，高一年级推荐了4名男生，2名女生，高二年级推荐了3名男生，5名女生，从参加集训的男生中随机抽取3人，女生中随机抽取3人组成代表队参加市上比赛.

(1)求高一恰好有1名学生入选代表队的概率；  
(2)正式比赛时，从代表队的6名队员中随机抽取2人参赛，设表示参赛的男生人数，求 $\xi$ 的分布列和数学期望

# 第11讲分布列概率中的三大最值问题

# 题型一：二项分布的转化为数列问题求最值

$\textcircled{1}$ 当 $p$ 给定时，可得到函数 $f ( k ) = C _ { n } ^ { k } p ^ { k } ( 1 - p ) ^ { n - k } , k = 0 , 1 , 2 , \cdots n$ ，这个是数列的最值问题.$\frac { p _ { k } } { p _ { k - 1 } } = \frac { C _ { k } ^ { n } p ^ { k } ( 1 - p ) ^ { n - k } } { C _ { n } ^ { k - 1 } p ^ { k - 1 } ( 1 - p ) ^ { n - k + 1 } } = \frac { ( n - k + 1 ) p } { k ( 1 - p ) } = \frac { k ( 1 - p ) + ( n + 1 ) p - k } { k ( 1 - p ) } = 1 + \frac { ( n + 1 ) p - k } { k ( 1 - p ) } .$ 分析：当 $k < ( n + 1 ) p$ 时， $p _ { k } > p _ { k - 1 }$ ， $p _ { k }$ 随 $k$ 值的增加而增加；当 $k > ( n + 1 ) p$ 时，$p _ { k } < p _ { k - 1 }$ ， $p _ { k }$ 随 $k$ 值的增加而减少.如果 $( n + 1 ) p$ 为正整数，当 $k = ( n + 1 ) p$ 时， $p _ { k } = p _ { k - 1 }$ ，此时这两项概率均为最大值.如果 $( n + 1 ) p$ 为非整数，而 $k$ 取 $( n + 1 ) p$ 的整数部分，则 $p _ { k }$ 是唯一的最大值.注：在二项分布中，若数学期望为整数，则当随机变量 $k$ 等于期望时，概率最大.

# 【精选例题】

【例1】某人在11次射击中击中目标的次数为 $X$ ，若 $X \sim \mathit { B } \left( 1 1 , 0 . 8 \right)$ ，若 $P \left( X = k \right)$ 最大，则 $k =$ （）

A.7 B.8 C.9 D.10【例2】（多选题）下列选项中正确的是（）

A．已知随机变量 $X$ 服从二项分布 $B \biggl ( 1 0 , \frac { 1 } { 2 } \biggr )$ 则 $D \left( 2 X \right) = 5$   
B．口袋中有大小相同的7个红球、2个蓝球和1个黑球，从中任取两个球，记其中红球的个数为随机变量 $X$ ，  
则X的数学期望E(x)=  
C．抛掷一枚质地均匀的骰子一次，所得的样本空间为 $\varOmega = \left\{ 1 , 2 , 3 , 4 , 5 , 6 \right\}$ ，令事件 $A = \left\{ 2 , 3 , 4 \right\}$ ，事件 $B = \{ 1 , 2 \}$ ，  
则事件A与事件 $B$ 相互独立  
D．某射击运动员每次射击击中目标的概率为0.8，则在9次射击中，最有可能击中的次数是7次

# 选择性必修第三册

【例3】高中生的数学阅读水平与其数学阅读认知、阅读习惯和方法等密切相关．为了解高中生的数学阅读现状，调查者在某校随机抽取100名学生发放调查问卷，在问卷中对于学生每周数学阅读时间统计如下：

<html><body><table><tr><td>时间（x小时/周）</td><td>0</td><td>0&lt;x≤0.5</td><td>0.5&lt;x≤1</td><td>x&gt;1</td></tr><tr><td>人数</td><td>2040</td><td></td><td>30</td><td>10</td></tr></table></body></html>

(1)为了解学生数学阅读时间偏少的原因，采用样本量比例分配的分层随机抽样从这100名学生中随机抽取10名学生，再从这10人中随机抽取2名进行详细调查，求这2名学生中恰有一人每周数学阅读时间大于0.5小时的概率；

(2)用频率估计概率，从该校所有学生中随机抽取10名学生，用 $P \left( X = k \right)$ 表示这10名学生中恰有$k \left( k \in \mathbf { N } , 0 \leq k \leq 1 0 \right)$ 名学生数学阅读时间在(0,0.5]小时的概率，求 $P ( X = k )$ 取最大值时对应的 $k$ 的值.

# 【题型专练】

1.（多选题）某同学共投篮12次，每次投篮命中的概率为0.8，假设每次投篮相互独立，记他投篮命中的次数为随机变量 $X$ ，下列选项中正确的是（）

A. $X \sim { \cal B } \left( 1 2 , 0 . 8 \right)$ B. $E \left( X \right) = 9 . 6 $ c. $D \left( 2 X \right) = 3 . 8 4$ D．该同学投篮最有可能命中9次

2.若随机变量 $X$ 服从二项分布 $B \biggl ( 1 5 , \frac { 1 } { 4 } \biggr )$ ，则使 $P \left( X = k \right)$ 取得最大值时， $k =$ （24号

3．已知随机变量 $X \sim B \left( 6 , 0 . 8 \right)$ ，若 $P \left( X = k \right)$ 最大，则 $D \left( k X + 1 \right) =$

4.一年之计在于春，一日之计在于晨，春天是播种的季节，是希望的开端．某种植户对一块地的 $n$ 个坑进行播种，每个坑播3粒种子，每粒种子发芽的概率均为 $\frac { 1 } { 2 }$ ，且每粒种子是否发芽相互独立．对每一个坑而言，如果至少有两粒种子发芽，则不需要进行补播种，否则要补播种，则当 $n =$ 时，有3个坑要补播种的概率最大，最大概率为

5．小区为了加强对“新型冠状病毒"的防控，确保居民在小区封闭期间生活不受影响，小区超市采取有力措施保障居民正常生活物资供应.为做好甲类生活物资的供应，超市对社区居民户每天对甲类生活物资的购买量进行了调查，得到了以下频率分布直方图.

![](images/017ffe56c9512dff20505b90aa2b288b914d0ad3ce023440d8b109235670de00.jpg)

(1)从小区超市某天购买甲类生活物资的居民户中任意选取5户.若抽取的5户中购买量在[3,6]（单位：kg）的户数为2户，从5户中选出3户进行生活情况调查，记3户中需求量在[3,6]（单位：kg）的户数为，求 $\xi$ 的分布列和期望；(2)将某户某天购买甲类生活物资的量与平均购买量比较，当超出平均购买量不少于 $0 . 5 \mathrm { k g }$ 时，则该居民户称为“迫切需求户”，若从小区随机抽取10户，且抽到 $k$ 户为"迫切需求户"的可能性最大，试求 $k$ 的值.

题型二：二项分布的转化为导数问题求最值当 $k$ 给定时，可得到函数 $f ( p ) = C _ { n } ^ { k } p ^ { k } ( 1 - p ) ^ { n - k } ,$ $p \in ( 0 , 1 )$ ，这个是函数的最值问题，这可以用导数求函数最值与最值点.

分析： $f ^ { \prime } ( p ) = C _ { n } ^ { k } \Bigl [ k p ^ { k - 1 } ( 1 - p ) ^ { n - k } - p ^ { k } ( n - k ) ( 1 - p ) ^ { n - k - 1 } \Bigr ]$   
$= C _ { n } ^ { k } p ^ { k - 1 } ( 1 - p ) ^ { n - k - 1 } \bigl [ k ( 1 - p ) - ( n - k ) p \bigr ] = C _ { n } ^ { k } p ^ { k - 1 } ( 1 - p ) ^ { n - k - 1 } ( k - n p ) .$   
当 $k = 1 , 2 , \cdots , n - 1$ 时，由于当 $p < { \frac { k } { n } }$ 时， $f ^ { \prime } ( p ) > 0$ $f ( p )$ 单调递增，当 $p > { \frac { k } { n } }$ 时， $f ^ { \prime } ( p ) < 0$ ， $f ( p )$ 单调递减，故当 $p = { \frac { k } { n } }$ 时 $f ( p )$ 取得最大值， $f ( \boldsymbol { p } ) _ { \mathrm { m a x } } = f ( \frac { k } { n } )$ 又当 $p \to 0 , f ( p ) \to 1$ 当 $p  0$ 时 $f ( \boldsymbol { p } ) \to 0$ 从而 $f ( p )$ 无最小值.

# 【精选例题】

【例1】（2018年全国1卷）．某工厂的某种产品成箱包装，每箱200件，每一箱产品在交付用户之前要对产品作检验，如检验出不合格品，则更换为合格品．检验时，先从这箱产品中任取20件作检验，再根据检验结果决定是否对余下的所有产品作检验，设每件产品为不合格品的概率都为 $p ( 0 < p < 1 )$ ，且各件产品是否为不合格品相互独立.

（1）记20件产品中恰有2件不合格品的概率为 $f ( p )$ 求 $f ( p )$ 的最大值点 $p _ { \theta }$ ：

（2）现对一箱产品检验了20件，结果恰有2件不合格品，以（1）中确定的 $p _ { o }$ 作为 $p$ 的值．已知每件产品的检验费用为2元，若有不合格品进入用户手中，则工厂要对每件不合格品支付25元的赔偿费用.

（i）若不对该箱余下的产品作检验，这一箱产品的检验费用与赔偿费用的和记为 $X$ ，求 $E X$ ·（ii）以检验费用与赔偿费用和的期望值为决策依据，是否该对这箱余下的所有产品作检验？

【例2】设离散型随机变量 $X$ 和Y有相同的可能取值，它们的分布列分别为 $P \left( X = a _ { k } \right) = x _ { k }$ ， $P \left( \boldsymbol { Y } = \boldsymbol { a } _ { k } \right) = \boldsymbol { y } _ { k }$ ， $x _ { k } > 0$ ，$y _ { k } > 0$ ， $k = 1 , 2 , \cdots , n , \sum _ { k = 1 } ^ { n } x _ { k } = \sum _ { k = 1 } ^ { n } y _ { k } = 1$ ，指标 $D ( X \parallel Y )$ 可用来刻画 $X$ 和 $Y$ 的相似程度，其定义为$D ( X \parallel Y ) = \sum _ { k = 1 } ^ { n } x _ { k } \ln \frac { x _ { k } } { y _ { k } }$ 设 $X \sim B ( n , p ) , 0 < p < 1$

(1)若 $Y \sim B ( n , q ) , 0 < q < 1$ ，求 $D ( X \| Y )$   
(2)若 $n = 2 , P ( Y = k - 1 ) = \frac { 1 } { 3 } , k = 1 , 2 , 3$ =1,2,，求D最小  
(3）对任意与 $X$ 有相同可能取值的随机变量 $Y$ ，证明： $D ( X \parallel Y ) \geq 0$ ，并指出取等号的充要条件

# 选择性必修第三册

# 【跟踪训练】

1.某超市推出了一项优惠活动，规则如下：  
规则一：顾客在本店消费满100元，返还给顾客10元消费券；  
规则二：顾客在本店消费满100元，有一次抽奖的机会，每次中奖，就会有价值20元的奖品，顾客每次抽奖是否中奖相互独立.  
(1)某顾客在该超市消费了300元，进行了3次抽奖，每次中奖的概率均为 $p$ .记中奖2次的概率为 $f ( p )$ ，求 $f ( p )$ 取得最大值时， $p$ 的值 $p _ { o }$ ：  
(2)若某顾客有3次抽奖的机会，且中奖率均为 $p _ { \theta }$ ，则该顾客选择哪种规则更有利？请说明理由.

2.某单位为了激发党员学习党史的积极性，现利用"学习强国"APP中特有的"四人赛"答题活动进行比赛，活动规则如下：一天内参与“四人赛"活动，仅前两局比赛可获得积分，第一局获胜得3分，第二局获胜得2分，失败均得1分，小张周一到周五每天都参加了两局"四人赛"活动，已知小张第一局和第二局比赛获胜的概率分别为 $p$ $\cdot 0 { < } p { < } 1 )$ ， $\frac { 1 } { 2 }$ ，且各局比赛互不影响.

（1）若 $p = \frac { 2 } { 3 }$ 记小张一天中参加“四人赛”活动的得分为X，求 $X$ 的分布列和数学期望；(2)设小张在这5天的“四人赛"活动中，恰有3天每天得分不低于4分的概率为 $f ( p )$ ，试问当 $p$ 为何值时， $f \left( p \right)$ 取得最大值.

题型三：超几何分布的概率最值

将从 $( a + b )$ 件产品中取出 $n$ 件产品的可能组合全体作为样本点，总数为 $C _ { a + b } ^ { n }$ .其中，次品出现 $k$ 次的可能为  
$C _ { a } ^ { k } C _ { b } ^ { n - k }$ 令 $N = a + b$ ，则所求概率为h（N)=CCC  
即 C N2-aN-nN+aN （号 $\frac { h _ { k } ( N ) } { h _ { k } ( N - 1 ) } = \lambda$ 则当 $a n > k N$ 时 $\lambda > 1$ 当 $a n < k N$ 時时CN-  
$\lambda < 1$ ，即当 $N < \frac { a n } { k }$ 时， $h _ { k } ( N )$ 是关于 $N$ 的增函数；当 $N > \frac { a n } { k }$ 时， $h _ { k } ( N )$ 是关于 $N$ 的减函数.所以当  
$N = \left[ { \frac { a n } { k } } \right]$ 时， $h _ { k } ( N )$ 达到最大值.

# 【精选例题】

【例1】设随机变量 $X \sim H ( 1 0 , M , 1 0 0 0 )$ $2 \leq M \leq 9 9 2$ 且 $M \in \mathbf { N } ^ { * }$ ）， $H ( 2 ; 1 0 , M , 1 0 0 0 )$ 最大时， $E ( X ) = \left( \begin{array} { l l l } \end{array} \right)$

A.1.98 B.1.99 C.2.00 D.2.01

【例2】（2023届四省联考）一个池塘里的鱼的数目记为 $N$ ，从池塘里捞出200尾鱼，并给鱼作上标识，然后把鱼放回池塘里，过一小段时间后再从池塘里捞出500尾鱼， $X$ 表示捞出的500尾鱼中有标识的鱼的数目.

（1）若 $N = 5 0 0 0$ ，求 $X$ 的数学期望；  
（2）已知捞出的500尾鱼中15尾有标识，试给出 $N$ 的估计值（以使得 $P ( X = 1 5 )$ 最大的 $N$ 的值作为 $N$ 的估计  
值）

# 【跟踪训练】

1.2023年中央一号文件指出,艮旋要复兴,乡村必振兴.为助力乡村振兴,某电商平台准备为某地的农副特色产品开设直播带货专部.(公众号浙江省高中数学)直播前，此平台用不同的单价试销,并在购买的顾客中进行体验调本向卷.已知有 $N ( N > 3 0 )$ 名热心参与问卷的顾客，此平台决定在直播中专门为他们设置两次抽奖活迹次抽奖都是由系统独立、随机地从这 $N$ 名顾客中抽取20名顾客,抽中顾客会有礼品赠送，若直拱时这 $N$ 名顾客都在线，记两次抽中的顾客总人数为 $X$ (不重复计数).

（1）若甲是这 $N$ 名顾客中的一人,且甲被抽中的概率为 $\frac { 9 } { 2 5 }$ ，求 $N$ （2）求使 $P ( X = 3 0 )$ 取得最大值时的整数 $N$ ·

# 【考点分析】

$\textcircled{1}$ 转移概率：对于有限状态集合 $S$ ，定义： $P _ { i , j } = P ( X _ { n + 1 = j } \mid X _ { n = i } )$ 为从状态 $i$ 到状态 $j$ 的转移概率.$\textcircled{2}$ 马尔可夫链：若 $P ( X _ { n + 1 = j } \mid X _ { n = i } , X _ { n - 1 = i _ { n - 1 } } , \cdot \cdot \cdot , X _ { 0 = i _ { 0 } } ) = P ( X _ { n + 1 = j } \mid X _ { n = i } ) = P _ { i j }$ ，即未来状态 $X _ { n + 1 }$ 只受当前状态$X _ { n }$ 的影响，与之前的 $X _ { n - 1 } , X _ { n - 2 } , \cdots , X _ { 0 }$ 无关.

$\textcircled{3}$ 完备事件组：如果样本空间 $\Omega$ 中一组事件组 $\{ A _ { 1 } , A _ { 2 } , \cdots A _ { n } \}$ 符合下列两个条件：

（1） $A _ { i } \cap A _ { j } = \emptyset , i \neq j , i , j = 1 , 2 , \cdots n \ ; \ \left( 2 \right) \ \bigcup _ { k = 1 } ^ { n } A _ { k } = \Omega .$ 则称 $\{ A _ { 1 } , A _ { 2 } , \cdots A _ { n } \}$ 是 $\Omega$ 的一个完备事件组，也称是 $\Omega$ 的一个分割.

$\textcircled{4}$ 全概率公式：设 $\{ A _ { 1 } , A _ { 2 } , \cdots A _ { n } \}$ 是一个完备事件组，则有

$$
P ( B ) = \sum _ { k = 1 } ^ { n } P ( A _ { k } ) P ( B \mid A _ { k } )
$$

$\textcircled{5}$ 一维随机游走模型，即：设数轴上一个点，它的位置只能位于整点处，在时刻 $t = 0$ 时，位于点 $x = i ( i \in N ^ { + } )$ ，下一个时刻，它将以概率 $\alpha$ 或者 $\beta$

（ $\alpha \in ( 0 , 1 ) , \alpha + \beta = 1 \ ;$ 向左或者向右平移一个单位.若记状态 $X _ { t = i }$ 表示：在时刻 $t$ 该点位于位置 $x = i ( i \in N ^ { + } )$ ，那么由全概率公式可得：

$$
P ( X _ { _ { t + 1 = i } } ) = P ( X _ { _ { t = i - 1 } } ) \cdot P ( X _ { _ { t + 1 = i } } \mid X _ { _ { t = i - 1 } } ) + P ( X _ { _ { t = i + 1 } } ) \cdot P ( X _ { _ { t + 1 = i } } \mid X _ { _ { t = i + 1 } } )
$$

另一方面，由于 $P ( X _ { \scriptscriptstyle { t + 1 = i } } \mid X _ { \scriptscriptstyle { t = i - 1 } } ) = \beta , P ( X _ { \scriptscriptstyle { t + 1 = i } } \mid X _ { \scriptscriptstyle { t = i + 1 } } ) = \alpha$ ，代入上式可得：

$$
P _ { i } = \alpha \cdot P _ { i + 1 } + \beta \cdot P _ { i - 1 } .
$$

进一步，我们假设在 $x = 0$ 与 $x = m ( m > 0 , m \in N ^ { + } )$ 处各有一个吸收壁，当点到达吸收壁时被吸收，不再游走.  
于是， $P _ { 0 } = 0 , P _ { m } = 1$ .随机游走模型是一个典型的马尔科夫过程.

进一步，若点在某个位置后有三种情况：向左平移一个单位，其概率为 $a$ ，原地不动，其概率为 $b$ ，向右平移一个单位，其概率为 $c$ ，那么根据全概率公式可得：

$$
P _ { i } = a P _ { i - 1 } + b P _ { i } + c P _ { i + 1 }
$$

# 【精选例题】

【例1】（2023·新高考1卷）甲、乙两人投篮，每次由其中一人投篮，规则如下：若命中则此人继续投篮，若末命中则换为对方投篮.无论之前投篮情况如何，甲每次投篮的命中率均为0.6，乙每次投篮的命中率均为0.8．由抽签确定第1次投篮的人选，第1次投篮的人是甲、乙的概率各为0.5.

（1）求第2次投篮的人是乙的概率；（2）求第i次投篮的人是甲的概率；  
（3）已知：若随机变量 $X _ { i }$ 服从两点分布，且 $P \left( X _ { i } = 1 \right) = 1 - P \left( X _ { i } = 0 \right) = q _ { i } , i = 1 , 2 , \cdots , n$ ，则 $E { \Bigg ( } \sum _ { i = 1 } ^ { n } X _ { i } { \Bigg ) } = \sum _ { i = 1 } ^ { n } q _ { i }$ .记  
前 $n$ 次（即从第1次到第 $n$ 次投篮）中甲投篮的次数为 $Y$ ，求 $E \big ( Y \big )$ ：

【例2】某公司为激励员工，在年会活动中，该公司的 $n \left( n \geq 3 \right)$ 位员工通过摸球游戏抽奖，其游戏规则为：每位员工前面都有1个暗盒，第1个暗盒里有3个红球与1个白球.其余暗盒里都恰有2个红球与1个白球，这些球的形状大小都完全相同.第1位员工从第1个暗盒里取出1个球，并将这个球放入第2个暗盒里，第2位员工再从第2个暗盒里面取出1个球并放入第3个暗盒里，依次类推，第 $\mathfrak { n } - 1$ 位员工再从第n-1个暗盒里面取出1个球并放入第 $n$ 个暗盒里.第 $n$ 位员工从第 $n$ 个暗盒中取出1个球，游戏结束.若某员工取出的球为红球，则该员工获得奖金1000元，否则该员工获得奖金500元.设第 $i \left( 1 \leq i \leq n \right)$ 位员工获得奖金为 $X _ { i }$ 元

（1)求 $X _ { _ 2 } = 1 0 0 0$ 的概率；  
（2）求 $X _ { i }$ 的数学期望 $E \left( { X } _ { i } \right)$ ，并指出第几位员工获得奖金额的数学期望最大.

# 选择性必修第三册

【例3】网球运动是一项激烈且耗时的运动，对于力量的消耗是很大的，这就需要网球运动员提高自己的耐力.耐力训练分为无氧和有氧两种训练方式．某网球俱乐部的运动员在某赛事前展开了一轮为期90天的封闭集训，在封闭集训期间每名运动员每天选择一种方式进行耐力训练．由训练计划知，在封闭集训期间，若运动员第$n \left( n \in \mathbf { N } ^ { * } , n \leq 8 9 \right)$ 天进行有氧训练，则第 $n { + 1 }$ 天进行有氧训练的概率为 $\frac { 5 } { 9 }$ 第 $n { + 1 }$ 天进行无氧训练的概率为 $\frac { 4 } { 9 }$ $n$ $n { + 1 }$ $\frac { 7 } { 9 }$ 第 $n { + 1 }$ 天进行无氧训练的概率为 $\frac { 2 } { 9 }$ .若运动员封闭集训的第1天进行有氧训练与无氧训练的概率相等.

(1)封闭集训期间，记3名运动员中第2天进行有氧训练的人数为 $X$ ，求 $X$ 的分布列与数学期望；(2)封闭集训期间，记某运动员第 $n$ 天进行有氧训练的概率为 $P _ { n }$ ，求 $P _ { 4 5 }$ ：

【例4】甲乙两人进行投篮比赛，两人各投一次为一轮比赛，约定如下规则：如果在一轮比赛中一人投进，另一人没投进，则投进者得1分，没进者得-1分，如果一轮比赛中两人都投进或都没投进，则都得0分，当两人各自累计总分相差4分时比赛结束，得分高者获胜，在每次投球中甲投进的概率为0.5，乙投进的概率为0.6，每次投球都是相互独立的.

(1)若两人起始分都为0分，求恰好经过4轮比赛，甲获胜的概率.

(2)若规定两人起始分都为2分，记 $P ( i )$ （ $i = 0 , 1 , 2 , 3 , 4$ ）为甲累计总分为 $i$ 时，甲最终获胜的概率，则$P ( 0 ) = 0 , P ( 4 ) = 1$

$\textcircled{1}$ 求证 $\left\{ P ( i + 1 ) - P ( i ) \right\}$ （ $i = 0 , 1 , 2 , 3$ ）为等比数列$\textcircled{2}$ 求 $P ( 2 )$ 的值.

【例5】某学校新校区在校园里边种植了一种漂亮的植物，会开出粉红色或黄色的花．这种植物第1代开粉红色花和黄色花的概率都是 $\frac { 1 } { 2 }$ ，从第2代开始，若上一代开粉红色的花，则这一代开粉红色的花的概率是 $\frac { 3 } { 5 }$ ，开黄色花的概率是 $\frac { 2 } { 5 }$ 若上一代开黄色的花，则这一代开粉红色的花的概率为 $\frac { 1 } { 5 }$ ，开黄色花的概率为 $\frac { 4 } { 5 }$ 设第 $n$ 代开粉红色花的概率为 $P _ { n }$

(1)求第2代开黄色花的概率；(2）证明： $\sum _ { i = 1 } ^ { n } \frac { 1 - 3 P _ { i } } { 5 P _ { i } P _ { i + 1 } } < 2$

# 【跟踪训练】

1.有一个质地均匀的正方体骰子与一个有61个格子的矩形方格图，矩形方格图上从0，1，2，.，60依次标号.一个质点位于第0个方格中，现有如下游戏规则：先投掷骰子，若出现1点或2点，则质点前进1格，否则质点前进2格，每次投掷的结果互不影响.

(1)求经过两次投掷后，质点位于第4个格子的概率；  
(2)若质点移动到第59个格子或第60个格子时，游戏结束，设质点移动到第 $_ { n }$ 个格子的概率为 $p _ { n }$ ，求 $p _ { 5 9 }$ 和 $p _ { 6 0 }$ 的值.

2.重庆南山风景秀丽，可以俯瞰渝中半岛，是徒步休闲的好去处．上南山的步道很多，目前有标识的步道共有 18条．某徒步爱好者俱乐部发起一项活动，若挑战者连续12天每天完成一次徒步上南山(每天多次上山按一次计算)运动，即可获得活动大礼包．已知挑战者甲从11月1号起连续12天都徒步上南山一次，每次只在凉水井步道和清水溪步道中选一条上山，甲第一次选凉水井步道上山的概率为 $\frac { 3 } { 4 }$ ，而前一次选择了凉水井步道，后一次继续选择凉水井步道的概率为 $\frac { 1 } { 4 }$ ，前一次选择清水溪步道，后一次继续选择清水溪步道的概率为 $\frac { 1 } { 2 }$ ，如此往复．设甲第 $n ( n { = } 1$ ，2，..，12)天走凉水井步道上山的概率为 $P _ { n }$

（1）求 $P _ { 2 }$ 和 $P _ { n }$

(2)求甲在这12天中选择走凉水井步道上山的概率小于选择清水溪步道上山概率的天数.

# 选择性必修第三册

3.有 $n$ 个编号分别为 $1 , 2 , \cdots , n$ 的盒子，第1个盒子中有2个红球和1个白球，其余盒子中均为1个红球和1个白球，现从第1个盒子中任取一球放入第2个盒子，现从第2个盒子中任取一球放入第3个盒子，，依次进行.

（1）求从第2个盒子中取到红球的概率；  
(2）求从第 $_ n$ 个盒子中取到红球的概率；  
(3）设第 $n$ 个盒子中红球的个数为 $X$ ， $X$ 的期望值为 $E ( X )$ ，求证： $\frac { 3 } { 2 } < E ( X ) \leq 2$ ：

4.马尔可夫链是因俄国数学家安德烈·马尔可夫得名，其过程具备“无记忆"的性质，即第 $n { + 1 }$ 次状态的概率分布只跟第 $n$ 次的状态有关，与第 $n - 1 , n - 2 , n - 3 , \cdots$ 次状态是“没有任何关系的”现有甲、乙两个盒子，盒子中都有大小、形状、质地相同的2个红球和1个黑球.从两个盒子中各任取一个球交换，重复进行 $n \big ( n \in \mathbb { N } ^ { * } \big )$ 次操作后，记甲盒子中黑球个数为 $X _ { n }$ ，甲盒中恰有1个黑球的概率为 $a _ { n }$ ，恰有2个黑球的概率为 $b _ { n }$

（1）求 $X _ { 1 }$ 的分布列；  
（2）求数列 $\left\{ a _ { n } \right\}$ 的通项公式；  
（3）求 $X _ { n }$ 的期望.

5.足球是一项大众喜爱的运动.2022卡塔尔世界杯揭幕战将在2022年11月21日打响，决赛定于12月18日晚进行，全程为期28天.

校足球队中的甲、乙、丙、丁四名球员将进行传球训练，第1次由甲将球传出，每次传球时，传球者都等可能的将球传给另外三个人中的任何一人，如此不停地传下去，且假定每次传球都能被接到，记开始传球的人为第1次触球者，第 $n$ 次触球者是甲的概率记为 $P _ { n }$ ，即 $P _ { 1 } = 1$

（1）求 $P _ { 3 }$ （直接写出结果即可）；

（2）证明；数列 $\left\{ P _ { n } - { \frac { 1 } { 4 } } \right\}$ 为等比数列，并判断第19次与第20次触球者是甲的概率的大小。

6.为了治疗某种疾病，研制了甲、乙两种新药，希望知道哪种新药更有效，为此进行动物试验．试验方案如下：每一轮选取两只白鼠对药效进行对比试验．对于两只白鼠，随机选一只施以甲药，另一只施以乙药．一轮的治疗结果得出后，再安排下一轮试验，当其中一种药治愈的白鼠比另一种药治愈的白鼠多4只时，就停止试验，并认为治愈只数多的药更有效．为了方便描述问题，约定：对于每轮试验，若施以甲药的白鼠治愈且施以乙药的白鼠未治愈则甲药得1分，乙药得-1分；若施以乙药的白鼠治愈且施以甲药的白鼠未治愈则乙药得1分，甲药得-1分；若都治愈或都未治愈则两种药均得0分，甲、乙两种药的治愈率分别记为α和 $| \beta \rrangle$ ，一轮试验中甲药的得分记为 $X$

（1）求 $X$ 的分布列；

（2）若甲药、乙药在试验开始时都赋予4分， $p _ { i } ( i = 0 , 1 , \cdots , 8 )$ 表示“甲药的累计得分为 $i$ 时，最终认为甲药比乙药更有效"的概率，则 $p _ { 0 } = 0$ ， $p _ { 8 } = 1$ ， $p _ { i } = a p _ { i - 1 } + b p _ { i } + c p _ { i + 1 }$ $( i = 1 , 2 , \cdots , 7 )$ ，其中 $a = P ( X = - 1 )$ ，$b = P ( X = 0 ) , c = P ( X = 1 )$ 假设 $\alpha = 0 . 5$ ， $\beta = 0 . 8$ ：

（i）证明： $\{ p _ { i + 1 } - p _ { i } \} ( i = 0 , 1 , 2 , \cdots , 7 )$ 为等比数列；  
（ii）求 $p _ { 4 }$ ，并根据 $p _ { 4 }$ 的值解释这种试验方案的合理性.

# 第13讲期望方差的实际应用

# 【精选例题】

【例1】最新研发的某产品每次试验结果为成功或不成功，且每次试验的成功概率为 $p ( 0 < p < 1 )$ ．现对该产品进行独立重复试验，若试验成功，则试验结束；若试验不成功，则继续试验，且最多试验8次．记 $\chi$ 为试验结束时所进行的试验次数， $X$ 的数学期望为 $E ( X )$

(1)证明： $E \left( X \right) < { \frac { 1 } { p } } ;$

(2)某公司意向投资该产品，若 $p = 0 . 2$ ，每次试验的成本为 $a ( a > 0 )$ 元，若试验成功则获利 $8 a$ 元，则该公司应如何决策投资？请说明理由.

【例2】某市为筛查新冠病毒，需要检验核酸样本是否为阳性，现有 $k ( k \in \mathcal { N } ^ { * }$ 且 $k \geq 2 )$ 份核酸样本，可采用以下两种检验方式： $\textcircled{1}$ 逐份检验：对 $k$ 份样本逐份检验，需要检验 $k$ 次； $\textcircled{2}$ 混合检验：将 $k$ 份样本混合在一起检验，若检验结果为阴性，则 $k$ 份样本全为阴性，因而这 $k$ 份样本只需检验1次；若检验结果为阳性，为了确定其中的阳性样本，就需重新采集核酸样本后再对这 $k$ 份新样本进行逐份检验，此时检验总次数为 $k { + } 1$ 次.假设在接受检验的核酸样本中，每份样本的检验结果是相互独立的，且每份样本结果为阳性的概率是 $p ( 0 < p < 1 )$

(1)若对 $k$ 份样本采用逐份检验的方式，求恰好经过4次检验就检验出2份阳性的概率（结果用 $p$ 表示）；

（2）若 $k { = } 2 0$ ，设采用逐份检验的方式所需的检验次数为 $X ,$ 采用混合检验的方式所需的检验次数为 $Y$ ，试比较 $E ( X )$ 与 $E ( Y )$ 的大小.

【例3】2022北京冬奥会和冬残奥会吉祥物冰墩墩、雪容融亮相上海展览中心，为了庆祝吉祥物在上海的亮相，某商场举办了一场赢取吉祥物挂件的“定点投篮”活动，方案如下：

方案一：共投9次，每次投中得1分，否则得0分，累计所得分数记为Y；

方案二：共进行三轮投篮，每轮最多投三次，直到投中两球为止得3分，否则得0分，三轮累计所得分数记为 $X$ ：累计所得分数越多，所获得奖品越多，现在甲准备参加这个“定点投篮"活动，已知甲每次投篮的命中率为$p ( 0 < p < 1 )$ ，每次投篮互不影响.

(1)若 $p = \frac { 1 } { 2 }$ 甲选择方案二，求第一轮投篮结束时，甲得3分的概率；

(2)以最终累计得分的期望值为决策依据，甲在方案一，方案二之中选其一，应选择哪个方案？

【例4】某校从高三年级选拔一个班级代表学校参加“学习强国知识大赛”，经过层层选拔，甲、乙两个班级进入最后决赛，规定选手回答1道相关问题，根据最后的评判选择由哪个班级代表学校参加大赛．每个班级有5名选手，现从每个班级的5名选手中随机抽取3人回答这道问题．已知甲班的5人中只有3人可以正确回答这道题目，乙班的5人能正确回答这道题目的概率均为 $\frac { 3 } { 5 }$ 甲、乙两个班每个人对问题的回答都是相互独立的.

(1)求甲、乙两个班抽取的6人中至少有3人能正确回答这道题目的概率；  
(2)设甲班被抽取的选手中能正确回答题目的人数为 $X$ ，求随机变量 $X$ 的分布列与数学期望，并利用所学的知识分析由哪个班级代表学校参加大赛更好，

【例5】为加强进口冷链食品监管，某省于2020年底在全省建立进口冷链食品集中监管专仓制度，在口岸、目的地市或县（区、市）等进口冷链食品第一入境点，设立进口冷链食品集中监管专仓，集中开展核酸检测和预防性全面消毒工作，为了进一步确定某批进口冷冻食品是否感染病毒，在入关检疫时需要对其采样进行化验，若结果呈阳性，则有该病毒；若结果呈阴性，则没有该病毒，对于 $n \left( n \in \mathbf { N } ^ { * } \right)$ 份样本，有以下两种检验方式：一是逐份检验，则需检验 $n$ 次：二是混合检验，将 $k$ 份样本分别取样混合在一起，若检验结果为阴性，那么这 $k$ 份全为阴性，因而检验一次就够了；如果检验结果为阳性，为了明确这 $k$ 份究竟哪些为阳性，就需要对它们再次取样逐份检验，则 $k$ 份检验的次数共为 $k + 1$ 次若每份样本没有该病毒的概率为 $\sqrt { p } ( 0 < p < 1 )$ ，而且样本之间是否有该病毒是相互独立的.

(1）若 $p = \frac { 1 } { 3 }$ ，求2份样本混合的结果为阳性的概率.

(2）若 $p = \frac { 2 } { 3 }$ 取得4份样本，考虑以下两种检验方案：

方案一：采用混合检验：

方案二：平均分成两组，每组2份样本采用混合检验.

若检验次数的期望值越小，则方案越“优”，试问方案一、二哪个更"优”？请说明理由.

【例6】甲、乙两个学校进行体育比赛，比赛共设三个项目，每个项目胜方得10分，负方得0分，没有平局.三个项目比赛结束后，总得分高的学校获得冠军．已知甲学校在三个项目中获胜的概率分别为0.5，0.4，0.8，各项目的比赛结果相互独立.

(1)求甲学校获得冠军的概率；

(2）用 $X$ 表示乙学校的总得分，求 $X$ 的分布列与期望.

【例7】在某地区进行流行病学调查，随机调查了100位某种疾病患者的年龄，得到如下的样本数据的频率分布直方图：

![](images/37fa56827d7eb5ef8eca0d96f3b3c4610d6fc0fa7aa6a5fdefdb8cf027c5b57a.jpg)

(1)估计该地区这种疾病患者的平均年龄（同一组中的数据用该组区间的中点值为代表)；

(2)估计该地区一位这种疾病患者的年龄位于区间[20,70)的概率；

(3)已知该地区这种疾病的患病率为 $0 . 1 \%$ ，该地区年龄位于区间[40,50)的人口占该地区总人口的 $16 \%$ .从该地区中任选一人，若此人的年龄位于区间[40,50)，求此人患这种疾病的概率．（以样本数据中患者的年龄位于各区间的频率作为患者的年龄位于该区间的概率，精确到0.0001）.

【例8】一种微生物群体可以经过自身繁殖不断生存下来，设一个这种微生物为第0代，经过一次繁殖后为第1代，再经过一次繁殖后为第2代.，该微生物每代繁殖的个数是相互独立的且有相同的分布列，设 $X$ 表示1个微生物个体繁殖下一代的个数， $P ( X = i ) = p _ { i } ( i = 0 , 1 , 2 , 3 )$ ·

（1）已知 $p _ { 0 } = 0 . 4 , p _ { 1 } = 0 . 3 , p _ { 2 } = 0 . 2 , p _ { 3 } = 0 . 1$ ，求 $E ( X )$ ：

（2）设 $p$ 表示该种微生物经过多代繁殖后临近灭绝的概率， $p$ 是关于 $x$ 的方程： $p _ { \circ } + p _ { 1 } x + p _ { 2 } x ^ { 2 } + p _ { 3 } x ^ { 3 } = x$ 的一个最小正实根，求证：当 $E ( X ) \leq 1$ 时， $p = 1$ ，当 $E ( X ) > 1$ 时， $p < 1$

（3）根据你的理解说明（2）问结论的实际含义.

【例9】在核酸检测中，“k合1”混采核酸检测是指：先将 $k$ 个人的样本混合在一起进行1次检测，如果这 $k$ 个人都没有感染新冠病毒，则检测结果为阴性，得到每人的检测结果都为阴性，检测结束：如果这 $k$ 个人中有人感染新冠病毒，则检测结果为阳性，此时需对每人再进行1次检测,得到每人的检测结果，检测结束.现对100人进行核酸检测，假设其中只有2人感染新冠病毒，并假设每次检测结果准确.

（I）将这100人随机分成10组，每组10人，且对每组都采用“10合1"混采核酸检测.

(i)如果感染新冠病毒的2人在同一组，求检测的总次数;

(ii)已知感染新冠病毒的2人分在同一组的概率为 $\frac { 1 } { 1 1 }$ 设 $X$ 是检测的总次数，求 $X$ 的分布列与数学期望E(X).

(II）将这100人随机分成20组，每组5人，且对每组都采用"5合1"混采核酸检测.设Y是检测的总次数，试判断数学期望E(Y与(I)中E（X)的大小.(结论不要求证明)

选择性必修第三册

【例10】某企业准备投产一批特殊型号的产品，已知该种产品的总成本 $C$ 与产量 $q$ 的函数关系式为$C = \frac { { { q } ^ { 3 } } } { 3 } - 3 { { q } ^ { 2 } } + 2 0 q + 1 0 ( q > 0 )$ ，该种产品的市场前景无法确定，有三种可能出现的情况，各种情形发生的概率及产品价格 $p$ 与产量 $q$ 的函数关系式如下表所示：

<html><body><table><tr><td>市场情况</td><td>概率</td><td>价格P与产量q的函数关系式</td></tr><tr><td>好</td><td>0.4</td><td>p=164-3q</td></tr><tr><td>中</td><td>0.4</td><td>p=101-3q</td></tr><tr><td>差</td><td>0.2</td><td>p=70-3q</td></tr></table></body></html>

设 $L _ { 1 } , L _ { 2 } , L _ { 3 }$ 分别表示市场情形好、中、差时的利润，随机变量 $\xi _ { q }$ ，表示当产量为 $q$ ，而市场前景无法确定时的利润.试求：

（1）分别求利润 $L _ { 1 } , L _ { 2 } , L _ { 3 }$ 与产量 $q$ 的函数关系式；(2）当产量 $q$ 确定时，求期望 $E { \left( \xi _ { q } \right) }$ （3）试问产量 $q$ 取何值时， $E { \left( \xi _ { g } \right) }$ 取得最大值.

【例11】现有甲、乙两个项目，对甲项目每投资10万元，一年后利润是1.2万元、1.18万元、1.17万元的概率分别为 $\frac { 1 } { 6 } , \frac { 1 } { 2 } , \frac { 1 } { 3 }$ ；已知乙项目的利润与产品价格的调整有关，在每次调整中，价格下降的概率都是 $p ( 0 < p < 1 )$ ，设乙项目产品价格在一年内进行两次独立的调整.记乙项目产品价格在一年内的下降次数为 $X ,$ 对乙项目每投资10万元， $X$ 取0、1、2时，一年后相应利润是1.3万元、1.25万元、0.2万元.随机变量 $X _ { 1 } , X _ { 2 }$ 分别表示对甲、乙两项目各投资10万元一年后的利润.

（1）求 $X _ { 1 } , X _ { 2 }$ 的概率分布和均值 $E \big ( X _ { 1 } \big ) , E \big ( X _ { 2 } \big )$

(2）当 $E { \bigl ( } X _ { 1 } { \bigr ) } < E { \bigl ( } X _ { 2 } { \bigr ) }$ 时，求 $p$ 的取值范围.

【例12】一盒中装有9张各写有一个数字的卡片，其中4张卡片上的数字是1,3张卡片上的数字是2,2张卡片上的数字是3，从盒中任取3张卡片.

（1）求所取3张卡片上的数字完全相同的概率；  
(2） $X$ 表示所取3张卡片上的数字的中位数，求 $X$ 的分布列与数学期望.  
（注：若三个数 $a , b , c$ 满足 $a \leq b \leq c$ ，则称 $b$ 为这三个数的中位数）.

【例13】计划在某水库建一座至多安装3台发电机的水电站，过去50年的水文资料显示，水库年入流量X（年入流量：一年内上游来水与库区降水之和.单位：亿立方米）都在40以上.其中，不足80的年份有10年，不低于80且不超过120的年份有35年，超过120的年份有5年.将年入流量在以上三段的频率作为相应段的概率，并假设各年的年入流量相互独立.

（1）求未来4年中，至多1年的年入流量超过120的概率；

（2）水电站希望安装的发电机尽可能运行，但每年发电机最多可运行台数受年入流量 $X$ 限制，并有如下关系：

<html><body><table><tr><td>年入流量X</td><td>40&lt;X&lt;80</td><td>80≤X≤120</td><td>X&gt;120</td></tr><tr><td>发电量最多可运行台数</td><td></td><td>2</td><td>3</td></tr></table></body></html>

若某台发电机运行，则该台年利润为5000万元；若某台发电机未运行，则该台年亏损800万元，欲使水电站年总利润的均值达到最大，应安装发电机多少台？

【例14】已知2件次品和3件正品混放在一起，现需要通过检测将其区分，每次随机检测一件产品，检测后不放回，直到检测出2件次品或者检测出3件正品时检测结束.

（I）求第一次检测出的是次品且第二次检测出的是正品的概率；  
（II）已知每检测一件产品需要费用100元，设 $X$ 表示直到检测出2件次品或者检测出3件正品时所需要的检测费用（单位：元），求 $X$ 的分布列和数学期望.

# 【题型专练】

1．甲、乙两人玩如下游戏：两人分别拿出一枚硬币同时扣在桌子上(硬币的正反面自己决定，两人互不影响)，然后把手拿开，如果都是正面，则乙给甲3元，如果都是反面，则乙给甲1元，如果一正一反则甲给乙2元，如此进行下去，把频率当做概率.  
(1)若甲出正面的频率0.7，乙出正面的频率为0.5，甲、乙各出硬币一次，求甲的收益 $X$ 的分布列及数学期望；(2)这个游戏多次进行下去，乙能否通过调整自己出正面的频率，使得无论甲出正面还是反面，自己都不会输？如果能，求出乙不输时出正面的频率的范围，如果不能，说明理由.

2.葫芦岛市矿产资源丰富，拥有煤、钼、锌、铅等51种矿种，采矿业历史悠久，是葫芦岛市重要产业之一．某选矿场要对即将交付客户的一批200袋钼矿进行品位（即纯度）检验，如检验出品位不达标，则更换为达标产品，检验时；先从这批产品中抽20袋做检验，再根据检验结果决定是否对余下的所有钼矿做检验，设每袋钼矿品位不达标的概率都为 $p \left( 0 < p < 1 \right)$ ，且每袋钼矿品位是否达标相互独立.

(1)若20袋钼矿中恰有2袋不达标的概率为 $f ( p )$ ，求 $f ( p )$ 的最大值点 $p _ { \theta }$ (2)已知每袋钼矿的检验成本为10元，若品位不达标钼矿不慎出场，对于每袋不达标钼矿要赔付客户110元．现 对这批钼矿检验了20袋，结果恰有两袋品位不达标.

$\textcircled{1}$ 若剩余钼矿不再做检验，以(1)中确定的 $p _ { \theta }$ 作为 $p$ 的值.这批钼矿的检验成本与赔偿费用的和记作 $\xi$ ，求 $E \mathopen { } \mathclose \bgroup \left( \xi \aftergroup \egroup \right)$ $\textcircled{2}$ 以 $\textcircled{1}$ 中检验成本与赔偿费用和的期望值为决策依据，是否该对余下的所有钼矿进行检验？

3．现有甲、乙两个投资项目，对甲项目投资十万元，根据对市场120份样本数据的统计，甲项目年利润分布如下表：

<html><body><table><tr><td>年利润</td><td>1.2万元</td><td>1.0万元</td><td>0.9万元</td></tr><tr><td>频数</td><td>20</td><td>60</td><td>40</td></tr></table></body></html>

对乙项目投资十万元，年利润与产品质量抽查的合格次数有关，在每次抽查中，产品合格的概率均为 $\frac 1 3$ 在一年之内要进行2次独立的抽查，在这2次抽查中产品合格的次数与对应的利润如下表：

<html><body><table><tr><td>合格次数</td><td>2</td><td>1</td><td>0</td></tr><tr><td>年利润</td><td>1.3万元</td><td>1.1万元</td><td>0.6万元</td></tr></table></body></html>

记随机变量 $X$ ， $Y$ 分别表示对甲、乙两个项目各投资十万元的年利润，将甲项目年利润的频率作为对应事件的概率.

（1）求 $\mathrm { X } > \mathrm { Y }$ 的概率；  
(2)某商人打算对甲或乙项目投资十万元，判断哪个项目更具有投资价值，并说明理由.

4.某牛奶店每天以每盒3元的价格从牛奶厂购进若干盒鲜牛奶，然后以每盒5元的价格出售，如果当天卖不完，剩下的牛奶作为垃圾回收处理.

（1）若牛奶店一天购进50盒鲜牛奶，求当天的利润y（单位：元）关于当天需求量n（单位：盒， $n \in N ^ { * }$ ）的函数解析式；

（2）牛奶店老板记录了某100天鲜牛奶的日需求量（单位：盒），整理得下表：

<html><body><table><tr><td>日需求量</td><td>48</td><td>49</td><td>50</td><td>51</td><td>52</td><td>53</td><td>54</td></tr><tr><td>频数</td><td>10</td><td>20</td><td>16</td><td>16</td><td>15</td><td>13</td><td>10</td></tr></table></body></html>

以这100天记录的各需求量的频率作为各需求量发生的概率.

$\textcircled{1}$ 若牛奶店一天购进50盒鲜牛奶， $X$ 表示当天的利润（单位：元），求 $X$ 的分布列及均值；  
$\textcircled{2}$ 若牛奶店计划一天购进50盒或51盒鲜牛奶，从统计学角度分析，你认为应购进50盒还是51盒？请说明理由.

5.某高校设计了一个实验学科的考查方案：考生从6道备选题中一次性随机抽取3题，按照题目要求独立完成全部实验操作，规定至少正确完成其中2题才可提交通过，已知6道备选题中考生甲有4道题能正确完成，2道题不能完成；考生乙每题正确完成的概率都是 $\frac { 2 } { 3 }$ 且每题正确完成与否互不影响.

（1）分别写出甲、乙两位考生正确完成实验操作的题数的分布列，并计算均值；

（2）试从甲、乙两位考生正确完成实验操作的题数的均值、方差及至少正确完成2题的概率方面比较两位考生的实验操作能力.

6．北京时间2022年7月25日3时13分，问天实验舱成功对接于天和核心舱前向端口，2022年7月25日10时03分，神舟十四号航天员乘组成功开启问天实验舱舱门，顺利进入问天实验舱.8月，中国空间站第2个实验舱段——梦天实验舱已运抵文昌航天发射场，计划10月发射.中国空间站"天宫"即将正式完成在轨建造任务，成为长期有人照料的国家级太空实验室，支持开展大规模、多学科交叉的空间科学实验.为普及空间站相关知识，某部门组织了空间站模拟编程闯关活动，它是由太空发射、自定义漫游、全尺寸太阳能、空间运输等10个相互独立的程序题目组成.规则是：编写程序能够正常运行即为程序正确.每位参赛者从10个不同的题目中随机选择3个进行编程，全部结束后提交评委测试，若其中2个及以上程序正确即为闯关成功.现已知10个程序中，甲只能正确完成其中6个，乙正确完成每个程序的概率为0.6，每位选手每次编程都互不影响.

(1)求乙闯关成功的概率；  
(2)求甲编写程序正确的个数 $X$ 的分布列和期望，并判断甲和乙谁闯关成功的可能性更大.

7．今年高考数学考试中，兰老师监考第002号考室，到考室后发现考室里有很多蚊子.为了给考生营造更好的考试环境，兰老师准备将考室内的9把风扇（布局如图）全部打开.已知一个开关控制一把风扇，每个开关上均有挡位标志，但开关和风扇的对应关系是随机的.

一 1 1一 一 一 1  
- 1>- 1 Y一 一 一 1一 一  
靠墙列 中 靠窗列 一进台

(1)因为教室内靠墙一边的蚊子多，所以兰老师想将靠墙一列的3把风扇开为二挡，而靠窗一边的蚊子少，所以想将靠窗一列的3把风扇开为一挡，中间一列的3把风扇用一挡二挡均可.若兰老师将每个开关开成一挡或二挡的概率都为 $\frac { 1 } { 2 }$ ，各个开关所开挡位互不影响.求事件“靠窗和靠墙的这6把风扇中，挡位满足兰老师预期的风扇不少于4把”的概率；

(2)若兰老师从这9个开关中选择5个，并将其调成二挡，另外4个调为一挡，将靠墙这一列的3把风扇中是二 挡风的风扇把数记为 $X$ ，求 $X$ 的分布列和期望.

8.第24届冬奥会于2022年2月4日至2月20日在北京和张家口举行，组委会需要招募翻译人员做志愿者，某外语学院的一个社团中有7名同学，其中有5人能胜任法语翻译工作；5人能胜任英语翻译工作（其中有3人两项工作都能胜任），现从中选3人做翻译工作，试求：

(1)在选中的3人中恰有2人胜任法语翻译工作的概率；  
(2)在选中的3人中既能胜任法语翻译工作又能胜任英语翻译工作的人数X的分布列和数学期望.

9．某校从高三年级中选拔一个班级代表学校参加“学习强国知识大赛”，经过层层选拔，甲、乙两个班级进入最后决赛，规定回答1道相关问题做最后的评判选择由哪个班级代表学校参加大赛．每个班级4名选手，现从每个班级4名选手中随机抽取2人回答这个问题．已知这4人中，甲班级有3人可以正确回答这道题目，而乙班级4人中能正确回答这道题目的概率均为 $\frac { 3 } { 4 }$ 甲、乙两班级每个人对问题的回答都是相互独立、互不影响的.

(1)求甲、乙两个班级抽取的4人都能正确回答的概率.  
(2)设甲、乙两个班级被抽取的选手中能正确回答题目的人数分别为 $X , Y$ ，求随机变量 $X , Y$ 的期望 $E \left( \boldsymbol { X } \right) , E \left( \boldsymbol { Y } \right)$ 和方差 $D ( X )$ ， $D \left( Y \right)$ ，并由此分析由哪个班级代表学校参加大赛更好.

10．端午假期即将到来，永辉超市举办“浓情端午高考加油"有奖促销活动，凡持高考准考证考生及家长在端年节期间消费每超过600元（含600元），均可抽奖一次，抽奖箱里有10个形状、大小完全相同的小球（其中红球有3个，黑球有7个)，抽奖方案设置两种，顾客自行选择其中的一种方案.

![](images/94b2fc86b371c9c02c983985d698a7591eb1847cf8bab9fae5aac0cf82bb9211.jpg)

方案一：  
从抽奖箱中，一次性摸出3个球，其中奖规则为：若摸到3个红球，享受免单优惠；若摸出2个红球则打6折，若摸出1个红球，则打7折；若没摸出红球，则不打折.  
方案二：  
从抽奖箱中，有放回每次摸取1球，连摸3次，每摸到1次红球，立减 200元.每次摸取1球，连摸3次，每摸  
到1次

（1）若小南、小开均分别消费了600元，且均选择抽奖方案一，试求他们均享受免单优惠的概率；（2）若小杰消费恰好满1000元，试比较说明小杰选择哪一种抽奖方案更合算？

11．某公司计划购买2台机器，该种机器使用三年后即被淘汰，机器有一易损零件，在购进机器时，可以额外购买这种零件作为备件，每个200元．在机器使用期间，如果备件不足再购买，则每个500元．现需决策在购买机器时应同时购买几个易损零件，为此搜集并整理了100台这种机器在三年使用期内更换的易损零件数，得下面柱状图：

频数40200 > 891011更换的易损零件数

以这100台机器更换的易损零件数的频率代替1台机器更换的易损零件数发生的概率，记 $X$ 表示2台机器三年内共需更换的易损零件数， $n$ 表示购买2台机器的同时购买的易损零件数.

（1）求 $X$ 的分布列；  
（2）若要求 $P ( X \leq n ) \geq 0 . 5$ ，确定 $_ n$ 的最小值；  
（3）以购买易损零件所需费用的期望值为决策依据，在 $n = 1 9$ 与 $n = 2 0$ 之中选其一，应选用哪个？

# 考点题型方法总结第14讲正态分布常考考点

# 【考点分析】

# 考点一：正态分布曲线

$\textcircled{1}$ 正态曲线：函数 $\varphi _ { \mu , \ \sigma } ( x ) = \frac { 1 } { \sqrt { 2 \pi } \sigma } \mathrm { e } ^ { - \frac { ( x - \mu ) ^ { 2 } } { 2 \sigma ^ { 2 } } }$ ， $x \in ( - \infty , + \infty )$ ，其中实数 $\mu$ 和 $\sigma ( \sigma { > } 0 )$ 为参数， $\varphi _ { \mu , \ o } ( x )$ 的图象为正态分布密度曲线，简称正态曲线.

$\textcircled{2}$ 正态曲线 $\varphi _ { \mu }$ $\scriptstyle \eta _ { \mu , \ \sigma } ( x ) = { \frac { 1 } { \sqrt { 2 \pi } \sigma } } \ e ^ { - { \frac { ( x - \mu ) ^ { 2 } } { 2 \sigma ^ { 2 } } } }$ ， $x \in \mathbf { R }$ 有以下性质：

(1)曲线位于 $x$ 轴上方，与 $x$ 轴不相交；

(2)曲线是单峰的，它关于直线 $x { = } \mu$ 对称；

(3）曲线在 $x { = } \mu$ 处达到最大值 $\frac { 1 } { \sigma { \sqrt { 2 \pi } } }$

(4)曲线与 $x$ 轴之间的面积为1；

（5）当 $\sigma$ 一定时，曲线的位置由 $\mu$ 确定，曲线随着 $\mu$ 的变化而沿 $x$ 轴平移，如图 $\textcircled{1}$

（6）当 $\mu$ 一定时，曲线的形状由 $\sigma ^ { i }$ 确定， $\sigma$ 越小，曲线越“瘦高”； $\sigma$ 越大，曲线越"矮胖”，如图 $\textcircled{2}$

![](images/****************************************92e522d454da0291d19d6364.jpg)

# 考点二：正态分布

$\textcircled{1}$ 正态分布：一般地，如果对于任何实数 $a$ ， $b ( a < b )$ ，随机变量 $X$ 满足 $P ( a < X \leq b )$ 等于正态分布曲线与 $x$ 轴所围成的区域面积，则称随机变量 $X$ 服从正态分布．正态分布完全由参数 $\mu$ 和 $\sigma$ 确定，因此正态分布常记作 $N ( \mu$ ，$\sigma ^ { 2 }$ ，如果随机变量X服从正态分布，则记为 $X \sim \mathit { N } \left( \mu , \sigma ^ { 2 } \right)$ 。（此时， $X$ 不是离散型随机变量，是连续型随机变量。）

$\textcircled { 2 } X { \sim } N ( \mu , \ \sigma ^ { 2 } )$ 中 $\mu$ ， $\sigma$ 的统计意义

(1) $\mu$ 可取任意实数，表示平均水平的特征数， $E ( X ) = \mu$   
(2) $\sigma { > } 0$ 表示标准差， $D ( X ) = \sigma ^ { 2 }$ 当 $\mu$ 一定时， $\sigma$ 越小，正态曲线越“瘦高”，表示总体的分布越集中； $\sigma _ { \ast }$ 越大，曲线越“矮胖”，表示总体的分布越  
分散

考点三：正态总体在三个特殊区间内取值的概率值及 $3 \sigma$ 原则

$$
\textcircled { 1 } P ( \mu - \sigma \textless X \leq \mu + \sigma ) = 0 . 6 8 2 6 ; \quad P ( \mu - 2 \sigma \textless X \leq \mu + 2 \sigma ) = 0 . 9 5 4 4 ; \quad P ( \mu - 3 \sigma \textless X \leq \mu + 3 \sigma ) = 0 . 9 9 7 4 .
$$

$\textcircled{2}$ 由 $P ( \mu - 3 \sigma < X \le \mu + 3 \sigma ) = 0 . 9 9 7 4$ ，知正态总体几乎总取值于区间 $( \mu - 3 \sigma , \mu + 3 \sigma )$ 之内.而在此区间以外取值的概率只有0.0026，通常认为这种情况在一次试验中几乎不可能发生.

$\textcircled{3}$ 在实际应用中，通常认为服从于正态分布 $N ( \mu , \sigma ^ { 2 } )$ 的随机变量 $X$ 只取 $( \mu - 3 \sigma , \mu + 3 \sigma )$ 之间的值，并简称之为

$3 \sigma$ 原则.

# 题型一：正态曲线

# 【精选例题】

【例1】设有一正态总体，它的概率密度曲线是函数f(x)的图像，且f(x)= $f \left( x \right) = \frac { 1 } { \sqrt { 8 \pi } } \mathrm { e } ^ { - \frac { \left( x - 1 0 \right) ^ { 2 } } { 8 } } \left( x \in \mathbf { R } \right)$ ，则这个正态总体的平均数与标准差分别是（）.

A.10与8 B.10与2 C.8与10 D.2与10

【例2】甲、乙两类水果的质量（单位：kg）分别服从正态分布 $N \left( \mu _ { 1 } , \sigma _ { 1 } ^ { 2 } \right)$ ， $N \left( \mu _ { 2 } , \sigma _ { 2 } ^ { 2 } \right)$ ，其相应的分布密度曲线如图所示，则下列说法正确的是（）

（注：正态曲线的函数解析式为 $f ( x ) = { \frac { 1 } { \sqrt { 2 \pi } \cdot \sigma } } \mathrm { e } ^ { - { \frac { ( x - \mu ) ^ { 2 } } { 2 \sigma ^ { 2 } } } } ,$ $\boldsymbol { x } \in \mathbb { R }$ ）

![](images/066bd44f2b103984be8a289be5c840dbefff46e7130f479a0ffe5d9d200c37bb.jpg)

A．甲类水果的平均质量 $\mu _ { \mathrm { 1 } } = 0 . 4 \mathrm { k g }$ B.乙类水果的质量比甲类水果的质量更集中于均值左右C．甲类水果的平均质量比乙类水果的平均质量大D．乙类水果的质量服从的正态分布的参数 $\sigma _ { 2 } = 1 . 9 9$

【例3】已知三个正态密度函数φ(x)= $\varphi _ { i } \left( x \right) = \frac { 1 } { \sqrt { 2 \pi } \sigma _ { i } } \mathrm { e } ^ { - \frac { \left( x - \mu _ { i } \right) } { 2 \sigma _ { i } ^ { 2 } } }$ 2 （ $x \in R$ ， $i = 1 , 2 , 3$ ）的图像如图所示，则（）

A. $\mu _ { 1 } = \mu _ { 3 } > \mu _ { 2 }$ ， $\sigma _ { 1 } = \sigma _ { 2 } > \sigma _ { 3 }$ 正 $3 . \mu _ { 1 } < \mu _ { 2 } = \mu _ { 3 } , \sigma _ { 1 } < \sigma _ { 2 } < \sigma _ { 3 }$ C. $\mu _ { 1 } = \mu _ { 3 } > \mu _ { 2 }$ ， $\sigma _ { 1 } = \sigma _ { 2 } < \sigma _ { 3 }$ $\mathrm { D } . \quad \mu _ { 1 } < \mu _ { 2 } = \mu _ { 3 } , \sigma _ { 1 } = \sigma _ { 2 } < \sigma _ { 3 }$

![](images/f1b3a525e751c6adf371aa6eb7c06ee33d05ea5a179a658f0a5cfbbb2cd19c49.jpg)

【例4】若随机变量 $X$ 服从正态分布，记为 $X \sim N \left( \mu , \sigma ^ { 2 } \right)$ ，则关于 $X$ 的密度函数及其图象，下列说法中错误的是（）

A.当 $\mu = 0 , \sigma = 1$ 时，正态曲线关于 $y$ 轴对称 B．正态曲线一定是单峰的C．曲线的峰值为 $\frac { 1 } { \sqrt { 2 \pi } }$ D．当x无限增大时，曲线无限接近 $x$ 轴

# 选择性必修第三册

# 【题型专练】

1．李明上学有时坐公交车，有时骑自行车，他各记录了50次坐公交车和骑自行车所花的时间，经数据分析得到，假设坐公交车用时 $X$ 和骑自行车用时 $Y$ 都服从正态分布， $X \sim \mathrm { N } \left( \mu _ { 1 } , 6 ^ { 2 } \right) , Y \sim \mathrm { N } \left( \mu _ { 2 } , 2 ^ { 2 } \right)$ ： $X$ 和 $Y$ 的分布密度曲线如图所示．则下列结果正确的是（）

A $D ( X ) = 6$ B. $\mu _ { 1 } > \mu _ { 2 }$   
C. $P ( X \leq 3 8 ) < P ( Y \leq 3 8 )$ ${ \mathrm { D } } . \quad P ( X \leq 3 4 ) < P ( Y \leq 3 4 )$

![](images/901971a6ff97b91c4ab6416d83506b7128dff3aff446d0b279060395690c875b.jpg)

2.(多选题)已知随机变量X的概率密度函数为(x)= $\varphi \left( x \right) = \frac { 1 } { \sqrt { 2 \pi } a } \mathrm { e } ^ { - \frac { \left( x - b \right) ^ { 2 } } { 2 a ^ { 2 } } } \left( a > 0 \mathrm { ~ , ~ } b > 0 \right)$ ，且 $\varphi ( x )$ 的极大值点为 $x = 2 a$ ，记 $f \left( k \right) = P \left( X < k \right)$ ， $g \left( k \right) = P \left( X > k + a \right)$ ，则（）

A. $X \sim N \left( b , a \right)$ B $X \sim \mathcal { N } \left( 2 a , a ^ { 2 } \right) \qquad \mathbb { C } . \quad f \left( a \right) = g \left( 2 a \right) \qquad \mathbb { D } . \quad f \left( 2 a \right) + g \left( 2 a \right) = f \left( a \right) + g \left( a \right)$

3.（多选题）关于正态密度曲线f(x)= $f ( x ) = { \frac { 1 } { \sigma { \sqrt { 2 \pi } } } } \mathrm { e } ^ { - { \frac { \left( x - \mu \right) ^ { 2 } } { 2 \sigma ^ { 2 } } } }$ 下列说法正确的是（）

A．曲线关于直线 $x = \mu$ 对称 B．曲线的峰值为 $f { \bigl ( } x { \bigr ) } = { \frac { 1 } { \sqrt { 2 \pi } } }$ C. $\sigma$ 越大，曲线越“矮胖” D．对任意 $\sigma > 0$ ，曲线与 $x$ 轴围成的面积总为1

4．设随机变量X\~N(μ,o²)，X的正态密度函数为f(x) $f ( x ) = \frac { 1 } { \sqrt { 2 \pi } } e ^ { - \frac { x ^ { 2 } } { 2 } }$ ，则 $\mu =$

# 题型二利用正态分布求概率

# 【精选例题】

【例1】某物理量的测量结果服从正态分布 $N \left( 1 0 , \sigma ^ { 2 } \right)$ ，下列结论中不正确的是（）

A. $\sigma$ 越大，该物理量在一次测量中在(9.9,10.1)的概率越大B. $\sigma$ 越小，该物理量在一次测量中大于10的概率为0.5C. $\sigma$ 越大，该物理量在一次测量中小于9.99与大于10.01的概率相等D. $\sigma$ 越小，该物理量在一次测量中落在(9.9,10.2)与落在(9.8,10.1)的概率相等

# 选择性必修第三册

【例2】随机变量 $X$ 服从正态分布 $N ( \mu , \sigma ^ { 2 } )$ ，则 $P ( \mu - 2 \sigma { \leq } X { < } \mu + \sigma ) = \mathrm { ~ ( ~ ) ~ }$ 附：

<html><body><table><tr><td>概率</td><td></td><td></td><td>P(u-o≤X&lt;μ+o）|P(u-2o≤X&lt;μ+2σ）|P(u-30≤X＜μ+3σ）</td></tr><tr><td>近似值</td><td>0.6827</td><td>0.9545</td><td>0.9973</td></tr></table></body></html>

A.0.8186 B.0.4772 C.0.84 D.0.9759

【例3】随机变量 $X$ 的概率分布密度函数 $f \left( x \right) = \frac { 1 } { \sigma \sqrt { 2 \pi } } \mathrm { e } ^ { - \frac { \left( x - 1 \right) ^ { 2 } } { 2 \sigma ^ { 2 } } } \left( x \in \mathbf { R } \right)$ ，其图象如图所示，设 $P \left( X \geq 2 \right) = p$ 则图中阴影部分的面积为（）

A. $p$ B. $2 p$ C. ${ \frac { 1 } { 2 } } - p$ D. $1 - 2 p$

【例4】已知随机变量 $X$ 服从正态分布 $N \left( 1 , \sigma ^ { 2 } \right)$ ，且 $P \left( 1 < X \leq 3 \right) = 0 . 4$ ，则$P \left( X > 3 \right) = ( )$

![](images/f36f9cb62a0d2a08eb8db960ca2531fa01996606a2784085a4046e4aa0be958d.jpg)

A.0.3 B.0.3 C.0.2 D.0.1

【例5】已知随机变量服从正态分布 $X \sim N ( 2 , \sigma ^ { 2 } )$ ，若 $P ( X \leq 1 - 2 a ) + P ( X \leq 1 + a ) = 1 .$ ，则 $a = ~ ( ~ )$

A.0 B.2 C.-1 D.-2

【例6】（多选题）设 $X$ 是随机变量，那么（）

A.若 $X \sim B \Bigg ( 4 , \frac { 1 } { 4 } \Bigg )$ 则 $E ( 2 X + 3 ) = 5$ （204 B.若 $X - N \left( 1 , \sigma ^ { 2 } \right)$ ， $P \left( X \leqslant 4 \right) = 0 . 7 9$ ，则 $P \left( X { \leqslant } - 2 \right) = 0 . 2 1$ C若 $X \sim \mathcal { N } \left( 3 , 2 ^ { 2 } \right)$ ， $X = 2 Y + 3$ $D \left( Y \right) = 1$ D若 $X - B { \Bigg ( } 4 { \mathrm { , } } { \frac { 1 } { 4 } } { \Bigg ) }$ $\sigma ( 2 X + 3 ) = \frac { \sqrt { 3 } } { 2 }$

【例7】（多选题）将二项分布 $X { \sim } B ( 1 0 0 , 0 . 5 )$ 近似看成一个正态分布 $X \sim N \left( \mu , \sigma ^ { 2 } \right)$ ，其中 $\mu = E { \big ( } X { \big ) }$ ， $\sigma ^ { 2 } = D \left( { \boldsymbol { X } } \right)$ 设Y= $Y = { \frac { X - \mu } { \sigma } }$ ，则 $Y – N \ ( 0 , \ 1 )$ 记 $\Phi ( a ) = P \bigl ( Y < a \bigr )$ ，已知 $\Phi \left( 0 . 1 \right) = 0 . 5 3 9 8$ ， $\Phi ( 0 . 4 ) = 0 . 6 5 5 4$ ，则（）

A. $\mu = 5 0$ ， $\sigma = 5$ B. $\Phi { \bigl ( } - a { \bigr ) } + \Phi { \bigl ( } a { \bigr ) } < 1$ C. $P \left( X \geq 5 2 \right) \approx 0 . 3 4 4 6 ~ \mathrm { D } . ~ P \left( X = 5 0 \right) \approx 0 . 0 9 5 6$

【例8】（多选题）已知随机变量 $X$ 服从二项分布 $B \left( 4 , p \right)$ ，其数学期望 $E { \big ( } X { \big ) } = 2$ ，随机变量 $Y$ 服从正态分布$N \left( p , 4 \right)$ ，且 $P \left( X = 3 \right) + P \left( Y < a \right) = 1$ ，则（）

$$
\mathrm { ~ \mathsf ~ { A } ~ . ~ } \mathsf { ~ \Gamma } _ { p } = \frac { 1 } { 4 } \mathrm { ~ \qquad ~ \mathsf ~ { B } ~ . ~ } \mathsf { ~ \Gamma } _ { p } = \frac { 1 } { 2 } \mathrm { ~ \qquad ~ \mathsf ~ { C } ~ . ~ } \mathsf { ~ P } \bigl ( Y > 1 - a \bigr ) = \frac { 1 } { 4 } \mathrm { ~ \qquad ~ \mathsf ~ { D } ~ . ~ } \mathsf { ~ P } \bigl ( Y > 1 - a \bigr ) = \frac { 3 } { 4 }
$$

【例9】已知随机变量 $X$ ， $Y$ 分别满足 $X \sim B \left( n , p \right)$ ， $Y \sim N \left( 5 , 4 \right)$ ，且均值 $E \big ( X \big ) = E \big ( Y \big )$ ，方差 $D \left( { \boldsymbol { X } } \right) = D \left( { \boldsymbol { Y } } \right)$ ，则 $p =$

# 【题型专练】

1.已知两个随机变量 $X$ ， $Y$ ，其中 $X \sim B \Bigg ( 4 , \frac { 1 } { 4 } \Bigg )$ ， $Y \sim N \left( \mu , \sigma ^ { 2 } \right) ( \sigma > 0 )$ 若 $E \big ( X \big ) = E \big ( Y \big )$ ，且 $P \left( { \big | } Y { \big | } < 1 \right) = 0 . 4$ ，则 $P \left( Y > 3 \right) = ( )$

A.0.4 B.0.3 C.0.2 D.0.1

2.已知随机变量 $X$ 服从正态分布 $N ( 2 , \sigma ^ { 2 } ) \ : \ : \left( \sigma > 0 \right)$ ，且 $P ( X < 0 ) = 0 . 1$ ，则 $P ( 2 < X < 4 ) = ($ ）

A.0.1 B.0.2 C.0.3 D.0.4

3．下列说法正确的是（）

A.“A与 $B$ 是互斥事件”是“A与 $B$ 互为对立事件"的充分不必要条件  
B.随机变量 $X \sim B \left( n , p \right)$ ，若 $E \left( X \right) = 3 0$ ， $D \left( X \right) = 1 0$ ，则 $p = \frac { 4 } { 3 }$   
C．随机变量 $X$ 服从正态分布 $N \big ( 4 , 1 \big )$ ，且 $P \left( X \geq 5 \right) = 0 . 1 5 8 7$ ，则 $P { \big ( } 3 < X < 5 { \big ) } = 0 . 8 4 1 3$   
D．随机变量 $X$ 服从正态分布 ${ \cal N } \left( 3 , 4 \right)$ ，且满足 $X + 2 Y = 3$ ，则随机变量 $Y$ 服从正态分布 $N \big ( 0 , 1 \big )$

4．随机变量 $X \sim N \left( \mu , \sigma ^ { 2 } \right)$ ，已知其概率分布密度函数 $f ( x ) = { \frac { 1 } { \sigma { \sqrt { 2 \pi } } } } \mathrm { e } ^ { \frac { ( x - \mu ) ^ { 2 } } { 2 \sigma ^ { 2 } } }$ $x = { \sqrt { 2 } }$ 处取得最大值为 ，则$P ( X > 0 ) = \left( \begin{array} { l l l } { } & { } \end{array} \right)$

A.0.6827 B.0.84135 C.0.97725 D.0.9545

5.已知随机变量 $X$ 服从正态分布 $N ( 6 , \sigma )$ ，若 $P \left( X < 4 \right) + 5 P \left( X > 8 \right) = 1$ ，则 $P \left( 4 < X < 6 \right) =$ （）

A. 1-6 B.4 C. 1-3 $\mathrm { D . } \ \frac { 1 } { 9 }$

6.（多选题）某产品的质量指标值服从正态分布 $\left( 5 0 , \sigma ^ { 2 } \right)$ ，则下列结论正确的是（）

A. $\sigma$ 越大，则产品的质量指标值落在(49.9,50.1)内的概率越大 B．该产品的质量指标值大于50的概率为0.5 C．该产品的质量指标值大于50.01的概率与小于49.99的概率相等 D．该产品的质量指标值落在(49.9,50.2)内的概率与落在(50,50.3)内的概率相等

7.（多选题）下列说法正确的有（）.

A．从10名男生，5名女生中选取4人，则其中至少有一名女生的概率为 $\frac { 4 } { 1 3 }$ B．若随机变量 $X \sim B \Bigg ( 1 0 , \frac { 1 } { 3 } \Bigg )$ 则方差 $D \left( 3 X + 2 \right) = 2 0$ C．若随机变量 $X \sim N ( 1 , \sigma ^ { 2 } )$ ， $P \left( X < 4 \right) = 0 . 7 9$ ，则 $P \left( X \leqslant - 2 \right) = 0 . 2 1$ D.已如随机变量 $X$ 的分布列为 $P \left( X = i \right) = \frac a { i \left( i + 1 \right) } \left( i = 1 , 2 , 3 \right)$ 则 $P \left( X = 2 \right) = \frac { 2 } { 9 }$

# 题型三：正态分布的实际应用

【例1】某地组织普通高中数学竞赛.初赛共有20000名学生参赛，统计得考试成绩 $X$ （满分150分）服从正态分布 $N \big ( 1 1 0 , 1 0 0 \big )$ .考试成绩140分及以上者可以进入决赛.本次考试可以进入决赛的人数大约为（）附：$P ( \mu - \sigma < X < \mu + \sigma ) = 0 . 6 8 2 6 , P ( \mu - 2 \sigma < X < \mu + 2 \sigma ) = 0 . 9 5 4 4 , P ( \mu - 3 \sigma < X < \mu + 3 \sigma ) = 0 . 9 9 7 4 .$

A.26 B.52 C.456 D.13【例2】已知在体能测试中，某校学生的成绩服从正态分布 $N ( 7 0 , 2 5 )$ ，其中60分为及格线，则下列结论中正确的是（）

附：随机变量5服从正态分布 $N \left( \mu , \sigma ^ { 2 } \right)$ ，则 $P \left( \mu - 2 \sigma < \xi < \mu + 2 \sigma \right) = 0 . 9 5 4 5$

A．该校学生成绩的均值为25 B．该校学生成绩的标准差为 $\sqrt { 7 0 }$ C．该校学生成绩的标准差为70 D．该校学生成绩及格率超过 $9 5 \%$ 【例3】某班一次数学考试（满分150分）的成绩 $X$ 服从正态分布 $N \left( \mu , \sigma ^ { 2 } \right)$ ，若 $P \left( X \leq 8 5 \right) = P ( X \geq 1 0 5 )$ ，则估计该班这次数学考试的平均分为（）

A.85 B.90 C.95 D.105【例4】小明上学有时做公交车，有时骑自行车，他记录多次数据，分析得到：坐公交车平均用时 $3 0 \mathrm { { m i n } }$ ，样本方差为36；骑自行车平均用时 $3 4 \mathrm { { m i n } }$ ，样本方差为4，假设做公交车用时 $X \sim \mathit { N } ( 3 0 , 6 ^ { 2 } )$ ，骑自行车用时 $Y \sim \mathit { N } ( 3 4 , 2 ^ { 2 } )$ ，则（）

A. $P ( X \leq 3 8 ) > P ( Y \leq 3 8 )$ ${ \mathrm { B } } . \quad P ( X \leq 3 4 ) > P ( Y \leq 3 4 )$ C．如果有38分钟可用，小明应选择坐公交车D．如果有34分钟可用，小明应选择自行车【例5】（多选题）某校对高一学生进行了一次物理测试，得到学生的物理成绩 $X \sim \mathit { N } \left( 7 0 , 1 0 0 \right)$ ，其中60分及以上为及格，90分及以上为优秀．则下列说法正确的是（）

参考数据：随机变量 $\xi \sim \mathcal { N } \left( \mu , \sigma ^ { 2 } \right)$ ，则 $\begin{array} { r } { P \big ( \mu - \sigma < \xi < \mu + \sigma \big ) = 0 . 6 8 2 6 , P \big ( \mu - 2 \sigma < \xi < \mu + 2 \sigma \big ) = 0 . 9 5 4 4 , } \end{array}$ $P \left( \mu - 3 \sigma < \xi < \mu + 3 \sigma \right) = 0 . 9 9 7 4 .$

A．该校高一学生物理成绩的方差为10 B．该校高一学生物理成绩的期望为70C．该校高一学生物理成绩的及格率不到 $8 5 \%$ D．该校高一学生物理成绩的优秀率超过 $5 \%$ 【例6】（多选题）在网课期间，为了掌握学生们的学习状态，某省级示范学校对高二一段时间的教学成果进行测试.高二有 $1 ~ 0 0 0$ 名学生，某学科的期中考试成绩(百分制且卷面成绩均为整数) $Z$ 服从正态分布 $N \left( 8 2 . 5 , 5 . 4 ^ { 2 } \right)$ ，则（人数保留整数）（）

参考数据：若 $Z { \sim } N ( \mu , \ \sigma ^ { 2 } )$ ，贝 $\begin{array} { r } { \| P ( \mu { - } \sigma < Z \leq \mu + \sigma ) \approx 0 . 6 8 2 7 , P ( \mu - \Sigma < Z \leq \mu + 2 \sigma ) \approx 0 . 9 5 4 5 , } \end{array}$ $P ( \mu { - } 3 \sigma < Z \leq \mu { + } 3 \sigma ) \approx 0 . 9 9 7 \ 3 .$

A．年级平均成绩为82.5分 B．成绩在95分以上(含95分)人数和70分以下(含70分)人数相等C．成绩不超过77分的人数少于150D．超过98分的人数为1

【例7】已知某种袋装食品每袋质量 $X \sim \mathit { N } ( 5 0 0 , 1 6 )$ ，则随机抽取10000袋这种食品，袋装质量在区间(492,504] 的约 袋（质量单位：g）.（附： $X \sim N \left( \mu , \sigma ^ { 2 } \right)$ ，则 $P ( \mu - \sigma < X \leq \mu + \sigma ) = 0 . 6 8 2 7 ,$ $P ( \mu - 2 \sigma < X \leq \mu + 2 \sigma ) = 0 . 9 5 4 5 ~ , ~ P ( \mu - 3 \sigma < X ~ \mu + 3 \sigma ) = 0 . 9 9 7 3 ~ .$

【例8】为了保障某种药品的主要药理成分在国家药品监督管理局规定的值范围内，某制药厂在该药品的生产过程中，检验员在一天中按照规定每间隔2小时对该药品进行检测，每天检测4次：每次检测由检验员从该药品生产线上随机抽取20件产品进行检测，测量其主要药理成分含量（单位： $\mathfrak { m } \mathrm { g }$ ）根据生产经验，可以认为这条药品生产线正常状态下生产的产品的其主要药理成分含量服从正态分布 $N ( \mu , \sigma ^ { 2 } )$ ·

(1)假设生产状态正常，记 $X$ 表示某次抽取的20件产品中其主要药理成分含量在 $( \mu - 3 \sigma , \mu + 3 \sigma )$ 之外的药品件数，求 $X$ 的数学期望；

(2)在一天的四次检测中，如果有一次出现了主要药理成分含量在 $( \mu - 3 \sigma , \mu + 3 \sigma )$ 之外的药品，就认为这条生产线在这一天的生产过程可能出现异常情况，需对本次的生产过程进行检查；如果有两次或两次以上出现了主要药理成分含量在 $( \mu - 3 \sigma , \mu + 3 \sigma )$ 之外的药品，则需停止生产并对原材料进行检测.

$\textcircled{1}$ 下面是检验员在某次抽取的20件药品的主要药理成分含量：

<html><body><table><tr><td>10.02</td><td>9.78</td><td>10.04</td><td>9.92</td><td>10.14</td><td>10.04</td><td>9.22</td><td>10.13</td><td>9.91</td><td>9.95</td></tr><tr><td>10.09</td><td>9.96</td><td>9.88</td><td>10.01</td><td>9.98</td><td>9.95</td><td>10.05</td><td>10.05</td><td>9.96</td><td>10.12</td></tr></table></body></html>

经计算得， ${ \overline { { x } } } = { \frac { 1 } { 2 0 } } \sum _ { i = 1 } ^ { 2 0 } x _ { i } = 9 . 9 6 \ , s = { \sqrt { { \frac { 1 } { 2 0 } } \sum _ { i = 1 } ^ { 2 0 } \left( x _ { 1 } - { \overline { { x } } } \right) ^ { 2 } } } = 0 . 1 9 \ ,$ 其中 $x _ { i }$ 为抽取的第i件药品的主要药理成分含量 $( i = 1 , 2 , \cdots , 2 0 )$ ，用样本平均数 $\overline { { x } }$ 作为 $\mu$ 的估计值 $\hat { \mu }$ ，用样本标准差 $s$ 作为 $\sigma$ 的估计值 $\hat { \sigma }$ ，利用估计值判断是否需对本次的生产过程进行检查？

$\textcircled{2}$ 试确定一天中需停止生产并对原材料进行检测的概率（精确到0.001）：附：若随机变量 $Z$ 服从正态分布 $N ( \mu , \sigma ^ { 2 } )$ ，则 $P ( \mu { - } 3 \sigma < Z < \mu { + } 3 \sigma ) = 0 . 9 9 7 4 , 0 . 9 9 7 4 ^ { 1 9 } \approx 0 . 9 5 1 7$ $0 . 9 9 7 4 ^ { 2 0 } \approx 0 . 9 4 9 3 , 0 . 9 4 9 3 ^ { 2 } \approx 0 . 9 0 1 2 , 0 . 9 4 9 3 ^ { 3 } \approx 0 . 8 5 5 5 , 0 . 9 4 9 3 ^ { 4 } \approx 0 . 8 1 2 1 .$

# 选择性必修第三册

【例9】为了切实维护居民合法权益，提高居民识骗防骗能力，守好居民的“钱袋子”，某社区开展“全民反诈在行动——反诈骗知识竞赛"活动，现从参加该活动的居民中随机抽取了100名，统计出他们竞赛成绩分布如下：

<html><body><table><tr><td>成绩（分）</td><td>[40.50)</td><td>[50,60）</td><td>[60,70）</td><td>[70,80）</td><td>[80,90）</td><td>（90,100]</td></tr><tr><td>人数</td><td>2</td><td>4</td><td>22</td><td>40</td><td>28</td><td>4</td></tr></table></body></html>

(1)求抽取的100名居民竞赛成绩的平均分 $\boldsymbol { \overline { { x } } }$ 和方差 $s ^ { 2 }$ （同一组中数据用该组区间的中点值为代表）；

(2)以频率估计概率，发现该社区参赛居民竞赛成绩 $X$ 近似地服从正态分布 $N \left( \mu , \sigma ^ { 2 } \right)$ ，其中 $\mu$ 近似为样本成绩平均分x， $\sigma ^ { 2 }$ 近似为样本成绩方差 $s ^ { 2 }$ ，若 $\mu - \sigma < X \leq \mu + 2 \sigma$ ，参赛居民可获得“参赛纪念证书”；若 $X > \mu + 2 \sigma$ ，参赛居民可获得“反诈先锋证书”，  
$\textcircled{1}$ 若该社区有3000名居民参加本次竞赛活动，试估计获得"参赛纪念证书"的居民人数（结果保留整数)；$\textcircled{2}$ 试判断竞赛成绩为96分的居民能否获得“反诈先锋证书”.  
附：若 $X \sim N \left( \mu , \sigma ^ { 2 } \right)$ ，则 $P ( \mu - \sigma < X \le \mu + \sigma ) \approx 0 . 6 8 2 7$ ， $P ( \mu - 2 \sigma < X \le \mu + 2 \sigma ) \approx 0 . 9 5 4 5 ,$   
$P ( \mu - 3 \sigma < X \le \mu + 3 \sigma ) \approx 0 . 9 9 7 3$

# 【题型专练】

1．近年来中国进入一个鲜花消费的增长期，某农户利用精准扶贫政策，贷款承包了一个新型温室鲜花大棚，种植销售红玫瑰和白玫瑰．若这个大棚的红玫瑰和白玫瑰的日销量分别服从正态分布 $N \left( \mu , 3 0 ^ { 2 } \right)$ 和 $N \left( 2 8 0 , 4 0 ^ { 2 } \right)$ ，则下列选项不正确的是（）

附：若随机变量 $X$ 服从正态分布 $N \left( \mu , \sigma ^ { 2 } \right)$ ，则 $P ( \mu - \sigma < X < \mu + \sigma ) \approx 0 . 6 8 2 6$ ，

A．若红玫瑰日销售量范围在 $( \mu - 3 0 , 2 8 0 )$ 的概率是0.6826，则红玫瑰日销售量的平均数约为250  
B．红玫瑰日销售量比白玫瑰日销售量更集中  
C．白玫瑰日销售量范围在 $( 2 4 0 , + \infty )$ 的概率约为0.8413  
D．白玫瑰日销售量范围在 $( 3 2 0 , + \infty )$ 的概率约为0.3413

2.（多选题）赵先生早上9：00上班，上班通常乘坐公交加步行或乘坐地铁加步行．赵先生从家到公交站或地铁站都要步行 $5 \mathrm { { m i n } }$ ，公交车多且路程近一些，但乘坐公交路上经常拥堵，所需时间（单位：min）服从正态分布 $N \left( 3 3 , 4 ^ { 2 } \right)$ ，下车后从公交站步行到公司要 $1 2 \mathsf { m i n }$ ；乘坐地铁畅通，但路线长且乘客多，所需时间（单位：min）服从正态分布 $N \left( 4 4 , 2 ^ { 2 } \right)$ ，下地铁后从地铁站步行到公司要 $5 \mathfrak { m i n }$ ．从统计的角度，下列说法中正确的是（）

参考数据：若 $Z \sim N \left( \mu , \sigma ^ { 2 } \right)$ ，则 $P \left( \mu - \sigma < Z \leq \mu + \sigma \right) \approx 0 . 6 8 2 6$ ， $P \left( \mu - 2 \sigma < Z \leq \mu + 2 \sigma \right) \approx 0 . 9 5 4 4 ,$ $\begin{array} { r } { P \left( \mu - 3 \sigma < Z \leq \mu + 3 \sigma \right) \approx 0 . 9 9 7 4 \ . } \end{array}$

A．若8：00出门，则乘坐公交上班不会迟到B．若8：02出门，则乘坐地铁上班不迟到的可能性更大C.若8：06出门，则乘坐公交上班不迟到的可能性更大D．若8：12出门，则乘坐地铁上班几乎不可能不迟到

3.（多选题）某物理量的测量结果 $X$ 服从正态分布 $N \left( 1 0 0 , \sigma ^ { 2 } \right)$ ，则（）

A．该正态分布对应的正态密度曲线关于直线 $x = 1 0 0$ 对称B. $\sigma$ 越大，该正态分布对应的正态密度曲线越尖陡C. $\sigma$ 越小，在一次测量中， $X$ 的取值落在(99,101)内的概率越大D．在一次测量中， $X$ 的取值落在(99,102)与落在(101,104)的概率相等

4.（多选题）“杂交水稻之父”袁隆平一生致力于杂交水稻技术的研究、应用与推广，创建了超级杂交稻技术体系，为我国粮食安全、农业科学发展和世界粮食供给作出了杰出贡献.某杂交水稻种植研究所调查某地水稻的株高，得出株高 $\xi$ （单位：cm）近似服从正态分布 $N \left( 1 0 0 , 1 0 ^ { 2 } \right)$ .已知 $X \sim N \left( \mu , \sigma ^ { 2 } \right)$ 时，有 $P ( | X - \mu | \leq \sigma ) \approx 0 . 6 8 2 7$ ，$P ( | X - \mu | \leq 2 \sigma ) \approx 0 . 9 5 4 5$ ， $P ( | X - \mu | \leq 3 \sigma ) \approx 0 . 9 9 7 3$ .下列说法正确的是（）

A．该地水稻的平均株高约为 $1 0 0 \mathrm { c m }$ B．该地水稻株高的方差约为100C．该地株高超过 $1 1 0 \mathrm { c n }$ 的水稻约占 $6 8 . 2 7 \%$ D．该地株高低于 $1 3 0 \mathrm { c m }$ 的水稻约占 $9 9 . 8 7 \%$

5.（多选题）设 $X { \sim } N ( \mu _ { I }$ ， $\sigma _ { 1 } ^ { 2 }$ ）， $Y { \sim } N ( \mu _ { 2 }$ ， $\sigma _ { 2 } ^ { 2 }$ )，这两个正态分布密度曲线如图所示．下列结论中错误的是（）

![](images/13164b8212001409c282c4749eb747a9d5d8a93ce095728dc288826832e83619.jpg)

A. $P ( Y \geq \mu _ { 2 } ) { \geq } P ( Y \geq \mu _ { I } )$ B. $P ( X \leq \sigma _ { 2 } ) { \leq } P ( X { \leq } \sigma _ { I } )$ C．对任意正数 $t$ ， $P ( X \leq t ) { > } P ( Y \leq t ) \ \mathrm { D }$ ．对任意正数 $t$ ， $P ( X { > } t ) { > } P ( Y { > } t )$

6.正态分布概念是由德国数学家和天文学家Moivre在1733年首先提出的，由于德国数学家高斯率先把其应用于天文学研究，故我们把正态分布又称作高斯分布．早期的天文学家通过长期对某一天体的观测收集到大量数据，对这些数据进行分析，发现这些数据变量 $X$ 近似服从 $N \left( 9 , \sigma ^ { 2 } \right)$ .若 $P \left( X < 1 0 \right) = 0 . 9 1$ ，则 $P \left( X \leq 8 \right) =$

7．某地有6000名学生参加考试，考试后数学成绩 $X$ 近似服从正态分布 $N \left( 1 1 0 , \sigma ^ { 2 } \right)$ ，若 $P \left( 9 0 \leq X \leq 1 1 0 \right) = 0 . 4 5$ ，则估计该地学生数学成绩在130分以上的人数为

8．我国是全球制造业大国，制造业增加值自2010年起连续12年位居世界第一，主要产品产量稳居世界前列，为深入推进传统制造业改造提升，全面提高传统制造业核心竞争力，某设备生产企业对现有生产设备进行技术攻坚突破.设备生产的零件的直径为 $X$ （单位：nm）.

(1)现有旧设备生产的零件共7个，其中直径大于 $1 0 \mathrm { n m }$ 的有4个.现从这7个零件中随机抽取3个.记 $\xi$ 表示取出的零件中直径大于10nm的零件的个数，求5的分布列及数学期望 $E ( \xi )$

(2)技术攻坚突破后设备生产的零件的合格率为 $\frac { 2 } { 3 }$ 每个零件是否合格相互独立.现任取6个零件进行检测，若合格的零件数 $\eta$ 超过半数，则可认为技术攻坚成功.求技术攻坚成功的概率及 $\eta$ 的方差；

(3)若技术攻坚后新设备生产的零件直径 $X : ( 9 , 0 . 0 4 )$ ，从生产的零件中随机取出10个，求至少有一个零件直径大于 $9 . 4 \mathrm { { n m } }$ 的概率.

参考数据：若 $X : ( \mu , \sigma ^ { 2 } )$ ，则 $P ( | X - \mu | \leq \sigma ) \approx 0 . 6 8 2 7 \ : \ : , P ( | X - \mu | \leq 2 \sigma ) \approx 0 . 9 5 4 5 \ : , P ( | X - \mu | \leq 3 \sigma ) \approx 0 . 9 9 7 3 \ : ,$ $0 . 9 7 7 2 5 ^ { 1 0 } \approx 0 . 7 9 4 4 \ , 0 . 9 5 4 5 ^ { 1 0 } \approx 0 . 6 2 7 7 \ .$

9．某车间生产一批零件，现从中随机抽取10个零件，测量其内径的数据如下（单位：cm)：

8787889295979899103104设这10个数据的平均值为 $\mu$ ，标准差为 $\sigma$ ：

（1）求 $\mu$ 与 $\sigma$ ：  
(2)假设这批零件的内径Z（单位：cm）服从正态分布 $N \left( \mu , \sigma ^ { 2 } \right)$   
$\textcircled{1}$ 从这批零件中随机抽取10个，设这10个零件中内径大于 $1 0 7 \mathrm { c m }$ 的个数为 $X$ ，求 $D \left( 2 X + 1 \right)$ ；（结果保留5位有效数字）  
$\textcircled{2}$ 若该车间又新购一台设备，安装调试后，试生产了5个零件，测量其内径分别为76，85，93，99，108（单位：cm），以原设备生产性能为标准，试问这台设备是否需要进一步调试，说明你的理由.  
参考数据：若 $X \sim N ( \mu , \sigma ^ { 2 } )$ ，则 $P ( \mu - 2 \sigma < X < \mu + 2 \sigma ) = 0 . 9 5 4 4$ ， $P ( \mu - 3 \sigma < X < \mu + 3 \sigma ) = 0 . 9 9 7 4$ ，取$0 . 9 9 7 4 ^ { 4 } = 0 . 9 9 .$

# 第15讲正态分布中的最值范围问题

# 【精选例题】

【例1】已知 $X \sim N \left( \mu , \sigma ^ { 2 } \right)$ ，则 $P \left( \mu - \sigma \leq X \leq \mu + \sigma \right) \approx 0 . 6 8 2 7 , P \left( \mu - 2 \sigma \leq X \leq \mu + 2 \sigma \right) \approx 0 . 9 5 4 5 ,$ $P \left( \mu - 3 \sigma \leq X \leq \mu + 3 \sigma \right) \approx 0 . 9 9 7 3$ .今有一批数量庞大的零件.假设这批零件的某项质量指标引单位：毫米）服从正态分布 $N \left( 5 . 4 0 , 0 . 0 5 ^ { 2 } \right)$ ,现从中随机抽取 $N$ 个，这 $N$ 个零件中恰有 $K$ 个的质量指标位于区间(5.35,5.55).若 $K = 4 5$ ，试以使得 $P \left( K = 4 5 \right)$ 最大的 $N$ 值作为 $N$ 的估计值，则 $N$ 为（）

A.45 B.53 C.54 D.90【例2】现实世界中的很多随机变量遵循正态分布.例如反复测量某一个物理量，其测量误差 $X$ 通常被认为服从正态分布.若某物理量做 $_ n$ 次测量，最后结果的误差 $X _ { n } \sim N \left( 0 , { \frac { 2 } { n } } \right)$ 则为使 $\vert X _ { n } \vert \geq \frac { 1 } { 4 }$ 的概率控制在0.0456以下，至少要测量的次数为（）

A.32 B.64 C.128 D.256【例3】某工厂生产的产品的质量指标服从正态分布 $N \left( 1 0 0 , \sigma ^ { 2 } \right)$ ．质量指标介于99至101之间的产品为良品，为使这种产品的良品率达到 $9 5 . 4 5 \%$ ，则需调整生产工艺，使得 $\sigma$ 至多为 （若 $X \sim N \left( \mu , \sigma ^ { 2 } \right)$ ，则$P ( | X - \mu | < 2 \sigma ) \approx 0 . 9 5 4 5 )$

# 【跟踪训练】

1.为了监控某种食品的生产包装过程，检验员每天从生产线上随机抽取 $k \left( k \in \mathbf { N } ^ { * } \right)$ 包食品，并测量其质量（单位：g）.根据长期的生产经验，这条生产线正常状态下每包食品质量服从正态分布 $N ( \mu , \sigma ^ { 2 } )$ .假设生产状态正常，记表示每天抽取的 $k$ 包食品中其质量在 $( \mu - 3 \sigma , \mu + 3 \sigma )$ 之外的包数，若 $\xi$ 的数学期望 $E ( \xi ) > 0 . 0 5$ ，则 $k$ 的最小值为附：若随机变量 $X$ 服从正态分布 $N \left( \mu , \sigma ^ { 2 } \right)$ ，则 $P ( \mu { - } 3 \sigma { < } X { < } \mu { + } 3 \sigma ) \approx 0 . 9 9 7 3$

2.对一个物理量做 $n$ 次测量，并以测量结果的平均值作为该物理量的最后结果，已知测量结果5服从正态分布$N { \binom { 1 0 , { \frac { 2 } { n } } } { n } } { \big ( } n \in N _ { + } { \big ) }$ ，为使测量结果 $\xi _ { n }$ 在(9.5,10.5)的概率不小于0.9545，则至少测量 次.（参考数据：若 $X \sim N \left( \mu , \sigma ^ { 2 } \right)$ ，则 $P ( \left| X - \mu \right| < 2 \sigma ) = 0 . 9 5 4 5 )$

3.小强对重力加速度做 $n$ 次实验，若以每次实验结果的平均值作为重力加速度的估值，已知估值的误差$\Delta _ { _ n } \sim N \left( 0 , { \frac { 9 } { n ^ { 2 } } } \right)$ 为使误差 $\Delta _ { n }$ 在 $\left( - 0 . 5 , 0 . 5 \right)$ 内的概率不小于0.6827，至少要实验 次．（参考数据：若$X \sim N \left( \mu , \sigma ^ { 2 } \right)$ ，则 $P \left( \mu - \sigma \leq X \leq \mu + \sigma \right) = 0 . 6 8 2 7 \ )$

1．我省高考总成绩由语文、数学、外语三门统考科目和思想政治、历史、地理、物理、化学、生物六门选考科目组成，将每门选考科目的考生原始成绩从高到低划分为 $A$ ， $B ^ { + }$ ， $B$ ， $C ^ { + }$ ， $C$ ， $D ^ { + }$ ， $D$ ， $E$ 共8个等级，参照正态分布原则，确定各等级人数所占比例分别为 $3 \%$ ， $7 \%$ ， $16 \%$ ， $24 \%$ ， $24 \%$ ， $16 \%$ ， $7 \%$ ， $3 \%$ ，选考科目成绩计入考生总成绩时，将 $A$ 至 $E$ 等级内的考生原始成绩，依照等比例转换法则，分别转换到[91,100]，[81,90],[71,80]，[61,70]，[51,60]，[41,50]，[31,40]，[21,30]八个分数区间，得到考生的等级成绩，如果某次高考模拟考试物理科目的原始成绩 $X \sim \mathit { N } ( 5 0 , 2 5 6 )$ ，那么 $D$ 等级的原始分最高大约为（）

附： $\textcircled{1}$ 點若樓 $X \sim N ( \mu , \sigma ^ { 2 } )$ ， $Y = { \frac { X - \mu } { \sigma } }$ 则 $Y \sim N ( 0 , 1 )$ $\textcircled{2}$ 当 $Y \sim N ( 0 , 1 )$ 时， $P ( Y \leq 1 . 5 ) \approx 0 . 9$ ：

A.23 B.29 C.26 D.43

2.某蓝莓基地种植蓝莓，按1个蓝莓果重量 $Z$ 克）分为4级： $Z > 2 0$ 的为 $A$ 级， $1 8 < Z \leq 2 0$ 的为 $B$ 级， $1 6 < Z \le 1 8$ 的为 $C$ 级， $1 4 < Z \leq 1 6$ 的为 $D$ 级， $Z \le 1 4$ 的为废果．将 $A$ 级与 $B$ 级果称为优等果．已知蓝莓果重量 $Z$ 可近似服从正态分布 $N ( 1 5 , 9 )$ ，对该蓝莓基地的蓝莓进行随机抽查，每次抽出1个蓝莓果、记每次抽到优等果的概率为 $P$ （精确到0.1）．若为优等果，则抽查终止，否则继续抽查直到抽出优等果，但抽查次数最多不超过 $n$ 次，若抽查次数 $\chi$ 的期望值不超过3， $n$ 的最大值为（）附：

$$
P ( \mu - \sigma < Z \leq \mu + \sigma ) = 0 . 6 8 2 7 , P ( \mu - 2 \sigma < Z \leq \mu + 2 \sigma ) = 0 . 9 5 4 5 , P ( \mu - 3 \sigma < Z \leq \mu + 3 \sigma ) = 0 . 9 7 7 3
$$

A.4 B.5 C.6 D.7

3．甲、乙、丙三人相互做传球训练，第1次由甲将球传出，每次传球时，传球者都等可能地将球传给另外两个人中的任何一人，则6次传球后球在甲手中的概率为（）

A. Ⅱ-3 B. 316 C. 5-6 D.

4．现随机安排甲、乙等4位同学参加校运会跳高、跳远、投铅球比赛，要求每位同学参加一项比赛，每项比赛至少一位同学参加，事件 $A =$ “甲参加跳高比赛”，事件 $B =$ “乙参加跳高比赛”，事件C=“乙参加跳远比赛”，则（）

A.事件 $A$ 与 $B$ 相互独立 B.事件 $A$ 与 $C$ 为互斥事件C $P \left( C { \mid } A \right) = \frac { 5 } { 1 2 }$ I $\quad ) . \quad P \left( B { \big | } A \right) = { \frac { 1 } { 9 } }$

5.某人在 $n$ 次射击中击中目标的次数为 $X$ ， $X \sim B \left( n , p \right)$ ，其中 $n \in \mathrm { N } ^ { * } , 0 < p < 1$ ，击中奇数次为事件A，则（）

A.若 $n = 1 0$ $p = 0 . 8$ ，则 $P \left( X = k \right)$ 取最大值时 $k = 9$ B.当 $p = \frac { 1 } { 2 }$ 时， $D ( X )$ 取得最小值C.当 $0 < p < \frac { 1 } { 2 }$ 时， $P ( A )$ 随着 $n$ 的增大而增大 D.当 $\frac { 1 } { 2 } < p < 1$ 时， $P ( A )$ 随着 $n$ 的增大而减小

6．某教师准备对一天的五节课进行课程安排，要求语文、数学、外语、物理、化学每科分别要排一节课，则数学不排第一节，物理不排最后一节的情况下，化学排第四节的概率是（）

A. 320 B. 73 C. 33 D. $\frac { 1 7 } { 7 8 }$

7.已知 $X \sim N \left( \mu , \sigma ^ { 2 } \right)$ ，则 $\mid P \left( \mu - \sigma \leq X \leq \mu + \sigma \right) \approx 0 . 6 8 2 7 , P \left( \mu - 2 \sigma \leq X \leq \mu + 2 \sigma \right) \approx 0 . 9 5 4 5 ,$ $P \left( \mu - 3 \sigma \leq X \leq \mu + 3 \sigma \right) \approx 0 . 9 9 7 3$ .今有一批数量庞大的零件.假设这批零件的某项质量指标引单位：毫米）服从正态分布 $N \left( 5 . 4 0 , 0 . 0 5 ^ { 2 } \right)$ ,现从中随机抽取 $N$ 个，这 $N$ 个零件中恰有 $K$ 个的质量指标位于区间(5.35,5.55).若 $K = 4 5$ ，试以使得 $P \left( K = 4 5 \right)$ 最大的 $N$ 值作为 $N$ 的估计值，则 $N$ 为（）

A.45 B.53 C.54 D.90

8．现实世界中的很多随机变量遵循正态分布.例如反复测量某一个物理量，其测量误差 $\chi$ 通常被认为服从正态分布.若某物理量做 $_ n$ 次测量，最后结果的误差 $X _ { n } \sim N \left( 0 , { \frac { 2 } { n } } \right)$ 则为使 $\vert X _ { n } \vert \geq { \frac { 1 } { 4 } }$ 的概率控制在0.0456以下，至少要测量的次数为（）

A.32 B.64 C.128 D.256

9．信息熵是信息论中的一个重要概念．设随机变量 $X$ 所有可能的取值为1，2，..， $_ n$ ，且  
$P ( X = i ) = p _ { \iota } > 0 ( i = 1 , 2 , \cdots , n ) , \sum _ { i = 1 } ^ { n } p _ { i } = 1$ ，定义 $X$ 的信息熵 $H ( X ) = - \sum _ { i = 1 } ^ { n } ( p _ { i } \log _ { 2 } p _ { i } )$ ，  
命题1：若 $p _ { i } = \frac { 1 } { n } ( i = 1 , 2 , \cdots , n )$ ，则 $H ( X )$ 随着 $n$ 的增大而增大；  
命题2：若 $n = 2 m$ ，随机变量Y所有可能的取值为 $1 , 2 , \ldots , m$ ，且 $P ( Y = j ) = p _ { j } + p _ { \scriptscriptstyle { 2 m + 1 - j } } ( j = 1 , 2 , \cdots ; m )$ ，则 $H ( X ) \leq H ( Y )$ 则以下结论正确的是（）

A．命题1正确，命题2错误 B.命题1错误，命题2正确C．两个命题都错误 D.两个命题都正确

10．下图是一块高尔顿板示意图：在一块木块上钉着若干排互相平行但相互错开的圆柱形小木钉，小木钉之间留有适当的空隙作为通道，前面挡有一块玻璃，将小球从顶端放入，小球在下落过程中，每次碰到小木钉后都等可能地向左或向右落下，最后落入底部的格子中，格子从左到右分别编号为1，2，3，，6，用 $\chi$ 表示小球落入格子的号码，则（）

A.P(X=1)=P(x=6)=4 B，B(x） C. $D \left( X \right) = \frac { 3 } { 2 }$ D.D(x)=

![](images/2731c430080bd30367b8fb7389360e05aa62472b270ae39bf96e3af1730729d0.jpg)

11.2021年高考结束后小明与小华两位同学计划去老年公寓参加志愿者活动．小明在如图的街道 $E$ 处，小华在如图的街道 $F$ 处，老年公寓位于如图的 $G$ 处，则下列说法正确的个数是（）

$\textcircled{1}$ 小华到老年公寓选择的最短路径条数为4条$\textcircled{2}$ 小明到老年公寓选择的最短路径条数为35条$\textcircled{3}$ 小明到老年公寓在选择的最短路径中，与到 $F$ 处和小华会合一起到老年公寓的概率为 $\frac { 1 8 } { 3 5 }$ $\textcircled{4}$ 小明与小华到老年公寓在选择的最短路径中，两人

![](images/7d2a19dcfa0c633417dbc372876aff35bf01add52f3253798ed54526175a1c1d.jpg)

并约定在老年公寓门口汇合，事件 $A$ ：小明经过 $F$ 事件 $B$ ；从 $F$ 到老年公寓两人的路径没有重叠部分（路口除外），则 $P ( B { \big | } A ) = { \frac { 2 } { 1 5 } }$

A.1个 B.2个 C.3个 D.4个

12．甲罐中有5个红球，2个白球和3个黑球，乙罐中有4个红球，3个白球和3个黑球(球除颜色外，大小质地均相同)．先从甲罐中随机取出一球放入乙罐，分别以 $A _ { 1 } , A _ { 2 }$ 和 $A _ { 3 }$ 表示由甲罐中取出的球是红球，白球和黑球的事件；再从乙罐中随机取出一球，以B表示由乙罐中取出的球是红球的事件，下列结论正确的个数是（）

D事件A与4相互独立；②A，4，A是两两互斥的事件；③P(B4)=：④P(B)= ⑤P(A|B)=4

A.5 B.4 C.3 D.2

# 选择性必修第三册

# 二、多选题

13.已知 $\Omega$ 为随机试验的样本空间，事件 $A$ ， $\boldsymbol { B }$ 满足 $A \subseteq \Omega , B \subseteq \Omega$ ，则下列说法正确的是（）

A、荷 $A \subseteq B$ $P \left( A \right) = \frac { 1 } { 3 } , P \left( B \right) = \frac { 1 } { 2 }$ 则 $P \left( A + B \right) = { \frac { 5 } { 6 } }$ B.若 $A \cap B = \emptyset$ ，且 $P \left( A \right) = \frac { 1 } { 3 } , P \left( B \right) = \frac { 1 } { 2 }$ ，则 $P \left( A + B \right) = { \frac { 5 } { 6 } }$ c.若 $P \left( A \right) = P \left( A \middle | B \right) = \frac { 1 } { 3 } , P \left( B \right) = \frac { 1 } { 2 }$ 则 $P \left( { \overline { { B } } } { \sqrt { A } } \right) = { \frac { 1 } { 4 } }$ D.着 $P \left( A \right) = \frac { 1 } { 2 } , P \left( \overline { { A } } \left| B \right. \right) = \frac { 3 } { 4 } , P \left( \overline { { A } } \left| \overline { { B } } \right. \right) = \frac { 3 } { 8 }$ 则 $P \left( { \overline { { B } } } \right) = { \frac { 2 } { 3 } }$

14.设 $\boldsymbol { A }$ ， $B$ 是一个随机试验中的两个事件，且 $P \left( A \right) = \frac { 1 } { 2 }$ ， $P \left( B \right) = \frac { 2 } { 3 }$ ， $P \left( A + { \overline { { B } } } \right) = { \frac { 3 } { 4 } }$ ，则（）

$$
{ \mathrm { ~  ~ { ~ \cal ~ A ~ . ~ } ~ } } \ { \cal P } ( { \mathit { \mathcal { A } } } { \mathit { \mathcal { B } } } ) = \frac { 1 } { 3 } { \mathrm { ~  ~ { ~ \cal ~ B . ~ } ~ } } \ { \cal P } ( \overline { { { \mathcal { A } } } } \overline { { { \mathit { B } } } } ) = \frac { 1 } { 4 } { \mathrm { ~  ~ { ~ \cal ~ C . ~ } ~ } } \ { \cal P } ( \overline { { { \mathcal { B } } } } | { \mathit { \mathcal { A } } } ) = \frac { 1 } { 6 } { \mathrm { ~  ~ { ~ \cal ~ D . ~ } ~ } } \ { \cal P } ( \overline { { { \mathcal { A } } } } | { \mathit { \mathcal { B } } } ) = \frac { 1 } { 2 }
$$

15．一个不透明的袋子中装有大小形状完全相同的红、黄、蓝三种颜色的小球各一个，每次从袋子中随机摸出一个小球，记录颜色后放回，当三种颜色的小球均被摸出过时就停止摸球.设 $A _ { i } =$ “第 $i$ 次摸到红球”， $B _ { i } =$ “第 $i$ 次摸到黄球”， $C _ { i } = \cdot$ 第 $i$ 次摸到蓝球”， $D _ { i } =$ “摸完第 $i$ 次球后就停止摸球”，则（）

A $P \left( D _ { 3 } \right) = \frac { 2 } { 9 }$ B.P(D14)=2  
C $P \left( D _ { n } \right) = { \frac { 2 ^ { n - 1 } - 2 } { 3 ^ { n - 1 } } }$ ， $n \geq 3$ $\mathrm { D } . \quad P \bigl ( D _ { n } \bigr | B _ { n - 1 } C _ { n - 2 } \bigr ) = \frac { 2 ^ { n - 3 } } { 3 ^ { n - 2 } } , \quad$ 2n≥3

16．有一座高度是10级（第1级\~第10级）台阶的楼梯，小明在楼梯底部（第0级）从下往上走，每跨一步只能向上1级或者向上2级，且每步向上1级与向上2级的概率相同，设第 $n$ 步后小明所在台阶级数为随机变量 $X _ { \eta }$ ，则（）

A $P \left( X _ { 2 } = 2 \right) = \frac { 1 } { 4 }$ B. $E \left( { { X } _ { 2 } } \right) = 3$ C. $P \left( X _ { 4 } = 6 \right) < P \left( X _ { 4 } = 7 \right)$ D. $P \left( X _ { n } = 1 0 \right)$ 中 $P \left( X _ { 7 } = 1 0 \right)$ 最大

17.一种微生物群体可以经过自身繁殖不断生存下来，设一个这种微生物为第0代，经过一次繁殖后为第1代，再经过一次繁殖后为第2代..，该微生物每代繁殖的个数是相互独立的且有相同的分布列，设 $X$ 表示1个微生物个体繁殖下一代的个数， $P ( X = i ) = p _ { i } ( i = 0 , 1 , 2 , 3 )$ .假设 $p$ 表示该种微生物经过多代繁殖后临近灭绝的概率，且 $p$ 是关于 $x$ 的方程： $p _ { 0 } + p _ { 1 } x + p _ { 2 } x ^ { 2 } + p _ { 3 } x ^ { 3 } = x$ 的一个最小正实根，则下列说法正确的是（）

A．1是方程： $p _ { \scriptscriptstyle 0 } + p _ { \scriptscriptstyle 1 } x + p _ { \scriptscriptstyle 2 } x ^ { 2 } + p _ { \scriptscriptstyle 3 } x ^ { 3 } = x$ 的根B.当 $E ( X ) = 1$ 时， $p = 1$ C.当 $E ( X ) > 1$ 时， $p = 1$ D.当 $E ( X ) < 1$ 时， $p = 1$

18．为排查新型冠状病毒肺炎患者，需要进行核酸检测．现有两种检测方式：（1）逐份检测：（2）混合检测：将其中 $k$ 份核酸分别取样混合在一起检测，若检测结果为阴性，则这 $k$ 份核酸全为阴性，因而这 $k$ 份核酸只要检测一次就够了，如果检测结果为阳性，为了明确这 $k$ 份核酸样本究竟哪几份为阳性，就需要对这 $k$ 份核酸再逐份检测，此时，这 $k$ 份核酸的检测次数总共为 $k + 1$ 次．假设在接受检测的核酸样本中，每份样本的检测结果是阴性还是阳性都是独立的，并且每份样本是阳性的概率都为 $p \left( 0 < p < 1 \right)$ ，若 $k = 1 0$ ，运用概率统计的知识判断下列哪些 $p$ 值能使得混合检测方式优于逐份检测方式．（参考数据： $\mathrm { l g 0 . 7 9 4 \approx - 0 . l }$ ）（）

A.0.4 B.0.3 C.0.2 D.0.1

19.某蓝莓基地种植蓝莓，按1个蓝莓果重量 $Z$ （克）分为4级： $Z \ge 1 0$ 的为A级， $8 \leq Z < 1 0$ 的为 $B$ 级， $6 \leq Z < 8$ 的为 $C$ 级， $4 \leq Z < 6$ 的为 $D$ 级， $Z < 4$ 的为废果．将A级与 $B$ 级果称为优等果．已知蓝莓果重量 $Z$ 服从正态分布$N ( 5 , 9 )$ ，对该蓝莓基地的蓝莓进行随机抽查，每次抽出1个蓝莓果．记每次抽到优等果的概率为 $p$ （可精确到0.1).若为优等果，则抽查终止，否则继续抽查直到抽出优等果，但抽查次数最多不超过 $_ n$ 次，若抽查次数 $X$ 的期望值不超过3， $n$ 的最大值为

附： $P ( \mu - \sigma < Z \leq \mu + \sigma ) = 0 . 6 8 2 7$ ， $P ( \mu - 2 \sigma < Z \le \mu + 2 \sigma ) = 0 . 9 5 4 5$ ，P(μ-3σ<Z≤μ+3σ)=0.9773

20．如图，已知正方体 $A B C D - A _ { 1 } B _ { 1 } C _ { 1 } D _ { 1 }$ 顶点处有一质点 $\mathcal { Q }$ ，点 $\mathcal { Q }$ 每次会随机地沿一条棱向相邻的某个顶点移动，且向每个顶点移动的概率相同．从一个顶点沿一条棱移动到相邻顶点称为移动一次．若质点 $\boldsymbol { Q }$ 的初始位置位于点 $A$ 处，则点 $\mathcal { Q }$ 移动2次后仍在底面ABCD上的概率为_ ；点 $\mathcal { Q }$ 移动 $n$ 次后仍在底面ABCD上的概率为

![](images/d39137d90c52b68dc5f4a2e149371b6a863a65e6ebcfa0231bca8b83381cd1ae.jpg)

21.现有 $n$ （ $n > 2$ ， $n \in  { \mathbb { N } } ^ { * }$ ）个相同的袋子，里面均装有 $n$ 个除颜色外其他无区别的小球，第 $k$ （ $k { = } 1$ ，2，3，..,，n）个袋中有 $k$ 个红球， $n - k$ 个白球.现将这些袋子混合后，任选其中一个袋子，并且从中连续取出三个球（每个取后不放回），若第三次取出的球为白球的概率是 $\frac { 7 } { 1 6 }$ ， 则 $n { = }$

1．奥运吉祥物"雪容融"是根据中国传统文化中灯笼的造型创造而成，现挂有如图所示的两串灯笼，每次随机选取其中一串并摘下其最下方的一个灯笼，直至某一串灯笼被摘完为止，则右边灯笼先摘完的概率为（）.

![](images/3d511f55860f833d4c7eed3ce41dccbde25a82ed552ad4638ffa3d432d0dbf90.jpg)

A. 1-4 B. 1-8 C. 3-16 D. 5-6

2．托马斯·贝叶斯（ThomasBayes）在研究"逆向概率"的问题中得到了一个公式：P(|B)= $P \left( A _ { i } \big | B \right) = \frac { P \left( A _ { i } \right) P \left( B \big | A _ { i } \right) } { \underset { n } { \sum } P \left( A _ { j } \right) P \left( B \big | A _ { j } \right) } ,$ 这个公式被称为贝叶斯公式（贝叶斯定理），其中 $\sum _ { n } ^ { j = 1 } { P \left( A _ { j } \right) P \left( B \left| A _ { j } \right. \right) }$ 称为 $B$ 的全概率，假设甲袋中有3个白球和2个红球，乙袋中有2个白球和2个红球．现从甲袋中任取2个球放入乙袋，再从乙袋中任取2个球．已知从乙袋中取出的是2个白球，则从甲袋中取出的也是2个白球的概率为（）

A. 37 B. 9 C. 187 D. $\frac { 1 } { 2 }$ 150 75

3．泊松分布是一种描述随机现象的概率分布，在经济生活、事故预测、生物学、物理学等领域有广泛的应用，泊松分布的概率分布列为 $P \left( x = k \right) = { \frac { \lambda ^ { k } } { k ! } } \mathrm { e } ^ { - \lambda } \left( k = 0 , 1 , 2 , \cdots \right)$ ，其中e为自然对数的底数， $\lambda$ 是泊松分布的均值．当$_ n$ 很大且 $p$ 很小时，二项分布近似于泊松分布，其中 $\lambda = n p$ ．一般地，当 $n \geq 2 0$ 而 $p \leq 0 . 0 5$ 时，泊松分布可作为二项分布的近似．若随机变量 $X \sim B \left( 1 0 0 0 , 0 . 0 0 1 \right)$ ， $P ( X \geq 2 )$ 的近似值为（）

A.1- B.1-2 e C.11 4 $\mathbf { D } . \quad 1 - \frac { 1 } { \mathrm { e } ^ { 2 } }$

4.1654年，法国贵族德·梅雷骑士偶遇数学家布莱兹·帕斯卡，在闲聊时梅雷谈了最近遇到的一件事：某天在一酒吧中，肖恩和尤瑟纳尔两人进行角力比赛，约定胜者可以喝杯酒，当肖恩赢20局且尤瑟纳尔赢得40局时他们发现桌子上还剩最后一杯酒．此时酒吧老板和伙计提议两人中先胜四局的可以喝最后那杯酒，如果四局、五局、六局、七局后可以决出胜负那么分别由肖恩、尤瑟纳尔、酒吧伙计和酒吧老板付费，梅雷由于接到命令需要勤见国王，没有等到比赛结束就匆匆离开了酒馆．请利用数学知识做出合理假设，猜测最后付酒资的最有可能是（）

A．肖恩 B．尤瑟纳尔 C．酒吧伙计 D．酒吧老板

5.《易经》是中国传统文化中的精髓，下图是易经后天八卦图（含乾、坤、巽、震、坎、离、艮、兑八卦），每一卦由三根线组成（表示一根阳线，表示一根阴线），从八卦中任取两卦，记事件 $A =$ “两卦的六根线中恰有两根阳线”， $B =$ “有一卦恰有一根阳线”，则 $P \left( A | B \right) = ( )$ ，

![](images/fbd8fee363ebf13c43a0eced8695f7068843dfb9ec17eaf917a36392390950d7.jpg)

A. 1-5 B. 1-6 c.1 D.

# 二、多选题

6．下图是一块高尔顿板示意图：在一块木块上钉着若干排互相平行但相互错开的圆柱形小木钉，小木钉之间留有适当的空隙作为通道，前面挡有一块玻璃，将小球从顶端放入，小球在下落过程中，每次碰到小木钉后都等可能地向左或向右落下，最后落入底部的格子中，格子从左到右分别编号为2，3，.，7，用 $X$ 表示小球落入格子的号码，则（） A

$P \left( X = 7 \right) = \frac { 1 } { 6 4 }$ $\begin{array} { l } { { \mathrm {  ~ B . ~ } } } \\ { { \mathrm {  ~ { \cal { E } } ~ } } } \\ { { \mathrm {  ~ { \cal { D } } . ~ } { \cal { D } } \big ( { \cal { X } } \big ) = \displaystyle \frac { 5 } { 4 } } } \end{array}$ C.当 $P$ 最大时， $X = 4$ 或5

6 。b 。 。 d6 。 。 。6 。 。 。Q Q Q Q Q

7．历史上著名的“伯努利错排问题"指的是：一个人有 $n \left( n \geq 2 \right)$ 封不同的信，投入 $n$ 个对应的不同的信箱，他把每封信都投错了信箱，投错的方法数为 $D _ { n }$ .例如：2封信都投错有 $D _ { 2 } = 1$ 种方法，3封信都投错有 $D _ { 3 } = 2$ 种方法，通过推理可得 $D _ { n + 1 } = n \big ( D _ { n } + D _ { n - 1 } \big )$ ．假设每个信箱只投入一封信，则下列结论正确的是（）

A．某人投6封信，则恰有3封信投对的概率为 $\frac { 1 } { 1 8 }$   
B．某人投6封信，则6封信都投错的概率为 $\frac { 1 1 } { 3 6 }$   
C．某人依次投6封信，则前2封信全部投对的情况下恰有4封信投对的概率为 $\frac { 1 } { 4 }$   
D．某人投6封信，则至少有3封信投对的概率为 $\frac { 7 } { 9 0 }$

8．在一次以“二项分布的性质"为主题的数学探究活动中，金陵中学高二某小组的学生表现优异，发现的正确结论得到老师和同学们的一致好评．设随机变量 $X \sim B \left( n , p \right)$ ，记 $p _ { k } = \mathsf C _ { n } ^ { k } p ^ { k } \left( 1 - p \right) ^ { n - k }$ ， $k = 0$ ，1，2，...，n.在研究 $p _ { k }$ 的最大值时，该小组同学发现：若 $( n + 1 ) p$ 为正整数，则 $k = \left( n + 1 \right) p$ 时， $p _ { k } = p _ { k - 1 }$ ，此时这两项概率均为最大值；若 $( n + 1 ) p$ 为非整数，当 $k$ 取 $( n + 1 ) p$ 的整数部分，则 $p _ { k }$ 是唯一的最大值．以此为理论基础，有同学重复投掷一枚质地均匀的骰子并实时记录点数1出现的次数，当投掷到第35次时，记录到此时点数1出现5次，若继续再进行65次投掷试验，则当投掷到第100次时，点数1一共出现的次数为 的概率最大.

9.高尔顿板是英国生物统计学家高尔顿设计用来研究随机现象的模型，在一块木板上钉着若干排相互平行但相互错开的圆柱形小木块，小木块之间留有适当的空隙作为通道，前面挡有一块玻璃．让一个小球从高尔顿板上方的通道口落下，小球在下落的过程中，每次碰到小木钉后都等可能地向左或向右落下，最后落入底部的球槽内，球槽从左到右分别编号为1,2.,...,7.

![](images/02a218d5e10378fd5b390fb10bfc260952b20f1809dd7725899b8c98cc33c228.jpg)

(1)若进行一次高尔顿板试验，求这个小球掉入1号球槽的概率；(2)小明同学在研究了高尔顿板后，利用该图中的高尔顿板来到社团文化节上进行盈利性“抽奖"活动，2元可以玩一次高尔顿板游戏，小球掉入X号球槽得到的奖金为Y元，其中Y={5,X=2或6 $Y = \left\{ \begin{array} { l } { { 1 0 , X = 1 \not \equiv \not \hat { \chi } 7 } } \\ { { 5 , X = 2 \not \equiv \not \tilde { \chi } 6 } } \\ { { 0 , X = 3 \not \equiv \not \chi 4 \not \equiv \not \tilde { \chi } 5 } } \end{array} \right. .$

$\textcircled{1}$ 求 $X$ 的分布列；$\textcircled{2}$ 高尔顿板游戏火爆进行，很多同学参加了游戏，你觉得小明同学能盈利吗？

10．近日，某芯片研发团队表示已自主研发成功多维先进封装技术XDFOI，可以实现 $4 \mathrm { n m }$ 手机 SOC芯片的封装，这是中国芯片技术的又一个重大突破，对中国芯片的发展具有极为重要的意义．可以说国产 $4 \mathrm { n m }$ 先进封装技术的突破，激发了中国芯片的潜力，证明了知名院士倪光南所说的先进技术是买不来的、求不来的，自主研发才是最终的出路．研发团队准备在国内某著名大学招募人才，准备了3道测试题，答对两道就可以被录用，甲、乙两人报名参加测试，他们通过每道试题的概率均为 $p \left( 0 < p < 1 \right)$ ，且相互独立，若甲选择了全部3道试题，乙随机选择了其中2道试题，试回答下列问题．（所选的题全部答完后再判断是否被录用）

(1)求甲和乙各自被录用的概率；  
(2)设甲和乙中被录用的人数为，请判断是否存在唯一的 $p$ 值 $p _ { \theta }$ ，使得 $E { \left( \xi \right) } = 1 . 5 \ ?$ 并说明理由.

# 【考点分析】

知识点一：变量间的相关关系

1.变量之间常见的关系

<html><body><table><tr><td>函数关系</td><td>变量之间的关系可以用函数表示</td></tr><tr><td>相关关系</td><td>变量之间有一定的联系，但不能完全用函数表示</td></tr></table></body></html>

2.相关关系与函数关系的区别

$\textcircled{1}$ 函数关系中两个变量间是一种确定性关系； $\textcircled{2}$ 函数是一种因果关系，有这样的因，必有这样的果.例如，圆的半径由1增大为2，其面积必然由 $\pi$ 增大到 $4 \pi$

$\textcircled{1}$ 相关关系是一种非确定性关系.例如，吸烟与患肺癌之间的关系，两者之间虽然没有确定的函数关系，但吸烟多的人患肺癌的风险会大幅增加，两者之间即是一种非确定性的关系； $\textcircled{2}$ 相关关系不一定是因果关系，也可能是伴随关系

知识点二：散点图及正、负相关的概念

# 1.散点图

将样本中 $n$ 个数据点 $( x _ { i } , \ y _ { i } ) ( i { = } 1 , 2 , \ . . . , \ n )$ 描在平面直角坐标系中，以表示具有相关关系的两个变量的一组数据的图形叫做散点图．点 $( \overline { { x } } \ , \ \overline { { y } } )$ 叫样本点中心．其中 ${ \overline { { x } } } = { \frac { 1 } { n } } \sum _ { i = 1 } ^ { n } x _ { i }$ ， $\overline { { y } } = \frac { 1 } { n } \sum _ { i = 1 } ^ { n } y _ { i } .$

2.正相关与负相关

(1)正相关：散点图中的点散布在从左下方到右上方的区域.(2)负相关：散点图中的点散布在从左上方到右下方的区域.知识点三：相关系数

散点图只是形象地描述点的分布情况，它的“线性”是否明显只能通过观察，要想把握其特征，必须进行定量的研究.

相关系数：对于变量 $x$ 与 $y$ 随机取到 $n$ 对数据 $( x _ { 1 } , y _ { 1 } ) , ( x _ { 2 } , y _ { 2 } ) , \cdots , ( x _ { n } , y _ { n } )$ ，则样本的线性相关系数

$$
r = { \frac { \displaystyle \sum _ { i = 1 } ^ { n } ( x _ { i } - { \overline { { x } } } ) ( y _ { i } - { \overline { { y } } } ) } { \sqrt { \displaystyle \sum _ { i = 1 } ^ { n } ( x _ { i } - { \overline { { x } } } ) ^ { 2 } } \sqrt { \displaystyle \sum _ { i = 1 } ^ { n } ( y _ { i } - { \overline { { y } } } ) ^ { 2 } } } } = { \frac { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } y _ { i } - n { \overline { { x } } } { \overline { { y } } } } { \displaystyle \sqrt { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } ^ { 2 } - n { \overline { { x } } } ^ { 2 } } \sqrt { \displaystyle \sum _ { i = 1 } ^ { n } y _ { i } ^ { 2 } - n { \overline { { y } } } ^ { 2 } } } }
$$

相关系数的性质：

(1） $\left| r \right| \leq 1$ (2） $| r |$ 越接近于1，相关程度越强； $\left| r \right|$ 越接近于0，相关程度越弱；（3）通常，当 $| r | \geq 0 . 7 5$ 时，我们认为两变量具有很强的相关性。

注：若 $r > 0$ ，则 $b > 0$ ，两变量正相关；若 $r < 0$ ，则 $b < 0$ ，两变量负相关；若 $r = 0$ ，则两变量不相关. 知识点四：回归直线

(1)回归直线：如果散点图中点的分布从整体上看大致在一条直线附近，就称这两个变量之间具有 关系，这条直线叫做回归直线．回归直线过样本点中心.

(2)线性回归方程：回归直线对应的方程叫做回归直线的方程，简称回归方程.

(3)最小二乘法：  
求线性回归方程 $\scriptstyle y = b x + a$ 时，使得样本数据的点到回归直线的距离的平方和最小的方法叫做最小二乘法．其中，  
$^ { b }$ 是线性回归方程的斜率， $a$ 是线性回归方程在 $y$ 轴上的截距

A A(4）用最小二乘法求回归方程中的 $a$ ， $^ { b }$ 有下面的公式：

$$
{ \hat { b } } = { \frac { \displaystyle \sum _ { i = 1 } ^ { n } ( x _ { i } - { \overline { { x } } } ) ( y _ { i } - { \overline { { y } } } ) } { \displaystyle \sum _ { i = 1 } ^ { n } ( x _ { i } - { \overline { { x } } } ) ^ { 2 } } } = { \frac { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } y _ { i } - n { \overline { { x } } } { \overline { { y } } } } { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } ^ { 2 } - n { \overline { { x } } } ^ { 2 } } } ; { \hat { a } } = { \overline { { y } } } - { \hat { b } } { \overline { { x } } } .
$$

其中 ${ \overline { { x } } } = { \frac { 1 } { n } } \sum _ { i = 1 } ^ { n } x _ { i }$ ${ \overline { { y } } } = { \frac { 1 } { n } } \sum _ { i = 1 } ^ { n } y _ { i }$ 这样，回归方程的斜率为 $\hat { b }$ ，纵截距为 $\hat { \boldsymbol a }$ ，即回归方程为 $\scriptstyle { \hat { y } } = { \hat { b } } x + { \hat { a } }$

题型一：相关关系的判断

# 【精选例题】

【例1】下列变量之间的关系是相关关系的是（）

A．正方体的表面积与体积 B．光照时间与果树的产量C．匀速行驶车辆的行驶距离与时间D．某运动会中某代表团的足球队的比赛成绩与乒乓球队的比赛成绩

【例2】已知变量 $x$ 、 $y$ 、 $z$ 都是正数， $y$ 与 $x$ 的回归方程： $\hat { y } = \hat { b } x + 3$ ，且 $x$ 每增加1个单位， $y$ 减少2个单位，$y$ 与 $z$ 的回归方程： $\hat { y } = 2 z ^ { 2 }$ ，则（）.

A. $y$ 与 $x$ 正相关， $z$ 与 $x$ 正相关 B. $y$ 与 $_ x$ 正相关， $z$ 与 $x$ 负相关 C. $y$ 与 $x$ 负相关， $z$ 与 $x$ 正相关 D. $y$ 与 $x$ 负相关， $_ z$ 与 $x$ 负相关

【例3】下列两个变量间的关系，是相关关系的是（）

A．任意实数和它的平方 B．圆半径和圆的周长C．正多边形的边数和内角度数之和 D．天空中的云量和下雨

【例4】下面各图中，散点图与相关系数 $r$ 不符合的有（）

![](images/3e7a0a624c7f8bb6f2e641d1b6956c2f28610a689a8be011f27c4558b27e282f.jpg)

# 【题型专练】

1．下列说法错误的是（）

A．正方体的体积与棱长之间的关系是函数关系  
B．人的身高与视力之间的关系是相关关系  
C.汽车的重量与汽车每消耗1升汽油所行驶的平均路程负相关  
D．体重与学习成绩之间不具有相关关系

2．从统计学的角度看，下列关于变量间的关系说法正确的是（）

A．人体的脂肪含量与年龄之间没有相关关系  
B．汽车的重量和汽车每消耗1L汽油所行驶的平均路程负相关  
C.吸烟量与健康水平正相关  
D.气温与热饮销售好不好正相关

3．如图是近十年来全国城镇人口、乡村人口的折线图（数据来自国家统计局）.

人口数（单位：万人）  
100000  
80000  
60000  
40000  
200002010201220142016201820202022★城镇人口一乡村人口

根据该折线图，下列说法错误的是（）

A．城镇人口与年份呈现正相关 B．乡村人口与年份的相关系数 $r$ 接近1C．城镇人口逐年增长率大致相同 D．可预测乡村人口仍呈现下降趋势

4．对四组数据进行统计，获得以下散点图，关于其相关系数的比较，正确的是（）

3530220151050 5 101520253035 3530252051050 5 101520253035相关系数为 $r _ { 1 }$ 相关系数为 $r _ { 2 }$ 35302220151050 33025201510505 101520253035 5101520253035相关系数为 $r _ { 3 }$ 相关系数为 $r _ { 4 }$

A $r _ { 2 } < r _ { 4 } < 0 < r _ { 3 } < r _ { 1 }$ B. $r _ { 4 } < r _ { 2 } < 0 < r _ { 1 } < r _ { 3 } \qquad \mathrm { C . } \quad r _ { 4 } < r _ { 2 } < 0 < r _ { 3 } < r _ { 1 } \qquad \mathrm { D . } \quad r _ { 2 } < r _ { 4 } < 0 < r _ { 1 } < r _ { 3 }$

# 题型二：求回归直线的方程

【例1】下面给出了根据我国2016年一2022年水果人均占有量 $y$ （单位： $\mathrm { k g }$ ）和年份代码 $x$ 绘制的散点图和线性回归方程的残差图（2016年—2022年的年份代码 $x$ 分别为1\~7）.

我国2016年-2022年水果人均占有量的散点图

↑ 19187615143 01234567年份代码x

我国2016年-2022年水果人均占有量的残差图

![](images/a7699cbceb677b746de7ed463fc51662fff590ea9ac39b6fc3363973341be6da.jpg)

(1)根据散点图分析 $y$ 与 $x$ 之间的相关关系；

(2)根据散点图相应数据计算得 $\sum _ { i = 1 } ^ { 7 } y _ { i } = 1 0 7 4$ ， $\sum _ { i = 1 } ^ { 7 } x _ { i } y _ { i } = 4 5 1 7$ ，求 $y$ 关于 $x$ 的线性回归方程（数据精确到0.01)；  
(3)根据线性回归方程的残差图，分析线性回归方程的拟合效果.

附：回归方程 $\hat { y } = \hat { b } x + \hat { a }$ 中的斜率和截距的最小二乘法估计公式分别为 $\hat { b } = \frac { \displaystyle \sum _ { i = 1 } ^ { n } ( x _ { i } - \overline { { x } } ) ( y _ { i } - \overline { { y } } ) } { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - \overline { { x } } \right) ^ { 2 } } , \hat { a } = \overline { { y } } - \hat { b } \overline { { x } }$

【例2】某种工程车随着使用年限的增加，每年的维修费用也相应增加，根据相关资料可知该种工程车自购人使用之日起，前5年中每年的维修费用如下表所示．已知 $y$ 与 $x$ 具有线性相关关系.

<html><body><table><tr><td>年份序号x</td><td>1</td><td>2</td><td>34</td><td></td><td>5</td></tr><tr><td>维修费用（万元）</td><td></td><td></td><td></td><td>1.11.622.52.8</td><td></td></tr></table></body></html>

参考数据： $\sum _ { i = 1 } ^ { 5 } x _ { i } ^ { 2 } = 5 5$ ， $\sum _ { i = 1 } ^ { 5 } x _ { i } y _ { i } = 3 4 . 3$ ，参考公式：线性回归方程 $\hat { y } = \hat { b } x + \hat { a }$ 的斜率和截距的最小二乘法估计分别为6= $| \hat { b } = \frac { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } y _ { i } - n \overline { { x y } } } { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } ^ { 2 } - n \overline { { x } } ^ { 2 } } , \hat { a } = \overline { { y } } - l$ 6x

（1）求 $y$ 关于 $x$ 的线性回归方程；

(2)根据实际用车情况，若某辆工程车每年维修费用超过4万元时，可以申请报备更换新车，请根据回归方程预估一辆该种工程车一般使用几年后可以申请报备更换新车.

【例3】炎炎夏日，酷暑难耐！一种新型的清凉饮料十分畅销，如图是某商店7月1日至15日售卖该种饮料的累计销售量（单位：十瓶）的散点图：

![](images/10783e03b493c4d97e642dd95ff623a86e56220740aed146392ea19a46ae0560.jpg)

（参考数据： $\sum _ { i = 1 } ^ { 1 5 } y _ { i } = 9 7 0 \ , \sum _ { i = 1 } ^ { 1 5 } x _ { i } ^ { 2 } = 1 2 4 0 \ , \sum _ { i = 1 } ^ { 1 5 } x _ { i } y _ { i } = 9 9 7 9 \ )$

(1)由散点图可知，15日的数据偏差较大，请用前14组数据求出累计销售量 $y$ （单位：十瓶）关于日期 $x$ （单位：日）的经验回归方程；

(2)请用（1）中求出的经验回归方程预测该商店9月份（共30天）售卖这种饮料的累计销售量.

附：经验回归方程 $\oint = \oint x + \frac { \oint } { a }$ 中斜率和截距的最小二乘估计公式分别为： $\hat { b } = \frac { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } y _ { i } - n \overline { { x y } } } { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } ^ { 2 } - n \overline { { x } } ^ { 2 } } ,$ s=y-8x.

# 选择性必修第三册

# 【题型专练】

1．从某居民区随机抽取10个家庭，获得第 $i$ 个家庭的月收入 $x$ （单位：千元)与月储蓄yi（单位：千元）的数据资料，算得 $\sum _ { i = 1 } ^ { 1 0 } x _ { i } = 8 0 , \sum _ { i = 1 } ^ { 1 0 } y _ { i } = 2 0 , \sum _ { i = 1 } ^ { 1 0 } x _ { i } y _ { i } = 1 8 4 , \sum _ { i = 1 } ^ { 1 0 } \hat { x } ^ { \textit { \mathrm { ~ } } } _ { i } = 7 2 0 .$

（1）求家庭的月储蓄 $y$ 对月收入 $x$ 的线性回归方程 $\oint _ { \bf \lambda } = \oint _ { \bf \lambda } x + \oint _ { \bf \lambda }$ ：（2）判断变量 $x$ 与 $y$ 之间是正相关还是负相关；（3）若该居民区某家庭月收入为7千元，预测该家庭的月储蓄.

附：线性回归方程\$=Sx+\$中，b= $\hat { b } = \frac { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } y _ { i } - n \overline { { x y } } } { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } ^ { 2 } - n ( \overline { { x } } ) ^ { 2 } }$ ， $\oint _ { a } = \overline { { y } } - \oint _ { a } ^ { - }$ ，其中，为样本平均值.

2．某书店销售刚刚上市的某高二数学单元测试卷，按事先拟定的价格进行5天试销，每种单价试销1天，得到如下数据：

<html><body><table><tr><td>单价x/元</td><td>18</td><td></td><td>19202122</td><td></td><td></td></tr><tr><td>销量y/册</td><td>61</td><td>56504845</td><td></td><td></td><td></td></tr></table></body></html>

（1）求试销5天的销量的方差和 $y$ 关于 $x$ 的回归直线方程；附： $\hat { b } = \frac { \displaystyle \sum _ { i = 1 } ^ { n } ( x _ { i } - \overline { { x } } ) ( y _ { i } - \overline { { y } } ) } { \displaystyle \sum _ { i = 1 } ^ { n } ( x _ { i } - \overline { { x } } ) ^ { 2 } } = \frac { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } y _ { i } - n \overline { { x y } } } { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } ^ { 2 } - n \overline { { x ^ { 2 } } } } \hat { a ^ { = } } \hat { y ^ { - } } - \hat { b \overline { { x ^ { - } } } } .$

（2）预计以后的销售中，销量与单价服从上题中的回归直线方程，已知每册单元测试卷的成本是10元，为了获得最大利润，该单元测试卷的单价应定为多少元？

3．在某地区2008年至2014年中，每年的居民人均纯收入 $y$ （单位：千元）的数据如下表：

<html><body><table><tr><td>年 份</td><td>2008</td><td>2009</td><td>2010</td><td>2011</td><td>2012</td><td>2013</td><td>2014</td></tr><tr><td>年份代号t</td><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td><td>6</td><td>7</td></tr><tr><td>人均纯收入y</td><td>2.7</td><td>3.6</td><td>3.3</td><td>4.6</td><td>5.4</td><td>5.7</td><td>6.2</td></tr></table></body></html>

对变量 $t$ 与 $y$ 进行相关性检验，得知 $t$ 与 $y$ 之间具有线性相关关系.

（1）求 $y$ 关于 $t$ 的线性回归方程；  
（2）预测该地区2016年的居民人均纯收入.

附：回归直线的斜率和截距的最小二乘估计公式分别为：$\hat { b } = \frac { \displaystyle \sum _ { i = 1 } ^ { n } ( t _ { i } - \overline { { t } } ) ( y _ { i } - \overline { { y } } ) } { \displaystyle \sum _ { i = 1 } ^ { n } ( t _ { i } - \overline { { t } } ) ^ { 2 } } , \hat { a } = \overline { { y } } - \hat { b } \overline { { t } }$

题型三：两个变量间的相关性分析

【例1】两个具有线性相关关系的变量的一组数据 $\left( x _ { 1 } , \ y _ { 1 } \right) \left( x _ { 2 } , \ y _ { 2 } \right)$ ，…， $\left( x _ { n } , \ y _ { n } \right)$ ，下列说法错误的是（）

A．落在回归直线方程上的样本点越多，回归直线方程拟合效果越好  
B．相关系数 $\left| r \right|$ 越接近1，变量 $x$ ， $y$ 相关性越强  
C.相关指数 $R ^ { 2 }$ 越小，残差平方和越大，即模型的拟合效果越差  
D.若 $x$ 表示女大学生的身高， $y$ 表示体重，则 $R ^ { 2 } \approx 0 . 6 5$ 表示女大学生的身高解释了 $65 \%$ 的体重变化

【例2】下列命题中正确的为（）

A.相关系数 $r$ 越大，两个变量的线性相关性越强  
B.相关系数 $r$ 越小，两个变量的线性相关性越弱  
C．残差平方和越小的模型，拟合的效果越好  
D.用相关指数 $R ^ { 2 }$ 来刻画回归效果， $R ^ { 2 }$ 越小，说明模型的拟合效果越好

【例3】下列四个命题：

$\textcircled{1}$ 由样本数据得到的回归直线方程 $\hat { y } = \hat { b } x + \hat { a }$ 至少经过样本点 $( x _ { 1 } , y _ { 1 } ) , ( x _ { 2 } , y _ { 2 } ) , \cdots , ( x _ { n } , y _ { n } )$ 中的一个；  
$\textcircled{2}$ 在回归分析中，若模型一的相关指数 $R _ { 1 } ^ { 2 } = 0 . 9 8$ ，模型二的相关指数 $R _ { 2 } ^ { 2 } = 0 . 8 5$ ，则模型一的拟合效果比模型二的好；  
$\textcircled{3}$ 回归直线一定经过样本点的中心 $\left( \overline { { x } } , \overline { { y } } \right)$   
$\textcircled{4}$ 在残差图中，残差点分布的水平带状区域越窄，说明模型的拟合精度越高.  
正确命题的个数为（）

A.1 B.2 C.3 D.4【例4】小华为了研究数学名次和物理名次的相关关系，记录了本班五名同学的数学和物理的名次，如图．后来发现第四名同学数据记录有误，那么去掉数据 $D \big ( 3 , 1 0 \big )$ 后，下列说法错误的是（）

物理名次 ↑ ·E(10,12)·D(3,10)·C(4,5)B(2,4)  
A（1,3)X  
0 数学名次

A．样本线性相关系数 $r$ 变大 B．残差平方和变大C.变量 $x$ 、 $y$ 的相关程度变强 D.线性相关系数 $r$ 越趋近于1【例5】对于一组具有线性相关关系的样本数据 $\left( x _ { \iota } , y _ { \iota } \right) ( i = 1 , 2 , \cdots , n )$ ，其样本中心为 $\left( \overline { { x } } , \overline { { y } } \right)$ ，回归方程为 $\oint = \oint x + \frac { \oint } { a }$ ，则相应于样本点 $\left( x _ { i } , y _ { i } \right)$ 的残差为（）

A $y _ { i } - { \overline { { y } } }$ B. $\overline { { y } } - y _ { i }$ $\mathsf { C } . \mathsf { y } _ { i } - \left( \hat { b } x _ { i } + \hat { a } \right) \qquad \mathsf { D } . \mathsf { \ } \left( \hat { b } x _ { i } + \hat { a } \right) - y _ { i }$

# 选择性必修第三册

# 【题型专练】

1.已知 $r _ { I }$ 表示变量 $\chi$ 与 $Y$ 之间的线性相关系数， $r _ { 2 }$ 表示变量 $U$ 与 $V$ 之间的线性相关系数，且 $r _ { I } { = } 0 . 8 3 7$ ， $r _ { 2 } =$ -0.957，则（）

A.变量 $X$ 与Y之间呈正相关关系，且 $X$ 与 $Y$ 之间的相关性强于 $U$ 与 $V$ 之间的相关性 B.变量 $X$ 与 $Y$ 之间呈负相关关系，且 $X$ 与 $Y$ 之间的相关性强于 $U$ 与 $V$ 之间的相关性 C.变量 $U$ 与 $V$ 之间呈负相关关系，且 $X$ 与 $Y$ 之间的相关性弱于 $U$ 与 $V$ 之间的相关性 D.变量 $U$ 与 $V$ 之间呈正相关关系，且 $X$ 与Y之间的相关性弱于 $U$ 与 $V$ 之间的相关性

2．下列说法错误的是（）

A．线性回归直线 $\hat { y } = \hat { b } x + \hat { a }$ 一定过样本点中心 $\left( \overline { { x } } , \overline { { y } } \right)$ B．在回归分析中， $R ^ { 2 }$ 为0.91的模型比 $R ^ { 2 }$ 为0.88的模型拟合的效果好C．在残差图中，残差点分布的带状区域的宽度越狭窄，其模型拟合的精度越高D．在线性回归分析中，相关系数 $r$ 的值越大，变量间的相关性越强

3．下列说法正确的是（）

A．线性回归模型 $y = b x + a + e$ 是一次函数B．在线性回归模型 $y = b x + a + e$ 中，因变量 $y$ 是由自变量 $x$ 唯一确定的C．在残差图中，残差点比较均匀地落在水平带状区域中，说明选用的模型比较合适D.用 $R ^ { 2 } = 1 - \frac { \displaystyle \sum _ { i = 1 } ^ { n } \Bigl ( y _ { i } - \widehat { y _ { i } } \Bigr ) ^ { 2 } } { \displaystyle \sum _ { i = 1 } ^ { n } \Bigl ( y _ { i } - \overline { { y } } \Bigr ) ^ { 2 } }$ 米刻画回归方程， $R ^ { 2 }$ 越小，拟合的效果越好

4．根据一组样本数据 $\left( { { X } _ { 1 } } , { { y } _ { 1 } } \right)$ ， $\left( { { X } _ { 2 } } , { { y } _ { 2 } } \right)$ ，L， $\left( x _ { n } , y _ { n } \right)$ 的散点图分析 $x$ 与 $y$ 之间是否存在线性相关关系，求得其线性回归方程为 $\hat { y } = 0 . 8 5 x - 8 5 . 7$ ，则在样本点(165,57)处的残差为（）

A. 54.55 B.2.45 C.3.45 D.111.55

5．（多选题）有一散点图如图所示，在5个 $\scriptstyle ( x , y )$ 数据中去掉 $D \left( 3 , 1 0 \right)$ 后，下列说法中正确的是（）

![](images/d183707fcae7f86e98ae466ee013485616d3ae1ae342678a555d0f1d12e1faae.jpg)

A．残差平方和变小B．相关系数 $r$ 变小C.决定系数 $R ^ { 2 }$ 变小D．解释变量 $x$ 与响应变量 $y$ 的相关性变强

6.（多选题）对具有相关关系的两个变量 $x$ 和 $y$ 进行回归分析时，经过随机抽样获得成对的样本数据${ \left( { x _ { i } , y _ { i } } \right) } { \left( { i = 1 , 2 , \cdots , n } \right) }$ ，则下列说法正确的是（）

A．若两变量 $x$ 、 $y$ 具有线性相关关系，则回归直线至少经过一个样本点B.变量 $x$ 、 $y$ 的线性相关系数 $r$ 的绝对值越接近1，则两个变量 $y$ 与 $x$ 的线性相关程度越强C．用残差平方和来比较两个模型的拟合效果时，残差平方和越小，模型的拟合效果越好D.用R²=1- $R ^ { 2 } = 1 - \frac { \displaystyle \sum _ { i = 1 } ^ { n } \left( y _ { i } - \overline { { y } } \right) ^ { 2 } } { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - \overline { { x } } \right) ^ { 2 } }$ $R ^ { 2 }$

的值为1

7.（多选题）研究变量 $x , y$ 得到一组样本数据，进行回归分析，以下说法正确的是（）

A．残差平方和越大的模型，拟合的效果越好  
B．用决定系数 $R ^ { 2 }$ 来刻画回归效果， $R ^ { 2 }$ 越大说明拟合效果越好  
C．在经验回归方程 $\hat { y } = 0 . 2 x + 0 . 5$ 中，当解释变量 $x$ 每增加1个单位时，相应观测值y增加0.2个单位  
D.经验回归直线一定经过样本中心点 $( { \overline { { x } } } , { \overline { { y } } } )$

【例1】某地经过多年的环境治理，已将荒山改造成了绿水青山.为了估计林区某种树木的总材积量，随机选取了10棵这种树木，测量每棵树的根部横截面积和材积量，得到如下数据：

<html><body><table><tr><td>样本号i</td><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td><td>6</td><td>7</td><td>8</td><td>9</td><td>10</td><td>总和</td></tr><tr><td>根部横截面积x0.040.060.040.080.080.050.050.070.070.060.6</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>材积量y</td><td></td><td>0.250.400.220.540.510.340.360.460.420.403.9</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

并计算得到 $\sum _ { i = 1 } ^ { 1 0 } x _ { i } ^ { 2 } = 0 . 0 3 8 , \sum _ { i = 1 } ^ { 1 0 } y _ { i } ^ { 2 } = 1 . 6 1 5 8 , \sum _ { i = 1 } ^ { 1 0 } x _ { i } y _ { i } = 0 . 2 4 7 4 .$

附：相关系数 $r = { \frac { \displaystyle \sum _ { \mathrm { i = 1 } } ^ { n } ( x _ { \mathrm { i } } - { \overline { { x } } } ) ( y _ { \mathrm { i } } - { \overline { { y } } } ) } { \sqrt { \displaystyle \sum _ { \mathrm { i = 1 } } ^ { n } ( x _ { \mathrm { i } } - { \overline { { x } } } ) ^ { 2 } \sum _ { \mathrm { i = 1 } } ^ { n } ( y _ { \mathrm { i } } - { \overline { { y } } } ) ^ { 2 } } } } , ~ { \sqrt { 1 . 8 9 6 } } \approx 1 . 3 7 7 \ .$

(1)估计该林区这种树木平均一棵的根部横截面积与平均一棵的材积量；

(2)求该林区这种树木的根部横截面积与材积量的样本相关系数（精确到0.01）；(3)现测量了该林区所有这种树木的根部横截面积，并得到所有这种树木的根部横截面积总和为 $1 8 6 { \mathrm m } ^ { 2 }$ ，已知树木的材积量与其根部横截面积近似成正比.利用以上数据给出该林区这种树木的总材积量的估计值.

【例2】近年来，新能源产业蓬勃发展，已成为一大支柱产业，据统计，某市一家新能源企业近5个月的产值如下表：

<html><body><table><tr><td>月份</td><td>6月7月</td><td></td><td>8月</td><td>9月10月</td><td></td></tr><tr><td>月份代码x</td><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td></tr><tr><td>产值（亿元）</td><td>16</td><td>20</td><td>27</td><td>30</td><td>37</td></tr></table></body></html>

(1)根据上表数据，计算 $y$ 与 $x$ 间的线性相关系数 $\gamma$ ，并说明 $y$ 与 $x$ 的线性相关性的强弱；（结果保留三位小数，若 $0 . 7 5 \leq \vert r \vert \leq 1$ ，则认为 $y$ 与 $x$ 线性相关性很强；若 $\vert r \vert < 0 . 7 5$ ，则认为 $y$ 与 $x$ 线性相关性不强.）

(2)求出 $y$ 关于 $x$ 的线性回归方程，并预测明年3月份该企业的产值.

$$
r = \frac { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } y _ { i } - n \overline { { x y } } } { \sqrt { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } ^ { 2 } - n \overline { { x } } ^ { 2 } } \sqrt { \displaystyle \sum _ { i = 1 } ^ { n } y _ { i } ^ { 2 } - n \overline { { y } } ^ { 2 } } } \hat { b _ { = } } \frac { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } y _ { i } - n \overline { { x y } } } { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } ^ { 2 } - n \overline { { x } } ^ { 2 } } , \hat { a ^ { = } } y ^ { - } - b \hat { x } ^ { - } .
$$

$$
\sum _ { i = 1 } ^ { 5 } x _ { i } y _ { i } = 4 4 2 , \sum _ { i = 1 } ^ { 5 } x _ { i } ^ { 2 } = 5 5 , \sum _ { i = 1 } ^ { 5 } y _ { i } ^ { 2 } = 3 6 5 4 , \overline { { y } } = 2 6 , \sqrt { 2 7 4 0 } \approx 5 2 . 3 .
$$

# 选择性必修第三册

【例3】已知一系列样本点 $\left( x _ { 1 } , y _ { 1 } \right)$ ， $\left( { { x } _ { _ 2 } } , { { y } _ { _ 2 } } \right)$ ，...， $\left( x _ { n } , y _ { n } \right)$ ，其中 $\boldsymbol { n } \in \mathbf { N } ^ { * }$ ， $n \geq 2$ ，响应变量 $y$ 关于 $x$ 的线性回归方程为 $\hat { y } = \hat { a } + \hat { b } x$ ．对于响应变量 $y$ ，通过观测得到的数据称为观测值，通过线性回归方程得到的 $\hat { y }$ 称为预测值，观测值减去预测值，称为残差，即 $\hat { \boldsymbol e } _ { i } = y _ { i } - \hat { y } _ { i } = y _ { i } - \hat { b } x _ { i } - \hat { a } \left( i = 1 , 2 , \cdots , n \right)$ ，称为相应于点 $\left( x _ { i } , y _ { i } \right)$ 的残差.

$$
r = \frac { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - \overline { { x } } \right) \left( y _ { i } - \overline { { y } } \right) } { \sqrt { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - \overline { { x } } \right) ^ { 2 } } \sqrt { \displaystyle \sum _ { i = 1 } ^ { n } \left( y _ { i } - \overline { { y } } \right) ^ { 2 } } } , \widehat { b } = \frac { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - \overline { { x } } \right) \left( y _ { i } - \overline { { y } } \right) } { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - \overline { { x } } \right) ^ { 2 } } , \widehat { \sf z } = \overline { { y } } - \widehat { \sf z } _ { N } ^ { - } .
$$

(1)证明： $\sum _ { i = 1 } ^ { n } \hat { e } _ { i } = 0$

(2)证明： $\sum _ { i = 1 } ^ { n } { \hat { e _ { i } } } ^ { 2 } = \sum _ { i = 1 } ^ { n } \left( 1 - r ^ { 2 } \right) \left( y _ { i } - { \overline { { y } } } \right) ^ { 2 }$ ，并说明 $\left| \boldsymbol { r } \right|$ 与线性回归模型拟合效果的关系，【例4】某沙漠地区经过治理，生态系统得到很大改善，野生动物数量有所增加.为调查该地区某种野生动物的数量，将其分成面积相近的200个地块，从这些地块中用简单随机抽样的方法抽取20个作为样区，调查得到样本数据 $\big ( x _ { i } , y _ { i } \big ) ( i = 1 , 2 , \cdots , 2 0 )$ ，其中 $x _ { i }$ 和 $y _ { j }$ 分别表示第 $i$ 个样区的植物覆盖面积（单位：公顷）和这种野生动物的数量(单位：头)，并计算得 ${ \frac { \mathbb { 1 } } { \lambda } } \sum _ { i = 1 } ^ { 2 0 } x _ { i } = 6 0 \ , \ \sum _ { i = 1 } ^ { 2 0 } y _ { i } = 1 2 0 0 \ , \ \sum _ { i = 1 } ^ { 2 0 } \Big ( x _ { i } - { \overline { { x } } } \Big ) ^ { 2 } = 8 0 \ , \ \sum _ { i = 1 } ^ { 2 0 } \Big ( y _ { i } - { \overline { { y } } } \Big ) ^ { 2 } = 9 0 0 0 \ , \ \sum _ { i = 1 } ^ { 2 0 } \Big ( x _ { i } - { \overline { { x } } } \Big ) \Big ( y _ { i } - { \overline { { y } } } \Big ) = 8 0 0 \ .$

(1)估计该地区这种野生动物的数量；(2)求样本 $\big ( x _ { i } , y _ { i } \big ) ( i = 1 , 2 , \cdots , 2 0 )$ 的相关系数.（精确到0.01）

# 选择性必修第三册

# 【题型专练】

1．某省为了坚决打赢脱贫攻坚战，在100个贫困村中，用简单随机抽样的方法抽取15个进行脱贫验收调查，调查得到的样本数据 $\left( x _ { i } , y _ { i } \right) \left( i = 1 , 2 , \cdots , 1 5 \right)$ ，其中 $x _ { i }$ 和 $\mathcal { \gamma } _ { i }$ 分别表示第 $i$ 个贫困村中贫困户的年平均收入（单位：万元）和产业扶贫资金投入数量（单位：万元），并计算得到 $\sum _ { i = 1 } ^ { 1 5 } x _ { i } = 1 5$ ， $\sum _ { i = 1 } ^ { 1 5 } y _ { i } = 7 5 0$ ， $\sum _ { i = 1 } ^ { 1 5 } \left( x _ { i } - { \overline { { x } } } \right) ^ { 2 } = 0 . 8 2 ,$ $\sum _ { i = 1 } ^ { 1 5 } { \left( y _ { i } - { \overline { { y } } } \right) ^ { 2 } } = 1 6 7 0 ~ , ~ \sum _ { i = 1 } ^ { 1 5 } { \left( x _ { i } - { \overline { { x } } } \right) \left( y _ { i } - { \overline { { y } } } \right) } = 3 5 . 3 ~ .$

(1)试估计该省贫困村的贫困户年平均收入；  
(2)根据样本数据，求该省贫困村中贫困户年平均收入与产业扶贫资金投入的相关系数（精确到0.01)；(3)根据现有统计资料，各贫困村产业扶贫资金投入差异很大．为了确保完成脱贫攻坚任务，准确地进行脱贫验收，请给出一种你认为更合理的抽样方法，并说明理由.

参考公式： $r = { \frac { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - { \overline { { x } } } \right) \left( y _ { i } - { \overline { { y } } } \right) } { \sqrt { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - { \overline { { x } } } \right) ^ { 2 } \sum _ { i = 1 } ^ { n } \left( y _ { i } - { \overline { { y } } } \right) ^ { 2 } } } }$

2．下图是我国2014年至2021年生活垃圾无害化处理量（单位：亿吨）的折线图.（注：年份代码1\~7分别对应年份 $2 0 1 4 \sim 2 0 2 1 . )$

![](images/e9a38ee26c4ac8055c70d29f32c48b726ae92a74b7480b5e07b99f56b108ca78.jpg)  
年份代码1

由折线图看出，可用线性回归模型拟合 $y$ 与 $t$ 的关系．请求出相关系数 $r$ ，并用相关系数的大小说明y与t相关性的强弱.

参考数据和公式： $\sum _ { i = 1 } ^ { 7 } y _ { i } = 1 0 . 9 7$ ， $\sum _ { i = 1 } ^ { 7 } t _ { i } y _ { i } = 4 7 . 3 6$ ， $\sqrt { \sum _ { i = 1 } ^ { 7 } \left( y _ { i } - { \overline { { y } } } \right) ^ { 2 } } = 0 . 6 6 4$ ，样本相关系数$r = { \frac { \displaystyle \sum _ { i = 1 } ^ { n } \Big ( t _ { i } - \overline { { t } } \Big ) \Big ( y _ { i } - \overline { { y } } \Big ) } { \displaystyle \sqrt { \sum _ { i = 1 } ^ { n } \Big ( t _ { i } - \overline { { t } } \Big ) ^ { 2 } } \cdot \sqrt { \sum _ { i = 1 } ^ { n } \Big ( y _ { i } - \overline { { y } } \Big ) ^ { 2 } } } } .$

3．为调查野生动物保护地某种野生动物的数量，将保护地分成面积相近的300个地块，并设计两种抽样方案.方案一：在该地区应用简单随机抽样的方法抽取30个作为样本区，依据抽样数据计算得到相应的相关系数$r = 0 . 8 1$ ;

方案二：在该地区应用分层抽样的方法抽取30个作为样本区，调查得到样本数据 $\left( x _ { i } , y _ { i } \right) \left( i = 1 , 2 , \cdots , 3 0 \right)$ ，其中 $x _ { i }$ 和 $y _ { i }$ 分别表示第 $i$ 个样区的植物覆盖面积(单位：公顷)和这种野生动物的数量,并计算得 $\sum _ { i = 1 } ^ { 3 0 } x _ { i } = 6 0 \ , \sum _ { i = 1 } ^ { 3 0 } y _ { i } = 1 2 0 0$ ，$\sum _ { i = 1 } ^ { 3 0 } ( x _ { i } - \overline { { x } } ) ^ { 2 } = 9 0 , \quad \sum _ { i = 1 } ^ { 3 0 } ( y _ { i } - \overline { { y } } ) ^ { 2 } = 8 0 0 0 , \quad \sum _ { i = 1 } ^ { 3 0 } ( x _ { i } - \overline { { x } } ) ( y _ { i } - \overline { { y } } ) = 8 0 0 .$

(1)求该地区这种野生动物数量的估计值（这种野生动物数量的估计值等于样区这种野生动物数量的平均数乘以地块数）；

(2)求方案二抽取的样本 $\left( x _ { i } , y _ { i } \right) \left( i = 1 , 2 , \cdots , 3 0 \right)$ 的相关系数 $r$ （精确到0.01)，并判定哪种抽样方法更能准确地估计这种野生动物的数量.

附：若相关系数 $\left| r \right| \in \left[ 0 . 7 5 , 1 \right]$ 则相关性很强， $\left| \boldsymbol { r } \right|$ 的值越大相关性越强.

4．近年来，随着社会对教育的重视，家庭的平均教育支出增长较快，随机抽样调查某市2015\~2021年的家庭平均教育支出，得到如下折线图.（附：年份代码1\~7分别对应的年份是2015\~2021）.

(1)用线性回归模型拟合 $y$ 与 $t$ 的关系，求出相关系数 $r$ （精确到0.01），并指出是哪一层次的相关性？（相关系数 $\left| \boldsymbol { r } \right| \in \left[ 0 . 7 5 , 1 \right]$ 时相关性较强，$| r | \in \left( 0 . 2 5 , 0 . 7 5 \right)$ 时相关性一般， $\left| \boldsymbol { r } \right| \in \left[ 0 , 0 . 2 5 \right]$ 时相关性较弱.)

(2)求教育支出所占家庭总支出的比例 $y$ 与年份代码 $x$ 的线性回归方程；当2022年该市某家庭总支出为10万元，预测该家庭教育支出约为多少万y教育支出所占家庭总支出比例（百分比605550454035302520012345678年份代码t

$r = { \frac { \displaystyle \sum _ { i = 1 } ^ { n } \left( t _ { i } - { \overline { { t } } } \right) \left( y _ { i } - { \overline { { y } } } \right) } { \sqrt { \displaystyle \sum _ { i = 1 } ^ { n } \left( t _ { i } - { \overline { { t } } } \right) ^ { 2 } \sum _ { i = 1 } ^ { n } \left( y _ { i } - { \overline { { y } } } \right) ^ { 2 } } } }$ $\hat { b } = \frac { \displaystyle \sum _ { i = 1 } ^ { n } \big ( t _ { i } - \overline { { t } } \big ) \big ( y _ { i } - \overline { { y } } \big ) } { \displaystyle \sum _ { i = 1 } ^ { n } \big ( t _ { i } - \overline { { t } } \big ) ^ { 2 } } , \hat { a } = \hat { y } - \hat { b } \overline { { t } } .$

$$
\sum _ { i = 1 } ^ { 7 } y _ { i } = 2 5 9 , \quad \sum _ { i = 1 } ^ { 7 } t _ { i } y _ { i } = 1 1 7 8 , \quad \sqrt { 7 } \approx 2 . 6 5 , \quad \sqrt { \sum _ { i = 1 } ^ { 7 } \left( y _ { i } - \overline { { y } } \right) ^ { 2 } } = 2 7 , \quad \sum _ { i = 1 } ^ { 7 } \left( t _ { i } - \overline { { t } } \right) \left( y _ { i } - \overline { { y } } \right) = 1 2 6 .
$$

5．随机选取变量 $x$ 和变量Y的5对观测数据，选取的第 $i \left( i = 1 , 2 , 3 , 4 , 5 \right)$ 对观测数据记为 $\left( x _ { i } , y _ { i } \right)$ ，其数值对应如下表所示：

<html><body><table><tr><td>编号i</td><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td></tr><tr><td>Xi</td><td>9</td><td>8</td><td>7</td><td>6</td><td>5</td></tr><tr><td>yi</td><td>75</td><td>95</td><td>110</td><td>135</td><td>150</td></tr></table></body></html>

计算得： $\overline { { x } } = \frac { 1 } { 5 } \sum _ { i = 1 } ^ { 5 } x _ { i } = 7$ ， $\overline { { y } } = \frac { 1 } { 5 } \sum _ { i = 1 } ^ { 5 } y _ { i } = 1 1 3$ ， $\sum _ { i = 1 } ^ { 5 } x _ { i } ^ { 2 } - 5 \overline { { x } } ^ { 2 } = 1 0$ ， $\sum _ { i = 1 } ^ { 5 } y _ { i } ^ { 2 } - 5 y ^ { 2 } = 3 6 3 0$ ，xy=3765.

(1）求变量 $x$ 和变量 $Y$ 的样本相关系数（小数点后保留4位），判断这两个变量是正相关还是负相关，并推断它们的线性相关程度；

(2)假设变量 $Y$ 关于 $x$ 的一元线性回归模型为 $\scriptstyle { \left\{ \begin{array} { l l } { Y = b x + a + e } \\ { E { \bigl ( } e { \bigr ) } = 0 , D { \bigl ( } e { \bigr ) } = \sigma ^ { 2 } } \end{array} \right. }$

（i）求 $Y$ 关于 $x$ 的经验回归方程，并预测当 $x = 1 0$ 时 $Y$ 的值；

（ii）设 $\hat { e } _ { i }$ 为 $x = x _ { i } \left( i = 1 , 2 , 3 , 4 , 5 \right)$ 时该回归模型的残差，求 $\widehat { e _ { 1 } } \cdot \widehat { e _ { 2 } } \cdot \widehat { e _ { 3 } } \cdot \widehat { e _ { 4 } } \cdot \widehat { e _ { 5 } }$ 的方差.

$$
r = \frac { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - \overline { { x } } \right) \left( y _ { i } - \overline { { y } } \right) } { \sqrt { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - \overline { { x } } \right) ^ { 2 } } \sqrt { \displaystyle \sum _ { i = 1 } ^ { n } \left( y _ { i } - \overline { { y } } \right) ^ { 2 } } } , \hat { b } = \frac { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - \overline { { x } } \right) \left( y _ { i } - \overline { { y } } \right) } { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - \overline { { x } } \right) ^ { 2 } } , \hat { a } = \overline { { y } } - \hat { b } \cdot \overline { { x } } .
$$

题型五：线性回归方程恒过样本中心点的应用【例1】如果在一次实验中，测得 $\left( x , y \right)$ 的五组数值如下表所示：

<html><body><table><tr><td></td><td>x01234</td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td>y1015203035</td><td></td></tr></table></body></html>

经计算知， $y$ 对 $x$ 的线性回归方程是 $\hat { \boldsymbol { y } } = 6 . 5 \boldsymbol { x } + \hat { \boldsymbol { a } }$ ，预测当 $x = 6$ 时， $y = ( )$

A. 47.5 B.48 C.49 D.49.5【例2】由变量 $x$ 与 $y$ 相对应的一组数据 $( 1 , y _ { 1 } ) , ( 2 , y _ { 2 } ) , ( 3 , y _ { 3 } ) , ( 4 , y _ { 4 } ) , ( 5 , y _ { 5 } )$ 得到的线性回归方程为 $\hat { y } = 2 x + 4 5$ ，根据样本中心 $\left( { \overline { { x } } } , { \overline { { y } } } \right)$ 满足线性回归方程，则 $\overline { { y } } =$ （）

A.45 B.51 C.67 D.63【例3】已知由样本数据点集合 $\{ x _ { i = } y _ { i } ) \mid i = 1 , 2 , \cdots , n \}$ ，求得的回归直线方程 $l _ { \ i }$ 为 $\hat { y } = 1 . 5 x + 0 . 5$ ，且 $\overline { { x } } = 3$ ．现发现两个数据点(0.9,2.4)和(5.1,7.6)误差较大，去除这两点后重新求得的回归直线方程 $l _ { 2 }$ 的斜率为1.2，则当 $x = 2$ 时，由 $l _ { 2 }$ 的方程得 $y$ 的估计值为（）

A.2.9 B.3.5 C.3.8 D.4.1【例4】（多选题）月亮公转与自转的周期都大约为27天，阴历是按月亮的月相周期安排的历法，人们根据长时间的观测，统计了月亮出来的时刻y（简称"月出时刻”，单位：h）与阴历日数 $x$ （ $\boldsymbol { x } \in \mathbf { N } ^ { * }$ ，且 $x \leq 3 0$ ）的有关数据如表所示，并且根据表中数据，求得 $y$ 关于 $x$ 的经验回归方程为 $\hat { y } = 0 . 8 x + \hat { a }$

<html><body><table><tr><td></td><td></td><td>x24710</td><td></td><td></td><td>15</td><td>22</td></tr><tr><td></td><td></td><td></td><td></td><td>y8.19.41214.418.524</td><td></td><td></td></tr></table></body></html>

其中，阴历22日是分界线，从阴历22日开始月亮就要到第二天（即23日0：00）才出来.则（）

A. $\overline { { x } } = 1 0$ ， $\overline { { y } } = 1 4 . 4$ B. $\hat { a } = 6 . 8$ C．预报月出时刻为16h的那天是阴历13日D．预报阴历27日的月出时间为阴历28日早上4：00【例5】（多选题）已知某高中的女生体重 $y$ （单位： $\mathrm { k g }$ ）与身高 $x$ （单位：cm）具有线性相关关系，根据一组样本数据 $\left( x _ { i } , y _ { i } \right)$ （ $i = 1 , 2 , 3 , \cdots , n )$ ，由最小二乘法近似得到 $y$ 关于 $x$ 的经验回归方程为 $\hat { y } = 0 . 8 x - 8 5 . 7 1$ ，则下列结论中正确的是（）

A. $y$ 与 $x$ 是正相关的  
B．该经验回归直线必过点 $\left( \overline { { x } } , \overline { { y } } \right)$   
C．若该高中的女生身高增加lcm，则其体重约增加 $0 . 8 5 \mathrm { k g }$ D．若该高中的女生身高为 $1 6 0 \mathrm { c m }$ ，则其体重必为 $5 0 . 2 9 \mathrm { k g }$

# 选择性必修第三册

# 【题型专练】

1．某单位为了解夏季用电量与月份的关系，对本单位2021年5月份到8月份的日平均用电量 $y$ （单位：千度）进行了统计分析，得出下表数据：

<html><body><table><tr><td>月份（x）</td><td>5</td><td>67</td><td></td><td>8</td></tr><tr><td>日平均用电量（y)</td><td></td><td>1.93.4t7.1</td><td></td><td></td></tr></table></body></html>

若 $y$ 与 $x$ 线性相关，且求得其线性回归方程 $\hat { y } = 1 . 7 8 x - 7 . 0 7$ ，则表中 $t$ 的值为（）

A.5.8 B.5.6 C.5.4 D.5.2

2.某公司为了确定下一年投入某种产品的宣传费，需了解年宣传费 $x$ （单位：万元）对年销售量y（单位：千件）的影响.现收集了近5年的年宣传费 $x$ （单位：万元）和年销售量 $y$ （单位：千件）的数据，其数据如下表所示，且 $y$ 关于 $x$ 的线性回归方程为 $\hat { y } = \hat { b } x - 8 . 2$ ，则下列结论错误的是（）

<html><body><table><tr><td></td><td></td><td></td><td></td><td>x4681012</td><td></td></tr><tr><td></td><td></td><td></td><td></td><td>y1571418</td><td></td></tr></table></body></html>

A. $x$ ， $y$ 之间呈正相关关系  
B. $\widehat { b } = 2 . 1 5$   
C．该回归直线一定经过点(8,7)  
D．当此公司该种产品的年宣传费为20万元时，预测该种产品的年销售量为34800件

3.已知变量 $x$ 与 $y$ 正相关，且由观测数据算得样本平均数 ${ \overline { { x } } } = 2$ ， $\overline { { y } } = 1 0$ ，则由观测的数据得到的线性回归方程可能为（）

A. $y = - 1 . 5 x + 1 1$ B. $y = - 0 . 5 x + 1 1$ C. $y = 0 . 5 x + 9$ D. $y = 1 . 5 x + 8$

4．用最小二乘法得到一组数据 $\left( x _ { i } , y _ { i } \right)$ （其中 $i = 1$ 、2、3、4、5）的线性回归方程为 $\hat { y } = b x + 3$ ，若 $\sum _ { i = 1 } ^ { 5 } x _ { i } = 2 5$ ，$\sum _ { i = 1 } ^ { 5 } y _ { i } = 6 5$ ，则当 $x = 8$ 时， $y$ 的预报值为（）

A.18 B.19 C.20 D.21

# 选择性必修第三册

5.（多选题）近年来考研成为许多大学生的热门选择，某研究机构为了解大学生考研情况，对2018年至2022年研究生报考人数（单位：万人）作出统计如下表：

<html><body><table><tr><td>年份</td><td></td><td>20182019</td><td></td><td>20202021</td><td>2022</td></tr><tr><td>年份代码</td><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td></tr><tr><td>研究生报考人数/万人</td><td>238</td><td>290</td><td>341</td><td>377</td><td>457</td></tr></table></body></html>

根据上述统计数据求得研究生报考人数 $y$ 与年份代码 $x$ 满足的线性回归方程为 $\hat { y } = \hat { b } x + 1 8 3 . 1$ ，则（)A. $\hat { b } = 5 2 . 5$

B．回归直线 $\hat { y } = \hat { b } x + 1 8 3 . 1$ 经过点（4,377)C．2018年至2022年每年研究生报考人数约增加183.1万人D．预测2024年研究生报考人数为550.6万人

6.（多选题）在统计中，由一组样本数据 $( x _ { I } , \ y _ { I } )$ ， $( x _ { 2 } , y _ { 2 } )$ ，...（xn，yn）利用最小二乘法得到两个变量的回归直线方程为 $y = b x + a$ ，那么下面说法正确的是（）

A.直线 $y = b x + a$ 至少经过点 $( x _ { I } , \ y _ { I } )$ ， $( x _ { 2 } , y _ { 2 } )$ ，...（xn，yn）中的一个点B.直线 $y = b x + a$ 必经过点 $( \overline { { x } } , \overline { { y } } )$ C.直线 $y = b x + a$ 表示最接近 $y$ 与 $x$ 之间真实关系的一条直线D. $| r | { \le } 1$ ，且 $| r |$ 越接近于1，相关程度越大； $| r |$ 越接近于0，相关程度越小

7.某设备的使用年限与所支出的维修费用的统计数据如下表：

<html><body><table><tr><td>使用年限×（单位：年）</td><td>2</td><td>3</td><td>4</td><td>s</td><td>6</td></tr><tr><td>维修费用（单位：万元）</td><td></td><td></td><td>1.54.55.56.57.0</td><td></td><td></td></tr></table></body></html>

根据上表可得回归直线方程为： $\hat { y } = 1 . 3 x + \hat { a }$ ，据此模型预测,若使用年限为10年，估计维修费用约为

# 【精选例题】

【例1】在一项调查中有两个变量 $x$ 和y，如图是由这两个变量近8年来的取值数据得到的散点图，那么适宜作为 $y$ 关于 $x$ 的回归方程的函数类型是（）

![](images/520471ecce344fca91dfa7ca0a4d0a3323e2fe8740b2ff7d4d6909371ebb781e.jpg)

A. $y = a + b x$ B.y=c+d√x $\mathrm { C . } \quad y = m + n x ^ { 2 } \qquad \mathrm { D . } \quad y = p + q c ^ { x } \left( q > 0 \right)$

【例2】用 $y$ 关于 $x$ 的方程 $y = m e ^ { n x }$ 来拟合一组数据 $\left( x _ { i } , y _ { i } \right) ( { \mathit { i } } = 1 , 2 , . . . , 1 0 )$ 时为了求出其回归方程，设 $z = \ln y$ ，得到 $z$ 关于 $_ { x }$ 的线性回归方程 $z = 0 . 6 x + 1$ ，则（）

A. $m = { \sf e }$ ， $n = 0 . 6$ B. $m = 0 . 6$ ， $n = { \mathfrak { e } }$ C. $m = 1$ ， $n = 0 . 6$ D. $m = 0 . 6$ ， $n = 1$

【例3】某工厂生产一种产品测得数据如下：

<html><body><table><tr><td>尺寸x（mm）</td><td>38</td><td>48</td><td>58</td><td>68</td><td>78</td><td>88</td></tr><tr><td>质量（g）</td><td>16.8</td><td>18.8</td><td>20.7</td><td>22.4</td><td>24</td><td>25.5</td></tr><tr><td>质量与尺寸的比x</td><td></td><td></td><td>0.4420.3920.357</td><td>0.329</td><td>0.3080.290</td><td></td></tr></table></body></html>

(1)若按照检测标准，合格产品的质量 $y ( \mathrm { g } )$ 与尺寸 $x \mathrm { ( m m ) }$ 之间近似满足关系式 $y = c \cdot x ^ { d }$ （ $c , d$ 为大于0的常数），求 $y$ 关于 $x$ 的回归方程；

(2)已知产品的收益 $z$ （单位：千元）与产品尺寸和质量的关系为 $z = 2 y - 0 . 3 2 x$ ，根据（1）中回归方程分析，当产品的尺寸 $x$ 约为何值时（结果用整数表示），收益 $z$ 的预报值最大？

附：（1）参考数据： $\sum _ { i = 1 } ^ { 6 } \left( \ln x _ { i } \cdot \ln y _ { i } \right) = 7 5 . 3 ~ , \sum _ { i = 1 } ^ { 6 } \left( \ln x _ { i } \right) = 2 4 . 6 ~ , \sum _ { i = 1 } ^ { 6 } \left( \ln y _ { i } \right) = 1 8 . 3 ~ , \sum _ { i = 1 } ^ { 6 } \left( \ln x _ { i } \right) ^ { 2 } = 1 0 1 . 4 ~ .$

(2）参考公式：对于样本 $\left( \nu _ { i } , u _ { i } \right) ( i = 1 , 2 , \cdots , n )$ ，其回归直线 $\boldsymbol { u } = \boldsymbol { \hat { b } } \cdot \boldsymbol { v } + \boldsymbol { \hat { a } }$ 的斜率和截距的最小二乘估计公式分别为：6= $= \frac { \displaystyle \sum _ { i = 1 } ^ { n } \bigl ( \nu _ { i } - \overline { { \nu } } \bigr ) \bigl ( u _ { i } - \overline { { u } } \bigr ) } { \displaystyle \sum _ { i = 1 } ^ { n } \bigl ( \nu _ { i } - \overline { { \nu } } \bigr ) ^ { 2 } } = \frac { \displaystyle \sum _ { i = 1 } ^ { n } \nu _ { i } u _ { i } - \overline { { n \nu u } } } { \displaystyle \sum _ { i = 1 } ^ { n } \nu _ { 1 } ^ { 2 } - \overline { { n \nu } } ^ { 2 } } , \hat { a } = \overline { { u } } - \hat { b } \overline { { \nu } } , \mathrm { e } \approx 2 . 7 1 8 2 .$

【例4】新型冠状病毒肺炎COVID-19疫情发生以来，在世界各地逐渐蔓延.在全国人民的共同努力和各级部门的严格管控下，我国的疫情已经得到了很好的控制.然而，小王同学发现，每个国家在疫情发生的初期，由于认识不足和措施不到位，感染人数都会出现快速的增长.下表是小王同学记录的某国连续8天每日新型冠状病毒感染确诊的累计人数.

<html><body><table><tr><td>日期代码x</td><td></td><td></td><td></td><td></td><td></td><td>12345678</td><td></td><td></td></tr><tr><td>累计确诊人数y481631517197122</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

为了分析该国累计感染人数的变化趋势，小王同学分别用两杆模型： $\textcircled { 1 } \hat { y } = b x ^ { 2 } + a$ ， $\textcircled { 2 } \hat { y } = d x + c$ 对变量 $x$ 和 $y$ 的关系进行拟合，得到相应的回归方程并进行残差分析，残差图如下(注：残差 $\hat { \mathbf { e } } _ { i } = y _ { i } - \hat { y } _ { i }$ )：经过计算得$\sum _ { i = 1 } ^ { 8 } ( x _ { i } - \overline { { x } } ) ( y _ { i } - \overline { { y } } ) = 7 2 8 \ , \sum _ { i = 1 } ^ { 8 } ( x _ { i } - \overline { { x } } ) ^ { 2 } = 4 2 \ , \sum _ { i = 1 } ^ { 8 } ( z _ { i } - \overline { { z } } ) ( y _ { i } - \overline { { y } } ) = 6 8 6 8 \ , \sum _ { i = 1 } ^ { 8 } ( z _ { i } - \overline { { z } } ) ^ { 2 } = 3 5 7 0 \ ,$ 其中 $z _ { i } = x _ { i } ^ { 2 }$ ， $\overline { { z } } = \frac { 1 } { 8 } \sum _ { i = 1 } ^ { 8 } z _ { i }$

![](images/41518a361d865ab71cd2e79e821e1de90c0a605ee4b9bf8d1c1eafb44f628b28.jpg)

(1)根据残差图，比较模型 $\textcircled{1}$ ， $\textcircled{2}$ 的拟合效果，应该选择哪个模型?并简要说明理由；  
(2)根据（1）问选定的模型求出相应的回归方程（系数均保留两位小数）；  
(3)由于时差，该国截止第9天新型冠状病毒感染确诊的累计人数尚未公布.小王同学认为，如果防疫形势没有得到明显改善，在数据公布之前可以根据他在（2）问求出的回归方程来对感染人数做出预测，那么估计该地区第9天新型冠状病毒感染确诊的累计人数是多少？（结果保留整数）

附：回归直线的斜率和截距的最小二乘估计公式分别为： $\hat { b } = \frac { \displaystyle \sum _ { i = 1 } ^ { 8 } \big ( x _ { i } - \overline { { x } } \big ) \big ( y _ { i } - \overline { { y } } \big ) } { \displaystyle \sum _ { i = 1 } ^ { 8 } \big ( x _ { i } - \overline { { x } } \big ) ^ { 2 } } , \hat { a } = \overline { { y } } - \hat { b } \overline { { x } } .$

# 选择性必修第三册

# 【题型专练】

1.2021年春季．新冠肺炎疫情在印度失控．下图是印度某地区在60天内感染新冠肺炎的累计病例人数y（万人）与时间t（天）的散点图．则下列最适宜作为此模型的回归方程的类型是（）

![](images/a6fc476453d0d5b4fd15aedeb62029286acee13726207962cf3dfc9d1b7b7877.jpg)

A. $y = a + b x$ B.y=a+b√x C. $y = a + b e ^ { x }$ D.y=a+blnx

2.某企业在一段时期内为准确把握市场行情做了如下调研：每投入金额为 $x$ （单位：万元），企业获得收益金额为 $y$ （单位：万元），现将投入金额与收益金额数据作初步统计整理如下表：（表中 $u _ { i } = \frac { 1 } { x _ { i } } , ~ \overline { { u } } = \frac { 1 } { 9 } \sum _ { i = 1 } ^ { 9 } u _ { i } \mathrm { ~ ) ~ }$ (1)利用样本相关系数的知识，判断 $y = a + b x$ 与 $y = c + { \frac { d } { x } }$ 哪一个更适宜作为收益金额 $y$ 关于投入金额 $x$ 的回归方程模型？

<html><body><table><tr><td></td><td></td><td>u</td><td></td><td>i=1</td><td>i=1</td><td>≌（x-²（-）²（）²（x）（-） i=1</td><td>（u，-2）（，-3 i=]</td></tr><tr><td>60</td><td>117.50</td><td>0.20</td><td>300</td><td>0.12</td><td>27</td><td>73.90</td><td>-1.50</td></tr></table></body></html>

(2)根据（1）的结果解答下列问题.  
$\textcircled{1}$ 建立 $y$ 关于 $x$ 的回归方程；  
$\textcircled{2}$ 样本对投入金额 $x = \frac { 2 5 } { 2 }$ 时，企业收益预报值是多少万元？

附：对于一组数据 $\left( t _ { 1 } , s _ { 1 } \right) , \left( t _ { 2 } , s _ { 2 } \right) , \mathrm { ~ L ~ } , \left( t _ { n } , s _ { n } \right)$ ，其线性相关系数 $r = { \frac { \displaystyle \sum _ { i = 1 } ^ { n } \left( t _ { i } - { \overline { { t } } } \right) \left( s _ { i } - { \overline { { s } } } \right) } { \displaystyle \sqrt { \sum _ { i = 1 } ^ { n } \left( t _ { i } - { \overline { { t } } } \right) ^ { 2 } \sum _ { i = 1 } ^ { n } \left( s _ { i } - { \overline { { s } } } \right) ^ { 2 } } } }$ ，其回归直线 $s = \alpha + \beta t$ 的斜率和截距的最小二乘估计分别为：β= $\hat { \beta } = \frac { \displaystyle \sum _ { i = 1 } ^ { n } \left( t _ { i } - \overline { { t } } \right) \left( s _ { i } - \overline { { s } } \right) } { \displaystyle \sum _ { i = 1 } ^ { n } \left( t _ { i } - \overline { { t } } \right) ^ { 2 } }$ $\stackrel { \frown } { \alpha } = \stackrel { - } { s } - \widehat { \beta } \stackrel { - } { t }$

3．为了解某地区未成年男性身高与体重的关系，对该地区12组不同身高 $x _ { i }$ （单位：cm）的未成年男性体重的平均值 $y _ { j }$ （单位： $\mathrm { k g }$ ）（ $i = 1 , 2 , \cdots , 1 2$ ）数据作了初步处理，得到下面的散点图和一些统计量的值.

![](images/6dda6d66bd6eebd2a820b147327fcee57711cb64b5d15d69ee33645d9bba1499.jpg)

<html><body><table><tr><td>-x</td><td></td><td></td><td>（-x）²</td><td>2（x-x）（(,-）</td><td>2（x,-x)（,-）</td></tr><tr><td>115</td><td>24.358</td><td>2.958</td><td>14300</td><td>6300</td><td>286</td></tr></table></body></html>

裁 $\omega _ { i } = \ln { y _ { i } } \left( i = 1 , 2 , \cdots , 1 2 \right)$ ， $\overline { { \omega } } = \frac { 1 } { 1 2 } \sum _ { i = 1 } ^ { 1 2 } \omega _ { i }$

（1）根据散点图判断 $y = a x + b$ 和 $y = e ^ { c x + d }$ 哪一个适宜作为该地区未成年男性体重的平均值 $y$ 与身高 $x$ 的回归方程类型？（给出判断即可，不必说明理由），

（2）根据（1）的判断结果及表中数据，建立 $y$ 关于 $x$ 的回归方程；

（3）如果体重高于相同身高的未成年男性平均值的1.2倍为偏胖，低于0.8倍为偏瘦，那么该地区的一位未成年男性身高为 $1 7 5 \mathrm { c m }$ ，体重为 $7 8 \mathrm { k g }$ ，他的体重是否正常？

附：对于一组数据 $\left( u _ { 1 } , \nu _ { 1 } \right) , \left( u _ { 2 } , \nu _ { 2 } \right)$ ，.， $\left( u _ { n } , v _ { n } \right)$ ，其回归直线 $\nu = \alpha + \beta u$ 的斜率和截距的最小二乘估计分别为${ \hat { \beta } } = { \frac { \displaystyle \sum _ { i = 1 } ^ { n } \bigl ( u _ { i } - { \overline { { u } } } \bigr ) \bigl ( \nu _ { i } - { \overline { { \nu } } } \bigr ) } { \displaystyle \sum _ { i = 1 } ^ { n } \bigl ( u _ { i } - { \overline { { u } } } \bigr ) ^ { 2 } } } , \ { \hat { \alpha } } = { \overline { { \nu } } } - { \hat { \beta } } { \overline { { u } } } , \ \ln 2 \approx 0 . 6 9 3 .$

4．住房和城乡建设部等六部门发布通知提出，到2025年，农村生活垃圾无害化处理水平明显提升，我国生活垃圾主要有填埋、烧与堆肥三种处理方式，随着我国垃圾处理结构的不断优化调整，烧处理逐渐成为市场主流：根据国家统计局公布的数据，对2013—2020年全国生活垃圾荧烧无害化处理厂的个数y（单位：座）进行统计，得到如下表格：

<html><body><table><tr><td>年份</td><td></td><td></td><td></td><td></td><td></td><td>20132014201520162017201820192020</td><td></td><td></td></tr><tr><td>年份代码x</td><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td><td>6</td><td>7</td><td>8</td></tr><tr><td>生活垃圾烧无害化处理厂的个数y166」188220」249</td><td></td><td></td><td></td><td></td><td>286</td><td>331</td><td>389</td><td>463</td></tr></table></body></html>

(1)由表中数据可知，可用线性回归模型拟合 $y$ 与 $x$ 之间的关系，请用相关系数加以说明；（精确到0.01）(2)求出 $y$ 关于 $x$ 的线性回归方程，并预测2022年全国生活垃圾焚烧无害化处理厂的个数；  
(3)对于2035年全国生活垃圾烧无害化处理厂的个数，还能用所求的线性回归方程预测吗？请简要说明理由.参考公式：相关系数 $r = { \frac { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - { \overline { { x } } } \right) \left( y _ { i } - { \overline { { y } } } \right) } { \sqrt { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - { \overline { { x } } } \right) ^ { 2 } \sum _ { i = 1 } ^ { n } \left( y _ { i } - { \overline { { y } } } \right) ^ { 2 } } } }$ ，回归方程 $\oint _ { } = \oint _ { } + \frac { \oint _ { } } { d \mathbf { \alpha } }$ 中斜率和裁距的最小二乘估计公式分别为${ \widehat { b } } = { \frac { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - { \overline { { x } } } \right) \left( y _ { i } - { \overline { { y } } } \right) } { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - { \overline { { x } } } \right) ^ { 2 } } } , \quad { \widehat { \mathbb { Q } } } = { \overline { { y } } } - { \widehat { \mathbb { B } } } { \overline { { x } } } .$   
参考数据： $\sum _ { i = 1 } ^ { 8 } y _ { i } = 2 2 9 2 , \sum _ { i = 1 } ^ { 8 } x _ { i } ^ { 2 } = 2 0 4 , \sum _ { i = 1 } ^ { 8 } y _ { i } ^ { 2 } = 7 3 0 3 4 8 , \sum _ { i = 1 } ^ { 8 } x _ { i } y _ { i } = 1 2 0 4 1 , 5 7 3 ^ { 2 } = 3 2 8 3 2 9 , \sqrt { 1 0 5 } \approx 1 0 . 2 5 ,$ $\sqrt { 7 3 6 9 } \approx 8 5 . 8 4$ ：

5．发展扶贫产业，找准路子是关键，重庆市石柱土家族自治县中益乡华溪村不仅找准了路，还将当地打造成了种植中药材黄精的产业示范基地．通过种植黄精，华溪村村民的收入逐年递增．以下是2014年至2020年华溪村村民每户平均可支配收入的统计数据：

<html><body><table><tr><td>年份</td><td>2014</td><td>2015</td><td>2016</td><td></td><td>20172018</td><td>20192020</td><td></td></tr><tr><td>年份代码x</td><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td><td>6</td><td>7</td></tr><tr><td>每户平均可支配收入（千元）</td><td>4</td><td></td><td></td><td>152226293132</td><td></td><td></td><td></td></tr></table></body></html>

根据以上数据，绘制如图所示的散点图：

![](images/88b3fae61bdd58db39c29178c5c84c4467e96742c9a908f39d1d6617f6d58db6.jpg)

(1)根据散点图判断， $y = a + b x$ 与 $y = c + d \ln x$ 哪一个更适宜作为每户平均可支配收入y（千元）关于年份代码x的回归方程模型（给出判断即可，不必说明理由)，并建立 $y$ 关于 $x$ 的回归方程(结果保留1位小数)；

(2)根据（1）建立的回归方程，试预测要到哪一年华溪村的每户平均可支配收入才能超过35（千元)；(3)从2014年至2020年中任选两年，求事件A：“恰有一年的每户平均可支配收入超过22（千元）"的概率.参考数据：

<html><body><table><tr><td>-</td><td>-u</td><td>Mxy 1</td><td>My</td><td></td><td>e21</td></tr><tr><td>22.7</td><td>1.2</td><td>759</td><td>235.1</td><td>13.2</td><td>8.2</td></tr></table></body></html>

其中 $u _ { i } = \ln x _ { i }$ ， $\overline { { u } } = \frac { 1 } { 7 } \sum _ { i = 1 } ^ { 7 } u _ { i }$

参考公式：线性回归方程 $\oint _ { \mathbf { \lambda } } = \oint _ { \mathbf { \lambda } } x + \oint _ { \mathbf { \lambda } }$ 中 ${ \hat { b } } = { \frac { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - { \overline { { x } } } \right) \left( y _ { i } - { \overline { { y } } } \right) } { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - { \overline { { x } } } \right) ^ { 2 } } }$

# 【考点分析】

$\textcircled{1} 2 \times 2$ 列联表设 $\chi$ ：， $Y$ 为两个变量，它们的取值分别为 $\{ x _ { 1 } , \ x _ { 2 } \}$ 和 $\left\{ y _ { 1 } , \ y _ { 2 } \right\}$ ，其样本频数列联表 $( 2 \times 2$ 列联表)如下：

<html><body><table><tr><td></td><td>y</td><td>y</td><td>总计</td></tr><tr><td>x1</td><td>a</td><td>b</td><td>a+b</td></tr><tr><td>x2</td><td>C</td><td>d</td><td>c+d</td></tr><tr><td>总计</td><td>a+c</td><td>b+d</td><td>a+b+c+d</td></tr></table></body></html>

$\textcircled{2}$ 独立性检验

利用随机变量 $K ^ { 2 }$ （也可表示为x2）的观测值 $\mathrm { K } ^ { 2 } = \frac { \mathrm { n } ( \mathrm { a d } - \mathrm { b c } ) ^ { 2 } } { ( \mathrm { a } + \mathrm { b } ) ( \mathrm { c } + \mathrm { d } ) ( \mathrm { a } + \mathrm { c } ) ( \mathrm { b } + \mathrm { d } ) }$ (其中 $n = a + b + c + d$ 为样本容量)来判断“两个变量有关系”的方法称为独立性检验.

$\textcircled{3}$ 两个临界值：3.841与6.635当 $\chi ^ { 2 } > 3 . 8 4 1$ 时，有 $9 5 \%$ 的把握说事件 $A$ 与 $B$ 有关；当 $x ^ { 2 } > 6 . 6 3 5$ 时，有 $9 9 \%$ 的把握说事件 $A$ 与 $B$ 有关；当 $x ^ { 2 } \leq 3 . 8 4 1$ 时，认为事件 $A$ 与 $B$ 是无关的.

题型一： $2 \times 2$ 列联表

【例1】下列是关于出生男婴与女婴调查的 $2 \times 2$ 列联表

<html><body><table><tr><td></td><td></td><td>晚上白天总计</td><td></td></tr><tr><td>男婴</td><td>45</td><td>A</td><td>B</td></tr><tr><td>女婴</td><td>E</td><td>35</td><td>C</td></tr><tr><td>总计</td><td>98</td><td>D</td><td>180</td></tr></table></body></html>

那么 $D =$

【例2】如表是 $2 \times 2$ 列联表，则表中的 $a , b$ 的值分别为（）

<html><body><table><tr><td></td><td>y</td><td>y</td><td>合计</td></tr><tr><td>X</td><td>a</td><td>8</td><td>35</td></tr><tr><td>X</td><td>11</td><td>34</td><td>45</td></tr><tr><td>合计</td><td>b</td><td>42</td><td>80</td></tr></table></body></html>

A.27.38 B.28.38 C.27.37 D.28.37

选择性必修第三册

【例3】假设有两个变量 $x$ 与 $y$ 的 $2 \times 2$ 列联表如下表：

<html><body><table><tr><td></td><td>y</td><td>y</td></tr><tr><td>X</td><td>a</td><td>b</td></tr><tr><td>X</td><td>C</td><td>d</td></tr></table></body></html>

对于以下数据，对同一样本能说明 $x$ 与 $y$ 有关系的可能性最大的一组为（）

A. $a = 2 0$ ， $b = 3 0$ ， $c = 4 0$ ， $d = 5 0$ B. $a = 5 0$ ， $b = 3 0$ ， $c = 3 0$ ， $d = 4 0$ C. $a = 3 0$ ， $b = 6 0$ ， $c = 2 0$ ， $d = 5 0$ D. $a = 5 0$ ， $b = 3 0$ ， $c = 4 0$ ， $d = 3 0$

# 【题型专练】

1.为了解某大学的学生是否爱好体育锻炼，用简单随机抽样方法在校园内调查了120位学生，得到如下 $2 \times 2$ 列联表：

<html><body><table><tr><td></td><td></td><td>男女</td><td>总计</td></tr><tr><td>爱好</td><td>a</td><td>b</td><td>73</td></tr><tr><td>不爱好</td><td></td><td>c25</td><td></td></tr><tr><td>总计</td><td>74</td><td></td><td></td></tr></table></body></html>

则 $a - b - c$ 等于（）

A.7 B.8 C.9 D.10

2．某村庄对该村内50名老年人、年轻人每年是否体检的情况进行了调查，统计数据如表所示：

<html><body><table><tr><td></td><td>每年体检</td><td>每年未体检</td><td>合计</td></tr><tr><td>老年人</td><td>a</td><td>7</td><td>C</td></tr><tr><td>年轻人</td><td>6</td><td>b</td><td>d</td></tr><tr><td>合计</td><td>e</td><td>f</td><td>50</td></tr></table></body></html>

已知抽取的老年人、年轻人各25名，则对列联表数据的分析错误的是（）

A. $a = 1 8$ B. $b = 1 9$ C. $c + d = 5 0$ D. $e - f = 2$

3．假设有两个分类变量 $x$ 与 $y$ 的 $2 \times 2$ 列联表如下表：

<html><body><table><tr><td></td><td>y</td><td>y</td></tr><tr><td></td><td>a</td><td>b</td></tr><tr><td>E. x2</td><td>c</td><td>d</td></tr></table></body></html>

对于以下数据，对同一样本能说明 $x$ 与 $y$ 有关系的可能性最大的一组为（）

A. $a = 5$ ， $b = 4$ ， $c = 3$ ， $d = 2$ B. $a = 5$ ， $b = 3$ ， $c = 4$ ， $d = 2$ C. $a = 2$ ， $b = 3$ ， $c = 4$ ， $d = 5$ D. $a = 2$ ， $b = 3$ ， $c = 5$ ， $d = 4$

# 题型二：等高条形图

【例1】为了解户籍性别对生育二胎选择倾向的影响，某地从育龄人群中随机抽取了容量为100的调查样本，其中城镇户籍与农村户籍各50人，男性40人，女性60人，绘制不同群体中倾向选择生育二胎与选择不生育二胎的人数比例图（如图所示)，其中阴影部分表示倾向选择生育二胎的对应比例，则关于样本下列叙述中正确的是（）

![](images/87bf3b193743f46b80d619cb23973d395d0481f3a3f44f0b2c01549e8f2cccb6.jpg)

A．是否倾向选择生育二胎与户籍无关  
B.是否倾向选择生育二胎与性别有关  
C．倾向选择生育二胎的人员中，男性人数与女性人数相同  
D.倾向选择不生育二胎的人员中，农村户籍人数少于城镇户籍人数

【例2】观察下面频率等高条形图，其中两个分类变量 $x$ ， $y$ 之间的随机变量 $\chi ^ { 2 }$ 的观测值最小的是（）

![](images/84544c6a255e9950ef3b055c8be1477e27ce16db92c87ae650f78ea33305fcf3.jpg)

【例3】在统计中，研究两个分类变量是否存在关联性时，常用的图表有（）

A.散点图和残差图 B．残差图和列联表C．散点图和等高堆积条形图D．等高堆积条形图和列联表【例4】某艺术馆为了研究学生性别和喜欢国画之间的联系，随机抽取80名学生进行调查（其中有男生50名，女生30名），并绘制等高条形图，则这80名学生中喜欢国画的人数为（）

![](images/fde9889c42caf7a409cff1af7f4ef50f6e2552a1b444e53842b3ff75826da160.jpg)

A.24B.32C.48D.58

【例5】为了解某高校学生使用手机支付和现金支付的情况，抽取了部分学生作为样本，统计其喜欢的支付方式，并制作出如下等高条形图(如图)，根据图中的信息，下列结论中不正确的是（）

![](images/1fce187b578bf9b5c7319756dfef07a2900111fb396431cd88b04011e828e3c4.jpg)

A．样本中的男生数量多于女生数量B．样本中喜欢手机支付的数量多于现金支付的数量C.样本中多数男生喜欢现金支付 D．样本中多数女生喜欢手机支付

# 选择性必修第三册

# 【题型专练】

1.我国目前部分普通高中学生在高一升高二时面临着选文理科的问题，某学校抽取了部分男、女学生意愿的一份样本，制作出如下两个等高堆积条形图

![](images/f9b3ff33de09c08c81f191e24b820f2c00245f886723e128af7052995247403c.jpg)

根据这两幅图中的信息，下列统计结论正确的是（）

A．样本中的男生数量多于女生数量B．样本中有理科意愿的学生数量少于有文科意愿的学生数量C．对理科有意愿的男生人数多于对文科有意愿的男生人数D．对文科有意愿的女生人数多于对理科有意愿的女生人数

2.“微信”和"QQ”是腾讯社交体系中的两款产品，小明为了解不同群体对这两款产品的首选情况，统计了周围老师和同学关于首选“微信”或“QQ的比例，得到如图等高条形图，根据等高条形图中的信息，可判断下列说法正确的是（）

![](images/d46cb88640c58aafe2e0a533e5bb594c796c5fbc67b85d3ac67d54337c24b12c.jpg)

A．对老师而言，更倾向于首选“微信”  
B．对学生而言，更倾向于首选 $\overleftrightarrow { Q Q } ^ { \ast }$   
C.首选“微信”的老师比首选“微信”的同学多  
D.如果首选“微信”的老师比首选“微信"的同学多，则小明统计的老师人数一定比学生多

3.（多选题）2018年12月1日，贵阳市地铁1号线全线开通，在一定程度上缓解了市内交通的拥堵状况．为了了解市民对地铁1号线开通的关注情况，某调查机构在地铁开通后的某两天抽取了部分乘坐地铁的市民作为样本，分析其年龄和性别结构，并制作出如下等高条形图：

82 09876543321035岁以上35岁以下 男性 女性□男性女性 35岁以下35岁以上根据图中(35岁以上含35岁)的信息，下列结论中一定正确的是（）

A．样本中男性比女性更关注地铁1号线全线开通B．样本中多数女性是35岁以上C.样本中35岁以下的男性人数比35岁以上的女性人数多D．样本中35岁以上的人对地铁1号线的开通关注度更高

4．某校为研究该校学生性别与体育锻炼的经常性之间的联系，随机抽取100名学生（其中男生60名，女生40名），并绘制得到如图所示的等高堆积条形图，则这100名学生中经常锻炼的人数为

![](images/a8f6c2cd2fcfece67c08c3fbd6249b25034714bf5c4b3ba1a26ec7e019f42eb7.jpg)

# 题型三：独立性检验解决实际问题

【例1】在研究某高中高三年级学生的性别与是否喜欢某学科的关系时，总共调查了 $N$ 个学生( $N = 1 0 0 \mathrm { m } , m \in \mathbf { N } ^ { * }$ 其中男女学生各半，男生中 $60 \%$ 表示喜欢该学科，其余表示不喜欢；女生中 $40 \%$ 表示喜欢该学科，其余表示不喜欢．若有 $9 9 . 9 \%$ 把握认为性别与是否喜欢该学科有关，则可以推测 $N$ 的最小值为（）

$$
K ^ { 2 } = \frac { n ( a d - b c ) ^ { 2 } } { ( a + b ) ( c + d ) ( a + c ) ( b + d ) } ,
$$

<html><body><table><tr><td>P(K²k)</td><td>0.050</td><td>0.010</td><td>0.001</td></tr><tr><td>k</td><td>3.841</td><td>6.635</td><td>10.828</td></tr></table></body></html>

A.400 B.300 C.200 D.100

【例2】（多选题）为预防近视，某校对“学生性别和喜欢躺着看书"是否有关做了一次调查，其中被调查的男女生人数相同，男生喜欢躺着看书的人数占男生人数的 2 $\frac { 2 } { 5 }$ ，女生喜欢躺着看书的人数占女生人数的 $\frac { 4 } { 5 }$ 若有 $9 5 \%$ 的把握认为是否喜欢躺着看书和性别有关，则调查人数中男生人数可能是（）

参考公式及数据： $K ^ { 2 } = { \frac { n ( a d - b c ) ^ { 2 } } { \left( a + b \right) \left( c + d \right) \left( a + c \right) \left( b + d \right) } }$ ，其中 $n = a + b + c + d$

<html><body><table><tr><td>附：</td><td>P(K²≥k）</td><td>0.05</td><td>0.010</td></tr><tr><td></td><td>k</td><td>3.841</td><td>6.635</td></tr></table></body></html>

A.8 B.10 C.12 D.14【例3】某棉纺厂为了解一批棉花的质量，在该批棉花中随机抽取了容量为120的样本，测量每个样本棉花的纤维长度（单位： $\mathsf { m m }$ ，纤维长度是棉花质量的重要指标)，所得数据均在区间[20,32]内，将其按组距为2分组，制作成如图所示的频率分布直方图，其中纤维长度不小于 $2 8 \mathsf { m m }$ 的棉花为优质棉.

(1)求频率分布直方图中 $a$ 的值；

(2)已知抽取的容量为120的样本棉花产自于 $A$ ， $B$ 两个试验区，部分数据如下 $2 \times 2$ 列联表：

<html><body><table><tr><td></td><td>A试验区</td><td>B试验区</td><td>合计</td></tr><tr><td>优质棉</td><td>10</td><td></td><td></td></tr><tr><td>非优质棉</td><td></td><td>30</td><td></td></tr><tr><td>合计</td><td></td><td></td><td>120</td></tr></table></body></html>

![](images/406cc9de004eb5f7210ec71ca4f2b3b0b854201d257dc44e42d1343b6af3b2d3.jpg)

将 $2 \times 2$ 列联表补充完整，并判断是否有 $9 9 . 9 \%$ 的把握认为优质棉与A， $B$ 两个试验区有关系；

(3)若从这批120个样本棉花中随机抽取3个，其中有 $X$ 个优质棉，求 $X$ 的分布列和数学期望 $E { \big ( } X { \big ) }$

注： $\textcircled{1}$ 独立性检验的临界值表：

<html><body><table><tr><td>P(x²≥x）</td><td>0.15</td><td>0.10</td><td>0.05</td><td>0.025</td><td>0.010</td><td>0.005</td><td>0.001</td></tr><tr><td>x</td><td>2.072</td><td>2.706</td><td>3.841</td><td>5.024</td><td>6.635</td><td>7.879</td><td>10.828</td></tr></table></body></html>

$\textcircled { 2 } \chi ^ { 2 } = \frac { n \left( a d - b c \right) ^ { 2 } } { \left( a + b \right) \left( c + d \right) \left( a + c \right) \left( b + d \right) }$ 其中 $n = a + b + c + d$ 【例5】某校在高一部分学生中调查男女同学对某项体育运动的喜好情况，其二维条形图如图（黑色代表喜欢，白色代表不喜欢，单位：人）.

人数4403020100 二女 男性别

(1）写出 $2 \times 2$ 列联表；  
(2)依据 $\alpha = 0 . 0 1$ 的独立性检验，分析喜欢这项体育运动是否与性别有关；  
(3)在这次调查中，从喜欢这项体育运动的一名男生和两名女生中任选两人进行专业培训，求恰是一男一女的概率.  
附表及公式：

<html><body><table><tr><td>a</td><td>0.1</td><td>0.05</td><td>0.01</td><td>0.005</td><td>0.001</td></tr><tr><td>Xa</td><td>2.706</td><td>3.841</td><td>6.635</td><td>7.879</td><td>10.828</td></tr></table></body></html>

$\chi ^ { 2 } = \frac { n \left( a d - b c \right) ^ { 2 } } { \left( a + b \right) \left( c + d \right) \left( a + c \right) \left( b + d \right) }$ ，其中 $n = a + b + c + d$

# 选择性必修第三册

【例6】随着电商事业的发展和工作生活节奏的加快，人们的生活方式和生活理念正在发生巨大的改变．通过外卖App下单订餐叫外卖，正受到越来越多的市民尤其是青年上班族的喜爱．为了解市民是否经常利用外卖平台点餐，调查机构借助网络进行了问卷调查，并从参与调查的网友中抽取了75人进行抽样分析，其中经常用外卖平台点餐的人数是基本不用外卖平台点餐的人数的2倍；40岁以上经常用外卖平台点餐的人数和基本不用外卖平台点餐的人数相等；40岁及以下有15人基本不用外卖平台点餐.

（1）请完善下面列联表（单位：人），并依据 $\alpha = 0 . 1$ 的独立性检验，分析经常利用外卖平台点餐是否与年龄有关？

<html><body><table><tr><td></td><td>经常用外卖平台点餐</td><td>基本不用外卖平台点餐</td><td>总计</td></tr><tr><td>40岁及以下</td><td></td><td>15</td><td></td></tr><tr><td>40岁以上</td><td></td><td></td><td></td></tr><tr><td>总计</td><td></td><td></td><td>75</td></tr></table></body></html>

（2）利用分层抽样方法在经常用外卖平台点餐的市民中随机抽取10人，再从以上10人中随机抽取3人．记被抽取的3人中“40岁以上"的人数为 $X$ ，求随机变量 $X$ 的分布列和均值 $E ( X )$ ·

附： $\chi ^ { 2 } = \frac { n ( a d - b c ) ^ { 2 } } { ( a + b ) ( c + d ) ( a + c ) ( b + d ) }$ ，其中 $n = a + b + c + d$ 临界值表：

<html><body><table><tr><td>P(x²≥x)</td><td>0.15</td><td>0.10</td><td>0.05</td><td>0.025</td></tr><tr><td>X</td><td>2.072</td><td>2.706</td><td>3.841</td><td>5.024</td></tr></table></body></html>

# 【题型专练】

1．已知变量 $X , Y$ ，由它们的样本数据计算得到 $K ^ { 2 }$ 的观测值 $k \approx 4 . 3 2 8$ ， $K ^ { 2 }$ 的部分临界值表如下：

<html><body><table><tr><td>p(k²≥k）</td><td>0.10</td><td>0.05</td><td>0.025</td><td>0.010</td><td>0.005</td></tr><tr><td>k</td><td>2.706</td><td>3.841</td><td>5.024</td><td>6.635</td><td>7.879</td></tr></table></body></html>

则最大有 的把握说变量 $X , Y$ 有关系.（填百分数）

2.为增强学生体质，充分展示当代青少年积极健康向上的精神风貌，某学校在校内新开设羽毛球课和健美操课，且每名同学只选一课．为了研究选课是否与性别有关系，现随机抽取了高一年级200名学生选课情况（其中男生120人，女生80人）.

(1)完成下面的 $2 \times 2$ 列联表，判断是否有 $9 9 . 5 \%$ 的把握认为选课与性别有关，并说明理由.

<html><body><table><tr><td></td><td>羽毛球课</td><td>健美操 课</td><td>合计</td></tr><tr><td>男</td><td></td><td></td><td></td></tr><tr><td>女</td><td></td><td>48</td><td></td></tr><tr><td>合计</td><td>112</td><td></td><td></td></tr></table></body></html>

(2)从上述120名男生中按选羽毛球课和选健美操课进行分层抽样，抽取6人，求从这6人中任取2人，至少有1人选择了羽毛球课的概率.

附：

<html><body><table><tr><td>P(K²≥k)</td><td>0.15</td><td>0.10</td><td>0.05</td><td>0.010</td><td>0.005</td><td>0.001</td></tr><tr><td>k</td><td>2.072</td><td>2.706</td><td>3.841</td><td>6.635</td><td>7.879</td><td>10.828</td></tr></table></body></html>

（参考公式： $K ^ { 2 } = \frac { n ( a d - b c ) ^ { 2 } } { ( a + b ) ( c + d ) ( a + c ) ( b + d ) }$ ，其中 $n = a + b + c + d$ ）

3．为迎接 2022年北京冬季奥运会，普及冬奥知识，某校开展了“冰雪答题王"冬奥知识竞赛活动．现从参加冬奥知识竞赛活动的学生中随机抽取100名学生，将他们的竞赛成绩(满分为100分)分为6组：[40,50),[50,60)，[60,70)，[70,80)，[80,90)，[90,100]，得到如图所示的频率分布直方图．

(1)估计这100名学生的平均成绩（同一组中的数据用该组区间的中点值为代表），并估计这100名学生成绩的中位数（精确到0.01）；

(2)在抽取的100名学生中，规定：竞赛成绩不低于80分为“优秀”，竞赛成绩低于80分为“非优秀”.

$\textcircled{1}$ 请将下面的 $2 \times 2$ 列联表补充完整，并判断是否有 $9 9 \%$ 的把握认为"竞赛成绩是否优秀与性别有关”？

![](images/d76b511320c8a0337de7c19dca5a5b0021834ffcd9913377bcb0b4710c9ff5ca.jpg)

$\textcircled{2}$ 求出等高条形图需要的数据，并画出等高条形图（按图中“优秀"和“非优秀"所对应阴影线画），利用条形图判断竞赛成绩优秀与性别是否有关系？

$2 \times 2$ 列联表

<html><body><table><tr><td></td><td>优秀</td><td>非优秀</td><td>合计</td></tr><tr><td>男生</td><td>10</td><td></td><td></td></tr><tr><td>女生</td><td></td><td></td><td>50</td></tr><tr><td>合计</td><td></td><td></td><td>100</td></tr></table></body></html>

![](images/51c0536a97f70713acae4e6fd8604d72027bbb2870877adf97c9ca96ecff3a7d.jpg)

<html><body><table><tr><td>p(K²≥k）</td><td>0.10</td><td>0.05</td><td>0.025</td><td>0.010</td><td>0.005</td><td>0.001</td></tr><tr><td>k</td><td>2.706</td><td>3.841</td><td>5.024</td><td>6.635</td><td>7.879</td><td>10.828</td></tr></table></body></html>

参考公式及数据：

$$
K ^ { 2 } = { \frac { n { \big ( } a d - b c { \big ) } ^ { 2 } } { { \big ( } a + b { \big ) } { \big ( } c + d { \big ) } { \big ( } a + c { \big ) } { \big ( } b + d { \big ) } } } , n = a + b + c + d ,
$$

4．某企业为响应国家在《“十四五"工业绿色发展规划》中提出的“推动绿色发展，促进人与自然和谐共生"的号召，推进产业结构高端化转型，决定开始投入生产某新能源配件.该企业初步用甲、乙两种工艺进行试产，为了解两种工艺生产新能源配件的质量情况，从两种工艺生产的产品中分别随机抽取了100件进行质量检测，得到下图所示的频率分布直方图，规定质量等级包含合格和优等两个等级，综合得分在[90,120)的是合格品，得分在[120,150]的是优等品.

![](images/8f70d939d2ef007e50973d4713c7cf75cd9d1fd13dd54660ff1679c68678770d.jpg)  
甲生产工艺

![](images/33bc75e6ec7bdf3cff458c41474436d898cb71fa6f0cf84bd72762400afa2d48.jpg)  
乙生产工艺

(1)从这100件甲工艺所生产的新能源配件中按质量等级分层抽样抽取5件，再从这5件中随机抽取2件做进一步研究，求恰有1件质量等级为优等品的概率；

(2)根据频率分布直方图完成下面的 $2 \times 2$ 列联表，并判断是否有 $9 5 \%$ 的把握认为新能源配件的质量等级与生产工艺有关？该企业计划大规模生产这种新能源配件，若你是该企业的决策者，你会如何安排生产，为什么？

<html><body><table><tr><td></td><td>合格品</td><td>优等品</td><td>合计</td></tr><tr><td>甲生产工艺</td><td></td><td></td><td></td></tr><tr><td>乙生产工艺</td><td></td><td></td><td></td></tr><tr><td>总计</td><td></td><td></td><td></td></tr></table></body></html>

附： (a+b)(c+d)(a+c)(b+d），其中n=a+b+c+d.

<html><body><table><tr><td>P(K²≥k)</td><td>0.100</td><td>0.050</td><td>0.010</td><td>0.001</td></tr><tr><td>k</td><td>2.706</td><td>3.841</td><td>6.635</td><td>10.828</td></tr></table></body></html>

5．为丰富学生的校园生活，提升学生的实践能力和综合素质能力，培养学生的兴趣爱好，某校计划借课后托管服务平台开设书法兴趣班，为了解学生对这个兴趣班的喜爱情况，该校随机抽取了该校100名学生，调查他们对这个兴趣班的喜爱情况，得到下面的 $2 \times 2$ 列联表：

<html><body><table><tr><td></td><td>喜爱</td><td>不喜爱</td><td>合计</td></tr><tr><td>男</td><td>35</td><td></td><td>60</td></tr><tr><td>女</td><td></td><td></td><td>40</td></tr><tr><td>合计70</td><td></td><td></td><td>100</td></tr></table></body></html>

以调查得到的男、女学生喜欢书法兴趣班的频率代替概率.

（1）完成题中的 $2 \times 2$ 列联表，并判断能否有 $9 9 \%$ 的把握认为是否喜欢书法兴趣班与性别有关；

(2)从该校喜欢书法兴趣班的学生中，用分层抽样的方法抽取6名学生，再从这6名学生中随机抽取2名学生，求这2名学生中至少有1名女学生的概率.

参考公式： $K ^ { 2 } = { \frac { n \left( a d - b c \right) ^ { 2 } } { \left( a + b \right) \left( c + d \right) \left( a + c \right) \left( b + d \right) } }$ 其中 $n = a + b + c + d$

参考数据：

<html><body><table><tr><td>P(K2≥k)</td><td>0.10</td><td>0.05</td><td>0.010</td><td>0.001</td></tr><tr><td>k</td><td>2.706</td><td>3.841</td><td>6.635</td><td>10.828</td></tr></table></body></html>

6.甲、乙两台机床加工同一规格（直径 $2 0 . 0 \mathrm { { m m } }$ ）的机器零件，为了比较这两台机床生产的机器零件精度的差异，随机选取了一个时间段，对该时间段内两台机床生产的所有机器零件直径的大小进行了统计，数据如下：甲：19.7，19.8，19.8，19.9，19.9，19.9，20.0，20.0，20.0，20.0，20.1，20.1，20.1，20.1，20.2，20.2，20.2，20.3  
乙：19.5，19.6，19.7，19.8，19.9，20.0，20.0，20.1，20.1，20.2，20.3，20.4  
规定误差不超过 $0 . 2 \mathrm { m m }$ 的零件为一级品，误差大于 $0 . 2 \mathrm { m m }$ 的零件为二级品.  
附 $K ^ { 2 } = \frac { n ( a d - b c ) ^ { 2 } } { ( a + b ) ( c + d ) ( a + c ) ( b + d ) }$ 其中 $n = a + b + c + d$

<html><body><table><tr><td>P(K²≤k)</td><td>0.100</td><td>0.050</td><td>0.010</td><td>0.005</td><td>0.001</td></tr><tr><td>k</td><td>2.706</td><td>3.841</td><td>6.635</td><td>7.879</td><td>10.828</td></tr></table></body></html>

(1)根据以上数据完成下面的 $2 \times 2$ 列联表，并判断是否有 $9 5 \%$ 的把握认为甲、乙两台机床生产的机器零件的精度存在差异：

<html><body><table><tr><td></td><td>一级品</td><td>二级品</td><td>总计</td></tr><tr><td>甲机床</td><td></td><td></td><td></td></tr><tr><td>乙机床</td><td></td><td></td><td></td></tr><tr><td>总计</td><td></td><td></td><td></td></tr></table></body></html>

(2)以该时间段内两台机床生产的产品的一级品和二级品的频率代替概率，从甲机床生产的零件中任取2个，从乙机床生产的零件中任取3个，比较甲、乙机床取到一级品个数的期望的大小.

7.2022年12月6日中国职业篮球联赛将开始第二阶段比赛，某队为了考察甲球员对篮球队的贡献，通过对甲参加的50场比赛和末参加的50场比赛调查，得到如下等高堆积条形图：

108063488   
0.2   
0.1   
0.0   
甲球员参加比赛甲球员未参加比赛   
□球队胜 球队负

(1)根据等高堆积条形图，填写列联表，并依据 $\alpha = 0 . 0 0 1$ 的独立性检验，分析该球队胜利与甲球员参赛是否有关(2)在训练过程中，甲乙丙三人相互做传球训练已知甲控制球时，传给乙的概率为 $\frac { 3 } { 4 }$ ，传给丙的概率为 $\frac { 1 } { 4 }$ 乙控制球时，传给甲和丙的概率均为 $\frac { 1 } { 2 }$ ；丙控制球时，传给甲的概率为 $\frac { 2 } { 3 }$ ，传给乙的概率为 $\frac 1 3$ 若先由甲控制球，经过3次传球后，球员乙控制球的次数为 $X$ ，求 $X$ 的分布列与期望 $E { \big ( } X { \big ) }$

<html><body><table><tr><td></td><td>甲参加比赛</td><td>甲末参加比赛</td><td>合计</td></tr><tr><td>球队胜</td><td></td><td></td><td></td></tr><tr><td>球队负</td><td></td><td></td><td></td></tr><tr><td>合计</td><td></td><td></td><td></td></tr></table></body></html>

附表及公式：

<html><body><table><tr><td>a</td><td>0.15</td><td>0.10</td><td>0.05</td><td>0.025</td><td>0.010</td><td>0.005</td><td>0.001</td></tr><tr><td>X</td><td>2.072</td><td>2.706</td><td>3.841</td><td>5.024</td><td>6.635</td><td>7.879</td><td>10.828</td></tr></table></body></html>

$$
\chi ^ { 2 } = \frac { n ( a d - b c ) ^ { 2 } } { \big ( a + b \big ) \big ( c + d \big ) \big ( a + c \big ) \big ( b + d \big ) } .
$$

8.北京时间2022年4月16日09时56分，神舟十三号载人飞船返回舱在东风着陆场成功着陆，神舟十三号载人飞行任务取得成圆满成功.某校为了解本校学生对此新闻事件的关注度，从本校学生中随机抽取了200名学生进行调查，调查样本中有80名女生.根据样本的调查结果绘制成如图所示的等高堆积条形图.

<html><body><table><tr><td></td><td>关注</td><td>不关注</td><td>合计</td></tr><tr><td>男生</td><td></td><td></td><td></td></tr><tr><td>女生</td><td></td><td></td><td></td></tr><tr><td>合计</td><td></td><td></td><td></td></tr></table></body></html>

![](images/4c2e671ef306d58713722a6a3bf1d4a56673828d4968bb2922e3858a3e4f84e2.jpg)

(1)完成上面的 $2 \times 2$ 列联表，并判断能否有 $9 9 . 9 \%$ 的把握认为学生是否关注“神州十三号飞船成功着陆”新闻事件与性别有关.

(2)从这200名学生里对“神州十三号飞船成功着陆"新闻事件不关注的学生中，按性别采用分层抽样的方法抽取6名学生，再从这6名学生中随机选取2人参与该新闻事件的学习.求这2名学生不全是男生的概率.

附： $K ^ { 2 } = \frac { n ( a d - b c ) ^ { 2 } } { ( a + b ) ( c + d ) ( a + c ) ( b + d ) }$ 其中 $n = a + b + c + d$

<html><body><table><tr><td>P(K²k）</td><td>0.050</td><td>0.010</td><td>0.005</td><td>0.001</td></tr><tr><td>k</td><td>3.841</td><td>6.635</td><td>7.879</td><td>10.828</td></tr></table></body></html>