---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 24
source_file: L5-3-不等式.md
title: L5-3-不等式
type: problem_type
---

# 不等式

一本一讲，共26讲（建议5个月学习）

11个考点+11个题型+28个视频

每周一讲（建议学习时间90分钟）

![](images/0388bbdb1a3b50f5cb8998441f1e2e46556f816f33f86c96d409694c9c560352.jpg)

# 视频内容研发团队

学而思优秀老师

学而思优秀老师和一线高级教师联合创作本书试题，并精心录制讲解视频学而思图书APP扫码即可观看

![](images/534a219f54b8dc13b2483db755639ac3cb414e7883d6b48ae2764ad9352b9137.jpg)

# 傅博宇 老师

毕业于北京大学元培学院  
网校和北大数院高考数学研究联合课题组成员；  
网校高中创新产品部负责人；  
荣获学而思网校“桃李满天下奖”“出类拔萃奖”等；腾讯网中国好老师；  
青少年教育导师认证；  
科学家长观体系的创立者

![](images/5c83fddf84ac25765856d9c696ce2be34a34dcd63352d679016e8b433e9f8447.jpg)

# 王侃老师

毕业于北京大学数学系  
学而思网校高中数学教研奠基人；  
学而思网校高中数学S级教师；  
荣获学而思网校“突出贡献奖” “桃李天下奖”等；擅长总结题型特点，提炼思想方法；  
擅长分层教学，因材施教

![](images/c848d215f7cfe0629ff7feae9af7967c4d2ef154b92caec959d9197bf9d61b66.jpg)

# 付恒岩 老师

毕业于大连理工大学  
网校高中部理科主讲岗后培训师；  
2020年荣获学而思网校最具魅力奖；  
2019年、2020年荣获学而思网校诲人不倦奖；2020年荣获学而思网校高考优秀评卷人；  
2021年担任新浪教育高考数学直播解析特邀嘉宾；“停课不停学”公益课高中数学主讲老师

![](images/df5d1aa15c5a40410f6b2dd8f790e1a2793c18b1d3ac3cf55d89982cc3ab6422.jpg)

# 武洪姣 老师

14年线上线下教学经验；  
学而思网校高中理科教研负责人；  
学而思高中数学特级教师；  
在教学的过程中擅长归纳题型，方法和技巧；  
在高中数学模块中最擅长讲解圆锥曲线和导数；  
无论你是从小数学不好，还是数学一直拔尖，都可以在武老师的课堂上收获很多

5级

# 不等式

一本一讲，共26讲（建议5个月学习）

11个考点 $+ 1 1$ 个题型 $+ 2 8$ 个视频

每周一讲（建议学习时间90分钟）

# 高维数学 5

![](images/f820c628436013235632d3a729d20381ae034d369c4a2e82788e71f81e37abe1.jpg)

# 提升篇

第1讲集合、逻辑、函数初步第2讲基本初等函数与函数 冲刺篇的性质第11讲函数性质的应用第3讲不等式第12讲函数零点与恒成立问题第4讲导数（一）第13讲导数的应用一单调性与极值第5讲导数（二）最值第6讲三角函数第14讲导数的应用一恒成立问题第7讲平面向量与解三角形第15讲导数的应用一零点个数与第8讲数列（一）隐零点问题第9讲立体几何第16讲导数的应用一极值点偏移第10讲解析几何第17讲三角函数与平面向量6第18讲解三角形第19讲数列（二）第20讲三视图与球的切接问题  
模块1 不等式 2 第21讲空间中的角  
模块2 基本不等式 9 第22讲统计概率  
模块3 线性规划 16第23讲圆锥曲线的几何性质第24讲几何条件的代数转化第25讲圆锥曲线中的最值范围问题第26讲定点定值问题与轨迹方程问是参考答案

# 不等式

# 直击课堂

<html><body><table><tr><td>知识模块</td><td>考点</td><td>对应例题</td><td>考查概率</td></tr><tr><td>不等式</td><td>不等式的性质 一元二次不等式解法 含参一元二次不等式 分式不等式与绝对值不等式</td><td>例1 例2 例3 例4</td><td>★★★★★ ★★★★★ ★★★★★</td></tr><tr><td>基本不等式</td><td>基本不等式易错点辨析 基本不等式综合</td><td>例6 例7 例8</td><td>★★★★★ ★★★☆☆ ★★★☆☆</td></tr><tr><td>线性规划</td><td>简单线性规划 线性规划常见变形</td><td>例10 例11</td><td>★★★★★ ★★★☆☆</td></tr></table></body></html>

# 本讲解读

不等式是高中数学阶段的重要板块，在高考试卷中涉及的考点也很多，从全国卷目前的高中数学要求来说,除了不等式证明以外，其他形式的考查还是很多的.从题型上来说,包含：线性规划、基本不等式、解不等式、不等式恒(能)成立,还有一些转化为不等式问题的题型.本讲将围绕“不等关系与解不等式”“基本不等式”“线性规划”三个模块进行展开，具体的考点考法将在本讲的后续内容中进行详细阐述.

# 模块1不等式

# APP扫码观看本模块讲解视频

D知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# 不等式的性质

![](images/4624a22add0ccb8bf1379da54b48a6edc32e042a7171bfb1fcbc9e94b44200e8.jpg)

# 重点笔记

<html><body><table><tr><td>性质名称</td><td>性质内容</td><td>注意</td></tr><tr><td>对称性</td><td>a&gt;bb&lt;a</td><td>可逆</td></tr><tr><td>传递性</td><td>a&gt;b,b&gt;c=a&gt;c</td><td>同向</td></tr><tr><td>可加性</td><td>a&gt;ba+c&gt;b+c</td><td>可逆</td></tr><tr><td>可乘性</td><td>a&gt;b,c&gt;0=→ac&gt;bc; a&gt;b,c&lt;0=ac&lt;bc</td><td>符号</td></tr><tr><td>移项法则</td><td>a+b&gt;ca&gt;c-b</td><td>可逆</td></tr><tr><td>同向可加性</td><td>a&gt;b,c&gt;d=→a+c&gt;b+d</td><td>同向</td></tr><tr><td>同向同正可乘性</td><td>a&gt;b&gt;0,c&gt;d&gt;0=ac&gt;bd</td><td>同向，同正</td></tr><tr><td>乘方开方法则</td><td>a&gt;b&gt;0=a&gt;b且a&gt;b (n∈N,n≥2）</td><td>同正</td></tr><tr><td>倒数法则</td><td>ab&gt;0,a&gt;b= 1 b a 1</td><td>同号</td></tr></table></body></html>

# ②比较大小的方法

（1)利用不等式的性质.(2）作差法： $a - b > 0 \Longleftrightarrow a > b ; a - b = 0 \Longleftrightarrow a = b ; a - b < 0 \Longleftrightarrow a < b .$ (3)作商法：若 $a > 0 , b > 0$ 则 $\frac { a } { b } > 1 \Longleftrightarrow a > b ; \frac { a } { b } = 1 \Longleftrightarrow a = b ; \frac { a } { b } < 1 \Longleftrightarrow$ $a < b$

# 精讲精练

# 拍照批改秒判对错

# 考点1：不等式的性质

# 例1

(1)若 $a > b > 0 , c < d < 0$ ，则一定有（

$\mathrm { A . } \ \frac { a } { c } > \frac { b } { d }$ $\mathrm { B } . \frac { a } { c } < \frac { b } { d }$ $\therefore { \frac { a } { d } } > { \frac { b } { c } }$ $\mathrm { D . } \frac { a } { d } < \frac { b } { c }$

# 学习笔记

(2）若 $a > b > 1 , 0 < c < 1$ ，则（ ）

A. $a ^ { c } < b ^ { c }$ B. $a b ^ { c } < b a ^ { c }$ $\therefore a \log _ { b } c < b \log _ { a } c$ D $\cdot \log _ { a } c < \log _ { b } c$

# 学习笔记

# 知识点睛

# $\textcircled{8}$ 解一元二次不等式

（1)把二次项的系数变为正；(2)解对应的一元二次方程（先看能否因式分解，若不能，再看 $\Delta$ ，最后求根)；(3)求解一元二次不等式(根据一元二次方程的根及不等号的方向).

# $\textcircled { 4 }$ 解高次不等式

（1)首先应该将式子因式分解成多个因式的积，且将 $x$ 的系数全部化为正数；(2）将各个因式等于0时的 $x$ 值标到数轴上，俗称“标根”；(3)穿线，一般从右上方开始，向下向左依次穿过各个点（因式指数为偶数时，该点不穿）；（4)数轴上方曲线对应区域使“ $> 0$ ”成立，下方曲线对应区域使“ $< 0$ ”成立.

# $\textcircled{5}$ 解分式不等式

对于含有分式的不等式解法思路：  
先将不等式整理为f（x） $| \frac { f ( x ) } { g ( x ) } > 0 \rangle$ 或（x） $\displaystyle \frac { f ( \boldsymbol { x } ) } { \boldsymbol { g } ( \boldsymbol { x } ) } \geq 0$ ,再化为整式不等式求解.X  
${ \frac { f ( x ) } { g ( x ) } } > 0 { \Longleftrightarrow } f ( x ) g ( x ) > 0 , { \frac { f ( x ) } { g ( x ) } } \geqslant 0 { \Longleftrightarrow } { \left\{ \right.} { f ( x ) g ( x ) \geqslant 0 , }  $

# $\textcircled{6}$ 解指数不等式、对数不等式

常见的两种指对不等式：

$( 1 ) a ^ { f ( x ) } > a ^ { g ( x ) }$ ：当 $a > 1$ 时，转化为 $f ( x ) > g ( x )$ ；当 $0 < a < 1$ 时，转化为 $f ( x ) < g ( x )$ $\log _ { a } f ( x ) > \log _ { a } g ( x )$ ：当 $a > 1$ 时，转化为 $\left\{ \begin{array} { l } { { \displaystyle g ( \boldsymbol { x } ) > 0 } , } \\ { { \displaystyle f ( \boldsymbol { x } ) > g ( \boldsymbol { x } ) } } \end{array} \right.$ 当 $0 < a < 1$ 时，转化为 $\begin{array} { l } { f ( x ) > 0 , } \\ { f ( x ) < g ( x ) . } \end{array}$ (2)与指数(或对数)复合的二次不等式（二次在外，指对在内）：通过整体换元，先解外层的一元二次不等式，再解内层不等式.

# 精讲精练

# 拍照批改秒判对错

# 考点2：一元二次不等式解法

# 例2

求下列一元二次不等式的解集.$( 1 ) x ^ { 2 } - 3 x - 1 0 \geqslant 0 ;$   
(2） $- 2 x ^ { 2 } - 3 x + 2 > 0 .$

# 学习笔记

# 考点3：含参一元二次不等式

# 例3

（1）设 $a < - 1$ ，则关于 $x$ 的不等式 $a ( x - a ) \left( x - { \frac { 1 } { a } } \right) < 0$ 的解集为(

A $\left\{ x \middle \vert x < a \right.$ $x > \frac { 1 } { a } \bigg \{$ B.{x|x>a} C $\left\{ x { \overset { \vartriangle } { \right. } } x > a$ $x < { \frac { 1 } { a } } \bigg \}$ （204 $\mathrm { D } . \left\{ x { \Bigg | } x < { \frac { 1 } { a } } \right\}$

# 学习笔记

(2)求关于 $x$ 的不等式 $a x ^ { 2 } - 2 ( a + 1 ) x + 4 > 0$ 的解集.

# 学习笔记

(3）解关于 $x$ 的不等式 $a x ^ { 2 } + 2 x + 1 > 0 ( a \in \mathbf { R } )$ ：

# 学习笔记

# 考点4：分式不等式与绝对值不等式

# 例4

（1）不等式-3 $\cdot \frac { x - 3 } { x + 1 } \geq 0$ 的解集为(

A. $\{ x \mid x \geqslant 3$ 或 $x < - 1 \}$ B. $\{ x \mid - 1 \leqslant x \leqslant 3 \}$ C. $\{ \ x \ | x \geqslant 3$ 或 $x \leqslant - 1 \ \}$ D. $\left\{ { \boldsymbol { x } } \ \lvert { \boldsymbol { x } } \leqslant - 3 \right.$ 或 $x > 1$

# 学习笔记

(2)不等式 $\left| { \frac { x + 1 } { x - 1 } } \right| < 1$ 的解集为(

A. $\{ x \ | 0 < x < 1 \} \cup \{ x \ | x > 1 \}$ B. $\{ x \ | 0 < x < 1 \}$ C. $\{ x \mid - 1 < x < 0 \}$ D. $\{ x \mid x < 0 \}$ （204

# 学习笔记

# 考点5：指对不等式

# 例5

解下列不等式：$\begin{array} { l } { { ( 1 ) 3 ^ { x ^ { + 1 } } - 9 ^ { x } > 2 ; } } \\ { { ( 2 ) \log _ { 2 } ( 4 + 3 x - x ^ { 2 } ) > \log _ { 2 } ( 2 x - 1 ) + 1 . } } \end{array}$

# 学习笔记

# 模块2 基本不等式

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# $\textcircled { 1 }$ 基本不等式

（1）如果ab∈R+（R\*表示正实数），那么a+b≥ ${ \frac { a + b } { 2 } } \geq { \sqrt { a b } }$ ，当且仅当 $a = b$ 时,式中等号成立.  
此不等式称为基本不等式或均值不等式.

(2) ${ \frac { a + b } { 2 } } { \vert { \frac { a } { \vert { \vert { \Lambda } } \vert } } }$ 叫做 $a , b$ 的算术平均值, $\sqrt { a b }$ 叫做 $a , b$ 的几何平均值,故基本不等式可表述为;两个正实数的算术平均值大于或等于它们的几何平均值.

# 注意

$\textcircled{1}$ 基本不等式成立的前提条件是 $a , b \in \mathbb { R } ^ { + }$ $\textcircled{2}$ 等号成立：当且仅当 $a = b$ ，即 $a = b$ 时，等号成立；反之若等号成立，则必有 $a = b$

# 重点笔记

# ②最值定理

两个正数的乘积为常数，则两数相等时，它们的和取得最小值；两个正数的和为常数，则两数相等时，它们的乘积取得最大值.即已知 $a , b \in \mathbb { R } ^ { + }$ ，

（1）如果乘积ab为定值P，那么当 $a$ 三 $b$ 时，和 $a + b$ 有最小值2√P；(2）如果和 $a + b$ 为定值S，那么当 $a = b$ 时，乘积ab有最大值 $\cdot \frac { 1 } { 4 } S ^ { 2 }$

# $\textcircled{6}$ 利用基本不等式求最值的条件

利用基本不等式求最值必须满足三个条件，即“一正二定三相等”.

（1)“一正”：即求最值的两项必须都是正数.如 $x + { \frac { 1 } { x } } \geq 2$ 当且仅当 $x > 0$ 时才成立,而当 $x < 0$ 时 $x + { \frac { 1 } { x } } = - \left[ \left( \begin{array} { l } { - x } \end{array} \right) + \left( \begin{array} { l } { - { \frac { 1 } { x } } } \end{array} \right) \right] \leqslant - 2 .$ （2）“二定”：要求 $a + b$ 的最小值，则 $a b$ 须是定值；要求 $a b$ 的最大值，则 $a + b$ 须是定值.若题中本身没有明显的定值条件，则往往需要对代数式作适当的变形来“凑定值”.（3）“三相等”；只有具备了不等式中等号成立的条件，才能使式子取到最大或最小值.如要求 $\sqrt { x ^ { 2 } + 2 } + \frac { 1 } { \sqrt { x ^ { 2 } + 2 } }$ 的最小值,利用基本不等式： $\sqrt { x ^ { 2 } + 2 } + \frac { 1 } { \sqrt { x ^ { 2 } + 2 } } \geq 2$ ，但要取等号必须满足 ${ \sqrt { x ^ { 2 } + 2 } } = { \frac { 1 } { \sqrt { x ^ { 2 } + 2 } } }$ ，即 $x ^ { 2 } + 2 = 1$ ,这是不可能的,所以其最小值不是2.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点6：常规基本不等式问题

# 例6

(1）若 $x > 0$ ，则 $6 x + 1 + { \frac { 1 } { 2 x } }$ 的最小值为(

A.23 B.2√3+1 C.5 D.3+3

# 学习笔记

(2）若 $0 < x < \frac { 3 } { 2 }$ ，则 $x ( 3 - 2 x )$ 的最大值为(

A.1 $\mathrm { B } . \frac { 9 } { 8 }$ C $\frac { 5 } { 4 }$ D.

# 学习笔记

(3）若 $a > 0 , b > 0 , 2 a + b = 2$ ，则 $a b$ 的最大值为(

A $\frac 1 2$ $\mathrm { B } . \frac { 2 } { 3 }$ $\mathrm { C . \frac { 4 } { 9 } }$ D.

# 学习笔记

# 考点7：基本不等式易错点辩析

# 例7

(1）当 $x < 0$ 时 $f ( x ) = - x - { \frac { 9 } { x } }$ 的最小值是

# 学习笔记

(2）在下列函数中，最小值是2的是（ ）

A $y = { \frac { x } { 2 } } + { \frac { 2 } { x } }$ $\mathrm { B } . \ y = { \frac { x + 2 } { \sqrt { x + 1 } } } ( x > 0 )$ $\mathrm { C . ~ } y = \sin \ x + \frac { 1 } { \sin \ x } \bigg ( 0 < x < \frac { \pi } { 2 } \bigg )$ $\mathrm { D } . \mathrm { \Delta } y = 7 ^ { x } + 7 ^ { - x }$

# 学习笔记

# 考点8：基本不等式综合

# 例8

(1)已知实数 $x , y$ 满足 $y = 2 ^ { 2 } - \log _ { 2 } x$ 则， ${ \frac { 2 } { x } } + { \frac { 1 } { y } }$ 的最小值为

# 学习笔记

（2）设 $a , b \in \mathbb { R } ^ { + }$ ,则下列不等式中不成立的是(

A $( a + b ) \left( { \frac { 1 } { a } } + { \frac { 1 } { b } } \right) \geq 4$ $\mathrm { B } . \frac { a ^ { 2 } + b ^ { 2 } } { \sqrt { a b } } \geq 2 \ \sqrt { a b }$ C. $\sqrt { a b } + \frac { 1 } { \sqrt { a b } } \geq 2$ $\operatorname { D } . { \frac { 2 a b } { a + b } } \geq { \sqrt { a b } }$

# 学习笔记

# 知识点睛

# $\textcircled { 4 }$ 基本不等式的常见变形

(1)凑定值：利用均值不等式求两个正数和的最小值时，关键在于构造条件，使其积为常数.通常要通过添加常数、凑分母等方式进行构造；利用均值不等式求几个正数积的最大值，关键在于构造条件，调整系数，使其和为常数.通常要通过乘或除以常数、平方等方式进行构造.

（2)二次分式函数：对于二次分式函数f（x）（即分子、分母的最高次数为二次的分式函数)，可以经过适当的变形（分离常数 $^ +$ 换元)，把 $f ( x )$ 最终化为 $g \left( x \right) + \frac { a } { g \left( x \right) } + b$ 的形式，进而利用均值不等式求函数的最值.

（3）“1”的代换：设 $a , b , c , d , e$ 为正实数， $x , y > 0$ ，则对于以下两类问题：已知 $a x + b y = c$ ，求 $\frac { d } { x } + \frac { e } { y }$ 的最小值；或者已知 $\frac { d } { x } + \frac { e } { y } = c$ 求 $a x + b y$ 的最小值我们都可以转化为求 ${ \frac { 1 } { c } } \left( a x + b y \right) \left( { \frac { d } { x } } + { \frac { e } { y } } \right)$ 的最小值

(4)建立不等式求最值：若题目中给出的条件是关于和与积的一个等式，则需要用均值不等式将已知条件转化为关于目标式的不等式，由不等式的解集来确定要求的最值.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点9：基本不等式的常见变式

# 例9

$( 1 ) y = 3 x ^ { 2 } + \frac { 1 6 } { 2 + x ^ { 2 } }$ 的最小值为(

$\mathrm { A } . 8 - 2 { \sqrt { 2 } }$ B.8 C.83 D.83-6

（2)已知b<a<0,且ab=1,则² ${ \frac { a ^ { 2 } + b ^ { 2 } } { a - b } } .$ 取得最小值时， $a + b$ 等于（

A. $- { \sqrt { 1 0 } }$ B. $- \sqrt { 6 }$ C. $- \sqrt { 3 }$ D. $- { \sqrt { 2 } }$

（3）若 $x > 0 , y > 0$ 且 ${ \frac { 1 } { x } } + { \frac { 9 } { y } } = 1$ ，则 $x + y$ 的最小值为(

A.6 B.12 C.16 D.24

(4)若正数 $x , y$ 满足 $4 x ^ { 2 } + 9 y ^ { 2 } + 3 x y = 3 0$ ，则 $x y$ 的最大值是(

A $\frac { 4 } { 3 }$ B $\frac { 5 } { 3 }$ C.2 D.4

# 学习笔记

# 模块3线性规划

# APP扫码观看本模块讲解视频

D知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# $\textcircled { 1 }$ 线性规划相关概念

(1)约束条件：变量 $x _ { \cdot } y$ 要满足的条件，如果约束条件是关于变量的一次不等式组或方程，约束条件称为线性约束条件.

（2）目标函数：在约束条件下要求最值的函数 $z = f ( \boldsymbol { x } , \boldsymbol { y } )$ ，若 $z = a x$ $+ b y$ ，目标函数称为线性目标函数.

(3)线性规划：在线性约束条件下求线性目标函数最值的问题.

（4)可行解：满足约束条件的解 $( x , y )$ ：

(5)可行域：所有可行解组成的集合.

(6)最优解：目标函数取到最大(小)值的可行解.

# ②二元一次不等式组表示平面区域

（1)以线定界：画出不等式对应方程所表示的直线，若原不等式可  
以取等则画实线，否则画虚线；(2)以点定域：将直线一侧一个位置明显的特殊点坐标代入不等  
式，若不等式成立，则此点所在的区域为不等式表示的区域，否则直线  
另一侧为不等式表示的区域;常用的特殊点有 $( 0 , 0 ) , ( 0 , \pm 1 ) , ( \pm 1 ,$   
0）等；(3)区域交集：取各个区域的公共部分，即为线性规划的可行域.

# $\textcircled{6}$ 简单线性规划问题的解法

在确定线性约束条件和线性目标函数 $z = a x + b y$ 的前提下,用图解法求最优解的步骤可概括为“画、移、求”，即：

(1)作图：在平面直角坐标系中画出可行域和直线 $a x + b y = 0$ (2)平移：平移直线 $a x + b y = 0$ ,确定使 $z = a x + b y$ 取得最大值或最小值的点；(3)求值：求出取得 $z$ 最大值或最小值的点的坐标,代入求得对应的最值.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点10：简单线性规划

# 例10

(1)若𝑥,y满足约束条件{𝑥+y-2≤0,则z=3x-4y 的最小值 $\left\{ { \begin{array} { l } { x - y \geqslant 0 , } \\ { x + y - 2 \leqslant 0 } \\ { y \geqslant 0 , } \end{array} } \right.$ 为

# 学习笔记

(2）若 $x , y$ 满足 $x + 1 \leqslant y \leqslant 2 x$ ，则 $2 y - x$ 的最小值是

# 学习笔记

# 考点11：线性规划常见变形

# 例11

(1)已知 $x , y$ 满足约束条件 $\begin{array}{c} \{ { { x - y - 2 \leqslant 0 } , }  \nonumber  \\ { { { x + 2 y - 5 \geqslant 0 } } } \\ { { { y - 2 \leqslant 0 } , } } \end{array} $ ，则 $z = { \frac { y + 1 } { x + 1 } }$ 的取值范围是（）

$$
r _ { \cdot } , 2 \Bigg ] \mathrm { B } _ { \cdot } \left[ - \frac { 1 } { 2 } , \frac { 1 } { 2 } \right] \mathrm { C } _ { \cdot } \left[ \frac { 1 } { 2 } , \frac { 3 } { 2 } \right] \mathrm { D } _ { \cdot } \left[ \frac { 3 } { 2 } , \frac { 5 } { 2 } \right]
$$

# 学习笔记

(2)设变量 $x , y$ 满足不等式组 $\left\{ { \begin{array} { l } { x + y \geqslant 3 , } \\ { x - y \geqslant - 1 } \\ { 2 x - y \leqslant 3 , } \end{array} } \right.$ ，则 $x ^ { 2 } + y ^ { 2 }$ 的最小值是(

A $\frac { 3 { \sqrt { 2 } } } { 2 }$ B. C. $\sqrt { 5 }$ D.5

# 学习笔记

（3）已知 $a > 0 , x , y$ 满足约束条件 $\left\{ \begin{array} { l } { { x \geqslant 1 , } } \\ { { } } \\ { { x + y \leqslant 3 , } } \\ { { } } \\ { { y \geqslant a ( x - 3 ) } } \end{array} \right.$ 若 $z = 2 x + y$ 的最小值为）

1,求 $a$ 的值.

#

![](images/10c601b94bbdd86db6beedc1aafcf87c23fb95f3e871fde25ebd4520f7d35e6a.jpg)

# 自我测试

# 拍照批改秒判对错

# 测试1

对于实数 $a , b , c$ ，有以下命题：

$\textcircled{1}$ 若 $a > b$ ，则 $a c < b c$ $\textcircled{2}$ 若 $a c ^ { 2 } > b c ^ { 2 }$ ，则 $a > b$ $\textcircled{3}$ 若 $a < b < 0$ ，则 $a ^ { 2 } > a b > b ^ { 2 }$ $\textcircled{4}$ 若 $a > b , { \frac { 1 } { a } } > { \frac { 1 } { b } }$ 则 $a > 0 , b < 0$ 其中正确的个数是（ ）

A.1 B.2 C.3 D.4

# 测试2

一元二次不等式 $- x ^ { 2 } + x + 2 > 0$ 的解集是(

A. $\{ x \mid x < - 1$ 或 $x > 2 \}$ B. $\{ x \mid x < - 2$ 或 $x > 1 \nmid$ C. $\{ x \mid - 1 < x < 2 \}$ D. $\{ x \mid - 2 < x < 1 \}$

# 测试3

不等式x<x+2 的解集是(

A. $( \ - 1 , 0 ) \cup ( 2 , + \infty )$ B. $( \mathbf { \Sigma } - \infty , - 1 ) \cup ( 0 , 2 )$ （204 C. $( \ - 2 , 0 ) \cup ( 1 , + \infty )$ D. $( \mathbf { \Sigma } - \infty , - 2 ) \cup ( 0 , 1 )$

# 测试4

下列函数：

$$
\begin{array} { l } { { \displaystyle \bigoplus _ { \mathcal { Y } } = x + \frac { 1 } { x } ( x \geqslant 2 ) \ : ; } } \\ { { \displaystyle \bigoplus _ { \mathcal { Y } } = \tan \ : x + \frac { 1 } { \tan \ : x } \ : ; } } \\ { { \displaystyle \bigoplus _ { \mathcal { Y } } = x - 3 \ : + \frac { 1 } { x - 3 } \ : ; } } \\ { { \displaystyle \bigoplus _ { \mathcal { Y } } = \sqrt { x ^ { 2 } + 2 } \ : + \frac { 1 } { \sqrt { x ^ { 2 } + 2 } } \ : , } } \end{array}
$$

其中最小值为2的有( ）

A.0个 B.1个 C.2个 D.3个

# 学而思秘籍系列图书|数学

# 思维培养

# 思维提升

# 思维突破

![](images/96193a62e742afcaf6955cf4b1d85eae76e83396c91930c2f8e1538c81a01688.jpg)

# 小学秘籍系列

学而思积淀近20年教研经验，培养受益一生的能力。

![](images/52130642d32d3c400e96374807944d7cb1eff30417d9f74466a0bf9a72476955.jpg)

# 初中秘籍系列

全面覆盖初中基础知识和重难点，帮助学生夯实基础，拓展认知。

![](images/d470a2426471f54a3d967b15d5b614404c98d9987ed5849c4b7184d28dd53c5a.jpg)

# 高中秘籍系列

全面覆盖高中基础知识和重难点，帮助学生提升能力，突破思维。

# 学而思秘籍系列图书|语文

# 提升素养

能力训练

![](images/9e55cea8edd8b3e34bddc23f3471389b39549e32272fc997b346c8532e358495.jpg)

# 小学秘籍系列

5大模块 $^ +$ 2条主线，能力与素养双向提升。

![](images/85bb14602a01ea05b7a6b18a65b919ad7a47ce0f2ff30c5fc92c91ebf22c4add.jpg)

# 初中秘籍系列

融合课改四大核心素养，培养爱阅读、 善写作、勤思考、会学习的学生。

# 创新体系|真题研习

![](images/3d6b698674b19cdc3c2821afa8790b49be95f23d15c9a9425670fb8a23a55218.jpg)

# 思维创新大通关数学

攻克数学思维难题，通向理想中学。

# 大家一起来“升级”

# 参与方式

您在使用本书时，如有任何疑问或对图书有任何建议，请扫码进行反馈，并查看反馈采纳结果。

# 奖励

您的反馈一经采纳，我们将会送出总价值35元的图书抵扣券（相同内容的反馈，依据反馈时间，奖励前三位）。请扫码关注公众号，并在对话框中发送反馈时填写的手机号，领取抵扣券。

![](images/d62d323428e63f77eb7626fa4912e1ae54014513b99ecbf2112fc7ee3844e830.jpg)

# 合理规划学习时间

![](images/0cc49af777b160706a89c82837fbe8831b056a08163ce350eae494d8162b749e.jpg)

1 先自己定一个目标，即制定半年学习规划。再将目标细化到每一周，每周学习一本（平均5个考点）。  
3 配套课堂巩固的练习，让学习更有效！

![](images/02782463919382f8d98a13ea356b1324eacfe67431663015cbf25fba5d260bf2.jpg)  
·共6级·每级17-26讲