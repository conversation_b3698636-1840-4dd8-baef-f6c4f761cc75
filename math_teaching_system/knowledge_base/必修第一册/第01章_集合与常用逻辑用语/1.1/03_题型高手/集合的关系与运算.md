---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 18
source_file: L1-2-集合的关系与运算.md
title: L1-2-集合的关系与运算
type: problem_type
---

# 集合的关系与运算

一本一讲，共26讲（建议5个月学习）

6个知识点+7个题型+29个视频

每周一讲（建议学习时间90分钟）

![](images/fc82cca69317b6ae8f2c3cadaa35ba23a1c8cf0f9c02689535a636f27cb628e4.jpg)

# 视频内容研发团队

学而思优秀老师

学而思优秀老师和一线高级教师联合创作本书试题，并精心录制讲解视频学而思图书APP扫码即可观看

# 傅博宇 老师

![](images/19c3dadf657ce4228796d1c4eb83fe5346144eb309c666043a353dff4d8d0fa0.jpg)

毕业于北京大学元培学院  
网校和北大数院高考数学研究联合课题组成员；  
网校高中创新产品部负责人；  
荣获学而思网校“桃李满天下奖”“出类拔萃奖”等；腾讯网中国好老师；  
青少年教育导师认证；  
科学家长观体系的创立者

# 王侃老师

![](images/0b6dfbc5bbe1dfc0442863427099069374aaa81a73a659b9f5e89669ab722cdd.jpg)

毕业于北京大学数学系  
学而思网校高中数学教研奠基人；  
学而思网校高中数学S级教师；  
荣获学而思网校“突出贡献奖” “桃李天下奖”等；擅长总结题型特点，提炼思想方法；  
擅长分层教学，因材施教

# 付恒岩 老师

![](images/dcea33522407918f0172444decb7dbb10dd84ef575374a592bbe05c9db4f27ce.jpg)

毕业于大连理工大学  
网校高中部理科主讲岗后培训师；  
2020年荣获学而思网校最具魅力奖；  
2019年、2020年荣获学而思网校诲人不倦奖；2020年荣获学而思网校高考优秀评卷人；  
2021年担任新浪教育高考数学直播解析特邀嘉宾；“停课不停学”公益课高中数学主讲老师

# 武洪姣 老师

![](images/7f2006639f846f56874e2fdf328e9ebe4f6b29bc9ab0747a0b0277297725b386.jpg)

14年线上线下教学经验；  
学而思网校高中理科教研负责人；  
学而思高中数学特级教师;  
在教学的过程中擅长归纳题型，方法和技巧;  
在高中数学模块中最擅长讲解圆锥曲线和导数；  
无论你是从小数学不好，还是数学一直拔尖，都可以在武老师的课堂上收获很多

1级

# 集合的关系与运算

一本一讲，共26讲（建议5个月学习）

6个知识点 $+ 7$ 个题型 $+ 2 9$ 个视频每周一讲（建议学习时间90分钟）

210y9-eln+31²  
= 2×2-e°+(8§)²  
二 4-1+4  
=7

# 预习篇

第1讲集合的概念与表示 提升篇第 $^ 2$ 讲集合的关系与运算·第11讲集合重难点专题第3讲解不等式 第12讲常用逻辑用语第4讲函数的概念与三要素 第13讲基本不等式第5讲函数的单调性（一） 第14讲函数三要素专题第6讲函数的奇偶性（一） 第15讲函数的单调性（二）第7讲指数幂运算与幂函数 第16讲函数的奇偶性（二）第8讲指数函数 第17讲抽象函数第9讲对数运算 第18讲指数幂运算与指数函数第10讲对数函数 第19讲对数运算与对数函数第20讲函数与方程第21讲恒成立与存在性问题第22讲三角函数基本概念与诱导公式第23讲三角函数的图象与性质  
模块1 集合的关系 2 第24讲三角公式的应用技巧  
模块2 集合的运算 11第25讲三角恒等变换重点题型第26讲正弦型函数的图象与性质参考答案

# 集合的关系与运算

# 直击课堂

<html><body><table><tr><td>知识模块</td><td>知识点</td><td>对应例题</td><td>星标统计</td></tr><tr><td rowspan="3">集合的关系</td><td>子集</td><td>例1</td><td rowspan="9">★7道题</td></tr><tr><td>集合相等</td><td>例2</td></tr><tr><td>真子集</td><td>例3</td></tr><tr><td rowspan="3">集合的运算</td><td>交集</td><td>例4</td><td rowspan="3">★★3道题 ★★★★1道题</td></tr><tr><td>并集</td><td>例5</td></tr><tr><td rowspan="2">补集</td><td>例6</td></tr><tr><td></td><td>例7</td></tr></table></body></html>

# 学习目标

$\textcircled{1}$ 理解集合之间包含与相等的含义.

$\textcircled{2}$ 能使用Venn图表达集合之间的关系.

$\textcircled{8}$ 理解集合的交集、并集和补集的含义，并会求两个简单集合的交集、并集和补集。

# 模块1集合的关系

# APP扫码观看本模块讲解视频

1知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# 子集

如果集合A中的每一个元素都是集合 $B$ 中的元素，则称集合 $A$ 是集合 $B$ 的子集.记作 $A$ n$B$ 或 $B \supseteq A$ ,读作：“A包含于B”或“ $B$ 包含 $A ^ { \prime \prime }$

![](images/2dbcceafdd5ed8a1a5dba679caee2ef58b47b1f8a89428288f4d14763708aeed.jpg)

我们规定空集是任何一个集合的子集，即对任意集合A，有A.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例1

# （1）★

集合 $M = \left\{ 0 , 1 , 2 , 4 \right\}$ ，则集合 $M$ 的非空子集的个数是

# 学习笔记

# （2）★★

已知集合 $A = \left\{ \ - 1 , 2 , 2 m - 1 \ \right\}$ $B = \left\{ 2 , m ^ { 2 } \right\}$ .若 $B \subseteq A$ ,则实数 $m = \_$

# 学习笔记

# （3）★★★★

若集合 $A = \left\{ x \left| x ^ { 2 } + x - 6 = 0 \right. \right\}$ ， $B = \left\{ x \left| x ^ { 2 } + x + a = 0 \right. \right\}$ ，且 $B \subseteq A$ ,求实数 $^ { a }$ 的取值范围.

# 学习笔记

# 变式1★★

已知集合 $A = \{ x \vert - 2 \leqslant x \leqslant 7 \}$ ， $B = \left\{ x \left| m + 1 < x < 2 m - 1 \right. \right\}$ ，且 $B \neq \emptyset$ ，若$B \subseteq A$ ,则（）

A. $- 3 \leqslant m \leqslant 4$ B.-3<m<4 C. $2 < m < 4$ D.2<m≤4

# 学习笔记

# 知识点睛

# $\textcircled { > }$ 集合相等

如果集合A是集合 $B$ 的子集( $. A \subseteq B ,$ ，且集合 $B$ 是集合 $A$ 的子集( $| B \subseteq A { \textrm { , } }$ ,此时,集合A与集合 $B$ 中的元素是一样的,因此,集合 $A$ 与集合 $B$ 相等,记为 $A = B$

#

![](images/06220374c1e508de8aa06f4dda883952a53e679b517c33b4825fbe98e0463a7e.jpg)

# 精讲精练

# 拍照批改秒判对错

# 例2

# （1）★

下列各组集合中，表示同一集合的是(

A. $M = \{ \ ( 2 , 3 ) \}$ ， $N = \{ \ : ( 3 , 2 ) \ : \}$   
B. $M = \left\{ 3 , 2 \right\} , N = \left\{ 2 , 3 \right\}$   
C. $M = \left\{ \left( x , y \right) \ \lvert x + y = 1 \right\} \ , N = \left\{ \ y \lvert x + y = 1 \right\}$ D. $M = \{ 1 , 2 \}$ ， $N = \{ \{ 1 , 2 \} \}$

# 学习笔记

# （2）★★

已知集合 $M = \{ a , 2 , 3 + a \}$ ，集合 $N = \{ 3 , 2 , a ^ { 2 } \}$ .若集合 $M = N$ ，则 $a =$ （）

A.1 B.3 C.0 D.0或1

# 学习笔记

# 变式2

# （1）★★

集合 $A = \left\{ 1 , x , y \right\} , B = \left\{ 1 , x ^ { 2 } , 2 y \right\}$ ，若 $A = B$ ,则实数 $x$ 的取值集合为

A $\left\{ { \frac { 1 } { 2 } } \right\}$ $\begin{array} { l } { { \mathrm { B . } \{ \displaystyle - \frac 1 2 , \frac { 1 } { 2 } \} } } \\ { { \mathrm { D . } \{ \displaystyle 0 , \frac { 1 } { 2 } ,  - \frac { 1 } { 2 } \} } } \end{array}$ C $\left\{ 0 , { \frac { 1 } { 2 } } \right\}$

# 学习笔记

# （2）★★

已知集合 $A = \left\{ x \mid x = 1 + a ^ { 2 } , a \in \mathbf { R } \right\}$ ， $B = \{ \gamma | y = a ^ { 2 } - 4 a + 5 , a \in \mathbf { R } \}$ ,判断这两个集合之间的关系.

# 学习笔记

# 知识点睛

# $\textcircled{8}$ 真子集

如果集合A是集合 $B$ 的子集,并且 $B$ 中至少有一个元素不属于A,那么集合A叫做集合 $B$ 的真子集，记作 $A \subsetneq B$ 或 $B \supsetneq A$ ,读作"A真包含于 $B$ ”或“ $B$ 真包含 $A ^ { \prime \prime }$

我们规定空集是任何非空集合的真子集，即对任意集合A，若A≠，则A.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例3

# （1）★

若集合 $A = \{ x \vert x > 3 \}$ ,则下列表示中正确的是(

A. $\pi \subsetneq A$ B.{π}A C.{π}∈A D.{π}gA

# 学习笔记

# （2）★★

集合 $A = \left\{ \begin{array} { l l }  - 1 , 0 , 1 , 2 \right\} \end{array}$ 的真子集的个数为(

A.13 B.14 C.15 D.16

# 学习笔记

# （3）★★★

若 $\left\{ 1 , 2 \right\} \subsetneq A \subseteq \left\{ 1 , 2 , 3 , 4 , 5 \right\}$ ,则满足条件的集合A的个数是( ）

A.6 B.7 C.8 D.9

# 学习笔记

# 变式3★★

已知集合 $M = \{ x \vert x ^ { 2 } - x { \leqslant } 0 , x { \in } \mathbf { R } \}$ ，集合 $N = ( 0 , 1 ]$ ,则集合 $M , N$ 的关系是（）

A $M \subsetneq N$ （204 B.NM C. $M = N$ D. $M \not \subseteq N$ 且 $N \not \in M$

# 学习笔记

# 模块2集合的运算

# APP扫码观看本模块讲解视频

知识与方法 例题与练习全程跟老师 高效学知识

# 知识点睛

# 交集

对于两个给定的集合 $A , B$ ,由属于 $A$ 又属于 $B$ 的所有元素构成的集合叫做 $A , B$ 的交集,记作“ $A \cap B ^ { \prime \prime }$ ，读作A交 $B$ ：

用符号语言表示： $A \cap B = \left\{ x \mid x \in A \right.$ 且 $x \in B \}$

图形语言表示：

![](images/5a3fb06146c1ac135690760eba54e5b792e47e63eff9701bbc9dcabd149ac7ed.jpg)

# 思考探究

$A = \{ x \mid x$ 是我们班级的女同学！，$B = \left\{ x \vert x \right.$ 是我们班级戴眼镜的同学！，$C = \{ x \vert x$ 是我们班级戴眼镜的女同学！，集合 $C$ 与集合 $A , B$ 是什么关系呢？

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例4

# （1）

已知集合 $A = \{ 1 , 2 , 3 , 4 \}$ $B = \left\{ \left. y \right| y = 3 x - 2 , x \in A \right\}$ ，则 $A \cap B = \left( \begin{array} { l l l } { \begin{array} { r l } \end{array} } & { \begin{array} { r l } \end{array} } & { } \end{array} \right)$

A.{1 B.{4} C.{1,3} D.{1,4}

# 学习笔记

# （2）★★

已知集合 $P = \{ x \vert x - 1 \leqslant 0 \}$ $M = \left\{ x \left| x + 2 > 0 \right. \right\}$ ，则 $P \cap M = ( \qquad )$

A $( \mathbf { \Sigma } - \infty \mathbf { \Sigma } , 1 ]$ B. $[ \ - 2 , + \infty \ )$ C.[1,2) D.（-2,1]

# 学习笔记

# 变式4

# （1）

若集合 $A = \left\{ x \mid - 2 < x < 1 \right\}$ ， $B = \left\{ x \vert x < - 1 \right.$ 或 $x > 3 \left\{ \begin{array} { l l } { \begin{array} { r l r } \end{array} } \end{array} \right.$ ，则 $A \cap B =$ ( ）

A. $\left\{ x \mid - 2 < x < - 1 \right\}$ B. $\{ x \mid - 2 < x < 3 \}$ C. $\{ x \mid - 1 < x < 1 \}$ D.{𝑥 |1<x<3}

# 学习笔记

# （2）★★★

已知集合 $A = \left\{ x \left| { \frac { 5 x + 1 } { x + 1 } } < 3 \right. \right\} , B = \left\{ x \left| 2 x - x ^ { 2 } \geqslant 0 \right. \right\}$ ，则 $A \cap B = \left( \begin{array} { l l l } { \begin{array} { r l } \end{array} } & { \begin{array} { r l } \end{array} } & { } \end{array} \right)$

A. (0,1) B.[0,1] C.[0,1) D. (0,1]

# 学习笔记

# 知识点睛

# ②并集

对于两个给定的集合 $A , B$ ,由两个集合所有元素构成的集合叫做A与 $B$ 的并集，记作“AUB”,读作“A并 $B ^ { \prime \prime }$

用符号语言表示为 $A \cup B = \{ x \mid x \in A$ 或 $x \in B \}$

用维恩（Venn)图表示如下：

阴影部分表示 $A \cup B$

![](images/47855fac02dd179b757795f40e0ed712809211457680b6fabbc4f08f0a9d131c.jpg)

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例5

# （1）★★

设集合 $A = \left\{ x \in \mathbf { N } \left| 0 \leqslant x \leqslant 2 \right. \right\}$ $B = \left\{ x \in \mathbf { N } \left| 1 \leqslant x \leqslant 3 \right. \right\}$ ，则 $A \cup B = \left( \begin{array} { l l l } { \begin{array} { r l } \end{array} } & { \begin{array} { r l } \end{array} } & { \begin{array} { r l } \end{array} } \end{array} \right)$

A $\left\{ 1 , 2 \right\}$ B. $\{ 0 , 1 , 2 , 3 \}$ （204 C. $\{ x \mid 1 \leqslant x \leqslant 2 \}$ （202 D.{x |0≤x≤3}

# 学习笔记

# （2）★★★

若集合 $A = \left\{ \ 1 , x ^ { 2 } \ \right\}$ ，集合 $B = \{ 1 , 3 , x \}$ ，且 $A \cup B = \{ 1 , 3 , x \}$ ，则 $_ x$

# 学习笔记

# 变式5★★★

已知集合 $A = \left\{ 1 , 3 , x ^ { 2 } \right\}$ ， $B = \left\{ 1 , 2 - x \right\}$ ，且 $B \subseteq A$

(1)求实数 $_ x$ 的值. (2)若 $B \cup C = A$ ,求集合 $C$

# 学习笔记

# 知识点睛

# $\textcircled{8}$ 补集

一般地，如果一个集合含有我们所研究问题中涉及的所有元素,那么就称这个集合为全集,通常记作 $U .$

对于一个集合 $A$ ，由全集 $U$ 中不属于集合 $A$ 的所有元素组成的集合称为集合A相对于全集$U$ 的补集,简称为集合 $A$ 的补集,记作 $\complement _ { U } A$ ,读作：A在 $U$ 中的补集.

用符号语言表示为： $\complement _ { U } A = \{ x \mid x \in U$ 且 $x \not \in A \}$

用维恩图表示如下，阴影部分表示集合 $A$ 的补集 $\complement _ { U } A .$

![](images/564056c4e0d9fb1c05f6a3aeb29683c3e32ebcc7d8b05e2045733ba264460539.jpg)

# 精讲精练

# 拍照批改秒判对错

# 例6

# （1）

设集合 $A = \{ 0 , 2 , 4 , 6 , 8 , 1 0 \}$ $B = \{ 4 , 8 \}$ ,则 $\complement _ { A } B =$ （204

# 学习笔记

# （2）★★

已知全集 $U = \left\{ x \in \mathbf { N } | x \leqslant 5 \right\}$ ，若 $A = \left\{ x \in \mathbf { N } \left| 2 x - 5 < 0 \right. \right\}$ ,则 $\mathsf { f } _ { U } A = \left( \begin{array} { l l l } { \begin{array} { r l } \end{array} } & { \begin{array} { r l } \end{array} } & { \begin{array} { r l } \end{array} } \end{array} \right)$

A.{3,4} B. {3,4,5} C. {2,3,4,5} D. $\{ 4 , 5 \}$

# 学习笔记

# 变式6

# （1）

已知全集 $U = \mathbb { R }$ ,集合 $A = \left\{ x \mid x < - 2 \right.$ 或 $x > 2$ ，则 $\mathsf { f } _ { U } A = ( \begin{array} { l l l } & { } & { } \end{array} )$

A.(-2,2) B. $( \ - \infty \ , - 2 ) \cup ( 2 , + \infty )$ C.[-2,2] D $\mathbf { \Sigma } , \mathbf { \Sigma } ( \mathbf { \Sigma } - \infty , - 2 ] \cup [ 2 , + \infty )$

# 学习笔记

# (2)★★

设集合 $A = \{ 4 , m , m ^ { 2 } + 3 m \}$ 中实数 $m$ 的取值集合为 $M$ ，则 $\complement _ { \mathbf { R } } M =$

# 学习笔记

# 例7★★★

已知全集 $U = \mathbb { R }$ ，集合 $A = \{ x \mid - 2 \leqslant x \leqslant 3 \}$ ， $B = \left\{ x \vert x < - 1 \right.$ 或 $x > 4$ ，则$A \cap \complement _ { U } B = ( \qquad )$

A. $\{ x \mid - 2 \leqslant x < 4 \}$ B. $\left\{ x \mid x \leqslant 3 \right.$ 或 $x \geqslant 4 \}$ C. $\left\{ x \mid - 2 \leqslant x \leqslant - 1 \right\}$ D. $\{ x \mid - 1 \leqslant x \leqslant 3 \}$

# 学习笔记

# 变式7

# （1）★★

已知集合 $A = \{ x \vert 3 \leqslant x < 7 \}$ $B = \left\{ x \vert 2 < x < 1 0 \right\}$ ,则 $\complement _ { \mathbb { R } } ( A \cup B ) = ($ ）

A $\{ x \ \vert 3 \leqslant x < 7 \}$ （204 B. $\{ x \ | 2 < x < 1 0 \}$ C. $\{ x \mid x \leqslant 2$ 或 $x \geqslant 1 0 \ \}$ D. $\{ x \vert x < 3$ 或 $x \geqslant 7 \}$

# 学习笔记

# （2）★★

已知全集 $U = \left\{ 1 , 2 , 3 , 4 , 5 , 6 , 7 , 8 , 9 , 1 0 \right\}$ ，集合 $A = \{ 2 , 3 , 5 , 9 \}$ ，集合 $B =$ {4,5,6,7,9}，则 $\mathrm {  { ~ ( ~  ~ } } \complement _ { U } A \mathrm {  { ~ ) \cap ( ~  ~ } } \complement _ { U } B \mathrm {  { ~ ) = ( ~  ~ } \qquad \mathrm {  { ~ ) } } }$

A.{5,9} B.{2,3} C.{1,8,10} D.{4,6,7}

# 学习笔记

![](images/42dc43bd30a706d00fe2266aaed4de0edce4b6dff647822a97a1b0879c7a5773.jpg)

# 学习总结

![](images/97b86c7fc5817a7f8cca6349904153d6f22534f4d292bd3e9736cbf43c362c0d.jpg)

# 提升篇你会遇见

（预习篇·P10,变式3)已知集合 $M = \{ x | x ^ { 2 } - x { \leqslant } 0 , x \in \mathbf { R } \}$ ，集合 $N = ( 0 , 1 ]$ ，则集合 $M$ ，$N$ 的关系是（

A $M \subsetneq N$ B. $N \subsetneq M$ C. $M = N$ D. $M \not \in N$ 且 $N \not \in M$ （提升满）已知集合 ${ \cal M } = \left\{ x \bigg | x = m + \frac { 1 } { 6 } , m { \in } { \bf Z } \right\} , { \cal N } = \left\{ x \bigg | x = \frac { n } { 2 } - \frac { 1 } { 3 } , n { \in } { \bf Z } \right\} ,$ $P = \left\{ x { \Biggl | } x = { \frac { p } { 2 } } + { \frac { 1 } { 6 } } , p \in \mathbf { Z } \right\}$ 则 $M , N , P$ 的关系为（

A $M = N \subseteq P$ B. $M \supseteq N = P$   
C. $M \subseteq N = P$ D. $M = N = P$

【点石成金】以上两题都考查了集合间的关系.对比发现，预习篇题目比较简单，可以计算出各个集合中的元素，从而判断它们的关系.而提升篇题目则比较复杂，每个集合都是无限集，并不容易一一列举出集合中的元素.需要我们分析集合所表示的内容.这类题目预习篇并没有见过，是我们提升篇解决的重难点，期待我们提升篇的学习吧！

# 学而思秘籍系列图书|数学

# 思维培养

![](images/ab89cf1d1451852cf11464328817329be929bcd93f4af2b73ed603a59ecb5129.jpg)

# 小学秘籍系列

学而思积淀近20年教研经验，培养受益一生的能力。

# 思维提升

![](images/06c31d3f639f2374995a7f461332ddf3c4d01c4ae9b22cefe874f9241ce7f24b.jpg)

# 初中秘籍系列

全面覆盖初中基础知识和重难点，帮助学生夯实基础，拓展认知。

# 思维突破

![](images/adb8abdd0a28e61810d0327b47d0e413168a680f8e4d3396fd5ec06633fd751a.jpg)

# 高中秘籍系列

全面覆盖高中基础知识和重难点，帮助学生提升能力，突破思维。

# 学而思秘籍系列图书|语文

# 提升素养

![](images/26c93d9f9e3809af3da6bdc66069ad6e78d044eaf580852bf063bfb71870ea1f.jpg)

# 小学秘籍系列

5大模块+2条主线，能力与素养双向提升。

# 能力训练

![](images/7d7a491c4d806f923892bc8307befc3395807a44ad73722b0a121b97d752568f.jpg)

# 初中秘籍系列

融合课改四大核心素养，培养爱阅读、 善写作、勤思考、会学习的学生。

# 创新体系|真题研习

![](images/fda04c98dea7221c0a6076cf9416f3e8967db620a9ca0ae039801de19610716f.jpg)

# 思维创新大通关数学

攻克数学思维难题，通向理想中学。

# 大家一起来“升级’

# 参与方式

您在使用本书时，如有任何疑问或对图书有任何建议，请扫码进行反馈，并查看反馈采纳结果。

# 奖励

您的反馈一经采纳，我们将会送出总价值35元的图书抵扣券（相同内容的反馈，依据反馈时间，奖励前三位）。请扫码关注公众号，并在对话框中发送反馈时填写的手机号，领取抵扣券。

![](images/26d12ff427d5bc8516224f3486098fd15f85dc6a578ab99223a68a140a1b5a71.jpg)

# 合理规划学习时间

先自己定一个目标，即制定半年学习规划。

2 再将目标细化到每一周，每周学习一本（平均5个考点）。

3 配套课堂巩固的练习， 让学习更有效！

![](images/8faada68907197cf99cc4a42233f45a69da3959be09770f72cbc6849690becca.jpg)

![](images/092cb5fdb794fafa5b6192e6c6287ae1236c13336df9ce276950eb46bb145ff5.jpg)  
·共6级·每级17-26讲