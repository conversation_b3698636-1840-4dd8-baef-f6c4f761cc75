---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 21
source_file: 专题01 集合与常用逻辑用语（原卷版）.md
title: 专题01 集合与常用逻辑用语（原卷版）
type: problem_type
---

# 专题01集合与常用逻辑用语

# 考情概览

<html><body><table><tr><td>命题解读</td><td>考向</td><td>考查统计</td></tr><tr><td>1.高考对集合的考查，重点是集合间的 基本运算，主要考查集合的交、并、补 运算，常与一元二次不等式解法、一元 一次不等式解法、分式不等式解法、指 如下两点：</td><td>交集的运算</td><td>2022·新高考I卷，1 2023·新高考I卷，1 2024·新高考I卷，1</td></tr><tr><td rowspan="3">数、对数不等式解法结合. 2.高考对常用逻辑用语的考查重点关注</td><td>根据集合的包含关系求参数</td><td>2022·新高考Ⅱ卷，1 2023·新高考Ⅱ卷，2</td></tr><tr><td>充分必要条件的判定</td><td>2023·新高考I卷，7</td></tr><tr><td>全称、存在量词命题真假的判断</td><td>2024·新高考Ⅱ卷，2</td></tr></table></body></html>

# 2024年真题研析

# 命题分析

2024年高考新高考Ⅱ卷未考查集合，I卷依旧考查了集合的交集运算，常用逻辑用语在新高考Ⅱ卷中考查了全称、存在量词命题真假的判断，这也说明了现在新高考“考无定题”，以前常考的现在不一定考了，抓住知识点和数学核心素养是关键！集合和常用逻辑用语考查应关注：（1）集合的基本运算和充要条件；（2)集合与简单的不等式、函数的定义域、值域的联系。预计 2025年高考还是主要考查集合的基本运算。

# 试题精讲

1．（2024 新高考卷·1）已知集合 $A = \left\{ x | - 5 < x ^ { 3 } < 5 \right\} , B = \{ - 3 , - 1 , 0 , 2 , 3 \}$ ，则 $A \cap B = { \textrm { ( ) } }$

A. $\{ - 1 , 0 \}$ （204 B. {2,3} $\begin{array} { c c c c c c } { { \mathrm { C . } } } & { { \{ - 3 , - 1 , 0 \} } } & { { } } & { { } } & { { } } & { { \mathrm { D . } } } & { { \{ - 1 , 0 , 2 \} } } \end{array}$

：．（2024新高考Ⅱ卷·2）已知命题 $p$ ： $\forall x \in \mathbf { R }$ ， $\vert x + 1 \vert > 1$ ；命题 $q$ ： $\exists x > 0$ ， $x ^ { 3 } = x$ ，则（）

A. $p$ 和 $q$ 都是真命题 B. $\neg p$ 和 $q$ 都是真命题C. $p$ 和 $\neg q$ 都是真命题 D. $\neg p$ 和 $\neg q$ 都是真命题

# 近年真题精选

1．（2022新高考I卷·1）若集合 $M = \{ x | { \sqrt { x } } < 4 \}$ ， $N = \{ x \vert 3 x \ge 1 \}$ ，则 $M \cap N = { \mathrm { ~ ( ~ ) ~ } }$

$$
\mathrm { ~  ~ { ~ A ~ } ~ } . ~ \left\{ x \Big \vert 0 \leq x < 2 \right\} ~ \mathrm { ~  ~ { ~ B ~ } ~ } . ~ \left\{ x \bigg \vert \frac { 1 } { 3 } \leq x < 2 \right\} \qquad \mathrm { ~  ~ { ~ C ~ } ~ } . ~ \left\{ x \Big \vert 3 \leq x < 1 6 \right\} ~ \mathrm { ~  ~ { ~ D ~ } ~ } . ~ \left\{ x \bigg \vert \frac { 1 } { 3 } \leq x < 1 6 \right\}
$$

2．（2023新高考I卷·1）已知集合 $M = \left\{ - 2 , - 1 , 0 , 1 , 2 \right\}$ ， $N = \left\{ x { \left| x ^ { 2 } - x - 6 \geq 0 \right. \right\} }$ ，则 $M \cap N = { \mathrm { ~ ( ~ ) ~ } }$

A.{-2,-1,0,1}B.{0,1,2} ${ \mathrm { C } } . \quad \left\{ - 2 \right\}$ D.{2}

3．（2022新高考Ⅱ卷·1）已知集合 $A = \left\{ - 1 , 1 , 2 , 4 \right\} , B = \left\{ x \left\| x - 1 \right\| \leq 1 \right\}$ ，则 $A \cap B = { \textrm { ( ) } }$

A.{-1,2} B.{1,2} $\mathrm { C . } \quad \{ 1 , 4 \} \qquad \mathrm { D . } \quad \{ - 1 , 4 \}$

4．（2023新高考Ⅱ卷·2）设集合 $A = \left\{ 0 , - a \right\}$ ， $B = \left\{ 1 , a - 2 , 2 a - 2 \right\}$ ，若 $A \subseteq B$ ，则 $a = \mathrm { ~  ~ { ~ \left( ~ \begin{array} { l } { \mathrm { ~ \theta ~ } } \\ { \mathrm { ~ \theta ~ } } \end{array} \right) } ~ }$ ：

A.2 B.1 C. $\frac { 2 } { 3 }$ D．-1

5．（2023新高考卷·7）记 $S _ { n }$ 为数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和，设甲： $\left\{ a _ { n } \right\}$ 为等差数列；乙： $\{ { \frac { S _ { n } } { n } } \}$ 为等差数列，则（）

A．甲是乙的充分条件但不是必要条件B．甲是乙的必要条件但不是充分条件C．甲是乙的充要条件D．甲既不是乙的充分条件也不是乙的必要条件

# 必备知识速记

# 一、元素与集合

1、集合的含义与表示

某些指定对象的部分或全体构成一个集合．构成集合的元素除了常见的数、点等数学对象外，还可以是其他对象.

2、集合元素的特征

（1）确定性：集合中的元素必须是确定的，任何一个对象都能明确判断出它是否为该集合中的元素.  
（2）互异性：集合中任何两个元素都是互不相同的，即相同元素在同一个集合中不能重复出现.  
（3）无序性：集合与其组成元素的顺序无关.

3、元素与集合的关系

元素与集合之间的关系包括属于(记作 $a \in A$ )和不属于(记作 $a \not \in A$ )两种.

4、集合的常用表示法

集合的常用表示法有列举法、描述法、图示法(韦恩图).

# 5、常用数集的表示

<html><body><table><tr><td>数集</td><td>自然数集</td><td>正整数集</td><td>整数集</td><td>有理数集</td><td>实数集</td></tr><tr><td>符号</td><td>N</td><td>N或N</td><td>Z</td><td></td><td>R</td></tr></table></body></html>

# 二、集合间的基本关系

（1）子集：一般地，对于两个集合 $A \setminus B$ ，如果集合 $A$ 中任意一个元素都是集合 $B$ 中的元素，我们就说这两个集合有包含关系，称集合 $A$ 为集合 $B$ 的子集，记作 $A \subseteq B$ （或 $B \supseteq A ^ { \prime } $ ，读作“ $A$ 包含于 $B$ ”（或“ $B$ 包含 $\boldsymbol { A } ^ { \flat } )$ ：

（2）真子集：对于两个集合 $A$ 与 $B$ ，若 $A \subseteq B$ ，且存在 $b \in B$ ，但 $b \not \in A$ ，则集合 $A$ 是集合 $B$ 的真子集，记作 $A \subsetneq B$ （或 $B \mathcal { \vec { z } } A$ ）.读作“ $A$ 真包含于 $B$ ”或“ $B$ 真包含 $A$ ”

（3）相等：对于两个集合 $A$ 与 $B$ ，如果 $A \subseteq B$ ，同时 $B \subseteq A$ ，那么集合 $A$ 与 $B$ 相等，记作 $A = B$ ：

（4）空集：把不含任何元素的集合叫做空集，记作 $\emptyset$ ； $\emptyset$ 是任何集合的子集，是任何非空集合的真子集.

# 三、集合的基本运算

（1）交集：由所有属于集合 $A$ 且属于集合 $B$ 的元素组成的集合，叫做 $A$ 与 $B$ 的交集，记作 $A \cap B$ ，即$A \cap B = \left\{ x \mid x \in A { \mathrm { E } } \bot x \in B \right\} .$

（2）并集：由所有属于集合 $A$ 或属于集合 $B$ 的元素组成的集合，叫做 $A$ 与 $B$ 的并集，记作 $A \cup B$ ，即$A \cup B = \{ x \mid x \in A  \forall x \in B \} .$

（3）补集：对于一个集合 $A$ ，由全集 $U$ 中不属于集合 $A$ 的所有元素组成的集合称为集合 $A$ 相对于全集 $U$ 的补集，简称为集合 $A$ 的补集，记作 $C _ { U } A$ ，即 $C _ { U } A = \{ x \vert x \in U$ ，且 $x \notin A \}$ ：

# 四、集合的运算性质

$$
\begin{array} { r l } & { A \bigcap A = A , A \bigcap \emptyset = \emptyset , A \bigcap B = B \bigcap A , A \cap B \subseteq A , A \cap B \subseteq B \cdot } \\ & { A \bigcup A = A , A \bigcup \emptyset = A , A \bigcup B = B \bigcup A , A \subseteq A \cup B , B \subseteq A \cup B \cdot } \\ & { A \bigcap ( C _ { \upsilon } A ) = \emptyset , A \bigcup ( C _ { \upsilon } A ) = U , C _ { \upsilon } ( C _ { \upsilon } A ) = A \cdot } \\ & { A \cap B = A \Leftrightarrow A \cup B = B \Leftrightarrow A \subseteq B \Leftrightarrow \complement _ { \upsilon } B \subseteq \complement _ { \upsilon } A \Leftrightarrow A \cap \complement _ { \upsilon } B = \emptyset } \end{array}
$$

# 【集合常用结论】

（1）若有限集 $A$ 中有 $n$ 个元素，则 $A$ 的子集有 $2 ^ { n }$ 个，真子集有 $2 ^ { n } - 1$ 个，非空子集有 $2 ^ { n } - 1$ 个，非空真子集有 $2 ^ { n } - 2$ 个.

（2）空集是任何集合 $A$ 的子集，是任何非空集合 $B$ 的真子集.

(3） $A \subseteq B \Leftrightarrow A \bigcap B = A \Leftrightarrow A \bigcup B = B \Leftrightarrow C _ { U } B \subseteq C _ { U } A \ .$ (4) $C _ { U } ( { \cal A } \bigcap { \cal B } ) = ( C _ { U } { \cal A } ) \bigcup ( C _ { U } { \cal B } ) , C _ { U } ( { \cal A } \bigcup { \cal B } ) = ( C _ { U } { \cal A } ) \bigcap ( C _ { U } { \cal B } ) \ .$

# 五、充分条件、必要条件、充要条件

1、定义如果命题"若 $p$ ，则 $q$ "为真（记作 $p \Longrightarrow q$ ），则 $p$ 是 $q$ 的充分条件；同时 $q$ 是 $p$ 的必要条件.

2、从逻辑推理关系上看

（1）若 $p \Longrightarrow q$ 且 $q \nRightarrow p$ ，则 $p$ 是 $q$ 的充分不必要条件；  
(2）若 $p \nRightarrow q$ 且 $q \Longrightarrow p$ ，则 $p$ 是 $q$ 的必要不充分条件；  
（3）若 $p \Longrightarrow q$ 且 $q \Longrightarrow p$ ，则 $p$ 是 $q$ 的的充要条件（也说 $p$ 和 $q$ 等价）；  
（4）若 $p \nRightarrow q$ 且 $q \nRightarrow p$ ，则 $p$ 不是 $q$ 的充分条件，也不是 $q$ 的必要条件.

# 六、全称量词与存在量词

（1）全称量词与全称量词命题．短语"所有的”、“任意一个"在逻辑中通常叫做全称量词，并用符号“ $\forall$ ”表示．含有全称量词的命题叫做全称量词命题．全称量词命题"对 $M$ 中的任意一个 $x$ ，有 $p ( x )$ 成立"可用符号简记为" $\forall { x } \in M , p ( { x } ) ^ { \prime \prime }$ ，读作“对任意 $x$ 属于 $M$ ，有 $p ( x )$ 成立”.  
（2）存在量词与存在量词命题．短语"存在一个”、“至少有一个"在逻辑中通常叫做存在量词，并用符号“"表示．含有存在量词的命题叫做存在量词命题．存在量词命题"存在 $M$ 中的一个 $x _ { 0 }$ ，使 $p ( x _ { 0 } )$ 成立"可用符号简记为" $\exists x _ { 0 } \in M , P ( x _ { 0 } ) ^ { \mathfrak { N } }$ ，读作"存在 $M$ 中元素 $x _ { 0 }$ ，使 $p ( x _ { 0 } )$ 成立”（存在量词命题也叫存在性命题），七、含有一个量词的命题的否定  
（1）全称量词命题 $p : \forall x \in M , p ( x )$ 的否定 $\neg p$ 为 $\exists x _ { 0 } \in M$ ， $\neg p ( x _ { 0 } )$ ：  
（2）存在量词命题 $p : \exists x _ { 0 } \in M , p ( x _ { 0 } )$ 的否定 $\neg p$ 为 $\forall x \in M , \lnot p ( x )$ ：  
注：全称、存在量词命题的否定是高考常见考点之一.

# 【常用逻辑用语常用结论】

1、从集合与集合之间的关系上看设 ${ \cal A } = \left\{ x \mid p ( x ) \right\} , B = \left\{ x \mid q ( x ) \right\} .$ （号

（1）若 $A \subseteq B$ ，则 $p$ 是 $q$ 的充分条件（ $p \Longrightarrow q$ ）， $q$ 是 $p$ 的必要条件；若 $A \subsetneq { \mathbb { B } }$ ，则 $p$ 是 $q$ 的充分不必要条件， $q$ 是 $p$ 的必要不充分条件，即 $p \Longrightarrow q$ 且 $q \nRightarrow p$ ;  
注：关于数集间的充分必要条件满足：“小 $\Rightarrow$ 大”  
(2）若 $B \subseteq A$ ，则 $p$ 是 $q$ 的必要条件， $q$ 是 $p$ 的充分条件；  
（3）若 $A = B$ ，则 $p$ 与 $q$ 互为充要条件.

# 名校模拟探源

# 一、单选题

1．（2024·河南·三模）命题‘ $\exists x > 0 , x ^ { 2 } + x - 1 > 0$ "的否定是（）

A. $\forall x > 0 , x ^ { 2 } + x - 1 > 0$ $\begin{array} { l l } { \mathrm { B . } } & { \forall x > 0 , x ^ { 2 } + x - 1 \le 0 } \\ & { } \\ { \mathrm { D . } } & { \exists x \le 0 , x ^ { 2 } + x - 1 \le 0 } \end{array}$   
C. $\exists x \leq 0 , x ^ { 2 } + x - 1 > 0$

2. （2024·湖南长沙·三模）已知集合 $M = \left\{ x | { \big | } x { \big | } \leq 2 \right\} , N = \{ x | \ln x < 1 \}$ ，则 $M \cap N = { \mathrm { ~ ( ~ ) ~ } }$

A.[2,e) B. [-2,1] c. [0,2) D. (0,2]

3．（2024·河北衡水·三模）已知集合 $A = \left\{ 1 , 2 , 3 , 4 , 5 \right\}$ ， $B = \left\{ x { \big | } - 1 \leq \log { \big ( } x - 1 { \big ) } \leq { \frac { 1 } { 2 } } \right\}$ 则 $A \cap B = { \textrm { ( ) } }$

$$
\left\{ x | \frac { 1 1 } { 1 0 } \leq x \leq 5 \right\} \mathrm { ~ B . ~ } \left\{ 2 , 3 , 4 \right\} \qquad \mathrm { ~ C . ~ } \left\{ 2 , 3 \right\} \qquad \mathrm { ~ D . ~ } \left\{ x | \frac { 1 1 } { 1 0 } \leq x \leq 3 \right\}
$$

4．（2024·陕西·三模）已知集合 $A = \left\{ x | - 1 \leq x \leq 2 \right\} , B = \left\{ x | - x ^ { 2 } + 3 x > 0 \right\}$ ，则 $A \cup B = \mathrm { ~ ( ~ ) ~ }$

A.R B. (0,2] $\mathrm { ~ \mathsf ~ { ~ C ~ . ~ } ~ } \left[ - 1 , 0 \right) \mathrm { ~ \mathsf ~ { ~ D ~ . ~ } ~ } \left[ - 1 , 3 \right)$

5．（2024·安徽·三模）已知集合 $A = \left\{ x \lvert - 5 \leq x \leq 1 \right\}$ ， $B = \left\{ x | x > - 2 \right\}$ ，则图中所示的阴影部分的集合可以表示为（）

![](images/9cb156f631a5bcf1499030b33117dff9f6c5c23f834d47b6217b7f4020a2c056.jpg)

A. $\left\{ x | - 2 \leq x \leq 1 \right\}$ $\begin{array} { l l } { { \mathrm { B . } } } & { { \left\{ x \left. - 2 < x \leq 1 \right\} \right. } } \\ { { } } & { { } } \\ { { \mathrm { D . } } } & { { \left\{ x \left. - 5 \leq x < - 2 \right\} \right. } } \end{array}$   
C. $\left\{ x | - 5 \leq x \leq - 2 \right\}$

6．（2024·湖南长沙·三模）已知直线 $l : k x - y + { \sqrt { 2 } } k = 0$ ，圆 $O : x ^ { 2 } + y ^ { 2 } = 1$ ，则“ $k < 1$ "是“直线 $l$ 上存在点 $P$ ，使点 $P$ 在圆 $O$ 内"的（）

A．充分不必要条件 B．必要不充分条件C．充要条件 D．既不充分也不必要条件

7．（2024·湖北荆州·三模）已知集合 $A = \left\{ x { \big | } 2 x - x ^ { 2 } \leq 0 \right\}$ ， $B = \complement _ { \mathbf { R } } A$ ，其中 $\mathbf { R }$ 是实数集，集合 $C = \left( - \infty , 1 \right]$ ，则$B \cap C = { \mathrm { ~ ( ~ ) ~ } }$

A. $\left( - \infty , 0 \right]$ B. (0.1] C. $\left( - \infty , 0 \right)$ D. (0.1)

8. （2024·北京·三模）已知集合 $A = \left\{ x | \mathrm { l n } x < 1 \right\}$ ，若 $a \not \in A$ ，则 $a$ 可能是（）

A. B.1 C.2 D.3 A

9. （2024·河北衡水·三模）已知函数 $f ( x ) = { \left( 2 ^ { x } + m \cdot 2 ^ { - x } \right) } \sin x$ ，则“ $m ^ { 2 } = 1$ 是“函数 $f ( x )$ 是奇函数"的（）

A．充分不必要条件 B．必要不充分条件C．充要条件D．既不充分也不必要条件

10．（2024·内蒙古·三模）设 $\alpha$ ， $\beta$ 是两个不同的平面， $m \ , \ l$ 是两条不同的直线，且 $\alpha \cap \beta = l$ 则" $m / / l$ ”是“ $m / / \beta$ 且 $m / \alpha$ ”的（）

A．充分不必要条件 B．充分必要条件C．必要不充分条件 D．既不充分也不必要条件

11．（2024·北京·三模）已知 $A = \{ x { | \log _ { 2 } { ( x - 1 ) } \leq 1 \} }$ ， $B = \left\{ x \Big \| x - 3 \Big | > 2 \right\}$ ，则 $A \cap B = { \textrm { ( ) } }$

A．空集 B. $\left\{ x | x \leq 3 { \frac { \pi } { \operatorname { \mathbb { X } } } } x > 5 \right\}$ C. $\left\{ x { \big | } x \leq 3 \right.$ 或 $x > 5$ 且 $x \neq 1 \}$ D．以上都不对

12．（2024·四川·三模）已知集合 $A = \left\{ 0 , 3 , 5 \right\}$ ， $B = \left\{ x { \big | } x { \big ( } x - 2 { \big ) } = 0 \right\}$ ，则 $A \cap B = { \textrm { ( ) } }$

A.Q B. $\left\{ 0 \right\}$ C. {0,2,3,5} D.{0,3}

13. （2024·重庆·三模）已知集合 $A = \{ x \in \mathbf { R } { | x ^ { 2 } - x - 2 < 0  } \} , B = \{ y | \ y = 2 ^ { x } , x \in A \}$ ，则 $A \cap B =$ （）

A. (-1,4) B $\left( { \frac { 1 } { 4 } } , 1 \right)$ $\left( { \frac { 1 } { 2 } } , 1 \right)$ $\mathbf { D } . \left( { \frac { 1 } { 2 } } , 2 \right)$

14．（2024·北京·三模）“ ${ \triangle A B C }$ 为锐角三角形"是"sin $A > \cos B$ ， $\sin B > \cos C$ ， $\sin C > \cos A$ ”的（）

A．充分不必要条件 B．必要不充分条件C．充分必要条件 D．既不充分也不必要条件

15．（2024·上海·三模）设 $1 < a < b$ ，集合 $A = \left\{ 1 , a , b \right\}$ ，集合 $B = \left\{ t { \left| t = x y + \frac { y } { x } , x , y \in A , x \neq y \right. } \right\}$ 对于集合 $B$ 有下列两个结论： $\textcircled{1}$ 存在 $a$ 和 $^ { b }$ ，使得集合 $B$ 中恰有5个元素； $\textcircled{2}$ 存在 $a$ 和 $^ { b }$ ，使得集合 $B$ 中恰有4个元素．则下列判断正确的是（）

A. $\textcircled{1} \textcircled{2}$ 都正确B. $\textcircled{1} \textcircled{2}$ 都错误 C. $\textcircled{1}$ 错误， $\textcircled{2}$ 正确D. $\textcircled{1}$ 正确， $\textcircled{2}$ 错误

# 二、多选题

16．（2024·江西南昌·三模）下列结论正确的是（）

A.若 $\left\{ x | x + 3 > 0 \right\} \cap \left\{ x | x - a < 0 \right\} = \emptyset$ ，则 $a$ 的取值范围是 $a < - 3$ B.若 $\left\{ x | x + 3 > 0 \right\} \cap \left\{ x | x - a < 0 \right\} = \emptyset$ ，则 $a$ 的取值范围是 $a \leq - 3$ （204 C.若 $\left\{ x { \big | } x + 3 > 0 \right\} \cup \left\{ x { \big | } x - a < 0 \right\} = \mathbf { R }$ ，则 $a$ 的取值范围是 $a \ge - 3$ （204 D.若 $\left\{ x { \big | } x + 3 > 0 \right\} \cup \left\{ x { \big | } x - a < 0 \right\} = \mathbf { R }$ ，则 $a$ 的取值范围是 $a > - 3$ （204

17．（2024·辽宁·三模）已知 $\operatorname* { m a x } \left\{ x _ { 1 } , x _ { 2 } , \cdots , x _ { n } \right\}$ 表示 $x _ { 1 } , x _ { 2 } , \cdots , x _ { n }$ 这 $n$ 个数中最大的数．能说明命题“ $\forall a , b , c$ ，$d \in \mathbb { R }$ ， $\operatorname* { m a x } { \left\{ a , b \right\} } + \operatorname* { m a x } { \left\{ c , d \right\} } \geq \operatorname* { m a x } { \left\{ a , b , c , d \right\} }$ ”是假命题的对应的一组整数 $a$ ， $^ { b }$ ， $c$ ， $d$ 值的选项有（）

A.1，2,3，4 B.-3，-1，7，5  
C.8， $^ { - 1 }$ ， $^ { - 2 }$ ，-3 D.5，3，0，-1

18．（2024·重庆·三模）命题"存在 $x > 0$ ，使得 $m x ^ { 2 } + 2 x - 1 > 0$ "为真命题的一个充分不必要条件是（）

A. $m > - 2$ （204 B. $m > - 1$ C. $m > 0$ （204 D. m>1

19．（2024·黑龙江齐齐哈尔·三模）已知 $a , b > 0$ ，则使得“ $a > b$ "成立的一个充分条件可以是（）

$$
\mathrm { ~ \mathsf ~ { ~ A ~ . ~ } ~ } \frac { 1 } { a } < \frac { 1 } { b } \qquad \mathrm { ~ \mathsf ~ { ~ B ~ . ~ } ~ } | a - 2 | > | b - 2 | \qquad \mathrm { ~ \mathsf ~ { ~ C ~ . ~ } ~ } a ^ { 2 } b - a b ^ { 2 } > a - b \mathrm { ~  ~ \mathsf ~ { ~ D ~ . ~ } ~ } \ln \left( a ^ { 2 } + 1 \right) > \ln \left( b ^ { 2 } + 1 \right)
$$

20．（2024·安徽安庆·三模）已知集合 $A = \left\{ x \in \mathbf { Z } { \big | } x ^ { 2 } - 2 x - 8 < 0 \right\}$ ，集合 $B = \left\{ x { \big | } 9 ^ { x } > 3 ^ { m } , m \in \mathbf { R } , x \in \mathbf { R } \right\}$ ，若 $A \cap B$ （204 有且仅有3个不同元素，则实数 $m$ 的值可以为（）

A.0 B.1 C.2 D.3

# 三、填空题

21．（2024·湖南长沙·三模）已知集合 $A = \left\{ 1 , 2 , 4 \right\}$ ， $B = \left\{ a , a ^ { 2 } \right\}$ ，若 $A \cup B = A$ ，则 $a = \_$

22．（2024·上海·三模）已知集合 $A = \left\{ 0 , 1 , 2 \right\}$ ， $B = \{ x { | x ^ { 3 } - 3 x \leq 1 \} }$ ，则 $A \cap B = .$

23. （2024·湖南衡阳·三模）已知集合 $A = \left\{ a , a + 1 \right\}$ ，集合 $B = \left\{ x \in \mathbf { N } | x ^ { 2 } - x - 2 \leq 0 \right\}$ ，若 $A \subseteq B$ ，则 a=

24. （202-湖南邵阳-三楼） $A = \{ x \in \mathbf { N } | \log _ { 2 } ( x - 3 ) \leq 2 \} , \quad B = \{ x \mathopen { } \mathclose \bgroup | \frac { x - 3 } { x - 7 } \leq 0 \} ,$ 實则 $A \bigcap B = \qquad .$

25．（2024·安徽·三模）已知集合 $A = \{ \lambda , 2 , - 1 \} , B = \{ y | y = x ^ { 2 } , x \in A \}$ ，若 $A \cup B$ 的所有元素之和为12，则实数 $\lambda = .$

26. （2024·山东聊城·三模）已知集合 $A = \left\{ 1 , 5 , a ^ { 2 } \right\} , B = \left\{ 1 , 3 + 2 a \right\}$ ，且 $A \cup B = A$ ，则实数 $a$ 的值为

27．（2024·重庆·三模）已知集合 $A = \left\{ x { \left| x ^ { 2 } - 5 x + 6 = 0 \right. \right\} }$ ， $B = \left\{ x | { - } 1 < x < 5 , x \in \mathbf { N } \right\}$ ，则满足 $A \subseteq C \boxed { \square B }$ 的集合 $C$ 的个数为

28．（2024·天津·三模）己知全集 $U = \{ x \in \mathbf { N } ^ { * } | x \leq 7 \}$ ，集合 $A = \left\{ 1 , 2 , 3 , 6 \right\}$ ，集合 $B = \{ x \in \mathbf { Z } | | x | < 5 \}$ ，则 $( \complement _ { \Uparrow } A ) \bigcap B = \qquad , A \cup B = \qquad .$

29．（2024·山东泰安·三模）已知集合 $A = \left\{ x { \Biggl | } { \frac { x + 2 } { x - 2 } } \leq 0 \right\}$ ， $B = \left\{ x { \big | } \log _ { 2 } x \geq a \right\}$ ，若 $B \subseteq \left( \complement _ { \mathbb { R } } A \right)$ ，则 $a$ 的取值范围 是

30．（2024·宁夏银川·三模）已知命题 $p$ ：关于 $x$ 的方程 $x ^ { 2 } - a x + 4 = 0$ 有实根；命题 $q$ ：关于 $x$ 的函数 $y = \log _ { 3 } \left( 2 x ^ { 2 } + a x + 3 \right)$ 在 $\left[ 3 , + \infty \right)$ 上单调递增，若 $\mathbf { \hat { \boldsymbol { p } } }$ 或 $q ^ { \bullet }$ 是真命题， $^ { \ast } p$ 且 $q ^ { \ast }$ "是假命题，则实数 $a$ 的取值范围 是