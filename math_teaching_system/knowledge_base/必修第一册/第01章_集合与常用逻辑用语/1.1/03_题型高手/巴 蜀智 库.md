---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 120
source_file: 高中数学能力进阶-二轮复习.md
title: 高中数学能力进阶-二轮复习
type: concept
---

# 巴 蜀智 库

能力进阶二轮复习

教材 基础 考点通关 过关 把关

# 能力进阶

二轮复习高中数学巴蜀智库图书·数学编写组/编著

图书在浙编目（OIP）G夏鸽二学义中

# 金力进新—二轮复习高中数学

NENGUJINUIEERLUN FUXIGAOZHONG SHUXUE

巴里图电·数字/维香  
证飞  
魏王梅张恒林立  
：世国  
野任对：郑系  
：自琴部念王

# 重氏出社 重庆出瓶集团 出

11p：bom  
天乡路有限费任司  
行国厅  
：02-20211  
B2118B：125：630干  
20112年1月1次  
：67.00元

MU2

# “巴蜀智库”系列图书编委会

主任：周刘波  
副主任：张煜  
委员：蒲韬 周斌 付洪健 周欣孟杨斌廖春红高亚浩 李有胜 杨德芳张 波王 欢罗英铭禹云霜 王飞涛

# 巴蜀智库图书·数学编写组

组长：赵文平   
副组长：付洪健 周欣孟 江天龙 杨先佑马洪超张应 黄建 李蔓莉 唐莲娇 刘蒙钟剑张楚铭 邓鹏 吴子轩 张焱彦

# 组员（按音序排列）：

柏华 陈红菊 邓鹏 范发云 龚小花 郭鹏何龙 胡丽娟 胡莉 黄建 江岸 江大军江天龙 蒋中伟 李蔓莉 李仁江 李水艳 李婷李晓明 刘陈 刘丽君 刘蒙 骆禄辉 马洪超聂娟 冉茂园 唐高翼 唐莲骄 王善荣 吴剑吴树才 吴子轩 先莹莹 熊丙章 杨先佑 张楚铭张金煜 张勤 张伟 张晓波 张焱彦 张应章玉琴 郑强 钟剑 周欣孟

支周：五王以标员委亚高

# 《能力进阶—二轮复习 高中数学》编写人员

主编：周欣孟 付洪健 田副主编：周欣孟 邓　鹏　杨先佑　吴子轩　张焱彦

平文：  
艾云工 共：  
烈雪 味曼季甄黄干吴

（批音）员

武天工现摄长吴  
宽 金光  
孟尔 琴王章

第一章集合、常用逻辑用语、不等式第1讲集合、常用逻辑用语、不等式客观题

# 第二章 平面向量与复数

第1讲平面向量与复数客观题 4

# 第三章 三角函数与解三角形

第1讲 三角函数的图象与性质 …7  
第2讲三角恒等变换与解三角形…10  
第3讲三角函数与解三角形大题13

# 第四章数列

第1讲等差、等比数列的性质20  
第2讲 等差、等比数列的判断与证明…22  
第3讲 裂项相消法求和 ·… 25  
第4讲 数列中的奇偶项问题… ·30  
第5讲 数列中的最值与不等式问题…33  
第6讲数学文化与数列问题…37

20

第五章立体几何与空间向量.…  
第1讲空间几何体… …45  
第2讲空间位置关系、空间角与空间距离48  
第3讲空间几何体的外接球、内切球与棱切球… 52  
第4讲立体几何中的最值与范围问题57  
第5讲立体几何中的动点轨迹问题60  
第6讲立体几何中的向量方法…… 62  
第7讲立体几何中的折叠与展开问题69  
0  
第8讲复杂几何体的建系与设点方法73  
第9讲传统方法应用于立体几何解答题79

# 第六章统计和概率

第1讲排列组合与二项式定理84  
第2讲条件概率与全概率公式……87

CONTENTS

第3讲递推型概率问题 91  
第4讲比赛问题 95  
第5讲决策问题 100  
第6讲概率中的最值问题 107  
第7讲统计与统计案例 113  
第8讲成对数据的统计分析…121

# 第七章解析几句. 129

第1讲直线与圆的综合问题 … ……129  
第2讲圆锥曲线中常考的二级结论131  
第3讲圆锥曲线中关于点坐标的运算  
133  
第4讲特殊到一般的探索思想 …136  
第5讲隐藏型定点定值的探究 …… 139

第6讲圆锥曲线中的四点共圆 ……-142  
第7讲非对称韦达定理的处理技巧…147  
第8讲圆锥曲线中的几何关系……150  
第9讲圆锥曲线中的最值范围归类…155

# 第八章函数与数.…

第1讲函数性质的应用 161  
第2讲导数中的数形结合 164  
第3讲比较大.… 165  
第4讲函数图象中的距离最值问题…167  
第5讲导数与三角函数 …… 169  
第6讲导数与数列 174  
第7讲导数与最值· 178  
第8讲比值代换与差值代换 182

工不手面最的中我文

# ·第1讲集合、常用逻辑用语、不等式客观题···

# 【核心知识聚焦】

# 一、集合相关结论

1.若集合A中含有 $_ n$ 个元素，则有 $2 ^ { n }$ 个子 集，有 $2 ^ { n } - 1$ 个非空子集,有 $2 ^ { n } - 1$ 个真子集， 有 $2 ^ { n } - 2$ 个非空真子集.

2.子集关系的传递性，即 $A \subseteq B , B \subseteq C \Rightarrow$ $A \subseteq C$

注意：空集是任何集合的子集，是任何非空集合的真子集，在涉及集合关系时，必须优先考虑空集的情况，否则会造成漏解.

$3 . A \subseteq B \Leftrightarrow A \cap B = A \Leftrightarrow A \cup B = B \Leftrightarrow \complement _ { v } A \supseteq$ $\complement _ { v } B \Longleftrightarrow A \cap ( \complement _ { v } B ) = \emptyset .$

# 二、常用逻辑用语

1.等价转化法判断充分条件、必要条件

$\textcircled{1} p$ 是 $q$ 的充分不必要条件 $\Longleftrightarrow \lnot { q }$ 是 $\neg p$ 的充分不必要条件；$\textcircled{2} p$ 是 $q$ 的必要不充分条件 $\Longleftrightarrow \lnot q$ 是 $\neg P$ 的必要不充分条件；$\textcircled{3} p$ 是 $q$ 的充要条件 $\Leftrightarrow \neg q$ 是 $\neg P$ 的充要条件；$\textcircled{4} p$ 是 $q$ 的既不充分也不必要条件 $\Leftrightarrow \neg q$ 是 $\neg p$ 的既不充分也不必要条件.

2.集合判断法判断充分条件、必要条件

若 $p$ 以集合 $A$ 的形式出现， $q$ 以集合 $B$ 的形式出现，即 $p \colon A \ = \ \left\{ \ x \mid p \ ( \ x ) \ \right\} \ , \ q \colon$ $q \colon B \ =$ $\{ x \mid q ( x ) \}$ ,则

$\textcircled{1}$ 若 $A \subseteq B$ ,则 $P$ 是 $q$ 的充分条件； $\textcircled{2}$ 若 $B \subseteq A$ ，则 $p$ 是 $q$ 的必要条件； $\textcircled{3}$ 若 $A \subsetneq B$ ,则 $P$ 是 $q$ 的充分不必要条件； $\textcircled{4}$ 若 $B \subsetneq A$ ,则 $P$ 是 $q$ 的必要不充分条件； $\textcircled{5}$ 若 $A = B$ ,则 $p$ 是 $q$ 的充要条件； $\textcircled{6}$ 若 $A$ 不包含于 $B$ 且 $B$ 不包含于 $A$ ，则 $p$ $q$ 的既不充分也不必要条件.

# 三、不等式必记结论

$\begin{array} { l } { ( \widetilde { \mathbb { D } } a > b , a b > 0 \Rightarrow \displaystyle \frac { 1 } { a } < \displaystyle \frac { 1 } { b } ; } \\ { ( \widetilde { \mathbb { Z } } a < 0 < b \Rightarrow \displaystyle \frac { 1 } { a } < \displaystyle \frac { 1 } { b } ; } \\ { ( \widetilde { \mathbb { D } } a > b > 0 , 0 < c < d \Rightarrow \displaystyle \frac { a } { c } > \displaystyle \frac { b } { d } ; } \end{array}$ $\textcircled{4}$ 若 $0 < a < x < b$ 或 $a < x < b < 0$ ,则$\frac { 1 } { b } < \frac { 1 } { x } < \frac { 1 } { a } ;$ $\textcircled{5}$ 若 $a > b > 0 , m > 0$ ,则$\begin{array} { c } { { \displaystyle \frac { b } { a } < \frac { b + m } { a + m } ; \frac { b } { a } > \frac { b - m } { a - m } ( b - m > 0 ) ; \frac { a } { b } > } } \\ { { \displaystyle \frac { a + m } { b + m } ; \frac { a } { b } < \frac { a - m } { b - m } ( b - m > 0 ) ; } } \end{array}$ $\textcircled { 6 } \sqrt { \frac { a ^ { 2 } + b ^ { 2 } } { 2 } } \geqslant \frac { a + b } { 2 } \geqslant \sqrt { a b } \geqslant \frac { 2 } { \frac { 1 } { a } + \frac { 1 } { b } } ( a >$ $0 , b > 0 )$ ：

# 四、常见求最值模型

模型一 ${ } ; m x + { \frac { n } { x } } ; \geq 2 { \sqrt { m n } } { \big ( } m > 0 , n > 0 , x >$ 0),当且仅当 $x = { \sqrt { \frac { n } { m } } }$ 时,等号成立；

模型二： $m x + { \frac { n } { x - a } } = m \left( x - a \right) + { \frac { n } { x - a } } +$ $m a \gtrless 2 \sqrt { m n } + m a \left( m > 0 , n > 0 , x > a \right)$ ，当且仅当 $x - a = { \sqrt { \frac { n } { m } } }$ 时,等号成立；

模型三： $\frac { x } { a x ^ { 2 } + b x + c } = \frac { 1 } { a x + b + \frac { c } { x } } \leqslant$ $\frac { 1 } { 2 \sqrt { a c } + b } ( a > 0 , c > 0 , x > 0 )$ ，当且仅当 $\boldsymbol { x } =$ $\sqrt { \frac { c } { a } }$ 时，等号成立；

$x \left( n - m x \right) \ = { \frac { m x { \bigl ( } n - m x { \bigr ) } } { m } } \leqslant { \frac { 1 } { m } }$ $\left( { \frac { m x + n - m x } { 2 } } \right) ^ { 2 } \ = { \frac { n ^ { 2 } } { 4 m } } \ \left( m > 0 , n > 0 , 0 < x < { \frac { n } { m } } \right) ,$ 当且仅当 $x = \frac { n } { 2 m }$ 时,等号成立.

# $\textcircled{9}$

# 【进阶提升】

# 题型一集合的基本运算

例1已知集合 $P = \left\{ x \mid y = \ln ( x - 1 ) \right\}$ ,集合 $Q = \left\{ y \mid y = 2 ^ { x - 1 } \right\}$ ，则（）

A. $P = Q$ $\mathbf { B } . P \subsetneqq Q$   
C. $Q \subseteq P$ $\mathbf { D } . P \cap Q = \emptyset$

变式1 已知集合 $A = \left\{ x \mid 1 < 3 ^ { x } \leqslant 9 \right\}$ ，$B = \left\{ x \left| { \frac { x + 2 } { x - 2 } } \leqslant 0 \right. \right\}$ 则， $A \cap B = ( \quad )$

A.(1,2) B.(0,1) c.(0,2) D.[ -2,2)

变式2 设非空集合 $A = \{ x \mid 3 - m <$ $x < 3 + m \} , B = \left\{ x \mid \log _ { 2 } x < 2 \right\}$ ，若 $A \cup B =$ $B$ ，则实数 $m$ 的取值范围是（ ）

A $( - \infty , 3 ]$ $\mathrm { B } , ( - \infty , 1 ]$ C.(0,1] D. (0,3]

# 题型二充分条件、必要条件的判断

例2“ $\sin ^ { 2 } \alpha + \sin ^ { 2 } \beta = 1$ ”是“s $\mathrm { i n } \ \alpha + \cos \beta =$ 0"的（）

A.充分不必要条件  
B.必要不充分条件  
C.充要条件  
D.既不充分也不必要条件

变式3 已知 $m$ 是直线， $\alpha , \beta$ 是两个相互垂直的平面，则“ $m / / \alpha$ ”是“ $m \perp \beta$ ”的（

A.充分不必要条件  
B.必要不充分条件  
C.充分必要条件  
D.既不充分也不必要条件

变式4已知等差数列 $\left\{ a _ { n } \right\}$ 的公差为$d$ ，数列 $\{ b _ { n } \}$ 满足 $a _ { n } \ \cdot \ b _ { n } = 1 ( n \in \mathbf { N } ^ { * } )$ ，则$d > 0$ ”是“ $\left\{ \boldsymbol { b } _ { n } \right\}$ 为递减数列”的（）

A.充分不必要条件  
B.必要不充分条件  
C.充分必要条件  
D.既不充分也不必要条件

# 题型三不等式及其综合应用

例3已知函数 $f ( x ) = \mathrm { e } ^ { - ( x - 1 ) ^ { 2 } }$ 记 $a \ : =$ $f \left( { \frac { \sqrt { 2 } } { 2 } } \right) , b = f \left( { \frac { \sqrt { 3 } } { 2 } } \right) , c = f \left( { \frac { \sqrt { 6 } } { 2 } } \right)$ 则）

A. $b > c > a$ B. $b > a > c$ C. $c > b > a$ D $\because c > a > b$

变式5 若命题“ $\exists a \in \left[ \ - 1 , 3 \right] , a x ^ { 2 } -$ $\left( 2 a - 1 \right) x + 3 - a < 0 ^ { , }$ 为假命题，则实数 $x$ 的取值范围为（

A.[ -1,4] $\mathbf { \beta } _ { \cdot } \left[ 0 , \frac { 5 } { 3 } \right]$   
$\begin{array} { r } { \mathrm { ~ C . ~ } [ { \bf { \sigma } } - 1 , 0 ] \cup \left[ \frac { 5 } { 3 } , 4 \right] } \\ { { \bf { \sigma } } } \\ { { \bf { D . \sigma } } [ { \bf { \sigma } } - 1 , 0 ) \cup \left( \frac { 5 } { 3 } , 4 \right] } \end{array}$

例4（多选题)已知 $a > 0 , b > 0$ ,且 $a b =$ $\frac 1 4$ ，则下列不等关系成立的是（ ）

$\mathbf { A } . { \frac { 1 } { a } } + { \frac { 1 } { b } } \geq 4$ （$\mathbf { B } . { \sqrt { a } } + { \sqrt { b } } \geq { \sqrt { 2 } }$ $\therefore \log _ { 2 } a \ \cdot \ \log _ { 2 } b \leqslant 1$ Da+1nb≥2-n2变式6（多选题）已知 $x ^ { 2 } ( y ^ { 2 } + 1 ) = 1$ 则

$$
\begin{array} { l } { { \displaystyle \mathrm { A } . \ x y < 1 \qquad \mathrm { S O } \quad \mathbf { B } . \ x ^ { 2 } y \geqslant - \frac { 1 } { 2 } } } \\ { { \displaystyle \mathrm { C } . \ x + x y \leqslant 1 \qquad \mathrm { D } . \ x ^ { 2 } + x y \leqslant \frac { 5 } { 4 } } } \end{array}
$$

变式7（多选题）设 $a , b , c$ 均为正数，且 $a ^ { 2 } + b ^ { 2 } + 4 c ^ { 2 } = 1$ ，则（s）（量向单）

A. $a b + 2 b c + 2 c a \leqslant 1$   
B.当 $a > \frac { \sqrt { 6 } } { 6 }$ 时 $a = b = c$ 可能成立C $\cdot a b < \frac { 1 } { 2 }$ ：医$\mathrm { D } . { \frac { 1 } { a ^ { 2 } } } + { \frac { 1 } { b ^ { 2 } } } + { \frac { 1 } { 4 c ^ { 2 } } } \geq 9$

例5根据不同的程序,3D打印既能打印实心的几何体模型,也能打印空心的几何体模型.如图所示的空心模型是体积为 ${ \frac { 1 7 \ { \sqrt { 1 7 } } } { 6 } } \pi \ \mathrm { c m } ^ { 3 }$ 的球挖去一个三棱锥 $P – A B C$ 后得到的几何体,其中$P A \perp A B$ ， $B C \bot$ 平面 $P A B$ ， $B C = 1 \ \mathrm { c m } .$ 不考虑打印损耗,求当用料最省时， $A C$ 的长度.

![](images/5c78cc1ccb30f11f05fde26331cf07aa207b08c02916d260352dfb1bab3d2c76.jpg)

变式8 在 $\triangle A B C$ 中，点 $P$ 满足 $B P =$ $2 P C$ ，过点 $P$ 的直线与 $A B$ ， $A C$ 所在的直线分别交于点 $M , N$ ，若 $\overrightarrow { A B } = \lambda \overrightarrow { A M } , \overrightarrow { A C } = \mu \overrightarrow { A N }$ $( \lambda > 0 , \mu > 0 )$ ，则 $\frac { 1 } { \lambda } + \frac { 1 } { \mu }$ 的最小值为

![](images/cbcf1ca5bfeeddd4e39469039c18ba7b223033414c0450df6f345a18f4202ca9.jpg)

A.1+2√2 B.1+23 3   
C. 2√2 D.2√3 3

# 【核心知识聚焦】

# 一、平面向量

1.平面向量数量积的坐标表示、模、夹角及性质

设非零向量 $\pmb { a } = \left( x _ { 1 } , y _ { 1 } \right)$ $\pmb { \mathrm { . } } b = \left( x _ { 2 } , y _ { 2 } \right) , \theta$ 是 $\pmb { a }$ 与 $\pmb { b }$ 的夹角.

(1)数量积： $ \mathbf { \partial } \cdot { \pmb { a } } \cdot { \pmb { b } } = { \left| \begin{array} { l } { { \pmb { a } } } \end{array} \right| } \left| \begin{array} { l } { { \pmb { b } } } \end{array} \right| \mathbf { c o s } { \pmb { \theta } } = \mathbf { x } _ { 1 } \mathbf { x } _ { 2 } +$ $\mathcal { Y } _ { 1 } \mathcal { Y } _ { 2 }$

(2）模： $\left| \mathbf { \sigma } \right| = { \sqrt { \mathbf { a } \cdot \mathbf { a } } } = { \sqrt { x _ { 1 } ^ { 2 } + y _ { 1 } ^ { 2 } } }$

(3)夹角:cos $\theta = { \frac { \pmb { a } \cdot \pmb { b } } { | \pmb { a } | | \ | \pmb { b } | } } =$ $\frac { x _ { 1 } x _ { 2 } + y _ { 1 } y _ { 2 } } { \sqrt { { x _ { 1 } } ^ { 2 } + { y _ { 1 } } ^ { 2 } } \cdot \sqrt { { x _ { 2 } } ^ { 2 } + { y _ { 2 } } ^ { 2 } } } .$

(4)垂直与平行 $: a \perp b \ominus a \cdot b = 0 \Leftrightarrow x _ { 1 } x _ { 2 } +$ $y _ { 1 } y _ { 2 } = 0 ; a / / b \Longleftrightarrow a \cdot b = \pm \mid a \mid \mid b \mid ,$

注：当向量 $\pmb { a }$ 与 $\pmb { b }$ 同向时， ${ \pmb a } \cdot { \pmb b } = \left| { \pmb a } \right| \left| { \pmb b } \right|$ 当向量 $\pmb { a }$ 与 $\pmb { b }$ 反向时， ${ \pmb a } \cdot { \pmb b } = - \left| { \pmb a } \right| \left| { \pmb b } \right|$

(5)性质： $| \textbf { \em a } \cdot \textbf { \em b } | \leqslant | \textbf { \em a } | | \textbf { \em b } |$ （当且仅当${ \pmb a } / / { \pmb b }$ 时，等号成立） $\iff \mid x _ { 1 } x _ { 2 } \ + \ y _ { 1 } y _ { 2 } \ \mid \ \leq$ ${ \sqrt { { x _ { 1 } } ^ { 2 } + { y _ { 1 } } ^ { 2 } } } \cdot { \sqrt { { x _ { 2 } } ^ { 2 } + { y _ { 2 } } ^ { 2 } } } .$

2.常见的向量表示形式

(1)重心.若点 $G$ 是 $\triangle A B C$ 的重心，则$\overrightarrow { G A } + \overrightarrow { G B } + \overrightarrow { G C } = { \bf 0 }$ 或 $\overrightarrow { P G } = \frac { 1 } { 3 } ( \overrightarrow { P A } + \overrightarrow { P B } + \overrightarrow { P C } )$ （其中 $P$ 为平面内任意一点).反之,若 ${ \overrightarrow { G A } } + { \overrightarrow { G B } } +$ $\stackrel { \longrightarrow } { G C } = \pmb { 0 }$ ,则点 $G$ 是 $\triangle A B C$ 的重心.

(2)垂心.若 $H$ 是 $\triangle A B C$ 的垂心，则 $\overrightarrow { H A }$ · $\overrightarrow { H B } = \overrightarrow { H B } \cdot \overrightarrow { H C } = \overrightarrow { H C } \cdot \overrightarrow { H A } .$ 反之，若 ${ \overrightarrow { H A } } \cdot { \overrightarrow { H B } } =$ $\overrightarrow { H B } \cdot \overrightarrow { H C } = \overrightarrow { H C } \cdot \overrightarrow { H A }$ ，则点 $H$ 是 $\triangle A B C$ 的垂心.

(3）内心.若点 $I$ 是 $\triangle A B C$ 的内心，则 $| \overrightarrow { B C } | \overrightarrow { I A } + \mid \overrightarrow { C A } \mid \overrightarrow { I B } + \mid \overrightarrow { A B } \mid \overrightarrow { I C } = { \bf 0 } .$ 反之，若 $\mid \overrightarrow { B C } \mid \overrightarrow { I A } + \mid \overrightarrow { C A } \mid \overrightarrow { I B } + \mid \overrightarrow { A B } \mid \overrightarrow { I C } = { \bf 0 }$ ，则点 $I$ 是 $\triangle A B C$ 的内心.

(4)外心.若点 $O$ 是 $\triangle A B C$ 的外心，则$( \overrightarrow { O A } + \overrightarrow { O B } ) \cdot \overrightarrow { B A } = ( \overrightarrow { O B } + \overrightarrow { O C } ) \cdot \overrightarrow { C B } = ( \overrightarrow { O C } +$ $\stackrel { \longrightarrow } { A C } = \mathbf { 0 }$ 或 $\mid { \overrightarrow { O A } } \mid = \mid { \overrightarrow { O B } } \mid = \mid { \overrightarrow { O C } } \mid$ .反之，若 $\vert \overrightarrow { O A } \vert = \vert \overrightarrow { O B } \vert = \vert \overrightarrow { O C } \vert$ ,则点 $o$ 是 $\triangle A B C$ 的外心

(5)投影向量：向量 $\pmb { a }$ 在向量 $\pmb { b }$ 上的投影向量等于 $| \textbf { \em a } | \cos \theta \textbf { \em e }$ （其中 $^ e$ 为与 $\pmb { b }$ 方向相同的单位向量),即 $| \stackrel {  } { \pmb { a } } | \cos \theta \stackrel {  } { \pmb { e } } = | \pmb { a } | \cos \theta \cdot \frac { \pmb { b } } { | \pmb { b } | } =$ ${ \frac { a \cdot b } { | b | } } \cdot { \frac { b } { | b | } }$

3.极化恒等式

(1)平行四边形性质：如下图所示，在平行四边形ABCD 中, $\vert A C \vert ^ { 2 } + \vert B D \vert ^ { 2 } = 2$ （ $\left| A B \right| ^ { 2 } +$ $| A D | ^ { 2 } )$ ：

![](images/b9743886f1bd14afd54a2459de9ded2854263d213dd7c8c61694009c43fbace0.jpg)

(2)极化恒等式的平行四边形模式：在平行四边形 ABCD 中， ${ \overrightarrow { A B } } \cdot { \overrightarrow { A D } } = { \frac { 1 } { 4 } } ( \mid A C \mid ^ { 2 } -$ （204$\mid B D \mid ^ { 2 } )$ ：

(3)极化恒等式的三角形模式： ${ \overrightarrow { A B } } \cdot { \overrightarrow { A D } } =$ $\mid A E \mid ^ { 2 } - \mid E B \mid ^ { 2 }$ ,其中 $E$ 为 $B D$ 中点.

提醒：极化恒等式主要用于解决数量积计算问题，利用极化恒等式，关键是取中点，巧妙之处是可将本身需要夹角才能计算的数量积转化为只需长度即可计算的量.

# 二、复数常用结论

$$
\textcircled { 1 } ( 1 \pm \mathrm { i } ) ^ { 2 } = \pm 2 \mathrm { i } , \frac { 1 + \mathrm { i } } { 1 - \mathrm { i } } = \mathrm { i } , \frac { 1 - \mathrm { i } } { 1 + \mathrm { i } } = \textrm { - } \mathrm { i } .
$$

②-b+ai=i(a+bi).

$\begin{array} { r } { \bigotimes _ { { \bf i } } ^ { - } { \bf \sigma } ^ { _ { \vee } } = 1 , { \bf i } ^ { 4 n + 1 } = { \bf i } , { \bf i } ^ { 4 n + 2 } = - 1 , { \bf i } ^ { 4 n + 3 } = - { \bf i } } \\ { \bigotimes _ { { \bf i } } ^ { 4 n } = { \bf N } ^ { \ast } { \bf \sigma } \mathrm { ~ ; ~ } { \bf i } ^ { 4 n + 1 } + { \bf i } ^ { 4 n + 2 } + { \bf i } ^ { 4 n + 3 } = 0 \mathrm { ~ ( ~ } n \in \partial \Omega \mathrm { ~ ) ~ } } \end{array}$ $\mathbf { N } ^ { * } )$ ：

$( \underline { { { \widehat { \bf 4 } } } } ) z \cdot \bar { z } = \left| z \right| ^ { 2 } = \mid \bar { z } \mid ^ { 2 } , \mid z _ { 1 } \cdot z _ { 2 } \mid \ = \mid z _ { 1 } \mid \ .$ $\mid z _ { 2 } \mid , \left| { \frac { z _ { 1 } } { z _ { 2 } } } \right| = { \frac { \mid z _ { 1 } \mid } { \mid z _ { 2 } \mid } } , \mid z ^ { n } \mid \ = \mid z \mid ^ { n } .$

# 【进阶提升】

# 题型一 投影向量

例1已知在 $\triangle A B C$ 中， $o$ 为 $B C$ 的中点，且 $\mid \overrightarrow { B C } \mid = 4$ ,|AB+AC|=|AB-AC|, $\angle A C B =$ $\frac { \pi } { 6 }$ ，则向量 $\overrightarrow { A O }$ 在向量 $\overrightarrow { A B }$ 上的投影向量为（）

A B丽 C.AB D.AB

变式1 已知平面向量 ${ \textbf { \em a } } , { \textbf { \em b } }$ 满足$\left| \mathbf { a } \right| = \sqrt { 3 } , \pmb { b } = ( 1 , \sqrt { 3 } ) , \left| \pmb { a } - 2 \pmb { b } \right| = \sqrt { 1 1 }$ ，则 $\pmb { a }$ 在 $\pmb { b }$ 上的投影向量为（）

$$
\begin{array} { l l } { { \displaystyle \mathrm { A . } \left( \frac { \sqrt { 3 } } { 2 } , \frac { 3 } { 2 } \right) } } & { { \qquad \mathrm { B . } \left( \frac { 1 } { 2 } , \frac { \sqrt { 3 } } { 2 } \right) } } \\ { { \displaystyle \mathrm { C . } \left( 1 , \sqrt { 3 } \right) } } & { { \qquad \mathrm { D . } \left( \frac { \sqrt { 6 } } { 2 } , \frac { 3 \sqrt { 2 } } { 2 } \right) } } \end{array}
$$

# 题型二向量的数量积运算

例2在 $\triangle A B C$ 中，内角 $A , B , C$ 所对的边分别为 $a , b , c$ ，且 $b = 6 , c = 4$ ，点 $O$ 为外心，则$\overrightarrow { A O } \cdot \overrightarrow { B C } = ( \qquad )$

A.-20 B.-10   
C.10 D.20

变式2 窗花是贴在窗纸或窗户玻璃上的前纸，它是中国古老的传统民间艺术之一、在2022年虎年新春来临之际，人们设计了一种由外围四个大小相等的半圆和中间正方形所构成的剪纸窗花（如图1）.已知正方形ABCD的边长为2，中心为 $o$ ，四个半圆的圆心均为正方形ABCD各边的中点(如图2)，若 $P$ 是 $\widehat { B C }$ 的中点，则 $\overrightarrow { P A } +$ PB)·PO=

![](images/eed08d33422a5124f89f498b35187abfc7798d329c09618d82a9fabf1192232c.jpg)  
图1

![](images/3d8ef202bc6f388a704f7ec3fe6a759fb3053347924fad96539f291851d3cc78.jpg)  
图2

例3 (多选题)设 $\pmb { e } _ { 1 } , \pmb { e } _ { 2 }$ 为单位向量,满足 $\mid 2 e _ { 1 } - 3 e _ { 2 } \mid \leqslant { \sqrt { 1 1 } } , a = e _ { 1 } + e _ { 2 } , b = 3 e _ { 1 } +$ $2 e _ { 2 }$ ,设 ${ \pmb a } , { \pmb b }$ 的夹角为 $\theta$ ，下列说法正确的是（）

$\mathbf { A } . { \boldsymbol { e } } _ { 1 } \cdot { \boldsymbol { e } } _ { 2 } \geq { \frac { 1 } { 6 } }$ B. $| \textbf { \em a } |$ 的最小值为2 C.cos²θ最小值为35

D.当 $x \neq 1$ 时，使方程 $\left| \pmb { a } + \pmb { b } \right| = \left| \pmb { a } + \pmb { x } \pmb { b } \right|$ 成立的 $_ x$ 一定是负数

变式3 已知直角梯形ABCD， $A = 9 0 ^ { \circ }$ $A B / / C D$ ， $A D = D C = \frac 1 2 A B = 1 ,$ $P$ 是 $B C$ 边上的一点，则 ${ \overrightarrow { A P } } \cdot { \overrightarrow { P C } }$ 的取值范围为（

![](images/5717d8b35c4c705c05dbcb19d55ff08c6f79604ad29ae64af65dc9f52b0457f3.jpg)

A.[-1,1] B.[0,2] C.[-2,2] D.[-2,0]

# 题型三极化恒等式的应用

例4已知 $A B$ 是圆 $O$ 的直径， $\mid A B \mid = 4$ ，C是圆 $o$ 上异于 $^ { A , B }$ 的一点， $\mathcal { P }$ 是圆 $o$ 所在平面内的任意一点,则 $( \overrightarrow { P A } + \overrightarrow { P B } ) \cdot \overrightarrow { P C }$ 的最小值是

![](images/0816b8d9197e4de6ff9e4923f70b27e7df65e8fbe4272c693896177c3414dcd9.jpg)

变式4 若 $o$ 和 $F$ 分别是椭圆 $\frac { x ^ { 2 } } { 4 } + \frac { y ^ { 2 } } { 3 }$ $= 1$ 的中心和左焦点， $P$ 为椭圆上一点，则$\overrightarrow { O P } \cdot \overrightarrow { F P }$ 的最大值是（

A.2B.3C.6D.8

![](images/fb5b99130fdd9df53baee94479440a3b4f2f9ab25305a12ee3fdca03d03db19d.jpg)

例5(多选题)在复平面内,复数 $z \ =$ $\frac { 2 } { 1 + { \sqrt { 3 } } \mathrm { i } }$ ,下列说法正确的是（ ）

A.复数 $\bar { z }$ 的模为1 B.复数 $z$ 在复平面内对应的点在第二 象限 C.复数 $z$ 是方程 $x ^ { 2 } - x + 1 = 0$ 的解 D.复数 $\omega$ 满足 $\mid \omega - z \mid = 1$ ,则 $| \omega | _ { \mathsf { m a x } } =$ $\sqrt { 2 } + 1$

变式5 （多选题）已知复数 $z$ 满足$\left| z \right| = \left| z - 1 \right| = 1$ ，且复数 $z$ 对应的点在第一象限，则下列结论正确的是（

A.复数 $z$ 的虚部为 $\frac { \sqrt { 3 } } { 2 } \mathrm { i }$   
B. $\left| \mathbf { \nabla } z \right| ^ { 2 } = z \cdot \bar { z }$   
$C . z ^ { 2 } = z - 1$   
D.复数 $z$ 的共轭复数为 $\frac { 1 } { 2 } - \frac { \sqrt { 3 } } { 2 } \mathrm { i }$

变式6 （多选题)已知复数 $z _ { 1 } = - 2 + i$ （i为虚数单位），复数 $z _ { 2 }$ 满足 $\left| z _ { 2 } - 1 + 2 \mathrm { i } \right| =$ ${ 2 , z _ { 2 } }$ 在复平面内对应的点为 $M ( x , y )$ ，则

A.复数 $z _ { 1 }$ 在复平面内对应的点位于第二象限$\mathbf { B } . \frac { 1 } { z _ { 1 } } = - \frac { 2 } { 5 } - \frac { 1 } { 5 } \mathrm { i }$ $\operatorname { C } . \left( x + 1 \right) ^ { 2 } + \left( y - 2 \right) ^ { 2 } = 4$ D. $\mid z _ { 2 } - z _ { 1 } \mid$ 的最大值为 $3 \sqrt { 2 } + 2$

# 第三章三角函数与解三角形

# 第1讲 三角函数的图象与性质

# 【核心知识聚焦】

# 一、正弦、余弦、正切函数的图象与性质(下表中 $k \in \mathbf { Z }$ ）

<html><body><table><tr><td>函数</td><td>y = sin x</td><td>y=cos x</td><td>y=tanx</td></tr><tr><td>图象</td><td>π2 1 0 =2 T</td><td>1 0 -1</td><td>2 0 x</td></tr><tr><td>定义域</td><td>R</td><td>R</td><td>{1xeR，xπ+2}</td></tr><tr><td>值域</td><td>[-1,1]</td><td>[-1,1]</td><td>R</td></tr><tr><td>周期性</td><td>2π</td><td>2π</td><td>T</td></tr><tr><td>奇偶性</td><td>奇函数</td><td>偶函数</td><td>奇函数</td></tr><tr><td>单调递增区间</td><td>[2π，2+]</td><td>[-π+2kπ,2kπ]</td><td>(-，+）</td></tr><tr><td>单调递减区间</td><td>[2kπ +,2π+]</td><td>[2kπ,π+2hπ]</td><td>无</td></tr><tr><td>对称中心</td><td>(kπ,0）</td><td>（+，0）</td><td>（，0）</td></tr><tr><td>对称轴方程</td><td>=+</td><td>x=kπ</td><td>无</td></tr></table></body></html>

注:正(余)弦曲线相邻两条对称轴之间的距离是 $\cdot \frac { T } { 2 }$ ;正(余)弦曲线相邻两个对称中心的距离是 $\cdot \frac { T } { 2 }$ ;正(余)弦曲线相邻的对称轴与对称中心之间的距离是 $\frac { T } { 4 }$ 4

# 二、函数 $\gamma = A \sin \left( \omega x + \varphi \right)$ 的图象的画法

1.变换作图法

由函数 $\gamma = \sin \ x$ 的图象通过变换得到 $\gamma = A \sin \left( \omega x + \varphi \right)$ $A > 0 , \omega > 0$ )的图象,有两种主要途径：“先平移后伸缩”与“先伸缩后平移”.如下图.

画出=sinx的图象 步骤1 画出 $\scriptstyle \gamma = \sin x$ 的图象向左(右)平移 $| \varphi |$ 个单位长度 横坐标变为 原来的 $\frac { 1 } { \omega }$ 倍得到 $\scriptstyle  \sin ( x + \varphi )$ 的图象 步骤2 得到y=sinox的图象横坐标变为 原来的 $\frac { 1 } { \omega }$ 倍 向左(右)平移 $\big | \frac { \varphi } { \omega } \big |$ 个单位长度得到 $\scriptstyle \gamma = \sin \left( \omega x + \varphi \right)$ 的图象 步骤3 得到 $\scriptstyle \gamma = \sin ( \omega x + \varphi )$ 的图象纵坐标变为 原来的A倍 纵坐标变为|原来的A倍得到 $\scriptstyle \gamma = A \sin ( \omega x + \varphi )$ 的图象 步骤4 得到 $\scriptstyle \gamma = A \sin ( \omega x + \varphi )$ 的图象

注：若是不同函数名之间的图象变换，则先用诱导公式将其化为同名函数，再用平移、伸缩规则.

(1)sin 化 cos: sin $x = \cos \left( { \frac { \pi } { 2 } } - x \right)$ $x =$ $- \cos ( x + \frac { \pi } { 2 } )$ ；(2)cos化 $\sin$ : cos $x = \sin \left( { \frac { \pi } { 2 } } + x \right)$ ,cos $x =$ $\sin \left( { \frac { \pi } { 2 } } - x \right)$

2.五点作图法

找五个关键点，分别为使 $y$ 取得最小值、最大值的点和曲线与 $_ x$ 轴的交点.其步骤为：

$\textcircled{1}$ 先确定最小正周期 $T = \frac { 2 \pi } { \omega }$ ,在一个周期内作出图象；

$\textcircled{2}$ 令 $X = \omega x + \varphi$ ，令 $X$ 分别取 $0 , \frac { \pi } { 2 } , \pi , \frac { 3 \pi } { 2 }$ $2 \pi$ ,求出对应的 $_ { x }$ 值,列表如下：

<html><body><table><tr><td>X =wx +φ</td><td>0</td><td>=2</td><td>T</td><td>3</td><td>2π</td></tr><tr><td>x</td><td></td><td></td><td>=-</td><td>3</td><td>2π-</td></tr><tr><td>y =Asin( ωx +4)</td><td>0</td><td>A</td><td>0</td><td>-A</td><td>0</td></tr></table></body></html>

由此可得五个关键点；$\textcircled{3}$ 描点画图，再利用函数的周期性把所得简图向左右分别扩展，从而得到 $\ y = A \sin \left( \omega x + \right.$ $\varphi$ ）的简图.

# 三、三角函数对称性的常用结论

1.正弦型函数 $\gamma = A \sin \left( \omega x + \varphi \right)$ 对称轴： $\omega x + \varphi = k \pi + { \frac { \pi } { 2 } } \Rightarrow x = { \frac { 1 } { \omega } } \left( { k \pi + { \frac { } { } } } \right.$ ${ \frac { \pi } { 2 } } - \varphi ) ( k \in { \bf Z } ) ;$ 对称中心横坐标： $\omega x + \varphi = k \pi \Longrightarrow x = { \frac { k \pi - \varphi } { \omega } }$ ${ \bf \Xi } ( k \in { \bf Z } )$ ：2.余弦型函数 $\gamma = A \cos \left( \omega x + \varphi \right)$ 对称轴： $\omega x + \varphi = k \pi { \Longrightarrow } x = \frac { k \pi - \varphi } { \omega } \big ( k \in \mathbf { Z } \big )$ 对称中心横坐标： $\omega x + \varphi = k \pi + \frac { \pi } { 2 } \Longrightarrow x =$ ${ \frac { 1 } { \omega } } \left( k \pi + { \frac { \pi } { 2 } } - \varphi \right) ( k \in { \bf Z } ) .$

3.正切型函数 $y = \tan ( \omega x + \varphi )$ 对称中心横坐标: $\omega x \ + \ \varphi \ = { \frac { k \pi } { 2 } } \Rightarrow x \ =$ ${ \frac { 1 } { \omega } } \left( { \frac { k \pi } { 2 } } - \varphi \right) ( k \in { \bf Z } ) .$

# 四、三角函数周期性的常用结论

1.求周期很多时侯考的是三角函数式的化简，只要化简成下面的三种形式，周期就能求出来.

$\begin{array} { l } { \displaystyle \textcircled { 1 } y = A \sin \left( \omega x + \varphi \right) + B { \Longrightarrow } T = \frac { 2 \pi } { | \omega | } ; } \\ { \displaystyle \textcircled { 2 } y = A \cos \left( \omega x + \varphi \right) + B { \Longrightarrow } T = \frac { 2 \pi } { | \omega | } ; } \\ { \displaystyle \textcircled { 3 } y = A \tan \left( \omega x + \varphi \right) + B { \Longrightarrow } T = \frac { \pi } { | \omega | } . } \end{array}$ 2. $y = \mid A \sin ( \omega x + \varphi ) \mid$ 和 $y = \mid A \cos \mid \omega x +$ $\varphi ) \mid$ 的最小正周期为 |,y = 丨Atan(ωx +$\varphi ) \ |$ 的最小正周期为 $\frac { \pi } { \mid { \boldsymbol { \omega } } \mid }$

# $\textcircled{9}$ 【进阶提升】

# 题型一 三角函数的图象与解析式

例1 (多选题)将函数 $f ( x ) = 2 \ \sin \left( 2 x - \right.$ ${ \begin{array} { l } { { \frac { \pi } { 6 } } } \end{array} } )$ 的图象向左平移 $\theta \left( \theta > 0 \right) ,$ 个单位长度，得到函数 $g \left( x \right)$ 的图象，下列说法正确的是（）

A.当 $\theta = \frac { 5 \pi } { 6 }$ 5时，g（x）为偶函数B.当 $\theta = \frac { 5 \pi } { 6 }$ 髙时 $\boldsymbol { g } ( \boldsymbol { x } )$ 在区间 $\left[ 0 , { \frac { \pi } { 3 } } \right]$ 上单调递减C.当 $\theta = \frac { \pi } { 4 }$ 时 $g ( x )$ 在区间 $\left[ { \begin{array} { l } { - { \frac { \pi } { 6 } } , { \frac { \pi } { 6 } } } \end{array} } \right]$ 上的值域为 $[ 0 , { \sqrt { 3 } } ]$ D.当 $\theta = \frac { \pi } { 4 }$ 时，点 $\left( \ - { \frac { \pi } { 6 } } , 0 \right)$ 是 $g ( x )$ 的图象的一个对称中心

变式1 （多选题）已知 $f ( x ) = A \sin ( \omega x$ $^ { + } \varphi ) \left( A > 0 , \omega > 0 \right)$ ， $- \frac { \pi } { 2 } < \varphi < \frac { \pi } { 2 } )$ 的部分图象如图所示，则（ ）

![](images/af446c6e141e57c24dedc2c1977f0a9481fa9109967fbe86fcf0d3745570bfc7.jpg)

$\mathrm { A } . f ( x )$ 的最小正周期为 $\pi$ B.当 $x \in \left[ \begin{array} { l } { - \frac { \pi } { 4 } , \frac { \pi } { 4 } } \end{array} \right]$ 时 $f ( x )$ 的值城为$\left[ - \frac { \sqrt { 3 } } { 2 } , \frac { \sqrt { 3 } } { 2 } \right]$ C.将函数 $\mathcal { f } ( \boldsymbol { x } )$ 的图象向右平移 $\frac { \pi } { 1 2 }$ 单位长度可得函数 $\begin{array} { r } { g ( x ) = \sin 2 x } \end{array}$ 的图象D.将函数 $f ( x )$ 的图象上所有点的横坐标伸长为原来的2倍，纵坐标不变，得到的函数图象关于点 $\left( { \frac { 5 \pi } { 6 } } , 0 \right)$ 对称

# 题型二 三角函数的性质

例2函数 $f ( x ) = { \frac { \tan x } { 1 + \tan ^ { 2 } x } }$ 的最小正周期为（）

$$
\mathrm { A . ~ } \frac { \pi } { 4 } \qquad \mathrm { B . ~ } \frac { \pi } { 2 } \qquad \mathrm { C . ~ } \pi \qquad \mathrm { D . ~ } 2 \pi
$$

变式2已知函数 $\begin{array} { r } { f ( \ x ) \ = 2 \ \cos ^ { 2 } x \ - } \end{array}$ $\sin ^ { 2 } x + 2$ ，则（

$\operatorname { A } . f ( x )$ 的最小正周期为 $\pi$ ，最大值为3  
$\mathrm { B } . \boldsymbol { \mathscr { J } } ( \boldsymbol { x } )$ 的最小正周期为 $\pi$ ，最大值为4  
$\mathrm { C } . \mathrm { \nabla } / ( \boldsymbol { x } )$ 的最小正周期为 $2 \pi$ ，最大值为3  
D. $f ( x )$ 的最小正周期为 $2 \pi$ ，最大值为4

变式3 设函数 $\begin{array} { r l } { \int \left( \begin{array} { l } { x } \end{array} \right) } & { { } = } \end{array}$ sin $3 x +$ $\lvert \sin { 3 x }$ 1，则 $f ( x )$ 为（）

A.周期函数，最小正周期为 $\frac { \pi } { 3 }$ B.周期函数，最小正周期为 $\frac { 2 \pi } { 3 }$

C.周期函数，最小正周期为 $2 \pi$ D.非周期函数

例3已知函数 $f ( x ) = 2 \cos \left( \omega x + \varphi \right) ( \omega >$ $0 , | \varphi | < \pi \rangle$ 的部分图象如图所示，若 $\mathcal { f } ( x _ { 1 } ) =$ ${ \mathcal { f } } ( x _ { 2 } ) = { \sqrt { 3 } } \left( x _ { 1 } \neq x _ { 2 } \right)$ ，则 $\mid x _ { 1 } \mid - x _ { 2 } \mid$ 的最小值为

![](images/7620e12cc447bc2bf142bfe0425b1885c9449952c235b717831d51120e52b0fd.jpg)

变式4 $f ( x ) = \sin \mid x \mid - { \sqrt { 3 } } \cos x$ ，若关電于 $x$ 的方程 $\textstyle f ( x ) = m$ 在区间 $\left[ - 2 \pi , \frac { 4 \pi } { 3 } \right] \pm$ 有三个不同的实根，则实数 $m$ 的取值范围是

例4已知函数 $f ( x ) = \cos { ( \omega x - \frac { \pi } { 3 } ) } ( \omega >$ 0）在区间 $\left[ { \frac { \pi } { 6 } } , { \frac { \pi } { 4 } } \right]$ 上单调递增，且当 $\qquad x \in$ $\left[ { \frac { \pi } { 4 } } , { \frac { \pi } { 3 } } \right]$ $\mathcal { I } ( x ) \geq 0$ 恒成立，则 $\omega$ 的取值范围

# 【核心知识聚焦】

# 一、两角和与差的正余弦与正切

$$
\begin{array} { r l } & { \displaystyle \mathbb { O } \mathrm { s i n } ( \alpha \pm \beta ) = \mathrm { s i n } \alpha \cos \beta \pm \cos \alpha \sin \beta } \\ & { \displaystyle \mathbb { O } \mathrm { c o s } ( \alpha \pm \beta ) = \cos \alpha \cos \beta \mp \sin \alpha \sin \beta ; } \\ & { \displaystyle \mathbb { O } \mathrm { t a n } ( \alpha \pm \beta ) = \frac { \mathrm { t a n } \alpha \pm \mathrm { t a n } \beta } { 1 \mp \mathrm { t a n } \alpha \tan \beta } . } \end{array}
$$

为（ ）

$$
\begin{array} { l l } { { \Lambda . \left( 0 , \displaystyle \frac { 5 } { 2 } \right) \cup \left[ \frac { 2 2 } { 3 } , \displaystyle \frac { 1 7 } { 2 } \right] } } & { { \mathrm { \normalfont ~ B . } \left( 0 , \displaystyle \frac { 4 } { 3 } \right] \cup \left[ 8 , \displaystyle \frac { 1 7 } { 2 } \right] } } \\ { { \mathrm { \normalfont ~ C . } \left( 0 , \displaystyle \frac { 4 } { 3 } \right) \cup \left[ 8 , \displaystyle \frac { 2 8 } { 3 } \right] } } & { { \mathrm { \normalfont ~ D . } \left( 0 , \displaystyle \frac { 5 } { 2 } \right] \cup \left[ \displaystyle \frac { 2 2 } { 3 } , 8 \right] } } \end{array}
$$

变式5 若函数 $f ( x ) = \sin ( \omega x + \frac { \pi } { 6 } )$ $( \omega > 0$ ）在 $\left[ 0 , \pi \right]$ 上有且仅有3个零点和2个极小值点，则 $\omega$ 的取值范围为

例5设 $0 < \alpha < \pi$ ，函数 $f ( x ) = \sin { ( x + \alpha ) }$ （ $0 < x < \pi ,$ 满足 $x f ( x ) \leqslant \alpha$ sin $2 \alpha$ ，则 $\alpha$ 落于区间（）

$$
\begin{array} { l l l } { { \mathrm { A . ~ } \left( 0 , \displaystyle \frac { 1 } { 2 } \right) ~ } } & { { ~ } } & { { ~ \mathrm { B . ~ } \left( \displaystyle \frac { 1 } { 2 } , 1 \right) } } \\ { { \mathrm { C . ~ } \left( 1 , \displaystyle \frac { 3 } { 2 } \right) ~ } } & { { ~ } } & { { ~ \mathrm { D . ~ } \left( \displaystyle \frac { 3 } { 2 } , 2 \right) } } \end{array}
$$

变式6 设 $a = \frac { \pi } { 6 } , b = \cos \mathrm { ~ l ~ } , c = \sin \mathrm { ~ \frac { 1 } { 3 } ~ }$ 这三个数的大小关系为（

$$
\begin{array} { l l } { \Lambda . \mathrm { ~ } a < b < c \qquad } & { \mathrm { B . ~ } c < b < a } \\ { \qquad } & { \mathrm { C . ~ } c < a < b \qquad } & { \mathrm { D . ~ } a < c < b } \end{array}
$$

# 二、二倍角公式

$\textcircled { 1 } \sin 2 \alpha = 2 \sin \alpha \cos \alpha ;$ $\textcircled { 2 } \mathrm { c o s } \ 2 \alpha = \mathrm { c o s } ^ { 2 } \alpha \ : - \ : \mathrm { s i n } ^ { 2 } \alpha = 2 \ \mathrm { c o s } ^ { 2 } \alpha \ : - \ : 1 \ : =$ $1 - 2 \sin ^ { 2 } \alpha$ $\textcircled { 3 } \tan 2 \alpha = \frac { 2 \tan \alpha } { 1 - \tan ^ { 2 } \alpha } .$

# 三、降次(幂）公式

sinα cos $\alpha = \frac { 1 } { 2 } \sin 2 \alpha$ ; $\begin{array} { l } { \sin ^ { 2 } \alpha = \displaystyle \frac { 1 - \cos 2 \alpha } { 2 } ; } \\ { \cos ^ { 2 } \alpha = \displaystyle \frac { 1 + \cos 2 \alpha } { 2 } . } \end{array}$

# 四、半角公式

$\sin { \frac { \alpha } { 2 } } = \pm { \sqrt { \frac { 1 - \cos \alpha } { 2 } } } ;$ co $\mathrm { ~ s ~ } \frac { \alpha } { 2 } = \pm \sqrt { \frac { 1 + \cos \alpha } { 2 } } ;$ tan ${ \frac { \alpha } { 2 } } = { \frac { \sin \alpha } { 1 + \cos \alpha } } = { \frac { 1 - \cos \alpha } { \sin \ a } } .$

# 五、辅助角公式

asin α + b $\cos \alpha = { \sqrt { a ^ { 2 } + b ^ { 2 } } } \sin ( \alpha + \varphi )$ （其中sin= $\varphi = { \frac { b } { \sqrt { a ^ { 2 } + b ^ { 2 } } } } , \cos \varphi = { \frac { a } { \sqrt { a ^ { 2 } + b ^ { 2 } } } }$ ,tan $\varphi =$ $\frac { b } { a } \bigg )$

# 六、基本定理公式

1.正弦定理、余弦定理

在 $\triangle A B C$ 中，角 $A , B , C$ 所对的边分别是$a , b , c , R$ 为 $\triangle A B C$ 外接圆半径，则

<html><body><table><tr><td>定理</td><td>正弦定理</td><td>余弦定理</td></tr><tr><td>公式</td><td>a b C sin A sin B sin C =2R</td><td>a²=b²+c²- 2bccos A; b²=c²+a²- 2accos B; c²=a²+b²- 2abcos C.</td></tr></table></body></html>

续表  

<html><body><table><tr><td>定理</td><td>正弦定理</td><td>余弦定理</td></tr><tr><td></td><td>（1)a=2R sinA,b= 2R sin B,c=2R sin C; (2)sin A 2R,sin B</td><td>cosA=²+²-² =²+2²-², cosB</td></tr></table></body></html>

2.面积公式

$S _ { \triangle A B C } = \frac { 1 } { 2 } a b \sin C = \frac { 1 } { 2 } b c \sin A = \frac { 1 } { 2 } a c \sin B$ $S _ { \triangle A B C } = \frac { a b c } { 4 R } = \frac { 1 } { 2 } ( a + b + c ) \cdot r ( r$ 是三角形内切圆的半径,并可由此计算 $R , r )$ ：

# 七、三角形中的一些重要结论

1.正弦定理的应用

$\textcircled{1}$ 边化角，角化边： $: a \colon b \colon c = \sin \ A$ ：sin $B$ sin $C$ $\textcircled{2}$ 大边对大角、大角对大边：$a > b \Longleftrightarrow A > B \Longleftrightarrow \sin \ A > \sin \ B \Longleftrightarrow \cos \ A < \cos \ B$ $\textcircled{3}$ 合分比： $\frac { a + b + c } { \sin A + \sin B + \sin C } \quad =$ ${ \frac { a + b } { \sin A + \sin B } } = { \frac { b + c } { \sin B + \sin C } } = { \frac { a + c } { \sin A + \sin C } } =$ ${ \frac { a } { \sin A } } = { \frac { b } { \sin B } } = { \frac { c } { \sin C } } = 2 R .$

2. $\triangle A B C$ 内角和定理： $A + B + C = \pi$

$\textcircled { 1 } \sin \ C = \sin \left( A \ + \ B \right) \ = \sin \ A \cos \ B \ +$   
cos Asin $B { \Longleftrightarrow } c = a \cos B + b \cos A .$ 同理， $a = b \cos \ C \ + \ c \cos \ B , \ b \ = c \cos \ A \ +$   
acos $C$ $\textcircled { 2 } - \cos \ C = \cos \left( A + B \right) = \cos \ A \cos \ B \ -$   
sin Asin $B$ $\textcircled{3}$ 在斜三角形中，-lan $C = \tan \left( A + B \right) =$   
$\frac { \tan \ A + \tan \ B } { \mathrm { \bf ~ l ~ - t a n } \ A \cdot \tan \ B } \Leftrightarrow \tan \ A \ + \ \tan \ B \ + \ \tan \ C \ =$   
lan A·lan $B \cdot$ tan $C$ $\textcircled { 4 } \sin \left( \frac { A + B } { 2 } \right) = \cos \frac { C } { 2 } ; \cos \left( \frac { A + B } { 2 } \right) = \sin \frac { C } { 2 } .$

$\textcircled{5}$ 在 $\triangle A B C$ 中，内角 $A , B , C$ 成等差数列 $\Leftrightarrow$ $B = \frac { \pi } { 3 } , A + C = \frac { 2 \pi } { 3 } .$

# $\textcircled{9}$

# 【进阶提升】

# 题型一 三角恒等变形

例1已知 $\scriptstyle | { \frac { \pi } { 4 } } \leqslant \alpha \leqslant \pi , \pi \leqslant \beta \leqslant { \frac { 3 \pi } { 2 } } , \sin 2 ,$ $\sin 2 \alpha =$ ${ \frac { 4 } { 5 } } , \cos ( \alpha + \beta ) = - { \frac { \sqrt { 2 } } { 1 0 } }$ 则 $\beta - \alpha = ( \begin{array} { l l l } { \begin{array} { r l } \end{array} } & { \begin{array} { r l } \end{array} } \end{array} )$

$$
\mathrm { A } . \frac { 3 } { 4 } \pi \qquad \mathrm { B } . \frac { \pi } { 4 } \qquad \mathrm { C } . \frac { 5 } { 4 } \pi \qquad \mathrm { D } . \frac { \pi } { 2 }
$$

变式1 已知 $\alpha$ 是第二象限角， $\alpha$ 终边与单位圆 $O$ 交于点 $P ( x _ { 0 } , y _ { 0 } )$ ，芳2 $\mathrm { ~ \cos ~ } \left( 2 \alpha + \frac { \pi } { 2 } \right)$ $\frac { \pi } { 3 } ) - 1 = 0$ ，则 $ { \boldsymbol { \gamma } } _ { 0 } = (  { \mathrm { ~ \textrm ~ { ~ ~ } ~ } } )$

$$
{ \mathrm { A } } , \ - { \frac { 1 } { 2 } } \qquad { \mathrm { B } } . \ - { \frac { \sqrt { 3 } } { 2 } } \qquad { \mathrm { C } } . \ { \frac { \sqrt { 3 } } { 2 } } \qquad { \mathrm { D } } . { \frac { 1 } { 2 } }
$$

变式2 已知锐角α满足sin(α-)$\frac { \sqrt { 5 } } { 5 }$ ，則Com $2 \alpha =$

例2已知锐角 $\alpha , \beta$ 满足 ${ \frac { \cos \alpha - \sin \alpha } { \cos \alpha + \sin \alpha } } =$ $\frac { \sin 2 \beta } { 1 - \cos 2 \beta }$ ，则 $\tan ( \alpha - \beta )$ 的值为（ ）

A.1 B.√ C.-1 D.-3 3

# 变式3

已知 $\frac { \tan \alpha } { \tan \left( \alpha + { \frac { \pi } { 4 } } \right) } = - \frac { 2 } { 3 }$ 则

$\sin ( 2 \alpha + \frac { \pi } { 4 } )$ 的值是

变式4已知lan $\alpha + \tan \beta = 3 , \sin ( \alpha +$ $\beta ) = 2$ sin $\alpha$ sin $\beta$ ，则 $\textstyle { \mathrm { l a n } } { \bigl ( } \alpha + \beta { \bigr ) } = { \bigl ( }$ （）

A.4B.6C. $- \frac { 3 } { 2 }$ D.-6

# 题型二解三角形

例3(多选题)在 $\triangle A B C$ 中，角 $A , B , C$ 所对的边分别为 $a , b , c$ ，已知 $( a + b ) \colon ( b + c ) \colon ( c$ $+ a ) = 5 \colon 6 \colon 7$ ，则下列结论正确的是（ ）

A.sinA:sin $B \colon$ sin $C = 2 \colon 3 \colon 4$ B. $\triangle A B C$ 为钝角三角形 C.若 $a = 6$ ，则 $\triangle A B C$ 的面积是 $6 ~ { \sqrt { 1 5 } }$ D.若 $\triangle A B C$ 外接圆半径是 $R$ ,内切圆半径 为r，则=16

变式5 在 $\triangle A B C$ 中，已知角 $A , B , C$ 所对的边分别为 $a , b , c , a = 2 b$ ，且 ${ \frac { 1 } { \tan A } } +$ tan B²sinC，则（

A $\cdot a , c , b$ 成等比数列 B.sin A:sin B:sin C=2:1:√2 C.若 $a = 4$ ，则 $\mathrm { S } _ { \Delta A B C } = \sqrt { 7 }$ $\operatorname { D } , A , B , C$ 成等差数列

例4在 $\triangle A B C$ 中，若sin $A = 2$ cos Bcos $C$ ，则 $\cos ^ { 2 } B + \cos ^ { 2 } C$ 的最大值为

变式6 $\triangle A B C$ 的内角 $A , B , C$ 的对边分别为 $a , b , c$ ，若 $3 b \cos \textit { C } + 3 c \cos \textit { B } =$ 5asin A，且 $A$ 为锐角，则当 $\frac { a ^ { 2 } } { b c }$ 取得最小值时，2b+c的 的值为

例5某景区为拓展旅游业务，拟建一个观景台 $P$ （如图所示），其中$A B$ ， $A C$ 为两条公路，$\angle B A C = 1 2 0 ^ { \circ } , M , N$ 为公路上的两个景点，测得 $A M = 2 ~ { \mathrm { k m } }$ ， $A N = 1 ~ { \bf k m }$ ，为了获得最佳观景效果，要求 $\angle M P N = 6 0 ^ { \circ }$ .现需要从观景台 $P$ 到$M , N$ 建造两条观光路线 $P M$ ， $P N$ ，且要求观光路线最长.若建造观光路线的宽为5米，每平方米造价为100元，则该景区预算需投人万元可完成改造. $( \sqrt { 7 } \approx 2 . 6 5$ ）

![](images/339976632f47c58ce075194a9043bc263715c51ef5ee8d1d7e20fb6a0b2c0a36.jpg)

变式7 为了测量一个不规则公园中$C , D$ 两点之间的距离，如图，在东西方向上选取相距 $1 \ k m$ 的 $A , B$ 两点，点 $B$ 在点 $A$ 的正东方向上，且 $A , P , C , D$ 四点在同一水平面上从点 $\boldsymbol { A }$ 处观测得点 $C$ 在它的东北方向上，点 $D$ 在它的西北方向上；从点 $B$ 处观测得点 $C$ 在它的北偏东 $1 5 ^ { \circ }$ 方向上，点 $D$ 在它的北偏西 $7 5 ^ { \circ }$ 方向上，则 $C , D$ 之间的距离为 $\mathrm { k m }$

![](images/d0a08b6817eb8af721608e38eecc00e137838cf91628a5252849486c0496c84b.jpg)

# 【核心知识聚焦】

在解三角形题目中，若已知条件同时含有边和角，但不能直接使用正弦定理或余弦定理,就要选择"边化角"或"角化边”，变换原则常用：

（1)若式子含有sin $_ { x }$ 的齐次式，优先考虑正弦定理，“角化边”；(2)若式子含有 $a , b , c$ 的齐次式，优先考虑正弦定理，“边化角”；(3）若式子含有cos $_ { x }$ 的齐次式，优先考虑余弦定理，“角化边”；(4)代数变形或者三角恒等变换前置；(5)含有面积公式的问题，要考虑结合余弦定理使用；(6)同时出现两个自由角（或三个自由角)时，要用到 $A + B + C = \pi$ ：

# 【进阶提升】

# 题型一结构开放问题

例1如图所示，已知圆 $o$ 是 $\triangle A B C$ 的外接圆，圆 $O$ 的直径 $B D = 2$ 设 $B C = a , A C = b$ ，$A B = c$ ,在下面给出条件中选一个条件解答后面的问题，

$\textcircled { 1 } \tan \angle B C A \cdot ( b - \sqrt { 3 } c \sin \angle B A C ) + \sqrt { 3 } c \cdot$ $\cos \angle B A C = 0$ $\textcircled { 2 } 2 \cos \angle B C A + \cos \angle B A C = ( 2 \sin \angle B C A -$ $\sin \angle B A C ) ~ \cdot ~ \tan \angle B A C ;$

$\textcircled{3} \triangle A B C$ 的面积为 $\frac { \sqrt { 3 } } { 4 } ( a ^ { 2 } + c ^ { 2 } - b ^ { 2 } )$ 选择条件

![](images/62c6430c0ad6aad7902c3fd5aaea4b961218c8a244e577f1e1fa72a5074033e4.jpg)

(1)求 $b$ 的值；   
(2)求 $\triangle A C D$ 的周长的取值范围.

变式在 $\textcircled { 1 } . 5 3 - . 3 \times \cos C = c \sin A$ $\textcircled { 2 }$ asin $B = \dot { 6 } \cos \frac { 4 } { 2 }$ ③（sin B+sin C）²$\sin ^ { 2 } A + 3$ sin Bsin $C$ 这三个条件中任选个补充在下面的横线上，并加以解答

在 $\triangle A B C$ 中，角 $A , B , C$ 的所对的边分别为 $a , b , c$

（1）求角 $\mathrm { _ A }$ 的大小：

（2）芳 $\triangle A B C$ 的面积为 $\frac { \sqrt { 3 } } { 3 } , a = 2$ ，求△ABC的周长.

例2已知函数 $f ( x ) = 2 { \sqrt { 3 } }$ sin xcos x-$2 \cos ^ { 2 } x .$

(1)求函数 $y = \log _ { 2 } f ( x )$ 的定义域和值域；

(2)已知锐角三角形ABC的三个内角分别为 $A , B , C$ ，它们的对边长分别是 $a , b , c .$ 若$f { \biggl ( } { \frac { A } { 2 } } { \biggr ) } = 0$ 求 $\cdot { \frac { b + c } { a } } $ 的最大值.

变式2 在锐角三角形 $A B C$ 中，角 $A$ ，$B , C$ 的对边分别为 $a , b , c$ ，向量 $m = { \bigl ( } c - a$ $( b + c ) \sin B .$ $\pmb { n } = ( \ b - c , ( c + a ) \sin \ A $ ，且$m / / n$

（1）求角 $C$ 的大小；

(2)若 $\triangle A B C$ 的面积为 $2 \sqrt { 3 }$ ，求 $2 a + b$ 的取值范围.

# 题型三中线、角平分线问题

例3在 $\triangle A B C$ 中，tan $\angle B A C = { \frac { 1 2 } { 5 } } , D$ 为BC上一点 $、 A D = 3 { \sqrt { 2 } }$ ：

(1)若 $D$ 为 $B C$ 的中点，求 $\triangle A B C$ 的面积的最大值；

(2)若 $\angle B A D = 4 5 ^ { \circ }$ ，求 $\triangle A B C$ 的面积的最 小值

![](images/46915a35fdb6ce5bbbfe526e3bfc493cf088f579042c408b6f7b603c9371a74f.jpg)

变式3 已知 $a , b , c$ 分别是 $\triangle A B C$ 三个内角 $A , B , C$ 的对边， $\triangle A B C$ 面积为 $S$ ，且$4 { \sqrt { 3 } } S = b ^ { 2 } + c ^ { 2 } + 2 b c - a ^ { 2 } .$

（1）求 $A$

(2）若 $a = 2$ ，且角 $A$ 的角平分线交 $B C$ 于点 $D , A D = { \sqrt { 3 } }$ ，求 $b$

# 题型四综合应用问题

例4如图，设 $\triangle A B C$ 中角 $A , B , C$ 所对的边分别为 $a , b , c , A D$ 为 $B C$ 边上的中线，已知$c = 1$ 且 2csin AcosB = asin A-bsin $B + { \frac { 1 } { 4 } } b \sin C$ ，$\cos \angle B A D = { \frac { \sqrt { 2 1 } } { 7 } } .$

![](images/a3c10e17afe555a25fbff27db18c33556d64afca232c480f38ecd336838d8898.jpg)

(1)求 $b$ (2)求 $\triangle A B C$ 的面积；(3）设点 $E , F$ 分别为边 $A B$ ， $A C$ 上的动点（含端点），线段 $E F$ 交 $A D$ 于点 $C$ ，且 $\triangle A E F$ 的面积为 $\triangle A B C$ 面积的 $\frac { 1 } { 6 }$ ，求 $\overrightarrow { A C } \cdot \overrightarrow { E F }$ 的取值范围.

变式4 如图，在锐角三角形 $A B C$ 中，角 $A , B , C$ 的对边分别为 $a , b , c ,$ 已知 $c = 3$ ，$\angle A C B = 6 0 ^ { \circ }$

![](images/9485893186fd76bd956ce1e6b62fd912c8d0f6f7f0a4e83573d13044f0424cbd.jpg)

（1）求 $\triangle A B C$ 面积的最大值；（2）若 $A B$ 边上的点 $D$ 满足 $A D = 2 D B$ ，求线段 $C D$ 长的最大值.

例5由于某地连晴高温，森林防灭火形势严峻,某部门安排了甲、乙两名森林防火护林员对该区域开展巡查.现甲、乙两名森林防火护林员同时从 $A$ 地出发,乙沿着正西方向巡视走了 $3 ~ \mathrm { k m }$ 后到达 $D$ 点，甲向正南方向巡视若干公里后到达 $B$ 点，又沿着南偏西 $6 0 ^ { \circ }$ 的方向巡视走到了 $C$ 点，经过测量发现 $\angle A C D =$ $6 0 ^ { \circ } .$ 设 $\angle A C B = \theta$ ，如图所示.

![](images/a87f6f258223192b143ccc83fe690f935a47930e92d6e0375de0aa91ed796014.jpg)

(1)设甲护林员巡视走过的路程为 $S =$ $A B + B C$ ,请用 $\theta$ 表示 $S$ ，并求 $S$ 的最大值；

(2)为了强化应急应战准备工作，有关部门决定在 $\triangle B C D$ 区域范围内储备应急物资，求$\triangle B C D$ 区域面积的最大值.

变式5在 $\triangle A B C$ 中， $\angle B A C = 1 2 0 ^ { \circ } , A B$ $= 2 , A C = 1$

（1）求 $\sin \angle A B C$

(2）若 $D$ 为 $B C$ 上一点，且 $\angle B A D =$ $9 0 °$ ，求 $\triangle A D C$ 的面积

# 【核心知识聚焦】

# 一、等差数列的常用性质

由等差数列的定义可得公差为 $d$ 的等差数列 $\left\{ a _ { n } \right\}$ 具有如下性质：

1.通项公式的推广： $\ a _ { n } = a _ { m } + \left( n - m \right) d$ ，$m , n \in \mathbf { N } ^ { \ast }$ ：

2.若 $m + n = p + q$ ,则 $a _ { { \scriptscriptstyle m } } + a _ { { \scriptscriptstyle n } } = a _ { { \scriptscriptstyle p } } + a _ { { \scriptscriptstyle q } } \big ( m ,$ $n , p , q \in \mathbf { N } ^ { * } \mathbf { \Lambda } )$

特别地,①若m+n=2p,则am+a=2ap$( m , n , p \in \mathbf { N } ^ { \ast } )$

$\textcircled{2}$ 若 $m + n + t = p + q + r$ ,则 $\begin{array} { r } { a _ { { n } } + a _ { { n } } + a _ { { t } } = } \end{array}$ $a _ { p } + a _ { q } + a _ { r } ( m , n , p , q , t , r \in \mathbf { N } ^ { * } ) .$

$\textcircled{3}$ 有穷等差数列中，与首末两项等距离的两项之和都相等，都等于首末两项的和，即$a _ { 1 } + a _ { n } = a _ { 2 } + a _ { n - 1 } = \cdots = a _ { i } + a _ { n + 1 - i } = \cdots .$

3.下标成等差数列的项 $a _ { k } , a _ { k + m } , a _ { k + 2 m } , \cdot$ ： 组成以md为公差的等差数列.

4.数列 $\left\{ \ t a _ { n } + \lambda \right\} \left( t , \lambda \right)$ 是常数)是公差为 $_ { t d }$ 的等差数列.

5.若数列 $\left\{ \boldsymbol { b } _ { n } \right\}$ 为等差数列,则数列 $\{ \iota a _ { n } \pm$ $\lambda b _ { n } \nmid ( t , \lambda$ 是常数)仍为等差数列.

6.若 $a _ { p } = q , a _ { q } = p$ ，则 $a _ { p + q } = 0$

# 二、等差数列前 $\scriptstyle n$ 项和的性质

1.若数列 $\left\{ a _ { n } \right\}$ 是公差为 $d$ 的等差数列， $S _ { n }$

为其前 $n$ 项和,则数列 $\left\{ { \frac { S _ { n } } { n } } \right\}$ 也是等差数列，且公差为

2.设等差数列 $\left\{ a _ { n } \right\}$ 的公差为 $d , S _ { n }$ 为其前 $n$ 项和,则 $S _ { \scriptscriptstyle m } , S _ { 2 { \scriptscriptstyle m } } - S _ { \scriptscriptstyle m } , S _ { 3 { \scriptscriptstyle m } } - S _ { 2 { \scriptscriptstyle m } } , S _ { 4 { \scriptscriptstyle m } } - S _ { 3 { \scriptscriptstyle m } } , \cdots$ 组成公差为 $m ^ { 2 } d$ 的等差数列.

3.在等差数列 $\left\{ a _ { n } \right\} , \left\{ b _ { n } \right\}$ 中,它们的前 $\scriptstyle n$ 项和分别记为 $S _ { n } , T _ { n }$ 则 $| \frac { a _ { n } } { b _ { n } } = \frac { S _ { 2 n - 1 } } { T _ { 2 n - 1 } } |$

4.若等差数列 $\left\{ a _ { n } \right\}$ 的项数为 $2 n$ ,则 $S _ { 2 n } = n$ $\left( a _ { n } + a _ { n + 1 } \right) , S _ { \ P } - S _ { \ P } = n d , \frac { S _ { \ P \ P } } { S _ { \ P } } = \frac { a _ { n + 1 } } { a _ { n } } _ { \circ }$

5.若等差数列 $\left\{ \begin{array} { l } { a _ { n } \rule { 0 ex } { 5 ex } } \end{array} \right\}$ 的项数为 $2 n - 1$ ，则 S=(n-) ${ \frac { n } { n - 1 } } .$

# 三、等比数列及其前 $n$ 项和的性质

若数列 $\left\{ a _ { n } \right\}$ 是公比为 $q$ 的等比数列，前 $\pmb { n }$ 项和为 $S _ { n }$ ,则有如下性质：

1.若 $m + n = p + q$ ，则 $a _ { { \scriptscriptstyle m } } a _ { { \scriptscriptstyle n } } = a _ { { \scriptscriptstyle p } } a _ { { \scriptscriptstyle q } }$ ；若 $m +$ $n = 2 r$ ,则 $a _ { _ m } a _ { _ n } = a _ { r } ^ { 2 } ( m , n , p , q , r \in \mathbf { N } ^ { * } )$ ：

推广： $\textcircled { 1 } a _ { 1 } a _ { n } = a _ { 2 } a _ { n - 1 } = \cdots a _ { i } a _ { n + 1 - i } = \cdots ;$ $\textcircled{2}$ 若 $m + n + t = p + q + r$ ，则 $a _ { { \scriptscriptstyle m } } a _ { { \scriptscriptstyle n } } a _ { { \scriptscriptstyle t } } = a _ { { \scriptscriptstyle p } } a _ { { \scriptscriptstyle q } } a _ { { \scriptscriptstyle r } }$

2.若 $m , n , p$ 成等差数列，则 $a _ { \scriptscriptstyle m } , a _ { \scriptscriptstyle n } , a _ { \scriptscriptstyle p }$ 成 等比数列.

3.数列 $\{ \lambda a _ { n } \} ( \lambda \ne 0 )$ 仍是公比为 $q$ 的等比数列;数列 $\left\{ { \frac { 1 } { a _ { n } } } \right\}$ 是公比为 $\frac { 1 } { q }$ 的等比数列；

数列 $\{ \ \mid a _ { n } \mid \}$ 是公比为 $\mid q \mid$ 的等比数列； 若数列 $\left\{ \begin{array} { l }  { b _ { n } } \end{array} \right\}$ 是公比为 $q ^ { \prime }$ 的等比数列,则数列 $\{ a _ { n } b _ { n } \}$ 是公比为 $q q ^ { \prime }$ 的等比数列.

4. $a _ { k } , a _ { k + m } , a _ { k + 2 m } , a _ { k + 3 m }$ ,…成等比数列,公比为 $q ^ { m }$

5.若 $\left\{ a _ { n } \right\}$ 是各项都为正数的等比数列，连续相邻 $k$ 项的和(或积)构成公比为 $q ^ { k }$ （或 $q ^ { k ^ { 2 } }$ ）的等比数列.

6.当 $q = 1$ 时， $\cdot \frac { S _ { n } } { S _ { m } } = \frac { n } { m }$ 当 $q \neq \pm 1$ 时 ${ \frac { S _ { n } } { S _ { m } } } =$ ${ \frac { 1 - q ^ { n } } { 1 - q ^ { m } } } .$

$$
7 . \ S _ { n + m } = S _ { m } + q ^ { m } S _ { n } = S _ { n } + q ^ { n } S _ { m } .
$$

8.若项数为 $_ { 2 n }$ ，则 ${ \frac { S _ { \{ \sharp \} } } { S _ { \sharp } } } = q$ ,若项数为 $2 n +$ 1,则- =q S

9.当 $q \not = - 1$ 时,连续 $_ m$ 项的和(如 $S _ { m } , S _ { 2 m }$ $- S _ { { } _ { m } } , S _ { { } _ { 3 m } } - S _ { { } _ { 2 m } } , \cdots )$ 仍组成等比数列（公比为$q ^ { m } , m \geqslant 2$ ).注意：这里连续 $m$ 项的和均非零.

# 【进阶提升】

# 题型一 基本量计算

例1已知各项均为正数的等比数列 $\left\{ a _ { n } \right\}$ 的前 $\pmb { n }$ 项和为 $S _ { n } , a _ { 2 } a _ { 4 } = 9 , 9 S _ { 4 } = 1 0 S _ { 2 }$ ，则 $a _ { 2 } +$ $a _ { 4 }$ 的值为（）

A.30 B.10 C.9 D.6

变式1 已知 $\left\{ a _ { n } \right\}$ 是各项均为正数的 等差数列，其公差为 $d \neq 0$ ，若 $\ln a _ { 1 } , \ln a _ { 3 }$ ， $\ln a _ { 6 }$ 也是等差数列，则其公差为（

A. ln $d$ $\mathbf { B } , \ln 2 d$ $\mathrm { C . } \ln { \frac { 2 } { 3 } }$ $\operatorname { D . } \ln { \frac { 3 } { 2 } }$

题型二等差、等比数列的性质

例2已知等差数列 $\left\{ a _ { n } \right\}$ 满足 $\underline { { { a } _ { 5 } } } + \underline { { { a } _ { 2 n - 5 } } } =$ $n ( n \in \mathbf { N } , n \geqslant 3 )$ ，则 $a _ { 1 } + a _ { 3 } + a _ { 5 } + a _ { 7 } + \cdots + a _ { 2 n - 3 }$ +a2n-1=

变式2 已知等比数列 $\left| \begin{array} { l } { a _ { n } } \end{array} \right|$ 满足 $\log _ { 2 } { a _ { 2 } } + \log _ { 2 } { a _ { 1 3 } } = 1$ ，且 $a _ { 5 } a _ { 6 } a _ { 8 } a _ { 9 } = 1 6$ ，则数 列 $\left\{ a _ { n } \right\}$ 的公比为（

A.2 $\mathbf { B } . \frac { 1 } { 2 } \qquad \mathbf { C } . \pm 2 \qquad \mathbf { D } . \pm \frac { 1 } { 2 }$

例3已知正项等比数列 $\left\{ a _ { n } \right\}$ 的前 $_ { n }$ 项和为 $S _ { n }$ ,若 $S _ { 4 } = 3$ ,则 $S _ { 2 } + S _ { 6 }$ 的最小值为（）

A.6 $\mathbf { B } . 6 { \sqrt { 2 } } - 3$   
$\mathrm { C } . 6 { \sqrt { 2 } }$ D.9

变式3 设等比数列 $\left| \mathbf { \Pi } \boldsymbol { a } _ { n } \right.$ 满足 $a _ { 1 } + a _ { 3 }$ $= 1 0 , a _ { 2 } + a _ { 4 } = 5$ ，则 $a _ { 1 } a _ { 2 } \cdots a _ { n }$ 的最大值为

# 题型三数列的周期性

例4已知数列 $\left\{ a _ { n } \right\}$ 满足： $a _ { 1 } = 1$ 且 $a _ { n + 1 }$ $+ \frac { 1 } { 1 + a _ { n } } = 0 ( n \in \mathbf { N } ^ { * } )$ ，则 $a _ { 2 0 1 8 } = \left( \begin{array} { l l l } { \mathrm { ~ } } & { \mathrm { ~ } } & { } \end{array} \right)$

A.2 C.0 D.1

变式4 （多选题）已知 $S _ { n }$ 是 $\left| \mathbf { \Pi } { \pmb { a } } _ { n } \right.$ 的前$\scriptstyle n$ 项和 $a _ { 1 } = 2 , a _ { n } = 1 - \frac { 1 } { a _ { n - 1 } } , n \geq 2 , n \in \mathbf { N } ^ { * }$ 则下列选项错误的是（

A. $a _ { 2 0 2 1 } = 2$   
B. $S _ { 2 0 2 1 } = 1 0 1 2$   
C $a _ { 3 n } a _ { 3 n + 1 } a _ { 3 n + 2 } = 1$   
D. $\left. a _ { n } \right.$ 是以3为周期的周期数列

# 【核心知识聚焦】

# 一、等差数列的四种判断方法

1.定义法 $a _ { n + 1 } - a _ { n } = d$ （或者 $a _ { n } - a _ { n - 1 } = d$ $\scriptstyle n \geq 2$ ） $d$ 是常数） $\Leftrightarrow \{ a _ { n } \}$ 是等差数列.

2.等差中项法： $2 a _ { n } = a _ { n - 1 } + a _ { n + 1 } \left( n \geqslant 2 \right)$ $( n \in \mathbf { N } ^ { * } ) \Leftrightarrow \{ a _ { n } \}$ 是等差数列.

3.通项公式： $a _ { n } = p n + q ( p , q$ 为常数) $\Leftrightarrow$ $\left\{ a _ { n } \right\}$ 是等差数列.（ $a _ { n }$ 可以看做关于 $n$ 的一次函数）

4.前 $\boldsymbol { n }$ 项和公式： $S _ { n } = A n ^ { 2 } + B n ( A , B$ 为常数） $\Leftrightarrow \{ a _ { n } \}$ 是等差数列.（ $S _ { n }$ 可以看做关于 $n$ 的二次函数，但是不含常数项 $C$ ).

提醒：证明一个数列是等差数列，只能用定义法或等差中项法.

# 二、等比数列的判断(证明)

1.定义： ${ \frac { a _ { n + 1 } } { a _ { n } } } = q$ （或者 $\frac { a _ { n } } { a _ { n - 1 } } = q \left( n \geqslant 2 \right) ;$ （可判断,可证明）.

2.等比中项法：验证 $a _ { n + 1 } ^ { 2 } = a _ { n } a _ { n + 2 }$ （特别注意 $a _ { n } \neq 0$ )(可判断，可证明).

3.通项公式法：验证通项是关于 $n$ 的指数型函数(只可判断).

# 【进阶提升】

# 题型一 等差数列的判断和证明

例1已知数列 $|  a _ { n } $ 满足： $a _ { 1 } = 1 , a _ { 2 } = 3$ ，

$$
2 { \left( { { a _ { n + 1 } } + 1 } \right) } \mathrm { ~ } = { a _ { n } } + { a _ { n + 2 } } , n \in \mathbf { N } ^ { * } .
$$

(1)证明数列 $\left\{ a _ { n + 1 } - a _ { n } \right\}$ 为等差数列；   
(2)求数列 $\left\{ a _ { n } \right\}$ 的通项公式.

变式1 已知数列 $\left| \left. a _ { n } \right. \right.$ 的前 $n$ 项和为$S _ { n } , a _ { 1 } = 1$ ，且 $S _ { n + 1 } = 4 a _ { n } + 1 , b _ { n } = \frac { a _ { n } } { 2 ^ { n } } ( n \in$ $\mathbf { N } ^ { * }$ ）

（1）证明：数列 $\left\{ b _ { n } \right\}$ 是等差数列；  
（2）求数列 $\left\{ a _ { n } \right\}$ 的通项公式.

# 题型二等比数列的证明

例2已知等比数列 $\left\{ a _ { n } \right\}$ 的前 $_ { n }$ 项和为$S _ { n } , a _ { n + 1 } = 2 S _ { n } + 1 ( n \in \mathbf { N } ^ { * } ) .$

(1)求数列 $\left\{ a _ { n } \right\}$ 的通项公式；

(2)在 $a _ { n }$ 和 $a _ { n + 1 }$ 之间插入 $_ n$ 个数，使这 $_ n$ $^ { + 2 }$ 个数组成一个公差为 $d _ { n }$ 的等差数列,在数列 $\{ d _ { n } \}$ 中是否存在3项 $d _ { m } , d _ { k } , d _ { p }$ （其中 $m , k , p$ 是公差不为0的等差数列)成等比数列？若存在,求出这3项;若不存在,请说明理由.

变式2 已知数列 $\left\{ \begin{array} { l } { \mathbf { a } _ { n } } \end{array} \right\}$ 满足， $a _ { 1 } = 3$ ！ $a _ { n } a _ { n + 1 } = 9 \times 2 ^ { 2 n - 1 } , n \in \mathbf { N } ^ { * } .$

（1）求数列 $\left| \alpha _ { n } \right|$ 的通项公式；

(2）证明：数列 $\left| ~ { \boldsymbol { a } } _ { n } \right|$ 中的任意三项均不能构成等差数列.

题型三等比数列的判断

例3已知数列 $\left| \begin{array} { l } { a _ { n } } \end{array} \right\}$ 的前 $\scriptstyle n$ 项和为 $S _ { n }$ ，$a _ { 1 } = 1$ ， $a _ { n } \ > 0$ ， $S _ { n } ^ { 2 } = a _ { n + 1 } ^ { 2 } - \lambda S _ { n + 1 }$ ,其中 $\lambda$ 为常数.

(1)证明： $S _ { n + 1 } = 2 S _ { n } + \lambda$ ： (2)若数列 $\mid a _ { n }$ |为等比数列,求 $\lambda$ 的值.

题型四数列的最值问题

例4已知数列 $\left\{ a _ { n } \right\}$ 满足： $\left( n - 1 \right) a _ { n + 1 } =$ $n a _ { n } - 1 , a _ { 2 } = 3$

(1)证明： $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 为等差数列,并求 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 的通项公式；

(2）数列 $b _ { n } = \left( { \frac { 1 } { 2 } } \right) ^ { a _ { n } + 1 } + n$ ，求满足 $b _ { 1 } + b _ { 2 }$ $+ b _ { 3 } + \cdots + b _ { n } < 1 0 0$ 的最大正整数 $n$

变式3已知公差不为0的等差数列$\left\{ \boldsymbol { a } _ { n } \right\}$ 中， $a _ { 1 } = 1 , a _ { 4 }$ 是 $a _ { 2 }$ 和 $a _ { 8 }$ 的等比中项.

（1）求数列 $\left| \alpha _ { n } \right\}$ 的通项公式：

(2）保持数列 $\left\{ a _ { n } \right\}$ 中各项先后顺序不变，在 $a _ { k }$ 与 $a _ { k + 1 } ( k = 1 , 2 , \cdots )$ 之间插入 $2 ^ { k }$ ，使它们和原数列的项构成一个新的数列$\left\{ \begin{array} { l } { { \boldsymbol { b } } _ { n } } \end{array} \right\}$ ，记 $\left\{ \boldsymbol { b } _ { n } \right\}$ 的前 $n$ 项和为 $T _ { n }$ ，求 $T _ { 2 0 }$ 的值.

变式4 已知数列 $\{ a _ { n } \}$ 满足 $a _ { 1 } = 1$ ， $a _ { n + 1 } = { \frac { a _ { n } } { 1 + 2 a _ { n } } } { \left( n \in \mathbf { N } ^ { * } \right) } .$

(1）求 $a _ { 2 } \ : , a _ { 3 } \ : , a _ { 4 }$ 的值；(2）证明数列 $\left\{ { \frac { 1 } { a _ { n } } } \right\}$ 为等差数列；（3）设 $\boldsymbol { c } _ { n } = \boldsymbol { a } _ { n } \boldsymbol { a } _ { n + 1 }$ ，求数列 $\left\{ \begin{array} { l } { c _ { n } } \end{array} \right\}$ 的前 $n$ 项和 $S _ { n }$ ，并求 $S _ { n }$ 的最小值.

![](images/3385e10f05f66ebe31a1c92b23b4659714a4563f49d18a9331639e7b58e23b2c.jpg)

# 【核心知识聚焦】

# 一、裂项相消法

1.等差型

$$
\textcircled { 1 } \frac { 1 } { n ( n + k ) } = \frac { 1 } { k } \bigg ( \frac { 1 } { n } - \frac { 1 } { n + k } \bigg ) .
$$

特别注意：当 $k = 1$ 时， ${ \frac { 1 } { n ( n + 1 ) } } = { \frac { 1 } { n } } \ -$ n当k=-1时，n $\cdot { \frac { 1 } { n ( n - 1 ) } } = { \frac { 1 } { n - 1 } } - { \frac { 1 } { n } }$ $\textcircled { 2 } \frac { 1 } { \left( k n - 1 \right) \left( k n + 1 \right) } = \frac { 1 } { 2 } \bigg ( \frac { 1 } { k n - 1 } - \frac { 1 } { k n + 1 } \bigg ) .$

如 $\frac { 1 } { 4 n ^ { 2 } - 1 } = \frac { 1 } { 2 } \left( \frac { 1 } { 2 n - 1 } - \frac { 1 } { 2 n + 1 } \right)$ (尤其要注意不能漏掉前边的 $\frac 1 2$ ）

2.无理型

$\textcircled { 1 } \frac { 1 } { \sqrt { n + k } + \sqrt { n } } = \frac { 1 } { k } \big ( \sqrt { n + k } - \sqrt { n } \big ) .$ 如 ${ \frac { 1 } { { \sqrt { n + 1 } } + { \sqrt { n } } } } = { \sqrt { n + 1 } } - { \sqrt { n } } .$

3.指数型$\textcircled { 1 } \frac { \left( a - 1 \right) a ^ { n } } { \left( a ^ { n + 1 } + k \right) \left( a ^ { n } + k \right) } = \frac { 1 } { a ^ { n } + k } - \frac { 1 } { a ^ { n + 1 } + k } .$

$$
{ \mathcal { W } } { \frac { 2 ^ { n } } { \left( 2 ^ { n + 1 } + k \right) \left( 2 ^ { n } + k \right) } } = { \frac { 1 } { 2 ^ { n } + k } } - { \frac { 1 } { 2 ^ { n + 1 } + k } } .
$$

4.通项裂项为“ $^ +$ ”型

$\mathbb { I } \mathbb { \textcircled { 1 } } ( - 1 ) ^ { n } \cdot \frac { 2 n + 1 } { n ( n + 1 ) } = ( - 1 ) ^ { n } \Big ( \frac { 1 } { n } +$ $\frac { 1 } { n + 1 } \biggr )$

$\textcircled { 2 } ( - 1 ) ^ { n } \frac { ( 3 n + 1 ) \cdot 2 ^ { n } } { n ( n + 1 ) } = ( - 1 ) ^ { n } \Big ( \frac { 2 ^ { n } } { n } +$ $\frac { 2 ^ { n + 1 } } { n + 1 } \bigg )$

本类模型典型标志在通项中含有 $( \mathbf { \partial } - 1 ) ^ { n }$ 乘以一个分式.

# 【进阶提升】

# 题型一 分离常数型裂项

例1设正项数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和为 $S _ { n }$ ，已知 $a _ { n } ^ { 2 } + a _ { n } = 2 S _ { n } \left( n \in \mathbf { N } ^ { * } \right)$

(1)求 $\left\{ a _ { n } \right\}$ 的通项公式；

(2）设 $b _ { n } = \frac { a _ { n + 1 } } { a _ { n } } + \frac { a _ { n } } { a _ { n + 1 } }$ ，求数列 $\left\{ \boldsymbol { b } _ { n } \right\}$ 的前 $n$ 项和.

变式1已知等差数列 $\mid a _ { n } \mid$ 的前 $n$ 项的和为 $M _ { n } , a _ { 2 } + M _ { 3 } = 2 0 , a _ { 5 } = 1 4$ 数列 $\mid b _ { n } \mid$ 的前 $n$ 项和为 $S _ { n } , b _ { 1 } = \frac { 1 } { 2 } , 2 S _ { n + 1 } = 2 S _ { n } + b _ { n } .$

（1）求数列 $\mid a _ { n } \mid$ 和 $\mid b _ { n } \mid$ 的通项公式；

（2）若 $c _ { n } = \frac { 1 } { a _ { n } a _ { n + 1 } }$ ，数列 $\mid c _ { n } \mid$ 的前 $n$ 项和为 $T _ { n }$ ，求证： $T _ { n } > \frac { 1 } { 6 } b _ { n }$

题型二 指数和二次函数混合型裂项

例2已知等差数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和为$S _ { n }$ ，且 $S _ { 6 } = 6 0 , a _ { 3 } + 3 a _ { 5 } = 4 8$ 当 $n \in \mathbf { N } ^ { \prime }$ 时， $2 ^ { n } b _ { 1 }$ $+ 2 ^ { n - 1 } b _ { 2 } + \cdots + 2 b _ { n } = 3 ^ { n } - 1 .$

(1)求数列 $\left\{ a _ { n } \right\} , \left\{ b _ { n } \right\}$ 的通项公式；

(2）若 $c _ { n } \ = \frac { \left( 2 b _ { n } - 1 \right) \left( 2 a _ { n } - 2 \right) } { a _ { n } a _ { n + 1 } }$ ，求数列 $\left\{ c _ { n } \right\}$ 的前 $n$ 项和 $T _ { n }$

变式2已知数列 $\{ a , \}$ 满足： $a _ { 1 } = 2$ ！， $n a _ { n + 1 } + ( n + 1 ) = ( n + 2 ) a _ { n } + ( n + 1 ) ^ { 3 } .$

(1）证明：数列 $\left\{ { \frac { a _ { n } } { n ( n + 1 ) } } \right\}$ 是等差数列；

（2）设 $b _ { n } = \frac { n ( n + 2 ) } { 2 ^ { n + 1 } a _ { n } }$ ，求数列 $\mid b _ { n } \mid$ 的前$\pmb { n }$ 项和 $S _ { n }$

# 题型三分母平方型裂项

例3已知正项数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 的前 $n$ 项和为 $S _ { n }$ ，且满足 $a _ { 1 } = 1 , a _ { 2 } = 3 , a _ { n + 2 } = 3 a _ { n + 1 } - 2 a _ { n }$ ，数 列 $\mid c _ { n } \mid$ 满足 $2 ^ { 2 } c _ { 1 } + 3 ^ { 2 } c _ { 2 } + 4 ^ { 2 } c _ { 3 } + \cdots + \left( n + 1 \right) ^ { 2 } c _ { n }$ $= n$ ：

(1)求出数列 $\left\{ a _ { n } \right\} , \left\{ c _ { n } \right\}$ 的通项公式；

(2)设数列 $\left\{ { \frac { c _ { n + 1 } \cdot ( n + 1 ) } { [ \log _ { 2 } ( a _ { n } + 1 ) ] ^ { 2 } } } \right\}$ 的前 $n$ 项和電为 $T _ { n }$ 求证： $T _ { n } < { \frac { 5 } { 1 6 } } .$

变式3 在递增数列 $\left| \boldsymbol { a } _ { n } \right.$ 中，已知 $a _ { 1 } =$ $1 , a _ { 2 } = 2$ ，且 $a _ { 2 n - 1 } , a _ { 2 n } , a _ { 2 n + 1 }$ 成等比数列，$a _ { 2 n } , a _ { 2 n + 1 } , a _ { 2 n + 2 }$ 成等差数列（ $\mathbf { \Gamma } _ { n \in \mathbf { N } } \cdot$ ）

（1)求数列 $\left\{ a _ { n } \right\}$ 的通项公式；

(2)设 $b _ { n } = \frac { 2 n + 1 } { a _ { 2 n - 1 } a _ { 2 n + 1 } } , T _ { n }$ 为数列 $\{ b _ { n } \}$ 的前 $n$ 项和.若对 $\forall n \in \mathbf { N } ^ { * }$ ，不等式 $k > T _ { n }$ 成立.求实数 $k$ 的取值范围.

# 题型四根式型裂项

例4设正项数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和为 $S _ { n }$ ， $a _ { 1 } = 1$ $a _ { n } = \frac { 2 } { 3 S _ { n } - 2 a _ { n } - S _ { n - 1 } } \left( n \in \mathbf { N } ^ { * } \right.$ ，且 $n \geqslant$ 2).

(1)求数列 $\left\{ a _ { n } \right\}$ 的通项公式；

(2)若 $b _ { n } = \frac { 1 } { S _ { n + 1 } + S _ { n } }$ 求数列 $\{ b _ { n } \}$ 的前 $n$ 项和 $T _ { n }$ ：

变式4 （多选题）数列 $\left| ~ \boldsymbol { a } _ { n } ~ \right|$ 依次为1，${ \frac { 1 } { 3 } } , { \frac { 1 } { 3 } } , { \frac { 1 } { 3 } } , { \frac { 1 } { 5 } } , { \frac { 1 } { 5 } } , { \frac { 1 } { 5 } } , { \frac { 1 } { 5 } } , { \frac { 1 } { 5 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } } , { \frac { 1 } { 7 } ( { } { \frac { 1 } } ) } , ) \ .$ ${ \frac { 1 } { 7 } } , { \frac { 1 } { 7 } } , { \frac { 1 } { 9 } } , { \frac { 1 } { 9 } } \cdots$ ，其中第一项为1,接下来三项为，再五项为 依次类推，记 $\left| \begin{array} { l } { a _ { n } } \end{array} \right|$ 的前 $n$ 项和为 $S _ { n }$ ，则下列说法正确的是

$a _ { 6 4 } = \frac { 1 } { 1 7 }$   
B. $\left\{ { \frac { 1 } { a _ { n ^ { 2 } } } } \right\}$ 为等差数列   
$\operatorname { C } . S _ { n ^ { 2 } } = n$   
D $a _ { n } { \leqslant } \frac { 1 } { 2 \sqrt { n } - 1 }$ 对于任意正整数 $\scriptstyle n$ 都

成立

# 【核心知识聚焦】

在数列问题中，若涉及到以下几种情况，往往要分 $\pmb { n }$ 为奇数、偶数进行讨论：

1.通项公式 $a _ { \mathrm { { s } } } = \left\{ { \begin{array} { l l } { f ( n ) , n = 2 k { \mathrm { ~ . } } } \\ { g ( n ) , n = 2 k } \end{array} } \right.$ （ $k \in$ $\mathbf { N } ^ { \circ }$ ）2. $\alpha _ { \scriptscriptstyle \equiv + 2 } - a _ { \scriptscriptstyle \equiv } = \lambda$ $3 . \frac { a _ { { \mathrm { { s } } + 2 } } } { a _ { \mathrm { { s } } } } = \lambda$ 4.在数列递推式中含有 $( \mathbf { \partial } - 1 ) ^ { n }$ 结构.

# 【进阶提升】

# 题型一 分组求和型

例1已知数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 满足： $a _ { n + 2 }$ + $( \mathbf { \partial } - 1 ) ^ { n + 1 } a _ { n } = 2 , a _ { 1 } = 1 , a _ { 2 } = 2$

(1)求数列 $\left\{ a _ { n } \right\}$ 的通项公式； (2)记数列 $\left\{ a _ { n } \right\}$ 的前 $\pmb { n }$ 项和为 $S _ { n }$ ，求 $S _ { 2 0 2 3 }$

变式1 数列 $\{ a _ { n }$ 满足 $a _ { n + 2 } + 1$ $( \mathbf { \nabla } - 1 ) ^ { \mathtt { A } } a _ { \mathtt { s } } = 3 n \mathbf { \nabla } - 1$ ，前16项和为540，则 a=

# 题型二并项求和型

例2已知等差数列 $\mid a _ { n } \mid$ 的公差为2,前 $\pmb { n }$ 项和为 $S _ { n }$ ，且 $S _ { 1 } , S _ { 2 } , S _ { 4 }$ 成等比数列.

(1)求数列 $\left\{ a _ { n } \right\}$ 的通项公式；

$b _ { n } = ( { \bf \alpha } - 1 { \bf \beta } ) ^ { n - 1 } \frac { 4 n } { a _ { n } a _ { n + 1 } }$ ，求数列 $\left\{ \boldsymbol { b } _ { n } \right\}$ 的前 $n$ 项和 $T _ { n }$ ：

变式2 在数列 $\left| ~ { a } _ { n } \right.$ 中， $a _ { 3 } = 3$ ，数列$\left\{ \boldsymbol { a } _ { n } \right\}$ 的前 $\scriptstyle { \pmb { n } }$ 项和 $S _ { n }$ 满足 $\frac { S _ { n } } { n } = \frac { 1 } { 2 } ( a _ { n } + 1 )$ $( n \in \mathbf { N } ^ { * } )$ （1）求数列 $\left\{ a _ { n } \right\}$ 的通项公式；(2）若 $b _ { n } = ( { \bf \Gamma } - 1 { \bf \Gamma } ) ^ { n } a _ { n } ^ { 2 }$ ，求数列 $\left\{ \begin{array} { l }  { b _ { n } } \end{array} \right\}$ 的前 $_ n$ 项和 $T _ { n }$

# 题型三结合数列增减性求最值

例3已知数列 $\left\{ a _ { n } \right\}$ 对于任意的 $\pmb { n } \in \mathbf { N } ^ { \prime }$ 均有 $a _ { 1 } \ + \ 3 a _ { 2 } \ + \ 5 a _ { 3 } \ + \ \cdots \ + \ \left( \ 2 n \ - \ 1 \ \right) a _ { n } \ =$ $\frac { n ( 2 n - 1 ) \left( 2 n + 1 \right) } { 3 }$ ；数列 $\mid b _ { \ L _ { \pi } } \mid$ 的前 $\pmb { n }$ 项和为$S _ { n }$ ，且 $b _ { n + 1 } = S _ { n } + 1 , b _ { 1 } = 1 .$

(1)求数列 $\left\{ a _ { n } \right\} , \left\{ b _ { n } \right\}$ 的通项公式；

(2）令 $c _ { n } =  \left\{ \begin{array} { l l } { - a _ { n } , \prime } \\ { b _ { n } , n \end{array} \right. }$ [-a,n为奇数， $T _ { n }$ 为数列内偶数，$\left\{ c _ { n } \right\}$ 的前 $\boldsymbol { n }$ 项和，且 $T _ { 2 n } + 2 n ^ { 2 } - n + 1 0 \geqslant \lambda b _ { n }$ 恒成立，求 $\lambda$ 的最大值.

# 题型四递推型数列求和

例4已知数列 $\left\{ a _ { n } \right\}$ 满足： $a _ { 1 } = 1$ ，且 $a _ { n + 1 } =$ $\lceil a _ { n } + 1 , n$ 为奇数，（ $\boldsymbol { n } \in \mathbf { N } ^ { \star }$ ).设 $b _ { n } = a _ { 2 n - 1 }$ $\lfloor 2 a _ { n } , n$ 为偶数

(1)证明：数列 $\{ b _ { n } + 2 \}$ 为等比数列，并求出 $\left\{ \boldsymbol { b } _ { n } \right\}$ 的通项公式；(2)求数列 $\left\{ a _ { n } \right\}$ 的前 $2 n$ 项的和.

变式3 已知数列 $\mid a _ { n } \mid$ 是各项均不为0的等差数列， $S _ { n }$ 为其前 $\pmb { n }$ 项和，且满足 $a _ { _ n } ^ { 2 }$ =S2n-1(n∈N°)，若不等式 $\frac { \lambda } { a _ { n + 1 } } \leqslant$ $\frac { n + 8 \cdot ( - 1 ) ^ { n } } { n }$ 对任意的 $\boldsymbol { n } \in \mathbf { N } ^ { \cdot }$ 恒成立，则实数 $\lambda$ 的最大值为

变式4 设数列 $\mid a _ { n } \mid$ 的前 $n$ 项和为 $S _ { n }$ ，$a _ { 1 } = 1 , 2 S _ { n } = \left( n + 1 \right) a _ { n } \left( n \in \mathbf { N } ^ { * } \right) .$ (1）求 $\left| \boldsymbol { a } _ { n } \right|$ 的通项公式；（2）对于任意的正整数 $n$ ， $\begin{array} { r l } { c _ { n } } & { { } = } \end{array}$ $\left\{ { \frac { 1 } { a _ { n } a _ { n + 2 } } } , n \right.$ 为奇数，求数列 $\mid c _ { n } \mid$ 的前 $2 n$ 项$1 2 ^ { a _ { n } } , n$ 为偶数，和 $T _ { 2 n }$

# 放缩技巧

所谓放缩的技巧：即欲证 $A \leqslant B$ ,欲寻找一个(或多个)中间变量 $c$ ,使 $A \leqslant C \leqslant B$ ，

由 $A$ 到 $c$ 叫做“放”，由 $B$ 到 $c$ 叫做“缩”.常用的放缩技巧：

$$
\sqrt { n - 1 } < \sqrt { n } , 2 \sqrt { n } > \sqrt { n } + \sqrt { n - 1 }
$$

$$
\begin{array} { r l } & { \sqrt { n + 1 } - 1 > \sqrt { n } - 1 , \sqrt { n ( n + 1 ) } > \sqrt { n ^ { 2 } } = n . } \\ & { \qquad ( 3 ) \frac { 1 } { n } - \frac { 1 } { n + 1 } = \frac { 1 } { n ( n + 1 ) } < \frac { 1 } { n ^ { 2 } } < \frac { 1 } { n ( n - 1 ) } } \\ & { = \frac { 1 } { n - 1 } - \frac { 1 } { n } ( n > 1 ) . } \\ & { \qquad ( 4 ) 2 ( \sqrt { n + 1 } - \sqrt { n } ) = \frac { 2 } { \sqrt { n + 1 } + \sqrt { n } } < } \\ & { \frac { 2 } { \sqrt { n } + \sqrt { n } } = \frac { 1 } { \sqrt { n } } < \frac { 2 } { \sqrt { n } + \sqrt { n - 1 } } = 2 ( \sqrt { n } - 1 ) } \\ & { \qquad \sqrt { n - 1 } ) . } \end{array}
$$

(5)若 $a , b , m \in \mathbf { R } ^ { + }$ ，则 $\frac { a } { b } > \frac { a } { b + m } , \frac { a } { b } <$ ${ \frac { a + m } { b } } .$

$\begin{array} { c } { { ( 6 ) 1 + \displaystyle \frac 1 2 + \displaystyle \frac 1 3 1 + \displaystyle \frac 1 2 + \displaystyle \frac 1 2 + \displaystyle \frac 1 2 + \displaystyle \frac 1 2 + \displaystyle \frac 1 2 + \displaystyle \frac 1 2 + \displaystyle \frac 1 2 + \displaystyle \frac 1 2 + \displaystyle \frac 1 2 + \displaystyle \frac 1 2 + \displaystyle \frac 1 2 } } \\ { { + \displaystyle \frac 1 { 2 ^ { ( \frac 1 2 - 1 ) } } } } \\ { { ( 7 ) 1 + \displaystyle \frac 1 2 + \displaystyle \frac 1 { 3 ! } + \displaystyle \frac 1 { 3 ! } + \cdots + \displaystyle \frac 1 { n ^ { 2 } } < 1 + \displaystyle \left( 1 - \displaystyle \frac 1 2 \right) + } } \\ { { \displaystyle \left( \frac 1 2 - \displaystyle \frac 1 3 \right) + \cdots + \displaystyle \left( \frac 1 { n - 1 } \displaystyle \frac 1 { n } \right) \left( \displaystyle \frac 1 { n ^ { 2 } } < \displaystyle \frac 1 { ( n - 1 ) n } \right) . } } \\ { { ( 8 ) \displaystyle \frac 1 { n + 1 } + \displaystyle \frac 1 { n + 2 } + \displaystyle \frac 1 { n + 3 } + \cdots + \displaystyle \frac 1 { 2 n } \displaystyle \frac 1 { n + 1 } + } } \\ { { \displaystyle \frac 1 { n + 1 } + \cdots + \displaystyle \frac 1 { n + 1 } = \displaystyle \frac 1 { n + 1 } < 1 , } } \end{array}$ 或 $: \frac { 1 } { n + 1 } + \frac { 1 } { n + 2 } + \frac { 1 } { n + 3 } + \cdots + \frac { 1 } { 2 n } \geq \frac { 1 } { 2 n } + \frac { 1 } { 2 n } +$ 1 n 1+2n 2n 2(9)1+ √2 1 + √3 1 +…+ n 1 7 n 1 + n 1 +$\cdots + { \frac { 1 } { \sqrt { n } } } = { \frac { n } { \sqrt { n } } } = { \sqrt { n } } ,$ $\frac { 1 } { k ( k + 1 ) } < \frac { 1 } { k ^ { 2 } } < \frac { 1 } { k ( k - 1 ) }$ 当 $k \geqslant 3$ 时， $\frac { 1 } { k ! } \leqslant$ $\begin{array} { c } { { \displaystyle { \frac { 1 } { 2 } \left[ \frac { 1 } { \left( k - 1 \right) ! } - \frac { 1 } { k ! } \right] } } } \\ { { ( 1 0 ) \displaystyle { \frac { 2 } { \sqrt { k } + \sqrt { k + 1 } } < \frac { 1 } { \sqrt { k } } < \frac { 2 } { \sqrt { k } + \sqrt { k - 1 } } } . } } \end{array}$

# 【进阶提升】

# 题型一数列前 $n$ 项和最值问题

例1已知数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和为 $S _ { n }$ ，且 满足 $n a _ { n + 1 } - \left( n + 1 \right) a _ { n } = 5$ ，且 $a _ { 1 } \neq - 5$

（1)求证：数列 $\left\{ { \frac { a _ { n } + 5 } { n } } \right\}$ 为常数列,并求$\left| ~ { a } _ { n } \right|$ 的通项公式；(2)若使不等式 $S _ { n } > 2 0$ 成立的最小整数为7，且 $\ b { a } _ { 1 } \in \mathbf { Z }$ ，求 $a _ { 1 }$ 和 $S _ { n }$ 的最小值.

变式1 已知公差不为0的等差数列  
$\left\{ a _ { n } \right\}$ 的前 $n$ 项和为 $S _ { n }$ ，且 $a _ { 1 } , a _ { 2 } , a _ { 5 }$ 成等比  
数列， $a _ { 2 } a _ { 3 } = a _ { 8 }$ （1）求数列 $\left\{ a _ { n } \right\}$ 的通项公式和前 $n$ 项  
和 $S _ { n }$ （2）若n $\geq 2 , \frac { 1 } { S _ { 2 } - 1 } + \frac { 1 } { S _ { 3 } - 1 } + \frac { 1 } { S _ { 4 } - 1 } +$   
$\cdots + { \frac { 1 } { S _ { n } - 1 } } { \leqslant } { \frac { 2 1 } { 4 0 } }$ ，求满足条件的 $n$ 的集合.

1（）

# 题型二数列不等式证明

例2已知等差数列 $\left\{ a _ { n } \right\}$ 的前 $\scriptstyle n$ 项和为$S _ { n } , a _ { 4 } + a _ { 7 } = 2 0 , S _ { 9 } = 2 7 a _ { 2 } .$

(1)求 $\left\{ a _ { n } \right\}$ 的通项公式；

(2)设 $b _ { n } = \frac { 2 } { \sqrt { a _ { n + 2 } } + \sqrt { a _ { n } } }$ ，数列 $\left\{ \begin{array} { l } { \boldsymbol { b } _ { n } } \end{array} \right\}$ 的前 $n$ 项和为 $T _ { n }$ ,证明：当 $n { \geqslant } 3$ 时 $2 T _ { n } > { \sqrt { a _ { n + 1 } } }$

变式2 （多选题）已知各项都是正数的数列 $\left| ~ a _ { n } \right.$ 的前 $n$ 项和为 $S _ { n }$ ，且 $S _ { n } = { \frac { a _ { n } } { 2 } } +$ $\frac { 1 } { 2 a _ { n } }$ ，则下列结论正确的是（

A. $\{ S _ { n } \}$ 是等差数列 B $. \ a _ { n + 1 } > a _ { n }$ $\mathrm { C . ~ } S _ { n } + S _ { n + 2 } < 2 S _ { n + 1 } \mathrm { D . ~ } S _ { n } - \frac { 1 } { S _ { n } } \geq \ln n$

# 题型三数列函数不等式

例3已知 $\left\{ a _ { n } \right\}$ 是等差数列， $a _ { 2 } + a _ { 5 } = 1 6$ ，$a _ { 5 } - a _ { 3 } = 4$

(1)求 $\left\{ a _ { n } \right\}$ 的通项公式 和 $\sum _ { i = 2 ^ { n - 1 } } ^ { 2 ^ { n } - 1 } a _ { i } .$

(2)已知 $\left\{ \begin{array} { l }  { b _ { n } } \end{array} \right\}$ 为等比数列,对于任意 $k \in$ $\mathbf { N } ^ { * }$ ，若 $2 ^ { k - 1 } \leqslant n \leqslant 2 ^ { k } - 1$ ，则 $b _ { k } < a _ { n } < b _ { k + 1 }$ ，

$\textcircled{1}$ 当 $k \geqslant 2$ 时,求证 $: 2 ^ { k } - 1 < b _ { k } < 2 ^ { k } + 1$ ;

$\textcircled{2}$ 求 $\left\{ b _ { n } \right\}$ 的通项公式及其前 $_ n$ 项和.

变式3 设数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和为 $S _ { n }$ 且 $3 a _ { n } = 2 \left( S _ { n } + 2 n \right) , n \in \mathbf { N } ^ { * }$ (1）证明：数列 $\left| \begin{array} { l l l } { a _ { n } + 2 } \end{array} \right|$ 是等比数列，并求 $\left| \boldsymbol { a } _ { n } \right|$ 的通项公式；（2）设 $b _ { n } = \log _ { 3 } { \frac { a _ { n } + 2 } { 2 } }$ ，证明：$\left( 1 + { \frac { 1 } { b _ { 1 } } } \right) \left( 1 + { \frac { 1 } { b _ { 3 } } } \right) \left( 1 + { \frac { 1 } { b _ { 5 } } } \right) \ \cdots \ \left( 1 + { \frac { 1 } { b _ { 2 n - 1 } } } \right) \ >$ $\sqrt { b _ { 2 n + 1 } }$

# 题型四递推型数列不等式

例4已知数列 $\left\{ a _ { n } \right\}$ 满足 $a _ { 1 } = 1 , a _ { n + 1 } =$ $\frac { a _ { n } } { 1 + \sqrt { a _ { n } } } \big ( n \in \mathbf { N } ^ { * } \ ;$ ).记数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和为$S _ { n }$ ，则（ ）

$$
\begin{array} { c c } { { \mathrm { A . ~ } \displaystyle \frac { 3 } { 2 } < S _ { 1 0 0 } < 3 ~ } } & { { ~ \mathrm { B . ~ } 3 < S _ { 1 0 0 } < 4 } } \\ { { } } & { { } } \\ { { \mathrm { C . ~ } 4 < S _ { 1 0 0 } < \displaystyle \frac { 9 } { 2 } ~ } } & { { ~ \mathrm { D . ~ } \displaystyle \frac { 9 } { 2 } < S _ { 1 0 0 } < 5 } } \end{array}
$$

变式4已知数列 $\left\{ a _ { n } \right\}$ 满足 $a _ { 1 } = 1$ ，且 $a _ { n + 1 } - a _ { n } = n + 1 , S _ { n }$ 是 $\left\{ { \frac { 1 } { a _ { n } } } \right\}$ 的前 $n$ 项和.

（1）求 $S _ { n }$

（2)若 $T _ { n }$ 为数列 $\left\{ \left( { \frac { S _ { n } } { n } } \right) ^ { 2 } \right\}$ 的前 $n$ 项和，求证： $T _ { n } < \frac { 2 5 } { 9 }$

# 第6讲 数学文化与数列问题

# 【核心知识聚焦】

# 一、杨辉三角形

第0行 第1行 $\begin{array} { c } { { 1 } } \\ { { \ldots } } \\ { { 1 \quad 2 \quad 1 } } \\ { { 1 \quad 3 \quad 3 \quad 1 } } \\ { { 1 \quad 4 \quad 6 \quad 4 \quad 1 } } \\ { { 1 \quad 5 \quad 1 0 \quad 1 0 \quad 5 } } \\ { { 6 \quad 1 5 \quad 2 0 \quad 1 5 \quad 6 } } \end{array}$   
第2行  
第3行  
第4行  
第5行 二 1  
第6行 1 1  
：

第 $_ { ; n }$ -1行1 $\mathrm { C } _ { n - 1 } ^ { 1 } \mathrm { C } _ { n - 1 } ^ { 2 } \cdots \mathrm { C } _ { n - 1 } ^ { r - 1 } \quad \mathrm { C } _ { n - 1 } ^ { r } \cdots \mathrm { C } _ { n - 1 } ^ { n - 3 } \quad \mathrm { C } _ { n - 1 } ^ { n - 2 } \quad 1$   
第 $_ n$ 行！ $\mathbf { C } _ { \eta } ^ { 1 }$ $\mathrm { C } _ { _ n } ^ { 2 }$ Cm² $\mathbf { C } _ { n } ^ { n - 1 }$ 1

如图所示，从图中可以直观地得到前四个数列的求和公式：

$( 1 ) 1 + 1 + 1 + \cdots + 1 = n$ （常数列的和）；$( 2 ) 1 + 2 + 3 + \cdots + \mathrm { C } _ { n - 1 } ^ { 1 } = \mathrm { C } _ { n } ^ { 2 }$ （等差数列的和)；$( 3 ) 1 + 3 + 6 + \cdots + \mathrm { C } _ { n - 1 } ^ { 2 } = \mathrm { C } _ { n } ^ { 3 }$ （二阶等差数列的和)；( $4 ) 1 + 4 + 1 0 + \cdots + \mathrm { C } _ { n - 1 } ^ { 3 } = \mathrm { C } _ { n } ^ { 4 }$ （三阶等差数列的和).(5)可以发现：自腰上右上角的某个1开始，平行于左边腰的一条线上的连续 $n - r$ 个数的和等于最后一个数斜右下方的那个数，即$\mathrm { C } _ { r } ^ { r } + \mathrm { C } _ { r + 1 } ^ { r } + \mathrm { C } _ { r + 2 } ^ { r } + \cdots + \mathrm { C } _ { n - 1 } ^ { r } = \mathrm { C } _ { n } ^ { r + 1 } .$

，由一般性的公式可以猜到 $r$ 阶等差数列的求和公式（可以用数学归纳法来证明）：$\mathrm { C } _ { r } ^ { r } + \mathrm { C } _ { r + 1 } ^ { \prime } + \mathrm { C } _ { r + 2 } ^ { r } + \cdots + \mathrm { C } _ { n - 1 } ^ { \prime } = \mathrm { C } _ { n } ^ { r + 1 }$

# ${ } = { \sqrt { } }$ 进制问题

1.定义：设整数 $p > 1$ ,则每个正整数 $n$ 可唯一表示为 $n = a _ { k } p ^ { k } + a _ { k - 1 } p ^ { k - 1 } + \cdots + a _ { 1 } p + a _ { 0 } ,$ 其中 $a _ { _ i }$ 满足 $0 \leqslant a _ { i } \leqslant p - 1 , i \in \mathbf { N } ^ { \bullet } , a _ { k } \neq$ ,则称$a _ { i }$ 为正整数 $n$ 的 $p$ 进制表示中的数码.特别地，当 $p = 2$ 时就可得到正整数 $n$ 的二进制表示.

2.二进制的运算性质.

(1)若 $n = a _ { k } \cdot 2 ^ { k } + a _ { k - 1 } \cdot 2 ^ { k - 1 } + \cdots + a _ { 1 }$ $2 + a _ { 0 }$ ，则称 $\omega ( n ) = \sum _ { i = 0 } ^ { k } a _ { i }$ 为正整数 $n$ 的2进制表示中的数码和,显然 $\omega ( 2 ^ { m } \cdot n ) = \omega ( n )$

证明：由于 $n = a _ { k } \cdot 2 ^ { k } + a _ { k - 1 } \cdot 2 ^ { k - 1 } + \cdots +$ $a _ { 1 } \cdot 2 + a _ { 0 }$ ，则 $2 ^ { m } \cdot n = \sum _ { i = 0 } ^ { k } { \left( a _ { i } 2 ^ { i + m } \right) }$ ,显然可得 $\omega ( 2 ^ { m } \cdot n ) = \omega ( n ) ,$

(2)二进制的加法运算:“逢二进一”.

$( 3 ) \omega ( 2 ^ { m } \cdot n + t ) = \omega ( n ) + \omega ( t )$ ,其中正整数 $t$ 的二进制展开式中最高次数小于 $_ m$

证明：由于 $n = a _ { k } \cdot 2 ^ { k } + a _ { k - 1 } \cdot 2 ^ { k - 1 } + \cdots +$ $a _ { 1 } \cdot 2 + a _ { 0 }$ 则， $2 ^ { m } \cdot n = \sum _ { i = 0 } ^ { k } { \left( a _ { i } 2 ^ { i + m } \right) }$ ，另一方面，令$t = \sum _ { j = 0 } ^ { l } { ( b _ { j } 2 ^ { j } ) }$ ,则 $\omega ( 2 ^ { m } \cdot n + t ) \ = \omega ( n ) \ +$ $\omega ( t )$ ：

# 【进阶提升】

# 题型一知数列和求数列通项型

例1将一个顶角为 $1 2 0 ^ { \circ }$ 的等腰三角形(含边界和内部)的底边三等分，挖去由两个等分点和上顶点构成的等边三角形，得到与原三角形相似的两个全等三角形，再对余下的所有三角形重复这一操作.如果这个操作过程无限继续下去…最后剩下的就是一条“雪花"状的曲线，如图所示已知最初等腰三角形的而积为1，则经过4次操作之后所得图形的面积是（）

![](images/2f7553c23d98bea2982370e4117d7c14ade27f3938310d1cd5f24e9b6f660f31.jpg)

A16 B.2 c. D.1

变式1 图1是第七届国际数学教育大会（简称ICME-7）的会徽图案，会徽的主体图案是由如图2所示的一连串直角三角形演化而成的，其中 $O A _ { 1 } = A _ { 1 } A _ { 2 } = A _ { 2 } A _ { 3 } =$ $\dots = A _ { 7 } A _ { 8 } = 1$ ，如果把图2中的直角三角形继续作下去，记 $O A _ { 1 } , O A _ { 2 } , \cdots , O A _ { n }$ 的长度构成的数列为 $\{ a _ { n } \}$ ，则此数列的通项公式$a _ { n } = ( \begin{array} { l l l } { \begin{array} { r l } \end{array} } & { \begin{array} { r l } \end{array} } \end{array} )$

A. $n$ B. $\sqrt { n }$   
C. $n + 1$ D. $\sqrt { n + 1 }$

![](images/2c561235b640989e1aade0dc944f79641c6dc1cbdd62df1ad4c5e143a8912906.jpg)  
图1

![](images/3051d1cbcbc55fa58918f85e2e30351de41dd69f9ce72504e683a2ef5e0fc242.jpg)  
图2

# 题型二 等差、等比数列基本问题

例2南宋数学家杨辉为我国古代数学研究作出了杰出贡献，他的著名研究成果“杨辉三角"记录于其重要著作《详解九章算法》,该著作中的"垛积术"问题介绍了高阶等差数列.以高阶等差数列中的二阶等差数列为例，其特点是从数列中的第二项开始,每一项与前一项的差构成等差数列.若某个二阶等差数列的前

4项为：2,3,6，11，则该数列的第15项为（ ）

A.196B.197C.198D.199

变式2 对给定的数列 $\mid a _ { n } \mid ( a _ { n } \ne 0 )$ ，记 $b _ { n } = \frac { a _ { n + 1 } } { a _ { n } }$ ，则称数列 $\{ \boldsymbol { b } _ { \scriptscriptstyle { n } } \}$ 为数列 $\mid a _ { n } \mid$ 的一阶商数列；记 $c _ { n } = { \frac { b _ { n + 1 } } { b _ { n } } }$ ，则称数列 $\{ c _ { n } \}$ 为数列 $\left\{ a _ { n } \right\}$ 的二阶商数列；以此类推，可得数列 $\mid a _ { n } \mid$ 的 $P$ 阶商数列（ $P \in \mathbf { N } ^ { \prime }$ ），已知数列$\{  a _ { n } \}$ 的二阶商数列的各项均为e（e为自然对数的底数），且 $a _ { 1 } = 1 , a _ { 2 } = 1$ ，则 $a _ { 1 0 } =$

# 题型三 多变量需统一参数型

例3图1是中国古代建筑中的举架结构， $A A ^ { \prime } , B B ^ { \prime } , C C ^ { \prime } , D D ^ { \prime }$ 是桁,相邻桁的水平距离称为步，垂直距离称为举.图2是某古代建筑屋顶截面的示意图,其中 $D D _ { 1 } , C C _ { 1 } , B B _ { 1 } , A A _ { 1 }$ 是举， $O D _ { 1 } , D C _ { 1 } , C B _ { 1 } , B A _ { 1 }$ 是相等的步，相邻桁的举步之比分别为 $\imath \frac { D D _ { 1 } } { O D _ { 1 } } = 0 . 5 , \frac { C C _ { 1 } } { D C _ { 1 } } = k _ { 1 } , \frac { B B _ { 1 } } { C B _ { 1 } } = k _ { 2 }$ ，$\frac { A A _ { 1 } } { B A _ { 1 } } = k _ { 3 }$ ，已知 $k _ { 1 } , k _ { 2 } , k _ { 3 }$ 成公差为0.1的等差数列,且直线 $O A$ 的斜率为0.725,则 $k _ { 2 } =$ （）

![](images/c4ece8ceb6cda2a0a0c8f92999c717e411ff1fd86e8688d305478a288573af36.jpg)

![](images/841ad6c23e9a778ded85b417f06a023e309c054a93f3e2005a4e1f0e151a8cc2.jpg)  
图1  
图2

A.0.75B.0.8C.0.85D.0.9

变式3 山西大同的辽金时代建筑华严寺的大雄宝殿共有9间，左右对称分布，最中间的是明间，宽度最大，然后向两边均依次是次间、次间、梢间、尽间.每间宽度从明间开始向左右两边均按相同的比例逐步递减，且明间与相邻的次间的宽度比为8：7.若设明间的宽度为 $a$ ，则该大殿9间的总宽度为（

$$
\begin{array} { l l } { { \mathrm { A . ~ \displaystyle \left( \frac { 7 } { 8 } \right) ^ { 4 } a } } } & { { \mathrm { B . ~ } 1 5 a - 1 4 a \left( \frac { 7 } { 8 } \right) ^ { 5 } } } \\ { { \mathrm { C . ~ } 1 4 a \displaystyle \left[ 1 - \left( \frac { 7 } { 8 } \right) ^ { 4 } \right] } } & { { \mathrm { D . ~ } 1 5 a - 1 4 a \left( \frac { 7 } { 8 } \right) ^ { 4 } } } \end{array}
$$

# 题型四 斐波那契数列

例4（多选题）已知数列1,1,2,3,5，8,被称为“斐波那契数列”,该数列是以兔子繁殖为例子引入的，故又称为“兔子数列”，斐波那契数列 $\left\{ a _ { n } \right\}$ 满足 $a _ { 1 } = a _ { 2 } = 1 , a _ { n } = a _ { n - 1 } +$ $a _ { n - 2 } \left( n \geqslant 3 , n \in \mathbf { N } ^ { * } \right)$ ，则下列说法正确的是（）

$\mathrm { A . } \mathrm { ~ } a _ { 3 } + a _ { 5 } = \frac { a _ { 8 } } { a _ { 4 } }$ $\mathrm { B } . a _ { 1 } + a _ { 3 } + a _ { 5 } + \cdots + a _ { 2 0 2 3 } = a _ { 2 0 2 4 }$ $\mathrm { C . } 3 a _ { n } = a _ { n - 2 } + a _ { n + 2 } \left( n \geqslant 3 , n \in \mathbf { N } ^ { \ast } \right)$ D $\cdot a _ { 1 } a _ { 2 } + a _ { 2 } a _ { 3 } + \cdots + a _ { n } a _ { n + 1 } = a _ { n + 1 } ^ { 2 }$

变式4 斐波那契数列又称为“兔子数列”.此数列在现代物理、准晶体结构、化学等领域都有着广泛的应用，斐波那契数列$\left\{ a _ { n } \right\}$ 可以用如下方法定义： $a _ { n + 2 } = a _ { n + 1 } ~ +$ $a _ { n }$ ，且 $\boldsymbol { a } _ { 1 } = \boldsymbol { a } _ { 2 } = 1$ ，若此数列各项除以4的余数依次构成一个新数列 $\left\{ \begin{array} { l } { { \boldsymbol { b } } _ { n } } \end{array} \right\}$ ，则数列 $\mid b _ { n } \mid$ 的前2023项的和为（

A.2023 B.2024   
C.2696 D. 2697

# 题型五 $n$ 进制数列问题

例5（多选题)设正整数 $n = a _ { 0 } \cdot 2 ^ { 0 } +$ $a _ { 1 } \cdot 2 + \cdots + a _ { k - 1 } \cdot 2 ^ { k - 1 } + a _ { k } \cdot 2 ^ { k }$ ,其中 $a _ { i } \in \{ 0$ ，$1 \}$ ，记 $\omega { \big ( } n { \big ) } = a _ { 0 } + a _ { 1 } + \cdots + a _ { k } .$ 则（）

A $\omega ( 2 n ) = { \omega } ( n )$ B $. \omega \big ( 2 n + 3 \big ) = \omega \big ( n \big ) + 1$ $\mathsf { C } . \omega ( 8 n + 5 ) = \omega \mathopen { } \mathclose \bgroup \left( 4 n + 3 \aftergroup \egroup \right)$ $) . \omega \big ( 2 ^ { n } - 1 \big ) = n$

变式5 欧拉函数 $\varphi ( n ) ( n \in \mathbf { N } ^ { * } )$ 的函数值等于所有不超过正整数 $n$ ，且与 $_ n$ 互质的正整数的个数，例如： $\varphi \ ( \ 1 \ ) \ = \ 1$ $\varphi ( 4 ) = 2$

（1）求 $\varphi ( 3 ^ { 2 } ) , \varphi ( 3 ^ { 3 } )$ （2）令 $a _ { n } = \frac { 1 } { 2 } \varphi ( 3 ^ { n } )$ ，求数列[logan}的前n项和。an

# 题型六 杨辉三角有关问题

例6如图,在“杨辉三角"中从第2行右边的1开始按箭头所指的数依次构成一个数列：1,2,3,3,6,4,10,5，，则此数列的前30项的和为（）

第1行 11  
第2行 12-1  
第3行 13-31  
第4行 14-641  
第5行15101051

A.680B.679C.816D.815

变式6 “杨辉三角”是中国古代数学文化的瑰宝之一，它揭示了二项展开式中的组合数在三角形数表中的一种几何排列规律，如图所示，则下列关于“杨辉三角”的结论错误的是（

第0行 $( a + b ) ^ { 0 }$ $\begin{array} { c } { { 1 } } \\ { { 1 \quad \begin{array} { c c c c } { { 1 } } & { { 1 } } & { { 1 } } & { { } } \\ { { } } & { { 1 } } & { { 2 } } & { { 1 } } \end{array} } } \\ { { 1 \quad \begin{array} { c c c c } { { 1 } } & { { 3 } } & { { 3 } } & { { 1 } } \\ { { } } & { { 4 } } & { { 6 } } & { { 4 } } \end{array} } } \\ { { 1 \quad \begin{array} { c c c c } { { 1 } } & { { 5 } } & { { 1 0 } } & { { 6 } } & { { 5 } } \\ { { 1 } } & { { 6 } } & { { 1 5 } } & { { 2 0 } } & { { 1 5 } } \end{array} } } \\ { { \cdot \begin{array} { c c c c } { { 7 } } & { { 2 1 } } & { { 3 5 } } & { { 3 5 } } & { { 2 1 } } \\ { { 8 } } & { { 2 8 } } & { { 5 6 } } & { { 7 0 } } \end{array} } } \\ { { \quad \begin{array} { c c c c } { { 8 } } & { { 2 8 } } & { { 5 6 } } & { { 2 8 } } & { { 2 8 } } \end{array} } } \end{array} {array} { 1 } $   
第1行（a+b）  
第2行 $( a + b ) ^ { 2 }$   
第3行（a+b）³  
第4行（a+b）  
第5行（a+b）  
第6行（a+b）  
第7行（a+b）  
第8行（a+b）8

A.第6行的第7个数、第7行的第7个数及第8行的第7个数之和等于第9行的第8个数B.第2023行中第1012个数和第1013个数相等C.记“杨辉三角”第 $n$ 行的第 $i$ 个数为$a _ { i }$ ，则 $\vert \sum _ { i = 1 } ^ { n + 1 } { \bigl ( } 2 ^ { i - 1 } a _ { i } { \bigr ) } \ = \ 3 ^ { n }$ D.第34行中第15个数与第16个数之比为2:3

# 【核心知识聚焦】

# 一、数列新定义问题背景

数列中的新定义问题是对等差数列、等比数列、递推公式和求和公式等的综合运用，考生需对常见的求通项公式和求和的方法、数列性质等知识点需要有一定的掌握，同时涉及到数列与函数、数列与解析几何、数列与二项式定理、数列与排列组合等的综合应用，复习时需要回顾函数的性质、解析几何常见结论、二项式的赋值法以及排列数公式和组合数公式.

# 二、解题步骤

1.读懂定义,理解新定义数列的含义；

2.通过特例列举(一般是前面5项)寻找新定义数列的规律及性质，以及新定义数列与已知数列(如等差数列、等比数列)的关系，进行求解.

# 【进阶提升】

# 题型一与集合有关的新定义问题

例1已知各项均为正数的数列 $\left\{ a _ { n } \right\}$ 的前$_ n$ 项和为 $S _ { n }$ ，且 $a _ { n } , S _ { n } , a _ { n } ^ { 2 }$ 为等差数列.

(1)求数列 $\left\{ a _ { n } \right\}$ 的通项公式；

(2)若 $_ m$ 为正整数,记集合 $\left\{ a _ { n } \ \right. \frac { a _ { n } } { 2 } + \frac { 2 } { a _ { n } } \leqslant$ $m \}$ 的元素个数为 $b _ { m }$ ,求数列 $\left\{ \begin{array} { l } { \boldsymbol { b } _ { n } } \end{array} \right\}$ 的前50项 的和.

变式1 已知等差数列 $\mid a _ { n } \mid$ 满足 $\textbf { \ -- } n +$ $1 ) a _ { \scriptscriptstyle \mathrm { R } } = 4 n ^ { 2 } + n + k , k \in { \bf R }$ 数列 $\mid b _ { n } \mid$ 的前 $\boldsymbol { n }$ 项和 $T _ { n }$ 满足 $2 T _ { n } = 3 b _ { n } - 3$

（1）求数列 $| \mathbf { \Pi }  \mathbf { a } _ { \mathrm { \Pi _ { \perp } } } |$ 和 $\mid b _ { n } \mid$ 的通项公式；

(2）对于集合 $A , B$ ，定义集合 $A - B =$ $\vert x \vert x \in A$ 且 $x \notin B \}$ 、设数列 $\left| ~ a _ { \mathfrak { a } } ~ \right|$ 和 $\mid b _ { n } \mid$ 中的所有项分别构成集合 $A , B$ ，将集合 $A - B$ 的所有元素按从小到大依次排列构成一个新数列 $\mid c _ { \mathfrak { a } } \mid$ ，求数列 $\mid c _ { n } \mid$ 的前50项和 $S _ { 5 0 }$

# 题型二指定等式型新定义问题

例2在数列 $\left\{ a _ { n } \right\}$ 中,若 $a _ { n + 1 } - a _ { 1 } a _ { 2 } a _ { 3 } \cdots$ $\begin{array} { r } { \boldsymbol { a } _ { n } = \boldsymbol { d } ( n \in \mathbf { N } ^ { \bullet } ) } \end{array}$ ，则称数列 $\left\{ a _ { n } \right\}$ 为“泛等差数列”，常数 $d$ 称为“泛差”.已知数列 $\left\{ a _ { n } \right\}$ 是一个“泛等差数列”，数列 $\left\{ \boldsymbol { b } _ { n } \right\}$ 满足 $a _ { 1 } ^ { 2 } + a _ { 2 } ^ { 2 } + \cdots +$ $a _ { n } ^ { 2 } = a _ { 1 } a _ { 2 } a _ { 3 } \cdots a _ { n } - b _ { n } .$

(1)若数列 $\left\{ a _ { n } \right\}$ 的“泛差” $d = 1$ ，且 $a _ { 1 } , a _ { 2 }$ ， ${ \pmb a } _ { 3 }$ 成等差数列,求 $a _ { \scriptscriptstyle 1 }$

(2)若数列 $\left\{ a _ { n } \right\}$ 的“泛差” $d = - 1$ ，且 $a _ { 1 } =$ $\frac { 1 } { 2 }$ ,求数列 $\left\{ b _ { n } \right\}$ 的通项公式.

变式2已知数列 $A _ { n } : a _ { 1 } , a _ { 2 } , \cdots , a _ { n }$ 如果数列 $B _ { n } : b _ { 1 } , b _ { 2 } , \cdots , b _ { n }$ 满足 $b _ { 1 } = a _ { n } , b _ { k } =$ $a _ { k - 1 } + a _ { k } - b _ { k - 1 }$ ，其中 $k = 2 , 3 , \cdots , n$ ，则称$B _ { n }$ 为 $A _ { n }$ 的“衍生数列”

（1）若数列 $A _ { 4 } : a _ { 1 } , a _ { 2 } , a _ { 3 } , a _ { 4 }$ 的“衍生数列”是 $B _ { 4 } : 5 , - 2 , 7 , 2$ ，求 $A _ { 4 }$

(2）若 $n$ 为偶数，且 $A _ { n }$ 的“衍生数列”是$B _ { n }$ ，证明： $B _ { n }$ 的“衍生数列”是 $A _ { n }$ ；

（3）若 $n$ 为奇数，且 $A _ { n }$ 的“衍生数列”是 $B _ { n } , B _ { n }$ 的“衍生数列”是 $C _ { n }$ ，…依次将数列 $A _ { n } , B _ { n } , C _ { n } ,$ …第 $i ( i = 1 , 2 , \cdots , n )$ 项取出，构 成数列 $\varOmega _ { i } : a _ { i } , b _ { i } , c _ { i } \cdots$ 求证： $\varOmega _ { i }$ 是等差数 列

# 题型三观察型新定义问题

例3已知数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和 $S _ { n } = 2 ^ { n + 1 }$ -2,数列 $\left\{ \boldsymbol { b } _ { n } \right\}$ 满足 $b _ { n } = \log _ { 2 } { a _ { n } }$

(1)求数列 $\left\{ a _ { n } \right\} , \left\{ b _ { n } \right\}$ 的通项公式；

(2)由数列 $\left\{ a _ { n } \right\} , \left\{ b _ { n } \right\}$ 中的项构成的 $n \times n$ 阶数阵如图所示,求该数阵中所有项的和 $T _ { n }$

$$
\left( \begin{array} { c c c c c c } { { a _ { 1 } b _ { 1 } , } } & { { a _ { 1 } b _ { 2 } , } } & { { a _ { 1 } b _ { 3 } , } } & { { \cdots , } } & { { a _ { 1 } b _ { n } } } \\ { { } } & { { } } & { { } } & { { } } & { { } } \\ { { a _ { 2 } b _ { 1 } , } } & { { a _ { 2 } b _ { 2 } , } } & { { a _ { 2 } b _ { 3 } , } } & { { \cdots , } } & { { a _ { 2 } b _ { n } } } \\ { { } } & { { } } & { { } } & { { } } & { { } } \\ { { a _ { 3 } b _ { 1 } , } } & { { a _ { 3 } b _ { 2 } , } } & { { a _ { 3 } b _ { 3 } , } } & { { \cdots , } } & { { a _ { 3 } b _ { n } } } \\ { { } } & { { } } & { { } } & { { } } & { { } } \\ { { } } & { { } } & { { \cdots } } & { { } } & { { } } \\ { { } } & { { } } & { { } } & { { } } & { { } } \\ { { a _ { n } b _ { 1 } , } } & { { a _ { n } b _ { 2 } , } } & { { a _ { n } b _ { 3 } , } } & { { \cdots , } } & { { a _ { n } b _ { n } } } \end{array} \right)
$$

变式3 从下面的表格中选出3个数字（其中任意两个数字不同行且不同列）作为递增等差数列 $\left| \left. a _ { n } \right. \right.$ 的前三项.

<html><body><table><tr><td>行</td><td>第1列</td><td>第2列</td><td>第3列</td></tr><tr><td>第1行</td><td>7</td><td>2</td><td>3</td></tr><tr><td>第2行</td><td>1</td><td>5</td><td>4</td></tr><tr><td>第3行</td><td>6</td><td>9</td><td>8</td></tr></table></body></html>

（1）求数列 $\left. \begin{array} { c } { { } } \\ { { a _ { n } } } \end{array} \right.$ 的通项公式，并求$\left| ~ \boldsymbol { a } _ { n } \right|$ 的前 $n$ 项和 $S _ { n }$

（2）若 $b _ { n } = \frac { 1 } { a _ { n } ^ { 2 } }$ ，记 $\mid b _ { n } \mid$ 的前 $n$ 项和 $T _ { n }$ ，求证 $T _ { n } < { \frac { 1 } { 2 } } .$

# 第五章立体几何与空间向量

# 第1讲 空间几何体··

# 【核心知识聚焦】

# 一、旋转体的表面积

<html><body><table><tr><td>几何体</td><td>圆柱（底面半径为r,母线长 为l）</td><td>圆锥（底面半径为r,母线长</td><td>圆台（上、下底面半径分别为 r&#x27;,r,母线长为l)</td></tr><tr><td>侧面展开图</td><td>2r</td><td></td><td>2πr&#x27; 2Tπr io</td></tr><tr><td>底面面积</td><td>S=πr²</td><td>S =r²</td><td>S上底=Tr&#x27;²,S下底=πr²</td></tr><tr><td>侧面面积</td><td>S侧 =2πrl</td><td>S =Trl</td><td>S侧=πl(r&#x27;+r)</td></tr><tr><td>表面积</td><td>S=2πr(r+l)</td><td>S=πr(r+l）</td><td>S=π(r&#x27;²+r²+r&#x27;l+rl）</td></tr></table></body></html>

# 二、多面体的表面积

1.多面体的表面积就是各个面的面积之和,也就是展开图的面积.

2.正棱锥、正棱台、正棱柱的侧面积公式间的联系：（其中 $c$ 是下底周长， $c ^ { \prime }$ 是上底周长，$h$ 是高， $. h ^ { \prime }$ 是斜高)

c'=0 S正楼锥侧 ch' $S _ { \mathrm { { \scriptsize { 1 } } E B E 6 6 9 } } { = } \frac 1 2 ( c { + } c ^ { \prime } ) h ^ { \prime }$ $c { = } c ^ { \prime }$ S正ch h=h'

# 三、柱体、锥体、台体的体积公式

<html><body><table><tr><td>几何体</td><td>体积</td></tr><tr><td>柱体</td><td>V柱体=Sh(S为底面面积,h为高),V圆桂= πr²h(r为底面半径,h为高)</td></tr><tr><td rowspan="2">锥体</td><td>= Sh(S为底面面积,h为高)， V维体</td></tr><tr><td>πr²h(r为底面半径,h为高) V圆维</td></tr></table></body></html>

续表  

<html><body><table><tr><td>几何体</td><td>体积</td></tr><tr><td>台体</td><td>(S&#x27;+√SS+S)h(S&#x27;,S分别为 3 上、下底面面积,h为高)， πh(r²+r&#x27;r+²)(r&#x27;,r分别为 V红= V圆=</td></tr></table></body></html>

# 四、柱体、锥体、台体体积公式间的关系

![](images/93ac0ce33ab60560d3b1e00b3dd5d9d385a5acad2b2c40adf872f7bcc79a703b.jpg)

# 五、球的表面积和体积公式

设球的半径为 $R$ ，它的体积与表面积都由半径 $R$ 唯一确定，是以 $R$ 为自变量的函数，其表面积公式为 $4 \pi R ^ { 2 }$ ，即球的表面积等于它的大圆面积的4倍;其体积公式为 $\frac { 4 } { 3 } \pi R ^ { 3 }$

# 【进阶提升】

题型一 空间几何体的表面积

例1如图所示，已知三棱台ABC-$A _ { 1 } B _ { 1 } C _ { 1 }$ 的上、下底面都是等腰直角三角形，$\angle A C B = 9 0 ^ { \circ }$ $C C _ { 1 } \bot$ 平面ABC, $A C = 2 , A _ { 1 } C _ { 1 } =$ $1 , C C _ { 1 } = 1$ ,则这个三棱台的侧面积为（）

![](images/776c6cef99de1ef493ac9ddfb85e87933d4b5ed36544614220e1f43c135a36eb.jpg)

A 6+33 B.10+3/3   
2 2   
C.11+33 2 D.3+2√3

变式1 某同学在参加《通用技术》实践课时，制作了一个实心工艺品（如图所示).该工艺品可以看成是一个球体被一个棱长为4的正方体的6个面所截后剩余的部分（球心与正方体的中心重合），其中一个截面圆的周长为 $3 \pi$ ，则该球的半径为 ；现给出定义：球面被平面所截得的一部分叫做球冠，截得的圆叫做球冠的底，垂直于截面的直径被截得的一段叫做球冠的高，如果球的半径是$R$ ，球冠的高是 $h$ ，那么球冠的表面积计算公式是 $S = 2 \pi R h$ .由此可知，该实心工艺品的表面积是

![](images/37d7d690e96bcc01ce6f493ef9c8820c8a350ca7d6513f907ffec45788cd9b9d.jpg)

# 题型二空间几何体的体积

例2中国古代数学名著《九章算术》中记载：“今有羨除”.刘徽注：“羡除，隧道也.其所穿地，上平下邪."现有一个羨除如图所示，四边形ABCD，ABFE，CDEF均为等腰梯形，$A B / / C D / / E F$ ， $A B = 6 , C D = 8 , E F = 1 0 , E F$ 到平面ABCD的距离为3,CD与 $A B$ 间的距离为10,则这个羨除的体积是（）

![](images/0b20d843786b7fef40700be9696b87c6435619b5f90971e60903b2c1d0f49859.jpg)

A.110 B.116   
C.118 D.120

变式2 如图所示，在直径 $A B = 4$ 的半圆 $o$ 内作一个内接直角三角形ABC，使$\angle B A C = 3 0 ^ { \circ }$ ，将图中阴影部分，以直线 $A B$ 为旋转轴旋转 $1 8 0 ^ { \circ }$ 形成一个几何体，求该几何体的表面积及体积.

![](images/f22e84058afdaa280284e5c3545c713d8f080f72fc1fdbfb383420c3679705fd.jpg)

#

例3如图一个半球,挖掉一个内接直三棱柱 $A B C - A _ { 1 } B _ { 1 } C _ { 1 }$ （棱柱各顶点均在半球面上）， $A B = A C$ ,棱柱侧面 $B B _ { 1 } C _ { 1 } C$ 是一个边长为4的正方形.

(1)求挖掉的直三棱柱的体积;   
(2)求剩余几何体的表面积.

![](images/dedea7b5347973a48124873f01402fdc29d992e9aa2ad2df8b9358e67251c215.jpg)

# 题型三截面问题

例4如图,在正方体 $A B C D - A _ { 1 } B _ { 1 } C _ { 1 } D _ { 1 }$ 中，点 $E , F$ 分别是棱 $B _ { 1 } B , B _ { 1 } C _ { 1 }$ 的中点，点 $G$ 是棱 $C _ { 1 } C$ 的中点，则过线段 $_ { A G }$ 且平行于平面$A _ { 1 } E F$ 的截面图形为（ ）

![](images/c915e39dac6949162af5f58a47d6a359dc3afc6518050689d90f8088eeccdaef.jpg)

A.等腰梯形 B.三角形C.正方形 D.矩形

变式3 在棱长为3的正方体 $A B C D -$ $A _ { 1 } B _ { 1 } C _ { 1 } D _ { 1 }$ 中，点 $P$ 是侧面 $A D D _ { 1 } A _ { 1 }$ 上的点，且点 $P$ 到棱 $A A _ { 1 }$ 与到棱 $A D$ 的距离均为1，用过点 $P$ 且与 $B D _ { 1 }$ 垂直的平面去截该正方体，则截面在正方体底面ABCD的投影多边形的面积是（

AB.5C.D.8

例5在三棱锥 $P$ -ABC中， $P B = 6 , A C = 3$ ，$G$ 为 $\triangle P A C$ 的重心，过点 $G$ 作三棱锥的一个截面,使截面平行于直线PB 和AC,则截面的周长为

# 第2讲空间位置关系、空间角与空间距离···

# 【核心知识聚焦】

# 一、线线平行、线面平行、面面平行的转换关系

判定 线//面 判定性质 性质线//线 面//面性质

1.证明直线与平面平行的常用方法：(1)利用定义，证明直线 $^ { a }$ 与平面 $\pmb { \alpha }$ 没有公共点，一般结合反证法证明；(2)利用线面平行的判定定理,即线线平行 $\Rightarrow$ 线面平行.辅助线的作法为：平面外直线的端点进平面，同向进面，得平行四边形的对边，不同向进面，延长交于一点得平行于第三边的线段；

(3)利用面面平行的性质定理,把面面平行转化成线面平行.

2.证明面面平行的常用方法：

(1)利用面面平行的定义，此法一般与反证法结合；(2)利用面面平行的判定定理；(3)利用两个平面垂直于同一条直线；(4)证明两个平面同时平行于第三个平面

3.证明线线平行的常用方法：(1)利用直线和平面平行的性质定理；(2)利用平行公理.

# 二、线线垂直、线面垂直、面面垂直的转换关系：

直线 $\perp$ 直线定定直线 $\perp$ 平面定理平面」平面

1.证明线线垂直的方法

(1)等腰三角形底边上的中线是高；(2)勾股定理的逆定理；(3)菱形对角线互相垂直；(4)圆的直径所对的圆周角是直角；(5)向量的数量积为零；(6)线面垂直的性质 $( a \perp \alpha , b \subset \alpha { \Rightarrow } a \perp b )$ ;(7)平行线垂直直线的传递性 $( a \perp c , a / / b$ $\Rightarrow b \bot c )$ ：

# 2.证明线面垂直的方法

(1)线面垂直的定义；(2)线面垂直的判定定理 $( a \perp b , a \perp c , c \mathsf { C }$ $\alpha , b \subset \alpha , b \cap c = P \Rightarrow a \perp \alpha )$ (3)面面垂直的性质定理 $( \alpha \bot \beta , \alpha \cap \beta =$ $\ : b , a \perp b , a \subset \alpha { \Rightarrow } a \perp \beta )$ (4)平行线垂直平面的传递性 $( a \perp \alpha , b / / a$ $\Rightarrow b \bot \alpha )$ ;（5)面面垂直的性质 $( \alpha \perp \gamma , \beta \perp \gamma , \alpha \cap \beta =$ $l { \Rightarrow } l \bot \gamma$ ）

3.证明面面垂直的方法

(1)面面垂直的定义；(2)面面垂直的判定定理 $( a \perp \beta , a \subset \alpha \preceq$ $\alpha \perp \beta )$

# 三、空间中的线面平行、垂直的位置关系结构图.

性质 线//面 判定  
线//线 判定判定 性质 面//面  
性质 判定 性质判定质  
判定 判定  
线线 线面 面上面  
性质 性质

# 四、空间角与空间距离

1.线与线的夹角

（1)位置关系的分类：平行直线  
共面直线相交直线  
异面直线：不同在任何一个平面内，没有公共点

（2)异面直线所成的角

定义：设 $a , b$ 是两条异面直线，经过空间 任一点 $o$ 作直线 $a ^ { \prime } / / a , b ^ { \prime } / / b$ ,把 $a ^ { \prime }$ 与 $b ^ { \prime }$ 所成 的锐角(或直角)叫做异面直线 $^ { a }$ 与 $b$ 所成的 角(或夹角).

范围： $\left( 0 , { \frac { \pi } { 2 } } \right]$

2.线与面的夹角

定义：平面的一条斜线与它在平面的射影所成的锐角即为斜线与平面的夹角.范围 $\left[ 0 , { \frac { \pi } { 2 } } \right]$ ：

3.二面角

(1)二面角定义：从一条直线出发的两个半平面所组成的图形称为二面角,这条直线称为二面角的棱,这两个半平面称为二面角的面.（记作二面角 $\alpha - l - \beta$ 或者是二面角 $A - C D - B )$

![](images/1f85a47fd9ed22d2601f7ea280d3a0952ff40a9df4204155f2c27a9157b7e450.jpg)

(2)二面角的平面角的概念：平面角是指以二面角的棱上一点为端点，在两个半平面内分别作垂直于棱的两条射线，这两条射线所成的角就叫做该二面角的平面角，它的范围是$[ 0 , \pi ]$

4.空间中的距离：求点到平面的距离转化为三棱锥等体积法求解.

# 【进阶提升】

# 题型一 平行与垂直的判定与性质

例1已知直线 $m \perp$ 平面 $\alpha , n$ 表示直线， $\beta$ 表示平面，有以下四个结论：$\textcircled { 1 } \alpha \perp \beta \Rightarrow m / / \beta ; \textcircled { 2 } m / / n , n \subset \beta \Rightarrow \alpha \perp \beta ;$ $\textcircled { 3 } n / / \alpha { \Rightarrow } m \perp n$ $\textcircled{4}$ 若 $\beta$ 与 $_ m$ 相交，则 $\beta$ 与 $\alpha$ 相交.其中正确的结论的个数是（）

A.4 B.3 C.2 D.1

例2（多选题)如图,在正方体中， $O$ 为底面的中心， $P$ 为所在棱的中点， $M , N$ 为正方体的顶点.则满足 $M N \bot O P$ 的是（ ）

![](images/23fb572083c9d329fe933ff41533de2d1a7de0dab6bd89baed2656cee585c884.jpg)

变式1 （多选题）已知 $O _ { 1 }$ 是正方体$A B C D \mathrm { ~ - ~ } A _ { 1 } B _ { 1 } C _ { 1 } D _ { 1 }$ 的中心 $O$ 关于平面$A _ { 1 } B _ { 1 } C _ { 1 } D _ { 1 }$ 的对称点，则下列说法中错误的是（

A. $O _ { 1 } C _ { 1 }$ 与 $A _ { 1 } C$ 是异面直线B. $O _ { 1 } C _ { 1 }$ //平面 $A _ { 1 } B C D _ { 1 }$

![](images/9cf368ee190bcb03da1bfb68d04d7ebfe2759e691ce5d251abfcf7713ae3eaba.jpg)

$\complement _ { \cdot } O _ { 1 } C _ { 1 } \bot A D$ D. $0 , C _ { 1 } \bot$ 平面 $B D D _ { 1 } B _ { 1 }$

# 题型二空间角

例3如图，已知三棱锥S-ABC中， $S C \bot$ 平面 $A B C$ $\angle A B C = 9 0 ^ { \circ }$ ，且 $A B = 2 B C = 2 S C$ ， $D , E$ 分别为SA，BC的中点，则异面直线 $D E$ 与AC所成角的余弦值为（）

![](images/b4e9cc84da7b28b28b674c0c5372c25d58a5aacc2ec73f6aebec4fd727a59d5d.jpg)

$$
{ \mathrm { A . } } { \frac { 3 } { 5 } } \qquad { \mathrm { B . } } { \frac { 4 } { 5 } } \qquad { \mathrm { C . } } { \frac { \sqrt { 1 0 } } { 1 0 } } \quad { \mathrm { D . } } { \frac { 3 { \sqrt { 1 0 } } } { 1 0 } }
$$

例4如图,在三棱台 $A B C - A _ { 1 } B _ { 1 } C _ { 1 }$ 中，$A A _ { 1 } \bot$ 平面 $A B C$ ， $\angle A B C = 9 0 ^ { \circ }$ $A A _ { 1 } = A _ { 1 } B _ { 1 } =$ $B _ { 1 } C _ { 1 } = 1$ $A B = 2$ ,则 $A C$ 与平面 $B C C , B _ { 1 }$ 所成的角为（）

![](images/78116f8910a2e1514cc5fec8e5b25a0cda86e020ee19b38deac0fcfa938b14f3.jpg)

A B.4 c. D.

变式2 在三棱锥A-BCD中， $A B \perp$ 平面 $B C D$ ， $C D \bot B C$ ，且 $B C = \sqrt { 3 } \ A B$ ，则直线$A B$ 与平面ACD所成的角为（）

$$
{ \mathrm { A } } . { \frac { \pi } { 6 } } \qquad { \mathrm { B } } . { \frac { \pi } { 4 } } \qquad { \mathrm { C } } . { \frac { \pi } { 3 } } \qquad { \mathrm { D } } . { \frac { \pi } { 2 } }
$$

变式3（多选题）已知正方体 $A B C D \textrm { - }$ $A _ { 1 } B _ { 1 } C _ { 1 } D _ { 1 }$ ，则（）

A.直线 $B C _ { 1 }$ 与 $D A _ { 1 }$ 所成的角为 $9 0 ^ { \circ }$ B.直线 $B C _ { 1 }$ 与 $C A _ { 1 }$ 所成的角为 $9 0 ^ { \circ }$ C.直线 $B C _ { 1 }$ 与平面 $B B _ { 1 } D _ { 1 } D$ 所成的角 为 $4 5 ^ { \circ }$ D.直线 $B C _ { 1 }$ 与平面ABCD所成的角为

# 题型四空间距离

例5已知在正四面体 $P - A B C$ 中，点 $D$ ，$E , F$ 分别在棱 $P A , P B , P C$ 上,若 $P E = 4 , P F =$ $P D = 2$ ，则点 $P$ 到平面DEF的距离为（ ）

$\mathrm { A } . { \frac { \sqrt { 1 1 } } { 2 } }$ $\mathrm { B } . \frac { 4 \sqrt { 2 2 } } { 1 1 }$ C.35 D.25 4 3

变式4 如图，在四棱锥 $P - A B C D$ 中， 底面ABCD为矩形， $P A \perp$ 底面ABCD， $P A =$ $A B = \sqrt { 2 }$ $A D = 1$ ，点 $E$ 是棱 $P B$ 的中点直 线 $A B$ 与平面ECD的距离为（

![](images/84e236a24d2dbe9e71108dac035e27dcfa17e277db61c970d3bd118e41a924c7.jpg)

A1BD.√

# 题型五空间位置关系的证明

例6小明同学参加综合实践活动，设计了一个封闭的包装盒，包装盒如图所示：底面ABCD是边长为8（单位：cm）的正方形，△EAB,△FBC,△GCD, $\triangle H D A$ 均为正三角形，且它们所在的平面都与平面ABCD垂直.

![](images/01cd356ea7c90bc6f0a7490cd485ed03db0109f1133114cb9c1428813912429c.jpg)

（1）证明：EF//平面ABCD；(2)求该包装盒的容积（不计包装盒材料的厚度).

![](images/89c043d4115bf20f8dfdcedf325da60b16e690aa92df63e742740070d554f832.jpg)

变式5 如图，在三棱锥A-BCD中，平面 $A B D \perp$ 平面 $B C D$ ， $A B = A D$ ， $O$ 为 $B D$ 的中点

![](images/e4fe0737b0c7a6f84f9b993dfbca25c089909beee536a1d277bb306cf5a4f785.jpg)

(1）证明： $O A \bot C D$ (2)若 $\triangle { O C D }$ 是边长为1的等边三角形，点 $E$ 在棱 $A D$ 上， $D E = 2 E A$ ，且二面角 $E$ $- B C - D$ 的大小为 $4 5 ^ { \circ }$ ，求三棱锥 $A - B C D$ 的体积.

面四五烟， ()= = ak,n= 08-0k,m=

以山三 （m）

![](images/32af6a44fde6cef0ba6ec36845ab3df05126ca3197c2e55c4be0978f4f6a8294.jpg)

![](images/13f5536562e516e052cd1cb4c2ea0b7ae884d1687717f84a0746d948f791646c.jpg)

# ·第3讲空间几何体的外接球、内切球与棱切球 ${ \mathfrak { o } } _ { \| }$

# 【核心知识聚焦】

# 一、正方体、长方体的外接球

1.正方体的外接球的球心为其体对角线的中点，半径为体对角线长的一半.2.长方体的外接球的球心为其体对角线的中点，半径为体对角线长的一半.

# 3.补成长方体

(1)若三棱锥的三条侧棱两两互相垂直， 则可将其放入某个长方体内，如图 $\textcircled{1}$ 所示.

(2)若三棱锥的四个面均是直角三角形， 则此时可构造长方体,如图 $\textcircled{2}$ 所示.

${ \frac { \sqrt { 2 } } { 2 } } a$ ,显然正四面体和正方体有相同的外球.正方体外接球半径为 $R = \frac { \sqrt { 2 } } { 2 } a \cdot \frac { \sqrt { 3 } } { 2 } = :$ $^ a$ ,即正四面体外接球半径为 $R = { \frac { \sqrt { 6 } } { 4 } } a$

(4)如图 $\textcircled{4}$ ，在四面体ABCD中， $A B = 1$ $\ O = m , A C = B D = n , A D = B C = t$ ，这种四面体做对棱相等四面体，可以通过构造长方体来决这类问题.

![](images/f12aaeb2b6629a94e27f45f11df18d781fb582d29a698c2f5fb87b0d7fa8c659.jpg)  
图 $\textcircled{1}$

设长方体的长、宽、高分别为 $a , b , c ,$ $\left\lfloor { \begin{array} { l } { b ^ { 2 } + c ^ { 2 } = m ^ { 2 } } \\ { a ^ { 2 } + c ^ { 2 } = n ^ { 2 } } \\ { a ^ { 2 } + b ^ { 2 } = t ^ { 2 } } \\ { m ^ { 2 } + n ^ { 2 } + t ^ { 2 } } \\ { 2 } \end{array} } \right\rfloor { \left( \begin{array} { l } { a } \\ { { } } \\ { a ^ { 2 } + c ^ { 2 } = n ^ { 2 } } \\ { { } } \end{array} \right) }$ ,三式相加可得 $a ^ { 2 } + b ^ { 2 } + c$

![](images/d9729db31d004d884652757b9e812da08a28a9790a39abd00186e9252f939514.jpg)  
图 $\textcircled{2}$

(3)如图 $\textcircled{3}$ ,设正四面体ABCD的棱长为$^ { a }$ ，将其放人正方体中，则正方体的棱长为

而显然四面体和长方体有相同的外接 设外接球半径为 $R$ ，则 $a ^ { 2 } + b ^ { 2 } + c ^ { 2 } = 4 R ^ { 2 } , \ddot { \mu }$ $R = { \sqrt { \frac { m ^ { 2 } + n ^ { 2 } + t ^ { 2 } } { 8 } } } .$

![](images/51ca0a7aeba6677f035ddaf4b39a529991a6a84496da4c4fb0d71f96c3f67afa.jpg)  
图 $\textcircled{3}$

![](images/****************************************77066a7542f4a87d61a0293e.jpg)  
图④

# 二、直棱柱的外接球

如图 $\textcircled{1}$ ，图 $\textcircled{2}$ ，图 $\textcircled{3}$ ,直三棱柱内接于球（同时直棱柱也内接于圆柱,棱柱的上、下底面可以是任意三角形）

![](images/aec9a5c92e45cc250a1460451b768360f3e852408d0b9b46e37c2c7e63adb7f4.jpg)  
图①

![](images/f9c5d7886b3f49c01d2aa4a8f2110754a215a1ec09385562aa2fb95f146a2315.jpg)  
图②

![](images/d5f5ce1e53b1c445872207d325d5f4a9173b01318e90781b47aabbe15f96587c.jpg)  
图 $\textcircled{3}$

(1)确定球心 $o$ 的位置， $O _ { 1 }$ 是 $\triangle A B C$ 的外心，则 $O O _ { 1 } \bot$ 平面 $A B C$ (2)算出小圆 $O _ { \imath }$ 的半径 $O _ { 1 } A = r , O _ { 1 } O =$ $\frac { 1 } { 2 } A A _ { 1 } = \frac { 1 } { 2 } h ( A A _ { 1 } = h , h$ 是圆柱的高）；(3)勾股定理： ${ \cal O } A ^ { 2 } = { \cal O } _ { 1 } A ^ { 2 } + { \cal O } _ { 1 } { \cal O } ^ { 2 } \Rightarrow { \cal R } ^ { 2 } =$ $r ^ { 2 } + \left( \frac { h } { 2 } \right) ^ { 2 } \Rightarrow R = \sqrt { r ^ { 2 } + \left( \frac { h } { 2 } \right) ^ { 2 } }$ ，解出 $R .$

# 三、有一条侧棱垂直于底面的三棱锥的外接球

如图， $P A \perp$ 平面 $A B C$ ,求三棱锥P-ABC的外接球的半径.

![](images/b6993995c0cb307029c6670496a66e08773ab49ee39377ea7f20de1e45148b7c.jpg)

解题步骤：

(1)将 $\triangle A B C$ 画在小圆面上，A为小圆直径的一个端点，作小圆的直径 $A D$ ,连接 $P D$ ,则$P D$ 必过球心 $o$

(2)设点 $O _ { \imath }$ 为 $\triangle A B C$ 的外心，所以 $O O _ { 1 } \bot$ 平面 $A B C$ ,算出小圆 $O _ { 1 }$ 的半径 $O _ { 1 } D = r$ （三角形的外接圆直径算法：利用正弦定理,得 $\frac { a } { \sin A } =$ $\frac b { \sin B } = \frac c { \sin C } = 2 r ) \ , \ O O _ { 1 } = \frac 1 2 P A ;$

(3)利用勾股定理求三棱锥的外接球半径：$\begin{array} { r c l } { { \displaystyle \bigoplus } } & { { ( 2 R ) ^ { 2 } } } & { { = } } & { { P A ^ { 2 } + ( 2 r ) ^ { 2 } \iff 2 R = } } \\ { { } } & { { } } & { { } } \\ { { \displaystyle \sqrt { P A ^ { 2 } + ( 2 r ) ^ { 2 } } ; \bigoplus R ^ { 2 } } } & { { = } } & { { r ^ { 2 } + O O _ { 1 } { } ^ { 2 } \iff R } } \\ { { } } & { { } } & { { } } \\ { { \displaystyle \sqrt { r ^ { 2 } + O O _ { 1 } { } ^ { 2 } } . } } & { { } } & { { } } \\ { { } } & { { } } & { { } } \end{array}$

# 四、正棱锥与侧棱相等模型

1.正棱锥的外接球半径： $R = { \frac { r ^ { 2 } + h ^ { 2 } } { 2 h } } ,$

![](images/9657f1f45c7ecba8d6fdb502bac04b7697e562d2a1764917057cd58c5633a9f7.jpg)

2.侧棱相等模型：

如图， $P$ 的射影是 $\triangle A B C$ 的外心 $\Leftrightarrow$ 三棱锥P-ABC的三条侧棱相等 $\Leftrightarrow$ 三棱锥 $P { - } A B C$ 的底面△ABC在圆锥的底上，顶点 $P$ 也是圆锥的顶点.

![](images/d9e5bc7617bc96401312d36a6d3ce20f97771255af7c9e472b3823f395d36eb0.jpg)

解题步骤：

(1)确定球心 $o$ 的位置，取 $\triangle A B C$ 的外心$O _ { 1 }$ ，则 $P , O , O _ { \ l }$ 三点共线；(2)先算出小圆 $O _ { \mathfrak { r } }$ 的半径 $A O _ { 1 } = r$ ,再算出棱锥的高 $P O _ { 1 } = h$ (也是圆锥的高)；(3)勾股定理： $O A ^ { 2 } = O _ { 1 } A ^ { 2 } + O _ { 1 } O ^ { 2 } \Rightarrow R ^ { 2 } =$ $( h - R ) ^ { 2 } + r ^ { 2 }$ ，解出 $R = \frac { r ^ { 2 } + h ^ { 2 } } { 2 h } .$

# 五、共斜边拼接模型

如图,在四面体ABCD中， $A B \perp A D$ ， $C B \perp$ $C D$ ,此四面体可以看成是由两个共斜边的直角三角形拼接而形成的， $B D$ 为公共的斜边,故以“共斜边拼接模型”命名之.设点 $o$ 为公共斜边$B D$ 的中点，根据直角三角形斜边中线等于斜边的一半的结论可知， $O A = O B = O C = O D$ ,即点 $o$ 到 $A , B , C , D$ 四点的距离相等，故点 $o$ 就是四面体ABCD外接球的球心,公共的斜边 $B D$ 就是外接球的一条直径.

![](images/f85d7d731397d9d66c7468e6e899915cbd427626ad0f5a88d7868622aadbd811.jpg)

# 六、垂面模型

如图 $\textcircled{1}$ 所示为四面体 $P - A B C$ ，已知平面$P A B \bot$ 平面 $A B C$ ,确定其外接球球心的步骤如下：

(1)找出 $\triangle P A B$ 和 $\triangle A B C$ 的外接圆圆心，分别记为 $O _ { \imath }$ 和 $O _ { 2 }$

(2)分别过 $O _ { \imath }$ 和 $O _ { 2 }$ 作平面 $P A B$ 和平面ABC的垂线，其交点为球心,记为 $o$ (3)过 $O _ { \imath }$ 作 $A B$ 的垂线，垂足记为 $D$ ,连接$O _ { 2 } D$ ,则 $O _ { 2 } D \bot A B$ (4)在四棱锥 $A - D O _ { 1 } O O _ { 2 }$ 中， $A D$ 垂直于平面 $D O _ { 1 } O Q _ { 2 }$ ，如图 $\textcircled{2}$ 所示，底面四边形 $D O _ { 1 } O Q _ { 2 }$ 的四个顶点共圆且OD为该圆的直径.

![](images/4b653f009fcb12ab72f9b1a9ee9886677725dd2ad13dcc88c2adbe3945eca5f3.jpg)  
图①

![](images/63f89db0064eb94f24cdc2be54e49c04f480d38dfd8fa834010794de63025db6.jpg)  
图②

# 七、二面角模型

如图 $\textcircled{3}$ 所示为四面体 $P { \mathrm { - } } A B C$ ，已知二面角$P - A B - C$ 大小为 $\pmb { \alpha }$ ,确定其外接球球心的步骤如下：

(1)找出 $\triangle P A B$ 和 $\triangle A B C$ 的外接圆圆心，分别记为 $O _ { \imath }$ 和 $O _ { 2 }$ (2)分别过 $O _ { \imath }$ 和 $O _ { 2 }$ 作平面 $P A B$ 和平面ABC的垂线，其交点为球心，记为 $o$ (3)过 $O _ { \parallel }$ 作 $A B$ 的垂线，垂足记为 $D$ ,连接$O _ { 2 } D$ ,则 $O _ { 2 } D \bot A B$ (4)在四棱锥 $A - D O _ { 1 } O O _ { 2 }$ 中， $, A D$ 垂直于平面 $D O _ { 1 } O Q _ { 2 }$ ，如图 $\textcircled{4}$ 所示，底面四边形 $D O _ { 1 } O Q _ { 2 }$ 的四个顶点共圆且OD为该圆的直径.

![](images/616b0a4bb9a6d9d3f0c3a536b465c9ed6caf89b7f4fac06b6a3b31f4a8ad8ae9.jpg)  
图③

![](images/4b39e054a43b0ba64c2fe3baec73104519c3f90fe0e15ad59259e4a4b5341507.jpg)  
图 $\textcircled{4}$

# 八、圆锥、圆柱、圆台模型

1.球的内接圆锥如图 $\textcircled{1}$ ，设圆锥的高为 $h$ ,底面圆半径为 $r$ ，球的半径为 $R .$ 通常在 $\triangle { O C B }$ 中，由勾股定理建立方程来计算 $R _ { ☉ }$ 如图 $\textcircled{2}$ ,当 $P C > C B$ 时,球心在圆锥内部；如图 $\textcircled{3}$ ,当 $P C < C B$ 时,球心在圆锥外部.和本专题前面的内接正三棱锥问题情形相同，图 $\textcircled{2}$ 和图 $\textcircled{3}$ 两种情况建立的方程是一样的,故无需提前判断.

由图 $\textcircled{2}$ 、图 $\textcircled{3}$ 可知， $O C = h - R$ 或 $R - h$ ,故$\left( h - R \right) ^ { 2 } + r ^ { 2 } = R ^ { 2 }$ ，所以 $R = \frac { h ^ { 2 } + r ^ { 2 } } { 2 h }$

![](images/75580fb39e796cc8a63e57686fa4acc918a7a13f45c893a6d8b18edaf3873a7f.jpg)  
图 $\textcircled{1}$

![](images/****************************************b3aa1f64442ba2577dc29b02.jpg)  
图 $\textcircled{2}$

![](images/****************************************de4af209dab2045d28dd0865.jpg)  
图 $\textcircled{3}$

2.球的内接圆柱

如图,圆柱的底面圆半径为 $r$ ，高为 $h$ ,其外 接球的半径为 $R$ ，三者之间满足 $\left( { \frac { h } { 2 } } \right) ^ { 2 } + r ^ { 2 } = R ^ { 2 }$

![](images/a6910e989c37007f8bd5368957397bc143eb014d7774772a5965f9e4678f7fef.jpg)

3.球的内接圆台

$R ^ { 2 } = r _ { 2 } ^ { 2 } + \left( \frac { r _ { 2 } ^ { 2 } - r _ { 1 } ^ { 2 } - h ^ { 2 } } { 2 h } \right) ^ { 2 }$ ,其中 $r _ { 1 } , r _ { 2 } , h$ 分别为圆台的上底面半径、下底面半径、高.

# 九、锥体内切球

1.几何法：可利用相似三角形列关于内切球半径的比例式；

2.等体积法公式，即 $R = \frac { 3 V } { S }$ ， ，其中 $V$ 是锥

体的体积,S是锥体的表面积.

# 十、棱切球

方法：找切点,找球心,构造直角三角形.

# 【进阶提升】

题型一正方体、长方体的外接球

例1在三棱锥A-BCD 中, $B D \perp$ 平面ADC, $B D = 2$ $A B = 2 \sqrt { 2 }$ $A C = B C = 2 { \sqrt { 5 } }$ ，则三棱锥A-BCD的外接球的体积为

变式1 在三棱锥 $P { - } A B C$ 中， $P A =$ $B C = 4 , P B = A C = 5 , P C = A B = \sqrt { 1 1 }$ ，则三棱锥 $P { - } A B C$ 的外接球的表面积为（）

$$
\begin{array} { r l r } { \mathrm { A } . 2 6 \pi } & { { } \mathrm { B } . 1 2 \pi } & { \mathrm { C } . 8 \pi } \end{array} \quad \mathrm { D } . 2 4 \pi
$$

# 题型二多面体的外接球

例2在三棱锥 $P \ - \ A B C$ 中， $\angle P A C =$ $\angle P A B , A C = 2 A B = 4 , P A = P B = \sqrt { 2 } , B C = 2 \sqrt { 3 }$ 则三棱锥 $P - A B C$ 外接球的表面积为（）

$$
{ \mathrm { A . ~ } } 2 2 \pi \qquad { \mathrm { B . ~ } } 2 6 \pi \qquad { \mathrm { C . ~ } } { \frac { 6 4 \pi } { 3 } } \qquad { \mathrm { D . ~ } } { \frac { 6 8 \pi } { 3 } }
$$

例3在三棱锥 $P - A B C$ 中， $P A \perp$ 底面ABC, $^ { \dag } A = 4 , A B = A C = B C = 2 a , M$ 为 $A C$ 的中点，球 $o$ 为三棱锥 $P - A B M$ 的外接球, $D$ 是球$o$ 上任一点,若三棱锥 $D - P A C$ 体积的最大值是 $4 { \sqrt { 3 } }$ ,则球 $o$ 的体积为

变式2 如图，在平面四边形ABCD中， $A D \perp C D$ $A C \bot B C$ ， $\angle D A C = \angle B A C =$ $3 0 ^ { \circ }$ ，现将 $\triangle A C D$ 沿 $A C$ 折起，并连接 $B D$ ，使得平面 $A C D \perp$ 平面 $A B C$ ，若所得三棱锥 $D$ $- A B C$ 的外接球的表面积为 $4 \pi$ ，则三棱锥$D - A B C$ 的体积为（

![](images/adae6d42a1afee9c8420042f1a5a98daa6c3adda69ce1cb7918073e72f200188.jpg)

$$
\mathrm { A } \frac { 1 } { 4 } \quad \mathbb { B } . \frac { \sqrt { 3 } } { 4 } \quad \mathrm { C } . \frac { \sqrt { 3 } } { 8 } \quad \mathbb { D } . \frac { \sqrt { 3 } } { 6 }
$$

变式3 在三棱锥 $P - A B C$ 中，三条棱$P A$ ， $P B$ ，PC两两垂直，且 $P A = P B = P C =$ 2，则平面 $A B C$ 截该三棱锥的外接球所得截面圆的面积为

例4在三棱锥 $A - B C D$ 中， $\triangle B C D$ 是边长为3的正三角形，且 $A D = { \sqrt { 3 } }$ $A B = 2 \sqrt { 3 }$ ，二面角 $A - B D - C$ 的大小为 $6 0 ^ { \circ }$ ,则此三棱锥的外接球的体积为

例5在正四棱台 $A B C D - A _ { 1 } B _ { 1 } C _ { 1 } D _ { 1 }$ 中，$A _ { 1 } B _ { 1 } = 2 A B = 4 , A A _ { 1 } = 2$ ，则（ ）

A.该棱台的体积为 $2 8 \sqrt { 2 }$ ,该棱台外接球的表面积为 $4 0 \pi$ B.该棱台的体积为 $\frac { 2 8 { \sqrt { 2 } } } { 3 }$ ,该棱台外接球的表面积为 $4 0 \pi$ C.该棱台的体积为 $2 8 \sqrt { 2 }$ ,该棱台外接球的表面积为 $5 2 \pi$ D.该棱台的体积为 $\frac { 2 8 { \sqrt { 2 } } } { 3 }$ ，该棱台外接球的表面积为 ${ 5 2 \pi }$

# 题型三多面体的内切球

例6六氟化硫是一种无机化合物,化学式为 $\mathrm { S F } _ { 6 }$ ，常温常压下为无色无臭无毒不燃的稳定气体，密度约为空气密度的5倍,是强电负性气体，广泛用于超高压和特高压电力系统.六氟化硫分子结构呈正八面体排布(8个面都是正三角形).若此正八面体的表面积为 $3 2 { \sqrt { 3 } }$ ,则该正八面体的内切球的体积为

![](images/33c6c34fa0c40e312689294ee4acc73bbee652fc68606a68ba593ad8aa043da1.jpg)

变式4 正四棱锥的外接球与内切球的半径分别是 $R , r$ ，则 $\frac { R } { r }$ 的取值范围是题型四多面体的棱切球

例7已知正三棱锥S-ABC, $S A = S B =$ $S C = 2 \sqrt { 3 }$ $A B = 3$ ,球 $o$ 与三棱锥 $S - A B C$ 的所有棱相切，则球 $O$ 的表面积为

变式5 正四面体 $P - A B C$ 的棱长为 4，若球 $O$ 与正四面体的每一条棱都相切， 则球 $O$ 的表面积为（

A.2π $\mathrm { B } . 8 \pi$ 82C. 3 TT D.12π

# 题型五旋转体的外接球与内切球

例8(多选题)如图，已知圆锥顶点为 $P$ ，其轴截面 $\triangle P A B$ 是边长为6的为正三角形， $O _ { \parallel }$ 为底面的圆心， $E F$ 为圆 $O _ { \imath }$ 的一条直径，球 $O$ 内切于圆锥(与圆锥底面和侧面均相切）,点 $Q$ 是球 $o$ 与圆锥侧面的交线上一动点,则（）

![](images/9effbbe999fa452786b77c438c034c92519edf0f17010adad4a50f5b3a5d8dc1.jpg)

A.圆锥的表面积是 $4 5 \pi$

B.球 $O$ 的体积是 $4 { \sqrt { 3 } } \pi$   
C.四棱锥 $Q - A E B F$ 体积的最大值为 $9 { \sqrt { 3 } }$   
D. $Q E + Q F$ 的最大值为 $6 \sqrt { 2 }$

变式6 （多选题）已知圆锥 $O P$ 的底 面半径 $r = \sqrt { 3 }$ ，侧面积为 $6 \pi$ ，内切球的球心 为 $O _ { 1 }$ ，外接球的球心为 $O _ { 2 }$ ，则下列说法正 确的是（

A.外接球 $O _ { 2 }$ 的表面积为 $1 6 \pi$ B.设内切球 $O _ { 1 }$ 的半径为 $r _ { 1 }$ ，外接球$O _ { 2 }$ 的半径为 $r _ { 2 }$ ，则 $r _ { 2 } = 2 r _ { 1 }$ C.过点 $P$ 作平面 $\alpha$ 截圆锥 $O P$ 的截面面积的最大值为2D.设母线 $P B$ 的中点为 $M$ ，从点 $A$ 沿圆锥表面到点 $M$ 的最短路线长为 $\sqrt { 1 5 }$

![](images/181fea6cb985c5231cd85a397e4223aaf86de1c524f5dc49f7aa289ee98214b3.jpg)

# 题型六最值问题

例9已知球 $o$ 的半径为1,四棱锥的顶点为 $o$ ,底面的四个顶点均在球 $O$ 的球面上，则当该四棱锥的体积最大时,其高为（ ）

$$
{ \mathrm { A . ~ } } { \frac { 1 } { 3 } } \qquad { \mathrm { B . ~ } } { \frac { 1 } { 2 } } \qquad { \mathrm { C . ~ } } { \frac { \sqrt { 3 } } { 3 } } \qquad { \mathrm { D . ~ } } { \frac { \sqrt { 2 } } { 2 } }
$$

变式7 如图，四棱锥 $P - A B C D$ 的底面ABCD是直角梯形， $B C \bot C D$ ， $B C / / A D$ ，$A D = 2 B C = 2 C D = 2 , P A = P B = 1$ ，当三棱锥$D - P A B$ 的体积最大时，三棱锥 $D - P A B$ 的外接球的体积为

![](images/19608eb62b9dec156168040e4ecad03b80ab09d4fe464be20b2848f24cc976d0.jpg)

# 第4讲 立体几何中的最值与范围问题·

# 【核心知识聚焦】

1.立体几何中求关于距离的最值的一般处理方式：

（1)几何法：通过位置关系，找到取最值的位置(条件),直接求最值；(2)代数法：建立适当的坐标系，利用代数

法求最值.

2.对于几何体内部的折线的最值，可采用转化法，转化为两点间的距离，结合勾股定理求解；

3.计算多面体或旋转体的表面上的最值问题时，一般采用转化的方法来进行，即将侧面展开化为平面图形，即“化折为直"或“化曲为直"来解决，要熟练掌握多面体与旋转体的

侧而展开图的形状；

4、空间中动线段的距离和的最值问题，可以类比平而中的距离和的最值处理方式，利用对称性来转化，另外异面直线间的公垂线段的长度可利用点到平而的距离来处理

# 【进阶提升】

# 题型一 折线最值问题

例1如图所示，在直三棱柱ABC-$A _ { 1 } B _ { 1 } C _ { 1 }$ 中、 ${ \bf \nabla } \cdot { \bf A } A _ { 1 } = 1$ $A B = B C = \sqrt { 3 }$ $\cos \angle A B C =$ ${ \frac { 1 } { 3 } } , P$ 是 $A _ { 1 } B$ 上的一动点、则 $A P + P C _ { 1 }$ 的最小值为（）

![](images/2a6d3c78e4d9e6d543dafaec5d64bd60320b1c7556094f2fc4532a4d9a7ba9e8.jpg)

A.√5B.√C.1+√3D.3

变式】 如图，在棱长为丨的正方体$\ A B C D - A ^ { \prime } B ^ { \prime } C ^ { \prime } D ^ { \prime }$ 中，点 $P$ 是线股 $A D ^ { \prime }$ 上的动点， $E$ 是 $A ^ { \prime } C ^ { \prime }$ 上的动点， $F$ 是 $B D$ 上的动点，则 $P E + P F$ 长度的最小值为（ ）

![](images/806b73d1e1ea5e848ee411aacc8667f2d446a296f6335888fcde577c1f73566f.jpg)

A1 B2CD1+

例2如图，在圆锥 $P O$ 中， $\varLambda B$ 是底面圆 $o$ 的直径，点 $C$ 是圆 $o$ 上异于 $A , B$ 的点， $P O =$ $O B = 1$ $B C = \sqrt { 2 }$ ，点 $E$ 在线段 $P B$ 上，则 $C E +$

OE的最小值为

![](images/00b949fc1fa578b32f18d80c5c929bd0248ed76def70d0db86e0b75b4047022d.jpg)

变式2 在三枝A-BCD $| \big | ^ { \flat }$ ，所有的校长都相等， $E$ 为 $\varLambda B$ 的中点， $F$ 为 $\varLambda C$ 上动点，若 $D F + F E$ 的最小值为 $2 \sqrt { 7 }$ ，则该三枝锥的外接球体积为（

$$
\begin{array} { l l l } { { \Lambda . 8 \sqrt { 6 } \pi } } & { { } } & { { { \mathrm { B } . 7 } \sqrt { 6 } \pi } } \\ { { } } & { { } } & { { } } \\ { { \mathsf { C } . 6 \sqrt { 6 } \pi } } & { { } } & { { { \mathrm { D } . 5 } \sqrt { 6 } \pi } } \end{array}
$$

例3如图，在棱长为1的正方体ABCD-$A _ { 1 } B _ { 1 } C _ { 1 } D _ { 1 }$ 中， $P$ 为线段 $\varLambda B _ { 1 }$ 的中点， $M , N$ 分别为体对角线 $\ d _ { \mathscr { A } } C _ { \mathscr { r } }$ 和棱 $C _ { 1 } D _ { \parallel }$ 上任意一点，则$2 P M + { \sqrt { 2 } } M N$ 的最小值为（）

![](images/b4e263ab47528223a9400d06c29e5d8db005c304e8b8f4d5ad3b8ac26d9af8a1.jpg)

2 B.√2C.2D.2√2

# 题型二两线上点的距离的最值问题

例4如图，三棱锥 $O - A B C$ 各棱的棱长均为 $\sqrt { 3 }$ ，点 $D$ 是棱 $\varLambda B$ 的中点，点 $E$ 是棱 $O C$ 上的动点，则 $D E$ 的最小值为（）

![](images/42eeb07c0b92c0e795783feee5802a65f9e3d7616726359cbf6abda30a010efe.jpg)

B. c. D.1 2 2

变式3 已知正方体ABCD-A,B,CD,的校长为1，在对角线 $A _ { 1 } D$ 上取点M，在$C { \cal D } _ { 1 }$ 上取点 $N$ 使得线段MN平行于对角而 $\Lambda C C _ { 1 } \Lambda _ { 1 }$ ，则线段MN长的最小值为）

$\Lambda . { \sqrt { 2 } }$

# 题型三面上动点距离的最值问题

例5在棱长为丨的正方体 $A B C D - A _ { 1 } B _ { 1 }$ $C _ { 1 } D _ { 1 }$ 中，点 $M , N$ 分别是棱BC， $C C _ { 1 }$ 的中点，动点 $P$ 在正方形 $B C C , B _ { 1 }$ （包括边界)上运动.若$P A _ { 1 }$ //平面AMN，则 $P A _ { 1 }$ 的最小值是（）

![](images/7de36aece77c5fe534ff2ece005d50ebc90385f8ad676923edb604c574fa0041.jpg)

A.1 B. c.3 D.

变式4 在正四枝锥S-ABCD中，底面边长为 $2 \sqrt { 2 }$ ，侧枝长为4，点 $\mathcal { P }$ 是底面ABCD内一动点，且 $S P = { \sqrt { 1 3 } }$ ，则 $A , P$ 两点间距离的最小值为（）

$$
1 . \frac { 1 } { 2 } \qquad \mathrm { B } . \frac { 2 } { 3 } \qquad \mathrm { C } . 1 \qquad \mathrm { D } . \sqrt { 2 }
$$

# 题型四周长的最值问题

例6如图，在正三棱锥 $\ A \ - \ B C D$ 中，$\triangle B C D$ 是正三角形. $\angle B A D = 2 0 ^ { \circ }$ ，侧梭长为4，过点 $C$ 的平面与侧梭 $\varLambda B$ ， $\varLambda D$ 相交于点 $B _ { \parallel }$ $D _ { \parallel }$ ，则 $\triangle C B _ { 1 } D _ { 1 }$ 的周长的最小值为（）

![](images/efc8a90c40d0114ad3517484d6a1b22fd61eadaddeb890ab25a43d1d82f8bcc9.jpg)

A.2√2B.4 C.2√3D.2

变式5 如图，在枝长为2的正方休$\lambda B C D - A _ { 1 } B _ { 1 } C _ { 1 } D _ { 1 }$ 中， $E$ 为校 $C C _ { 1 }$ 的中点，$P , Q$ 分别为面 $A _ { 1 } B _ { 1 } C _ { 1 } D _ { 1 }$ 和线段 $\boldsymbol { B } _ { 1 } \boldsymbol { C }$ 上的动点，则 $\triangle E P Q$ 周长的最小值为（）

![](images/5e0175b717185ea7aa402f6995a79ab4f375607c6d89b028dd2d3e6e0f264349.jpg)

.2√2 B.√10   
C.2√3 D.3√2

题型五其他(面积、体积、角度)最值问题

例7已知正方体 $\varLambda B C D - A _ { 1 } B _ { 1 } C _ { 1 } D _ { 1 }$ 的棱长为 $2 \sqrt { 5 } , M$ 为 $C C _ { \parallel }$ 的中点，点 $N$ 在侧面$A D D _ { 1 } A _ { 1 }$ 内,若 $B M \perp A _ { \scriptscriptstyle 1 } N$ ，则 $\triangle A B N$ 面积的最小值为（）

A.√5 B.2√5C.5D.25

变式6 $- \downarrow 0$ 图，正方休ABCD$A _ { 1 } B _ { 1 } C _ { 1 } D _ { 1 }$ 的枝长为2，动点 $E , F$ 在棱 $A _ { 1 } B _ { 1 }$ 上，动点 $P , Q$ 分别在校 $\varLambda I D$ ， $\boldsymbol { C D }$ 上，若 $E F$ $= 1 , A _ { 1 } E = x , D Q = y , D P = z ( x , y , z$ 大于零），则四面休PEFQ的体积（）

$\Lambda$ 、与 $x _ { 1 } y _ { 1 } z$ 都有关B、与 $x$ 有关，与 $y , s ,$ 无关C.与 $\gamma$ 有关，与 $x , z$ 无关

![](images/d80885ef29c9b87d454c8fb20c653d2ec2a28cb795eaeda1b815a6ce70d15c0e.jpg)

例8如图.在正方休 $\lambda n c { \boldsymbol { \imath } } { \boldsymbol { \jmath } } - \lambda _ { 1 } { \boldsymbol { \imath } } | t _ { 1 } c _ { 1 } { \boldsymbol { \jmath } } _ { 1 }$ 中.点口为线图BD的中点.世点 $\boldsymbol { \mathsf { \Pi } } ^ { p }$ 在线险 $c C _ { 1 }$ 上.直OP与平面 $\varLambda _ { 1 } B D$ 所成的角为 $\pmb { \alpha }$ m的取值范围是（三[2

![](images/60a6c7bc554fbcc8c7799a07e25b7f0e523298c2fc4ef9cdd920d740f4611856.jpg)

# 第5讲立体几何中的动点轨迹问题·

# 【核心知识聚焦】

# 一、空间点的轨逊的几种常见情形

1.平面内到空间定点的阳离等于定长，可纳合球面得轨迹；2.与定点的连线与菜平而平行，利用平行平而得点的轨迹；3.与定点的连线与某直线垂直，利用垂直平面得点的轨迹；4.与空间定点连线 $^ { 1 } \cdot$ 某直线成等角，可结合圆维侧而得轨迹.

# 二、空间点的轨迹间题常用解题策略

1.定义法；借助圆锥曲线的定义判断.2.坐标法：建立合适的坐标系，用方程来表  
示所求点的轨迹，借助方程来判断轨迹形状.3.交轨法，运动的同时在两个空间几何  
体上，如平而与圆锥、圆柱、球相交，球与球相

交，等等.

4.平而化，把空间几何关系转化到同一平面内，进而探究平面内的轨迹回题，使间题更易解决.空间间题平而化也是解决立休几何题目的一般性思路.

# 三、与平行、垂直有关的轨迹问题

1.与平行有关的轨迹问应的解题策略（1)线而平行转化为面面平行得轨迹；(2)平行时可利用法向量垂直关系求轨迹2.与垂直有关的轨迹问题的解题策略（1)可利用线线垂直、线面垂直，转化为面面垂直，得交线求轨迹；(2)利用空间坐标运算求轨迹；(3)利用垂直关系转化为平行关系求轨迹

# 四、与距离、角度有关的轨迹问题

1.与距离有关的轨迹回题的解题策略(1)距离，可转化为在一个平面内的距离美系，情助干圆维曲线宠义成者球和圆的定义知以水解机迹1

(2）利川空间半标计算求机迹

2.与角度有关的机迹回题的解题策略

（1）直线与平面成定角，可能尾圆锥侧m；(2）直线与宠直线成等伯，可能是圆销酬面：(3)利川空间坐标系计算求轨迹，

3.与翻折有关的机迹回题的解题策略

（1）翻折过程中寻找不的垂直关系求  
机血：(2)翻折过程中寻找不变的长度关系求  
机迹：（3)可以利用空间坐标运算求轨迹.

# ? 【进阶提升】

# 触型一线线、线面恒定平行求轨迹

例！已知梭长为丨的正方体AIOD-$A _ { 1 } h _ { 1 } \mathcal { O } _ { 1 } , D _ { 1 } , M$ 尼 $B { \cal B } _ { \| }$ 的中点，动点 $\boldsymbol { \mathsf { \Pi } } ^ { p }$ 在正方休内部或表面上，MP//平而 $\lambda I I | | I _ { \ell }$ ，则动从 $P$ 的轨迹所形成区域的而积是（）

B.2 C.ID.2

变式 如图，在三伎壮ADO-AU，$P , A B = 4 , A C = 3 , B C = 5 , A _ { 1 } = 6 , D N$ $\boldsymbol { C } \boldsymbol { C } _ { \parallel }$ 的中点，E为HH上-点，Hn=3HD$\angle A _ { 1 } A C = 6 0 ^ { \circ } , N$ 为侧面 $M _ { 1 } \mathcal { O } _ { 1 } \mathcal { O }$ 上一起，且BM//平面ADE，则点M的航迹的长度为

![](images/60a998008dabd29edc9d343a0e8ee9303f20a1722e01f129859e22f64b74fe12.jpg)

A1 B.√2 C.√3 1.2

# 砸型二线线、线面恒定垂直求

例2如图，在自钰 $ 4 I I C - A _ { j }  /  I _ { j }  /  ,$ 中，侧楼长为2,AC=I0=1. $\angle A C B = 9 0 ^ { \circ }$ $\boldsymbol { \mathit { \Pi } } _ { I } \boldsymbol { \mathit { \Pi } } _ { \mathbf { \Pi } }$ $A _ { 1 } I I _ { 1 }$ 的中心。 $\boldsymbol { { \mathscr { F } } }$ 昆侧面 $\mathcal { M } , \mathcal { I } , \mathcal { I } ($ 會边界）上的动以，要使 $A I I , \perp$ 平四 $C _ { 1 } / / { F }$ 则线段 $\varOmega _ { 1 } / ^ { \prime }$ 的长的最大值为（

![](images/411027a7e9491e826a7a94fafccfb112428ec2f660a522cd60a77dd58e6b14bf.jpg)

$\Lambda . \frac { \sqrt { 5 } } { 2 }$ $1 1 , { \sqrt { 2 } }$ 0. 3 D.√5

式2 $\langle \ j _ { \parallel } ,$ 图，在等肠稀形AHOD中。AH//OD,AH =2,AD =BC =1 $N / { I } > \vert / { I } \vert$ 着A0把△AO1折起，点 $\ j \ j$ 到达点 $I I _ { 1 }$ 处，$\smash { \upsilon _ { p } }$ 在平面ABO上的射形恰好落在边A日上当边长OD亚化时，点 $I I _ { 1 }$ 的仇迹长度为（

![](images/ba05620681e25295622fe74659fdbcbaf53355086b2589cd060e65c66130eb9f.jpg)

# 题型三角度恒定求轨迹

例3如图，斜线段A0与平而 $\pmb { \alpha }$ 所成的角为 $6 0 ^ { o }$ 为斜尼，平Ⅲ $\pmb { \alpha }$ 上的动从 $\prime$ 满足$\angle P A I I = 3 0 ^ { \circ }$ ，则从 $\boldsymbol { { \mathit { \Sigma } } } ^ { p }$ 的轨迹尼（ ）

![](images/39eab3942169488d5e4730152063997d3e4a2c7bde3ae0990bf21871418ca736.jpg)

A.直线 B.抛物线C.椭圆 D.双曲线的一支

变式3 如图，在直三棱柱ABC$A _ { 1 } B _ { 1 } C _ { 1 }$ 中，已知 $\triangle A B C$ 是边长为1的等边三角形， ${ \boldsymbol { A } } { \boldsymbol { A } } _ { 1 } = 2$ ，点 $E , F$ 分别在侧面 $A A _ { 1 } B _ { 1 } B$ 和侧面 $A A _ { 1 } C _ { 1 } C$ 内运动（含边界），且满足直线 $A A _ { 1 }$ 与平面AEF所成的角为 $3 0 ^ { \circ }$ ，点$A _ { 1 }$ 在平面 $A E F$ 上的射影 $H$ 在 $\triangle A E F$ 内（含边界）.令直线 $B H$ 与平面 $A B C$ 所成的角为 $\theta$ ，则 $\tan \theta$ 的最大值为（

![](images/65db9618b81f3236e522c7afb948f3cd37bc74136e69d947a43b247cda395ab9.jpg)

A.3（2+3) B.3 C.3 D.3(2-√3)

# 题型四 定长求轨迹

例4已知正方体 $A B C D - A _ { 1 } B _ { 1 } C _ { 1 } D _ { 1 }$ 的棱

长为 $4 , P , Q$ 分别在直线AC, $B _ { 1 } D _ { 1 }$ 上运动，且满足 $P Q = 8$ ，则 $P Q$ 的中点 $E$ 的轨迹为（）

A.直线 B.椭圆 C.抛物线 D.圆

# 题型五翻折中的轨迹问题

例5在矩形ABCD中， $A B = 2 , A D = { \sqrt { 3 } } , E$ 为 $A B$ 的中点，将 $\triangle A D E$ 沿 $D E$ 折起至 $\triangle A ^ { \prime } D E$ ，记二面角 $A ^ { \prime } - D E - C$ 的平面角为 $\theta$ ，当 $\theta$ 在[0,$\pi ]$ 内变化时，点 $A ^ { \prime }$ 的轨迹长度为

![](images/4892e27292a3d9b689d8da1af957aab20c523903c5298b8dd6ce02e46bc4bd57.jpg)

题型六 阿波罗尼斯圆与球(线段定比)

例6在棱长为6的正方体ABCD-$A _ { 1 } B _ { 1 } C _ { 1 } D _ { 1 }$ 中，点 $M$ 是线段BC的中点，点 $P$ 在正方形 $D C C _ { 1 } D _ { 1 }$ （包括边界）上运动，且满足$\angle A P D = \angle M P C$ ，则点 $P$ 的轨迹周长为

# 第6讲 立体几何中的向量方法··

# 【核心知识聚焦】

# 一、向量法判定直线、平面间的位置关系

1.直线与直线的位置关系：设不重合的两条直线 $a , b$ 的方向向量分别为 $^ { a , b }$

若 $\pmb { a } / / \pmb { b }$ ,即 $\pmb { a } = \lambda \pmb { b }$ ，,则 $a / / b$ ；若 $\mathbf { \Delta } \mathbf { \ } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf { \Sigma } \mathbf \Sigma { \Sigma } \mathbf \mathbf { \Sigma } \mathbf { \Sigma } \mathbf \Sigma \Sigma \mathbf { \Sigma } \mathbf \Sigma \Sigma \mathbf { \Sigma } \mathbf \Sigma \Sigma \Sigma \mathbf { \Sigma } \mathbf \Sigma \Sigma \Sigma \Sigma \mathbf { \Sigma } \Sigma \mathbf \Sigma \Sigma \Sigma \Sigma \mathbf \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \mathbf \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \mathbf \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \Sigma \ S \Sigma \Sigma \Sigma \Sigma \Sigma \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S \ S$ ,即

${ \pmb a } \cdot { \pmb b } = 0$ ,则 $\mathbf { \Delta } a \perp b$

2.直线与平面的位置关系：直线 $l$ 的方向向量为 $\pmb { a }$ ，平面 $\pmb { \alpha }$ 的法向量为 $\pmb { n }$

若 ${ \pmb a } / / n$ ，,即 ${ \pmb a } = \lambda { \pmb n }$ ，则 $l \perp \alpha$ ；若 ${ \pmb { a } } \perp { \pmb { n } }$ ,即 $\pmb { a } \cdot \pmb { n } = 0$ ，则 $\pmb { a } / / \alpha$

![](images/38882f61e70a6e6f16614f7e6c619730af9114941175218dedcfdfd0ca2e820f.jpg)

3.平面与平面的位置关系：平面 $\alpha$ 的法向量为 ${ \pmb n } _ { 1 }$ ，平面 $\beta$ 的法向量为 ${ \pmb n } _ { 2 }$

若 ${ \pmb n } _ { 1 } / / n _ { 2 }$ ,即 ${ \pmb n } _ { 1 } = \lambda { \pmb n } _ { 2 }$ ,则 $\alpha / / \beta$ ；若 ${ \pmb n } _ { 1 } \perp$ ${ \pmb n } _ { 2 }$ ,即 $\pmb { n } _ { 1 } \cdot \pmb { n } _ { 2 } = 0$ ，则 $\alpha \perp \beta$

![](images/8102c2ad856049eaabeb0003d394f1ca602b2056f14a318c62aabd196d95b414.jpg)

# 二、向量法求空间角与空间距离

1.异面直线所成的角

设向量 $^ { a , b }$ 分别为异面直线 $l _ { 1 } , l _ { 2 }$ 的方向向量， $\theta$ 为异面直线所成角的大小，

则cos $\theta = \mid \cos \langle \boldsymbol { a } , \boldsymbol { b } \rangle \mid \mathbf { \theta } = \frac { \mid \boldsymbol { a } \cdot \boldsymbol { b } \mid } { \mid \boldsymbol { a } \mid \mid \boldsymbol { b } \mid }$

2.线面角

设 $l$ 为平面 $\alpha$ 的斜线， $\mathbfcal { a }$ 为 $l$ 的方向向量， $\pmb { n }$ 为 $\pmb { \alpha }$ 的法向量， $\theta$ 为 $l$ 与 $\pmb { \alpha }$ 所成角的大小，

则sin $\theta = \mid \cos \langle \boldsymbol { a } , \boldsymbol { n } \rangle \mid \mathbf { \theta } = \frac { \mid \boldsymbol { a } \cdot \boldsymbol { n } \mid } { \mid \boldsymbol { a } \mid \mid \boldsymbol { n } \mid } .$ （24号

3.二面角

设 ${ \pmb n } _ { 1 } , { \pmb n } _ { 2 }$ 分别为二面角的两个平面 $\alpha , \beta$ 的法向量,二面角的大小为 $\theta$ ,则 $\boldsymbol { \theta } = \left. \pmb { n } _ { 1 } , \pmb { n } _ { 2 } \right.$ 或 $\pi - \left. \pmb { n } _ { 1 } , \pmb { n } _ { 2 } \right.$ （需要根据具体情况判断相等或互补),其中 $\mid \cos \theta \mid = \frac { \mid \pmb { n } _ { 1 } \cdot \pmb { n } _ { 2 } \mid } { \mid \pmb { n } _ { 1 } \mid \mid \pmb { n } _ { 2 } \mid } .$

# 4.异面直线间的距离

两条异面直线间的距离也不必寻找公垂线段,只需利用向量的正射影性质直接计算即可.

如图,设两条异面直线 $a , b$ 的公垂线的方向向量为 $\pmb { n }$ ，这时分别在 $^ { a , b }$ 上任取 $^ { A , B }$ 两点，则向量 $\overrightarrow { A B }$ 在 $\pmb { n }$ 上的正射影长就是两条异面直线 $^ { a , b }$ 的距离.则 $d = \left| \overrightarrow { A B } \cdot \frac { \pmb { n } } { | \pmb { n } | } \right| = \frac { | \overrightarrow { A B } \cdot \pmb { n } | } { | \pmb { n } | }$ 即两异面直线间的距离,等于两异面直线上分别任取两点的向量和公垂线方向向量的数量积的绝对值与公垂线的方向向量的模的比值.

![](images/835e0e318e573dc7775c525c994696401c23880f263135365f2207e82e81268d.jpg)

5.点到平面的距离

A为平面 $\alpha$ 外一点（如图）， $\pmb { n }$ 为平面 $\alpha$ 的法向量,过点 $A$ 作平面 $\alpha$ 的斜线 $A B$ ,斜足是点$B$ ，作垂线 $A H$ ，垂足是点 $H _ { \cdot }$

![](images/a8e57dfe08b9f2ce50245fc643a8e682c49475288f1a733d89c66561021e2d2d.jpg)

在 $\mathrm { R t } \triangle A B H$ 中， $\mid \overrightarrow { A H } \mid ~ = ~ \mid \overrightarrow { A B } \mid ~ \cdot ~ \sin ~ \theta =$ $\mid \overrightarrow { A B } \mid \ \cdot \ \mid \cos \langle \overrightarrow { A B } , { \pmb n } \rangle \mid \ = \mid \overrightarrow { A B } \mid \frac { \mid \overrightarrow { A B } \cdot { \pmb n } \mid } { \mid \overrightarrow { A B } \mid \ \mid { \pmb n } \mid } \ =$ $\frac { | { \overrightarrow { A B } } \cdot \pmb { n } | } { | \pmb { n } | }$ 点 $A$ 到平面 $\alpha$ 的距离是 $d \ =$ ${ \frac { \mid { \overrightarrow { A B } } \cdot n \mid } { \mid n \mid } } .$

# 三、线上的动点问题

在立体几何大题中,点在直线上运动是常见的题型.

举例如下：设 $A ( 1 , 0 , 1 )$ ,B(0,2,2)， $o$ 是  
原点，点 $P$ 在线段 $A B$ 上,具体位置待定,那么  
可以设 $\overrightarrow { A P } = \lambda \overrightarrow { A B } , P ( x , y , z )$ ，则 $\overrightarrow { A P } = \left( x - 1 , y \right)$ ，  
$z - 1 \big )$ ）， $\overrightarrow { A B } = \left( \begin{array} { r r } { - 1 , 2 , 1 } \end{array} \right)$ ,由向量关系式 $\overrightarrow { A P } = \lambda$ $\left\{ \begin{array} { l l } { x - 1 = - \lambda } \\ { \gamma = 2 \lambda , } \\ { z - 1 = \lambda , } \end{array} \right.$ ，  
$\overrightarrow { A B }$ 可以建立方程组 从而得出$\boldsymbol { \mathscr { x } } = 1 - \boldsymbol { \lambda }$ ，  
$\gamma = 2 \lambda$ ，即 $P ( 1 - \lambda , 2 \lambda , 1 + \lambda )$ ,这样点 $P$ 的$\lfloor z = 1 + \lambda$ ，  
坐标就只有一个未知数 $\lambda$ 了，后续运算会较为方便.

事实上，还可以优化这个求点 $P$ 坐标的过程,我们知道 $\overrightarrow { O P }$ 的坐标和点 $P$ 的坐标是一样的,故可以用下面的式子一步到位直接求出 $\overrightarrow { O P }$ 的坐标,从而得出点P的坐标:OP=OA+AP=$\overrightarrow { O A } + \lambda \overrightarrow { A B } = \left( 1 , 0 , 1 \right) + \lambda \left( \ - 1 , 2 , 1 \right) = \left( 1 - \lambda , \right.$ （$2 \lambda , 1 + \lambda )$ ：

另外,如果动点本来就在一些特殊的直线上,如 $_ x$ 轴上,则可以直接设其坐标为 $^ { ( a , 0 , 0 ) }$ ：

![](images/bdaf4557febc32aa058b3ecd6dda5242a3641882beaa4bbbb2be555f2364a1cb.jpg)

# 四、立体几何中的向量方法

用向量法可以证点共线、线共点、线（或点)共面、两直线(或线与面、面与面)垂直的问题,也可以求空间角和距离.因此,凡涉及上述类型的问题，都可以考虑利用向量法求解，且其解法一般都比较简单.

用向量法解题的途径有两种：一种是坐标法，即通过建立空间直角坐标系，确定出一些点的坐标，进而求出向量的坐标，再进行坐标运算;另一种是基底法，即先选择基向量（除要求不共面外,还要能够便于表示所求的目标向量,并优先选择夹角已知的向量作为基底，如常选择几何体上共点而不共面的三条棱所在的向量为基底），然后将有关向量用基底向量表示,并进行向量运算.

# 【进阶提升】

# 题型一 空间向量的基本运算

例1已知在平行六面体 $A B C D - A _ { 1 } B _ { 1 } C _ { 1 }$

$D _ { 1 }$ 中，同一顶点为端点的三条棱长都等于1,且彼此的夹角都是 $6 0 ^ { \circ }$ ，则此平行六面体的对角线 $A C _ { 1 }$ 的长为（ ）

A.6 B.6 C.3 D $\sqrt { 3 }$

变式1 定义：两条异面直线之间的距离是指其中一条直线上任意一点到另一条直线距离的最小值.在棱长为1的正方体$A B C D - A _ { 1 } B _ { 1 } C _ { 1 } D _ { 1 }$ 中，直线AC与 $B C _ { 1 }$ 之间的距离是（

A $\frac { \sqrt { 2 } } { 2 }$ B $\frac { \sqrt { 3 } } { 3 }$ $\frac 1 2$ $\frac 1 3$

例2 (多选题)在正三棱柱 $A B C - A _ { 1 } B _ { 1 } C _ { 1 }$ 中， $A B = A A _ { 1 } = 1$ ，点 $P$ 满足 $\overrightarrow { B P } = \lambda \overrightarrow { B C } + \mu \overrightarrow { B B _ { 1 } }$ ，其中 $\lambda \in \left[ 0 , 1 \right]$ $\mu \in \left[ 0 , 1 \right]$ ,则（）

A.当 $\lambda = 1$ 时， $\triangle A B _ { 1 } P$ 的周长为定值B.当 $\mu = 1$ 时，三棱锥 $P - A _ { 1 } B C$ 的体积为定值C.当 $\lambda = \frac { 1 } { 2 }$ 时,有且仅有一个点 $P$ ，使得$A _ { 1 } P \bot B P$ D.当 $\mu = \frac { 1 } { 2 }$ 时,有且仅有一个点 $P$ ，使得$A _ { 1 } B \bot$ 平面 $A B _ { 1 } P$

题型二空间向量与空间位置关系、空间角

例3在四棱锥 $P - A B C D$ 中， $P D \perp$ 底面ABCD，CD // AB, $A D = D C = C B = 1$ $A B = 2$ ，$D P = { \sqrt { 3 } }$ ，

![](images/2497dcd14727205699a7c6b815accbd55ebc3cf5dfa4179e0aaf0ff98133dcb2.jpg)

(1)证明： $B D \perp P A$ (2)求 $P D$ 与平面 $P A B$ 所成角的正弦值.

1'00

![](images/c519f0d9bbd63833cd731c0ec48f67e38718004f707cd0d4d9ba2a08a1c59288.jpg)

0ak:RI(D 00-09-8m0 a01

![](images/660163e634f883628b410388e3b034089dd37ab1623e095fb7077273558cae1e.jpg)

![](images/947922dfe188c6a7ab23b0f77088040367b4735ef4b6f7f97189c9bcf52b3b2f.jpg)

变式2 如图，在三棱柱 $A B C - A _ { 1 } B _ { 1 } C _ { 1 }$ 中，侧面 $B C C _ { 1 } B _ { 1 }$ 为正方形，平面 $B C C _ { 1 } B _ { 1 } \bot$ 平面 $A B B _ { 1 } A _ { 1 }$ ， $A B = B C = 2 , M , N$ 分别为$A _ { 1 } B _ { 1 }$ ${ \mathbf { } } A C$ 的中点

（1)求证:MN//平面 $B C C , B _ { 1 }$ ！

(2)再从条件 $\textcircled{1}$ 、条件 $\textcircled{2}$ 这两个条件中选择一个作为已知，求直线 $A B$ 与平面BMN所成角的正弦值.

条件 $\textcircled{1}$ $\textcircled { 1 } : A B \perp M N$ ；条件 $\textcircled{2}$ $B M = M N .$

![](images/****************************************212ac69794df53c647eb339a.jpg)

![](images/07924360509be880d704e14a4377bddbb231454ed54c83793f77d74af6c47ab9.jpg)

例4如图,AC，BD为圆柱 $o o ^ { \prime }$ 底面 $\odot O$ 的两条直径, $P A$ 为圆柱 $O O ^ { \prime }$ 的一条母线，且$A P = A C$

![](images/232292dc39edfa664dd8b1a3e8b2be8c2a57ebbabffe09a3009fe795997ef73b.jpg)

(1)证明： $A B \perp P D$

(2)若 $\angle A O B = \frac { \pi } { 3 }$ ,求二面角 $B - P C - D$ 的 正弦值.

# 题型三线上的动点问题

例5已知在直三棱柱 $A B C - A _ { 1 } B _ { 1 } C _ { 1 }$ 中，侧面 $A A _ { 1 } B _ { 1 } B$ 为正方形， $A B = B C = 2 , E , F$ 分别为 $A C$ 和 $C C _ { 1 }$ 的中点， $D$ 为棱 $A _ { 1 } B _ { 1 }$ 上的点,$B F \bot A _ { 1 } B _ { 1 }$ ：

![](images/245b0e57fe39bf1b2d7e04a2444d059757df9a73ee9b7671f453042339974c24.jpg)

(1)证明： $B F \bot D E$ ： (2)当 $B _ { 1 } D$ 为何值时,平面 $B B _ { 1 } C _ { 1 } C$ 与平 面 $D E F$ 所成的二面角的正弦值最小?

变式3 如图所示，在四棱锥 $P - A B C D$ 中， $A B / / C D$ ， $A B \bot B C$ ， $A B = B C = 1$ ， $\triangle P C D$ 是边长为2的正三角形，且平面 $P C D \perp$ 平面ABCD, $E$ 为棱 $P C$ 上一点.

（1）设平面 $P A B \cap$ 平面 $P C D = l$ ，证明：l//平面ABCD；

（2）若二面 $\Cap$ $E - A D - C$ 的大小为60°，水 水CE的值A

# 【核心知识聚焦】

# 一、折叠与展开问题的几何处理方式

平面图形折叠成空间图形问题是高考立体几何中的常见题型，对空间想象能力要求较高，解决这类问题的突破口有两点：1.分析折叠前后未发生变化的几何关系，得出折叠后空间图形的几何特征，将问题明朗化；2.在折叠前后的图形中，抓住与折痕线垂直的直线，将空间的计算问题转换到平面上来进行.

# 二、折叠与展开问题的建系处理方式

翻折型几何体，寻找翻折前和翻折后的“变与不变"的点、线、面关系：1.翻折前与翻折后在同一平面内的点、线，数量关系不变；2.翻折后，一般情况下是存在垂直的平而，可以利用垂面法建系计算；3.翻折后，可以构造三棱锥，利用三棱锥斜面建系法来建系计算.

# 【进阶提升】

# 题型一选择题、填空题中的折叠问题：翻折中的"变"与"不变"

例1在平行四边形ABCD中 $\angle 1 = 4 5 ^ { \circ } , A B$ $\ E = \sqrt { 2 } A D = 2$ ，现将平行四边形ABCD 沿对角线

BD折起,当异面直线AD和 $_ { B C }$ 所成的角为$6 0 ^ { \circ }$ 时， $\boldsymbol { A } \boldsymbol { C }$ 的长为

![](images/428c767063e55e9b0e543cdb4090bf464a2c86ff69cefaecea09353fdec91cfd.jpg)

变式1 如图所示，已知在平面四边形ABCD中， $A B = B C = 3 , C D = 1 , A D = \sqrt { 5 }$ ，$\angle A D C = 9 0 ^ { \circ }$ ，沿直线 $\boldsymbol { A } \boldsymbol { C }$ 将 $\triangle A C D$ 翻折成$\triangle A C D ^ { \prime }$ ，连接 $B D ^ { \prime }$ ，则直线 $A C$ 与 $B D ^ { \prime }$ 所成角的余弦的最大值是

![](images/136239b18561c74c6888ec06b6eab4162739d13423cdefaf5e5d1579b411c30f.jpg)

例2如图所示，在长方形ABCD中， $A B =$ $2 , B C = 1 , E$ 为 $\it C D$ 的中点， $F$ 为线段 $E C$ 上（端点除外)一动点，现将 $\triangle A F D$ 沿 $\varLambda F$ 折起,使平面 $A B D \perp$ 平面ABC,在平面ABD内过点 $D$ 作$D K \bot A B$ ， $\boldsymbol { K }$ 为垂足，设 $\varLambda K = \iota$ ，则 $\iota$ 的取值范围是

![](images/a2943a7639bace3955bc7a6dab5cc2085852820889f57d5bcf33100acd2ec060.jpg)

变式2如图所示，圆形纸片的圆心为$O$ ，半径为 $5 ~ \mathrm { \ c m }$ ，该纸片上的等边三角形ABC的中心为 $O , D , E , F$ 为圆 $o$ 上的点，△DBC,△ECA，△FAB分别是以 $B C , C A$ $A B$ 为底边的等腰三角形，沿虚线剪开后，分别以BC，CA、AB为折痕折起 $\triangle D B C$ $\triangle E C A , \triangle F A B$ ，使得 $D , E , F$ 三点重合，得到三梭锥 $P - A B C$ 当 $\triangle A B C$ 的边长变化时，三棱锥 $P - A B C$ 体积（单位： $\mathrm { c m } ^ { 3 }$ ）的最大值为

![](images/d5fcbfc668639d067c8033bf1af5b15a10e6f6fd2b9c48663396e91f4c787e30.jpg)

题型二解答题中的折叠问题：翻折型建系

例3如图,菱形ABCD的对角线 $A C$ 与$B D$ 交于点 $O , A B = 5 , A C = 6$ ，点 $E , F$ 分别在$A D$ ，CD上， $\ A E = C F = \frac { 5 } { 4 }$ $E F$ 交 $B D$ 于点 $H .$ 将$\triangle D E F$ 沿 $E F$ 折到 $\triangle D ^ { \prime } E F$ 位置， $O D ^ { \prime } = \sqrt { 1 0 }$

(1）证明： $D ^ { \prime } H \bot$ 平面ABCD；

(2)求二面角 $B - D ^ { \prime } A - C$ 的平面角的余 弦值

![](images/1ba4e0304205a944516b4791f8aee261a5226f9c06aba0f1a9e746a8f2cabe5c.jpg)

变式3 如图 $\textcircled{1}$ ，在平行四边形ABCM中， $A B = 2 B C = 2 \sqrt { 3 }$ ， $\angle M A D = 6 0 ^ { \circ } , D$ 为CM的中点， $\overrightarrow { A F } = \frac { 1 } { 2 } \overrightarrow { F C }$ ， $\overrightarrow { A H } = \overrightarrow { H D }$ ，沿AD将△MAD翻折到 $\triangle P \ / { \it { M D } }$ 的位置，如图 $\textcircled{2}$ $P F \bot A C$

![](images/****************************************82cbb2d1a1db17bb90469f8c.jpg)  
图①

![](images/1033aef33963e2aff91e76e5a72cb02b4421bed9086ad4102e4cbffcd6d79f1c.jpg)  
图②

（1）证明： $H F / /$ 平面PBD；  
（2）求平面PBC和平面PCD的夹角.

![](images/40c51626d925f41eb64323ac814e11560d0638e04df5c4c4d035cbfe587423d2.jpg)

例4如图 $\textcircled{1}$ 所示，在四边形ABCD中， $B C$ $\perp C D$ ， $\pmb { { \cal E } }$ 为 $_ { B C }$ 上一点， $A E = B E = A D = 2 C D =$ $2 , C E = \sqrt { 3 }$ ,将四边形AECD沿 $A E$ 折起，使得$B C = \sqrt { 3 }$ ，得到如图 $\textcircled{2}$ 所示的四棱锥.

![](images/****************************************3b1f9ec64dfcd5fb20c8b92d.jpg)  
图 $\textcircled{1}$

![](images/****************************************64d67bbde852ab9d76f4136a.jpg)  
图②

(1)若平面 $B C D \cap$ 平面 $A B E = l$ ，证明：$C D / / l ;$

(2）点 $\boldsymbol { F }$ 是棱 $B E$ 上一动点，且直线 $B D$ 与 平面 $A D F$ 所成角的正弦值为 $\frac { \sqrt { 2 2 } } { 1 1 }$ 求 $\cdot \frac { E F } { E B } .$

变式4 如图， $E , F$ 分别是矩形ABCD的边 $A D$ ， $B C$ 上的点，沿 $E F$ 将矩形ABCD翻折成多面体 $A _ { 1 } B _ { 1 } - C D E F$ ， $A D = \sqrt { 3 } A B$ ，$A E = C F = { \frac { 1 } { 3 } } B C .$ CALL

（1）证明： $E F \perp B _ { \parallel } D$

(2)当 $B _ { 1 } D = C D$ 时，求二面角 $A _ { 1 }$ $B _ { 1 } D - C$ 的余弦值.

![](images/6c919af1d5292e51141292dce2bda27261bd7162702322b4292c6b63ba63d9b3.jpg)

不

#

#

# ·第8讲复杂几何体的建系与设点方法

BCC

# 【核心知识聚焦】

# 一、坐标法解决立体几何问题的核心思想是找“墙角”，主要分为以下几种类型：

1.有三条两两垂直的直线(墙角),以这三

条直线为坐标轴；

2.没有明显的"墙角"时,需通过添加辅助线“找墙角"或"造墙角”；3.实在没有时可借助直角坐标系,另一条坐标轴“悬空”.

二、坐标瓷的里雅点星求康的樂桥和利用  
实量公式道算、在求病坐标的位程中，有以下  
几触直：1作标鹅（或心倾平）的W吧，自接找1直接市、纯等量美系（线园行度、言

# 三不秋物几同体的建系型展

多見有需需、所山希简动系、水布于$\Re \Re$ 把”空中坐标”、$2$ 有统之、mD需型建系.3.漫有希线和最而、丽川以通过选择合的几个双、”切翻洲”三被、移化为斜而型三极雅来建系设双，

通常来讲、在历年的高考题中、立休几何小题以综合法为主、立休几何大题以坐标法为主.在近儿不的高考中，立休几何大题慢慢从工整的简单几何休(长方体，直三棱柱)向复杂儿何体(斜棱柱、棱锥，组合休等)转变，有向综合法倾斜的趋势，但仍然可以用建系的方法做，但对技巧性要求更高，本专题就是针对这类题型进行探究

# 【进阶提升】

# 题型一利用面面垂直关系建系

例1如图所示，在多面体ABCDEP中，底ACD为血角秘形，A/ $\mathcal { \# K }$ ，AB⊥BC,侧面ABP为形，平面AB $\mathscr { L }$ 平面ABCD，M为被桃的中点

$\Lambda ^ { \prime }$ 为的中点求EMN平HAN（2）薛Ag=AC=÷AD、∠EH=O"求平m与平央价的余炫信

道

# 脑型二台体建系

例2如图，在三棱台 $\lambda l l - \lambda , l l , C _ { \iota }$ 中$\lambda _ { 1 } C _ { 1 } = 4 \ : . A C = 6 \ : . D$ 为线段A0上近点 $\varDelta$ 的三分血

![](images/a535c2a8591029218bf7d06cf647980201eb63192a9d121a25d1b403990eaf3a.jpg)

(1)线段BC上是否存在点 $ { \boldsymbol { { \mathit { L } } } }$ 便得A,B//平面 $C _ { 1 } D { \cal E }$ ，若不存在，请说明理出；若存在，请求山 $\mathit { \Pi } _ { \bar { D } \bar { C } } ^ { \mu \vec { D } }$ 的值；

(2）若 $\lambda , A = A H = 4 , \angle A , A C = \angle B A C = \frac { \pi } { 3 }$ 、 $A _ { 1 }$ 到平面ABC的离为3，且点A,在版离 ABC的射影落在△ABC内部，水自线 ${  { \boldsymbol { \mathit { \mathit { \mathit { \mathit { \mathit { \Pi } } } } } } } } _ { 1 } ,  { \boldsymbol { \mathit { \Pi } } } $ 与平 面 $A C C , A ,$ 所成角的正弦值

# 题型三凌空建系

例3如图、已知四棱锥 $ { \boldsymbol { P } } -  { \boldsymbol { A } }  { \boldsymbol { B } }  { \boldsymbol { C } }  { \boldsymbol { D } }$ △PAD是以AD为斜边的等腰直角三角形、 $B C / / A D$ ，$C D \perp A D$ $P C = A D = 2 D C = 2 C B$ $\boldsymbol { \varepsilon }$ 为 $P D$ 的中点

（1）证明： $G E / /$ 平面 ${ \cal P A B }$

（2）求直线 $\boldsymbol { G } \boldsymbol { E }$ 与平面PBC所成角的正弦 值

![](images/8f831640418d379d177f03638e04155fd0588f3ad928e3843eb59f4360488526.jpg)

题型四“坐标轴倒置”建系

例4如图，在斜三棱柱 $\lambda B C - A _ { 1 } B _ { 1 } C _ { 1 }$ 中，$\angle B A C = 9 0 ^ { \circ }$ $A B = 2 A C$ ， $B _ { 1 } C \bot A _ { 1 } C _ { 1 }$ ，且 $\triangle A _ { \ u _ { \mathrm { i } } } B _ { \ u _ { \mathrm { i } } } C$ 为等边三角形.

（1)求证：平面 $A _ { 1 } B _ { 1 } C \bot$ 平面 $\ A B C$

(2)求直线 $B B _ { 1 }$ 与平面ABC所成角的正 弦值.

![](images/203ab8bcaae4b6c98278289261b7164262981fae249146366731260736bc523b.jpg)

# 题型五投影设点

例5如图，在三棱锥 $P - A B C$ 中，底面ABC是边长为2的等边三角形， $P A = P C = \sqrt { 5 }$ ，点 $F$ 在线段 $B C$ 上，且 $F C = 3 B F$ $D$ 为 $A C$ 的中点， $\boldsymbol { \mathbf { \mathit { E } } }$ 为 $P D$ 的中点

(1)求证： $E F / /$ 平面 $P A B$ ：

(2)若二面角 $P - A C - B$ 的平面角的大小 为 $\lceil \frac { 2 \pi } { 3 }$ ,求直线 $D F$ 与平面 $P A C$ 所成角的正弦 值

![](images/8ac9f2417779282b7afc06d75a9170e8006c6aae8072f6eda72a1ec741d00c1b.jpg)

![](images/f6aede93cc3161c1ffdfeb68f3a41afeec3de079bd9deafcdd9418a999f27c84.jpg)

# 题型六“暴力"设点

例6如图,在四棱锥 $P - A B C D$ 中， $M , N$ 分别是 $A B$ ， $A P$ 的中点， $A B \perp B C , ~ M D \perp P C$ ，$M D / / B C$ ， $B C = 1$ ， $A B = 2$ $P B = 3$ ， $C D = \sqrt { 2 }$ ，$P D = { \sqrt { 6 } }$

(1)证明： $P C / /$ 平面MND；

(2)求直线 $P A$ 与平面 $P B C$ 所成角的正弦 值

![](images/24ee0e11ef16b422389acebd43076ec580972067f0f91761e4660b49bb0fcbe5.jpg)

![](images/1a510f152cfae94e8ddfac0734fde2f7ce0ef6ee49076cdf1d7aa9262c05b7f9.jpg)

# 【核心知识聚焦】

# 一、平行与垂直的探索型问题的几何方法

先把“未知点”定性为“已知点”，然后通过其他条件推导出相关的平行或者垂直关系，再借助平行得比例线段,或者垂直得到对应的垂直关系.

# 二、空间角与空间距离的几何方法

1.线与线的夹角的求法

通过平移将异面直线 $a , b$ 平移到同一平面内,放在同一三角形内解三角形.

# 2.线与面的夹角的求法

(1)利用面面垂直的性质定理,得到线面 垂直，进而确定线面角的垂足，明确斜线在平 面内的射影,即可确定线面角.

(2)在构成线面角的直角三角形中，可利用等面积法或等体积法求解垂线段的长度 $h$ ，从而不必作出线面角,则线面角 $\theta$ 满足sin $\theta =$ ${ \frac { h } { l } } ( l$ 为斜线段长),进而可求得线面角.

(3)建立空间直角坐标系，利用向量法求解，设 $\pmb { a }$ 为直线 $l$ 的方向向量， $\mathbf { \nabla } _ { \mathbf { \mu } } \mathbf { \mathcal { n } }$ 为平面的法向量，则线面角 $\theta$ 的正弦值为 $\mathrm { s i n } \theta = \mid \cos \langle a , \pmb { n } \rangle \mid$

# 3.二面角的求法

(1)定义法

在棱上取点，分别在两个半平面内引两条射线与棱垂直，这两条射线所成的角就是二面角的平面角,如图在二面角 $\alpha - l - \beta$ 的棱上任取一点 $o$ ，以 $o$ 为垂足，分别在半平面 $\alpha$ 和 $\beta$ 内作垂直于棱的射线OA和 $O B$ ，则射线OA和OB所成的角称为二面角的平面角（当然两条垂线的垂足点可以不相同,那求二面角就相当于求两条异面直线的夹角或其补角即可).

![](images/410fb3c6ef981db0ecfe9293aefda3e485a306cfc1a5f21c49cbfdce1db65edc.jpg)

(2)三垂线法

如图,在平面 $\alpha$ 内找一合适的点A,作 $A O$ $\perp \beta$ 于点 $o$ ,过点 $O$ 作 $O B \bot c$ 于点 $B$ ，连接 $A B$ ， 则直线 $c$ 垂直于斜线 $A B$ ,则 $\angle A B O$ 为二面角 $\alpha - c - \beta$ 的平面角.

![](images/258fd89b544f86c5ada7e2c49d8d1f4bb8ddccb25f5ceceb5b349315be4b4fc1.jpg)

(3)射影面积法

凡在二面角的图形中含有可求原图形面积和该图形在另一个半平面上的射影图形面积的都可利用射影面积公式 $\Bigg ( \cos \theta = \frac { S _ { \vartheta \vartheta } } { S _ { \vartheta \vartheta } } =$ S,如图)求出二面角的大小. SABC

![](images/8ad77b6ef49c22a012957e34eb3b5f88064fbb89a55c5f1dca89d126f78649eb.jpg)

(4)补棱法

当构成二面角的两个半平面没有明确交线时，要将两平面的图形补充完整，使之有明确的交线(称为补棱)，然后借助前述的定义法与三垂线法解题.当两个半平面没有明确的交线时，也可直接用射影面积法解题.

# (5)垂面法

由二面角的平面角的定义可知两个半平面的公垂面与棱垂直，因此公垂面与两个半平面的交线所成的角，就是二面角的平面角.

如图,过二面角外一点 $A$ 作 $A B \perp \alpha$ 于点$B$ ，作 $A C \bot \beta$ 于点 $c$ ，平面 $A B C$ 交棱 $^ { a }$ 于点 $o$ ，则 $\angle B O C$ 就是二面角的平面角.此法应用比较少,此处就不一一举例分析了.

![](images/d8b0142d73e2c054359316089f2afb306e6ac002b6451659cc98a7f65f8272df.jpg)

# 4.空间中的距离

求点到面的距离转化为三棱锥的高，用等 体积法求解.

# 【进阶提升】

题型一 平行与垂直的探索问题

例1在三棱锥 $P - A B C$ 中， $A C = B C$ ， $P A$ $\mathbf { \Phi } = P B , D , E$ 分别是棱 $B C , P B$ 的中点.

(1)证明： $A B \bot P C$

(2)线段 $A C$ 上是否存在点 $F$ ，使得 $A E$ //平面PDF？若存在,指出点 $F$ 的位置;若不存在,请说明理由.

![](images/342006c552c0d98bcabe07d58302572e51540c3c21d8764a64b6187d9553d047.jpg)

例2如图，在三棱柱 $A B C - A _ { 1 } B _ { 1 } C _ { 1 }$ 中，$B C _ { 1 } \bot C C _ { 1 } , B C _ { 1 } \bot A _ { 1 } C$ ，且 $E , F$ 分别是 $B C , A _ { 1 } B _ { 1 }$ 的中点.

![](images/e11a2f732702de3e198c26ba69d804ccbd91ff2508def76362fc52882b4827f2.jpg)

(1)求证： $E F / /$ 平面 $A _ { 1 } C _ { 1 } C A$ (2)在线段 $A B$ 上是否存在点 $P$ ，使得$B C _ { 1 } \bot$ 平面EFP?若存在,求出 $| \frac { A P } { A B } \rrangle$ 的值;若不存在,请说明理由.

(3)棱PB上是否存在点F,使得CF//平面PAE？说明理由.

![](images/ba108ae0abb54ed51f1e92fc9c66f819a413a1e954738b8000618ff0ab1da684.jpg)

例3如图,在四棱锥 $P - A B C D$ 中， $P A \perp$ 平面ABCD,底面ABCD为菱形， $E$ 为 $C D$ 的中点

(1)求证： $B D \perp$ 平面 $P A C$ (2)若 $\angle A B C = 6 0 ^ { \circ }$ ,求证：平面 $P A B \perp$ 平面

# 题型二空间角与空间距离

例4如图、在三棱柱 $A B C - A _ { 1 } B _ { 1 } C _ { 1 }$ 中、$A B = B C = 2 , A C = 2 \sqrt { 2 } , \angle B _ { 1 } B C = 6 0 ^ { \circ }$ 、四边形$A B B _ { 1 } A _ { 1 }$ 为正方形， $E , F$ 分别为 $_ { B C }$ 与 $A _ { 1 } C _ { 1 }$ 的中点

![](images/7b96b81a2725accbb7b1cb117242fe94a9dbb4a8f06e1db33b18a82b1f2a64c5.jpg)

（1）求证： $E F / /$ 平面 $A B B _ { 1 } A _ { 1 }$ （2）求直线EF与平面 $A C C _ { 1 } A _ { 1 }$ 所成角的正弦值、

例5在四棱锥 $S - A B C D$ 中，底面 $A B C D$ 是平行四边形， $\angle D B C = 9 0 ^ { \circ } , S C = S D = D C , \mathbb { H }$ 平面 $S C D \perp$ 平面ABCD,点 $E$ 在棱 $S C$ 上，直线SA//平面 BDE.

![](images/27f55a5167299963c445fb7a4272b85b39d48267903fc9a8e0571632e3a59c64.jpg)

(1)求证：点 $E$ 为棱 $S C$ 的中点； (2)设二面角 $S - B D - C$ 的大小为 $\theta , \mathbb { A }$ an $\theta = \sqrt { 6 }$ .求直线 $\it { B E }$ 与平面ABCD所成角的 正切值.

例6如图,在等腰直角三角形ABC 中,$A C = B C = 4 , D$ 是 $A C$ 的中点， $E$ 是 $A B$ 上一点，且 $D E \bot A B$ 将 $\triangle A D E$ 沿着 $D E$ 折起,形成四棱锥 $P - B C D E$ ,其中点 $A$ 对应的点为 $P$

![](images/da9f9b1ab204c48b91162ef6203fbdda079769097cc7520642d5355bc18a49fe.jpg)

(1)在线段 $P B$ 上是否存在一点 $_ { F }$ ，使得CF //平面PDE?若存在，求出 $\frac { P F } { P B }$ 的值，并证明；若不存在，请说明理由；(2）设平面PBE与平面 $P C D$ 的交线为 $\boldsymbol { l }$ ，若二面角 $D - { \boldsymbol { \ell } } - E$ 的大小为 $\frac { \pi } { 3 }$ 求四棱锥 $P -$ BCDE的体积

![](images/bbd61897fcb22034b1b5f706f1f1c0f945769aa57321a7c66a1866dad96c40ed.jpg)

# 【核心知识聚焦】

1.分类加法计数原理与分步乘法计数原理

<html><body><table><tr><td></td><td>一般形式</td><td>区别</td></tr><tr><td>分类加 法计数 原理</td><td>完成一件事有两类不 同方案，在第1类方案 中有m种不同的方法， 在第2类方案中有n 种不同的方法,那么完 成这件事共有N=m+ n种不同的方法</td><td>分类加法计数 原理与分步乘 法计数原理,都 涉及完成一件 事情的不同方 法种数.它们的 区别在于：分类 加法计数原理 与分类有关,各 种方法相互独 立,用其中的任</td></tr><tr><td>分步乘 法计数 原理</td><td>完成一件事需要两个 步骤,做第1步有m种 不同的方法,做第2步 有n种不同的方法,那 么完成这件事共有N= m×n种不同的方法</td><td>何一种方法都 可以完成这件 事;分步乘法计 数原理与分步 有关,各个步骤 相互依存,只有 各个步骤都完 成了，这件事才 算完成</td></tr></table></body></html>

2.排列、组合的定义

<html><body><table><tr><td>排列的定义</td><td rowspan="2">从n个不同元素 中取出m（m≤ n)个元素</td><td>按照一定的顺 序排成一列</td></tr><tr><td>组合的定义</td><td>作为一组</td></tr></table></body></html>

3.排列数、组合数的定义、公式、性质

<html><body><table><tr><td></td><td>排列数</td><td>组合数</td></tr><tr><td>定义</td><td>从n个不同元素中 取出m(m≤n)个元 素的所有不同排列 的个数</td><td>从n个不同元素中 取出m（m≤n）个 元素的所有不同组 合的个数</td></tr><tr><td>公式</td><td>A=n(n-1）(n- 2)…(n-m+1)= n！ (n-m）！</td><td>C = = n(n-1)(n-2) m！ n！ = m！(n-m）！</td></tr><tr><td>性质</td><td>A=n!,0！=1</td><td>= C-”,C C + C-1 = Cn+1,C = 1,C =1</td></tr></table></body></html>

# 4.二项式定理

<html><body><table><tr><td>二项式定理</td><td>(a+b)&quot;=Ca+C&#x27;a&quot;-1b&#x27;+. +Ca&quot;-b+…+C²（n∈ N）</td></tr></table></body></html>

<html><body><table><tr><td>二项展开式 的通项</td><td>Tk+=Ca&quot;-*b，它表示展开式 的第k+1项</td></tr><tr><td>二项式系数</td><td>二项展开式中各项的系数C (k=0，1,2,,n)</td></tr></table></body></html>

5.二项式系数的性质

$( 1 ) \mathsf { C } _ { n } ^ { 0 } = 1 , \mathsf { C } _ { n } ^ { n } = 1 , \mathsf { C } _ { n + 1 } ^ { m } = \mathsf { C } _ { n } ^ { m - 1 } + \mathsf { C } _ { n } ^ { m } , \mathsf { C } _ { n } ^ { m } =$ $\mathrm { C } _ { n } ^ { n - m } \left( 0 \leqslant m \leqslant n \right)$

(2)二项式系数先增后减中间项最大.

因为$\begin{array} { c } { { \mathrm { C } _ { n } ^ { k } = \displaystyle \frac { n ( n - 1 ) \cdots ( n - k + 2 ) \left( n - k + 1 \right) } { ( k - 1 ) ! k } = } } \\ { { { } } } \\ { { { \mathrm { C } _ { n } ^ { k - 1 } \displaystyle \frac { n - k + 1 } { k } , \qquad } } } \\ { { { \mathrm { \normalfont ~ \displaystyle { \mathbb { E } _ { n } ^ { k } } } = \displaystyle \frac { n - k + 1 } { k } , \qquad } } } \end{array}$ 所以，当n-k+1 ${ \frac { n - k + 1 } { k } } > 1$ 即 $k < \frac { n + 1 } { 2 }$ 时 ${ \mathrm { C } } _ { n } ^ { k }$ 随$k$ 的增加而增大.由对称性知,二项式系数的后半部分， $\operatorname { C } _ { n } ^ { k }$ 随 $k$ 的增加而减小.当 $\scriptstyle n$ 是偶数时，中间的一项 $\mathbf { C } _ { n } ^ { \frac { n } { 2 } }$ 取得最大值；当 $\boldsymbol { n }$ 是奇数时,中间的两项 $C _ { n } ^ { \frac { n - 1 } { 2 } }$ 与 $\mathbf { C } _ { n } ^ { \frac { n + 1 } { 2 } }$ 相等,且同时取得最大值.

(3)各二项式系数和： $\mathbb { C } _ { n } ^ { 0 } + \mathbb { C } _ { n } ^ { 1 } + \mathbb { C } _ { n } ^ { 2 } + \cdots +$ ${ \mathrm { C } } _ { n } ^ { n } = 2 ^ { n } , { \mathrm { C } } _ { n } ^ { 0 } + { \mathrm { C } } _ { n } ^ { 2 } + { \mathrm { C } } _ { n } ^ { 4 } + \cdots = { \mathrm { C } } _ { n } ^ { 1 } + { \mathrm { C } } _ { n } ^ { 3 } + { \mathrm { C } } _ { n } ^ { 5 } + \cdots =$ $2 ^ { n - 1 }$ ：

# 【进阶提升】

# 题型一组数问题

例14张卡片的正、反面分别写有数字1,2；1,3；4,5；6,7.将这4张卡片排成一排，可构成不同的四位数的个数为（）

A.288B.336C.368D.412

变式1 小小的火柴棒可以拼成几何图形，也可以拼成数字.如下图所示，我们可以用火柴棒拼出1至9这9个数字比如：“1”需要2根火柴棒，“7”需要3根火柴棒.若用8根火柴棒以适当的方式全部放入右面的表格中（没有放入火柴棒的空位表示数字 $\mathbf { \boldsymbol { \rho } } ^ { \ast }$ )，那么最多可以表示无重复数字的三位数有 个123456789

# 题型二排列组合混合型

例2将六枚棋子 $A , B , C , D , E , F$ 放置在$2 \times 3$ 的棋盘中,并用红、黄、蓝三种颜色的油漆对其进行上色(颜色不必全部选用),要求相邻棋子的颜色不能相同,且棋子 $A , B$ 的颜色必须相同，则一共有（ )种不同的放置与上色方式

A. 11232 B. 10483   
C. 10368 D.5616

变式2 现有红、黄、青、蓝四种颜色，对如图所示的五角星的内部涂色（分割成六个不同部分），要求每个区域涂一种颜色且相邻部分（有公共边的两个区域）的颜色不同，则最多使用三种颜色的不同涂色方法有 种.（用数字作答）

![](images/a87b9980fc4ab69e1a80162782b6e6e882de273becadf77ec5fa0213a5cd5cd8.jpg)

# 题型三站位问题

例3甲、乙、丙、丁、戊5名同学站成一排参加文艺汇演,若甲和乙相邻,丙不站在两端，则不同的排列方式共有（ ）

A.12种 B.24种C.36种 D.48种

变式3 某运动会即将举办，现在从6男4女共10名青年志愿者中，选出3男2女共5名志愿者，安排到编号为1、2、3、4、5的5个赛场，每个赛场只有一名志愿者，其中女志愿者甲不能安排在编号为1、2的赛场，编号为2的赛场必须安排女志愿者，那么不同安排方案有（ ）

A.1440种 B.2352种C.2880种 D.3960种

# 题型四分组分类问题

例4（多选题)现安排甲、乙、丙、丁、戊5名同学参加志愿者服务活动，有翻译、导游、礼仪、司机四项工作可以安排,则以下说法错误的是（）

A.若每人都安排一项工作，则不同的方法种数为 $5 ^ { 4 }$ B.若每项工作至少有1人参加，则不同的方法种数为 $\mathrm { A } _ { s } ^ { 4 } \mathrm { C } _ { 4 } ^ { 1 }$ C.每项工作至少有1人参加，甲、乙不会开车但能从事其他三项工作,丙、丁、戊都能胜任四项工作,则不同安排方法的种数是 $\mathrm { C } _ { 3 } ^ { 1 } \mathrm { C } _ { 4 } ^ { 2 } \mathrm { A } _ { 3 } ^ { 3 }$ $+ \mathbf { C } _ { 3 } ^ { 2 } \mathbf { A } _ { 3 } ^ { 3 }$ D.如果司机工作不安排,其余三项工作至少安排1人，则这5名同学全部被安排的不同方法数为 $\bigl ( \mathbf { C } _ { 5 } ^ { 3 } \mathbf { C } _ { 2 } ^ { 1 } + \mathbf { C } _ { 5 } ^ { 2 } \mathbf { C } _ { 3 } ^ { 2 } \bigr ) \mathbf { A } _ { 3 } ^ { 3 }$

变式4 近年来喜欢养宠物猫的人越来越多.某猫舍只有5个不同的猫笼，金渐层猫3只（猫妈妈和2只小猫）、银渐层猫4只、布偶猫1只.该猫舍计划将3只金渐层猫放在同一个猫笼里，4只银渐层猫每2只放在一个猫笼里，布偶猫单独放在一个猫笼里，则不同的安排有（

A.8种 B.30种C.360种 D.1440 种

变式5 甲、乙、丙、丁、戊5名志愿者参加消防安全宣传志愿者活动，现有A，B，$C$ 三个小区可供选择，每个志愿者只能选其中一个小区.则每个小区至少有一名志愿者，且甲不在A小区的概率为（

A.243 B.10 C

变式6有10个运动员名额，分给7个班，每班至少一个，有 种分配方案

# 题型五二项式定理相关问题

例5（多选题)已知 $\left( { 2 x - \frac { 1 } { \sqrt { x } } } \right) ^ { \prime }$ 的展开式的二项式系数和为128,则下列说法正确的是（）

A. $n = 7$ / B.展开式中各项系数的和为1 C.展开式中第4项的系数为35 D.展开式中含 $x ^ { 4 }$ 项的系数为672 变式7（多选题）已知 $\left( 2 - x \right) ^ { 8 } = a _ { 0 } +$ $a _ { 1 } x + a _ { 2 } x ^ { 2 } + \cdots + a _ { 8 } x ^ { 8 }$ ，则（

A. $a _ { 0 } = 2 ^ { 8 }$   
$\mathrm { B } . a _ { 1 } + a _ { 2 } + \cdots + a _ { 8 } = 1$   
C. $\left| { { a } _ { 1 } } \right| + \left| { { a } _ { 2 } } \right| + \left| { { a } _ { 3 } } \right| + \cdots + \left| { { a } _ { 8 } } \right| = 3 ^ { 8 }$ $\mathrm { D } . a _ { 1 } + 2 a _ { 2 } + 3 a _ { 3 } + \cdots + 8 a _ { 8 } = - 8$

# 【核心知识聚焦】

# 一、条件概率定义

一般地,设 $^ { A , B }$ 为两个随机事件,且 $P ( A ) >$ ${ \underline { { n } } } { \big ( } A B { \big ) }$ 0,我们称 $P \left( B \mid A \right) = { \frac { n ( A B ) } { n ( A ) } } = { \frac { \overline { { { n ( \Omega ) } } } } { \underline { { { n ( A ) } } } } } =$ $n ( \varOmega )$ $\frac { P ( A B ) } { P ( A ) }$ 为在事件 $A$ 发生的条件下,事件 $B$ 发生的条件概率，简称条件概率.特别地，当$P ( B | A ) = P ( B )$ ，且 $P ( A ) > 0$ 时，事件 $^ { A , B }$ 相互独立，则 $P ( A B ) = P ( A ) P ( B )$

# 二、条件概率的性质

$P ( A ) > 0$ $\varOmega$

设 ,样本空间为 ，则 $( 1 ) P ( \varOmega | A ) = 1$ (2）如果 $B$ 与 $c$ 是两个互斥事件，则 $P ( ( B \cup C ) \mid A ) = P ( B \mid A ) + P ( C \mid A ) ;$ (3）设事件 $\overline { B }$ 和 $B ^ { \prime }$ 互为对立事件，则 $P ( \overline { { B } } \mid A ) = 1 - P ( B \mid A )$

# 三、全概率公式

一般地,设 $A _ { 1 } , A _ { 2 } , \cdots , A _ { n }$ 是一组两两互斥的事件， $A _ { 1 } \cup A _ { 2 } \cup \cdots \cup A _ { n } = \Omega$ 且 $P ( A _ { i } ) > 0 , i =$ $1 , 2 , \cdots , n$ ,则对任意的事件 $B \subseteq \varOmega$ ，有 $P ( B )$ $= \ \sum _ { i = 1 } ^ { n } P ( A _ { i } ) P ( B \mid A _ { i } )$ ．称上面的公式为全概率公式.

4.贝叶斯公式

设 $A _ { 1 } , A _ { 2 } , \cdots , A _ { n }$ 是一组两两互斥的事件，$A _ { 1 } \cup A _ { 2 } \cup \cdots \cup A _ { n } = \Omega .$ 且 $P ( A _ { i } ) > 0 , i = 1 , 2 , \cdots$ $n$ ，,则对任意事件 $B \subseteq \varOmega , P ( B ) > 0$ ，有 $P ( A _ { i } \mid B ) = { \frac { P ( A _ { i } ) P ( B \mid A _ { i } ) } { P ( B ) } } =$ ${ \frac { P ( A _ { i } ) P ( B \mid A _ { i } ) } { \stackrel { n } { \sum } } } , i = 1 , 2 , \cdots , n .$ 在贝叶斯$\sum _ { k = 1 } P ( A _ { k } ) P ( B \mid A _ { k } )$ 公式中， $P ( A _ { i } )$ 和 $P ( A _ { i } \mid B )$ 分别称为先验概率和后验概率.

# 【进阶提升】

# 题型一 全概率公式的应用

例1某市举办了党史知识的竞赛.初赛采用"两轮制"方式进行，要求每个单位派出两个小组，且每个小组都要参加两轮比赛，两轮比赛都通过的小组才具备参与决赛的资格.某单位派出甲、乙两个小组参赛,在初赛中，若甲小组通过第一轮与第二轮比赛的概率分别是$\frac { 3 } { 4 } , \frac { 4 } { 5 }$ ,乙小组通过第一轮与第二轮比赛的概率分别是 $\cdot \frac { 3 } { 5 } , \frac { 2 } { 3 }$ ,且各个小组所有轮次比赛的结果互不影响.

(1)若该单位获得决赛资格的小组个数为  
$X$ ，求 $X$ 的数学期望；(2)已知甲、乙两个小组都获得了决赛资  
格,决赛以抢答题形式进行.假设这两组在决  
赛中对每个问题回答正确的概率恰好是各自  
获得决赛资格的概率.若最后一道题被该单位

的某小组抢到，且甲、乙两个小组抢到该题的可能性分别是 $45 \%$ ， $55 \%$ ,该题如果被答对，计算恰好是甲小组答对的概率

变式1 某人从甲地到乙地，乘火车、轮船、飞机的概率分别为0.2，0.4，0.4，乘火车迟到的概率为0.4，乘轮船迟到的概率为0.3，乘飞机迟到的概率为0.5，则这个人迟到的概率是 ；如果这个人迟到了，他乘船迟到的概率是

# 题型二 全概率公式中的新定义问题

例2一医疗团队为研究某地的一种地方性疾病与当地居民的卫生习惯（卫生习惯分为良好和不够良好两类)的关系，在已患该疾病的病例中随机调查了100 例（称为病例组），同时在未患该疾病的人群中随机调查了100人（称为对照组），得到如下数据：

<html><body><table><tr><td>分类</td><td>不够良好</td><td>良好</td></tr><tr><td>病例组</td><td>40</td><td>60</td></tr><tr><td>对照组</td><td>10</td><td>90</td></tr></table></body></html>

<html><body><table><tr><td>α</td><td>0.05</td><td>0.01</td><td>0.001</td></tr><tr><td>xa</td><td>3.841</td><td>6.635</td><td>10.828</td></tr></table></body></html>

(1)根据小概率值 $\alpha = 0 . 0 0 1$ 的独立性检验，判断患该疾病群体与未患该疾病群体的卫生习惯是否有差异？

(2)从该地的人群中任选一人,A表示事件"选到的人卫生习惯不够良好”， $B$ 表示事件“选到的人患有该疾病”，P(BIA）与P(B|）的比值是卫生习惯不够良好对患该疾病风险程度的一项度量指标,记该指标为 $R$

(i)证明 $ . R = \frac { P ( A \mid B ) } { P ( \overline { { A } } \mid B ) } \cdot \frac { P ( \overline { { A } } \mid \overline { { B } } ) } { P ( A \mid \overline { { B } } ) } ;$ (ii)利用该调查数据，给出 $P ( A \mid B ) , P ( A \mid \overline { { B } } )$ 的估计值,并利用(i)的结果给出 $R$ 的估计值附 $\chi ^ { 2 } = \frac { n \left( a d - b c \right) ^ { 2 } } { \left( a + b \right) \left( c + d \right) \left( a + c \right) \left( b + d \right) } ,$

变式2 银行储存卡的密码由6位数字组成.某人在银行自助取款机上取钱时，忘记了密码的最后1位数字，求：

（1）任意按最后1位数字，不超过2次就按对的概率；(2)如果记得密码的最后1位是偶数，不超过2次就按对的概率.

# 题型三检验问题

例3在某地区进行流行病调查，随机调查了100名某种疾病患者的年龄，得到如下的样本数据频率分布直方图.

![](images/560af1ee079e9e29e175d7012d0c88abcab3efb65ad27be22e122fa0cc6d98fa.jpg)

(1)估计该地区这种疾病患者的平均年龄（同一组中的数据用该组区间的中点值代表）；(2)估计该地区一人患这种疾病年龄在区间[20,70)的概率.

(3)已知该地区这种疾病的患病率为$0 . 1 \%$ ，该地区的年龄位于区间[40,50)的人口占该地区总人口的 $16 \%$ ，从该地区任选一人，若此人年龄位于区间[40,50），求此人患该种疾病的概率.（样本数据中的患者年龄位于各地区的频率作为患者年龄位于该区间的概率，精确到0.0001)

变式3 有研究显示，人体内某部位的直径约 $1 0 \ \mathrm { m m }$ 的结节约有 $0 . 2 \%$ 的可能性会在1年内发展为恶性肿瘤.某医院引进一台检测设备，可以通过无创的血液检测，估计患者体内直径约 $1 0 ~ \mathrm { m m }$ 的结节是否会在1年内发展为恶性肿瘤，若检测结果为阳性，则提示该结节会在1年内发展为恶性肿瘤，若检测结果为阴性，则提示该结节不会在1年内发展为恶性肿瘤.这种检测的准确率为 $8 5 \%$ ，即一个会在1年内发展为恶性肿瘤的患者有 $8 5 \%$ 的可能性被检出阳性，一个不会在1年内发展为恶性肿瘤的患者有 $85 \%$ 的可能性被检出阴性.患者甲被检查出体内长了一个直径约$1 0 \ \mathrm { m m }$ 的结节，他做了该项无创血液检测.

（1）求患者甲检查结果为阴性的概率；（2）若患者甲的检查结果为阴性，求他的这个结节在1年内发展为恶性肿瘤的概率（结果保留5位小数）；（3）若某公司为每位参加该项检查的职工患者缴纳200元保险费，对于检测结果为阴性，但在1年内发展为恶性肿瘤的患者，保险公司赔付该患者20万元，若每年参加该项检查的患者有1000人，请估计保险公司每年在这个项目上的收益.

# 第3讲　递推型概率问题··

# 【核心知识聚焦】

# 一、递推型概率的原理

由全概率公式，我们既可以构造某些递推关系求解概率,还可以推导经典的一维随机游走模型,即：设数轴上一个点,它的位置只能位于整点处,在时刻 $t = 0$ 时，位于点 $x = i ( i \in$ $\mathbf { N } ^ { * }$ ),下一个时刻,它将以概率 $\alpha$ 或者 $\beta ( \alpha \in$ (0,1)， $\alpha + \beta = 1$ )向左或者向右平移一个单位.若记状态 $X _ { t x = i }$ 表示：在时刻 $i$ 该点位于位置 $_ x$ $\mathbf { \Lambda } = i ( \mathbf { \Lambda } _ { i } \in \mathbf { N } ^ { * } \mathbf { \Lambda } )$ ,那么由全概率公式可得，

$P ( X _ { \iota + 1 = i } ) = P ( X _ { \iota = i - 1 } ) \ \cdot \ P ( X _ { \iota + 1 = i } \mid X _ { \iota = i - 1 } )$ $+ P ( X _ { \iota = i + 1 } ) \cdot P ( X _ { \iota + 1 = i } \mid X _ { \iota = i - 1 } ) .$

另一方面,由于 $P \left( \left. X _ { t + 1 = i } \right. \right| \left. X _ { t = i - 1 } \right) = \beta$ ，$P ( X _ { t + 1 = i } \mid X _ { t = i + 1 } ) = \alpha$ ，代人上式可得：

$P _ { i } = \alpha \cdot P _ { { i + 1 } } + \beta \cdot P _ { { i - 1 } }$ 进一步,我们假设在 $x = 0$ 与 $x = m \big ( m > 0 , m \in \mathbf { N } ^ { * } $ )处各有一个吸收壁,当点到达吸收壁时被吸收,不再游走.于是 $\scriptstyle P _ { 0 } = 0 , P _ { { } _ { m } } = 1$ 随机游走模型是一个典型的马尔科夫过程.进一步,若点在某个位置后有三种情况：向左平移一个单位，其概率为 $b$ ，原地不动，其概率为 $n$ ,向右平移一个单位,其概率为 $n$ ,那么根据全概率公式可得：

$$
P _ { i } = a P _ { i - 1 } + b P _ { i } + c P _ { i + 1 }
$$

# 二、常见的递推概率类型

1. $a _ { n } = p a _ { n - 1 } + q$   
2. $a _ { n + 1 } = p a _ { n } + f ( n )$ ;  
3. $a _ { n + 1 } = a _ { n } \cdot f ( n )$ ;

$4 . a _ { n + 1 } = p a _ { n } + q a _ { n - 1 } .$

# 【进阶提升】

# 题型一给定递推关系型

例1为了治疗某种疾病,研制了甲、乙两种新药,希望知道哪种新药更有效，为此进行动物试验.试验方案如下：每一轮选取两只白鼠对药效进行对比试验.对于两只白鼠,随机选一只施以甲药,另一只施以乙药.一轮的治疗结果得出后,再安排下一轮试验.当其中一种药治愈的白鼠比另一种药治愈的白鼠多4只时，就停止试验,并认为治愈只数多的药更有效.为了方便描述问题,约定：对于每轮试验,若施以甲药的白鼠治愈且施以乙药的白鼠未治愈则甲药得1分，乙药得-1分;若施以乙药的白鼠治愈且施以甲药的白鼠未治愈则乙药得1分，甲药得-1分;若都治愈或都未治愈则两种药均得0分.甲、乙两种药的治愈率分别记为 $\alpha$ 和 $\beta$ ,一轮试验中甲药的得分记为 $X$

(1)求 $X$ 的分布列；

(2)若甲药、乙药在试验开始时都赋予4分 $, p _ { i } ( i = 0 , 1 , \cdots , 8 )$ 表示“甲药的累计得分为 $i$ 时,最终认为甲药比乙药更有效”的概率，则$p _ { 0 } = 0 , p _ { 8 } = 1 , p _ { 1 } = a p _ { i - 1 } + b p _ { 1 } + c p _ { i + 1 } \big ( i = 1 , 2 ,$ …,7)，其中 $a = P ( X = { - } 1 ) , b = P ( X = 0 ) , c =$ $P ( X = 1 )$ .假设 $\alpha = 0 . 5 , \beta = 0 . 8$

$\textcircled{1}$ 证明： $\{ p _ { i + 1 } - p _ { i } \}$ $\dot { \cdot } = 0 , 1 , 2 , \cdots , 7 )$ 为等比数列；

$\textcircled{2}$ 求 $p _ { 4 }$ ,并根据 $p _ { 4 }$ 的值解释这种试验方案 的合理性.

变式1 校足球队中的甲、乙、丙、丁四名球员将进行传球训练，第1次由甲将球传出，每次传球时，传球者都等可能的将球传给另外三个人中的任何一人，如此不停地传下去，且假定每次传球都能被接到.记开始传球的人为第1次触球者，第 $\boldsymbol { n }$ 次触球者是甲的概率记为 $P _ { n }$ ，即 $P _ { 1 } = 1$

（1）求 $P _ { 3 }$ （直接写出结果即可）；

（2）证明：数列 $\{ P _ { n } - \frac { 1 } { 4 } \}$ 为等比数列，并判断第19次与第20次触球者是甲的概率的大小

# 题型二期望递推型

例2为了提高学生对航空航天科技的兴趣,培养学生良好的科学素养，某校组织学生参加航空航天科普知识答题竞赛，每位参赛学生答题若干次，答题赋分方法如下：第1次答题，答对得20分，答错得10分；从第2次答题开始，答对则获得上一次答题得分的两倍,答错得10分.学生甲参加答题竞赛，每次答对的概率为 $\frac { 3 } { 4 }$ ,各次答题结果互不影响.

(1)求甲前3次答题得分之和为40分的   
概率； (2)记甲第 $_ i$ 次答题所得分数 $X _ { i } ( i \in \mathbf { N } ^ { \bullet }$ ）   
的数学期望为 $E ( X _ { i } )$ $\textcircled{1}$ 写出 $E ( X _ { i - 1 } )$ 与 $E ( X _ { i } )$ 满足的等量关系   
式(直接写出结果,不必证明)； $\textcircled{2}$ 若 $E ( X _ { i } ) > 1 0 0$ ，求 $i$ 的最小值.

变式2 甲口袋中装有2个黑球和1个白球，乙口袋中装有3个白球.现从甲、乙两口袋中各任取一个球交换放入另一口袋，重复 $n$ 次这样的操作，记甲口袋中黑球个数为 $X _ { n }$ ，恰有2个黑球的概率为 $p _ { n }$ ，恰有1个黑球的概率为 $q _ { n }$

（1）求 $p _ { 1 } , q _ { 1 }$ 和 $P _ { 2 } , q _ { 2 }$ ：

（2）求 $2 p _ { n } + q _ { n }$ 与 $2 p _ { n - 1 } + q _ { n - 1 }$ 的递推关系式和 $X _ { n }$ 的数学期望 $E ( X _ { n } )$ （用 $n$ 表示）

# 题型三探索型递推

例3马尔科夫链是概率统计中的一个重要模型,也是机器学习和人工智能的基石,在强化学习、自然语言处理、金融领域、天气预测等方面都有着极其广泛的应用.其数学定义为：假设我们的序列状态是 $\cdot , X _ { \iota - 2 } , X _ { \iota - 1 } , X _ { \iota }$ $X _ { t + 1 }$ ,…,那么 $X _ { \iota + 1 }$ 时刻的状态的条件概率仅依赖前一状态 $X _ { \iota }$ ,即 $P ( X _ { t + 1 } \mid \cdots , X _ { t - 2 } , X _ { t - 1 } , X _ { t } ) =$ $P ( X _ { t + 1 } \mid X _ { t } )$

现实生活中也存在着许多马尔科夫链,例如著名的赌徒输光模型.

假如每一局赌徒赌赢的概率为 $50 \%$ ，且每局赌赢可以赢得1元，每一局赌徒赌输的概率为 $50 \%$ ，且赌输就要输掉1元.赌徒会一直玩下去，直到遇到如下两种情况才会结束赌博游戏：一种是手中赌金为0元，即赌徒输光；一种是赌金达到预期的 $B$ 元,赌徒停止赌博.记赌徒的本金为 $A \left( A \in \mathbf { N } ^ { \ast } , A < B \right)$ ,赌博过程如下图的数轴所示.

0.50.5 25 W A-1AA+1 W 0 B 0.50.5

当赌徒手中有 $n$ 元 $( 0 \leqslant n \leqslant B , n \in \mathbf { N } )$ 时，最终输光的概率为 $P ( n )$ ,请回答下列问题：

(1)请直接写出 $P ( 0 )$ 与 $P ( B )$ 的数值.(2)证明 $\{ P ( n ) \}$ 是一个等差数列,并写出

公差 $d$

(3)当 $A = 1 0 0$ 时,分别计算 $B = 2 0 0 , B =$ 1000时， $P ( A )$ 的数值，并结合实际，解释当 $B {  }$ $\pmb { \alpha }$ 时， $P ( A )$ 的统计含义.

变式3 有 $n$ 个编号分别为 $1 , 2 , \cdots , n$ 的盒子，第1个盒子中有2个白球，1个黑球，其余盒子中均为1个白球，1个黑球，现从第1个盒子中任取一球放入第2个盒子，再从第2个盒子中任取一球放入第3个盒子，以此类推，则从第2个盒子中取到白球的概率是 ，从第 $n$ 个盒子中取到白球的概率是

# 第4讲比赛问题·

# 【核心知识聚焦】

# 比赛问题处理细节

1.引入变量表示事件：可用“字母 $^ +$ 变量角标”的形式表示事件“第几局胜利”，例如： $A _ { i }$ 表示“第 $_ i$ 局比赛胜利”，则 $\textstyle { \left| { \overline { { A _ { i } } } } \right. }$ 表示"第i局比赛失败”.

2.理解事件中常见词语的含义： $A , B$ 中至少有一个发生的事件为 $A \cup B ; A , B$ 都发生的事件为 $A B { \mathrm { ; } } A , B$ 都不发生的事件为 $\overline { { A } } \overline { { B } } ; A , B$ 恰有一个发生的事件为 $A \ { \overline { { B } } } \cup { \overline { { A } } } B ; A , B$ 至多一个发生的事件为 $A \ { \overline { { B } } } \cup { \overline { { A } } } B \cup { \overline { { A } } } \ { \overline { { B } } }$

3.善于"正难则反"求概率：若所求事件含情况较多，可以考虑求对立事件的概率,再用

$P ( A ) = 1 - P ( \overline { { A } } )$ 解出所求事件概率

4.搞清楚比赛结束的条件.

# 【进阶提升】

# 题型一 知道比分型比赛问题

例111分制乒乓球比赛，每赢一球得1分，当某局打成 $1 0 \colon 1 0$ 平后，每球交换发球权，先多得2分的一方获胜，该局比赛结束.甲、乙两位同学进行单打比赛，假设甲发球时甲得分的概率为0.5,乙发球时甲得分的概率为0.4,各球的结果相互独立.在某局双方 $1 0 \colon 1 0$ 平后，甲先发球，两人又打了 $X$ 个球该局比赛结束.

(1)求 $P ( X = 2 )$ ;   
(2)求事件“ $X = 4$ 且甲获胜”的概率.

变式1 甲、乙两队进行篮球决赛，采取七场四胜制（当一队赢得四场胜利时，该队获胜，决赛结束）.根据前期比赛成绩，甲队的主客场安排依次为“主主客客主客主”.设甲队主场取胜的概率为0.6，客场取胜的概率为0.5，且各场比赛结果相互独立，则甲队以 $4 : 1$ 获胜的概率是

# 题型二 知单场概率型比赛问题

例2甲、乙两个学校进行体育比赛,比赛共设三个项目，每个项目胜方得10分，负方得0分，没有平局.三个项目比赛结束后，总得分高的学校获得冠军.已知甲学校在三个项目中获胜的概率分别为0.5,0.4,0.8,各项目的比赛结果相互独立.

(1)求甲学校获得冠军的概率；(2)用 $X$ 表示乙学校的总得分，求 $X$ 的分布列与期望.

变式2 甲乙两人进行围棋比赛，约定先连胜两局者直接贏得比赛，若赛完5局仍未出现连胜，则判定获胜局数多者赢得比赛，假设每局甲获胜的概率为 $\frac { 2 } { 3 }$ ，乙获胜的概率为 $\frac 1 3$ ，各局比赛结果相互独立.

（1）求甲在4局以内（含4局）赢得比赛的概率；

(2）记 $X$ 为比赛决出胜负时的总局数，求 $X$ 的分布列和均值.

# 题型三轮换型比赛问题

例3甲、乙、丙三位同学进行羽毛球比赛,约定赛制如下：累计负两场者被淘汰；比赛前抽签决定首先比赛的两人，另一人轮空；每场比赛的胜者与轮空者进行下一场比赛,负者下一场轮空，直至有一人被淘汰;当一人被淘汰后,剩余的两人继续比赛，直至其中一人被淘汰,另一人最终获胜,比赛结束.经抽签，甲、乙首先比赛，丙轮空.设每场比赛双方获胜的概率都为 $\frac { 1 } { 2 }$ ,每场比赛结果相互独立.

(1)求甲连胜四场的概率；  
(2)求需要进行第五场比赛的概率;  
(3)求丙最终获胜的概率.

变式3 中学阶段，数学中的“对称性”不仅体现在平面几何、立体几何、解析几何和函数图象中，还体现在概率问题中.例如，甲乙两人进行比赛，若甲每场比赛获胜概率均为 $\cdot \frac { 1 } { 2 }$ ，且每场比赛结果相互独立，则由对称性可知，在5场比赛后，甲获胜次数不低于3场的概率为 $\frac 1 2$ 现甲乙两人分别进行独立重复试验，每人抛掷一枚质地均匀的硬币.

（1)若两人各抛掷3次，求抛掷结果中甲正面朝上次数大于乙正面朝上次数的概率；(2）若甲抛掷 ${ \bigl ( } n + 1 { \bigr ) }$ 次，乙抛掷 $n$ 次，$n \in \mathbf { N } ^ { * }$ ，求抛掷结果中甲正面朝上次数大于乙正面朝上次数的概率.

# 题型四决策型比赛问题

例4某学校组织“一带一路”知识竞赛,有 $A , B$ 两类问题,每位参加比赛的同学先在两类问题中选择一类并从中随机抽取一个问题回答,若回答错误,则该同学比赛结束;若回答正确,则从另一类问题中再随机抽取一个问题回答,无论回答正确与否,该同学比赛结束.A类问题中的每个问题回答正确得20分，否则得0分， $B$ 类问题中的每个问题回答正确得80分，否则得0分;已知小明能正确回答A类问题的概率为0.8,能正确回答 $B$ 类问题的概率为0.6,且能正确回答问题的概率与回答次序无关.

(1)若小明先回答A类问题，记 $X$ 为小明的累计得分，求 $X$ 的分布列；(2)为使累计得分的期望最大，小明应选择先回答哪类问题？并说明理由.

变式4 某校举行校园足球比赛.根据比赛规则，淘汰赛阶段，参赛双方有时需要通过“点球大战”的方式决定胜负.“点球大战”的规则如下：>>0中其

$\textcircled{1}$ 两队各派5名队员，双方轮流踢点球，累计进球个数多者胜；

$\textcircled{2}$ 如果在踢满5轮前，一队的进球数已多于另一队踢满5轮最多可能射中的球数，则不需要再踢（例如：第4轮结束时，双方“点球大战”的进球数比为2：0，则不需要再踢第5轮）；

$\textcircled{3}$ 若前5轮“点球大战”中双方进球数持平，则从第6轮起，双方每轮各派1人踢点球，若均进球或均不进球，则继续下一轮，直到出现一方进球另一方不进球的情况，进球方胜出

假设每轮点球中进球与否互不影响，各轮结果也互不影响.

（1）假设踢点球的球员等可能地随机选择球门的左、中、右三个方向射门，门将也会等可能地选择球门的左、中、右三个方向来扑点球，而且门将即使方向判断正确，左右两边将球扑出的可能性为 $\frac { 1 } { 5 }$ ，中间方向扑出的可能性为 $\frac { 3 } { 5 }$ 若球员射门均在门内，在一次“点球大战”中，求门将在前4次扑出点球的个数 $\chi$ 的分布列和数学期望

(2)现有甲、乙两队在淘汰赛中相遇，需要通过“点球大战”来决定胜负.设甲队每名队员射进点球的概率均为 $\frac { 3 } { 4 }$ 乙队每名队员射进点球的概率均为 $\frac { 2 } { 3 }$ ，若甲队先踢，求甲队恰在第4轮取得胜利的概率.

![](images/48621eda60dd5839db9212e0056ffba1160fccd75de7649c6e56419ef9a14a9f.jpg)

心

#

# 【核心知识聚焦】

# 一、离散型随机变量的均值与方差

一般地,若离散型随机变量 $X$ 的分布列为

<html><body><table><tr><td>X</td><td>x</td><td>x</td><td></td><td>xn</td></tr><tr><td>P</td><td>P</td><td>P</td><td></td><td>P</td></tr></table></body></html>

1.均值

称 $E ( \boldsymbol { X } ) = x _ { 1 } p _ { 1 } + x _ { 2 } p _ { 2 } + \cdots + x _ { n } p _ { n }$ 为随机变量 $X$ 的均值或数学期望.它反映了随机变量取值的平均水平.

2.方差

称 $D ( X ) \ = \ \sum _ { i = 1 } ^ { n } \ { \big ( } x _ { i } - E ( X ) ) ^ { 2 } p _ { i }$ 为随机变量 $X$ 的方差,它刻画了随机变量 $X$ 与其均值$E ( X )$ 的偏离程度,并称 $\sqrt { D ( X ) }$ 为随机变量 $X$ 的标准差.

<html><body><table><tr><td>X</td><td>0</td><td>1</td></tr><tr><td>P</td><td>1-p</td><td>P</td></tr></table></body></html>

其中 $0 < p < 1$ ,则称离散型随机变量 $X$ 服从两点分布.

# 四、超几何分布

一般地,假设一批产品共有 $N$ 件,其中有$M$ 件次品，从 $N$ 件产品中随机抽取 $_ n$ 件（不放回)，用 $X$ 表示抽取的几件产品中的次品数，则$X$ 的分布列为P(x=k)=CC-M( $P ( X = k ) = { \frac { \mathbb { C } _ { M } ^ { k } \mathbb { C } _ { N - M } ^ { n - k } } { \mathbb { C } _ { N } ^ { n } } } { \big ( } k = m , m +$ $1 , m + 2 , \cdots , r )$

其中 $n , N , M \in \mathbf { N } ^ { * }$ ， $M \leqslant N$ ， $n \leqslant N$ ， $\textbf { \textit { m } } =$ $\operatorname* { m a x } \left\{ 0 , n - N + M \right\} , r = \operatorname* { m i n } \left\{ n , m \right\}$

如果随机变量 $X$ 的分布列具有上式的形式,则称随机变量 $X$ 服从超几何分布.

# 二、均值与方差的性质

$\textcircled { 1 } E ( a X + b ) = a E ( X ) + b .$ $\textcircled { 2 } D \big ( a X + b \big ) = a ^ { 2 } D \big ( X \big )$ ： $( a , b$ 为常数)

# 三、两点分布

如果随机变量 $X$ 的分布列为

# 五、独立重复试验与二项分布

1.独立重复试验是指在相同条件下可重复进行的,各次之间相互独立的一种试验,在这种试验中每一次试验只有两种结果,即要么发生，要么不发生，且任何一次试验中发生的概率都是一样的.

2.在 $\boldsymbol { n }$ 重伯努利试验中,设每次试验中事件 $A$ 发生的概率为 $p , ( 0 < P < 1 )$ 用 $X$ 表示事件 $A$ 发生的次数，则 $X$ 的分布列为 $P ( X = k ) =$ $\complement _ { n } ^ { k } p ^ { k } ( 1 - p ) ^ { n - k } ( k = 0 , 1 , 2 , \cdots , n )$ ,则称随机变量 $X$ 服从二项分布，记为 $X \sim B ( n , p )$

# 六、两点分布与二项分布的均值、方差

1.若随机变量 $X$ 服从两点分布,则 $E ( X ) =$ $p , D ( X ) = p ( 1 - p )$ 2.若 $X \sim B ( \boldsymbol { n } , \boldsymbol { p } )$ ，则 $E ( X ) = n p$ ， $D ( X ) =$ $n p ( 1 - p )$ ：

# 七、正态分布

1.正态曲线：函数 $f ( x ) = { \frac { 1 } { \sqrt { 2 \pi } \sigma } } \mathrm { e } ^ { - { \frac { ( x - \mu ) ^ { 2 } } { 2 \sigma ^ { 2 } } } }$ ,${ \boldsymbol { x } } \in \mathbf { R }$ ,其中 $\sigma > 0 , \mu \in \mathbf { R }$ 为参数.我们称 $f ( x )$ 的图象为正态密度曲线,简称正态曲线.

2.正态曲线的特点

$\textcircled{1}$ 曲线位于 $_ x$ 轴上方，与 $_ x$ 轴不相交;$\textcircled{2}$ 曲线是单峰的,它关于直线 $x = \mu$ 对称；$\textcircled{3}$ 曲线在 $x = \mu$ 处达到峰值 $\scriptstyle \cdot { \frac { 1 } { \sigma { \sqrt { 2 \pi } } } } ,$ $\textcircled{4}$ 曲线与 $_ x$ 轴之间的面积为1;$\textcircled{5}$ 当参数 $\sigma$ 一定时，曲线的位置由 $\mu$ 确定，曲线随着 $\mu$ 的变化而沿 $_ x$ 轴平移,如图甲所示；$\textcircled{6}$ 当 $\mu$ 一定时,曲线的形状由 $\sigma$ 确定， $\sigma$ 越小，曲线越“瘦高”,表示随机变量 $X$ 的分布越集中； $\sigma$ 越大，曲线越“矮胖”,表示随机变量$X$ 的分布越分散，如图乙所示.

![](images/f2e212b07bbc3adb341031b507ba8912b485957de37155a2e43b075de89705db.jpg)

3.正态变量在三个特殊区间内取值的概 率值

$\textcircled { 1 } P ( \mu - \sigma \leqslant X \leqslant \mu + \sigma ) \approx 0 . 6 8 2 \ 7$ $\textcircled { 2 } P ( \mu - 2 \sigma \leqslant X \leqslant \mu + 2 \sigma ) \approx 0 . 9 5 4 ~ 5$ $\textcircled { 3 } P ( \mu - 3 \sigma \leqslant X \leqslant \mu + 3 \sigma ) \approx 0 . 9 9 7 \ 3 .$

# 【进阶提升】

# 题型一 期望对比型决策

例1某大型商场举行“消费领奖”的促销活动，在规定的商品中，顾客消费满200元（含200 元)即可抽奖一次，抽奖方式有两种(顾客只能选择其中一种).

方案一：从装有5个形状、大小完全相同的小球(其中红球1个,黑球4个)的抽奖盒中,有放回地摸出2个球,每摸出1次红球,立减100元.

方案二：从装有10个形状、大小完全相同的小球(其中红球2个,黑球8个)的抽奖盒中,不放回地摸出2个球,中奖规则为：若摸出2个红球,享受免费优惠;若摸出1个红球，1个黑球，则打5折;若摸出2个黑球，则抵扣现金50元.

(1)某顾客恰好消费200元，选择抽奖方  
案一，求该顾客实付现金的分布列和期望;(2)若顾客消费300元，试根据实付金额  
的期望分析顾客选择哪一种抽奖方式更合理.

JiN下的A类水果以20元每箱的价格出售给果汁加工企业，以这100天记录的日需求量的频率作为日需求量发生的概率。

（1）如果某天的进货量为24箱，用X表示该水果店当天卖出A类水果所获得的利润，求 $X$ 的分布列与数学期望；

(2）如果店老板计划某天购进24箱或25箱的A类水果，请以当天利润的期望作为决策依据，判断应当购进24箱还是25箱.

# 公态五寸

变式1 某地的水果店老板记录了过去100天A类水果的日需求量 $x$ （单位：箱），整理得到数据如下表所示.

<html><body><table><tr><td>x</td><td>22</td><td>23</td><td>24</td><td>25</td><td>26</td></tr><tr><td>频数</td><td>20</td><td>20</td><td>30</td><td>18</td><td>12</td></tr></table></body></html>

其中每箱A类水果的进货价为50元，出售价为100元，如果当天卖不完，就将剩

大曲

![](images/eb2f9372efbe8b49920e4ac8e5046fd199ea57544bdc4a39d35c10cadf5b8bae.jpg)

# 题型二开放型决策问题

例2某学校引进 $M , N$ 两种类型的自动体外除颤器(简称 AED)若干,并组织全校师生学习AED 的使用规则及方法.经过短期的强化培训,在单位时间内，选择 $M , N$ 两种类型AED 操作成功的概率分别为 $\frac { 2 } { 3 } \pi \frac { 1 } { 2 }$ ，假设每次操作能否成功相互独立.

(1)现有某受训学生进行急救演练，假定他每次随机等可能选择 $M$ 或 $N$ 型AED进行操作,求他恰好在第二次操作成功的概率;

(2)为激发师生学习并正确操作AED的热情，学校选择一名教师代表进行连续两次设备操作展示，下面是两种方案：

方案甲：在第一次操作时，随机等可能的选择M或 $N$ 型 AED中的一种,若第一次对某类型AED操作成功，则第二次继续使用该类型设备;若第一次对某类型AED操作不成功，则第二次使用另一类型AED进行操作.

方案乙：在第一次操作时，随机等可能的选择M或 $N$ 型AED中的一种，无论第一次操作是否成功，第二次均使用第一次所选择的设备

假定方案选择及操作不相互影响，以成功操作累积次数的期望作为决策依据，哪种方案更好？

出之0. dsil0/

变式2 某医院在10人一组的流感病毒检测中有1人抗体检测呈阳性，为了能找出这1例阳性感染者，且确认感染何种类型的流感病毒，需要通过做血清检测，血清检测结果呈阳性的即为感染人员，呈阴性的表示没被感染、拟采用两种方案检测、

方案甲：将这10人逐个做血清检测，直到能确定感染人员为止

方案乙：将这10人的血清随机等分成两组，随机将其中一组的血清混在一起检测，若结果为阳性，则表示感染人员在该组中，然后再对该组中每份血清逐个检测，直到能确定感染人员为止；若结果呈阴性，则对另一组中每份血清逐个检测，直到能确定感染人员为止.把采用方案甲，直到能确定感染人员为止，检测的次数记为 $X$

（1）求 $X$ 的数学期望 $E ( X )$ ;(2）如果每次检测的费用相同，以检测费用的期望作为决策依据，应选择方案甲与方案乙哪一种？

# 题型三最值型决策问题

例32022年卡塔尔世界杯采用的“半自动越位定位技术”成为本届比赛的一大技术亮点,该项技术的工作原理是将若干个传感器芯片内置于足球中，每个传感芯片都可以高频率定位持球球员，以此判断该球员是否越位.为了研究该技术的可靠性，现从生产的传感芯片中随机抽取100个，将抽取到的传感芯片的最高频率（单位： $\mathrm { H } \mathbf { z }$ )统计后，得到的频率分布直方图如图所示：

(1)求这批芯片的最高频率的平均值 $\bar { x }$ （同一组中的数据用该组区间的中点值作代表)和方差 $s ^ { 2 }$ (2)根据频率分布直方图，可以近似认为这批传感芯片的最高频率 $\eta$ 服从正态分布$N ( \mu , \sigma ^ { 2 } )$ .用样本平均数 $\overline { { x } }$ 作为 $\mu$ 的估计值 $\hat { \mu }$ ，用样本标准差 $s$ 作为 $\sigma$ 的估计值 $\hat { \sigma }$ ，试估计，从这批传感芯片中任取一个，其最高频率大于$8 0 ~ \mathrm { H z }$ 的概率（精确到0.01）；

(3)若传感芯片的最高频率大于 $8 0 ~ \mathrm { H z }$ ，则该传感志片是可精确定位的，现给每个足球内置 $k ( k = 1 , 2 , 3 , 4 )$ 个传感芯片，若每个足球中可精确定位的芯片数不少于一半，则该足球可以满足赛事要求，能够精确判定球员是否越位,否则就需要增加裁判数量，通过助理裁判指证、慢动作回放等方式进行裁定.已知每个传感芯片的生产和维护费用约为1万元/场，因足球不可精确定位而产生的一次性人力成本为12万元/场，从单场比赛的成本考虑，每个足球内置多少个芯片，可以让比赛的总成本最低？

![](images/58a5c2fe482742874bcc274be4f86419d0120116b7e3116bce373557ad2748af.jpg)

附： $P ( \mu - \sigma { \leqslant } x { \leqslant } \mu + \sigma ) \approx 0 . 6 8 2 7 , P ( \mu -$ $2 \sigma \leqslant x \leqslant \mu + 2 \sigma ) \approx 0 . 9 5 4 5 , P ( \mu - 3 \sigma \leqslant x \leqslant \mu +$ $3 \sigma ) \approx 0 . 9 9 7 3 .$

变式3 某区政府发放了市内旅游消费券，该消费券包含 $A , B , C , D , E , F$ 六个旅游项目，甲、乙、丙、丁四人每人计划从中任选两个不同的项目参加，且他们的选择互不影响.

(1)求甲、乙、丙、丁这四个人中至少有一人选择项目A的概率；

(2）记 $X$ 为这四个人中选择项目A的人数，求 $X$ 的分布列及数学期望；

（3）如果将甲、乙、丙、丁四个人改为n个人（ $\pi > 4$ ），其他要求相同，问：这 $n$ 个人中选择项目A的人数最有可能是多少人？

# 【核心知识聚焦】

# 二项分布的两类最值

1.当 $p$ 给定时，可得到函数 $f ( k ) ~ =$ $\mathrm { C } _ { n } ^ { k } p ^ { k } \left( 1 - p \right) { } ^ { n - k } , k = 0 , 1 , 2 , \cdots , n$ ，这个是数列的最值问题.

$\frac { p _ { k } } { p _ { k - 1 } } = \frac { \mathrm { C } _ { k } ^ { n } p ^ { k } \left( 1 - p \right) ^ { n - k } } { \mathrm { C } _ { n } ^ { k - 1 } p ^ { k - 1 } \left( 1 - p \right) ^ { n - k + 1 } } = \frac { \left( n - k + 1 \right) p } { k { \left( 1 - p \right) } } =$ ${ \frac { k ( 1 - p ) + ( n + 1 ) p - k } { k ( 1 - p ) } } = 1 + { \frac { ( n + 1 ) p - k } { k ( 1 - p ) } } .$

分析：当 $k < ( n + 1 ) p$ 时， $p _ { k } > p _ { k - 1 } , p _ { k }$ 随 $k$ 值的增加而增加；当 $k > ( n + 1 ) p$ 时 ${ \bf \nabla } _ { P _ { k } } < P _ { k - 1 }$ ，$p _ { k }$ 随 $k$ 值的增加而减少.如果 $( n + 1 ) p$ 为正整数，当 $k = { \left( { \begin{array} { l } { n + 1 } \end{array} } \right) } p$ 时， ${ P _ { k } } = { P _ { k - 1 } }$ ,此时这两项概率均为最大值.如果 $( n + 1 ) p$ 为非整数，而 $k$ 取$( n + 1 ) p$ 的整数部分，则 $p _ { k }$ 是唯一的最大值.

注：在二项分布中，若数学期望为整数，则当随机变量 $k$ 等于期望时，概率最大.

2.当 $k$ 给定时，可得到函数 $f \left( p \right) =$ $ { \mathrm { C } ^ { k } } _ { n } p ^ { k } \left( 1 - p \right) ^ { n - k } , p \in \left( 0 , 1 \right)$ ,这个是函数的最值问题，

这可以用导数求函数最值与最值点.

$$
\begin{array} { r l } & { \quad f ( p ) = \mathrm { C } _ { n } ^ { k } \left[ k p ^ { k - 1 } \left( 1 - p \right) ^ { n - k } - p ^ { k } \left( n - k \right) \right. \cdot } \\ & { \left. ( 1 - p ) ^ { n - k - 1 } \right] = \mathrm { C } _ { n } ^ { k } p ^ { k - 1 } \left( 1 - p \right) ^ { n - k - 1 } \left[ k ( 1 - p ) - \right. } \\ & { \left. ( n - k ) p \right] = \mathrm { C } _ { n } ^ { k } p ^ { k - 1 } \left( 1 - p \right) ^ { n - k - 1 } \left( k - n p \right) . } \end{array}
$$

当 $k = 1 , 2 , \cdots , n - 1$ 时,由于当 $p < { \frac { k } { n } }$ 时，$f ^ { \prime } ( \boldsymbol { p } ) > 0 , f ( \boldsymbol { p } )$ 单调递增，当 $p > { \frac { k } { n } }$ 髙时 $f ^ { \prime } ( p ) <$ $0 , f ( p )$ 单调递减，故当 $p = { \frac { k } { n } }$ 时，f(p)取得最大

值 $f \left( p \right) _ { \mathrm { { r a x } } } = f \left( { \frac { k } { n } } \right) .$ 又当 $p {  } 0 , f ( \ l _ { P } ) {  } 1$ ，当$p {  } 0$ 时， $f ( p ) {  } 0$ ，从而 $f ( p )$ 无最小值.

# 【进阶提升】

题型一 二项分布有关最值问题

例1通过历次考试，任课老师发现学生容易在多选题中由于多选和错选致误，因此决定为自己所带的两个班级的学生命制一套满分为100分的多项选择题专题卷，已知这两个班共有学生100名，陈老师根据两个班学生的考试成绩制作了如下表所示的频率分布表：

<html><body><table><tr><td></td><td>分值[30,40）[40,50）[50,60）[60,70）[70,80]</td><td></td><td></td><td></td><td></td></tr><tr><td>频率</td><td>0.1</td><td>0.3</td><td>0.3</td><td>0.2</td><td>0.1</td></tr></table></body></html>

(1)若每个分组取中间值作代表，试求两个班学生的成绩的平均值；

(2)为了更好地激发学生学习的热情，陈老师决定组建兴趣小组，若采取分层抽样的方法从两个班中成绩为[50,60)和[60,70）的学生中抽取5人，再从中确定3人为小组组长，如果用 $X$ 表示小组组长来自成绩为[50,60)的学生的人数，求的分布列和数学期望;

(3)为了更好地了解学生多项选择题失分的原因，陈老师从两个班中随机抽取20名学生进行深人交流，若这20名学生中有 $k$ 名学生本次考试成绩在[30,50)之间的概率为 $P _ { k }$ （ $1 \leqslant$ $k { \leqslant } 2 0 , k \in { \bf { Z } } )$ ，求 $P _ { \ k }$ 取得最大值时 $k$ 的值(将频率视为概率).

变式1 为调查 $A , B$ 两种同类药物在临床应用中的疗效，药品监管部门收集了只服用药物A和只服用药物 $B$ 的患者的康复时间，经整理得到如下数据：

<html><body><table><tr><td>康复时间</td><td>只服用药物A</td><td>只服用药物房</td></tr><tr><td>7天内康复</td><td>360人</td><td>160人</td></tr><tr><td>8至14天康复</td><td>228人</td><td>200人</td></tr><tr><td>14天内未康复</td><td>12人</td><td>40人</td></tr></table></body></html>

假设用频率估计概率，且只服用药物A和只服用药物 $B$ 的患者是否康复相互独立

（1）若一名患者只服用药物A治疗，估计此人能在14天内康复的概率；

(2）从样本中只服用药物A和只服用药物 $B$ 的急者中各随机抽取1人，以 $X$ 表示这2人中能在7天内康复的人数， $X$ 的分布列和数学期望；

(3）从只服用药物A的患者中随机拍取100人，用 ${ } ^ {  } P _ { 1 0 0 } ( k ) { } ^ { \pi }$ 表示这100人中恰有 $k$ 人在14天内未康复的概率，其中 $k =$ $0 , 1 , 2 , \cdots , 1 0 0 .$ 当 $P _ { 1 0 0 } \left( k \right)$ 最大时，写出 $k$ 的值（只需写出结论）

# 题型二导数法求最值问题

例2学习强国中有两项竞赛答题活动，一项为“双人对战”，另一项为“四人赛”.活动规则如下：一天内参与“双人对战”活动，仅首局比赛可获得积分，获胜得2分，失败得1分；一天内参与“四人赛”活动，仅前两局比赛可获得积分，首局获胜得3分，次局获胜得2分，失败均得1分.已知李明参加“双人对战”活动时,每局比赛获胜的概率为 $\cdot \frac { 1 } { 2 }$ ；参加“四人赛”活动(每天两局)时,第一局和第二局比赛获胜

的概率分别为p 李明周一到周五每天都参  
加了“双人对战”活动和“四人赛”活动（每天  
两局），各局比赛互不影响（1）求李明这5天参加“双人对战”活动的  
总得分 $\pmb { X }$ 的分布列和数学期望；(2)设李明在这5天的“四人赛”活动（每  
天两局)中，恰有3天每天得分不低于3分的  
概率为 $f ( p )$ 求 $\pmb { P }$ 为何值时， $f ( p )$ 取得最大值

变式2 某县种植的脐橙果实按果径 $X$ （单位： $\mathrm { m m }$ ）的大小分级，其中 $X \in ( 7 0 , 9 0 ]$ 为一级果， $X \in ( 9 0 , 1 1 0 ]$ 为特级果，一级果与特级果统称为优品.现采摘了一大批此品种脐橙果实，从中随机抽取1000个测量果径，得到频率分布直方图如下：

![](images/24f0a8d81e79aa348cb0b703c63665213d22076eb40e83a292220fae7c59836e.jpg)

（1）由频率分布直方图可认为，该品种脐橙果实的果径 $X$ 服从正态分布 $N ( \mu ,$ $\sigma ^ { 2 }$ )，其中 $\mu$ 近似为样本平均数 ${ \overline { { x } } } , \sigma$ 近似为样本标准差 $s$ ，已知样本的方差的近似值为100.若从这批脐橙果实中任取一个，求取到的果实为优品的概率（同一组中的数据用该组区间的中点值代表）.

(2)这批采摘的脐橙按2个特级果和$n ( n \geqslant 2$ ，且 $\boldsymbol { n } \in \mathbf { N } ^ { * }$ )个一级果为一箱的规格进行包装，再经过质检方可进入市场.质检员质检时从每箱中随机取出两个果实进行检验，若取到的两个果实等级相同，则该箱脐橙记为“同”，否则该箱脐橙记为“异”

$\textcircled{1}$ 试用含 $n$ 的代数式表示抽检的某箱脐橙被记为“异”的概率 $p$ ；

$\textcircled{2}$ 设抽检的5箱脐橙中恰有3箱被记为“异”的概率为 $f ( p )$ ，求函数 $f ( p )$ 的最大值，及取最大值时 $n$ 的值.

参考数据：若随机变量 $X$ 服从正态分布 $N ( \mu , \sigma ^ { 2 } )$ ，则 $P \left( \mu - \sigma \leqslant X \leqslant \mu + \sigma \right) \approx$ $0 . 6 8 2 7 , P ( \mu - 2 \sigma \leqslant X \leqslant \mu + 2 \sigma ) \approx 0 . 9 5 4 5$ $P ( \mu - 3 \sigma { \leqslant } X { \leqslant } \mu + 3 \sigma ) \approx 0 . 9 9 7 \ 3 .$

# 题型三函数型最值问题

例3一种微生物群体可以经过自身繁殖不断生存下来，设一个这种微生物为第0代,经过一次繁殖后为第1代，再经过一次繁殖后为第2代该微生物每代繁殖的个数是相互独立的且有相同的分布列，设 $X$ 表示1个微生物个体繁殖下一代的个数， $P ( X = i ) = p _ { i } ( i =$ 0,1,2,3).

(1)已知 $p _ { 0 } = 0 . 4 , p _ { 1 } = 0 . 3 , p _ { 2 } = 0 . 2 , p _ { 3 } =$ 0.1,求 $E ( X )$ ;

(2）设 $p$ 表示该种微生物经过多代繁殖后临近灭绝的概率， $p$ 是关于 $x$ 的方程 $p _ { 0 } + p _ { 1 } x +$ $p _ { 2 } x ^ { 2 } + p _ { 3 } x ^ { 3 } = x$ 的一个最小正实根,求证：当$E ( X ) \leqslant 1$ 时， $p = 1$ ,当 $E ( X ) > 1$ 时， $p < 1$

(3)根据你的理解说明(2)问结论的实际 含义.

变式3 某地 $A , B , C , D$ 四个商场均销售同一型号的冰箱，经统计，10月份这四个商场购进和销售该型号冰箱的台数如下表（单位：十台）：

<html><body><table><tr><td>商场</td><td>A商场</td><td>B商场C商场D商场</td><td></td><td></td></tr><tr><td>购讲该型 冰箱数x</td><td>3</td><td>4</td><td>5</td><td>6</td></tr><tr><td>销售该型 冰箱数</td><td>2.5</td><td>3</td><td>4</td><td>4.5</td></tr></table></body></html>

（1）已知可用一元线性回归模型拟合$\mathcal { T }$ 与 $_ { x }$ 的关系，求 $\mathcal { I }$ 关于 $_ x$ 的经验回归方程$\hat { y } = \hat { b } x + \hat { a }$

（2）假设每台冰箱的售价均定为4000元若进入 $A$ 商场的甲、乙两位顾客购买这种冰箱的概率分别为 $p , 2 p - 1 \left( \frac { 1 } { 2 } < p < 1 \right)$ 且甲乙是否购买冰箱互不影响，若两人购买冰箱总金额的期望不超过6000元，求 $P$ 的取值范围.

参考公式：经验回归方程 $\hat { y } = \hat { b } x + \hat { a }$ 中斜率和截距的最小二乘估计公式分别为$\hat { b } \ = \ \frac { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } y _ { i } \ - n \overline { { x y } } } { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } ^ { 2 } \ - n \overline { { x } } ^ { 2 } } , \hat { a } \ = \ \overline { { y } } \ - \ \hat { b } \overline { { x } } .$

个此人，一不就，.0= (q.P.0= y ID5（1）（）

T头县q测 小最个—随x=“） 1<()3i1I=q.t01s(

# ·第7讲统计和统计案例··

# 【核心知识聚焦】

# 一、常见统计图表的特点与区别

1.扇形图：用于直观描述各类数据占总数的比例,易于显示每组数据相对于总数的大小

2.条形图：主要用于直观描述不同类别或分组数据的频数和频率，适用于描述离散型数据.

3.直方图：主要用于直观描述不同类别或分组数据的频数和频率，直方图适用于描述连续型数据.频率分布直方图的性质

(1)因为小矩形的面积 $\underline { { \underline { { \mathbf { \delta \pi } } } } }$ 组距 $\times \frac { \frac { 1 5 \sqrt { 7 } } { 7 } = 9 } { \frac { 1 } { 7 } \textcircled { 1 } \textcircled { 1 } } =$ 频劵率,所以各小矩形的面积表示相应各组的频率

这样，频率分布直方图就以面积的形式反映了数据落在各个小组内的频率大小.

(2)在频率分布直方图中，各小矩形的面积之和等于1.

频数 (3） 相应的频率 =样本容量.

4.折线图：主要用于描述数据随时间的变化趋势.

# 二、总体百分位数的估计

1.百分位数定义：一般地，一组数据的第 $p$ 百分位数是这样一个值，它使得这组数据中至少有 $p \%$ 的数据小于或等于这个值,且至少有$( 1 0 0 - p ) \%$ 的数据大于或等于这个值.

2.常用的百分位数

（1）四分位数：第25百分位数，第50百分位数,第75百分位数.

(2)其他常用的百分位数：第1百分位数，第5百分位数，第95百分位数，第99百分位数

3.计算一组 $\scriptstyle n$ 个数据的第 $P$ 百分位数的一般步骤如下：

第1步，按从小到大排列原始数据；

第2步，计算 $i = n \times p \%$

第3步，若i不是整数,而大于 $\Dot { \iota }$ 的比邻整数为 $j ,$ 则第 $P$ 百分位数为第 $j$ 项数据；

若 $_ i$ 是整数，则第 $P$ 百分位数为第 $_ i$ 项与第 $( i + 1 )$ 项数据的平均数.

# 三、总体(样本)的主要数字特征

1.众数：一组数据中出现次数最多的数.

2.中位数：把一组数据按从小到大（或从大到小)的顺序排列,处在中间位置的数(或中间两个数的平均数)叫做这组数据的中位数.

3.平均数：如果 $\boldsymbol { n }$ 个数 $x _ { 1 } , x _ { 2 } , \cdots , x _ { n }$ ,那么${ \overline { { x } } } = { \frac { 1 } { n } } { \big ( } x _ { 1 } + x _ { 2 } + \cdots + x _ { n } { \big ) }$ 叫做这组数的平均数.

4.假设一组数据为 $x _ { 1 } , x _ { 2 } , \cdots , x _ { n }$ ，则这组数据的平均数x=x1+x+….+xn ，方差为

$s ^ { 2 } = \frac { 1 } { n } \sum _ { i = 1 } ^ { n } \left( x _ { i } - \overrightarrow { x } \right) ^ { 2 }$ ，标准差 $s \quad =$ ${ \sqrt { { \frac { 1 } { n } } \sum _ { i = 1 } ^ { n } \left( x _ { i } - { \overline { { x } } } \right) ^ { 2 } } } .$

# 5.数字特征的主要应用

（1)平均数、中位数和众数等都是刻画"中心位置”的量，它们从不同角度刻画了一组数据的集中趋势.

(2)一般地,对数值型数据(如用水量、身高、收入、产量等)集中趋势的描述，可以用平均数、中位数;而对分类型数据（如校服规格、性别、产品质量等级等)集中趋势的描述，可以用众数.

(3)标准差刻画了数据的离散程度或波动幅度,标准差越大,数据的离散程度越大；标准差越小,数据的离散程度越小.

6.频率分布直方图中平均数、中位数、众数的求法

(1)样本平均数：可以用每个小矩形底边中点的横坐标与小矩形面积的乘积之和近似代替.

(2)在频率分布直方图中，中位数左边和右边的直方图的面积应相等.

(3)将最高小矩形所在的区间中点作为众数的估计值.

# 7.平均数与方差的常见性质

(1)若 $x _ { 1 } , x _ { 2 } , \cdots , x _ { n }$ 的平均数为 $_ x$ ，那么$m x _ { 1 } + a , m x _ { 2 } + a , \cdots , m x _ { n } + a$ 的平均数是 $m x +$ $^ { a }$ ;

(2)数据 $x _ { 1 } , x _ { 2 } , \cdots , x _ { n }$ 与数据 $x _ { 1 } + a , x _ { 2 } +$ $a , \cdots , x _ { n } + a$ 的方差相等；

(3)若 $x _ { 1 } , x _ { 2 } , \cdots , x _ { n }$ 的方差为 $s ^ { 2 }$ 那么 $\mathbf { \alpha } _ { a x _ { 1 } }$ ，$a x _ { 2 } , \cdots , a x _ { n }$ 的方差为 $a ^ { 2 } s ^ { 2 }$

# 四、分类变量与列联表

# 1.分类变量

为了方便,会使用一种特殊的随机变量，区别不同的现象或性质，这随机变量称为分类变量.

$2 . 2 \times 2$ 列联表

$\textcircled { 1 } 2 \times 2$ 列联表给出了成对分类变量数据的交叉分类频数.$\textcircled{2}$ 定义一对分类变量 $X$ 和 $Y$ ,我们整理数据如下表所示：

<html><body><table><tr><td rowspan="2">X</td><td colspan="2">Y</td><td rowspan="2">合计</td></tr><tr><td>Y=0</td><td>Y=1</td></tr><tr><td>X=0</td><td>a</td><td>b</td><td>a+b</td></tr><tr><td>X=1</td><td>C</td><td>d</td><td>c+d</td></tr><tr><td>合计</td><td>a+c</td><td>b+d</td><td>n=a+b+c+d</td></tr></table></body></html>

# 五、独立性检验

1.独立性检验定义：

利用 $\chi ^ { 2 }$ 的取值推断分类变量 $X$ 和 $Y$ 是否独立的方法称为 $\chi ^ { 2 }$ 独立性检验,读作“卡方独立性检验”，简称独立性检验.

2.独立性检验公式：

$\chi ^ { 2 } = \frac { n \left( a d - b c \right) ^ { 2 } } { \left( a + b \right) \left( c + d \right) \left( a + c \right) \left( b + d \right) }$ $n = a + b + c + d$ （注意使用公式时分子的平方不要忽略了).

# 【进阶提升】

# 题型一 样本数字特征

例1已知某样本的容量为50，平均数为70,方差为75.现发现在收集这些数据时，其中的两个数据记录有误，一个错将80记录为60，另一个错将70记录为90.在对错误的数据进行更正后，重新求得样本的平均数为 $\overline { { x } }$ ，方差为$s ^ { 2 }$ ，则（）

$$
\begin{array} { r l } { \mathrm { A . ~ } \overline { { x } } = 7 0 \mathrm { , } s ^ { 2 } < 7 5 \quad } & { { } \mathrm { B . ~ } \overline { { x } } = 7 0 \mathrm { , } s ^ { 2 } > 7 5 } \\ { \mathrm { C . ~ } \overline { { x } } > 7 0 \mathrm { , } s ^ { 2 } < 7 5 \quad } & { { } \mathrm { D . ~ } \overline { { x } } < 7 0 \mathrm { , } s ^ { 2 } > 7 5 } \end{array}
$$

变式1（多选题）下列统计量中，能度量样本 $x _ { 1 } , x _ { 2 } , \cdots , x _ { n }$ 的离散程度的是（）

A.样本 $x _ { 1 } , x _ { 2 } , \cdots , x _ { n }$ 的标准差B.样本 $x _ { 1 } , x _ { 2 } , \cdots , x _ { n }$ 的中位数

C.样本 $x _ { 1 } , x _ { 2 } , \cdots , x _ { n }$ 的极差D.样本 $x _ { 1 } , x _ { 2 } , \cdots , x _ { n }$ 的平均数

变式2（多选题）下列说法中正确的是（）

A.一组从小到大排列的数据0，1，3，  
$^ { 4 , 6 , 7 , 9 , x , 1 1 , 1 1 }$ ，去掉 $x$ 与不去掉 $x$ ，它  
们的 $80 \%$ 分位数都不变，则 $x = 1 1$ B.两组数据 $x _ { 1 } , x _ { 2 } , x _ { 3 } , \cdots , x _ { m }$ 与 $\boldsymbol { y } _ { 1 } , \boldsymbol { y } _ { 2 }$ ，  
$y _ { 3 } , \cdots , y _ { n }$ ，设它们的平均值分别为 $E _ { x }$ 与  
$E _ { y }$ ，将它们合并在一起，则总体的平均值为  
${ \frac { m } { m + n } } E _ { x } + { \frac { n } { m + n } } E ,$ C.已知离散型随机变量 $X \sim B \vert 8 , \frac { 1 } { 4 } \vert$   
则 $D ( 2 X + 3 ) = 3$ D.一元线性回归模型中，相关系数 $r$   
的值越大，则这两个变量线性相关性越强

# 题型二直方图相关问题

例2近年来,我国加速推行垃圾分类制度,全国垃圾分类工作取得积极进展.某城市推出了两套方案，并分别在 $A , B$ 两个大型居民小区内试行.方案一：进行广泛的宣传活动，通过设立宣传点、发放宣传单等方式，向小区居民和社会各界宣传垃圾分类的意义，讲解分类垃圾桶的使用方式，垃圾投放时间等,定期召开垃圾分类会议和知识宣传教育活动；方案二：智能化垃圾分类,在小区内分别设立分类垃圾桶，垃圾回收前端分类智能化,智能垃圾桶操作简单，居民可以通过设备进行自动登录、自动称重、自动积分等一系列操作.建立垃圾分类激励机制,比如,垃圾分类换积分,积分可兑换礼品等，激发了居民参与垃圾分类的热情,带动居民积极主动地参与垃圾分类.经过一段时间试行之后，在这两个小区内各随机抽取了100名居民进行问卷调查，记录他们对试行方案的满意度得分（满分100分），将数据分成6组：[40,50），[50,60），[60,70），[70,80),[80,90）,[90,100],并整理得到如下频率分布直方图：

![](images/e611a5d0fb98bed6cde97ada315537b9638d266db41b96af8be626b5efc204f9.jpg)

(1)通过频率分布直方图分别估计两种方案满意度的平均得分，判断哪种方案的垃圾分类推广措施更受居民欢迎（同一组中的数据用该组中间的中点值作代表)；(2)估计A小区满意度得分的第80百分位数；(3)以样本频率估计概率,若满意度得分不低于70分说明居民赞成推行此方案，低于70分说明居民不赞成推行此方案.现从 $B$ 小区内随机抽取5个人，用 $X$ 表示赞成该小区推行方案的人数，求 $X$ 的分布列及数学期望.

变式3 某校组织学生参加100米短跑训练，在某次短跑测试中，抽取100名女生作为样本，统计她们的成绩（单位：秒），整理得到如图所示的频率分布直方图.

![](images/24a780131c88bd42659a4aa4b335fa9814bc00ea83d06c8318c98ddbb6503a27.jpg)

(1）估计样本中女生短跑成绩的平均数；（同一组的数据用该组区间的中点值为代表）

(2)由频率分布直方图，可以认为该校女生的短跑成绩 $X$ 服从正态分布 $N ( \mu ,$ $\sigma ^ { 2 }$ )，其中 $\mu$ 近似为女生短跑平均成绩 $\mathbf { \Pi } _ { x } ^ { - }$ ，$\sigma ^ { 2 }$ 近似为样本方差 $s ^ { 2 }$ ，经计算得， $s ^ { 2 } = { }$ 6.92，若从该校女生中随机抽取10人，记其中短跑成绩在[12.14，22.66]以外的人数为Y，求 $P ( \ Y \geqslant 1 )$

附参考数据： ${ \sqrt { 6 . 9 2 } } \approx 2 . 6 3$ ，随机变量$\chi$ 服从正态分布 $N ( \mu , \sigma ^ { 2 } )$ ，则 $P ( \mu - \sigma \leqslant$ $X { \leqslant } \mu + \sigma ) \approx 0 . 6 8 2 \ 7 , P ( \mu - 2 \sigma \leqslant X \leqslant \mu +$ $2 \sigma ) \approx 0 . 9 5 4 \ 5 \ : , P ( \mu - 3 \sigma \leqslant X \leqslant \mu + 3 \sigma ) \approx$ $0 . 9 9 7 \ 3 \ , 0 . 6 8 2 \ 7 ^ { 1 0 } \approx 0 . 0 2 2 \ 0 \ , 0 . 9 5 4 \ 5 ^ { 1 0 } \approx$ $0 . 6 2 7 7 , 0 . 9 9 7 3 ^ { 1 0 } \approx 0 . 9 7 4 3$

小

# 题型三 正态分布相关问题

例3脂肪含量(单位 $\%$ )指的是脂肪重量占人体总重量的比例.某运动生理学家在对某项健身活动参与人群的脂肪含量调查中，采用样本量比例分配的分层随机抽样，如果不知道样本数据，只知道抽取了男性120位，其平均数和方差分别为14和6,抽取了女性90位，其平均数和方差分别为21和17.

(1)试由这些数据计算出总样本的均值与方差，并对该项健身活动的全体参与者的脂肪含量的均值与方差作出估计.(结果保留整数)

(2)假设全体参与者的脂肪含量为随机变量 $X$ ，且 $X \sim N ( 1 7 , \sigma ^ { 2 } )$ ,其中 $\sigma ^ { 2 }$ 近似为(1)中计算的总样本方差.现从全体参与者中随机抽取3位，求3位参与者的脂肪含量均小于$1 2 . 2 \%$ 的概率.

附：若随机变量 $\times$ 服从正态分布 $N \left( \mu , \right.$ $\sigma ^ { 2 }$ )，则 $P ( \mu - \sigma { \leqslant } X { \leqslant } \mu + \sigma ) \approx 0 . 6 8 2 7 , P ( \mu -$ $2 \sigma \leqslant X \leqslant \mu + 2 \sigma ) \approx 0 . 9 5 4 \ 5$ ， ${ \sqrt { 2 2 } } \approx 4 . 7$ $\sqrt { 2 3 } \approx$ $4 . 8 , 0 . 1 5 8 ~ 6 5 ^ { 3 } \approx 0 . 0 0 4$

变式A 左花米草是禾本科草本植物，具根系发达，具有极高的繁殖系致，对近海生态具有蚊大的危富，某研究小组为了解甲、乙两镇的互花米草根系分布深度情况，采用按比例分层抽样的方法抽取样本，已如甲镇的样本容量 $m = 1 2$ ，样本平均致 $\bar { x } =$ 18.样本方差 $S _ { 1 } ^ { 2 } = 1 9$ ；乙镇的料本容量 $\pi =$ 18，样本平均效 $y = 3 6$ ，样本方差 $S _ { 2 } ^ { \itOmega } = 7 0$

（1）尔由两镇样本组成的总样本的平均数 $\hat { z }$ 及其方差 $S ^ { 2 }$

（2）为营造”广泛发动、全民参与”约浓厚氛围，甲、乙两镇决定进行一次“互花米草除泊大练兵”比赛，两镇各派一交代表队参加，经抽签确定第一场在甲镇举行，比赛规则：

每场比赛直至分出胜负为止，胜方得1分，负方得0分，下一场在负方所在镇举行，先得2分的代表队获胜，比赛结束

当比赛在 $\boldsymbol { \Psi }$ 镇举行时，甲镇代表队获胜的概率为 $\frac { 3 } { 5 }$ 当比赛在乙镇举行时，甲镇代表队孩胜的概率为 ${ \frac { 1 } { 2 } } .$ 假设每场比赛结果相亚独立.甲镇代表队的政终得分记为$\chi$ $E ( \boldsymbol { X } )$ 、

参考敬据： $1 2 \times 1 8 ^ { 2 } = 3 8 8 8 , 1 8 \times 3 6 ^ { 2 } =$ $2 3 3 2 8 , 2 8 . 8 ^ { 2 } = 8 2 9 . 4 4 , 1 2 \times 1 0 . 8 ^ { 2 }$ 1 399.68,18 ×7.2² =933.12.

# 题型四独立性检验相关问题

例4人类探索浩瀚太空的步伐从未停止，假设在未来，人类拥有了两个大型空回站，命名为“领航者号”和“非凡者号”，其中“领航者号”空间站上配有2艘“M2运输船”和1艘“T1转移塔”，“非凡者号”空间站上配有3艘T1转移塔”.现在进行两艘飞行器间的“交会对接”.假设“交会对接”在M年中重复了 $\boldsymbol { n }$ 次，现在一名航天员乘坐火箭登上这两个空间站中的一个检查“领航者号”剩余飞行器情况，记“领航者号”剩余2艘“M2运输船”的概率为 $\boldsymbol { P } _ { n }$ ，剩余1艘“M2运输船”的概率为 $q _ { \epsilon ^ { \prime } }$ 其中宇航员的性别与选择所登录空间站的情况如下表所示.

<html><body><table><tr><td>性别</td><td>男性宇航员</td><td>女性宇航员</td></tr><tr><td>&quot;领航者号&quot;空间站</td><td>380</td><td>220</td></tr><tr><td>“非凡者号&quot;空间站</td><td>120</td><td>280</td></tr></table></body></html>

<html><body><table><tr><td>a</td><td>0.05</td><td>0.01</td><td>0.005</td><td>0.001</td></tr><tr><td></td><td>3.841</td><td>6.635</td><td>7.879</td><td>10.828</td></tr></table></body></html>

$\chi ^ { 2 } = \frac { n \left( a d - b c \right) ^ { 2 } } { \left( a + b \right) \left( c + d \right) \left( a + c \right) \left( b + d \right) } , n =$ $a + b + c + d ,$

(1)根据小概率值 $\alpha = 0 , 0 0 1$ 的独立性检验，能否认为选择登录空间站的情况与性别相关联；

(2)若 $k$ 为函数 $f ( x ) = { \frac { x } { \ln x } } ;$ 极大值的 $\frac { 2 } { e }$ 倍，求 $k _ { \ell ^ { \prime } _ { \eta } } + q _ { \eta }$ 与 $k p _ { n - 1 } + q _ { n - 1 }$ 的递推关系式；

(3)求 $X _ { \mathfrak { n } }$ 的分布列与数学期望 $E ( X _ { n } )$

变式5 篮球和排球是我校学生最为喜爱的两项运动，为调查喜爱运动项目与性别之间的关系，某调研组在校内随机采访男生、女生各50人，每人必须从篮球和排球中选择最喜爱的一项，其中喜爱排球的归为甲组，喜爱篮球的归为乙组，调查发现甲组成员48人，其中男生18人。

（1）根据以上数据，填下述 $2 \times 2$ 列联表：

<html><body><table><tr><td>性别</td><td>甲组</td><td>乙组</td><td>合计</td></tr><tr><td>男生</td><td></td><td></td><td></td></tr><tr><td>女生</td><td></td><td></td><td></td></tr><tr><td>合计</td><td></td><td></td><td></td></tr></table></body></html>

(2）根据小概率值 $\alpha = 0 . 0 5$ 的独立性检验，能否认为学生喜欢排球还是篮球与性别有关？

(3）现从调查的女生中按分层随机抽样的方法选出5人组成一个小组，抽取的5人中再随机抽取3人发放礼品，求这3人中在甲组中的人数 $X$ 的分布列及其数学期望

参考公式 ${ \bf ; } \chi ^ { 2 } = \frac { n ~ ( a d - b c ) ^ { 2 } } { ( a + b ) ( c + d ) ( a + c ) ( b + d ) } ,$ 其中 $n = a + b + c + d$

参考数据：

<html><body><table><tr><td>α</td><td>0.1</td><td>0.05</td><td>0.01</td></tr><tr><td>xa</td><td>2.706</td><td>3.841</td><td>6.635</td></tr></table></body></html>

# 【核心知识聚焦】

# 一、相关关系

# 1.样本相关系数

由于度量对象和单位的不同等,数值会有大有小,为了去除这些因素的影响,统计学里$\begin{array} { r l } { - \frac { \hbar { \mathfrak H } } { \hbar \mathfrak L } \mathbb H \ r } & { = \frac { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - { \overline { x } } \right) \left( y _ { i } - { \overline { y } } \right) } { \displaystyle \sqrt { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - { \overline { x } } \right) ^ { 2 } } \sqrt { \displaystyle \sum _ { i = 1 } ^ { n } \left( y _ { i } - { \overline { y } } \right) ^ { 2 } } } } \\ & { = \frac { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } y _ { i } - n { \overline { x } } \ y } { \displaystyle \sqrt { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } ^ { 2 } - n { \overline { x } } ^ { 2 } } \sqrt { \displaystyle \sum _ { i = 1 } ^ { n } y _ { i } ^ { 2 } - n { \overline { y } } ^ { 2 } } } \ { \mathcal K } \oplus \mathbb H \ y } \end{array}$ 与 $_ x$ 的线性相关性强弱,我们称 $r$ 为变量 $_ x$ 和变量 $y$ 的样本相关系数.

2.样本相关系数 $r$ 的性质

$\textcircled{1}$ 当 $r > 0$ 时,称成对样本数据正相关;当$r < 0$ 时，成对样本数据负相关；当 $r = 0$ 时，成对样本数据间没有线性相关关系，不排除它们之间有其他相关关系.

$\textcircled{2}$ 样本相关系数 $r$ 的取值范围为[-1,1],当 $\mid r \mid$ 越接近1时，成对样本数据的线性相关程度越强；当 $\mid r \mid$ 越接近0时，成对样本数据的线性相关程度越弱.

# 二、一元线性回归模型参数的最小二乘法

(1)经验回归直线过样本点的中心 $( \overline { { x } } , \overline { { y } } )$ ，是经验回归方程最常用的一个特征;我们将$\hat { \boldsymbol { y } } = \hat { b } \boldsymbol { x } + \hat { a }$ 称为 $Y$ 关于 $_ x$ 的经验回归方程,也称经验回归函数或经验回归公式,其图形称为经验回归直线.这种求经验回归方程的方法叫做最小二乘法,求得的 $\hat { b } , \hat { a }$ ,叫做 $b , a$ 的最小二乘估计,其中 $\hat { b }$ 称为回归系数,它实际上也就是经验回归直线的斜率， $\hat { a }$ 为截距.

$$
\left\{ \begin{array} { l l } { \displaystyle { \hat { b } } = \frac { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - \overline { { x } } \right) \left( y _ { i } - \overline { { y } } \right) } { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - \overline { { x } } \right) ^ { 2 } } = \frac { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } y _ { i } - n \overline { { x y } } } { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } ^ { 2 } - n \overline { { x } } ^ { 2 } } , } \\ { \displaystyle { \hat { a } } = \overline { { y } } - \hat { b } \overline { { x } } . } \end{array} \right.
$$

# 三、残差和残差分析

对于响应变量Y,通过观测得到的数据称为观测值，通过经验回归方程得到的 $\hat { y }$ 称为预测值，观测值减去预测值称为残差.残差是随机误差的估计结果，通过残差分析可以判断模型刻画数据的效果,以及原始数据中是否存在可疑数据等，这方面工作称为残差分析.一元线性回归模型中对随机误差假定，残差是均值为0、方差是 $\sigma ^ { 2 }$ 的随机变量的观测值.

# 四、决定系数 $R ^ { 2 }$

1.残差平方和：残差平方和$\sum _ { i = 1 } ^ { n } { { { \left( { { y } _ { i } } - { \hat { y } } _ { i } \right) } ^ { 2 } } }$ ,残差平方和越小,模型拟合效果越好,残差平方和越大,模型拟合效果越差.

2.决定系数 $R ^ { 2 }$ ：决定系数 $R$ 是度量模型拟合效果的一种指标,在线性模型中，它代表解释变量客户预报变量的能力.

$R ^ { 2 } = 1 - \frac { \displaystyle \sum _ { i = 1 } ^ { n } ~ ( y _ { i } - \hat { y } _ { i } ) ^ { 2 } } { \displaystyle \sum _ { i = 1 } ^ { n } ~ ( y _ { i } - \overline { { y } } ) ^ { 2 } } , R ^ { 2 }$ 越大，即拟合效果越好， $R ^ { 2 }$ 越小，模型拟合效果越差.

# 【进阶提升】

# 题型一 求回归方程与拟合效果对比

例1某研究所为了研究某种昆虫的产卵数 $\boldsymbol { y }$ 与温度 $_ { x }$ （单位： $\mathfrak { C }$ )之间的关系，现将收集到的温度 $\boldsymbol { x } _ { i }$ 和一组昆虫的产卵数 $y _ { i } ( i = 1 , 2$ ,6)的6组观测数据作了初步处理,得到如图的散点图及一些统计数据.

08070504302000 产卵数 5101520253035 温度/℃

经计算得到以下数据 $\langle { \overline { { x } } } = { \frac { 1 } { 6 } } \sum _ { i = 1 } ^ { 6 } x _ { i } = 2 6$ ，$\overline { { { y } } } = \frac { 1 } { 6 } \sum _ { i \times 1 } ^ { 6 } { y _ { i } } = 3 3 , \sum _ { i \times 1 } ^ { 6 } \left( x _ { i } - \overline { { { x } } } \right) \left( y _ { i } - \overline { { { y } } } \right) = 5 5 7 ,$ $\begin{array} { r c l } { { \displaystyle \sum _ { i = 1 } ^ { 6 } \left( x _ { i } - \widehat { x } \right) ^ { 2 } ~ = ~ 8 4 , ~ \sum _ { i = 1 } ^ { 6 } ~ ( y _ { i } - \widehat { y } ) ^ { 2 } ~ = ~ 3 9 3 0 , } }  \\ { { \displaystyle \sum _ { i = 1 } ^ { 6 } \left( y _ { i } - \widehat { y } _ { i } \right) ^ { 2 } ~ = 2 3 6 , 6 4 , } } \end{array}$

(1）若用一元线性回归模型来拟合数据的  
变化关系，求 $\mathcal { I }$ 关于 $\mathscr { x }$ 的经验回归方程 $\hat { \boldsymbol { y } } =$   
$\hat { b } x + \hat { a }$ （结果精确到0.1）；(2）若用非线性回归模型来拟合数据的变  
化关系，求得 $\mathscr { I }$ 关于 $\mathscr { X }$ 的经验回归方程 $\hat { y } \approx$   
$0 . 0 6 \ell ^ { \mu , \mathcal { W } \mathcal { R } }$ ，且相关指数为 $\scriptstyle R ^ { 2 } = 0 , 9 6 7 2 ,$ $\textcircled{1}$ 试与(1）中的回归模型相比，用 $R ^ { 2 }$ 说明  
哪种模型的拟合效果更好；$\textcircled{2}$ 用拟合效果好的模型预测温度为 $3 5 \ \mathrm { ‰ }$

时该组昆虫的产卵数(结果取整数).

附参考公式：对于一组具有线性相关关系的数据 $\left( x _ { 1 } , y _ { 1 } \right) , \left( x _ { 2 } , y _ { 2 } \right) , \cdots , \left( x _ { n } , y _ { n } \right)$ ,其经验回归直线 $\hat { \boldsymbol { y } } = \boldsymbol { b } \boldsymbol { x } + \hat { \boldsymbol { a } }$ 截距和斜率的最小二乘估计公式分别为： $\hat { b } \ = \ \frac { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - \overline { { x } } \right) \left( y _ { i } - \overline { { y } } \right) } { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - \overline { { x } } \right) ^ { 2 } } , \hat { a } \ =$ $\bar { y } - \hat { b } x$ ，决定系数 $; R ^ { 2 } = 1 - \frac { \displaystyle \sum _ { i = 1 } ^ { n } ~ ( y _ { i } - \hat { y } _ { i } ) ^ { 2 } } { \displaystyle \sum _ { i = 1 } ^ { n } ~ ( y _ { i } - \bar { y } ) ^ { 2 } } .$ 参考数据： $\mathbf { e } ^ { 8 . 0 6 0 5 } \approx 3 1 6 7$ ，

变式1 根据一组样本数据 $\left( \boldsymbol { x } _ { 1 } , \boldsymbol { y } _ { 1 } \right)$ $( x _ { 2 } , y _ { 2 } ) , \cdots , ( x _ { n } , y _ { n } )$ ，求得经验回归方程为 $\hat { y } = 1 , 5 x + 0 , 5$ ，且 $\bar { x } \equiv 3$ ，现发现这组样本数据中有两个样本点（1.2，2.2）和（4.8，7.8）误差较大，去除后重新求得的经验回归直线 $\mathbf { \xi } _ { l }$ 的斜率为1.2，则（ ）

A.变量 $x$ 与 $y$ 具有正相关关系B.去除两个误差胶大的样本点后，重新求得的经验回归方程为 $\hat { y } = 1 , 2 x + 0 , 5$ C.去除两个误差较大的样本点后，y的估计值增加速度变快D.去除两个误差较大的样本点后，相应于样本点（2，3.75）的残差为0.05

# 题型二求经验回归方程与概率综合问题

例2某校高二年级发起了“发扬奥林匹克精神，锻炼健康体魄”的年度主题活动，经过一段时间后，学生的身体素质明显提高.

(1)为了解活动效果，该年级对开展活动以来近6个月体重超重的人数进行了调查，调查结果统计如下图，根据下面的散点图可以认为散点集中在曲线 $\boldsymbol { y } = \mathrm { e } ^ { \boldsymbol { { \mathit { \mathit { \omega } } } } ^ { \mathit { \mathit { | { \varepsilon } } \times { + } \boldsymbol { { \mathit { \varepsilon } } } } } } $ 的附近,请根据下表中的数据求出该年级体重超重人数 $\boldsymbol { y }$ 与月份 $_ { x }$ 之间的经验回归方程（系数 $a$ 和 $b$ 的最终结果精确到0.01），并预测从开展活动以来第几个月份开始该年级体重超标的人数降至10人以下？

y体重超重人数  
120  
100  
30640200 1 234567x月份

<html><body><table><tr><td>月份x</td><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td><td>0</td></tr><tr><td>体重超标人数y98</td><td></td><td>77</td><td>54</td><td>48</td><td>32</td><td>27</td></tr><tr><td>g=lny</td><td>4.584.373.983.873.463.29</td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

(2)在某次足球训练课上，球首先由A队员控制，此后足球仅在 $A , B , C$ 三名队员之间传递，假设每名队员控球时传给其他队员的概率如下表所示：

<html><body><table><tr><td>控球队员</td><td colspan="2">A</td><td colspan="2">B</td><td colspan="2">C</td></tr><tr><td>接球队员</td><td>B</td><td>C</td><td>4</td><td>C</td><td>AB</td><td></td></tr><tr><td>概率</td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

若传球3次，记 $B$ 队员控球次数为 $\chi$ ，求 $\chi$ 的分布列及均值

附：经验回归方程 $\hat { \mathbf { \xi } } _ { : } \hat { \mathbf { y } } ~ = ~ \hat { b } { \boldsymbol { x } } ~ + ~ \hat { a } $ 中， $\hat { b } \ =$ $\frac { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } y _ { i } - n \overline { { x } } \overline { { y } } } { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } ^ { 2 } - n \overline { { x } } ^ { 2 } } , \widehat { a } ~ = \overline { { y } } - \widehat { b } \overline { { x } } ;$ 参考数据： $\sum _ { i \ : \mid s \mid } ^ { 6 } z _ { i } = 2 3 , 5 2 , \sum _ { i \ : \mid s \mid } ^ { 6 } x _ { i } z _ { i } =$ $7 7 . 7 2 , \sum _ { i = 1 } ^ { 6 } \ x _ { i } ^ { 2 } \ = 9 1 , \ln 1 0 \approx 2 . 3 0 .$

（1）是否可用一元线性回归模型拟合$y$ 与 $x$ 的关系？请用样本相关系数 $r$ 加以说明；（当 $0 . 7 5 \leqslant \ | \ r | \leqslant 1$ 时，那么变量 $x , y$ 有较强的线性相关关系）(2)建立 $\boldsymbol { \mathscr { y } }$ 关于 $_ x$ 的经验回归方程 $\hat { y } =$ $\hat { b } x + \hat { a }$ （结果保留1位小数），并预测该商场12月份的收入情况.（结果保留整数）附 $\begin{array} { r l r } { \mathit { \widehat { b } } } & { { } = } & { \frac { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - { \overline { { x } } } \right) \left( y _ { i } - { \overline { { y } } } \right) } { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - { \overline { { x } } } \right) ^ { 2 } } } \end{array}$ $\begin{array} { l } { \displaystyle \sum _ { i = 1 } ^ { n } { x _ { i } y _ { i } } - n \overline { { x y } } } \\ { \displaystyle \sum _ { i = 1 } ^ { n } { x _ { i } } ^ { 2 } - n \overline { { x } } ^ { 2 } } \end{array} , \hat { a } = \overline { { y } } - \hat { b } \overline { { x } } .$

变式2 实施新规后，某商场1月份至10月份的收入情况如表.

并计算得 $\sum _ { i = 1 } ^ { 1 0 } \ x _ { i } y _ { i } \ = 8 9 0 , \sum _ { i = 1 } ^ { 1 0 } \ x _ { i } ^ { 2 } \ = 3 8 5$ $\sum _ { i = 1 } ^ { 1 0 } \ y _ { i } \ = \ 1 5 0 , \sqrt { \sum _ { i = 1 } ^ { 1 0 } \ ( x _ { i } - \overline { { x } } ) ^ { 2 } \sum _ { i = 1 } ^ { 1 0 } \ ( y _ { i } - \overline { { y } } ) ^ { 2 } }$ $\approx 7 5 . 9 9$

<html><body><table><tr><td>月份x12345678910</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>收入Y10121513161715161620 /万元</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

![](images/6c96dd4080eac18b18320cbe7b8c8d22d0f9443b3fb719b9be757777b7c898a5.jpg)

$$
\begin{array} { c } { { \displaystyle \beta \# \mathcal { H } _ { \mathbb { H } : \mathbb { H } : \mathbb { H } } ^ { i \mathbb { Z } } \hat { \cdot } \hat { b } = \displaystyle \frac { \sum _ { i = 1 } ^ { n } x _ { i } \mathcal { T } _ { i } - n \overline { { x } } \overline { { y } } } { \sum _ { i = 1 } ^ { n } x _ { i } ^ { 2 } - n \overline { { x } } ^ { 2 } } , \hat { a } = \overline { { y } } - \hat { b } \cdot \overline { { x } } , } } \\ { { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } ^ { 2 } - n \overline { { x } } ^ { 2 } } } \\ { { \overline { { x } } = 2 4 0 , \displaystyle \sum _ { i = 1 } ^ { 5 } x _ { i } ^ { 2 } = 3 6 5 0 0 0 , \displaystyle \sum _ { i = 1 } ^ { 5 } x _ { i } \mathcal { T } _ { i } = 4 5 7 . 5 , \overline { { z } } \approx } } \\ { { 5 . 3 5 , \overline { { z } } ^ { 2 } \approx 2 8 . 5 7 , \displaystyle \sum _ { i = 1 } ^ { 5 } z _ { i } ^ { 2 } \approx 1 4 4 . 2 4 , \displaystyle \sum _ { i = 1 } ^ { 5 } z _ { i } \mathcal { T } _ { i } \approx } } \\ { { 1 2 . 7 2 , e ^ { 5 } \approx 1 5 0 , e ^ { 5 4 } \approx 2 2 0 . } } \end{array}
$$

# 题型三求非线性经验回归方程

例3某创业者计划在某旅游景区附近租赁一套农房发展成特色“农家乐”，为了确定未来发展方向，此创业者对该景区附近五家“农家乐”跟踪调查了100天,这五家“农家乐”的收费标准互不相同，得到的统计数据如下表， $_ x$ 为收费标准（单位：元/日），t为人住天数（单位：天)，以频率作为各自的“人住率”，收费标准 $_ x$ 与“入住率” $y$ 的散点图如图.

<html><body><table><tr><td>x/（元/日）</td><td>100</td><td>150</td><td>200</td><td>300</td><td>450</td></tr><tr><td>/天</td><td>90</td><td>65</td><td>45</td><td>30</td><td>20</td></tr></table></body></html>

![](images/cff489ed3639dc535bac17119916a120fcc882676e66074590401d6f88d8746d.jpg)

(1)若从以上五家“农家乐”中随机抽取两家深入调查，记 $\xi$ 为“人住率"超过0.6的农家乐的个数，求 $\xi$ 的分布列；

(2)令 $z = \ln x$ ，由散点图判断 $\hat { y } = \hat { b } x + a$ 与$\hat { \boldsymbol { y } } = \hat { \boldsymbol { b } } \boldsymbol { z } + \boldsymbol { a }$ 哪个更合适于此模型(给出判断即可,不必说明理由)？并根据你的判断结果求经验回归方程；( $\hat { a } , \hat { b }$ 的结果精确到0.1)

(3)根据第(2)问所求的经验回归方程，试估计收费标准为多少时，100天销售额 $L$ 最大？

变式3 五一期间，文旅部门推出了多款套票文旅产品，得到消费者的积极回应.下面是文旅部门在某地区推出六款不同价位的旅游套票，每款的套票价格 $_ x$ （单位：元）与购买人数y（单位：万人）的数据如下表：

<html><body><table><tr><td>旅游类别</td><td>城市展 馆科技游</td><td>乡村 特色游</td><td>齐鲁 红色游</td></tr><tr><td>套票价格 x/元</td><td>39</td><td>49</td><td>58</td></tr><tr><td>购买数量 y/万人</td><td>16.7</td><td>18.7</td><td>20.6</td></tr><tr><td>旅游类别</td><td>登山 套票</td><td>游园 套票</td><td>观海 套票</td></tr><tr><td>套票价格 x/元</td><td>67</td><td>77</td><td>86</td></tr><tr><td>购买数量 y/万人</td><td>22.5</td><td>24.1</td><td>25.6</td></tr></table></body></html>

在分析数据、描点绘图中，发现散点$( v _ { i } , \omega _ { i } )$ C $1 \leqslant i \leqslant 6$ ）集中在一条直线附近，其中 $v _ { i } = \ln x _ { i } , \omega _ { i } = \ln y _ { i }$

(1）根据所给数据，求 $\mathcal { Y }$ 关于 $_ { \textrm { \tiny 3 } }$ 的经验回归方程；

(2）按照文旅部门的指标测定，当购买数量y与套票价格 $_ x$ 的比在区间$\left[ \frac { \mathrm { ~ e ~ } } { 9 } , \frac { \mathrm { ~ e ~ } } { 7 } \right]$ 上时,该套票受消费者的欢迎程度更高，可以被认定为“热门套票”，现有三位同学从以上六款旅游套票中，购买不同的三款各自旅游.记三人中购买“热门套票”的人数为 $X$ ，求随机变量 $X$ 的分布列和期望

附 ${ \uparrow } : \textcircled { 1 } \sum _ { i = 1 } ^ { 6 } v _ { i } \omega _ { i } = 7 5 . 3 , \sum _ { i = 1 } ^ { 6 } v _ { i } = 2 4 . 6 ,$ $\sum _ { i = 1 } ^ { 6 } \omega _ { i } = 1 8 . 3 , \sum _ { i = 1 } ^ { 6 } v _ { i } ^ { 2 } = 1 0 1 . 4 .$

$\textcircled{2}$ 对于一组数据 $\left( \upsilon _ { 1 } , \omega _ { 1 } \right) , \left( \upsilon _ { 2 } , \omega _ { 2 } \right)$ ，$\cdots , ( v _ { n } , \omega _ { n } ^ { } )$ ，其经验回归直线 $\boldsymbol { \hat { \omega } } = \boldsymbol { \hat { b } } \boldsymbol { v } + \boldsymbol { \hat { a } }$ 的斜率和截距的最小二乘估计值分别为$\begin{array} { r } { \widehat { b } = \frac { displaystyle \sum _ { i = 1 } ^ { n } { { \upsilon } _ { i } } { \omega } _ { i } - n \overline { { { \upsilon } { \omega } } } } { \displaystyle \sum _ { i = 1 } ^ { n } { { \upsilon } _ { i } ^ { 2 } } - n \overline { { { \upsilon } ^ { 2 } } } } , \widehat { a } = \overline { { \omega } } - \widehat { b } \overline { { v } } . } \end{array}$

![](images/50fc2c1da9cbc0566dcd52e9d3ef07c12c81e446264881af0521f2ba65212d45.jpg)

题型四非线性回归方程的实际应用

例4在某生态系统中,有甲、乙两个种群,两种群之间为竞争关系.设 $t$ 时刻甲、乙种群的数量分别为 $f ( \mathbf { \Psi } _ { t } ) \mathbf { \Psi } , \mathbf { \Psi } _ { g } ( \mathbf { \Psi } _ { t } )$ （起始时刻为 $t =$ 0).由数学家Lotka和Volterra提出的模型是函数 $f ( \mathbf { \boldsymbol { \mathbf { \mathit { \varepsilon } } } } _ { t } )$ ， $g \left( t \right)$ 满足方程 $f ^ { \prime } ( t ) = a f ( t ) -$ $b f ( t ) g ( t ) , g ^ { \prime } ( t ) = c g ( t ) - d f ( t ) g ( t )$ ,其中 $a$ ，$\textit { b } , c , d$ 均为非负实数.

(1)下图为没有乙种群时，一段时间内甲种群数量与时间的关系折线图.为预测甲种群的数量变化趋势，研究人员提出了两种可能的数学模型： ${ \widehat { \mathbb { ( D } } } f ( t ) = m { \sqrt { t } } + n ; \widehat { \mathbb { ( 2 ) } } f ( t ) = m \cdot n ^ { t }$ ,其中 $m , n$ 均为大于1的正数.根据折线图判断，应选用哪种模型进行预测，并说明理由.

210 甲种群数量 195   
190 170/   
170   
150 134150   
130 107112119 125   
110 100103   
90 012345678

(2)设 $a = c = 0 . 0 8 , d = 2 b = 0 . 0 0 8$ $\textcircled{1}$ 函数 $F ( \iota ) = e ^ { - 0 . 0 8 \iota } \left[ 2 f ( \iota ) - g ( \iota ) \right]$ 的单调性；$\textcircled{2}$ 根据 $\textcircled{1}$ 中的结论说明：在绝大多数情况下,经过充分长的时间后，或者甲种群灭绝，或者乙种群灭绝.注：在题设条件下，各种群数量均有上限值

变式A 美中学组织学生对果家奶茶店的管业情况进行调查纯计，得到的叙据如下：

月份 2 A 6 & 10 12净利润 0.9204.23.95.25.1/万元$( 1 ) 1 \% \mu _ { 1 } = 1 1 \times 1 0 \% = \sqrt { 1 1 } .$ 试建立y关  
于元的 $\mathfrak { g }$ 线性经验回归方程 $y = ( 1 1 1 1 ) x + 1 1$   
和 $y = \prime \eta \sqrt { z } + \eta$ （保 $( 9 2$ 位有效教字）；（2）从样本相关系数的角度确定哪一  
个模型的拟合效聚更好，并据此预测次年  
2月（仟 $\chi = | { \cal A } |$ ）的冷利润（保 $\mathcal { U }$ 1伍小  
）附 $: \mathrm { { O D } }$ 样本相关系教厂  
$\frac { \displaystyle \sum \limits _ { i = 1 } ^ { n } ( x _ { i } - x ) ( y _ { i } - y ) } { \displaystyle \int \sum _ { i = 1 } ^ { n } ( x _ { i } - x ) ^ { 2 } ) \sqrt { \sum _ { i = 1 } ^ { n } ( y _ { i } - y ) ^ { 2 } } }$ ，回归  
直线 $\mathit { \hat { y } } = \mathit { \hat { I } } _ { I } { \boldsymbol { x } } + \mathit { \hat { u } }$ 中斜率和戲距的最小二秦  
估计公共分别为h= $\dot { \lambda } = \frac { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - x \right) \left( y _ { i } - y \right) } { \displaystyle \sum _ { i = 1 } ^ { n } \left( x _ { i } - x \right) ^ { 2 } }$   
$\dot { \textbf { \textit { a } } } = \boldsymbol { y } - \hat { I } _ { I } \times \boldsymbol { \{ 2 \} }$ 参考数机 $\smash { ! 1 n 2 \sim 0 , 7 , 1 n \ 3 \sim }$   
$1 , 1 , 1 0 5 - 1 , 6 , 1 n 7 - 1 , 9 , \sqrt { 2 } - 1 , 4 , \sqrt { 6 }$   
$- 2 . 4 { \mathrm { , } } / 8 { \mathrm { \sim } } 2 . 8 { \mathrm { , } } / 1 0 { \mathrm { \sim } } 3 . 2 { \mathrm { , } } / 1 2 { \mathrm { \sim } } 3 . 5 { \mathrm { , } }$   
14\~3.7，√3322-57.6，/4583-67.7

# 第1讲直线与圆的综合问题·

# 【核心知识聚焦】

# 一、圆的切线、切点弦结论

1.求过圆 $C : ( x - a ) ^ { 2 } + ( y - b ) ^ { 2 } = \rho ^ { 2 } ~ \mathrm { f } -$ 点 $\textstyle P ( x _ { 0 } , y _ { 0 } )$ 的四 $\mathcal { C }$ 的切线的步骤如下：

(1)先验证经过点 $P$ 且垂直于 $\mathscr { z }$ 轴的直线悬否和园 $c$ 相切，若是，如图 $\textcircled{1}$ 所示，所求切线为 $\chi = \chi _ { _ { 0 } }$ ，问题求解完毕；若否，则进行下一步，

(2)设切线斜率为 $k$ ，如图 $\textcircled{2}$ 所示，由 $P C \bot$ 切线,求出 $k$ ，用点斜式写出切线的方程，问题求解完毕，

上述问题的结论：圆 $c$ 上点 $\rho$ 处的切线的方程为 $\left( x - a \right) \left( x _ { 0 } - a \right) + \left( y - b \right) \left( y _ { 0 } - b \right) = r ^ { 2 } ,$

![](images/de659df54b21244df31e8a451f971e6add7f5c80cb0b16a2f83cb4ce4e76ebcb.jpg)

2.求过圆 $C : ( x - a ) ^ { 2 } + ( y - b ) ^ { 2 } = r ^ { 2 }$ 外一点 $P ( x _ { 0 } , y _ { 0 } )$ 的圆 $c$ 的切线的步骤如下：

(1)先验证过点 $\boldsymbol { \mathscr { p } }$ 且垂直于 $\pmb { \mathscr { x } }$ 轴的直线是否和圆相切，若是，如图 $\textcircled{3}$ 所示，其中一条切线为 $\boldsymbol { x } = \boldsymbol { x } _ { 0 }$

(2)设切线的斜率为 $k$ ，用点斜式写出切线的方程，由圆心到切线的距离 $d = r$ ，解出 $k$ ，求得切线方程.

3.过圆 $C _ { : } ( x - a ) ^ { 2 } + ( y - b ) ^ { 2 } = r ^ { 2 }$ 外一点$P ( x _ { 0 } , y _ { 0 } )$ 作圆 $c$ 的两条切线，切点分别为 $A$ 和$\boldsymbol { \mathscr { B } }$ ，如图 $\textcircled{4}$ 所示，则切点弦 $A B$ 所在直线的方程为 $( x - a ) ( x _ { 0 } - a ) + ( y - b ) ( y _ { 0 } - b ) = r ^ { 2 } .$

![](images/a302e31cca5e39d020c551b720b1fbd12855707ab1326c1bbfd74c0f41bfebfb.jpg)  
图③

![](images/bc581a7ac11a37abff62e8d5995e9dca7c0fed616c810b568a33004e41c0461b.jpg)  
图 $\textcircled{4}$

# 二、隐形园与阿氏园

1.在解析几何问题中，若题干中某个动点的轨迹是圆，这类回题我们称之为隐形圆回题，解题的关键是发现隐形圆，运用圆的性质来求解答案.本专题后续内容将详细归纳隐形圆常见的几类题型

2.阿氏圆；设 $A , B$ 是平面上的两个定点，若平面内的动点 $\boldsymbol { p }$ 满足 ${ \frac { \mid P A \mid } { \mid P B \mid } } = \lambda ( \lambda > 0$ 且$\lambda \not = 1 .$ ，则点 $\boldsymbol { \mathscr { p } }$ 的轨迹是圆，该圆叫做阿氏圆.

3.考题中常用的阿氏圆性质：

(1)圆心位置：当 $\lambda > 1$ 时，圆心 $M$ 在 $A B$ 的延长线上；当 $0 < \lambda < 1$ 时，圆心 $\pmb { M }$ 在 $B A$ 的延长线上.

(2)半径公式： $r = \frac { \lambda d } { \mid \lambda ^ { 2 } - 1 \mid }$ ，其中 $d$ 为两定点 $^ { A , B }$ 之间的距离，

(3）找定点：如下图所示，设圆 $M$ 的半径为 $r$ ，对于圆 $M$ 外任意一点A，连接AM交圆 $M$ 于点 $N$ ，则在线段 $M N$ 上必定存在点 $B$ ，使得对于圆 $M$ 上任意一点 $P$ ，都有 ${ \frac { \mid P A \mid } { \mid P B \mid } } = \lambda$ ，可以根据 $\mid M A \mid { \bf \sigma } \cdot \mid M B \mid = r ^ { 2 } .$ 找到点 $B$ .根据 $\displaystyle \int \frac { \mid { \cal M } { \cal A } \mid } { \mid { \cal M } { \cal B } \mid } =$ $\lambda$ 求出 $\lambda$

![](images/bd0cf7298694843bcb7d079995026b0fba93d690266ade02b1acb8ec19600731.jpg)

# 【进阶提升】

# 题型一圆的切线、切点弦问题

例1（1）圆 $C : ( x - 1 ) ^ { 2 } + y ^ { 2 } = 4$ 在点$P ( 0 , { \sqrt { 3 } } )$ 处的切线方程为

(2)已知圆 $C _ { : } x ^ { 2 } + y ^ { 2 } - 2 x - 4 y + 1 = 0$ 外一点 $P ( \mathbf { \varepsilon } - 2 , 1 )$ ，过点 $P$ 作圆 $\boldsymbol { c }$ 的两条切线，切点分别为 $\boldsymbol { \lambda }$ 和 $B$ ，则直线 $\varLambda B$ 的方程为

变式1 已知圆 $C : ( x - 1 ) ^ { 2 } + y ^ { 2 } = 4$ ，则：

（1）圆 $c$ 的过点 $P ( \mathbf { \varepsilon } - 2 , 0 )$ 的切线方程为

(2）圆 $c$ 的过点Q（3，1）的切线方程为

例2已知直线 $l ; y = x + 4$ 与 $_ { \pmb { x } }$ 轴交于点$\textit { \textbf { T } }$ ，过直线 $\mathbf { \xi } _ { l }$ 上的动点 $P$ 作圆 $O : x ^ { 2 } + y ^ { 2 } = 4$ 的两条切线，切点分别为 $A , B$ ，设 $\varLambda B$ 中点为 $M$ ，则|TM丨的最小值为（）

$\Lambda , 2 \sqrt { 2 }$ $\mathbb { B } , 3 \sqrt { 2 }$ C. $\sqrt { 1 7 }$ D.3

变式2 已知圆 $Q : x ^ { 2 } + y ^ { 2 } = 4 , P$ 为直线 $l _ { \{ y \} } = x + 4$ 上一点，过点 $\boldsymbol { \mathsf { \Pi } } ^ { p }$ 作团 $o$ 的两条切线，切点分别为A和 $B$ ，若四边形PAOB的面积为12，则直线AB的方程为

# 题型二隐形圆与阿氏圆

例3 (1)若圆 $( x - a ) ^ { 2 } + ( y - a + 1 ) ^ { 2 } = 4$ 上存在点 $P$ ，使得 $P$ 点到原点的距离为3,则实数 $^ { a }$ 的取值范围为

(2)在平面直角坐标系 $x O y$ 中，已知点$M ( 0 , 2 )$ 和 $N ( 0 , 1 )$ ,若直线 $x - 2 y + a = 0$ 上存在点 $P$ 使 $\mid P M \mid = 2 \mid P N \mid$ ,则实数 $a$ 的取值范围为

变式3 已知点A（2，2)， $B ( 4 , 2 m )$ ，点P在直线x-y+2=0上，若满足PA.PB=2的点 $P$ 有两个，则实数 $m$ 的取值范围为

例4在平面直角坐标系 $x O y$ 中，已知 $B$ ，$c$ 为圆 $x ^ { 2 } + y ^ { 2 } = 9$ 上两点，点A(2,2），且 $A B \perp$ AC，则线段 $B C$ 的长的取值范围为

变式4在平面直角坐标系 $x O y$ 中，已知两个圆 $x ^ { 2 } + y ^ { 2 } = 4$ 和 $x ^ { 2 } + y ^ { 2 } = 9$ ，定点$P ( \boldsymbol { \mathrm { 1 } } , \boldsymbol { \mathrm { 0 } } )$ ，动点 $A , B$ 分别在两个圆上，满足$\angle A P B = 9 0 ^ { \circ }$ ，则| $A B$ 丨的取值范围为

例5在平面直角坐标系中，已知点 $B ( 4 .$ 0）， $D ( 1 , 4 )$ ， $C$ 为圆 $M : ( x + 4 ) ^ { 2 } + y ^ { 2 } = 1 6$ 上的动点，则 $\mid C B \mid + 2 \mid C D \mid$ 的最小值为

变式5 在平面直角坐标系中，已知点$B ( 4 , 0 ) , D ( - 4 , 2 ) ,$ $C$ 为圆 $M : ( x + 4 ) ^ { 2 } +$ $y ^ { 2 } = 1 6$ 上的动点，则 $\mid C B \mid - 2 \mid C D \mid$ 的最大值为

# 【核心知识聚焦】

# 一、在焦点三角形背景下求椭圆的离心率

一般结合椭圆的定义，关键是运用已知条件研究出 $\triangle P F _ { 1 } F _ { 2 }$ 的三边长之比或内角正弦值之比.

![](images/8b56abd3658e876767bd803c58629ebc727040dc20c40c25610a4d28b5bd6c12.jpg)

$e ~ = ~ { \frac { c } { a } } ~ = ~ { \frac { 2 c } { 2 a } } ~ = ~ { \frac { \mid F _ { 1 } F _ { 2 } \mid } { \mid P F _ { 1 } \mid ~ + ~ \mid P F _ { 2 } \mid } } ~ = ~$ $\frac { \sin \angle F _ { 1 } P F _ { 2 } } { \sin \angle P F _ { 1 } F _ { 2 } + \sin \angle P F _ { 2 } F _ { 1 } } .$

# 二、在焦点三角形背景下求双曲线的离心率

一般结合双曲线的定义，关键是运用已知条件研究出 $\triangle P F _ { 1 } F _ { 2 }$ 的三边长之比或内角正弦值之比.

![](images/ce8bc5c846cd3826cf8264896426ce1253925198d13f009d22fd23ae642ea3c6.jpg)

公式：e== $e = { \frac { c } { a } } = { \frac { 2 c } { 2 a } } = { \frac { \mid { F _ { 1 } F _ { 2 } } \mid } { \mid \mid { P F _ { 1 } } \mid \mid - \mid { P F _ { 2 } } \mid \mid } } =$ $\frac { \sin \angle F _ { 1 } P F _ { 2 } } { | \sin \angle P F _ { 1 } F _ { 2 } - \sin \angle P F _ { 2 } F _ { 1 } | } .$

三、设双曲线 $C : \frac { x ^ { 2 } } { a ^ { 2 } } - \frac { y ^ { 2 } } { b ^ { 2 } } = 1 \left( a > 0 , b > 0 \right)$ 的焦点分别为 $F _ { 1 } , F _ { 2 }$ ,则有以下结论：

1.双曲线的焦点到渐近线的距离等于虚半轴长 $b$ 2.如图 $\textcircled{1}$ 所示，以 $F _ { 1 } F _ { 2 }$ 为直径的圆与双曲线 $c$ 的渐近线在第一象限的交点为 $( a , b )$ 3.如图 $\textcircled{2}$ 所示，过双曲线 $C$ 上任意一点 $P$ 作双曲线 $c$ 的两条渐近线的平行线,则它们与两条渐近线所围成的平行四边形PIOJ的面积是定值 $\cdot { \frac { a b } { 2 } } .$

![](images/650a31b69cce41a1bb71ca41b97aa29a61dc09b73be572805cdee7dd4f28874a.jpg)  
图

![](images/7a04a4900434ce613ef1cefa87964fc92e77c2bfc4fad01e9238058865b674fc.jpg)  
图 $\textcircled{2}$

4.如图 $\textcircled{3}$ 所示，双曲线 $C$ 上任意一点 $P$ 处 的切线与双曲线 $\boldsymbol { C }$ 的两条渐近线分别交于A 和 $B$ 两点，则 $P$ 为 $A B$ 的中点，且 $\triangle A O B$ 的面积 为定值ab.

![](images/4a8e06e2c97c1c4740ad906596ad09fe7058bfedf1d0422f7c40e4376a9ea04c.jpg)  
图 $\textcircled{3}$

![](images/****************************************a5aa3b133939a0ad3278d4e0.jpg)  
图④

5.如图 $\textcircled{4}$ 所示， $^ { , A , B }$ 分别在双曲线 $c$ 的两条渐近线上, $D$ 为 $A B$ 的中点,若直线 $O D , A B$ 的斜率都存在,则它们的斜率之积为 $\cdot { \frac { b ^ { 2 } } { a ^ { 2 } } } .$

# 四、阿基米德三角形

1.如图 $\textcircled{1}$ 所示，不妨设抛物线为 $x ^ { 2 } = 2 p y$ $\left( p > 0 \right)$ )，抛物线上 $A , B$ 两点处的切线交于点$P$ ，则：

(1)设 $A B$ 的中点为 $M$ ，则 $P M$ 平行于(或重合)抛物线的对称轴；

(2)PM的中点 $s$ 在抛物线上,且抛物线在 点 $s$ 处的切线平行于弦AB.

![](images/9d665166d56589ece4fb10f848b10f2756b5db83704b9a86b95087804dd41846.jpg)  
图 $\textcircled{1}$

![](images/****************************************43e738c4cb4f1647eca11d86.jpg)  
图 $\textcircled{2}$

2.如图 $\textcircled{2}$ 所示,不妨设抛物线为 $x ^ { 2 } = 2 p y$ $\left( p > 0 \right)$ ），抛物线上 $A , B$ 两点处的切线交于点$P$ ，则：

(1)若弦 $A B$ 过抛物线内的定点 $Q$ ，则点 $P$ 的轨迹是直线;特别地,若弦 $A B$ 过定点 $( 0 , m )$ $\left( m > 0 \right)$ ，则点 $P$ 的轨迹是直线 $y = - m$

(2)若弦 $A B$ 过抛物线内的定点 $Q$ ，则以 $Q$ 为中点的弦与(1)中点 $P$ 的轨迹平行.

3.如图 $\textcircled{3}$ 所示，不妨设抛物线为 $x ^ { 2 } = 2 p y$ $\left( p > 0 \right)$ ，抛物线上 $A , B$ 两点处的切线交于点$P$ ,若 $A B$ 过焦点 $F$ ,则点 $P$ 的轨迹为抛物线的准线， $P A \bot P B , P F \bot A B$ ，且 $\triangle P A B$ 的面积的最小值为 $p ^ { 2 }$

![](images/7e67db118482ad8264415fcc5a81856d4f74b039be09b29876d1f4aecedfdfad.jpg)  
图 $\textcircled{3}$

![](images/****************************************b1507ea38170ec6707b17c81.jpg)  
图 $\textcircled{4}$

4.如图 $\textcircled{4}$ 所示，不妨设抛物线为 $x ^ { 2 } = 2 p y$ $\left( { \boldsymbol { p } } > 0 \right)$ ，抛物线上 $A , B$ 两点处的切线交于点$P$ ，则：

(1） $\angle P F A = \angle P F B ;$ (2) $| A F | \cdot | B F | = | P F | ^ { 2 } .$

提醒：阿基米德三角形在小题和大题中都可能涉及，小题可以直接用性质速解，大题则必须给出详细的求解过程.

# 【进阶提升】

题型一椭圆、双曲线焦点三角形下的离心率问题

例1 (1)过椭圆 $\frac { x ^ { 2 } } { a ^ { 2 } } + \frac { y ^ { 2 } } { b ^ { 2 } } = 1 \left( a > b > 0 \right)$ 的 左焦点 $F _ { 1 }$ 作 $_ x$ 轴的垂线交椭圆于 $A , B$ 两点， 椭圆的右焦点为 $F _ { 2 }$ ,若 $\cos \angle A F _ { 2 } B = \frac { 1 } { 8 }$ ，则椭圆 的离心率为

(2)在 $\triangle A B C$ 中， $A B = 2 , B C = 1$ ，且 $6 0 ^ { \circ } \leqslant$ $\angle A B C \leqslant 9 0 ^ { \circ }$ ,若以 $B , C$ 为焦点的椭圆经过点$A$ ,则该椭圆的离心率的取值范围为

变式1 在 $\triangle A B C$ 中， $A B \perp A C$ ，ta $\alpha \angle A B C = \frac { 1 } { 3 }$ ，则以 $B , C$ 为焦点，且经过点A的椭圆的离心率为

例2(1)已知 $F _ { 1 } , F _ { 2 }$ 是双曲线 $C : \frac { x ^ { 2 } } { a ^ { 2 } } \ .$ $\frac { \gamma ^ { 2 } } { b ^ { 2 } } = 1$ 的左、右焦点,过 $F _ { 1 }$ 且与 $_ x$ 轴垂直的直线 与双曲线 $C$ 交于 $^ { A , B }$ 两点，若 $\triangle A B F _ { 2 }$ 是等腰直 角三角形，则双曲线 $C$ 的离心率为

(2)设 $F _ { 1 } , F _ { 2 }$ 是椭圆 $C : \frac { x ^ { 2 } } { a ^ { 2 } } + \frac { y ^ { 2 } } { b ^ { 2 } } = 1 ( a > b >$ 0)的左、右焦点，点 $P$ 在 $C$ 上，且 $\angle P F _ { 1 } F _ { 2 } =$ $4 5 ^ { \circ } , \cos \angle P F _ { 2 } F _ { 1 } = \frac { 4 } { 5 }$ ,则椭圆C的离心率为

# 题型二关于双曲线渐近线中的常见结论

例3（1)已知双曲线 $C : \frac { x ^ { 2 } } { a ^ { 2 } } - \frac { y ^ { 2 } } { b ^ { 2 } } = 1 \left( a > \right.$ $0 , b > 0 )$ 的左、右焦点分别为 $F _ { 1 } , F _ { 2 }$ ，过 $F _ { 1 }$ 的直 线与 $C$ 的两条渐近线分别交于 $A , B$ 两点,若 $\overrightarrow { F _ { 1 } A } = \overrightarrow { A B }$ $\overrightarrow { F _ { 1 } B } \cdot \overrightarrow { F _ { 2 } B } = 0$ ，,则 $C$ 的离心率为

(2)设直线 $x - 3 y + m = 0 { \bigl ( } m \neq 0 { \bigr ) }$ 与双曲 $\mathring { \chi } \frac { x ^ { 2 } } { a ^ { 2 } } - \frac { \gamma ^ { 2 } } { b ^ { 2 } } = 1 \big ( a > 0 , b > 0 \big )$ 的两条渐近线分别交 于点 $A , B$ ，若点 $P ( m , 0 )$ 满足 $\mid P A \mid = \mid P B \mid$ ， 则该双曲线的离心率是

变式2 已知双曲线 $C : \frac { x ^ { 2 } } { 2 } - \frac { y ^ { 2 } } { 6 } = 1$ 的右焦点为 $F$ ，点 $A$ 为双曲线 $\boldsymbol { C }$ 在第一象限上的一点，且 $A F \perp x$ 轴，过 $A$ 作 $C$ 的两条渐近线的平行线与双曲线的渐近线交于 $M$ ，$N$ 两点，则四边形OMAN的面积为

# 题型三 关于抛物线中阿基米德三角形

例4（1)已知抛物线 ${ C : \gamma } ^ { 2 } = 4 x$ 的焦点为$F$ ，过 $F$ 的直线与抛物线 $\boldsymbol { C }$ 交于 $A , B$ 两点，抛物线 $\boldsymbol { C }$ 在 $A , B$ 两点处的切线相交于点 $P$ ，若$\vert A F \vert = 3$ ,则 $| P F | =$

(2)已知抛物线 $C : x ^ { 2 } = 4 y$ ，过点 $P ( 1 , - 1 )$ 作抛物线 $\boldsymbol { C }$ 的两条切线，切点分别为 $A$ 和 $B$ ，则经过 $P , A , B$ 三点的圆的方程为

变式3 已知过抛物线 $x ^ { 2 } = 2 y$ 的焦点$F$ 的直线与抛物线交于 $A , B$ 两点，抛物线在 $A , B$ 处的切线交于点 $C$ ，则 $\triangle A B C$ 面积的最小值为

# 【核心知识聚焦】

过圆锥曲线 $C$ 上一已知点A作直线与 $C$ 交于另外一点 $B$ ，则点 $B$ 坐标可求.具体做法如下：

(1)设出直线 $A B$ 的方程；(2)联立直线方程和圆锥曲线 $C$ 的方程；(3)由韦达定理求出 $x _ { A } x _ { B }$ ,根据已知的 $x _ { \lambda }$ 求出xB；

(4)将 $x _ { B }$ 代人直线方程求出 $\boldsymbol { \mathcal { Y } } _ { B }$

# 【进阶提升】

# 题型一 已知一点求另一点坐标

例1如图所示,曲线 $C$ 由上半椭圆 $C _ { \iota }$ ：$\frac { y ^ { 2 } } { a ^ { 2 } } + \frac { x ^ { 2 } } { b ^ { 2 } } = 1 \left( a > b > 0 , y \geqslant 0 \right)$ 和部分抛物线 $C _ { 2 }$ ：$y = - x ^ { 2 } + 1 \left( y \leqslant 0 \right)$ 连接而成， $C _ { 1 } , C _ { 2 }$ 的公共点为 $^ { A , B }$ ，其中 $C _ { 1 }$ 的离心率为 $\frac { \sqrt { 3 } } { 2 } .$

(1)求 $^ { a , b }$ 的值；

(2)过点 $B$ 的直线 $\boldsymbol { l }$ 与 $C _ { 1 } , C _ { 2 }$ 分别交于 $P , Q$ (均异于点 $^ { A , B }$ ，若 $A P \bot A Q$ ，求直线 $l$ 的方程

![](images/d22dc75bac3994ba6daaf7878d6daf43f3622002e63ae903776b29f8437ff139.jpg)

例2已知椭圆 ${ \frac { x ^ { 2 } } { a ^ { 2 } } } + { \frac { y ^ { 2 } } { b ^ { 2 } } } = 1 \left( a > b > 0 \right)$ 的离心率 $e = { \frac { \sqrt { 3 } } { 2 } }$ ,连接椭圆的4个顶点得到的菱形的面积为4.

(1)求椭圆的方程;

(2)设直线 $l$ 与椭圆相交于不同的两点 $A$ ， $B$ ，已知点A的坐标为 $( \mathbf { \nabla } - a , 0 )$ ，点 $Q ( 0 , y _ { 0 } )$ 在 线段 $A B$ 的垂直平分线上,且 $\overrightarrow { Q A } \cdot \overrightarrow { Q B } = 4$ ，求 $y _ { 0 }$ 的值.

例3已知椭圆 $\frac { x ^ { 2 } } { a ^ { 2 } } + \frac { y ^ { 2 } } { b ^ { 2 } } = 1 ( a > b > 0 )$ 的一个顶点为 $A \left( 0 , - 3 \right)$ ，右焦点为 $F$ ，且 $\mid O A \mid =$ $| O F$ 一,其中 $O$ 为原点.

(1)求椭圆的方程；

(2)已知点 $C$ 满足 $3 { \overrightarrow { O C } } = { \overrightarrow { O F } }$ ，点 $B$ 在椭圆上（ $B$ 异于椭圆的顶点）,直线 $A B$ 与以 $C$ 为圆心的圆相切于点 $P$ ，且 $P$ 为线段 $A B$ 的中点，求直线 $A B$ 的方程.

例4设椭圆 $\vert \frac { x ^ { 2 } } { a ^ { 2 } } + \frac { y ^ { 2 } } { 3 } = 1 ( a > \sqrt { 3 } )$ 的右焦点为 $F$ ，右顶点为 $A$ ，已知 ${ \frac { 1 } { \mid O F \mid } } + { \frac { 1 } { \mid O A \mid } } =$ ,其中0为原点,e为椭圆的离心率.

(1)求椭圆的方程；

(2)设过点A的直线 $\iota$ 与椭圆交于点 $B$ （点 $B$ 不在 $_ { x }$ 轴上),垂直于 $\iota$ 的直线与 $\iota$ 交于点 $M$ ， 与 $\boldsymbol { \mathscr { I } }$ 轴交于点 $H .$ 若 $B F \perp H F$ ，且 $\angle M O A \leqslant$ ∠MAO,求直线 $l$ 的斜率的取值范围.

![](images/7a61409fd11326b5ab9e6d86ce76e5cbcbfd7d5fa9253a50bf0da1d7308ed492.jpg)

# 【核心知识聚焦】

在圆锥曲线的解答题中，有一类探索性问题，直接求解目标较为困难，此时不妨先用一两种特殊情况将问题的答案求出来，再论证该结果对一般的情形也成立，解决问题.由特殊到一般，是一种重要的探索问题的思想方法.

# 【进阶提升】

# 题型一直线方程的特殊性探路

例1已知双曲线 $E$ ${ \bar { \ : } } \because { \frac { x ^ { 2 } } { a ^ { 2 } } } - { \frac { y ^ { 2 } } { b ^ { 2 } } } = 1 ( a > 0 , b >$ 0)的两条渐近线分别为 $l _ { 1 } : y = 2 x , l _ { 2 } : y = - 2 x$ （1)求双曲线的离心率；

(2)如图所示，动直线 $\boldsymbol { l }$ 分别交直线 $l _ { 1 } , l _ { 2 }$ 于 $A , B$ 两点 $( A , B$ 分别在第一、四象限），且$\triangle { O A B }$ 的面积恒为8,试探究：是否总存在与直线 $\iota$ 只有一个交点的双曲线 $E ?$ 若存在,求出双曲线 $E$ 的方程,不存在,说明理由.

![](images/8dbaa0ba60ca6707430ec7da5774175f18d7284c9f0551eb9812482a9feb48ce.jpg)

# 题型二 根据对称性探路

例2如图所示,椭圆 $: \frac { x ^ { 2 } } { a ^ { 2 } } + \frac { y ^ { 2 } } { b ^ { 2 } } = 1 ( a > b$ ${ > } 0 )$ 的左焦点为 $F _ { 1 }$ ,右焦点为 $F _ { 2 }$ ，离心率 $e =$ $\frac { 1 } { 2 }$ 过 $F _ { 1 }$ 的直线交椭圆于 $A , B$ 两点，且 $\triangle A B F _ { 2 }$ 的周长为8.

(1)求椭圆 $E$ 的方程；

(2)设动直线 $l { : } y = k x + m$ 与椭圆 $E$ 有且只有一个公共点 $P$ ,且与直线 $x = 4$ 相交于点Q.试探究：在坐标平面内是否存在定点 $M$ ，使得以 $P Q$ 为直径的圆恒过点 $M ?$ 若存在,求出点M的坐标;若不存在,说明理由.

![](images/4bb543b0e87639892a0e6399364d17f2ae42dd3204ec2f3224e6a7bd0229af2f.jpg)

# 题型三 根据几何直观探路

例3已知椭圆 $E$ 的中心为坐标原点，对称轴为 $_ x$ 轴 $\mathbf { \nabla } \cdot \mathbf { \boldsymbol { { y } } }$ 轴，且过 $A ( 0 , - 2 )$ ， $B \left( { \frac { 3 } { 2 } } , - 1 \right)$ 两点.

(1)求 $E$ 的方程；

(2)设过点 $P ( 1 , - 2 )$ 的直线交 $E$ 于 $M , N$ 两点，过 $M$ 且平行于 $_ x$ 轴的直线与线段 $A B$ 交 于点 $T$ ，点 $H$ 满足 ${ \overrightarrow { M T } } = { \overrightarrow { T H } } .$ 证明：直线 $H N$ 过 定点

A（S

# 题型四必要性探路

例4如图所示，椭圆 $E : \frac { x ^ { 2 } } { a ^ { 2 } } + \frac { y ^ { 2 } } { b ^ { 2 } } = 1 ( a >$ $b > 0 ^ { \prime } ,$ 的离心率是 $\frac { \sqrt { 2 } } { 2 }$ ，过点 $P ( 0 , 1 )$ 的动直线 $\iota$ 与椭圆交于 $A , B$ 两点.当直线 $\iota$ 平行于 $_ x$ 轴时，直线 $\iota$ 被椭圆 $E$ 截得的线段长为 $2 \sqrt { 2 }$

(1)求椭圆 $E$ 的方程；

(2)在平面直角坐标系中是否存在与点 $P$ 不同的定点 $Q$ ，使得 ${ \frac { \mid Q A \mid } { \mid Q B \mid } } = { \frac { \mid P A \mid } { \mid P B \mid } }$ 恒成立？若存在,求出点 $Q$ 的坐标;若不存在,说明理由.

![](images/53e3673073a0caa78d650143829b824c6c12726e59392fc19a0e963d9a3f0e46.jpg)

![](images/ee2f5ad1c7bb99820f4d31cca975759bca2250cb62cdb5f57beff204d4e26a23.jpg)

# ·第5讲隐藏型定点定值的探究··

# 【核心知识聚焦】

圆锥曲线中有些题目看似是求最值、范围问题,往往隐含了直线过定点或者某个表达式是定值的结论,如果能有意识地去发现此类结论的条件在题干上，那么解决这类问题就事半功倍.例如,斜率和、斜率积为定值时，能得到动直线过定点等等.

# 【进阶提升】

# 题型一直线方程的特殊性探路

例1已知椭圆 $C : \frac { x ^ { 2 } } { a ^ { 2 } } + \frac { y ^ { 2 } } { b ^ { 2 } } = 1 ( a > b > 0 )$ 的离心率为 $\frac { \sqrt { 2 } } { 2 }$ ，过点 $P ( \mathbf { \varepsilon } - 6 \mathbf { \varepsilon } , 0 )$ 作椭圆 $C$ 的两条切线 $m , n$ 互相垂直.

(1)求椭圆 $C$ 的方程；

(2）如图，设椭圆 $C$ 的左、右顶点分别为A,B,若过点 $A$ 作互相垂直的直线 $l _ { 1 } , l _ { 2 }$ 分别与椭圆交于 $M , N$ 两点， $M , N$ 两点不同于点A,求三角形BMN面积的最大值.

![](images/0adc6c60273379c43e051abe2e993d643920826a089b2cf857f7060e7a7bd10f.jpg)

例2如图，已知抛物线 $C _ { : } y ^ { 2 } = 2 p \mathrm { r } ( p >$ 0)的焦点 $\boldsymbol { \mathsf { \Sigma } } ^ { F }$ 、且经过点 $\Lambda \left( 2 p , m \right) \left( m > 0 \right)$ $\vert A F \vert = 5$

(1)求 $P$ 和 $_ m$ 的值；(2）点 $M , N$ 在 $C$ 上，且 $A M \perp A N .$ 过点A作 $A D \perp M N$ ， $D$ 为垂足，证明：存在定点 $Q$ ，使得 $| D Q |$ 为定值.

K $\mathbf { A }$ $\underline { { \boldsymbol { N } } }$

# 题型二 斜率和为定值

例3已知椭圆 C: $\frac { x ^ { 2 } } { 6 } + \frac { y ^ { 2 } } { b ^ { 2 } } = 1 ( b > 0 )$ 的左右焦点分别为 $F _ { \mathrm { 1 } } , F _ { \mathrm { 2 } } , C$ 是椭圆的中心，点 $M$ 为其上的一点满足 $\mid M F _ { 1 } \mid { \bf \theta } \cdot { \bf \sigma } \mid M F _ { 2 } \mid { \bf \theta } = 5 { \bf \theta } , \mid M C \mid$ 2

(1)求椭圆 $C$ 的方程；

(2)设定点 $T ( t , 0 )$ ，过点 $T$ 的直线 $l$ 交椭圆 $c$ 于 $P , Q$ 两点，若在 $C$ 上存在一点 $A$ 、使得直线 $A P$ 的斜率与直线 $A Q$ 的斜率之和为定值，求的范围、

# 题型三动点轨迹中的定点定值

例4已知椭圆 $C _ { : } \frac { x ^ { 2 } } { a ^ { 2 } } + \frac { y ^ { 2 } } { b ^ { 2 } } = 1 ( a > b > 0 )$ 的离心率为 $\frac { \sqrt { 3 } } { 2 }$ 且经过点(2,1).

(1)求 $\mathrm { C }$ 的方程；

(2)过椭圆 $\mathrm { \Delta } G$ 外一动点 $\boldsymbol { { \cal P } }$ 作椭圆 $C$ 的两条切线 $l _ { 1 } , l _ { 3 }$ ，斜率分别为 $k _ { 1 } , k _ { 3 }$ $k _ { 1 } \cdot k _ { 3 } \equiv$ $\downarrow$ 恒成立，证明：存在两个定点、使得点 $\mathbf { \nabla } \mathcal { P }$ 到这两定点的版离之和为定值、

# 【核心知识聚焦】

圆锥曲线中的四点共圆问题在高考中是一大难点，常规的做法是用垂径定理找圆心，并通过计算得出圆心到四点的距离相等,从而证出四点共圆，再求出圆的方程.除此之外，应用曲线系方程也可以很好地解决这类问题，本节所有题目两种解法都会给出.下面先分析曲线系方程的用法：

1.曲线系方程：设 $f ( x , y ) = 0$ 和 $\boldsymbol { g } ( x , y ) =$ 0分别表示平面上的两条有公共点的曲线，则经过两曲线交点的曲线系方程可以为 $\lambda f ( x , y )$ $+ g ( x , y ) = 0 .$

2.高考中常见的四点共圆问题是两条直线与圆锥曲线交于不同的四点,判断四点是否在同一圆上，如果是,需求出圆的方程.应用曲线系方程求解这类四点共圆问题的解题步骤是：

(1)设经过圆锥曲线和两直线交点的曲线系方程为 $\lambda f ( x , y ) + g ( x , y ) = 0$ ,其中 $f ( x , y ) = 0$ 表示圆锥曲线方程， $g ( x , y ) = 0$ 表示两直线构成的曲线的方程；

(2)将 $\lambda f ( x , y ) + g ( x , y ) = 0$ 展开,合并同类项，

与圆的一般方程 $x ^ { 2 } + y ^ { 2 } + D x + E y + F = 0$ 比较系数,求出 $\lambda$ 的值；

(3)将 $\lambda$ 反代回方程 $\lambda f ( x , y ) \ : + g ( x , y ) =$ 0的展开式，化为圆的标准方程,从而得出四点共圆且求出了圆的方程.

3.圆锥曲线中四点共圆问题的结论：设两条直线和圆锥曲线(椭圆、双曲线、抛物线)交于四点,则四个交点在同一个圆上的充要条件是两直线倾斜角互补.即两条直线 $\gamma _ { 0 } = \frac { \gamma _ { 1 } + y _ { 2 } } { 2 } =$ ${ \frac { \sqrt { 2 } } { 4 } } \left( { \sqrt { x _ { 1 } } } + { \sqrt { x _ { 2 } } } \right)$ 与二次曲线 $y _ { 0 } = { \frac { y _ { 1 } + y _ { 2 } } { 2 } } =$ $\frac { \sqrt { 2 } } { 4 } \left( \sqrt { x _ { 1 } } + \sqrt { x _ { 2 } } \right)$ 有四个交点,则这四个交点共圆的充要条件是 $\gamma _ { 0 } = \frac { y _ { 1 } + y _ { 2 } } { 2 } = \frac { \sqrt { 2 } } { 4 } \big ( \sqrt { x _ { 1 } } + \sqrt { x _ { 2 } } \big )$ （24号

4.相交弦定理： $P A \cdot P B = P C \cdot P D$

X X $P$ $B$ $D$

![](images/c472afe965860f78a5f34ad7251e8c9fdb6dacfb137b23bedd4be7977a0fbc59.jpg)

# 【进阶提升】

# 题型一相交线定理与曲线系

例1在平面直角坐标系 ${ { x O y } }$ 中，已知点$F _ { 1 } \left( \textrm { -- } \sqrt { 1 7 } , 0 \right) , F _ { 2 } \left( \sqrt { 1 7 } , 0 \right)$ ，且动点 $M$ 满足：$\mid M F _ { 1 } \mid - \mid M F _ { 2 } \mid = 2$ ，点 $M$ 的轨迹为 $C$

(1)求 $C$ 的方程；

(2)设点 $T$ 在直线 $x = \frac { 1 } { 2 }$ 上，过 $T$ 的两条直 线分别交 $C$ 于 $A , B$ 两点和 $P , Q$ 两点，且满足 $\mid T A \mid \cdot \mid T B \mid = \mid T P \mid \cdot \mid T Q$ 1，求直线 $A B$ 与直线 $P Q$ 的斜率之和.

例2如图所示,已知 $o$ 为坐标原点， $F$ 为椭圆 $C : x ^ { 2 } + \frac { y ^ { 2 } } { 2 } = 1$ 在 $\boldsymbol { \mathscr { y } }$ 轴正半轴上的焦点,过$F$ 且斜率为 $- \sqrt { 2 }$ 的直线 $l$ 与 $C$ 交于 $A , B$ 两点，点 $P$ 满足 $\overrightarrow { O A } + \overrightarrow { O B } + \overrightarrow { O P } = { \bf 0 }$

(1)证明：点 $P$ 在椭圆 $C$ 上；

(2）设点 $P$ 关于点 $O$ 的对称点为 $Q$ ,证明： $A , P , B , Q$ 四点在同一圆上.

![](images/8732521588ba571f9cf864ee9971ce5fe6a61833906fde848ef05e6521a09b93.jpg)

![](images/348c7fe780c75c596db16d98b3636c22641662004129254840374601633fb4dc.jpg)

# 题型二 四点共圆中的几何关系

例3已知抛物线 $C : y ^ { 2 } = 2 p x { \bigl ( } p > 0 { \bigr ) }$ 的焦点为 $F$ ，直线 $y = 4$ 与 $\boldsymbol { \mathscr { I } }$ 轴的交点为 $P$ ，与 $\boldsymbol { C }$ 的交点为 $Q$ 且 $\mid Q F \mid = \frac { 5 } { 4 } \mid P Q \mid$

(1)求 $C$ 的方程；

(2)过 $F$ 的直线 $l$ 与 $C$ 相交于 $A , B$ 两点，若 $A B$ 的垂直平分线与 $C$ 相交于 $M , N$ 两点，且$A , M , B , N$ 四点在同一圆上，求 $l$ 的方程.

![](images/1409e61149a4580fca4705080a2ffd85b563bb600916859d2bd08400909edffc.jpg)

例4在平而直角坐标系 $x \pmb { O } y$ 中、双曲线 $C _ { \bar { \mathbf { \Phi } } _ { a } ^ { 3 } } \frac { \gamma ^ { 2 } } { a ^ { 2 } } - \frac { \mathrm { \bf { \Phi } } _ { x } ^ { 3 } } { b ^ { 2 } } = 1 \left( \mathbf { \Phi } _ { a } > 0 , b > 0 \right)$ 的离心率为 $\sqrt { 2 }$ 、实轴 长为4

(1）求 $\boldsymbol { c }$ 的方程；

(2)如图、点 $A$ 为双曲线的下顶点，直线！过点 $P ( 0 , \ell )$ 且垂直于 $y$ 轴( $P$ 位于原点与上顶点之间），过 $P$ 的直线交 $c$ 于 $G , H$ 两点，直线AG、AH分别与 $l$ 交于 $M , N$ 两点，若 $\smash { 0 , A , N , M }$ 四点共图，求点 $P$ 的坐标

![](images/57059be7eba7c4e82b722c9c2facd7b13dbd4454186a1eefdd46d7ac1502fdf2.jpg)

# ·第7讲非对称韦达定理的处理技巧·

# 【核心知识聚焦】

在一些定点、定值、定线问题中,还常出现需要证明类似 $\frac { ( y _ { 2 } - 2 ) x _ { 1 } } { ( y _ { 1 } + 2 ) x _ { 2 } }$ 为定值的情形，通过直线代换可得 ${ \frac { \left( y _ { 2 } - 2 \right) x _ { 1 } } { \left( y _ { 1 } + 2 \right) x _ { 2 } } } = { \frac { \left( k { \cdot } x _ { 2 } + 2 \right) x _ { 1 } } { \left( k { \cdot } x _ { 1 } + 6 \right) x _ { 2 } } } =$ $\frac { k ; \mathrm { r } _ { 1 } \mathrm { x } _ { 2 } + 2 \mathrm { x } _ { 1 } } { k ; \mathrm { r } _ { 1 } \mathrm { x } _ { 2 } + 6 \mathrm { x } _ { 2 } }$ ,但此时式子并不能完全整理为韦达定理的形式，这种式子一般称为“非对称韦达定理".或者在处理斜率比值时

$$
\frac { k _ { P S } } { k _ { P B } } = \frac { \frac { y _ { 1 } - t } { x _ { 1 } } } { \frac { y _ { 2 } - t } { x _ { 2 } } } = \frac { x _ { 2 } y _ { 1 } - t x _ { 2 } } { x _ { 1 } y _ { 2 } - t x _ { 1 } } = \frac { k x _ { 1 } x _ { 2 } + ( m - t ) x _ { 2 } } { k x _ { 1 } x _ { 2 } + ( m - t ) x _ { 1 } } ,
$$

还有诸如线段的比例关系会得到 $x _ { 1 } = \lambda x _ { 2 }$ 或者 $y _ { 1 } = \lambda y _ { 2 }$ 的结构.

我们明明求了韦达定理却无法代人，这时我们就需要通过所求得的韦达定理找到 $x _ { 1 } +$ $x _ { 2 }$ 和 $x _ { 1 } \cdot x _ { 2 }$ 之间的关系，将其中一个替换，常用手段是把乘法的替换成加法.

这样的非对称形式，即韦达定理无法直接代入，可以通过下面的方法解决.

（1）利用关系式 $\lambda + { \frac { 1 } { \lambda } } = { \frac { x _ { 1 } } { x _ { 2 } } } + { \frac { x _ { 2 } } { x _ { 1 } } } =$ $\frac { ( x _ { 1 } + x _ { 2 } ) ^ { 2 } } { x _ { 1 } \cdot x _ { 2 } } - 2$ ,将问题转化韦达定理求解.

(2)韦达定理构造互化公式，先局部互化，然后可整理成对称型具体办法之一为联立方程后得到韦达定理 $\mathbb { I } \Big \{ \begin{array} { l } { x _ { 1 } + x _ { 2 } = \int ( t ) , } \\ { x _ { 1 } x _ { 2 } = g ( t ) } \end{array} \qquad \Longrightarrow m ( t ) \left( x _ { 1 } + x _ { 2 } \right) = n \big ( t \big ) x _ { 1 } x _ { 2 } ,$ 代人之后进行代换消元解题.

# 【进阶提升】

# 题型一 两根之和与两根之积的关系

例1已知点 $F$ 为椭圆 $E$ $: \frac { x ^ { 2 } } { 4 } + \frac { y ^ { 2 } } { 3 } = 1$ 的右焦点， $^ { , A , B }$ 分别为其左、右顶点,过 $F$ 作直线 $\iota$ 与椭圆交于 $M , N$ 两点(不与 $A , B$ 重合），记直线AM 与 BN 的斜率分别为 $k _ { 1 } , k _ { 2 }$ ，证明 $| \frac { k _ { 1 } } { k _ { 2 } } \rangle$ 为定值.

例2已知椭圆 $E \colon \frac { x ^ { 2 } } { a ^ { 2 } } + \frac { y ^ { 2 } } { b ^ { 2 } } = 1 \left( a > b > 0 \right)$ 的右焦点为 $F _ { 2 }$ ,上顶点为 $H , O$ 为坐标原点,$\angle O H F _ { 2 } = 3 0 ^ { \circ }$ 点 $\left( 1 , { \frac { 3 } { 2 } } \right)$ 在椭圆 $E$ 上

(1)求椭圆 $E$ 的方程；

(2)设经过点 $F _ { 2 }$ 且斜率不为0的直线 $\mathbf { \xi } _ { l }$ 与  
椭圆 $E$ 相交于 $A , B$ 两点，点 $P ( \mathbf { \varepsilon } - 2 , 0 )$ ， $Q ( 2$ ，  
0).若 $M , N$ 分别为直线 $A P$ ， $B Q$ 与 $y$ 轴的交  
点，记 $\triangle M P Q , \triangle N P Q$ 的面积分别为 $S _ { \triangle M P Q }$ ，  
$\boldsymbol { S } _ { \Delta N P Q }$ 求 SMP的值SNPQ

山y

> 0

![](images/ec41dd09ef09cdc0461af793306f2104a66f1c739bf6d1b834b56762c2d24600.jpg)

# 题型二 利用两根之和进行消元

例3如图所示,椭圆有两个顶点A（-1,0）， $B ( 1 , 0 )$ ，过其焦点 $F ( 0 , 1 )$ 的直线 $l$ 与椭圆交于 $^ { C , D }$ 两点,并与 $_ x$ 轴交于点 $P$ ，直线AC与 $B D$ 交于点 $Q$

![](images/9451a4bc9384345d162f63530f68ff43f74bbf0b5a2a8fbf3d933cafeb612c2a.jpg)

（1)当|CD|=3√2 时，求直线 $l$ 的方程；(2)当 $P$ 点异于 $A , B$ 两点时，证明： $\overrightarrow { O P }$ ：为定值.

，桑头正一重雷天房斯題8县山9原中心+=（

![](images/55ef3980d1b56861aa87784fdc4f48d5cd140d81d423cd55dc89f33dd208d93e.jpg)

# 欧向，三

四示

#

#

# 题型三整体意识

例4点 $A , B$ 是椭圆 $E$ $: \frac { x ^ { 2 } } { 4 } + \frac { y ^ { 2 } } { 3 } = 1$ 的左右顶点，若直线 $l { : } y = k ( \ x - 1 )$ 与椭圆 $E$ 交于 $M , N$ 两点，求证：直线 $A M$ 与直线 $B N$ 的交点在一条定直线上.

# 【核心知识聚焦】

# 一、等腰三角形问题

等腰三角形这一几何关系的处理方法是抓住中线与底边垂直，转化为向量的数量积为零,或斜率之积等于-1来进行计算.

# 二、平行四边形问题

如图所示，四边形ABCD是平行四边形，在解析几何中，平行四边形这一对象的处理方法一般有两种：

(1)抓住对角线互相平分这一特征，即AC的中点 $P$ 也是 $B D$ 的中点,从而建立等量关系，加以计算；

(2)抓住 $\overrightarrow { A C } = \overrightarrow { A B } + \overrightarrow { A D }$ 这一特征,计算点 $c$ 的坐标,再进行下一步的运算.

$P$ A

# 三、菱形问题

如图所示，四边形ABCD为菱形，在解析

几何中,对菱形的处理一般抓住AC与BD互相垂直平分这一性质.

![](images/544bb242de15b89b25bfbcd19b207a70fb5c07c7f3baee2449fd666b992665d4.jpg)

# 四、倾斜角互补问题

当两直线倾斜角互补时，根据 $\tan ( \pi - \alpha )$ $= - \tan \alpha$ ，可以得出两直线斜率互为相反数，所以倾斜角互补的问题，在解析几何中往往采用斜率和为零来进行计算.

# 五、一般角度问题

在解析几何大题中，锐角、直角、钝角的计算可用向量的数量积来完成：

(1) $\angle A P B$ 为锐角： $\overrightarrow { P A } \cdot \overrightarrow { P B } > 0$ (2) $\angle A P B$ 为直角： $\overrightarrow { P A } \cdot \overrightarrow { P B } = 0$ (3) $\angle A P B$ 为钝角： $\overrightarrow { P A } \cdot \overrightarrow { P B } < 0$

提醒： $\textcircled{1}$ 有时题干不一定明说 $\angle A P B$ 是锐角（直角、钝角），可能会换一种说法，例如，点$P$ 在以 $A B$ 为直径的圆外（圆上、圆内）； $\textcircled{2}$ 注意需单独验证 $\angle A P B = 0 ^ { \circ }$ 或 $1 8 0 ^ { \circ }$ 的情形.

# 【进阶提升】

# 题型一 等腰三角形问题

例1已知双曲线 $C : \frac { x ^ { 2 } } { a ^ { 2 } } - \frac { y ^ { 2 } } { b ^ { 2 } } = 1$ 的左、右焦点分别为 $F _ { 1 } , F _ { 2 }$ ，离心率为3,直线 $\scriptstyle y = 2$ 与 $C$ 的两个交点之间的距离为 $\sqrt { 6 }$

(1)求 $^ { a , b }$

(2)设过 $F _ { 2 }$ 的直线 $l$ 与 $c$ 的左、右两支分 别相交于 $A , B$ 两点， $\mid A F _ { 1 } \mid \ = \mid B F _ { 1 }$ ,证明： $\mid A F _ { \sb 2 } \mid , \mid A B \mid , \mid B F _ { \sb 2 } \mid$ 成等比数列.

# 题型二 平行四边形问题

例2已知椭圓 $C : 9 x ^ { 2 } + y ^ { 2 } = m ^ { 2 } ( m > 0 )$ ，直线1不过原点 $o$ 且不平行于坐标轴， $l$ 与 $c$ 有两个交点 $A , B$ 线段 $A B$ 的中点为 $M .$

(1)证明：直线 $o M$ 的斜率与 $l$ 的斜率的乘积为定值；

(2）若 $\iota$ 过点 $\left( { \frac { m } { 3 } } , m \right)$ ，延长线段 $o M$ 与 $c$ 交于点 $P$ ，四边形OAPB能否为平行四边形？ 若能,求此时 $\iota$ 的斜率;若不能，说明理由.

# 题型三菱形问题

例3已知圆 $P$ 过点 $\textstyle Q ( 0 , 2 )$ ，且在 $_ x$ 轴上截得的弦长为4，记圆心 $P$ 的轨迹为曲线 $C$

(1)求曲线 $C$ 的方程；

(2）以 $M ( 0 , a ) \left( a > 0 \right)$ 为顶点能作三个菱 形MANB,使 $A , B , N$ 均在抛物线 $C$ 上,求 $^ a$ 的 取值范围.

# 题型四倾斜角互补问题

例4椭圆 $C _ { : } \frac { x ^ { 2 } } { a ^ { 2 } } + \frac { y ^ { 2 } } { b ^ { 2 } } = 1 ( a > b > 0 )$ 的右焦点为 $F ( c , 0 )$ ,短轴长为2,且 $c$ 截直线 $x = c$ 所得弦MN的长为 $\sqrt { 2 }$

(1)求椭圆 $C$ 的方程；

(2)若 $^ { A , B }$ 是 $C$ 上的两个动点，且 $\angle A F M =$ ∠BFM,证明直线 $A B$ 过定点，并求出定点的坐标.

# 题型五 一般角度问题

例5设 $_ { A , B }$ 分别为桶圆 $\Bigl \vert \frac { x ^ { 2 } } { a ^ { 2 } } + \frac { \gamma ^ { 2 } } { b ^ { 2 } } = 1 ( a >$ ${ \pmb b > } { \pmb 0 }$ )的左、右顶点，桶圆长半轴的长等于焦距，且过点 $\cdot \left( { \sqrt { 2 } } , { \frac { \sqrt { 6 } } { 2 } } \right)$ ，

(1)求桶圆的方程；

(2)设 $P$ 为直线 $\scriptstyle { \pmb { x } } = { \pmb { 4 } }$ 上不同于点(4,0)的任意一点，若直线 $A P , B P$ 分别与權圆相交于异于 $^ { A , B }$ 的点 $M , N$ ，证明：点 $\pmb { B }$ 在以MN为直径的圆内

# 【核心知识聚焦】

求最值(范围)问题解题的一般步骤：

(1)设直线的方程为 $\mathbf { \boldsymbol { y } } = \mathbf { \boldsymbol { k } } \mathbf { \boldsymbol { x } } + \mathbf { \boldsymbol { b } } $ 或 ${ \boldsymbol { x } } = \pi \mathbf { y } + t$ (2)将直线的方程代入圆锥曲线中，计算弦长、点到直线的距离等中间量；(3)将求范围的目标量表示成直线中引入的参数的函数关系式；(4)运用函数、均值不等式等基本方法求出最值(范围).

# @

# 【进阶提升】

# 题型一面积最值问题

倒1已知点 $A ( 0 , - 2 )$ ,椭圆 $E : \frac { x ^ { 2 } } { a ^ { 2 } } + \frac { y ^ { 2 } } { b ^ { 2 } } =$ 1 $\mathbf { a } > b > \mathbf { 0 }$ 的离心率为 $\frac { \sqrt { 3 } } { 2 }$ ,F是椭圆的焦点， 直线 $A F$ 的斜率为 ${ \frac { 2 { \sqrt { 3 } } } { 3 } } , O$ 为坐标原点.

(1)求 $E$ 的方程；(2)设过点 $A$ 的直线 $\iota$ 与 $E$ 相交于 $P , Q$ 两点，当 $\triangle { O P Q }$ 的面积最大时,求 $\iota$ 的方程.

# 题型二线段比的最值问题

例2已知椭圆 $C : \frac { x ^ { 2 } } { a ^ { 2 } } + \frac { y ^ { 2 } } { b ^ { 2 } } = 1 ( a > b > 0 )$   
的焦距为4,其短轴的两个端点与长轴的一个  
端点构成正三角形.(1)求椭圆 $c$ 的标准方程；(2）设 $F$ 为椭圆 $c$ 的左焦点， $T$ 为直线 ${ \boldsymbol { x } } =$   
-3上任意一点,过 $F$ 作 $T F$ 的垂线与椭圆交  
于点 $P , Q$ (i)证明： $o T$ 平分线段 $P Q$ （其中 $o$ 为坐标  
原点）；(ii)当 $\frac { \mid T F \mid } { \mid P Q \mid }$ 最小时,求点 $T$ 的坐标.

O100

# 题型三角度的最值问题

例3设抛物线 $C : y ^ { 2 } = 2 p x \left( p > 0 \right)$ 的焦点 为 $F$ ，点 $D ( p , 0 )$ ，过 $F$ 的直线交 $C$ 于 $M , N$ 两 点，当直线 $M D$ 垂直于 $_ x$ 轴时, $\vert M F \vert = 3$

(1)求 $C$ 的方程；

(2)设直线 $M D , N D$ 与 $C$ 的另一个交点分别为 $A , B$ ,记直线 $M N , A B$ 的倾斜角分别为 $\alpha$ ，$\beta$ ，当 $\alpha - \beta$ 取得最大值时,求直线 $A B$ 的方程.

# 题型四函数与方程思想

例4如图,设抛物线 $\gamma ^ { 2 } = 2 p x ( p > 0 )$ 的焦点为 $F$ ,抛物线上的点A到 $\boldsymbol { \mathscr { r } }$ 轴的距离等于$\left| ~ A F \right| - 1$ ：

(1)求 $p$ 的值；

(2)若直线 $A F$ 交抛物线于另一点 $B$ ，过点$B$ 与 $_ { x }$ 轴平行的直线和过点 $F$ 与 $A B$ 垂直的直线交于点 $N , A N$ 与 $_ { x }$ 轴交于点 $M$ ,求 $M$ 的横坐标的取值范围.

![](images/73fe4422b12f0d7ff39d387b7fae2f1b5cfce3d9e003475acd5d8d40a5bcb066.jpg)

#

# 题型五一类特殊函数求最值方法

例5设点 $A \left( \ - 2 , 0 \right) , B \left( 2 , 0 \right)$ ，动点 $M ( x ,$ y)满足直线 AM 与 BM的斜率之积为 $- { \frac { 1 } { 2 } }$ 记$M$ 的轨迹为曲线 $C .$

(1)求 $C$ 的方程,并说明 $C$ 是什么曲线；

(2)过坐标原点的直线交 $C$ 于 $P , Q$ 两点，点 $P$ 在第一象限， $P E \bot x$ 轴，垂足为 $E$ ，连接 $Q E$ 并延长交 $C$ 于点 $G _ { \ast }$

(i)证明： $\triangle P Q G$ 是直角三角形；  
(ii)求 $\triangle P Q G$ 面积的最大值.

# 题型六利用导数求最值

例6如图所示,已知抛物线 $x ^ { 2 } = y$ ，点$A { \Big ( } - { \frac { 1 } { 2 } } , { \frac { 1 } { 4 } } { \Big ) } , B { \Big ( } { \frac { 3 } { 2 } } , { \frac { 9 } { 4 } } { \Big ) }$ ，抛物线上的点 $P ( x , y )$ $\left( - \frac { 1 } { 2 } < x < \frac { 3 } { 2 } \right)$ 过点 $B$ 作直线 $A P$ 的垂线，垂足为 $Q$ ，

(1)求直线 $A P$ 斜率的取值范围；  
(2)求 $\mid P A \mid \cdot \mid P Q \mid$ 的最大值.

![](images/9f8e949c3b97e84296436991a045da2696e98b1b4cd76690fdddebcb857e3df4.jpg)

# 第八章函数与导数

# ·第1讲函数性质的应用

# 【核心知识聚焦】

# 一、单调性、奇偶性、对称性、周期性与定义域的关系

1.单调性：(1)单调区间Ω定义域;(2)应用单调性时，注意讨论变量是否在所属单调区间内.2.奇偶性：函数具有奇偶性的必要条件是其定义域关于原点对称.3.对称性：函数具有对称性的必要条件是其定义域关于对称轴或对称中心对称.4.周期性：函数具有周期性的必要条件是其定义域具无界性.

# 二、单调性的辨别

1. (1) $\forall x _ { 1 } , x _ { 2 } \in D$ 且 $x _ { 1 } \neq x _ { 2 }$ ，则 $\left( x _ { 1 } - x _ { 2 } \right)$ ·$\left[ f ( x _ { 1 } ) - f ( x _ { 2 } ) \right] > 0 \Longleftrightarrow { \frac { f ( x _ { 1 } ) - f ( x _ { 2 } ) } { x _ { 1 } - x _ { 2 } } } > 0 \Longleftrightarrow f ( x )$ 在$D$ 上单调递增；(2) $\forall x _ { 1 } , x _ { 2 } \in D$ 且 $\boldsymbol { x } _ { 1 } \neq \boldsymbol { x } _ { 2 }$ ,则 $\left( x _ { 1 } - x _ { 2 } \right)$ ·$\left[ f ( x _ { 1 } ) - f ( x _ { 2 } ) \right] < 0 \Longleftrightarrow { \frac { f ( x _ { 1 } ) - f ( x _ { 2 } ) } { x _ { 1 } - x _ { 2 } } } < 0 \Longleftrightarrow f ( x )$ 在 $D$ 上单调递减;（3）Vxx∈D且x≠x,则f（x）-f（x）$> m { \Longleftrightarrow } g ( x ) = f ( x ) - m x$ 在 $D$ 上单调递增；

（4）x,x∈D且x≠x,则f（x）-f（x）$< m { \Leftrightarrow } g ( x ) = f ( x ) - m x$ 在 $D$ 上单调递减.

2.运算函数的单调性规律

(1)若 $f ( x )$ 和 $g ( x )$ 均为增(或减)函数，则在 $f ( x )$ 和 $g ( x )$ 的公共定义域上 $f ( x ) + g ( x )$ 为增(或减)函数；若 $f ( x )$ 和 $g \left( x \right)$ 均为增(或减)函数，且$f ( x ) > 0 , g ( x ) > 0$ 恒成立，则在 $f ( x )$ 和 $\boldsymbol { g } ( \boldsymbol { x } )$ 的公共定义域上 $f ( x ) g ( x )$ 为增(或减)函数.(2)若 $f ( x ) > 0$ 且 $f ( x )$ 为增函数，则函数$\sqrt { f ( \boldsymbol { x } ) }$ 为增函数， $\displaystyle { \cdot } { \frac { 1 } { f ( { \boldsymbol { x } } ) } }$ 为减函数；若 $f ( { \boldsymbol { x } } ) > 0$ 且 $f ( x )$ 为减函数，则函数$\sqrt { f ( \boldsymbol { x } ) }$ 为减函数 $\displaystyle { \frac { 1 } { f ( { \boldsymbol { x } } ) } }$ 为增函数.

# 三、奇偶性的辨别

$1 . \ \forall { x } \in D , { f } ( \ - { x } ) \ = { f } ( { x } ) \Leftrightarrow { f } ( { x } )$ 为偶函  
数； $\forall x \in D , f ( \mathbf { \partial } - x ) = - f ( x ) \Leftrightarrow f ( x )$ 为奇函数.2.运算函数的奇偶性规律：(1)奇 $\pm$ 奇 $=$ 奇；偶 $\pm$ 偶 $=$ 偶；奇 $\pm$ 偶 $=$ 非  
奇非偶；奇 $\mathsf { x } \left( \mathsf { \Omega } \div \mathsf { \Gamma } \right)$ 奇 $=$ 偶；奇 $\times ( \div )$ 偶 $=$ 奇；  
偶 $\mathsf { x } \left( \mathsf { \Omega } \div \mathsf { \Gamma } \right)$ 偶 $=$ 偶.(2) $f ( x )$ 为偶函数，其导函数 $f ^ { \prime } ( x )$ 为奇函  
数； $f ( x )$ 为奇函数，其导函数 $f ^ { \prime } ( x )$ 为偶函数.(3)复合函数 $\scriptstyle y = f [ g ( x ) ]$ 的奇偶性：内偶

则偶,两奇为奇.

特别地，函数 $f ( \textbf { | } x \mid )$ 类型的一切函数均为偶函数.

3.常见奇偶性函数模型(1)指数型函数：假设 $a > 0$ 且 $a \neq 1$ ：

$\textcircled { 1 } f ( x ) = \pm \left( a ^ { x } - a ^ { - x } \right)$ 为奇函数； $f ( x ) =$ $\pm \left( a ^ { x } + a ^ { - x } \right)$ 为偶函数.

$\textcircled { 2 } f ( x ) = m \left( \frac { a ^ { x } + 1 } { a ^ { x } - 1 } \right) ( x \neq 0 )$ 或函数 $f ( x ) =$ $m \left( { \frac { a ^ { x } - 1 } { a ^ { x } + 1 } } \right)$ 为奇函数.

注意：写成函数 $f ( x ) = m + { \frac { 2 m } { a ^ { x } - 1 } } ( x \neq 0 )$ 或函数 $f ( x ) = m - { \frac { 2 m } { a ^ { x } + 1 } } ( m \in \mathbf { R } )$ 求导运算更方便.

(2）对数型函数：假设 $a > 0$ 且 $a \neq 1$

$\textcircled { 1 } f ( x ) = \log _ { a } \frac { 1 - x } { 1 + x } , g ( x ) = \log _ { a } \frac { 1 + x } { 1 - x } ( a >$ $0 , a \neq 1$ )都为奇函数.

$f ( x ) = \log _ { a } { \frac { x + m } { x - m } } = \log _ { a } \left( 1 + { \frac { 2 m } { x - m } } \right)$ 或函数$f ( x ) = \log _ { a } { \frac { x - m } { x + m } } = \log _ { a } \left( 1 - { \frac { 2 m } { x + m } } \right)$ 为奇函数.

$\textcircled { 2 } f ( x ) = \log _ { a } \left( \sqrt { x ^ { 2 } + 1 } + x \right)$ 或函数 $f ( x ) =$ $\log _ { a } \left( { \sqrt { x ^ { 2 } + 1 } } - x \right)$ 为奇函数.

$f ( x ) = \log _ { a } \left( b x + { \sqrt { 1 + b ^ { 2 } x ^ { 2 } } } \right) ( a > 0 , a \neq 1 )$ 为奇函数.

$\textcircled { 3 } f ( x ) = \log _ { a } \left( a ^ { b x } + 1 \right) - \frac { b } { 2 } x ( a > 0$ 且 $a \neq$ 1)为偶函数.

# 四、对称性的辨别

1. (1)若函数 $\scriptstyle y = f ( x )$ 关于直线 $x = a$ 对称，则 $f ( a + x ) = f ( a - x ) \Leftrightarrow f ( x ) = f ( 2 a - x )$ $f ( x + b ) = f ( a - x ) \Leftrightarrow f ( x )$ 关于 $\alpha = \frac { a + b } { 2 } \mathbb { X } \mathrm { \cdotp }$ 称.

(2)若函数 $\scriptstyle y = f ( x )$ 关于点 $( a , b )$ 对称,则$\begin{array} { l } { { f ( a + x ) + f ( a - x ) = 2 b \Leftrightarrow f ( x ) + f ( 2 a - x ) = } } \\ { { } } \\ { { 2 b , } } \end{array}$

$f ( x + b ) \ + f ( a - x ) = 2 c \Leftrightarrow f ( x )$ 关于点$\left( { \frac { a + b } { 2 } } , c \right)$ 对称

2.运算函数的对称性规律

(1)若函数 $\scriptstyle y = f ( x )$ 关于直线 $x = a$ 对称，则其导函数 $f ^ { \prime } ( x )$ 关于 $( a , 0 )$ 对称；(2)若函数 $\scriptstyle y = f ( x )$ 关于点 $( a , b )$ 对称,则其导函数 $f ^ { \prime } ( x )$ 关于直线 $x = a$ 对称.

# 3.三次函数的对称性

对称中心：三次函数 $f ( x ) = a x ^ { 3 } + b x ^ { 2 } + c x +$ $d ( a { \neq } 0 )$ 的对称中心为点 $\left( \ - { \frac { b } { 3 a } } , f { \biggl ( } \ - { \frac { b } { 3 a } } { \biggr ) } \right)$ ：

# 五、周期性的辨别

1.若函数 $f ( x )$ 满足 $f ( x + b ) = f ( a + x )$ ，则$f ( x )$ 为周期函数,周期为 $\mid a - b \mid$

2.两个对称性可得到周期性

(1)若函数 $\scriptstyle y = f ( x )$ 有两条对称轴 $x = a$ ，$x = b ( a < b )$ ，则函数 $f ( x )$ 是周期函数，且 $T =$ $2 ( b - a )$ ;

(2)若函数 $\scriptstyle y = f ( x )$ 的图象有两个对称中心 $\left( a , c \right) , \left( b , c \right) \left( a < b \right)$ ,则函数 $\scriptstyle y = f ( x )$ 是周期函数，且 $\scriptstyle { T = 2 ( b - a ) }$ ;

(3)若函数 $\scriptstyle { \boldsymbol { y } } = f ( { \boldsymbol { x } } )$ 有一条对称轴 ${ \boldsymbol { x } } = { \boldsymbol { a } }$ 和一个对称中心 $\left( \boldsymbol { b } , \boldsymbol { 0 } \right) \left( \boldsymbol { a } < \boldsymbol { b } \right)$ ,则函数 $\scriptstyle y = f ( x )$ 是周期函数，且 $\scriptstyle { T = 4 ( b - a ) }$ ：

# 【进阶提升】

# 题型一 抽象函数综合应用

例1已知函数 $f ( x )$ 及其导函数 $f ^ { \prime } ( x )$ 的定义域均为R,记 ${ \pmb g } ( { \pmb x } ) = { \pmb f } ^ { \prime } ( { \pmb x } )$ 若 $f { \biggl ( } { \frac { 3 } { 2 } } - 2 x { \biggr ) }$ ，$\textstyle { g ( 2 + x ) }$ 均为偶函数,则（）

${ \bf A . } f ( 0 ) = 0 \qquad { \bf B . } g \biggl ( - { \frac { 1 } { 2 } } \biggr ) = 0$ $\mathrm { C . } f ( \mathrm { ~ - 1 ~ } ) = f ( 4 ) \qquad \mathrm { D . } \ : g ( \mathrm { ~ - 1 ~ } ) = g ( 2 )$

例2已知函数 $f ( x )$ 的定义域为 $\mathbf { R }$ ，且 $f ( x _ { } )$ $+ y \big ) \ f \big ( x - y \big ) \ = f ^ { 2 } \big ( x \big ) \ - f ^ { 2 } \big ( y \big ) , \ f ( 1 ) \ = \sqrt { 3 } ,$ $f { \biggl ( } 2 x + { \frac { 3 } { 2 } } { \biggr ) }$ 为偶函数，则（）

$\mathbf { A } . f ( 0 ) = 0$   
$\ \mathbf { B } . f ( x )$ 为偶函数  
$\operatorname { C } . f ( 3 + x ) = - f ( 3 - x )$   
$\mathrm { D } . \sum _ { k = 1 } ^ { 2 0 2 3 } f ( k ) = \sqrt { 3 }$

$$
\begin{array} { l } { \displaystyle \mathbb { C } . \left( \mathrm { e } ^ { x _ { 1 } } + 1 \right) \left( \mathrm { e } ^ { x _ { 2 } } + 1 \right) \left( \mathrm { e } ^ { x _ { 1 } } + \mathrm { e } ^ { x _ { 2 } } \right) > 8 } \\ { \displaystyle \mathrm { D } . } \\ { \displaystyle \mathrm { D } . \mathrm {  { x } } _ { 1 } + 2 a < x _ { 2 } < x _ { 1 } + 2 a + \frac { 4 } { 3 } } \end{array}
$$

例5已知 ${  { g } } { \left( { \textbf { \em x } } \right) } = 8 m { \ln { \left| x \right| } } + \frac { 1 } { 4 } x ^ { 2 } - 4 x +$ cos $x , x \in \left( \mathrm { ~ - ~ } \pi , 0 \right) \cup \left( 0 , \pi \right)$ ,若对任意 $x _ { 1 } \in$ $\left( \mathbf { \Sigma } - \pi , 0 \right) \cup \left( 0 , \pi \right)$ ,都存在唯一 $x _ { 2 } \in \left( \begin{array} { l } { - \pi _ { : } } \end{array} \right.$ $0 ) \cup ( 0 , \pi ) \left( x _ { 2 } \neq x _ { 1 } \right)$ 使得 $g ( x _ { 1 } ) - x _ { 1 } g ^ { \prime } ( x _ { 1 } ) =$ $g ( x _ { 2 } ) - x _ { 2 } g ^ { \prime } ( x _ { 2 } )$ ,求正整数 $_ m$ 的最小值.

# 题型二具体函数性质的应用

例3已知 $f ( x )$ 为奇函数， $g ( x ) = f ( x ) +$ $^ { 1 , g ( \ - 2 ) = 3 }$ ，则 $f ( 2 ) =$

变式1已知函数 $f ( x ) = \ln ( \sqrt { 1 + x ^ { 2 } } -$ $x ) + 1 , f ( a ) = 4$ ，则 $f ( - a ) = . n - 1 \leq$

变式2已知函数 $f ( x ) = a x ^ { 3 } + b \sin x +$ $ \mathrm { ~ 4 ~ } ( \mathrm { ~ \boldsymbol ~ a ~ } , \mathrm { ~ \boldsymbol ~ b ~ } \in \mathrm { ~ \bf ~ R ~ } )$ ， $f \left( \mathrm { \log \left( \log _ { 2 } 1 0 \right) } \right) = 5$ ，则 $f ( \lg ( \lg 2 ) ) =$

变式3 已知函数 $ = f ( x ) =$ ${ \frac { \mid x \mid - \sin x + 1 } { \mid x \mid + 1 } } ( x \in \mathbf { R } )$ 的最大值为 $M$ ，最小值为 $m$ ，则 $M + m = \qquad .$

变式4 已知函数 $f ( x ) = ( x - 1 ) ^ { 3 } - 2 x +$ $\mathrm { e } ^ { \tau - 1 } - \mathrm { e } ^ { 1 - x }$ ，若 $f ( a ) ^ { * } = 4$ ，则 $f ( 2 - a ) =$

例4已知函数 $f ( x ) = \mathrm { e } ^ { x } ( x - a ) - x - a$ 有两个不同零点 $\mathbf { \boldsymbol { x } } _ { 1 } , \mathbf { \boldsymbol { x } } _ { 2 }$ ，且 $x _ { 1 } < x _ { 2 }$ ,则下列选项正确的是（，）） [0.α-)/

A. $a < - 2$ B. $x _ { 1 } + x _ { 2 } = 0$ 不、干关映口

#

关品

贝为心

# ·第2讲导数中的数形结合·

# 【核心知识聚焦】

# 一、函数图象的应用

1.利用函数的图象研究不等式：当不等式问题不能用代数法求解但其与函数有关时，常将不等式问题转化为两函数图象的上、下关系问题,从而利用数形结合求解.

2.利用函数的图象研究方程根的个数：当方程与基本函数有关时，可以通过函数图象来研究方程的根.

(1)方程 $f ( { \boldsymbol { x } } ) = 0$ 的根就是函数 $f ( x )$ 的图 象与 $_ x$ 轴交点的横坐标.

(2)方程 $f ( x ) = g ( x )$ 的根就是函数 $f ( x )$ 与 $\pmb { g } ( \pmb { x } )$ 图象交点的横坐标.尤其注意曲线与直线的交点问题.

# 二、函数中的极限思想

高中数学虽没有系统地学习极限的相关理论,但掌握一些简单的分析极限的方法，可以更准确地画出函数的图象，可以巧妙地解决一些选择题,其本质是特值法的思想.

洛必达法则

1.零比零 $\cdot \left( { \frac { 0 } { 0 } } \right)$ 型，即 $_ { x }$ 趋向于某数时，分子$f ( { \pmb x } )$ 、分母 $\pmb { g } ( \pmb { x } )$ 趋向于零.

若函数 $f ( x )$ 和 $\textstyle { g ( x ) }$ 满足下列条件：

$$
\operatorname* { l i m } _ { x  a } f ( x ) = 0 , \operatorname* { l i m } _ { x  a } g ( x ) = 0 ;
$$

(2)在点 $^ { a }$ 的某去心邻域内两者都可导， 且 $\pmb { \mathrm { g } } ^ { \prime } ( \pmb { x } ) { \neq } 0$ ;

(3）)limL（x） $\operatorname* { l i m } _ { x \to a } { \frac { f ^ { \prime } ( x ) } { g ^ { \prime } ( x ) } } = A \left( A \right.$ 可为实数,也可为 $\pm \infty .$ ，那么 $\operatorname* { l i m } _ { x \to a } { \frac { f ( x ) } { g ( x ) } } = \operatorname* { l i m } _ { x \to a } { \frac { f ^ { \prime } ( x ) } { g ^ { \prime } ( x ) } } = A .$

2.无穷比无穷 $\left( \frac { \infty } { \infty } \right)$ 型，即 $_ { x }$ 趋向于某数时，分子 $f ( x )$ 、分母 $g ( x )$ 趋向于无穷.

若函数 $f ( x )$ 和 $g ( x )$ 满足下列条件：

(1） $\operatorname * { l i m } _ { x \to a } f ( x ) = \infty \ , \operatorname * { l i m } _ { x \to a } g ( x ) = \infty \ ;$ (2)在点 $^ { a }$ 的某去心邻域内两者都可导，  
且 ${ \mathbf { } g ^ { \prime } ( \pmb x ) } { \neq } 0$ $\operatorname* { l i m } _ { x \to a } { \frac { f ^ { \prime } ( x ) } { g ^ { \prime } ( x ) } } = A$ （A可为实数，也可为  
$\pm \infty$ ，那么$\operatorname* { l i m } _ { x \to a } { \frac { f ( x ) } { g ( x ) } } = \operatorname* { l i m } _ { x \to a } { \frac { f ^ { \prime } ( x ) } { g ^ { \prime } ( x ) } } = A .$

# 【进阶提升】

题型一 利用图象解决不等式或方程根的选择、填空题

例1已知函数 $f ( x ) = \left\{ \begin{array} { l l } { - x ^ { 2 } + 2 x , x { \leqslant } 0 , } \\ { \ln ( x + 1 ) , x > 0 , } \end{array} \right.$ 若 $\mid f ( x ) \mid \geqslant a x$ ,则 $\pmb { a }$ 的取值范围是（ ）

A. $\left( \mathbf { \epsilon } - \infty , 0 \right]$ B.（-8,1] C.[-2,1] D.[-2,0]

例2已知关于 $_ x$ 的不等式 $m x ^ { 2 } - 2 x -$ ln $\pmb { x } < 0$ 在 $( 0 , + \infty )$ 上有唯一的整数解,则实数 $_ m$ 的取值范围为

# 题型二，函数中的极限思想应用

例3已知函数 $f ( x ) = 2 ^ { \left| \log _ { 2 ^ { x } } \right| } - \left| x - { \frac { 1 } { x } } \right|$ 则不等式 $f ( x ) > f \left( { \frac { 1 } { 2 } } \right)$ 的解集是（）

$$
\begin{array} { l } { \displaystyle \mathrm { A . \left( \frac { 1 } { 4 } , \frac { 1 } { 2 } \right) \cup ( 3 , + \infty ) } } \\ { \displaystyle \mathrm { B . \left( \frac { 1 } { 4 } , 3 \right) } } \\ { \displaystyle \mathrm { C . \left( 0 , \frac { 1 } { 2 } \right) \cup ( 2 , + \infty ) } } \\ { \displaystyle \mathrm { D . \left( \frac { 1 } { 2 } , 2 \right) } } \end{array}
$$

例4设函数 $f ( x ) ~ = ~ \ln ~ \left| ~ 2 x ~ + ~ 1 ~ \right| ~ -$ $\ln \mid 2 x - 1 \mid$ ，则 $f ( x )$ （）

A.是偶函数，且在 $\left( { \frac { 1 } { 2 } } , + \infty \right)$ 上单调递增B.是奇函数，且在 $\left. { - \frac 1 2 , \frac 1 2 } \right.$ 上单调递减

C.是偶函数,且在 $\left( - \infty , - { \frac { 1 } { 2 } } \right)$ 上单调递增D.是奇函数,且在 $\left( - \infty , - { \frac { 1 } { 2 } } \right)$ 上单调递减

例5已知函数 $f ( x ) = ( x ^ { 2 } + m x ) \cdot \mathrm { e } ^ { x } ( m$ 是不为零的常数),则（）

A.函数 $f ( x )$ 的极大值点为负B.函数 $f ( x )$ 的极小值点为正C.函数 $f ( x )$ 的极大值为正D.函数 $f ( x )$ 的极小值为负

例6已知函数 $f ( x ) = x { \bigl ( } 1 + a | x | { \bigr ) } .$ ），设关于 $_ x$ 的不等式 $f ( x + a ) < f ( x )$ 的解集为 $A$ ，若$\left[ - { \frac { 1 } { 2 } } , { \frac { 1 } { 2 } } \right] \subset A$ ，则实数 $^ a$ 的取值范围是

# 第3讲 比较大小·

# 【核心知识聚焦】

# 一、泰勒展开公式(估值）

$1 , n$ 阶泰勒公式：设函数 $f ( x )$ 在点 $x _ { 0 }$ 处的某邻域内具有 $n + 1$ 阶导数，则对该邻域内异于 $\scriptstyle { \boldsymbol { x } } _ { 0 }$ 的任意点 $_ x$ ,在 $x _ { 0 }$ 与 $_ x$ 之间至少存在一点$\xi$ ，使得

$$
{ \begin{array} { r l } & { f ( x ) = f ( x _ { 0 } ) + f ^ { \prime } ( x _ { 0 } ) \left( x - x _ { 0 } \right) + { \frac { f ^ { \prime \prime } ( x _ { 0 } ) } { 2 ! } } \ . } \\ & { \left( x - x _ { 0 } \right) ^ { 2 } + \cdots + { \frac { f ^ { ( n ) } \left( x _ { 0 } \right) } { n ! } } \left( x - x _ { 0 } \right) ^ { n } + R _ { n } \left( x \right) , } \\ & { R _ { n } ( x ) = { \frac { f ^ { ( n + 1 ) } \left( \xi \right) } { \left( n + 1 \right) ! } } ( x - x _ { 0 } ) ^ { n + 1 } , } \end{array} }
$$

上式称为 $_ n$ 阶泰勒公式.

2.麦克劳林公式：若 $x _ { 0 } = 0$ ,则泰勒公式称为麦克劳林公式,其中 $o ( x ^ { n } )$ 为 $\pmb { n }$ 阶无穷小，$\begin{array} { l } { { \displaystyle \mathbb { A } \mathbb { I } f ( x ) = f ( 0 ) + f ^ { \prime } ( 0 ) x + \frac { f ^ { \prime \prime } ( 0 ) } { 2 ! } x ^ { 2 } + \cdots } } \\ { { + \frac { f ^ { ( n ) } ( 0 ) } { n ! } x ^ { n } + o \left( x ^ { n } \right) . } } \end{array}$

3.常用的初等函数的麦克劳林公式

$( 1 ) \mathbf { e } ^ { x } = 1 + x + { \frac { x ^ { 2 } } { 2 ! } } + \cdots + { \frac { x ^ { n } } { n ! } } + o \left( x ^ { n } \right) ;$ (2)si $\mathrm {  ~ \lambda ~ } x = x - \frac { x ^ { 3 } } { 3 ! } + \frac { x ^ { 5 } } { 5 ! } - \cdots + ( - 1 ) ^ { n } .$ ${ \frac { x ^ { 2 n + 1 } } { ( 2 n + 1 ) ! } } + o { \bigl ( } x ^ { 2 n + 2 } { \bigr ) } ;$

$$
\begin{array} { c } { { ( 3 ) \cos x = 1 - \frac { x ^ { 2 } } { 2 ! } + \frac { x ^ { 4 } } { 4 ! } - \frac { x ^ { 4 } } { 6 ! } + \cdots + ( - 1 ) ^ { * } \cdot } } \\ { { \frac { x ^ { 5 } } { ( 2 n ) ! } + \sigma ( \frac { n - 1 } { n ! } ) _ { \downarrow } } } \\ { { ( 4 ) \ln { ( 1 + x ) } = x - \frac { x ^ { 2 } } { 2 } + \frac { x ^ { 3 } } { 3 } - \cdots + } } \\ { { \nonumber } } \\ { { ( - 1 ) ^ { * } \frac { x ^ { 4 - 1 } } { n + 1 } + \sigma ( x ^ { - 4 } ) \cdot } } \\ { { ( 5 ) \frac { 1 } { 1 - x } = 1 + x + x ^ { 2 } + \cdots + x ^ { 5 } + \sigma ( x ^ { 5 } ) _ { \downarrow } } } \\ { { ( 6 ) \left( 1 + x \right) ^ { * } = 1 + m x + \frac { m \left( m - 1 \right) } { 2 ! } x ^ { 2 } + \cdots } } \\ { { \ } } \\ { { + \frac { m \left( m - 1 \right) \cdots - 1 } { n ! } \ \left( m - n + 1 \right) x ^ { * } + \sigma ( x ^ { 5 } ) . } } \end{array}
$$

# 二、放缩法比较大小

1.与对数型函数有关的常见不等式有：

$$
\begin{array} { c } { { \ln ( x + 1 ) \leqslant x , \ln x \leqslant x - 1 , \ln x \geqslant 1 - \displaystyle \frac { 1 } { x } , \ln x < } } \\ { { } } \\ { { x , \ln x < \sqrt { x } , \ln x < 2 \left( x - \displaystyle \frac { 1 } { x } \right) \left( x > 1 \right) , \ln x > } } \\ { { } } \\ { { 2 \left( x - \displaystyle \frac { 1 } { x } \right) \left( 0 < x < 1 \right) . } } \end{array}
$$

2.与指数型函数有关的常见不等式有：$\mathrm { e } ^ { x } \geqslant x + 1 , \mathrm { e } ^ { x } > x , \mathrm { e } ^ { x } \geqslant \mathrm { e } x , \mathrm { e } ^ { x } < \frac { 1 } { 1 - x } \big ( x > 0 \big ) ,$ $\mathbf { e } ^ { x } < - \frac { 1 } { x } \big ( x < 0 \big ) , \mathbf { e } ^ { x } \geqslant 1 + x + \frac { 1 } { 2 } x ^ { 2 } \big ( x > 0 \big ) .$

3.与三角函数有关的常见不等式有：

sin $x \ < \ x \ ( \ x > 0 )$ ，sin $\textit { x } < \textit { x } <$ tan $_ x$ $\left( 0 < x < \frac { \pi } { 2 } \right) , \sin x \geqslant x - \frac { 1 } { 2 } x ^ { 2 } , 1 - \frac { 1 } { 2 } x ^ { 2 } \leqslant \cos x \leqslant$ 1-2m2x

# 三、构造函数比较大小

1.同构形式的比较大小：构造相同函数，  
比较不同函数值.2.构造不同函数，比较相同函数值：这类  
问题虽然可能几个数的形式不一致，但它们是

不同的函数取了相同的函数值，所以实质上是在比较不同函数差值或者商的性质.

3.构造差值函数或者商值函数.

4.先同构，再构造,再比较：当题干呈现一个较复杂的等式或者不等式关系，并没有前几类那么明显的数字时，往往可能现需要同构(变形)出一个函数之后再来比较大小.

# 【进阶提升】

# 题型一 异构数值大小比较

例1已知 $a = { \frac { 3 1 } { 3 2 } } , b = \cos { \frac { 1 } { 4 } } , c = 4 \ \sin { \frac { 1 } { 4 } }$ ，则（ ）

$$
\begin{array} { l l } { { \mathrm { A . ~ } c > b > a ~ } } & { { ~ \mathrm { B . ~ } b > a > c } } \\ { { { } } } & { { { } } } \\ { { \mathrm { C . ~ } a > b > c ~ } } & { { ~ \mathrm { D . ~ } a > c > b } } \end{array}
$$

例2设 $a = \ln 1 . 0 2 , b = \frac { 1 } { 6 0 } , c = \sin 0 . 0 2$ 则（）

$$
\begin{array} { l l } { { \mathrm { A . ~ } c > b > a ~ } } & { { ~ \mathrm { B . ~ } c > a > b } } \\ { { \mathrm { C . ~ } b > c > a ~ } } & { { ~ \mathrm { D . ~ } a > c > b } } \end{array}
$$

变式1 设 $a = \log _ { 3 } 4 , b = \log _ { 0 . 8 } 0 . 7 , c =$ $1 . 0 2 ^ { 5 1 }$ ，则 $a , b , c$ 的大小关系为（）

$$
\begin{array} { l l } { \mathrm { A . ~ } a < c < b } & { \qquad \mathrm { B . ~ } a < b < c } \\ { \mathrm { C . ~ } b < a < c } & { \qquad \mathrm { D . ~ } c < a < b } \end{array}
$$

变式2 设 $a = 0 . 1 \mathrm { e } ^ { 0 . 1 }$ ， $b = { \frac { 1 } { 9 } }$ , $c =$ $- \ln { 0 . 9 }$ ，则（

A. $a < b < c$ B.c<b<a $\complement . c < a < b$ D. $a < c < b$

# 题型二有等式条件或不等式条件的大小比较

例3设 $x , y , z$ 为正数,且 $2 ^ { x } = 3 ^ { y } = 5 ^ { z }$ ，则（）

$$
\begin{array} { r } { \mathrm { A } . 2 x < 3 y < 5 z \qquad \mathrm { B } . 5 z < 2 x < 3 y } \end{array}
$$

$$
\mathrm { C } . 3 y < 5 z < 2 x \qquad \mathrm { D } . 3 y < 2 x < 5 z
$$

例4已知 $9 ^ { \prime \prime } = 1 0 , a = 1 0 ^ { \prime \prime } - 1 1 , b = 8 ^ { \prime \prime } -$ 9,则（）

$$
\begin{array} { l l } { { \mathrm { A } . \ : a > 0 > b } } & { { \qquad B . \ : a > b > 0 } } \\ { { \mathrm { C } . \ : b > a > 0 } } & { { \qquad \mathrm { D } . \ : b > 0 > a } } \end{array}
$$

例5已知 $a < 5$ 且 $a \mathrm { e } ^ { 5 } = 5 \mathrm { e } ^ { a }$ ， $b < 4$ 且$b \mathrm { e } ^ { 4 } = 4 \mathrm { e } ^ { b } , c < 3$ 且 $c \mathrm { e } ^ { 3 } = 3 \mathrm { e } ^ { c }$ ，则（）

$$
\begin{array} { l l } { \mathrm { A . ~ } c < b < a } & { \qquad \mathrm { B . ~ } b < c < a } \\ { \mathrm { C . ~ } a < c < b } & { \qquad \mathrm { D . ~ } a < b < c } \end{array}
$$

变式3 已知实数 $m , n , p \in ( 0 , 1 )$ ，且 $m = \ln \frac { m } { 2 } + 2 , n = \ln \frac { n } { 2 } + 3 , p = \ln \frac { p } { 3 } + 3$ ，则

A.p<n<m B.n<m<pC. $m < p < n$ D.n<p<m

变式4 若 $2 ^ { a } + \log _ { 2 } { a } = 4 ^ { b } + 2 \log _ { 4 } { b }$ ，则

A. $a > 2 b$ B. $a < 2 b$   
C. $a > b ^ { 2 }$ $\mathrm { D } , a < b ^ { 2 }$

变式5 已知e为自然对数的底数， $a$ ， $b$ 均为大于1的实数，若 $a \mathbf { e } ^ { \circ + 1 } + b < b \ln b$ ， 则（

$$
\begin{array} { l l } { \mathrm { A . ~ } b < \mathsf { e } ^ { a + 1 } } & { \mathrm { ~ B . ~ } b > \mathsf { e } ^ { a + 1 } } \\ { \mathrm { C . ~ } a b < \mathsf { e } } & { \mathrm { ~ D . ~ } a b > \mathsf { e } } \end{array}
$$

例6已知 $5 ^ { s } < 8 ^ { 4 }$ ， $1 3 ^ { 4 } < 8 ^ { 5 }$ .设 $a = \log _ { s } 3$ ，$b = \log _ { 8 } 5 , c = \log _ { 1 3 } 8$ ,则（）

A. $a < b < c$ $\mathrm { B } , b < a < c$ （ $\therefore b < c < a$ $\mathsf { D } . \mathsf { c } < a < b$

# …第4讲 函数图象中的距离最值问题···

# ）【核心知识聚焦】

# 一、求函数图象中的距离最值方法

1.将距离代数表示，构造距离函数,通过求函数最值解决问题2.数形结合，借助切线找到距离最值.

# 二、常见的三类距离

1.函数图象上的点到直线的距离最值

(1)设函数上任一点坐标，用点到直线距离公式表示出距离函数,求最值.

(2)若两个动点 $P , Q$ 分别在函数 $f ( x )$ 和直线 $l$ 上,那么 $\left| \ P Q \right. \left| \mathbf { \Lambda } _ { \operatorname* { m i n } } \right.$ 为当 $f ( x )$ 在点 $P$ 处的

切线与直线 $\mathbf { \xi } _ { l }$ 平行时， $P$ 到直线 $\mathbf { \xi } _ { l }$ 的距离.

2.相异曲线上两点间距离

(1)分别设出 $f ( x )$ 上任一点 $P ( x _ { 1 } , y _ { 1 } )$ ，$g ( x )$ 上任一点 $Q \left( { { x } _ { 2 } } , { { y } _ { 2 } } \right)$ ,用两点间距离公式表示出距离函数，求最值.

(2)数形结合,特别地当两曲线有对称轴时,可借助与对称轴平行的切线,求出距离最值. 顺，

# 3.相异曲线图象上的水平距离最值

分别设出 $f ( x )$ 上任一点 $P ( x _ { 1 } , m )$ ， ${ \pmb g } ( { \pmb x } )$ 上任一点 $Q ( x _ { 2 } , m )$ ,则相异曲线图象上的水平距离最值即为 $\mid x _ { 1 } - x _ { 2 } \mid$

# 【进阶提升】

题型一函数图象上的点到直线的距离最值

例1已知 $P$ 是自线 $\mathbf { y } = \sin \textbf { \em x } + \cos \textbf { x }$ $\left( \mathbf { x } \in \left[ 0 , \frac { 3 \mathit { \Pi } _ { \overline { { 4 } } } } { 4 \mathit { \Pi } } \right] \right)$ 上的动点,点 $Q$ 在直线 ${ \pmb x } \div { \pmb y } \mathrm { - }$ $\mathbf { 6 = 0 }$ 上运动，则当 $\mid P Q \mid$ 取最小值时，点 $P$ 的坐标为（）

$$
\mathbf { A } - { \frac { \pi } { 4 } } \qquad \mathbf { B } . { \frac { \pi } { 3 } } \qquad \mathbf { C } . { \frac { \pi } { 2 } } \qquad \mathbf { D } . { \frac { 2 \pi } { 3 } }
$$

变式1 $\begin{array}{c} \begin{array} { r c l c r c l } { \equiv } & { \mathbf { \dot { \varepsilon } } } & { } & { } & { } & { } & { } & { } \\ { \equiv } & { \mathbf { \dot { \varepsilon } } } & { } & { } & { } & { } & { } & { } \\ { \quad } & { } & { } & { } & { } & { } & { } & { } \\ { \quad } & { } & { } & { } & { } & { } & { } & { } & { } \end{array} +  \end{array}$ $\left( { \frac { \underline { { x } } } { \mathrm { e } ^ { z } } } - \sigma - 2 \right) ^ { . }$ （ ${ \boldsymbol { \alpha } } \in \mathbf { R }$ )，则 $\mathcal { T }$ 的最小值为

是（ ）

$$
\begin{array} { l l } { { { \bf { A . 2 } } { \sqrt { 2 } } \qquad } } & { { { \bf { B . 9 - 4 } } { \sqrt { 2 } } } } \\ { { { \bf { C . 1 } } + 2 { \sqrt { 2 } } \qquad } } & { { { \bf { D . 8 } } } } \end{array}
$$

例4在同一平面直角坐标系中， $P , Q$ 分别是函数 $f ( \mathbf { \lambda } _ { \mathbf { x } } ) \ = a \mathfrak { r e } ^ { \mathfrak { x } } \ - \ \ln \left( \ a x \right)$ 和 ${ \boldsymbol { g } } \left( { \boldsymbol { \mathbf { \mathit { x } } } } \right) =$ 2ln(x-1)图象上的动点,若对任意a>0,有$| P Q \ | \geqslant m$ 恒成立，则实数 $_ m$ 的最大值为

题型三相异曲线图象上的水平距离最值例5设 $f ( x ) = \left\{ { \displaystyle \frac { \ln x , x > 1 } { 2 } } , \right. $ 若 $\pmb { m } < \pmb { n }$ ，，且 $f ( m ) = f ( n )$ ，则 $\textbf { \em n } - \textbf { \em m }$ 的最小值为

A√B.2C²D.

# 题型二相异曲线上两点间距离

例2(多选题)设点 $P$ 在函数 $f ( x ) =$ $\frac { 1 } { 2 } e ^ { z }$ 图象上，点 $\pmb { Q }$ 在函数 ${ \pmb g } ( { \pmb x } ) = { \bf { l n } } ( 2 { \pmb x } )$ 的图象上,则下列说法正确的是（ ）

$\mathbf { A } _ { - } f ( \pmb { x } )$ 与 $\pmb { \mu } ( \pmb { x } )$ 图象关于 $\mathbf { y } = \pmb { x }$ 对称  
$\mathbb { B } _ { - } f ( \pmb { x } )$ 与 $\underline { { \sigma } } ( \underline { { \boldsymbol { x } } } )$ 图象关于 $\mathbf { y } = - \mathbf { \nabla } \mathbf { x }$ 对称  
C. $\mid P Q \mid$ 最小值为 $\sqrt { 2 } \left( 1 - \ln 2 \right)$   
D. $\mid P Q \mid$ 最小值为 $\mathbf { I } - \mathbb { I } \mathbf { n } \ 2$   
例3已知 ${ \pmb x } , m , n \in \mathbb { R }$ 且 $x \neq 0 , m ^ { 2 } + n ^ { 2 } =$

1,则 $( 1 + x - m ) ^ { 2 } + \left( 1 - x - { \frac { 2 } { x } } + n \right) ^ { 2 }$ 的最小值

变式2 设函数 $f ( x ) = \mathrm { e } ^ { x } , g ( x ) = 1 +$ $\ln x$ ，若 $f ( a ) = g ( b )$ ，则 $b - a$ 的最小值为

变式3 直线 $y = a$ 分别与直线 $y =$ $2 ( x + 1 )$ ，白线 $y = x + \ln x$ 交于点 $A , B$ ，则$\mid A B \mid$ 的最小值为（）

A.3B.2 $\mathrm { C } . \frac { 3 \sqrt { 2 } } { 4 } \qquad \mathrm { D } . \frac { 3 } { 2 }$

变式4 已知函数 $f ( x ) = \mid \mathrm { e } ^ { x } - 1 \mid$ ， $\scriptstyle x _ { \mathrm { i } } \leqslant 0 \leqslant x _ { \mathrm { 2 } } \leqslant 2$ ，函数 $f ( x )$ 的图象在点 $A ( x _ { 1 }$ $f ( x _ { 1 } )$ )和点 $B ( x _ { 2 } , f ( x _ { 2 } )$ )处的两条切线互 相垂直，且分别交 $y$ 轴于 $M , N$ 两点，则 $\mid M N \mid$ 的取值范围是

# 第5讲 导数与三角函数

# 【核心知识聚焦】

# 【进阶提升】

由于三角函数具有周期性，无法通过多次求导使三角函数消失，使得后续问题的处理比较困难，从而造成学生思维上的难度.可从以下几个角度来突破此类问题的难点.

# 一、分段讨论

# 题型一 分段讨论

例1已知函数 $f ( x ) = \ln x - x + 2 \sin x$ ，证明：

(1) $f ( x )$ 在区间 $( 0 , \pi )$ 存在唯一极大值 点； $( 2 ) f ( x )$ 有且仅有2个零点.

1.以 $- { \frac { \pi } { 2 } } , 0 , { \frac { \pi } { 2 } } , \pi , \cdots$ ·为端点分区间讨论；（利用角所在象限作为分类讨论标准）

2.以三角函数的最值点为端点分段讨论.（利用三角函数的有界性作为分类讨论标准）

# 二、巧用放缩，消去三角函数(也可考虑泰勒展开式的应用）

1.正弦函数：当 $x > 0$ 时， $x < \sin { \ x } < x -$ x

2.余弦函数 $1 - { \frac { 1 } { 2 } } x ^ { 2 } \leqslant \cos x .$

3.正切函数：当 $x \in \left( 0 , \frac { \pi } { 2 } \right)$ 时,sin x <x<tan $_ { x }$ ：

4.正、余弦函数的有界性： $\sin \ x \in [ - 1$ ，1],cos ${ \boldsymbol { x } } \in \left[ \begin{array} { l } { - 1 , 1 } \end{array} \right]$

# 三、分离函数

将含有三角函数的式子放到一起.

![](images/8feffd4dd1693915e08c428e3316e960f6f0702d265d4d569c11b106698a915c.jpg)

#

与四丽：点

1

变式1 已知函数 $f ( x ) = \cos x + { \frac { 1 } { 4 } } x ^ { 2 } - 1 .$

(1）证明： $f ( x ) \mathop { \le 0 } , x \in \left[ { - \frac { \pi } { 2 } , \frac { \pi } { 2 } } \right]$ （2）判断 $\scriptstyle y = f ( x )$ 的零点个数，并给出 证明过程.

# 函离长三

# 题型二巧用放缩

例2 (1)求证：当 $x > 0$ 时， $\mathrm { e } ^ { x } > \frac { 1 } { 2 } x ^ { 2 } + x +$ 1；

(2)若关于 $_ x$ 的方程 $: \frac { \mathrm { e } ^ { x } - 1 } { x } = a \sin { x } + 1 \frac { } { }$ 在 $( 0 , \pi )$ 内有解，求实数 $^ a$ 的取值范围.

例3已知函数 $f ( x ) = \sin x + e ^ { * } + a \mathrm { l n } \left( x \right)$ +1).

（1）当 $a = - 2$ 时，求函数 $\textstyle f ( \pmb { \tau } )$ 在(-1.0]上的最小值;

(2)若 $f ( \boldsymbol { \textbf { z } } ) \geqslant 1$ 恒成立，求实数 $\mathbf { \Delta } _ { \mathbf { a } }$ 的值

题型三分离函数：将含有三角函数的式子放到一起

例4已知函数 $f ( x ) = \sin x - 2 a x , a \in \mathbf { R }$ $( 1 ) a \geqslant \frac { 1 } { 2 }$ 时，求函数 $f ( x )$ 在区间 $[ 0 , \pi ]$ 上的最值；(2)若关于 $\mathcal { Z }$ 的不等式 $f ( x ) \leqslant a x \cos x .$ 在区间 $( 0 , + \infty$ )上恒成立，求 $a$ 的取值范围

![](images/c8a2f84c8e306e400993cab9331f162ac01fe5169f1d4d0479ef75425d8c9b68.jpg)

#

# ?

#

0<

# 【核心知识聚焦】

# 一、逐项比较

对于不等式 $\sum _ { i \mathop { = } 1 } ^ { n } a _ { i } < f ( n ) \left( \mathit { \Omega } > f ( n ) \right) ,$ ，可将$f ( n )$ 看作另一个数列的前 $n$ 项和,计算该数列的通项公式 $b _ { n }$ ，证明 $\forall n \ \in \textbf { N } ^ { \bullet } \ , a _ { n } \ < \ b _ { n }$ （或$a _ { n } \ > b _ { n }$ ）即可.

# 二、数列单调性

对于不等式 $\sum _ { i = 1 } ^ { n } a _ { i } < f ( n )$ 记 $g ( n ) \ = \ \sum _ { i = 1 } ^ { n } a _ { i } \ -$ $f ( n )$ ，先验证 $g ( 1 ) < 0$ ,如果数列 $\textstyle { \left| { \boldsymbol { g } } ( { \boldsymbol { n } } ) \right| }$ 单调递减,则问题得证. $( \sum _ { i = 1 } ^ { n } a _ { i } > f ( n )$ 类似可证)

# 三、合理放缩

利用题目中前几问中涉及的已证不等式或者恒成立不等式,进行数列的放缩.

# 【进阶提升】

# 题型一 逐项比较

例1已知函数 $f ( x ) = { \frac { 1 - x } { a x } } + \ln x .$

(1)若 $f ( x ) \geq 0$ 对 $\forall { x } > 0$ 恒成立,求 $^ a$ 的值；(2)求证： $\ln \left( n + 1 \right) > { \frac { 1 } { 2 ^ { 2 } } } + { \frac { 2 } { 3 ^ { 2 } } } + \cdots + { \frac { n - 1 } { n ^ { 2 } } }$ $( n \in \mathbf { N } ^ { * } \mathbf { \Lambda } )$

变式1 已知函数 $f ( x ) = \ln ( x + 1 ) -$ $x , h ( x ) = { \frac { x ^ { 2 } + x + m } { x + 1 } } { \big ( } m \in \mathbf { R } { \big ) } .$

(1）若对 $\forall x > 0 , f ( x ) + h ( x ) > 1$ 恒成立，求 $m$ 的取值范围；(2)求证： $\forall n \in \mathbf { N } ^ { * }$ ， $\mathrm { e } ^ { \frac { 1 } { 2 } + \frac { 1 } { 3 } + \cdots + \frac { 1 } { n + 1 } } < n +$

# 题型二利用数列单调性

例2设函数 $f ( x ) = ( x - 1 ) ^ { 2 } + b \ln { x }$ ，其中$b$ 为常数.

(1)判断函数 $f ( x )$ 在定义域上的单调性；

(2)求证： ${ \frac { 1 } { 3 ^ { 2 } } } + { \frac { 1 } { 4 ^ { 2 } } } + \cdots + { \frac { 1 } { n ^ { 2 } } } < \ln \left( n + 1 \right)$ $( x \geqslant 3 , n \in \mathbf { N } ^ { \bullet } )$

# 题型三合理放缩

例3已知函数 $f ( x ) = \sin x - \ln ( x + 1 )$

(1)求证：当 $x \in \left( { - 1 , \frac { \pi } { 2 } } \right)$ 时 $f ( x ) \geq 0$

(2）求证 $\frac { 1 } { 2 } \ln \left( n + 1 \right) < \sin \frac { 1 } { 2 } + \sin \frac { 1 } { 4 } +$ $\sin { \frac { 1 } { 6 } } + \cdots + \sin { \frac { 1 } { 2 n } } < { \frac { 1 } { 2 } } \ln n + \ln 2 { \bigl ( } n \in \mathbf { N } ^ { * } { \bigr ) } .$

例4已知 $f ( x ) = \mathrm { e } ^ { x } - a x - 1 \left( a \in \mathbf { R } \right)$

(1)若 $f ( x ) \geq 0$ 对 ${ \boldsymbol { x } } \in \mathbf { R }$ 恒成立，求实数 $a$ 范围；

(2)求证：对 $\forall n \in \mathbf { N } ^ { \ast }$ ，都有 $\left( { \frac { 1 } { n + 1 } } \right) ^ { n + 1 } +$ $\left( { \frac { 2 } { n + 1 } } \right) ^ { n + 1 } + \left( { \frac { 3 } { n + 1 } } \right) ^ { n + 1 } + \cdots + \left( { \frac { n } { n + 1 } } \right) ^ { n + 1 } < 1 .$

变式2 例4中（2）求证：对 $\forall n \in \mathbf { N } ^ { \cdot }$ 都有+1 Vi! <e

变式3 例4(2)求证：对 $\forall n \geq \mathbf { N } ^ { \cdot }$ ，都 $\left( { \frac { 1 } { n + 1 } } \right) ^ { n + 1 } + \leq$ $\left( { \frac { 2 } { n + 1 } } \right)$ n+l 有sin X $\sin \left( { \frac { 3 } { n + 1 } } \right) ^ { n + 1 } + \cdots + \sin \left( { \frac { n } { n + 1 } } \right) ^ { n + 1 } < { \frac { 1 } { \mathrm { e } - 1 } } .$

# 第7讲 导数与最值··

# 【核心知识聚焦】

# 已知函数最值求参数

# 一、假设法

如果是闭区间的最值,那么最值只可能出现在端点及极值点处，那么就分类讨论各点处取题目条件中的最值,先求出参数值，再验证此时真的在该点处取得最值.

该法只适用于端点、极值点明确且个数较少的情况.

# 二、含参讨论

讨论出来的最值 $=$ 题目条件给的最值；可以用最值来得到参数范围的必要条件，缩小参数范围，可能会减少讨论的情况，甚至不讨论.

# 三、转化为不等式恒成立且“ $=$ ”能取的问题

即：(1)不等式恒成立；（2） ${ } ^ { \mathfrak { a } } f ( x ) =$ 最值” 方程有解,然后求交集,算出参数值或范围.

一般情况，方法一适用范围窄;方法二是常规思路;方法三是剑走偏锋.

# 【进阶提升】

# 题型一 已知最值求参数的三种类型

例1已知函数 $\varrho \left( { \mathrm {  ~ } } x \right) = - x + { \mathrm { e } } ^ { x } { \mathrm { c o s } } x - m$ 在 $\left[ 0 , { \frac { \pi } { 2 } } \right]$ 上的最大值为0,求实数 $m$ 的值

例2已知函数 $f ( x ) = x \mathrm { e } ^ { - a x } - \ln x + a x - 1$ （ $\mathbf { \boldsymbol { a } } \in \mathbf { \mathbf { R } } \mathbf { \boldsymbol { \mathrm { ~  ~ } } } ,$ ，其中 $^ { \mathrm { e } }$ 为自然对数的底数.若 $f ( x )$ 的最小值为0,求 $^ a$ 的最大值.

例3已知函数 $f ( x ) = 1 + \ln x + k x ( k \in$ $\mathbf { R } )$ ：

(1)求 $f ( x )$ 的单调区间;

(2)若函数 $F ( x ) = \left| \mathrm { e } ^ { x } - { \frac { f ( x ) } { x } } \right|$ 的最小值为0,求实数 $k$ 的取值范围.

TD

# 题型二最值存在性问题

例4已知函数 $f ( x ) = 3 x \ln x - a x ^ { 3 } + 6 x$ $\left( a > 0 \right)$ ：

(1）若 $f ^ { \prime } ( x )$ 的零点有且只有一个，求 $^ { a }$ 的值；(2)若 $\textstyle f ( x )$ 存在最大值,求 $^ { a }$ 的取值范围.

例5已知函数 $f ( x ) = a x ( \ln x - 2 ) -$ $e ^ { 1 - s }$

(1)当 $a = - 1$ 时,求曲线 $\scriptstyle y = f ( x )$ 在(1,$f ( 1 ) )$ 处的切线方程；

(2)若 $f ( x )$ 存在最小值 $m$ ，且 $m + 3 a \leqslant 0$ ，求 $^ a$ 的取值范围.

未生，志

# 【核心知识聚焦】

比值代换，差值代换，它不仅可以解决很多极值点偏移问题，还可以解决很多其他的多变量问题、比值代换多用于对数式函数，差值代换多用于指数式函数.通过比差值代换，可以将多变量问题转化为单变量问题来处理，达到消元的效果.常见的变换处理方法：

1.假设 $\cdot \frac { x _ { 1 } } { x _ { 2 } } = \iota \Longrightarrow x _ { 1 } = \iota x _ { 2 }$ 或 $x _ { 1 } - x _ { 2 } = \iota \Longrightarrow x _ { 1 } =$ $t + x _ { 2 }$ ，根据题干条件找寻 $x _ { 2 }$ 与 $\iota$ 的关系，从而 实现比差值代换.

2.对数减法,指数除法： $\ln x _ { 1 } - \ln x _ { 2 } = \ln { \frac { x _ { 1 } } { x _ { 2 } } }$ 或 $\vdots \frac { \ln x _ { 1 } - \ln x _ { 2 } } { x _ { 1 } - x _ { 2 } } , \frac { \mathrm { e } ^ { x _ { 1 } } } { \mathrm { e } ^ { x _ { 2 } } } = \mathrm { e } ^ { x _ { 1 } - x _ { 2 } } .$

3.齐次分式：例如 $| \frac { x _ { 1 } - x _ { 2 } x _ { 1 } ^ { 2 } - x _ { 2 } ^ { 2 } } { x _ { 1 } + x _ { 2 } \ x _ { 1 } x _ { 2 } }$

4.合分比结构：如果 ${ \frac { a } { b } } = { \frac { c } { d } }$ ，则+=c+d ， $( a \neq b , c \neq d )$ ·

5.非对称型：如 $x _ { 1 } \pm k x _ { 2 } > 2 x _ { 0 }$ 或商型结构 $\displaystyle { \frac { x _ { 1 } } { x _ { 2 } } }$ 或分式型等。

# 【进阶提升】

# 题型一对数减法，指数除法

例」已知函数 $\begin{array} { r } { f ( x ) = \mathrm { e } ^ { x ^ { - 2 } } - a \mathrm { l n } ( x - 1 ) } \end{array}$ (1)当 $a = 1$ ，研究 $\boldsymbol { \mathscr { f } } ( \boldsymbol { x } )$ 的单调性；

（2）令 $\begin{array} { r } { \varrho \left( x \right) = \frac { x } { \int \left( x + 2 \right) + a \mathsf { l n } \left( x + 1 \right) } } \end{array}$ ，若存在 $x _ { 1 } < x _ { 2 }$ 使得 $\boldsymbol { \wp } \left( \boldsymbol { \mathscr { x } } _ { 1 } \right) = \boldsymbol { \mathscr { g } } \left( \boldsymbol { \mathscr { x } } _ { 2 } \right)$ ,求证 $\mathsf { I n \ } \boldsymbol { x } _ { 2 }$ 一$\ln ( { \mathrm { ~ l ~ } } - x _ { 1 } ) > \ln { \mathrm { ~ } } 3 .$

例2已知函数 $f ( x ) ~ = a \mathrm { e } ^ { - x } ~ + ~ \mathrm { l n } ~ x ~ - ~ \mathrm { l }$ $( a \in \mathbf { R } )$ ：

(1)当 $a \leqslant \mathbf { e }$ 时，讨论函数 $\mathcal { I } ( x )$ 的单调性；

(2)若函数 $\textstyle f ( x )$ 恰有两个极值点 $x _ { 1 } , x _ { 2 }$ $\left( x _ { 1 } < x _ { 2 } \right)$ ，且 $x _ { 1 } + x _ { 2 } { \leqslant } 2 \ln 3$ ，求 $\cdot \frac { x _ { 2 } } { x _ { 1 } }$ 的最大值.

变式1 已知函数 $\begin{array} { l } { \displaystyle { f ( x ) = \mathrm { e } ^ { x } - \frac { 1 } { 2 } a x ^ { 2 } - } } \end{array}$ $2 a x$ ，其中 $a \in \mathbf { R }$ （1）若函数 $f ( x )$ 在 $[ 0 , + \infty )$ 上单调递增，求 $^ a$ 的取值范围；(2）若函数 $f ( x )$ 存在两个极值点 $x _ { 1 } , x _ { 2 }$ $( x _ { 1 } < x _ { 2 }$ ，当 $x _ { 1 } + x _ { 2 } \in \left[ 3 \ln 2 - 4 , \frac { 5 - 3 { \mathrm e } } { { \mathrm e } - 1 } \right]$ 时，求之+2 的取值范围.

# 题型二分式型,非对称型

例3已知函数 $\begin{array} { r } { g \left( \mathbf { \Delta x } \right) ~ = ~ \mathrm { e } ^ { 2 x } ~ + ~ a x ^ { 2 } ~ + ~ 2 \mathrm { e } ^ { 2 } x } \end{array}$ $( a \in \mathbf { R } )$ 有两个极值点为 $x _ { 1 } , x _ { 2 } \big ( x _ { 1 } < x _ { 2 } \big )$ ：

(1)求实数 $^ { a }$ 的取值范围;

(2)求证： $1 + \frac { 2 \mathrm { e } ^ { 2 } } { \mid a \mid } < x _ { 1 } + x _ { 2 } < \ln \frac { \mid a \mid } { 2 } .$

例4已知函数 $f ( x ) = \left( x ^ { 2 } + m x + n \right) \mathrm { e } ^ { x }$

(1)若 $m = n = 0$ ，求 $\textstyle f ( x )$ 的单调区间； (2)若 $m = a + b + 2 , n = a ^ { 2 } + b ^ { 2 } + 2$ ，且 $\mathcal { I } ( x )$ 有两个极值点，分别为 $x _ { 1 }$ 和 $x _ { 2 } \left( \begin{array} { l l } { x _ { 1 } } & { < x _ { 2 } } \end{array} \right)$ ，求 $\frac { f ( x _ { 2 } ) - f ( x _ { 1 } ) } { \mathrm { e } ^ { x _ { 2 } } - \mathrm { e } ^ { x _ { 1 } } }$ 的最小值.

巴蜀智库能力进阶二轮复习教通材 + 过关 + 老把点

![](images/dd072482754f281dd76768a3bd0cb3ad38e08322686ebff4903ba0500e34eee2.jpg)