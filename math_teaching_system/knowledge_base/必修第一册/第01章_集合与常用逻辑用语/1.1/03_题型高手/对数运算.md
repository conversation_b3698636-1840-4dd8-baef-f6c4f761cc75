---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 中等
estimated_study_time: 15
source_file: L1-9-对数运算.md
title: L1-9-对数运算
type: problem_type
---

# 对数运算

一本一讲，共26讲（建议5个月学习）

7个知识点+6个题型+24个视频

每周一讲（建议学习时间90分钟）

![](images/de8d38ad56198baf614d4c7faee9ce8e6135b3a6179b3c73d9b3647c8963e14a.jpg)

# 视频内容研发团队

学而思优秀老师

学而思优秀老师和一线高级教师联合创作本书试题，并精心录制讲解视频学而思图书APP扫码即可观看

![](images/d8657fc8fd410a11b5e329bce3728fd148d7e6820f2f306b48ec8b0bd919e059.jpg)

# 傅博宇 老师

毕业于北京大学元培学院  
网校和北大数院高考数学研究联合课题组成员；  
网校高中创新产品部负责人；  
荣获学而思网校“桃李满天下奖”“出类拔萃奖”等；腾讯网中国好老师；  
青少年教育导师认证；  
科学家长观体系的创立者

鼎

# 王侃老师

毕业于北京大学数学系  
学而思网校高中数学教研奠基人；  
学而思网校高中数学S级教师；  
荣获学而思网校“突出贡献奖” “桃李天下奖”等；擅长总结题型特点，提炼思想方法；  
擅长分层教学，因材施教

![](images/8d1ccf2ebadddcc190f8106e92603aeea1d81324c1279a7ccf318507a1f8b100.jpg)

# 付恒岩 老师

毕业于大连理工大学  
网校高中部理科主讲岗后培训师；  
2020年荣获学而思网校最具魅力奖；  
2019年、2020年荣获学而思网校诲人不倦奖；2020年荣获学而思网校高考优秀评卷人；  
2021年担任新浪教育高考数学直播解析特邀嘉宾；“停课不停学”公益课高中数学主讲老师

![](images/ff9b7fa4a62a2b4dfb0d09054d8475a7d649616d5ba4a12c13ff20b7cb9c055e.jpg)

# 武洪姣 老师

14年线上线下教学经验；  
学而思网校高中理科教研负责人；  
学而思高中数学特级教师;  
在教学的过程中擅长归纳题型，方法和技巧;  
在高中数学模块中最擅长讲解圆锥曲线和导数；  
无论你是从小数学不好，还是数学一直拔尖，都可以在武老师的课堂上收获很多

1级

# 对数运算

一本一讲，共26讲（建议5个月学习）

7个知识点 $+ 6$ 个题型 $+ 2 4$ 个视频每周一讲（建议学习时间90分钟）

2199-e1+37 = 2×2- e° +(8)²二 4-1+4=7

# 预习篇

第1讲集合的概念与表示第2讲集合的关系与运算第3讲解不等式  
第4讲函数的概念与三要素第5讲函数的单调性（一）第6讲函数的奇偶性（一）第 $7$ 讲指数幂运算与幂函数第8讲指数函数  
第9讲对数运算  
第10讲对数函数

<html><body><table><tr><td>模块1</td><td>对数的概念 2</td></tr><tr><td>模块2</td><td>对数的运算 9</td></tr><tr><td>模块3</td><td>换底公式 13</td></tr></table></body></html>

# 提升篇

第11讲集合重难点专题  
第12讲常用逻辑用语  
第13讲基本不等式  
第14讲函数三要素专题  
第15讲函数的单调性（二）  
第16讲函数的奇偶性（二）  
第17讲抽象函数  
第18讲指数幂运算与指数函数  
第19讲对数运算与对数函数  
第20讲函数与方程  
第21讲恒成立与存在性问题  
第22讲三角函数基本概念与诱导公式  
第23讲三角函数的图象与性质  
第24讲三角公式的应用技巧  
第25讲三角恒等变换重点题型  
第26讲正弦型函数的图象与性质  
参考答案

# 对数运算

# 直击课堂

<html><body><table><tr><td>知识模块</td><td>知识点</td><td>对应例题</td><td>星标统计</td></tr><tr><td rowspan="4">对数的概念</td><td>对数的概念</td><td>例1</td><td rowspan="9">★4道题 ★★8道题 ★★★8道题 ★★★★1道题</td></tr><tr><td>指数与对数的互化</td><td>例2</td></tr><tr><td>对数的性质</td><td>例3</td></tr><tr><td rowspan="2">积的对数</td><td>例4</td></tr><tr><td></td></tr><tr><td rowspan="3">对数的运算</td><td>商的对数</td><td rowspan="3">例5</td></tr><tr><td>幂的对数</td></tr><tr><td>换底公式及其推论公式</td></tr><tr><td>换底公式</td><td></td><td>例6</td><td></td></tr></table></body></html>

# 学习目标

$\textcircled{9}$ 通过实例推导对数的运算性质，准确地运用对数运算性质进行运算、求值、化简，并掌握化简求值的技能.

$\textcircled{2}$ 运用对数运算性质解决有关问题.

$\textcircled { 6 }$ 培养学生分析、综合解决问题的能力.

$\textcircled{4}$ 培养学生数学应用的意识和科学分析问题的精神和态度.

# 模块1对数的概念

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# 1对数的概念

如果 $a ^ { b } = N ( a > 0$ ,且 $a \neq 1$ ),那么我们把 $b$ 叫做以 $a$ 为底 $N$ 的对数，记作 $b = \log _ { a } N$ ,其中 $a$ 叫做对数的底数， $N$ 叫做真数.

图示：

根据对数的定义，对数 $\log _ { a } N ( a > 0$ ，且 $a \neq 1$ )具有如下性质：

$\textcircled{1}$ 0和负数没有对数，即 $N > 0$ $\textcircled{2}$ 1的对数为0，即log1=0；$\textcircled{3}$ 底的对数等于1，即logaa=1.

# ②对数恒等式

$$
a ^ { \log _ { a } N } = N
$$

# 常用对数

以10 为底的对数叫做常用对数,为了简便,通常把底数10 略去不写,并把“log”简记为“ $\mathrm { l g }$ ”，即 $\log _ { 1 0 } N = \lg N .$

# $\textcircled { 2 }$ 自然对数

以e为底的对数称为自然对数，为了简便,通常把底数e略去不写,并把“log”简记为“ln”,即 $\log _ { \mathrm { e } } N = \ln \ N .$ 其中 $\mathrm { e } \approx 2 . 7 1 8 \ 2 8 .$

# 精讲精练

# 拍照批改秒判对错

# 例1★

下列指数式与对数式互化不正确的一组是(

A ${ { 1 0 } ^ { \circ } } = 1$ 与 $\lg 1 = 0$ B. $2 7 \ ^ { - { \frac { 1 } { 3 } } } = { \frac { 1 } { 3 } }$ $\log _ { 2 7 } { \frac { 1 } { 3 } } = - { \frac { 1 } { 3 } }$ C. $\log _ { ( { \mathrm { ~ - 5 ~ } } ) } 2 5 = 2$ 与 $\left( \mathbf { \partial } - 5 \right) ^ { 2 } = 2 5$ D. $\log _ { 5 } 5 = 1$ 与 $5 ^ { 1 } = 5$

# 学习笔记

# 变式1

# （1）★

下列指数式与对数式互化不正确的一组是( ）

A. $\mathbf { e } ^ { 0 } = 1$ 与 $\ln 1 = 0$ B. $\log _ { 3 } 9 = 2$ 与 $9 ^ { \frac { 1 } { 2 } } = 3$ C. $8 \ ^ { - { \frac { 1 } { 3 } } } = { \frac { 1 } { 2 } }$ 与log8 $\log _ { 8 } { \frac { 1 } { 2 } } = - { \frac { 1 } { 3 } }$ D. $\log _ { 7 } 7 = 1$ 与 $7 ^ { 1 } = 7$

# 学习笔记

# （2）★★

已知 $4 ^ { a } = 2 , \lg x = a$ ，则 $x = { } .$

# 学习笔记

# 例2★

对数式 $\log _ { ( x - 1 ) } \left( x + 2 \right)$ 中实数 $x$ 的取值范围是

# 学习笔记

# 变式2★

对数式 $\log _ { ( a - 2 ) } \left( 5 - a \right)$ 中实数 $^ { a }$ 的取值范围是(

A. $\left( \begin{array} { l } { - \infty \ , 5 } \end{array} \right)$ B. (2,5)C. (2,3)U(3,5) D.（2,+∞）

# 学习笔记

# 例3★★

计算下列各式的值：

1 eln5   
②10013=   
③4g5 =   
④3lg² =

# 学习笔记

# 变式3★★

下列等式中正确的是(

A. $5 ^ { \log _ { 5 } 3 } = 9$ B. $3 ^ { \log _ { 4 } 3 } = 3$   
C. $3 ^ { \log _ { 3 } 1 } = 3$ D. $1 0 ^ { \log 1 8 } = 1 8$

# 学习笔记

# 例4★★★

求下列各式中的 $x$ $\log _ { 3 } \left( \log _ { 2 } x \right) = 0 ; \log _ { 3 } \left( \log _ { 7 } x \right) = 1 ; \log \left( \ln x \right) = 1 ; \log \left( \ln x \right) = 0 .$

# 学习笔记

# 变式4

# （1）★★★

求下列各式中 $x$ 的值.

$( \mathrm { { D } } \log _ { 5 } ( \log _ { 3 } x ) = 0$ ，则 $x =$ （204

$\textcircled { 2 } \log _ { 3 } \left( \log x \right) = 1$ ，则 $x = .$

$\left( 3 \right) \ln \left[ \log _ { 2 } \left( \log x \right) \right] = 0$ 则 $x = { } _ { , }$

![](images/a1fd0c3240c5da5d00bac83c2ccd181d974153c86fba490197a0949a265611a8.jpg)

# 学习笔记

# （2）★★★★

已知 $\log _ { 7 } { \left[ \log _ { 3 } { \left( \log _ { 2 } { x } \right) } \right] } = 0$ ，那么 $x ^ { - { \frac { 1 } { 2 } } } = ( \begin{array} { l l }  \begin{array} { r l } & { \begin{array} { r l } \end{array} } & { \begin{array} { r l } \end{array} } \end{array} ) \end{array}$

A. $\frac 1 3$ $\mathrm { B } . \ { \frac { 1 } { 2 { \sqrt { 3 } } } }$ C. $\frac { 1 } { 2 { \sqrt { 2 } } }$ D. 1   
33

# 学习笔记

# 模块2对数的运算

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# $\textcircled{1}$ 积的对数

$\log _ { a } ( M \cdot N ) = \log _ { a } M + \log _ { a } N .$ （积的对数等于对数的和）推广：log（NN.··N）=logN+logN+.+logN.

# ②商的对数

ggMgN（对于对的差）

# $\textcircled { 8 }$ 幂的对数

logM=αlogM（αER）.（幂的对数等于底数的对数乘幂指数）

#

# 思考探究

求下列各式的值,并分别用一个以相应底为底的对数表示出来，据此你有何猜想.

$( 1 ) \log 1 0 + \lg 1 0 0$   
(2） $\mathbf { \log _ { 3 } 9 } + \mathbf { \log _ { 3 } 2 7 }$   
（3)0g $\log _ { \frac { 1 } { 2 } } \frac { 1 } { 4 } + \log _ { \frac { 1 } { 2 } } 8 ;$   
（204 $( 4 ) \log _ { a } a ^ { 2 } + \log _ { a } a ^ { 5 } ( a > 0$ ，且 $a \neq 1$ ）

# 精讲精练

# 拍照批改秒判对错

# 例5

# （1）★★

下列等式成立的是( ）

A $\log _ { 2 } { ( 8 - 4 ) } = \log _ { 2 } { 8 } - \log _ { 2 } { 4 }$ B. ${ \frac { \log _ { 2 } 8 } { \log _ { 2 } 4 } } = \log _ { 2 } { \frac { 8 } { 4 } }$ C. $\log _ { 2 } 2 ^ { 3 } = 3 \log _ { 2 } 2$ D. $\log _ { 2 } { ( 8 + 4 ) } = \log _ { 2 } { 8 } + \log _ { 2 } { 4 }$ （204

# 学习笔记

![](images/c18330064fb86950ec31d4fd790be1adece1969733ba81f8b809cbbdb98b03da.jpg)

# （2）★★★

计算下列各式的值.

$$
\begin{array} { r l } & { \mathrm { { \text O } } \log _ { 2 } 1 0 - \log _ { 2 } 5 = \cfrac { \cdot } { \cdot } } \\ & { \mathrm { { \text O } } \geq 2 \log _ { 3 } 2 - \log _ { 3 } \cfrac { 3 2 } { 9 } + \log _ { 3 } 8 - 3 \log _ { 5 } 5 = \cfrac { \cdot } { \cdot } } \\ & { \mathrm { { \text O } } \cfrac { \log \mathrm { { \text O } } + \log 1 2 5 - \log 2 - \log 5 } { \log \sqrt { 1 0 } \cdot \log 0 . 1 } = \cfrac { \cdot } { \cdot } } \end{array}
$$

![](images/9a014d07dda826efa831e139719605adeebabb45d4488fb53112266baee27058.jpg)![](images/ffe87fac34e8ec24e4e89bf78fad490f9ea2ecefe79fc41c2e0f0ba1ce70dfd1.jpg)

# 变式5

# （1）★★

计算：lg2-1g8+lg5 ${ \frac { \lg 2 - \lg 8 + \lg 5 } { \lg 2 5 - \lg 2 0 } } = \lg$

# 学习笔记

# （2）★★★

计算下列各式的值.

$$
\begin{array} { r l } & { \textcircled { 1 } \log _ { \mathbb { S } ^ { 3 } } 5 - \log _ { \mathbb { S } ^ { 3 } } 1 5 = \cfrac { \cdot } { - } } \\ & { \textcircled { 2 } \lg \sqrt [ 5 ] { 1 0 0 } + \lg \textnormal { 4 } - \ln \mathrm { ~ e } ^ { 2 } + \lg \ 2 5 = \cfrac { \cdot } { - } } \\ & { \textcircled { 3 } \cfrac { \lg \ 1 2 + \lg \ 1 4 4 } { 1 + \frac { 1 } { 2 } \lg \ 0 . \ 3 6 + \frac { 1 } { 3 } \lg \ 8 } = \cfrac { \cdot } { - } } \end{array} .
$$

# 学习笔记

# 模块3换底公式

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# 换底公式

$$
\log _ { b } N = { \frac { \log _ { a } N } { \log _ { a } b } } ( a , b > 0 , a , b \neq 1 , N > 0 )
$$

推话：（1） $\log _ { a } b \log _ { b } a = 1 { \Longleftrightarrow } \log _ { a } b = \frac { 1 } { \log _ { b } a } ;$ （2）1og,1

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 例6

# （1）★★

计算： $\log _ { 8 } 9 \cdot \log _ { 2 7 } 3 2$ 的值为

# 学习笔记

# （2）★★

若 $\log _ { 3 } 4 \cdot \log _ { 8 } m = \log _ { 4 } 1 6$ ，则 $m$ 等于( ）

A.3 B.9 C.18 D.27

# 学习笔记

# （3）★★★

若 $\lg 2 = a , \lg 3 = b$ ，则 $\begin{array} { r } { \log _ { 2 } 6 = ( \begin{array} { l l l } { \phantom { - } } & { } & { } \end{array} ) } \end{array}$

A $\frac { 2 b } { a }$ $\mathbf { B } . \ { \frac { b } { a } }$ C. ${ \frac { a + b } { a } } $ D. ${ \frac { a + b } { a ^ { 2 } } } $

# 学习笔记

# （4）★★★

解关于 $x$ 的方程： $\log _ { 5 } ( x + 1 ) - \log _ { 5 } ^ { 1 } ( x - 3 ) = 1$

# 学习笔记

# 变式6

# （1）★★

计算： $\log _ { 2 } 9 \cdot \log _ { 3 } 8 = ($ ）

A.12 B.10 C.8 D.6

# 学习笔记

# （2）★★★

若 $\log _ { 3 } 4 \ \cdot \ \log _ { 1 6 } 8 \ \cdot \ \log _ { 8 } a = \log _ { 9 } 3$ ，则 $a$ 等于（

A.9 B.3 C.27 D.8

# 学习笔记

# （3）★★★

若 $a \mathrm { l o g } _ { 3 } 2 = 1 , b = \mathrm { l o g } _ { 3 } 8 \cdot \mathrm { l o g } _ { 4 } 4 \cdot \mathrm { l o g } _ { 8 } 2$ ，则（ ）

A. $a < b$ B. $a < 1 , b > 1$ C. $a = b$ D. ab =1

# 学习笔记

# 学习总结

![](images/f995cff0dec9c501acdd3578f01ef627c10bf5b2ad34f7166ac571c542f3a91f.jpg)

# 提升篇你会遇见

若实数 $x , y$ 满足 $2 ^ { x } = 6 ^ { 2 y } = A$ 且 $x + y = 2 x y$ ，则 $A = .$

【点石成金】预习篇学习了对数的基本运算以及换底公式，提升篇需要掌握对数运算的综合问题.此题既考查了指对互化，又考查了对数的运算以及换底公式，需要我们对公式熟练掌握并应用！

# 学而思秘籍系列图书|数学

# 思维培养

# 思维突破

![](images/6983a6660b465b28400a83a3a55868c250ed6625cfba74c80c56c14d963b50c2.jpg)

# 思维提升

学而思 8秘籍 有理数数轴基初中数学思维提升++ ..

学而思 2秘籍 集合的#595高中数学思维突破1-++

# 小学秘籍系列

学而思积淀近20年教研经验，培养受益一生的能力。

# 初中秘籍系列

全面覆盖初中基础知识和重难点，帮助学生夯实基础，拓展认知。

# 高中秘籍系列

全面覆盖高中基础知识和重难点，帮助学生提升能力，突破思维。

# 学而思秘籍系列图书|语文

# 提升素养

# 能力训练

# 小学秘籍系列

![](images/28e1c1e637783dc1615630d37bd2df1c1f152d48b711eb9af9a1646d17729079.jpg)

5大模块+2条主线，能力与素养双向提升。

![](images/19f7d507337503e471db2721c5f7f88f51c1157f6ccb6afc5df3a51cef516983.jpg)

# 初中秘籍系列

融合课改四大核心素养，培养爱阅读、 善写作、勤思考、会学习的学生。

# 创新体系|真题研习

![](images/3dc2102900c34aa795ca911966e2f8ab94be63c528f95a974d3cef1aa0a357df.jpg)

# 思维创新大通关 数学

攻克数学思维难题，通向理想中学。

# 大家一起来“升级”

# 参与方式

您在使用本书时，如有任何疑问或对图书有任何建议，请扫码进行反馈，并查看反馈采纳结果。

![](images/2699de9cb24549d3907f7f83742b8e244df66e92212b399521e3213ce97e20dd.jpg)

# 奖励

您的反馈一经采纳，我们将会送出总价值35元的图书抵扣券（相同内容的反馈，依据反馈时间，奖励前三位）。请扫码关注公众号，并在对话框中发送反馈时填写的手机号，领取抵扣券。

![](images/358b484892e916cb18540b6c75026bc79ddbf0dc70de1730b1fba8f5adbd778f.jpg)

# 合理规划学习时间

先自己定一个目标，即制定半年学习规划。

![](images/0194de44a07afe37433470f370fb53b7169ec0daaa233df65ad3161eac06fbf8.jpg)

2 再将學細化到每一5个考点）。

3 配套课堂巩固的练习， 让学习更有效！

![](images/7bde780faaf70a783fd5a8564344a0d9f01f3ccbc0ca832d4e9f82d79e4b858b.jpg)  
·共6级·每级17-26讲