---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 16
source_file: L1-20-函数与方程.md
title: L1-20-函数与方程
type: problem_type
---

# 函数与方程

一本一讲，共26讲（建议5个月学习）

4个考点+6个题型+20个视频

每周一讲（建议学习时间90分钟）

![](images/a9dca4f0907e5667b815d53a17f26cd1bf33ae7ce47ba430043f0a9c6579e96b.jpg)

# 视频内容研发团队

学而思优秀老师

学而思优秀老师和一线高级教师联合创作本书试题，并精心录制讲解视频学而思图书APP扫码即可观看

![](images/e7f85946c248c282864805eb7ee4ce74f474a05329f1fc2594ff6ad7cdbc6e74.jpg)

# 傅博宇 老师

毕业于北京大学元培学院  
网校和北大数院高考数学研究联合课题组成员；  
网校高中创新产品部负责人；  
荣获学而思网校“桃李满天下奖”“出类拔萃奖”等；腾讯网中国好老师；  
青少年教育导师认证；  
科学家长观体系的创立者

![](images/a8025a154ee803ecaefeb91bd24d371a028300925ca99de21be95d9fc9d8937e.jpg)

# 王侃老师

毕业于北京大学数学系  
学而思网校高中数学教研奠基人；  
学而思网校高中数学S级教师；  
荣获学而思网校“突出贡献奖”“桃李天下奖”等；擅长总结题型特点，提炼思想方法；  
擅长分层教学，因材施教

![](images/cad462e8ad26bffa8295e5c304a95ff2d6e06a2edef345098a8526780f63cbc1.jpg)

# 付恒岩 老师

毕业于大连理工大学  
网校高中部理科主讲岗后培训师；  
2020年荣获学而思网校最具魅力奖；  
2019年、2020年荣获学而思网校诲人不倦奖；2020年荣获学而思网校高考优秀评卷人；  
2021年担任新浪教育高考数学直播解析特邀嘉宾；“停课不停学”公益课高中数学主讲老师

![](images/4867e95a7f36fa2c85881c5d81b34e3d9aa1362eaf79b9b5b7cb9da59436b4a9.jpg)

# 武洪姣 老师

14年线上线下教学经验；  
学而思网校高中理科教研负责人；  
学而思高中数学特级教师;  
在教学的过程中擅长归纳题型，方法和技巧;  
在高中数学模块中最擅长讲解圆锥曲线和导数；  
无论你是从小数学不好，还是数学一直拔尖，都可以在武老师的课堂上收获很多

1级

# 函数与方程

一本一讲，共26讲（建议5个月学习）

4个考点 $+ 6$ 个题型 $+ z 0$ 个视频每周一讲（建议学习时间90分钟）

21099-el+3  
= 2×2- e°+(8§)²  
二 4-1+4  
=7

# 预习篇

第1讲集合的概念与表示 提升篇第2讲集合的关系与运算第11讲集合重难点专题第3讲解不等式第12讲常用逻辑用语第4讲函数的概念与三要素第13讲基本不等式第5讲函数的单调性（一）第14讲函数三要素专题第6讲函数的奇偶性（一）第15讲函数的单调性（二）第 $7$ 讲指数幂运算与幂函数第16讲函数的奇偶性（二）第8讲指数函数第17讲抽象函数第 $9$ 讲对数运算第18讲指数幂运算与指数函数第10讲对数函数第19讲对数运算与对数函数第20讲函数与方程第21讲恒成立与存在性问题第22讲三角函数基本概念与诱导公式  
模块1 零点区间问题 2第23讲三角函数的图象与性质  
模块2|零点转换 9第24讲三角公式的应用技巧第25讲三角恒等变换重点题型第26讲正弦型函数的图象与性质参考答案

# 函数与方程

# 直击课堂

<html><body><table><tr><td>知识模块</td><td>考点</td><td>对应例题</td><td>星标统计</td></tr><tr><td rowspan="2">零点区间问题</td><td>求函数零点所在区间</td><td>例1</td><td rowspan="6"></td></tr><tr><td>利用二分法确定方程</td><td>例2</td></tr><tr><td></td><td>例3</td><td>★★3题 ★★★★3道题</td></tr><tr><td rowspan="2">零点转换</td><td>求函数零点个数</td><td>例4</td></tr><tr><td>已知零点个数求参数</td><td>例5</td></tr><tr><td></td><td></td><td>例6</td><td></td></tr></table></body></html>

# 学习目标

$\textcircled{1}$ 理解函数零点的定义，会求简单函数零点所在的区间及函数零点个数.

$\textcircled{2}$ 了解方程的根与函数零点的关系，会将函数零点问题转换为交点问题求解函数的零点.

# 模块1零点区间问题

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# $\textcircled{1}$ 零点的概念

一般地,如果函数 $\scriptstyle { y = f \left( x \right) }$ 在实数 $x _ { 0 }$ 处的函数值为零，即 $f \left( x _ { \mathrm { 0 } } \right) = 0$ ，则 $x _ { 0 }$ 叫做这个函数的零点.

# 注意

$\textcircled{1}$ 函数 $\scriptstyle { y = f \left( x \right) }$ 的零点就是方程 $f ( x ) = 0$ 的实数根；而从图象上看，函数 $\scriptstyle { y = f \left( x \right) }$ 的零点就是它的图象与 $_ x$ 轴交点的横坐标.$\textcircled{2}$ 函数的零点不是指一个点，而是指一个实数.如函数 $y = x -$ 1的零点是 $x = 1$ ，而不是(1,0).

# 函数零点的求法

(1)代数法：求方程 $f \left( x \right) = 0$ 的实数根;   
(2)几何法：画出函数 $\scriptstyle { y = f \left( x \right) }$ 的图象，从图象上找出零点.

# $\textcircled { 8 }$ 零点与函数图象交点

函数 $F ( x ) = f \left( x \right) - \dot { g } \left( x \right)$ 的零点就是方程 $f \left( x \right) = g \left( x \right)$ 的实数根,也是函数 $\boldsymbol { y } _ { 1 } ^ { } = \boldsymbol { f } ^ { } \left( \boldsymbol { x } _ { } \right)$ 与 ${ \boldsymbol { y } } _ { 2 } = { \boldsymbol { g } } ( { \boldsymbol { x } } )$ 的图象交点的横坐标.

# $\textcircled { 2 }$ 零点存在性定理

若函数 $f \left( x \right)$ 在闭区间 $[ a , b ]$ 上的图象是一条连续不断的曲线，并且在区间端点的函数值符号相反，即 $f \left( a \right) f \left( b \right) < 0$ ,则在区间 $( a , b )$ 内，函数 $f \left( x \right)$ 至少有一个零点.

# 注意

$\textcircled{1}$ 此判定定理只能判断出零点的存在性，而不能判断出零点的个数。  
如图所示： $f \left( a \right) f \left( b \right) < 0$ ，函数可以有多个零点。

![](images/b9b158d1a520827efc18814351d245fffc849590e7e4684d6e47f7b8001ae829.jpg)

$\textcircled{2}$ 此定理是不可逆的：若 $f \left( x \right)$ 在区间 $( a , b )$ 内存在零点，不一定推出 $f \left( a \right) f \left( b \right) < 0$ ，如图所示：

![](images/cd97133f353b93126259024717d1ea00a14efe90278e9b2283954bc62628484d.jpg)

# $\textcircled { 6 }$ 单调函数的零点存在定理

若函数f（x）在闭区间[a，b]上的图象是一条连续不断的曲线，且f（x）在（a，b）上单调，则f（a）f（b）<0<函数f（x）在区间（a，b）内有且只有-个零点

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点1：求函数零点所在区间

# 例1★★

若函数 $\scriptstyle { y = f \left( x \right) }$ 在区间 $[ a , b ]$ 上的图象为连续不断的一条曲线,则下列说法正确的是（ ）

A.若 $f \left( a \right) f \left( b \right) > 0$ ,不存在实数 $c \in \left( a , b \right)$ ,使得 $f ( c ) = 0$ B.若 $f \left( a \right) f \left( b \right) < 0$ ,存在且只存在一个实数 $c \in \left( a , b \right)$ ,使得 $f ( c ) = 0$ C.若 $f \left( a \right) f \left( b \right) > 0$ ,有可能存在实数 $c \in \left( a , b \right)$ ,使得 $f ( c ) = 0$ D.若 $f \left( a \right) f \left( b \right) < 0$ ,有可能不存在实数 $c \in \left( a , b \right)$ ,使得 $f ( c ) = 0$

# 学习笔记

# 达标检测1★★★

已知函数 $\scriptstyle { y = f \left( x \right) }$ 在 $\mathbf { R }$ 上的图象是连续不断的一条曲线,且f（-1）·$f ( 1 ) > 0$ ，则 $\scriptstyle { y = f \left( x \right) }$ （ ）

A.在区间 $[ \mathbf { \Pi } - 1 , 1 ]$ 上有2个零点  
B.在区间[-1,1]上零点个数是偶数个  
C.在区间[-1,1]上零点个数可能为 $\boldsymbol { k } , \boldsymbol { k } \in \mathbf { N }$   
D.在区间[-1,1]上没有零点

# 学习笔记

# 例2

# （1）★★

函数 $f ( x ) = { \sqrt { x } } - x + 1$ 的零点所在的区间是(

A. (0,1) B. (1,2) C. (2,3) D. (3,4)

# （2）★★★

已知三个函数 $f \left( x \right) = 2 ^ { x } + x , g \left( x \right) = x - 1 , h \left( x \right) = \log _ { 2 } x + x$ 的零点依次为 $a , b , c$ ,则（）

A. $a < b < c$ $\begin{array} { l l } { \mathbf { B . } } & { b < a < c } \\ { \mathbf { \qquad } } & { } \\ { \mathbf { D . } } & { a < c < b } \end{array}$   
C. $c < a < b$

# （3）★★★★

设 $f \left( x \right) = 1 - \left( x - a \right) \left( x - b \right) \left( a < b \right) , m , n$ 为 $\scriptstyle { y = f \left( x \right) }$ 的两个零点,且m$< n$ ，则 $a , b , m , n$ 的大小关系是（ ）

A. $a < m < n < b$ B. $m < a < b < n$   
C. $a < b < m < n$ D. $m < n < a < b$

# 学习笔记

![](images/56bef05f2ca135e8cda9d93be373b6a4be8fe8c29837c2a2154856b4de88fcdd.jpg)

# 达标检测2★★★

方程 $\ln ( x + 1 ) - { \frac { 2 } { x } } = 0 ( x > 0 )$ 的根存在的大致区间是(

A. (0,1) B.(1,2) C. (2,e) D. (3,4)

# 学习笔记

# 进阶1★★★

设函数 $y = x ^ { 3 }$ 与 $y = 2 ^ { x } + 1$ 的图象的交点为 $( \ v x _ { 0 } , \ v y _ { 0 } )$ ，则 $x _ { 0 }$ 所在的区间是

A. (0,1) B. (1,2) C. (2,3) D. (3,4)

# 学习笔记

# 知识点睛

# $\textcircled{1}$ 二分法

对于在区间 $[ a , b ]$ 上连续不断且 $f \left( a \right) f \left( b \right) < 0$ 的函数 $\scriptstyle { y = f \left( x \right) }$ ,通过不断地把函数 $f \left( x \right)$ 的零点所在区间一分为二，使区间的两个端点逐步逼近零点,进而得到零点近似值的方法叫做二分法.

给定精确度 $\varepsilon$ ，用二分法求函数 $f \left( x \right)$ 零点近似值的步骤如下：

(1)确定区间 $[ a , b ]$ ，验证 $f \left( a \right) f \left( b \right) < 0$ ,给定精确度 $\varepsilon$   
(2)求区间 $[ a , b ]$ 的中点 $c$   
(3)计算 $f \left( c \right)$   
$\textcircled{1}$ 若 $f ( c ) = 0$ ，则 $c$ 就是函数的零点；  
$\textcircled{2}$ 若 $f \left( a \right) f \left( c \right) < 0$ ，,则令 $b = c$ （此时零点 $x _ { 0 } \in \left( a , c \right)$ ）；  
$\textcircled{3}$ 若 $f \left( c \right) f \left( b \right) < 0$ ,则令 $a = c$ （此时零点 $x _ { 0 } \in \left( c , b \right)$ ）  
（4)判断是否达到精确度 $\varepsilon$ ：即若 $\vert a - b \vert < \varepsilon$ ,即得到零点近似值;否则重复 $( 2 ) \sim ( 4 )$

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点2：利用二分法确定方程近似解

# 例3★★

用二分法研究函数 $f ( x ) = x ^ { 3 } - 2 x - 1$ 的零点时,若零点所在的初始区间为(1,2)，则下一个有解区间为（ ）

A.(1,2) B. (1.75,2) C. (1.5,2) D. (1,1.5)

# 学习笔记

# 达标检测3★★★

设 $f ( x ) = 3 ^ { x } + 3 x - 8$ ,用二分法求方程 $3 ^ { x } + 3 x - 8 = 0$ 在 $x \in \left( 1 , 2 \right)$ 内近似解的过程中得 $f \left( 1 \right) < 0 , f \left( 1 . 5 \right) > 0 , f \left( 1 . 2 5 \right) < 0$ ,则方程的根落在区间（）

A.(1,1.25) B. (1.25,1.5) C. (1.5,2) D.不能确定

# 学习笔记

# 模块2零点转换

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

<html><body><table><tr><td>根</td><td>零点</td><td>交点</td></tr><tr><td>方程f（x）=0的根</td><td>函数y=f(x)的零点</td><td>f(x)图象与x轴交点的横坐标</td></tr><tr><td>方程f（x）=g(x）的根</td><td>函数y=f(x）-g(x)的零点</td><td>f(x)与g(x)图象交点的横坐标</td></tr></table></body></html>

![](images/6868e7606a3766e86dbd471cd96dad30bef840ef8ba63a452b2a8b560a52b47e.jpg)

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点3：求函数零点个数

# 例4

# （1）★★★

函数 $f ( x ) = 2 ^ { x } + x - 5$ 的零点个数为( ）

A.1 B.2   
C.3 D.4

# （2）★★★

函数 $f ( x ) = | x - 2 \mid - \ln { x }$ 在定义域内零点的个数为(

A.0 B.1   
C.2 D.3

# （3）★★★★

函数 $f ( x ) = x ^ { 2 } - 4 x + 5 - 2 \ln x$ 的零点的个数为

# 学习笔记

# 达标检测4★★★

函数 $f \left( x \right) = x ^ { \frac { 1 } { 2 } } - \left( { \frac { 1 } { 3 } } \right) ^ { x }$ 的零点个数为(

A.0 B.1 C.2 D.3

# 学习笔记

# 考点4：已知零点个数求参数

# 例5★★★

已知函数 $f \left( x \right) = a x + 3$ 在区间[0,2]上有零点，则实数 $a$ 的取值范围 是

# 学习笔记

# 达标检测5★★★

函数 $f \left( x \right) = a x ^ { 2 } - 2 x + 1$ 在区间(-1,1)和区间(1,2)上分别存在一个零点,则实数 $a$ 的取值范围是（ ）

A. $- 3 < a < 1$ $\mathrm { B } . \frac { 3 } { 4 } < a < 1$ C. $- 3 < a < \frac { 3 } { 4 }$ D.a<-3或a>3

# 学习笔记

# 例6

# （1）★★★

已知函数 $f \left( x \right) = \left\{ \begin{array} { l l } { \displaystyle 1 + \frac { 4 } { x } , x \geqslant 4 , } \\ { \ } \\ { \displaystyle \log _ { 2 } x , x < 4 , } \end{array} \right.$ 若关于 $x$ 的方程 $f \left( x \right) = k$ 有两个不同的根,则实数 $k$ 的取值范围是(

A.(-∞,1) B.(-8,2) C.[1,2) D. (1,2)

# 学习笔记

若方程 $\mid \lg \ x \mid - \left( { \frac { 1 } { 3 } } \right) ^ { x } + a = 0$ 有两个不相等的实数根,则实数 $a$ 的取值范围是（ ）

$$
\mathrm { A . ~ \ } \left( { \frac { 1 } { 3 } } , + \infty \right) \qquad \mathrm { B . ~ \ } \left( { \textrm { -- } } \infty , { \frac { 1 } { 3 } } \right) \qquad \mathrm { C . ~ \ } \left( 1 , + \infty \right) \qquad \mathrm { D . ~ \ } \left( { \textrm { -- } } \infty , 1 \right) \qquad 
$$

# 学习笔记

# 进阶2★★★

已知函数 $f \left( x \right) = \left\{ { \begin{array} { l } { a x ^ { 2 } + 2 x + 1 \left( x \leqslant 0 \right) , } \\ { { } } \\ { a x - 3 \left( x > 0 \right) } \end{array} } \right.$ 有3个零点,则实数 $a$ 的取值范围是（）

A. $a < 1$ B. $a > 0$ C. $a \geqslant 1$ D.0<a<1

# 学习笔记

# 学习总结

![](images/e7b32f8b53a7fa2157a9e7c40f8e7cc66297b18cb90fe2ab2e41921dc8e2b29c.jpg)

# 直击高考

已知 $\lambda \in \mathbb { R }$ ，函数 $f \left( x \right) = \left\{ { x \atop x ^ { 2 } - 4 x + 3 , x < \lambda , } \right.$ 当 $\lambda = 2$ 时,不等式 $f \left( x \right) < 0$ 的解集是 ·若函数 $f \left( x \right)$ 恰有2个零点，则 $\lambda$ 的取值范围是

# 学而思秘籍系列图书|数学

# 思维培养

# 思维提升

# 思维突破

![](images/c364dc0fe182c68589215c5182de57cf77080a54e2c06f8e3e40943b37fb7615.jpg)

学而思秘籍 有理数数轴基初中数学思维提升++

![](images/02f817cf2bfc82cb3bfb7049c41511ed8b2604623ceae481b1056728627421b3.jpg)

# 小学秘籍系列

学而思积淀近20年教研经验，培养受益一生的能力。

# 初中秘籍系列

全面覆盖初中基础知识和重难点，帮助学生夯实基础，拓展认知。

# 高中秘籍系列

全面覆盖高中基础知识和重难点，帮助学生提升能力，突破思维。

# 学而思秘籍系列图书|语文

# 提升素养

# 能力训练

![](images/44b255db623d3b06e7c26c2b54cb5e3c5f46c85ab471c5b55b59b735e6dbb6fb.jpg)

# 小学秘籍系列

5大模块+2条主线，能力与素养双向提升。

![](images/836127fb7a12822f8532f9ee6c0c7822cdd8aee7d05ebc0c6557b3153d11e57c.jpg)

# 初中秘籍系列

融合课改四大核心素养，培养爱阅读、 善写作、勤思考、会学习的学生。

# 创新体系|真题研习

![](images/473a1e750d5b4803cf69212554d76f3bbfe1bcfb6a4e084f50a3b2cf32a8f557.jpg)

# 思维创新大通关数学

攻克数学思维难题，通向理想中学。

# 大家一起来“升级

# 参与方式

您在使用本书时，如有任何疑问或对图书有任何建议，请扫码进行反馈，并查看反馈采纳结果。

![](images/584ebf7b8151d19dc667ee62de26df67f38f0374249f74c74b7c089b552642fb.jpg)

# 奖励

您的反馈一经采纳，我们将会送出总价值35元的图书抵扣券（相同内容的反馈，依据反馈时间，奖励前三位）。请扫码关注公众号，并在对话框中发送反馈时填写的手机号，领取抵扣券。

# 合理规划学习时间

先自己定一个目标，即制定半年学习规划。再将目标细化到每一周，每周学习一本（平均5个考点）。3 配套课堂巩固的练习，让学习更有效！

![](images/16e3016f4aaf7a3f69a59f579b2bd71076f0331a3faad024844219ea951d650c.jpg)

![](images/145b90e2935a9258f5f7c3c04c8e442511af82581ac5b082377ff217be6647fa.jpg)  
·共6级·每级17-26讲