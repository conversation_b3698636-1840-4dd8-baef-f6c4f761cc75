---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 16
source_file: L5-13-导数的应用——单调性与极值最值.md
title: L5-13-导数的应用——单调性与极值最值
type: problem_type
---

# 导数的应用一 单调性与极值最值

一本一讲，共26讲（建议5个月学习）

7个考点+13个题型+16个视频

每周一讲（建议学习时间90分钟）

![](images/f7cde53b6d2def0a6250881a39d63347ff4d77e5b3706fb1ed199eea5bca188f.jpg)

# 视频内容研发团队

学而思优秀老师

学而思优秀老师和一线高级教师联合创作本书试题，并精心录制讲解视频学而思图书APP扫码即可观看

![](images/7771dc2dec51cfb94dcddda7d02cbb4cbb839397d778c73b39f9bde2fe6aa986.jpg)

# 傅博宇 老师

毕业于北京大学元培学院  
网校和北大数院高考数学研究联合课题组成员；  
网校高中创新产品部负责人；  
荣获学而思网校“桃李满天下奖”“出类拔萃奖”等；腾讯网中国好老师；  
青少年教育导师认证；  
科学家长观体系的创立者

![](images/378239c75c0f696c1a90e11fc84435fe0b84872a5243b98b8005e379f70d1617.jpg)

# 王侃老师

毕业于北京大学数学系  
学而思网校高中数学教研奠基人；  
学而思网校高中数学S级教师；  
荣获学而思网校“突出贡献奖”“桃李天下奖”等；擅长总结题型特点，提炼思想方法；  
擅长分层教学，因材施教

![](images/5985fa9fcf9bbc4fdcc95504489c48b15a1fccb5da787fe261dd3e9e15116742.jpg)

# 付恒岩 老师

毕业于大连理工大学  
网校高中部理科主讲岗后培训师；  
2020年荣获学而思网校最具魅力奖；  
2019年、2020年荣获学而思网校诲人不倦奖；2020年荣获学而思网校高考优秀评卷人；  
2021年担任新浪教育高考数学直播解析特邀嘉宾；“停课不停学”公益课高中数学主讲老师

![](images/0fc7ecdce4b95644f773a84247cee75f63b171e8ad0facf23a7a8635f63e48e2.jpg)

# 武洪姣 老师

14年线上线下教学经验；  
学而思网校高中理科教研负责人；  
学而思高中数学特级教师；  
在教学的过程中擅长归纳题型，方法和技巧；  
在高中数学模块中最擅长讲解圆锥曲线和导数；  
无论你是从小数学不好，还是数学一直拔尖，都可以在武老师的课堂上收获很多

# 导数的应用一 单调性与极值最值

一本一讲，共26讲（建议5个月学习）

7个考点 $+ 1 3$ 个题型 $+ 1 6$ 个视频

每周一讲（建议学习时间90分钟）

![](images/d8fc5e1408cd5a76150c7882b459d4fba5e0fd62f389ab198f337fe1779dc9c5.jpg)

# 提升篇

第1讲集合、逻辑、函数初步第2讲基本初等函数与函数 冲刺篇的性质第11讲函数性质的应用第3讲不等式第12讲函数零点与恒成立问题第4讲导数（一）第13讲导数的应用一单调性与极值第5讲导数（二）最值第6讲三角函数第14讲导数的应用- 一恒成立问题第7讲平面向量与解三角形第15讲导数的应用—零点个数与第8讲数列（一）隐零点问题第9讲立体几何第16讲导数的应用一一极值点偏移第10讲解析几何第17讲三角函数与平面向量第18讲解三角形第19讲数列（二）第20讲三视图与球的切接问题  
模块1 利用导数分析函数单调性 2 第21讲空间中的角  
模块2 极值与最值的求法 8第22讲统计概率  
模块3 导数应用中的逆向求参问题 12第23讲圆锥曲线的几何性质第24讲几何条件的代数转化第25讲圆锥曲线中的最值范围问题第26讲定点定值问题与轨迹方程问是参考答案

# 导数的应用— 单调性与极值最值

# 直击课堂

<html><body><table><tr><td>知识模块</td><td>考点</td><td>对应例题</td><td>考查概率</td></tr><tr><td>利用导数分析 函数单调性</td><td>判断不含参函数的单调性 判断含参函数的单调性 二阶求导问题</td><td>例1、例2 例3、例4、例5 例6、例7</td><td>★★★☆☆ ★★★★★ ★★★★☆</td></tr><tr><td>极值与最 值的求法</td><td>判断函数极值 判断函数最值</td><td>例8 例9</td><td>★★★★☆ ★★★★☆</td></tr><tr><td>导数应用中的 逆向求参问题</td><td>已知函数单调性求参数 已知极值、最值分布求参数</td><td>例10、例11 例12、例13</td><td>★★★★☆ ★★★★☆</td></tr></table></body></html>

# 本讲解读

# ①利用导数分析函数单调性

（1)了解函数单调性和导数的关系；  
(2)能利用导数研究函数的单调性,会求函数的单调区间(其中多项式函数一般不超过三次).

# ②极值与最值的求法

(1)了解函数在某点取得极值的必要条件和充分条件;会用导数求函数的极大值、极小值（其中多项式函数一般不超过三次）；(2)会求闭区间上函数的最大值、最小值(其中多项式函数一般不超过三次).

# 模块1利用导数分析函数单调性

# APP扫码观看本模块讲解视频

1知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# $\textcircled { 1 1 }$ 导数符号与单调性的关系

如果函数 $\scriptstyle y = f ( x )$ 在 $x$ 的某个开区间内，总有 $f ^ { \prime } ( x ) > 0$ ，则 $f ( x )$ 在这个区间上是增函数；  
如果函数 $\scriptstyle y = f ( x )$ 在 $x$ 的某个开区间内，总有 $f ^ { \prime } ( x ) < 0$ ，则 $f ( x )$ 在这个区间上是减函数.

# $\textcircled { 8 3 }$ 利用导数求函数单调区间的一般步骤

（1）（确定定义域)确定函数的定义域；(2）（求导函数零点)求导数 $f ^ { \prime } \left( x \right)$ ，令 $f ^ { \prime } \left( x \right) = 0$ ，解此方程，求出它在定义域内的所有实根；（3）（划分区间）把函数 $f ( x )$ 的间断点和上面的各实根按从小到大的顺序排列起来，然后用这些点把函数 $f ( x )$ 的定义域分成若干个小区间；（4）（判断单调性）确定 $f ^ { \prime } ( x )$ 在各个小区间内的符号，根据 $f ^ { \prime } ( x )$ 的符号确定 $f ( x )$ 在各个小区间内的增减性.

# 重点笔记

# 注意

# 重点笔记

$\textcircled{1}$ 不要忽视函数定义域；$\textcircled{2}$ 在对函数划分单调区间时，需要注意间断点和不可导的点；$\textcircled{3}$ 求导数零点时，通分与因式分解是常用的重要手段.另外，讨论导函数的符号时,很多时候需要对某些因式的范围作出合理估计；$\textcircled{4}$ 如果导函数的零点与符号难以确定，则往往需要对导函数再次求导，由二阶导函数的性质反推导函数的性质.

# 直击高考

高考中，常考查利用导数求函数的单调区间；利用导数与单调性的关系，将已知单调性求参数问题转化为不等式恒成立问题.此外，利用导数分析函数的单调性是压轴大题后续小题的重要基础,需要额外重视.

利用导数研究函数的单调性常见题型有以下三种：

$\textcircled{1}$ 已知函数解析式求单调区间,实质上是求 $f ^ { \prime } ( x ) > 0 , f ^ { \prime } ( x ) < 0$ 的解集，但是要注意定义域；

$\textcircled{2}$ 含参数的单调性问题要分类讨论，通过确定导数的符号，判断函数的单调性；

$\textcircled{3}$ 已知函数单调性可以利用已知区间和函数单调区间的包含关系或转化为恒成立问题两种思路解决.

# 方法点拨

# $\textcircled{4}$ 含参函数导数的分类讨论

(1)在研究含有参数的函数f（x）的单调性时，分类讨论的依据

# 如下：

①f(x)的导函数f'（x)正负分布情况，往往由首项系数决定;

②f'（x）=0的根的个数；

$\textcircled{3}$ f'（x）=0的根是否在f（x）的定义域内；

④f‘（x）=0的各个根，以及f（x)的间断点之间的大小关系.

(2)很多题目中的导函数都是由一个含参的二次函数决定符号，对于这种情形，分类讨论的流程如下图所示：

![](images/e64e0a7de020123143207798c80eac070a83567c01c99930655099e4bcc53650.jpg)

# $\textcircled { \times }$ 利用高阶导数判断单调性

在用导数研究函数时，很多时候求一次导数并不能判断导函数的零点及符号，这时就需要对导函数再次求导，然后由二阶导函数的符号判断一阶导函数的单调性，进而再根据一阶导函数特殊点的函数值判断一阶导函数的符号，从而得到原函数的单调性与最值.如果二阶导函数性状依然不好确定，那么还可以继续求导，直到能够确定其符号为止，但要注意最终须回归到原函数.

# $\textcircled { 8 }$ 多次求导的使用技巧

（1)在每次求导时，我们的目的是判断该阶导函数的符号，因此可以对此函数作适当变形，只针对其不能定号的部分来构造新函数，然后再去求导.(2)由各阶导数研究原函数时，往往需要结合零点存在定理(或者试根)找出导数的零点，或者说明导数恒正（负），然后得到原函数的单调性.

# 精讲精练

# 拍照批改秒判对错

# 考点1：判断不含参函数的单调性

# 例1

设函数 $f ( x ) = \left( 1 - x ^ { 2 } \right) \mathrm { e } ^ { x }$ 讨论 $f ( x )$ 的单调性.

# 学习笔记

# 例2

讨论函数 $f ( x ) = \mathrm { e } ^ { x } - \mathrm { l n } \ \left( x + 1 \right)$ 的单调性.

# 学习笔记

# 考点2：判断含参函数的单调性

# 例3

设函数 $f ( x ) = \mathbf { e } ^ { m x } + x ^ { 2 } - m x , m \in \mathbf { R } .$ 讨论 $f ( x )$ 的单调性.

# 学习笔记

# 例4

已知函数 $f ( x ) = \ln x - a x + { \frac { 1 - a } { x } } - 1 ( a \in \mathbf { R } )$ +1-a-1(aεR),讨论f(𝑥)的单调性.

# 学习笔记

# 例5

已知函数 $f ( x ) = ( x - 2 ) \mathbf { e } ^ { x } + a ( x - 1 ) ^ { 2 }$ .讨论 $f ( x )$ 的单调性.

# 学习笔记

# 考点3：二阶求导问题

# 例6

设函数 $f ( x ) = \left( 2 - x \right) \mathrm { e } ^ { x } + \mathrm { e } ^ { 2 } x$ ，求 $f ( x )$ 的单调区间.

# 学习笔记

# 例7

已知函数 $f ( x ) = ( 2 + x + a x ^ { 2 } ) \ln { ( 1 + x ) } - 2 x .$ 若 $a = 0$ ，证明：当 $- 1 < x < 0$ 时， $f ( x ) < 0$ ；当 $x > 0$ 时， $f ( x ) > 0$

# 学习笔记

# 模块2极值与最值的求法

# APP扫码观看本模块讲解视频

1知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# $\textcircled { 1 }$ 极值

已知函数 $\scriptstyle y = f ( x )$ ，设 $x _ { 0 }$ 是定义域内任一点，如果对 $x _ { \scriptscriptstyle 0 }$ 附近的所有点 $x$ ，都有 $f ( x ) < f ( x _ { \mathrm { 0 } } )$ ，则称函数 $f ( x )$ 在点 $x _ { \scriptscriptstyle ( ) }$ 处取极大值，记作 $y _ { \star \mathbb { R } \star } = f ( x _ { 0 } )$ .并把 $x _ { 0 }$ 称为函数 $f ( x )$ 的一个极大值点.

如果在 $x _ { \scriptscriptstyle ( ) }$ 附近都有 $f ( x ) > f ( x _ { 0 } )$ ，则称函数 $f ( x )$ 在点 $x _ { 0 }$ 处取极小值，记作 $y _ { _ { \mathrm { + } \mathrm { + } } , _ { \mathrm { + } } } = f ( x _ { _ { 0 } } )$ .并把 $x _ { \scriptscriptstyle 0 }$ 称为函数 $f ( x )$ 的一个极小值点.

极大值与极小值统称为极值.极大值点与极小值点统称为极值点.

# $\textcircled { 1 2 }$ 最值

函数的最大(小)值是函数在指定区间上的最大(小)值.

# 直击高考

高考试卷中常考查：利用导数求函数的极值，求闭区间上函数的最值；利用导数在极值点处的函数值为0建立关于参数的方程，以求参数值等.以上均属于高考中的热点和重难点.

# 重点笔记

# 方法点拨

# $\textcircled{9}$ 求解函数极值(点)的步骤

第一步：求函数的定义域利用导数求函数极值的步骤 第二步：求导数f（ $x$ ）第三步：解方程 $f ^ { \prime } \left( x \right) = 0$ 第四步：判断 $f ^ { \prime } \left( x \right)$ 在 $f ^ { \prime } \left( x \right) = 0$ 的根两侧的符号第五步：求极值并下结论

# 注意

$\textcircled{1}$ 函数的极值点的导数为0,但导数为0的点可能不是函数的极值点.也就是说，若$f ^ { \prime } ( c )$ 存在， $f ^ { \prime } ( c ) = 0$ 是 $f ( x )$ 在 $x = c$ 处取得极值的必要条件，但不是充分条件.比如 $f ( x ) =$ $x ^ { 3 }$ 在 $x = 0$ 处， $f ^ { \prime } ( 0 ) = 0$ ,但 $x = 0$ 不是函数的极值点，所以一定要注意点的左右变化趋势.

$\textcircled{2}$ 不可导的点也可能是极值点,比如 $y = | x |$ |在 $x = 0$ 时不可导,但是极小值点.

# ②求指定区间上函数的最值的步骤

（1）求函数在该区间上的极值；（2)把极值与端点的函数值作比较，最大的为最大值，最小的为最小值，

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点4：判断函数极值

# 例8

设函数 $f ( x ) = \sin x - \cos x + x + 1 , 0 < x < 2 \pi$ ，求函数 $f ( x )$ 的单调区间 与极值.

# 学习笔记

![](images/c6cac752c828b03994e1fe676b9c5ea75c3dbbd015dca4c4e2430826785f9bd6.jpg)

# 考点5：判断函数最值

# 例9

已知函数 $f ( x ) = \frac { 1 } { 2 } x ^ { 2 } - \left( a + 1 \right) x + a \mathrm { l n } \ x \left( a > 0 \right) .$

（1）求 $f ( x )$ 的极值点;

(2）当 $a = 2$ 时，函数 $f ( x )$ 在 $\left[ 1 , t \right] \left( t > 1 \right)$ 上的最大值是 $f ( t )$ ，求 $t$ 的最小整数值.

# 学习笔记

# APP扫码观看本模块讲解视频

1知识与方法 例题与练习 全程跟老师 高效学知识

# 精讲精练

# 拍照批改秒判对错

# 考点6：已知函数单调性求参数

# 例10

已知函数 $f ( x ) = x ^ { 2 } - 1 + a \mathrm { l n } \ ( \ 1 - x ) \ ,$ $a \in \mathbb { R } .$ 若函数 $f ( x )$ 为定义域上的单调函数，求实数 $a$ 的取值范围.

# 学习笔记

# 例11

已知函数 $f ( x ) = x ^ { 3 } + ( 2 - m ) x ^ { 2 } - \left( m ^ { 2 } - 1 \right) x + m ( m \in \bf { R } )$ ，若函数 $f ( x )$ 在区间（-1,1)上不单调,则实数 $m$ 的取值范围是（ ）

A.(-4,2) $\mathbf { B } . \left( \mathbf { \sigma } - 4 , \frac { 1 } { 2 } \right)$ $\mathrm { C } . \left( { \frac { 1 } { 2 } } , 2 \right)$ $\operatorname { D } . \left( - 4 , { \frac { 1 } { 2 } } \right) \cup \left( { \frac { 1 } { 2 } } , 2 \right)$

# 学习笔记

# 考点7：已知极值、最值分布求参数

# 例12

已知函数 $f ( x ) = { \frac { \mathrm { e } ^ { x } } { x } } - a \left( x - \ln x \right)$ .若 $f ( x )$ 在(0,1)内有极值,试求 $a$ 的取值范围.

# 学习笔记

# 例13

已知函数 $f ( x ) = \ln x - { \frac { x - a } { x } }$ 其中 $a$ 为常数.若函数 $f ( x )$ 在区间[1,3]上的最小值为 $\frac 1 3$ ，求 $a$ 的值.

# 学习笔记

# 学而思秘籍系列图书|数学

# 思维培养

![](images/2c3d1e56cc93987cc4a63f10f6f973ce3f5dbf7eac62e6a585c1863b98670ffb.jpg)

# 小学秘籍系列

学而思积淀近20年教研经验，培养受益一生的能力。

# 思维提升

![](images/d06a27fbcc30fdb978f2d201085d0a52ec8fd460aced6068c61c2a751d51c95d.jpg)

# 思维突破

# 初中秘籍系列

全面覆盖初中基础知识和重难点，帮助学生夯实基础，拓展认知。

![](images/1983b978a859be9309bd4d5da9b99471bc84002d8eedce53e489ac8cb55d77f2.jpg)

# 高中秘籍系列

全面覆盖高中基础知识和重难点，帮助学生提升能力，突破思维。

# 学而思秘籍系列图书|语文

# 提升素养

![](images/cca20e0a30a96da34d158410ab6ff61ac22b1bd9e4b3b47711aac88e7b91a977.jpg)

# 小学秘籍系列

5大模块+2条主线，能力与素养双向提升。

能力训练

![](images/f9bfa0f9bb9dc20abbd5fb338a0f50549c5d6fb75a73c5e148e64f999698f054.jpg)

# 初中秘籍系列

融合课改四大核心素养，培养爱阅读、 善写作、勤思考、会学习的学生。

# 创新体系|真题研习

![](images/90690bf7ae65780928c48fae0e2a173f3cb169ab5ccb19556ed3e88501fb69ea.jpg)

# 思维创新大通关数学

攻克数学思维难题，通向理想中学。

# 大家一起来“升级”

# 参与方式

您在使用本书时，如有任何疑问或对图书有任何建议，请扫码进行反馈，并查看反馈采纳结果。

![](images/b9c34aa625ed64bfdc45a8c860dcb941d30e663c923bf51094f6e31302538e87.jpg)

# 奖励

您的反馈一经采纳，我们将会送出总价值35元的图书抵扣券（相同内容的反馈，依据反馈时间，奖励前三位）。请扫码关注公众号，并在对话框中发送反馈时填写的手机号，领取抵扣券。

![](images/ce613912bdeb53f9eafbf04d251625a059bf88a6d21d3df63029076c5c79368d.jpg)

# 合理规划学习时间

先自己定一个目标，即制定半年学习规划。

![](images/9879acf1d1b8fbdf1ccdab9ea49f375d38512d14adc894c16d03fe8ab47c3287.jpg)

2 再将目标细化到每一周， 每周学习一本（平均5个考点）。

3 配套课堂巩固的练习， 让学习更有效！

![](images/fee1f52b0ddd9d5679e88dd219ac7bcb0958407bec0392b87a8c4c336609ddc1.jpg)  
·共6级·每级17-26讲