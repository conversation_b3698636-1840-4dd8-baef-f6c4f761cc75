---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 28
source_file: L5-8-数列（一）.md
title: L5-8-数列（一）
type: problem_type
---

# 数列（一）

一本一讲，共26讲（建议5个月学习）

1个考点+14个题型+28个视频

每周一讲（建议学习时间90分钟）

![](images/0e3535bac37406e48959a0feadc0de1bde9dfa0171b2cd304417f1a37288dd50.jpg)

# 视频内容研发团队

学而思优秀老师

学而思优秀老师和一线高级教师联合创作本书试题，并精心录制讲解视频学而思图书APP扫码即可观看

![](images/7c18612a1a429ae60d29f5b5efcf1a02252def7999e7efc0be06ea4e50ff5a2e.jpg)

# 傅博宇 老师

毕业于北京大学元培学院  
网校和北大数院高考数学研究联合课题组成员；  
网校高中创新产品部负责人；  
荣获学而思网校“桃李满天下奖”“出类拔萃奖”等；腾讯网中国好老师；  
青少年教育导师认证；  
科学家长观体系的创立者

![](images/4a0470e78e03f7ebbf42f0c1d9163d20a51d90183bea9ad5f300aafc2e4d19e6.jpg)

# 王侃老师

毕业于北京大学数学系  
学而思网校高中数学教研奠基人；  
学而思网校高中数学S级教师；  
荣获学而思网校“突出贡献奖” “桃李天下奖”等；擅长总结题型特点，提炼思想方法；  
擅长分层教学，因材施教

![](images/19e3778d97a5d6e863d11997c9b00afae283c346685a9018e7496d1ebf498672.jpg)

# 付恒岩 老师

毕业于大连理工大学  
网校高中部理科主讲岗后培训师；  
2020年荣获学而思网校最具魅力奖；  
2019年、2020年荣获学而思网校诲人不倦奖；2020年荣获学而思网校高考优秀评卷人；  
2021年担任新浪教育高考数学直播解析特邀嘉宾；“停课不停学”公益课高中数学主讲老师

![](images/7463b11a352506df6b3c06f74c51125fd08784f8200c5e10fa02bdd5dcd58657.jpg)

# 武洪姣 老师

14年线上线下教学经验；  
学而思网校高中理科教研负责人；  
学而思高中数学特级教师；  
在教学的过程中擅长归纳题型，方法和技巧；  
在高中数学模块中最擅长讲解圆锥曲线和导数；  
无论你是从小数学不好，还是数学一直拔尖，都可以在武老师的课堂上收获很多

# 数列（-

一本一讲，共26讲（建议5个月学习）

11个考点 $+ 1 4$ 个题型 $+ 2 8$ 个视频

每周一讲（建议学习时间90分钟）

# 提升篇

第1讲集合、逻辑、函数初步第2讲基本初等函数与函数 冲刺篇的性质第11讲函数性质的应用第3讲不等式第12讲函数零点与恒成立问题第4讲导数（一）第13讲导数的应用一单调性与极值第5讲导数（二）最值第6讲三角函数第14讲导数的应用一恒成立问题第7讲平面向量与解三角形第15讲导数的应用—零点个数与第8讲数列（一）隐零点问题第9讲立体几何第16讲导数的应用一极值点偏移第10讲解析几何第17讲三角函数与平面向量第18讲解三角形第19讲数列（二）第20讲三视图与球的切接问题  
模块1 数列的概念与性质 2 第21讲空间中的角  
模块2 等差数列与等比数列 9第22讲统计概率  
模块3 数列求和 17第23讲圆锥曲线的几何性质第24讲几何条件的代数转化第25讲圆锥曲线中的最值范围问题第26讲定点定值问题与轨迹方程问参考答案

# 数列（一）

# 直击课堂

<html><body><table><tr><td>知识模块</td><td>考点</td><td>对应例题</td><td>考查概率</td></tr><tr><td>数列的概 念与性质</td><td>递推公式 a与S</td><td>例2 例3 例4</td><td>★★★☆☆ ★★★★☆</td></tr><tr><td>等养数列与</td><td>等美、与应用</td><td></td><td>★</td></tr><tr><td>数列求和</td><td>分组求和法 数列lanl的前n项和 裂项相消法 错位相减法</td><td>例9、例10 例11 例12、例13 例14</td><td>★★★☆☆ ★☆☆☆☆ ★★★☆☆ ★★☆☆☆</td></tr></table></body></html>

# 本讲解读

数列是高中数学的重要内容,又是学习高等数学的基础,在高考中占有重要的地位.高考对数列的考查比较全面,一方面考查等差数列、等比数列的基础知识和基本技能;另一方面常和函数、不等式、方程、解析几何等相关内容交汇在一起综合考查,加以导数和向量等新增内容,使数列题更加有了施展的舞台.本讲将重点以“数列的概念与性质”“等差数列与等比数列”“数列求和”等多个模块进行讲解.

# 模块1数列的概念与性质

# APP扫码观看本模块讲解视频

1知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# $\textcircled{1}$ 数列的定义

按照一定顺序排列的一列数称为数列.数列中的每一个数叫做这个数列的项，数列中的每一项都和它的序号有关，排在第一位的数称为这个数列的首项.

# 数列的分类

（1)按照项数：有穷数列、无穷数列；  
（2)按照前后项的大小关系：递增数列、递减数列、常数列、摆动数列；  
（3）周期数列.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点1：根据规律推导通项公式

# 例1

（1)数列1，-3,5，-7,9,的一个通项公式为(

A. $\scriptstyle a _ { n } = 2 n - 1$ B $\displaystyle { \mathbf { \partial } } . a _ { n } = ( { \mathbf { \partial } } - 1 { \mathbf { \partial } } ) ^ { n } ( 1 { \mathbf { \partial } } - 2 n )$ C. $a _ { n } = ( \ - 1 ) ^ { n } ( 2 n - 1 )$ $\mathrm { D } . a _ { n } = ( \mathbf { \nabla } - 1 ) ^ { n } ( 2 n + 1 )$

# 学习笔记

(2)古希腊毕达哥拉斯学派的数学家研究过各种多边形数，如三角形数1,3,6,10,…，第n个三角形数为n(n+1） ${ \frac { n ( n + 1 ) } { 2 } } = { \frac { 1 } { 2 } } n ^ { 2 } + { \frac { 1 } { 2 } } n .$ 记第 $n$ 个 $k$ 边形数为$N ( n , k ) ( k \geqslant 3 )$ ,以下列出了部分 $k$ 边形数中第 $n$ 个数的表达式：

三角形数 $N ( n , 3 ) = \frac { 1 } { 2 } n ^ { 2 } + \frac { 1 } { 2 } n$   
正方形数 $N ( { n } , 4 ) = { n } ^ { 2 }$ ，  
五边形数 $N ( n , 5 ) = \frac { 3 } { 2 } n ^ { 2 } - \frac { 1 } { 2 } n$   
六边形数 $N ( n , 6 ) = 2 n ^ { 2 } - n$ ，  
可以推测 $N ( n , k )$ 的表达式,由此计算 $N ( 1 0 , 2 4 ) =$

# 学习笔记

# 知识点睛

# $\textcircled{8}$ 数列的表示方法

列表法、图象法、通项公式法、递推公式法.

(1)通项公式

如果数列 $\left\{ a _ { n } \right\}$ 的第 $n$ 项 $a _ { n }$ 与 $n$ 之间的关系可以用一个函数解析式 $a _ { n } = f ( n )$ 来表示，这个公式就叫做这个数列的通项公式.

(2)递推公式

递推公式也是给出数列的一种重要形式.如果已知数列 $\left\{ a _ { n } \right\}$ 的第1项(或前几项)，且任意一项 $a _ { n }$ 与它的前一项 $a _ { n - 1 }$ (或前几项)间的关系可以用一个公式来表示，那么这个公式就叫做数列的递推公式.

（3）前 $n$ 项和公式

数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和用 $S _ { n }$ 来表示，如果 $S _ { n }$ 与 $n$ 的关系可用一个公式表示，这个公式就叫做这个数列的前 $n$ 项和公式.

数列的前 $n$ 项和 $S _ { n } = a _ { 1 } + a _ { 2 } + \cdots + a _ { n - 1 } + a _ { n } .$ 于是有 $a _ { n } = \left\{ \begin{array} { l l } { S _ { 1 } , n = 1 , } \\ { \qquad } \\ { S _ { n } - S _ { n - 1 } , n \geq 2 , n \in \mathbf { N } ^ { * } . } \end{array} \right.$

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点2：递推公式

# 例2

(1)在数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 中， $a _ { 1 } = - 2 , a _ { n + 1 } = 3 a _ { n } + n$ ，则 $a _ { 4 } =$ （ ）

A. $- 1 3$ B.-36 C.-14 D.-41

# 学习笔记

(2)已知数列 $\left\{ a _ { n } \right\}$ 满足： $\forall m , n \in \mathbf { N } ^ { * }$ ，都有 $a _ { n } \cdot a _ { m } = a _ { n + m }$ ，且 $a _ { 1 } = \frac { 1 } { 2 }$ ，那么 $a _ { 5 } =$ （）

A32 $\mathrm { B } . \frac { 1 } { 1 6 }$ $\mathrm { C } . \frac { 1 } { 4 }$ $\mathrm { D } . { \frac { 1 } { 2 } }$

# 学习笔记

# 考点3:a与S

# 例3

(1)已知数列 $\left\{ \begin{array} { l } { a _ { n } \rule { 0 ex } { 5 ex } } \end{array} \right\}$ 的前 $n$ 项和 $S _ { n } = n ^ { 2 } + n + 1$ ，则 $a _ { \scriptscriptstyle 1 } + a _ { \scriptscriptstyle 4 } = ( \qquad )$

A.10 B.11 C.12 D.13

# 学习笔记

（2）已知数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 的前 $n$ 项和 $S _ { n } = 3 + 2 ^ { n }$ ，则数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 的通项公式为

# 学习笔记

# 知识点睛

# $\textcircled { 4 9 }$ 数列的单调性

（1）判断数列的单调性：两种方法$\textcircled{1}$ 邻项比较法：对于任意的正整数 $n$ ，比较 $a _ { n }$ 与 $a _ { n + 1 }$ 的大小.若 $a _ { n + 1 }$ 恒大于 $a _ { n }$ ，则 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 单调递增，若 $a _ { n + 1 }$ 恒小于 $a _ { n }$ ，则 $\left\{ a _ { n } \right\}$ 单调递减.$\textcircled{2}$ 函数特性法：数列是一个特殊的函数，因此判断函数单调性的方法同样适用于数列.我们可以先判断相应函数的单调性，进而得到数列的单调性.

(2)数列单调性的应用：求数列中的最大(小)项问题

$\textcircled{1}$ 利用当 $\left\{ { a _ { n } { \geqslant } a _ { n + 1 } } \right. ,$ 时， $a _ { n }$ 是数列中的最大项;当 $\left\{ { a _ { n } \leqslant a _ { n + 1 } , } \right.$ 时， $\boldsymbol { a } _ { n }$ 是数列中的最小项来求数列的最值.$\textcircled{2}$ 也可以利用函数方法判断出数列的单调性，进一步求出数列的最大(小)项.

# $\textcircled{5}$ 数列的周期性

类似于周期函数的概念，对于数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ ，若存在一个常数 $T ( T \in \mathbf { N } ^ { * }$ ),使得对任意的正整数 $n$ ，都有 $\boldsymbol { a } _ { n + T } = \boldsymbol { a } _ { n }$ 成立，则称 $\left\{ a _ { n } \right\}$ 是周期为 $T$ 的周期数列.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点4：数列的单调性与最值

# 例4

(1)设数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 的通项公式为 $a _ { n } = n ^ { 2 } + b n$ ，若数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 是单调递增数列，则实数 $b$ 的取值范围为

# 学习笔记

(2）数列 $\left\{ a _ { n } \right\}$ 的通项 $a _ { n } = \frac { n } { n ^ { 2 } + 9 0 }$ ，则数列 $\left\{ \boldsymbol { a } _ { n } \right\}$ 中的最大项是(

A.第9项 B.第8项和第9项C.第10项 D.第9项和第10项

# 学习笔记

# 模块2等差数列与等比数列

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师高效学知识

# 知识点睛

# $\textcircled{1}$ 基本公式与性质

<html><body><table><tr><td colspan="2">定义</td><td>等差数列</td><td>等比数列</td></tr><tr><td colspan="2">（递推公式）</td><td>an+1-an=d（∀n∈N*）</td><td>=q(∀n∈N*,q≠0） an+1 an</td></tr><tr><td colspan="2">通项公式</td><td>a=a+(n-1)d 推广:an=am+(n-m)d</td><td>推广:an=amq-” [n·a,q=1 an=a·qa-1</td></tr><tr><td colspan="2">求和公式</td><td>+ 2 2 +n(n-1)d S=（a+a）n =na</td><td>S={a(1-q&quot;） 1-q 若m+n=p+q，则an·an=ap ，q1</td></tr><tr><td rowspan="3"></td><td>角标和性质</td><td>特别地,若m+𝑛 =2p,则αm+an=2ap 若m+n=p+q,则am+an=ap+aq；</td><td>aq;特别地,若m+n=2p,则am· an=</td></tr><tr><td>性质等距性质</td><td>an,an+m，an+2m为等差数列,公差 为md</td><td>公比为q an,an+m,an+2m…为等比数列，</td></tr><tr><td>联系</td><td>若{an}是等差数列，则{a}是 等比数列，公比为ad</td><td>若{an}是正项的等比数列， 则{logan}是等差数列， 公差为log.q</td></tr></table></body></html>

# $\textcircled { \scriptsize { 1 } }$ 基本量计算

# 考法1.等差数列、等比数列基本量的计算

(1)有关等差、等比数列的简单计算问题，通常涉及五个元素： $\smash { a _ { 1 } \ldots d ( q ) \ldots n \ldots a _ { n } \ldots S _ { n } }$ ，项数 $n$ 一般是已知数，所以我们把剩下的4个元素叫做等差(等比)数列的基本量，其中 $a _ { 1 } , d ( q )$ 是确定等差(等比)数列的两个基本元素，只要求出这两个元素，便可以求出 $a _ { n }$ 和 $S _ { n }$ .因此解决这类问题,通常可以利用方程的思想,根据已知条件，由通项公式和求和公式列出关于 $a _ { 1 }$ 和 $d ( q )$ 的方程(组)，解出 $a _ { \uparrow }$ 和 $d ( q )$ ,进而去求解.这是求解基本量问题的最基本最一般的方法,必须要熟练掌握.

(2)等差(等比)数列的性质也是高考必考内容，要灵活运用一些常见性质简化计算过程，在解题过程中实现化繁为简的目的.

(3)数列部分的小题，往往还可以利用选填的特殊技巧来快速求解，比如取特殊数列、直接猜数列然后验证等等，平时做题时要着力培养“小题巧做”的意识.

# 考法2.等差数列、等比数列的综合计算问题

这类问题将等差数列与等比数列结合在一起，综合考查两类数列的相应公式和计算能力，在近几年高考中往往以小题或者解答题第一问的形式出现.解这类问题依然采用求基本量的思路，但要注意具体计算的方法.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点5：等差、等比基本量

# 例5

(1)已知等差数列 $\left\{ a _ { n } \right\}$ 满足 $a _ { 3 } + a _ { 5 } = - 1 4 , a _ { 1 } a _ { 7 } = 1 3$ ，则 $a _ { 2 } a _ { 6 } =$ （）

A.33 B.16 C.13 D.12

# 学习笔记

(2)设等差数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和为 $S _ { n }$ ，若 $S _ { 1 3 } = 1 0 4 , a _ { 6 } = 5$ ,则数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 的公差为（ ）

A.2 B.3 C.4 D.5

# 学习笔记

(3)公比为 $q$ 的等比数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 满足 $| q \ | > 1 , a _ { 2 } + a _ { 7 } = 2 , a _ { 4 } a _ { 5 } = - 1 5$ ，则 $a _ { 1 2 }$ =(）

A $\frac { 9 } { 5 }$ B. 95 $\mathrm { C } . \frac { 2 5 } { 3 }$ D. 2

# 学习笔记

(4)已知等比数列 $\left\{ a _ { n } \right\}$ 的公比为 $q$ ,其前 $n$ 项和为 $S _ { n }$ ，若 $S _ { 3 } , S _ { 9 } , S _ { 6 }$ 成等差数列，则 $q ^ { 3 }$ 等于（）

A.- B.1 C. 1或1 D.-1或

# 学习笔记

# 考点6：等差、等比数列的基本性质与应用

# 例6

(1)在等差数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 中,若 $a _ { 3 } + a _ { 7 } = 4$ ，则 $a _ { 2 } + a _ { 5 } + a _ { 8 }$ 等于（）

A.6 B.9 C.12 D.3

# 学习笔记

(2)两个等差数列 $\left\{ \left. a \right. _ { n } \right\}$ 和 $\left\{ \begin{array} { l } { { b _ { n } } } \end{array} \right\}$ ,其前 $n$ 项和分别为 $S _ { n } , T _ { n }$ 且 $\frac { S _ { n } } { T _ { n } } = \frac { 7 n + 2 } { n + 3 }$ 则 等于（ ）b

A.4 B.37 c.74 D.149   
24

# 学习笔记

(3)已知 $\left\{ a _ { n } \right\}$ 为等差数列,它的前 $n$ 项和为 $S _ { n }$ 若， $\cdot \frac { S _ { 6 } } { S _ { 3 } } = 3$ =3，则 $\big | \frac { S _ { 9 } } { S _ { 6 } } = ( \qquad )$

A.2 B $\frac { 7 } { 3 }$ $\mathrm { C } . \frac { 3 } { 2 }$ $\mathrm { D } . \frac { 5 } { 3 }$

# 学习笔记

（4）设 $\left\{ a _ { n } \right\}$ 是等比数列,则下列结论中正确的是( ）

A.若 $a _ { \scriptscriptstyle 1 } = 1 { , } a _ { \scriptscriptstyle 5 } = 4$ ，则 $a _ { 3 } = - 2$ B.若 $a _ { 1 } + a _ { 3 } > 0$ ，则 $a _ { 2 } + a _ { 4 } > 0$ C.若 $a _ { 2 } > a _ { 1 }$ ，则 $a _ { 3 } > a _ { 2 }$ （202 D.若 $a _ { 2 } > a _ { 1 } > 0$ ，则 $a _ { 1 } + a _ { 3 } > 2 a _ { 2 }$

# 学习笔记

（5）已知数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 是等比数列， $a _ { 2 } = 2 , a _ { 5 } = { \frac { 1 } { 4 } }$ 令 $T _ { n } = a _ { 1 } a _ { 2 } + a _ { 2 } a _ { 3 } + \cdots +$ $a _ { n } a _ { n + 1 }$ ，则 $T _ { n } = ( \begin{array} { l l l } { \mathrm { ~ } } & { } & { } \end{array} )$

A.16(1-4") $\begin{array} { l } { { \mathrm { B . ~ } 1 6 \left( 1 - 2 \mathrm { ~ } ^ { - n } \right) } } \\ { { \mathrm { ~ } } } \\ { { \mathrm { D . ~ } \displaystyle \frac { 2 2 } { 3 } ( 1 - 2 \mathrm { ~ } ^ { - n } ) } } \end{array}$ $\mathrm { C } . \frac { 3 2 } { 3 } ( 1 - 4 ^ { - n } )$

# 学习笔记

# 知识点睛

# $\textcircled { 6 9 }$ 等差或等比数列的判定与证明

（1)对于等差数列或等比数列的判定与证明，往往从以下几个角度入手：等差数列的等价条件： $\left\{ \left. a _ { n } \right\} \right.$ 是等差数列 $\Leftrightarrow$   
$\textcircled{1}$ 定义： $\forall n \in \mathbf { N } ^ { * }$ ， $a _ { n + 1 } - a _ { n } = d , d$ 为常数；  
$\textcircled{2}$ 等差中项： $\forall n \in \mathbf { N } ^ { * } , a _ { n + 2 } - a _ { n + 1 } = a _ { n + 1 } - a _ { n }$   
$\textcircled{3}$ 通项公式： $\forall n \in \mathbf { N } ^ { * } , a _ { n } = k n + b , k , b$ 为常数；  
$\textcircled{4}$ 前 $n$ 项和公式： $\forall n \in \mathbf { N } ^ { * }$ ${ { S } _ { n } } = { A { n } ^ { 2 } } + B n , A , B$ 为常数.

等比数列的等价条件： $\left\{ a _ { n } \right\}$ 是等比数列 $\Leftrightarrow$

$\textcircled{1}$ 定义： $\forall n \in \mathbf { N } ^ { * }$ ， $a _ { n + 1 } = q a _ { n } , q$ 为非零常数；$\textcircled{2}$ 等比中项： $\forall n \in \mathbf { N } ^ { * }$ $\frac { a _ { n + 2 } } { a _ { n + 1 } } = \frac { a _ { n + 1 } } { a _ { n } } , a _ { n } \cdot a _ { n + 1 } \cdot a _ { n + 2 } \ne 0 ;$ $\textcircled{3}$ 通项公式： $\forall n \in \mathbf { N } ^ { * } , a _ { n } = c q ^ { n } , c , q \neq 0$ 为常数.(2)高考的常见考法是出现在数列解答题的一或二小问中，根据题干已给的递推结构，证明某个变形的数列是等差或等比数列.此类问题大多是直接由等差(等比)数列的定义，证明该新数列的递推关系满足等差或等比数列.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点7：等差、等比数列的判定

# 例7

记 $S _ { n }$ 为等比数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和,已知 $S _ { 2 } = 2 , S _ { 3 } = - 6$

(1)求 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 的通项公式；   
(2）求 $S _ { n }$ ,并判断 $S _ { n + 1 } , S _ { n } , S _ { n + 2 }$ 是否成等差数列.

# 学习笔记

# 例8

在数列 $\left\{ a _ { n } \right\}$ 中， $a _ { 1 } = 2 , a _ { n + 1 } = 4 a _ { n } - 3 n + 1 , n \in \mathbf { N } ^ { * }$ 证明：数列 $\{ a _ { n } - n \}$ 为等比数列.

# 学习笔记

# 模块3数列求和

# APP扫码观看本模块讲解视频

知识与方法 例题与练习 全程跟老师 高效学知识

# 知识点睛

# $\textcircled{1}$ 公式法

(1)等差数列前 $n$ 项和： $S _ { n } = \frac { \left( a _ { 1 } + a _ { n } \right) n } { 2 } = n a _ { 1 } + \frac { n \left( n - 1 \right) } { 2 } d .$

（2)等比数列前n项和:S=a(1-q"） $\begin{array} { r } { \{ S _ { n } = \left\{ \begin{array} { l l } { n a _ { 1 } , q = 1 , } \\ { \qquad } \\ { \displaystyle \frac { a _ { 1 } \left( 1 - q ^ { n } \right) } { 1 - q } , q \neq 1 . } \end{array} \right. } \end{array}$ （3)数列{an},=n²:S=n(n+1)(2n+1).

$\left\{ a _ { n } \right\}$ $, a _ { n } = n ^ { 3 } : S _ { n } = \left[ { \frac { n ( n + 1 ) } { 2 } } \right] ^ { 2 } .$

(5)求等差数列 $\{ \begin{array} { l } { | a _ { n }  |  \} } \end{array}$ 的前 $n$ 项和：一般地, $\left\{ \begin{array} { l } { \mathbf { \Lambda } | a _ { n } } \end{array} \right.$ 的前 $n$ 项和$S _ { n }$ 与 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和 ${ \cal { S } } _ { n } ^ { \prime }$ ：当 $a _ { k } \geqslant 0$ 时，有 ${ S _ { n } } ^ { \prime } = S _ { n }$ ；当 $a _ { k } < 0$ 时,有 ${ \cal { S } } _ { n } ^ { \prime }$ $\mathbf { \omega } = - S _ { n }$ .若在数列中，若干项非负，而其余各项为负，设其和分别为$S ^ { + } , S ^ { - }$ ，,则有 $S _ { n } = S ^ { + } + S ^ { - } , S _ { n } ^ { \prime } = S ^ { + } - S ^ { - } = 2 S ^ { + } - S _ { n } = S _ { n } - 2 S ^ { - } .$

# $\textcircled { 2 }$ 倒序相加法

数列 $\left\{ a _ { n } \right\}$ ,若 $a _ { k } + a _ { n - k + 1 } = p ( p$ 为定值),那么求这个数列的 $S _ { n }$ 即可用倒序相加法，如等差数列的 $S _ { n }$ 即是用此法推导的.

# $\textcircled{8}$ 错位相减法

数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ ，若其各项是由一个等差数列和一个等比数列的对应项之积构成的，那么这个数列的前 $n$ 项和即可用此法来求，如等比数列的前 $n$ 项和就是用此法推导的.

# 裂项相消法

把数列的通项拆成两项之差，在求和时中间的一些项可以相互抵消，从而求得其和.常见的拆项公式有：

①(n+D1 $\displaystyle \frac { 1 } { n \ ( n + p ) } = \frac { 1 } { p } \cdot \left( \frac { 1 } { n } - \frac { 1 } { n + p } \right)$ （3）++1- （4）n（n+2）（n2

# 分组求和法

有一类数列，既不是等差数列，也不是等比数列，若将这类数列适当拆开，可分为几个等差、等比或常见的数列，即先分别求和，然后再合并.

# 重点笔记

# 精讲精练

# 拍照批改秒判对错

# 考点8：分组求和法

# 例9

若数列 $\left\{ \left. a _ { n } \right\} \right.$ 的通项公式为 $a _ { n } = 2 ^ { n - 1 } + 2 n - 1$ ，则数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 的前 $n$ 项和为

A.2”+n²-1 $\begin{array} { l } { { \mathrm { B } . 2 ^ { n + 1 } + n ^ { 2 } - 1 } } \\ { { \ } } \\ { { \mathrm { D } . 2 ^ { n } + n - 2 } } \end{array}$   
C.2"+1 +n²-2

# 学习笔记

# 例10

已知 $f ( n ) = \left\{ \begin{array} { l l } { { n ^ { 2 } , n \dag \mathrm { \ - ~ } \forall \mathrm { \ - ~ } \mathrm { \ - ~ } \mathrm { \ - ~ } } } \\ { { - n ^ { 2 } , n \dag \mathrm { \ - ~ } \mathrm { \ - ~ } \mathrm { \ - ~ } \mathrm { \ - ~ } } } \end{array} \right.$ 奇数， 且 $a _ { n } = f ( n ) + f ( n + 1 )$ ，则 $a _ { 1 } + a _ { 2 } + a _ { 3 } + \cdots +$ 王偶数，$a _ { 2 0 1 7 }$ 的值为（ ）

A.0 B.2 019   
C.-2 019 D.2 018 ×2 019

# 学习笔记

# 考点9：数列a的前n项和

# 例11

已知等差数列 $\left\{ \left. a _ { n } \right\} \right.$ $\ : \ : , a _ { 2 } \ : + a _ { 3 } \ : + a _ { 4 } = - 3 , a _ { 1 } = - 5 . \ :$

(1)求数列 $\{  a _ { n } \}$ 的通项公式； (2）记数列 $\left| a _ { n } \right. \left| \begin{array} { l } { \begin{array} { r } \end{array} } \end{array} \right\}$ 的前 $n$ 项和为 $T _ { n }$ ，求 $T _ { 5 0 }$

# 学习笔记

# 考点10:裂项相消法

# 例12

数列 $\left\{ a _ { n } \right\} , \left\{ b _ { n } \right\}$ 满足 $a _ { n } b _ { n } = 1 , a _ { n } = \left( n + 1 \right) \left( n + 2 \right)$ ，则 $\left\{ \begin{array} { l }  \displaystyle b _ { n } \right\} \end{array}$ 的前10项之和为（）

A $\frac 1 4$ （204 $\mathrm { B } . { \frac { 7 } { 1 2 } }$ $\mathrm { C } . \frac { 3 } { 4 }$ $\mathrm { D } . { \frac { 5 } { 1 2 } }$

# 学习笔记

# 例13

已知幂函数f（x）=x的图象过点(4,2），令an= $a _ { n } = { \frac { 1 } { f ( n + 1 ) \ + f ( n ) } } { \big ( } n \in$ $\mathbf { N } ^ { * }$ ),记数列 $\left\{ { { a } _ { n } } \right\}$ 的前 $n$ 项和为 $S _ { n }$ ，则 $S _ { 2 0 1 8 } = (  { \mathrm { ~ \textrm ~ { ~ ~ } ~ } } )$

A. $\sqrt { 2 \ 0 1 8 } \ + 1$ （204 B $\cdot { \sqrt { 2 { \ 0 1 8 } } } - 1$ C. $\sqrt { 2 \ 0 1 9 } \ + 1$ D.2019-1

# 学习笔记

# 考点11:错位相减法

# 例14

数列 $\left\{ \begin{array} { l l } { ( 2 n + 1 ) } & { \cdot 2 ^ { n } } \end{array} \right\}$ 的前 $n$ 项和为(

A. $( 2 n - 1 ) \ \cdot 2 ^ { n + 2 } \ - 2$ B $\mathbf { \partial } - ( 2 n - 1 ) \mathbf { \partial } \cdot 2 ^ { n + 1 } \mathbf { \partial } - 2$ C. $( 2 n - 1 ) \ \cdot 2 ^ { n + 1 } \ + 2$ I $) . ( 2 n - 3 ) ~ \cdot 2 ^ { n + 1 } + 2$

# 学习笔记

# 自我测试

# 拍照批改秒判对错

# 测试1

在数列 $1 , 1 , 2 , 3 , 5 , 8 , x , 2 1 , 3 4 , 5 5$ 中， $x$ 等于（ ）

A.11 B.12 C.13 D.14

# 测试2

数列 $\sqrt { 2 }$ $\sqrt { 5 } , 2 \sqrt { 2 }$ ， $\sqrt { 1 1 }$ ,…的一个通项公式是(

A. $a _ { n } = { \sqrt { 3 n - 3 } }$ B $\mathbf { \xi } _ { a _ { n } } = \sqrt { 3 n - 1 }$ C. $a _ { n } = { \sqrt { 3 n + 1 } }$ $) . a _ { n } = { \sqrt { 3 n + 3 } }$

# 测试3

已知数列 $\{ a _ { n } \}$ 为等差数列,且 $a _ { 3 } = - 4 , a _ { 7 } = - 1 6$ ，则 $a _ { \scriptscriptstyle 5 } = ( \begin{array} { l l l } { \mathrm { ~ } } & { } & { } \end{array} )$

A.8 B.-8 C.10 D. $- 1 0$

# 测试4

在等比数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 中， $a _ { \scriptscriptstyle 1 } = - 1 6 , a _ { \scriptscriptstyle 4 } = 8$ ，则 $a _ { 7 } = ( \begin{array} { l l l } { \mathrm { ~ } } & { } & { } \end{array} )$

A.-4 B.±4 C. $^ { - 2 }$ D.±2

# 测试5

已知数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 满足 $a _ { n } \ = { \frac { 1 } { 4 n ^ { 2 } - 1 } } \left( n \in \mathbf { N } ^ { * } \right.$ ），则数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 的前10项和为（）

$\mathrm { A } . \frac { 2 0 } { 2 1 }$ $\mathrm { B } . { \frac { 1 8 } { 1 9 } }$ $\mathrm { C } . \frac { 1 0 } { 2 1 }$ $\mathrm { D } . { \frac { 9 } { 1 9 } }$

# 学而思秘籍系列图书|数学

# 思维培养

# 思维提升

# 思维突破

学而思 8秘籍 推理比4小学数学思维培养

![](images/40f9fa1c0b99cafe4a092384826f9569f90c619ee16dbcff4cdf44030edc041f.jpg)

![](images/6167f850b3e84a4555ad7478a1527a7b3871e00c54940b07dbd3f000468ed04e.jpg)

# 小学秘籍系列

学而思积淀近20年教研经验，培养受益一生的能力。

# 初中秘籍系列

全面覆盖初中基础知识和重难点，帮助学生夯实基础，拓展认知。

# 高中秘籍系列

全面覆盖高中基础知识和重难点，帮助学生提升能力，突破思维。

# 学而思秘籍系列图书|语文

# 提升素养

# 能力训练

![](images/7f74dd8b276dea5d5ca6df3e6f245460a397facbba193d9995b0c0024a6b395b.jpg)

# 小学秘籍系列

5大模块+2条主线，能力与素养双向提升。

![](images/c79b5205a8d5acf96913268814eda6ec7285e34804eeb1e84f86cd408997b707.jpg)

# 初中秘籍系列

融合课改四大核心素养，培养爱阅读， 善写作、勤思考、会学习的学生。

# 创新体系|真题研习

![](images/9fc37a28f308e8321524ae4946123d83e729cc12293da5a37e3d5ef6bd0da2a2.jpg)

# 思维创新大通关数学

攻克数学思维难题，通向理想中学。

# 大家一起来“升级”

# 参与方式

您在使用本书时，如有任何疑问或对图书有任何建议，请扫码进行反馈，并查看反馈采纳结果。

# 奖励

![](images/b2bb3c55848d46bb0a96bb1ca15254101747987e7b4dd1ecb5f039f9043a23b7.jpg)

您的反馈一经采纳，我们将会送出总价值35元的图书抵扣券（相同内容的反馈，依据反馈时间，奖励前三位）。请扫码关注公众号，并在对话框中发送反馈时填写的手机号，领取抵扣券。

![](images/f5d6ca129c36d5f3f0d4b325ae311d9a8f3a6523cdb2cf49d8e8f91ce9693b8c.jpg)

# 合理规划学习时间

先自己定一个目标，即制定半年学习规划。

再将目标细化到每一周，2每周学习一本（平均5个考点）。

![](images/6b298f968f76eb7e691f28ffd4d5d0e641c299efbee32cb491f1144053eff4f0.jpg)

3 配套课堂巩固的练习， 让学习更有效！

![](images/5588ed834e2d3591d282b13dbaf20bc777081f1e4ca7b4fc30b2f5f9e7ee6690.jpg)  
·共6级·每级17-26讲