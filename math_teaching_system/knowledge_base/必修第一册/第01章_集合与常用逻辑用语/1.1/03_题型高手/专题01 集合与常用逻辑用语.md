---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 20
source_file: 专题01 集合与常用逻辑用语（学生版）.md
title: 专题01 集合与常用逻辑用语（学生版）
type: problem_type
---

# 专题01 集合与常用逻辑用语

# 2024年真题研析

1．（2024新高考Ⅰ卷·1）已知集合 $A = \{ x | - 5 < x ^ { 3 } < 5 \} , B = \{ - 3 , - 1 , 0 , 2 , 3 \}$ ，则（ ）

A． B． C.{-3,-1,0} D.{- 1,0,2}

2．（2024 新高考Ⅱ卷·2）已知命题 $p$ ： $\forall x \in \mathbf { R }$ ， $\vert x + 1 \vert { > } 1$ ；命题 $q$ ： $> 0$ ， $\boldsymbol { x } ^ { 3 } = \boldsymbol { x }$ ，则（ ）

A． $p$ 和 $q$ 都是真命题 B． $\neg p$ 和 $q$ 都是真命题C． $p$ 和 $\neg q$ 都是真命题 D． $\neg p$ 和 $\neg q$ 都是真命题

# 近年真题精选

1．（2022 新高考Ⅰ卷·1）若集合 $M = \{ x \mid \sqrt { x } < 4 \} , \quad N = \{ x \mid 3 x \geq 1 \}  _ { , \mathrm { ~ \ } \sharp \mid \mathrm { { \scriptsize / l } \mathrm { { \scriptsize ~ / l } \stackrel { \sim } { \sim } ( \tau ~ ) } } }$

$$
\mathbf { A } _ { . } \ \left\{ x \middle | 0 \leq x < 2 \right\} \qquad \mathbf { B } _ { . } \ \left\{ x \middle | \frac { 1 } { 3 } \leq x < 2 \right\} \qquad \mathbf { C } _ { . } \ \left\{ x \middle | 3 \leq x < 1 6 \right\} \qquad \mathbf { D } _ { . } \ \ \left\{ x \middle | \frac { 1 } { 3 } \leq x < 1 6 \right\}
$$

2．（2023 新高考Ⅰ卷·1）已知集合 ， ， 则 $M \cap N =$ （ ）

A． B． C． D．

3．（2022 新高考Ⅱ卷·1）已知集合 $A = \left\{ - 1 , 1 , 2 , 4 \right\} , B = \left\{ x \left\| x - 1 \right\| \leq 1 \right\}$ ，则 $A \cap B = { \textrm { ( ) } }$

A． B． C． D．

4．（2023 新高考Ⅱ卷·2）设集合 $A = \{ 0 , - a \}$ ， $B = \{ 1 , a - 2 , 2 a - 2 \}$ ，若 $A \subseteq B$ ，则 $a =$

（ ）．

A．2 B．1 C． 2 D．

5．（2023 新高考Ⅰ卷·7）记 为数列 的前 项和，设甲： 为等差数列；乙：为等差数列，则（ ）

A．甲是乙的充分条件但不是必要条件B．甲是乙的必要条件但不是充分条件C．甲是乙的充要条件D．甲既不是乙的充分条件也不是乙的必要条件

# 必备知识速记

# 一、元素与集合

1、集合的含义与表示  
某些指定对象的部分或全体构成一个集合．构成集合的元素除了常见的数、点等数学对象  
外，还可以是其他对象

2、集合元素的特征

（1）确定性：集合中的元素必须是确定的，任何一个对象都能明确判断出它是否为该集合  
中的元素（2）互异性：集合中任何两个元素都是互不相同的，即相同元素在同一个集合中不能重复  
出现（3）无序性：集合与其组成元素的顺序无关

3、元素与集合的关系元素与集合之间的关系包括属于(记作 $a \in A$ )和不属于(记作 $a \not \in A$ )两种

4、集合的常用表示法集合的常用表示法有列举法、描述法、图示法(韦恩图)

# 5、常用数集的表示

<html><body><table><tr><td>数集</td><td>自然数集</td><td>正整数集</td><td>整数集</td><td>有理数集</td><td>实数集</td></tr><tr><td>符号</td><td>Nä</td><td>N或N</td><td>Z</td><td>Q•</td><td>R</td></tr></table></body></html>

# 二、集合间的基本关系

（1）子集：一般地，对于两个集合 $A$ 、 $B$ ，如果集合 $A$ 中任意一个元素都是集合 $B$ 中的元素，我们就说这两个集合有包含关系，称集合 $A$ 为集合 $B$ 的子集 ，记作 $A \subseteq B$ （或$B \supseteq A$ ），读作“ $A$ 包含于 $B$ ”（或“ $B$ 包含 ”）.

（2）真子集：对于两个集合 $A$ 与 $B$ ，若 $A \subseteq B$ ，且存在 $b \in B$ ，但 $b \notin A$ ，则集合 $A$ 是集合$B$ 的真子集，记作 $A \oplus B$ （或 $B \supsetneq A$ ）.读作“ $A$ 真包含于 $B$ ”或“ $B$ 真包含 $A$ ”.

（3）相等：对于两个集合 $A$ 与 $B$ ，如果 $A \subseteq B$ ，同时 $B \subseteq A$ ，那么集合 $A$ 与 $B$ 相等，记作 $A = B$ ．

（4）空集：把不含任何元素的集合叫做空集，记作 $\oslash$ ； $\oslash$ 是任何集合的子集，是任何非空集合的真子集.

# 三、集合的基本运算

（1）交集：由所有属于集合 $A$ 且属于集合 $B$ 的元素组成的集合，叫做 $A$ 与 $B$ 的交集，记作 ，即

（2）并集：由所有属于集合 $A$ 或属于集合 $B$ 的元素组成的集合，叫做 $A$ 与 $B$ 的并集，记作 ，即

（3）补集：对于一个集合 $A$ ，由全集 $U$ 中不属于集合 $A$ 的所有元素组成的集合称为集合相对于全集 的补集，简称为集合 的补集，记作 ，即 ．

# 四、集合的运算性质

$$
\begin{array} { r } { A \cap A = A , A \cap \mathcal { O } = \emptyset , A \cap B = B \cap A , A \cap B \subseteq A , A \cap B \subseteq B . } \\ { \ A \cup A = A , A \cup \mathcal { O } = A , A \cup B = B \cup A , A \subseteq A \cup B , B \subseteq A \cup B . } \end{array}
$$

（3） $A \cap ( C _ { \upsilon } A ) = \varnothing \ , A \cup ( C _ { \upsilon } A ) = U \ , C _ { \upsilon } \left( C _ { \upsilon } A \right) = A \ .$ （4） $A \cap B = A \Leftrightarrow A \cup B = B \Leftrightarrow A \subseteq B \Leftrightarrow { \mathfrak { H } } B \subseteq { \mathfrak { g } } A \Leftrightarrow A \cap { \mathfrak { H } } B = \emptyset$

# 【集合常用结论】

（1）若有限集 $A$ 中有 $\boldsymbol { \eta }$ 个元素，则 $A$ 的子集有 $2 ^ { n }$ 个，真子集有 $2 ^ { n } - 1$ 个，非空子集有 $2 ^ { n } - 1$   
个，非空真子集有 $2 ^ { n } \cdot 2$ 个．  
（2）空集是任何集合 $A$ 的子集，是任何非空集合 $B$ 的真子集  
（3） $A \subseteq B \Leftrightarrow A \cap B = A \Leftrightarrow A \cup B = B \Leftrightarrow C _ { \scriptscriptstyle U } B \subseteq C _ { \scriptscriptstyle U } A .$   
（4） $C _ { U } ( A \cap B ) = ( C _ { U } A ) \cup ( C _ { U } B ) \ , C _ { U } ( A \cup B ) = ( C _ { U } A ) \cap ( C _ { U } B ) \ .$

# 五、充分条件、必要条件、充要条件

1、定义  
如果命题“若 $p$ ，则 $q$ ”为真（记作 $p \Rightarrow q$ ），则 $p$ 是 $q$ 的充分条件；同时 $q$ 是 $p$ 的必要条  
件．

2、从逻辑推理关系上看

（1）若 $p \Rightarrow q$ 且 $q \oplus p$ ，则 $p$ 是 $q$ 的充分不必要条件；（2）若 $p \spadesuit q$ 且 $q \Rightarrow p$ ，则 $p$ 是 $q$ 的必要不充分条件；（3）若 $p \Rightarrow q$ 且 $q \Rightarrow p$ ，则 $p$ 是 $q$ 的的充要条件（也说 $p$ 和 $q$ 等价）；（4）若 $p \bullet q$ 且 $q \oplus p$ ，则 $p$ 不是 $q$ 的充分条件，也不是 $q$ 的必要条件．

# 六、全称量词与存在量词

（1）全称量词与全称量词命题．短语“所有的”、“任意一个”在逻辑中通常叫做全称量词，并用符号“ ”表示．含有全称量词的命题叫做全称量词命题．全称量词命题“对 $M$ 中的任意一个 $\chi$ ，有 $p ( x )$ 成立”可用符号简记为“ ”，读作“对任意 属于

$M$ ，有 $p ( x )$ 成立”．

（2）存在量词与存在量词命题．短语“存在一个”、“至少有一个”在逻辑中通常叫做存在量词，并用符号“ ”表示．含有存在量词的命题叫做存在量词命题．存在量词命题“存在 $M$ 中的一个 $x _ { 0 }$ ，使 $p ( x _ { 0 } )$ 成立”可用符号简记为“ $\exists x _ { 0 } \in M , P ( x _ { 0 } )$ ”，读作“存在$M$ 中元素 $x _ { 0 }$ ，使 $p ( x _ { 0 } )$ 成立”（存在量词命题也叫存在性命题）

# 七、含有一个量词的命题的否定

（1）全称量词命题 $p : \forall x \in M , p ( x )$ 的否定 $\neg p$ 为 $\exists x _ { 0 } \in M$ ， $\neg p ( x _ { 0 } )$ ．

（2）存在量词命题 $p : \exists x _ { \scriptscriptstyle 0 } \in M , p ( x _ { \scriptscriptstyle 0 } )$ 的否定 $\neg p$ 为 $\forall { x } { \in } M , \lnot p ( x )$

注：全称、存在量词命题的否定是高考常见考点之一

# 【常用逻辑用语常用结论】

1、从集合与集合之间的关系上看 设 $A = \left\{ \left. x \right| p ( x ) \right\} , B = \left\{ \left. x \right| q ( x ) \right\} .$

（1）若 $A \subseteq B$ ，则 $p$ 是 $q$ 的充分条件（ $p \Rightarrow q$ ）， $q$ 是 $p$ 的必要条件；若 ，则 $p$ 是$q$ 的充分不必要条件， $q$ 是 $p$ 的必要不充分条件，即 $p \Rightarrow q$ 且 $q \oplus p$ ；注：关于数集间的充分必要条件满足：“小 $\Rightarrow$ 大”

（2）若 $B \subseteq A$ ，则 $p$ 是 $q$ 的必要条件， $q$ 是 $p$ 的充分条件；（3）若 $A = B$ ，则 $p$ 与 $q$ 互为充要条件

# 名校模拟探源

# 一、单选题

$\exists x > 0 , x ^ { 2 } + x - 1 > 0$ ，1．（2024·河南·三模）命题 的否定是（ ）

$\forall x > 0 , x ^ { 2 } + x - 1 > 0$ ∀x >0,x²+x-1≤0 A． B． ≤0,x²+x-1>0 x ≤0,x² + x- 1≤0 C． D．

2．（2024·湖南长沙·三模）已知集合 $M = \left\{ \left. x \right| \left| x \right| \bullet 2 \right\} , N = \{ \left. x \right| \ln x < 1 \}$ ，则 $M \cap N =  \left( \begin{array} { l } { \right) } \end{array}$

A． B． C． D．

3．（2024·河北衡水·三模）已知集合 $A = \left\{ 1 , 2 , 3 , 4 , 5 \right\} , B = \left\{ x | - 1 \leq \mathrm { l g } \left( x - 1 \right) \leq \frac { 1 } { 2 } \right\}$ ，则  
AnB=（ ）

A． B． C． D． $\left\{ x \vert \frac { 1 1 } { 1 0 } \leq x \leq 3 \right\}$

4．（2024·陕西·三模）已知集合 $A = \left\{ x | - 1 \leq x \leq 2 \right\} , B = \left\{ x | - \ x ^ { 2 } + 3 x > 0 \right\}$ ，则 $A \cup B = \left( \begin{array} { l l } { \mathbf { \Sigma } } & { \mathbf { \Sigma } } \end{array} \right)$

A． B． C． D．

5．（2024·安徽·三模）已知集合 $A = \left\{ x | - 5 \leq x \leq 1 \right\} , B = \left\{ x | x > - 2 \right\}$ ， 则图中所示的阴影部分的集合可以表示为（ ）

![](images/6536d3e1664f2e761dada89ca9200783b5ad2156943f63234b26a2753a07af7c.jpg)

A． C． $\begin{array} { r l } { \mathrm { B . } } & { \left\{ x \middle | - 2 < x \leq 1 \right\} } \\ { \mathrm { D . } } & { } \end{array}$

6．（2024·湖南长沙·三模）已知直线 $l : k x - y + { \sqrt { 2 } } k = 0$ 圆 ， $O : x ^ { 2 } + y ^ { 2 } = 1$ ，则 $_ { \textrm { \scriptsize 6 6 } } k < 1 ,$ 是“直线 上存在点 $P$ ，使点 $P$ 在圆 $O$ 内”的（ ）

A．充分不必要条件 B．必要不充分条件C．充要条件 D．既不充分也不必要条件

7．（2024·湖北荆州·三模）已知集 合 ， ，其中 是实数集，集合 ， 则 $B \cap C = { \mathrm { ~ ( ~ ) ~ } }$ ）

A． B． C． D．

8．（2024·北京·三模）已知集合 $A = \left\{ x \ln x < 1 \right\}$ ，若 $a \not \in A$ ，则 $a$ 可能是（ ）

A． B．1 C．2 D．3

9．（2024·河北衡水·三模）已知函数 $f ( x ) = { \big ( } 2 ^ { x } + m \cdot 2 ^ { - x } { \big ) } \sin x$ ，则“ $m ^ { 2 } = 1 ^ { , }$ 是“函数 $f ( x )$ 是奇函数”的（ ）

A．充分不必要条件 B．必要不充分条件 C．充要条件 D．既不充分也不必要条件

10．（2024·内蒙古·三模）设 $\alpha$ ， $\beta$ 是两个不同的平面， $m$ ， $l$ 是两条不同的直线，且αnβ =l则 "m//l, 是“ 且 m/lα， ”的（ ）

A．充分不必要条件 B．充分必要条件C．必要不充分条件 D．既不充分也不必要条件

11．（2024·北京·三模）已知 $A = \left\{ x \left| \log _ { 2 } \left( x - 1 \right) \le 1 \right. \right\}$ ， $B = \left\{ x { \big \| } x - 3 { \big | } > 2 \right\}$ ，则 $A \cap B = { \textrm { ( ) } }$

A．空集 B $\mathrm { ~ . ~ } ^ { \left\{ \chi \right. x \leq 3 }  . \mu \Sigma ^ { \left\{ \gamma \right\} }$ C $\left\{ x \middle \vert x \leq 3 _ { \tt \frac { H } { \cdot } \tt \frac { \partial } { \partial } x > 5 \tt \frac { \partial } { \partial } x } x \neq 1 \right\}$ D．以上都不对

12．（2024·四川·三模）已知集合 $A = \{ 0 , 3 , 5 \}$ ， $B = \left( x { \big | } x ( x - 2 ) = 0 \right)$ ，则 $A \cap B = { \textrm { ( ) } }$

A． ① B． C． D．

13．（2024·重庆·三模）已知集合 $A = \left\{ x \in \mathbf { R } \left| x ^ { 2 } - x - 2 < 0 \right. \right\} , B = \left\{ y \mid y = 2 ^ { x } , x \in A \right\}$ ，则  
AnB=（ ）

A． B． $\left( { \frac { 1 } { 4 } } , 1 \right)$ C． $\left( { \frac { 1 } { 2 } } , 1 \right)$ D． $\left( { \frac { 1 } { 2 } } , 2 \right)$

14．（2024·北京·三模）“ 为锐角三角形”是“ $\sin A > \cos B$ ， $\sin B > \cos C$ ，sin $C > \cos A$ ”的（ ）

A．充分不必要条件 B．必要不充分条件C．充分必要条件 D．既不充分也不必要条件

15．（2024·上海·三模）设 ， 集合 $A = \left\{ 1 , a , b \right\}$ ， 集合$B = \left\{ t { \left| t = x y + \frac { y } { x } , x , y \in A , x \neq y \right. } \right\}$ ，对于集合 $B$ 有下列两个结论： $\textcircled{1}$ 存在 $a$ 和 $^ { b }$ ，使得集合B中恰有 5 个元素； $\textcircled{2}$ 存在 $a$ 和 $^ { b }$ ，使得集合 $B$ 中恰有 4 个元素．则下列判断正确的是（ ）

A． $\textcircled{1} \textcircled{2}$ 都正确 B． $\textcircled{1} \textcircled{2}$ 都错误 C． $\textcircled{1}$ 错误， $\textcircled{2}$ 正确 D． $\textcircled{1}$ 正确， $\textcircled{2}$ 错误

# 二、多选题

16．（2024·江西南昌·三模）下列结论正确的是（ ）

A．若 $\Big \{ x \Big | x + 3 > 0 \Big \} \cap \Big \{ x \Big | x - a < 0 \Big \} = \emptyset$ ，则 $a$ 的取值范围是 $a < - 3$ B．若 $\Big \{ x \Big | x + 3 > 0 \Big \} \cap \Big \{ x \Big | x - a < 0 \Big \} = \emptyset$ ，则 $a$ 的取值范围是 $a \le - 3$ C．若 $\scriptstyle { \left\{ \left. { \boldsymbol { x } } \right| { \boldsymbol { x } } + 3 > 0 \right\} } \cup \left\{ { \boldsymbol { x } } { \big | } { \boldsymbol { x } } - { \boldsymbol { a } } < 0 \right\} = \mathbf { R }$ ，则 $a$ 的取值范围是 $a \ge - 3$ D．若 ， 则 $a$ 的取值范围是 $a > - 3$

17．（2024·辽宁·三模）已知 $\operatorname* { m a x } \left\{ x _ { 1 } , x _ { 2 } , \cdots , x _ { n } \right\}$ 表 示 这 个数中最大的数．能说明命题“ ， $d \in \mathrm { R }$ ， $\operatorname* { m a x } \left\{ a , b \right\} + \operatorname* { m a x } \left\{ c , d \right\} \geq \operatorname* { m a x } \left\{ a , b , c , d \right\}$ ”是假命题的对应的一组整数 a，b，c，d 值的选项有（ ）

A．1，2，3，4 B． ， ，7，5C．8， ， ， D．5，3，0，

18．（2024·重庆·三模）命题“存在 $\chi > 0$ ，使得 $m x ^ { 2 } + 2 x - 1 > 0$ ”为真命题的一个充分不必要条件是（ ）

A． $m > - 2$ B． $m > - 1$ C． $m > 0$ D． $m > 1$

19．（2024·黑龙江齐齐哈尔·三模）已知 $a , b > 0$ ，则使得“ $a > b$ ”成立的一个充分条件可以是（ ）

1<1 A． B. |a- 2|>|b- 2| C． D $\ln \left( a ^ { 2 } + 1 \right) > \ln \left( b ^ { 2 } + 1 \right)$

20．（2024·安徽安庆·三模）已知集合 $A = \left\{ x \in \mathbf { Z } { \big | } x ^ { 2 } - 2 x - 8 < 0 \right\}$ ，集合$B = \left\{ x \vert 9 ^ { x } > 3 ^ { m } , m \in \mathbf { R } , x \in \mathbf { R } \right\}$ ，若 $A \cap B$ 有且仅有3个不同元素，则实数 $m$ 的值可以为（ ）

A．0 B．1 C．2 D．3

# 三、填空题

21．（2024·湖南长沙·三模）已知集合 $A = \left\{ 1 , 2 , 4 \right\} , B = \left\{ a , a ^ { 2 } \right\}$ ，若 $A \cup B = A$ ，则 $a = \underline { { \underline { { \mathbf { \Pi } } } } } _ { \mathbf { \Pi } }$

22．（2024·上海·三模）已知集合 $A = \{ 0 , 1 , 2 \}$ ， $B = \left\{ \left. x \right| x ^ { 3 } - 3 x \le 1 \right\}$ ，则 $A \cap B = { _ { - } }$

23．（2024·湖南衡阳·三模）已知集合 ，集合 $B = \left\{ x \in \mathbf { N } | x ^ { 2 } - x - 2 \leq 0 \right\}$ ，若   
AnB a= ，则

24．（2024·湖南邵阳·三模） $A = \left\{ x \in \mathbf { N } | \log _ { 2 } \left( x - 3 \right) \leq 2 \right\} , B = \left\{ x { \bigg | } { \frac { x - 3 } { x - 7 } } \leq 0 \right\}$ ，则 $A \cap B = _ { - }$

25．（2024·安徽·三模）已知集合 $A = \{ \lambda , 2 , - 1 \} , B = \left\{ y \mid y = x ^ { 2 } , x \in A \right\}$ ，若 $A \cup B$ 的所有元素之和为12，则实数 $\lambda = \_$

26．（2024·山东聊城·三模）已知集合 $A = \left\{ 1 , 5 , a ^ { 2 } \right\} , B = \left\{ 1 , 3 + 2 a \right\}$ ，且 $A \cup B = A$ ，则实数 的值为

27．（2024·重庆·三模）已知集合 $A = \left\{ x { \big | } x ^ { 2 } - 5 x + 6 = 0 \right\}$ ， $B = \left\{ { \boldsymbol { x } } { \big | } - 1 < { \boldsymbol { x } } < 5 , { \boldsymbol { x } } { \in } \mathbf { N } \right\}$ ，则满足B 的集合 的个数为 .

28．（2024·天津·三模）己知全集 ，集合 ， 集合$B = \{ x \in { \bf Z } | | x | < 5 \} \quad _ { , \mathrm { ~ } \mathbb { J } | | { \bf j } } ( \sp \bullet \oplus { \bf A } ) \cap B = \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \ u \quad \ u \ u \ u \ u \ u \ u \ u \ u \ u \ u \ u \ u \ u \ u \ u \ u \ u \ u \ u \ u \ u \ u \ u $

29．（2024·山东泰安·三模）已知集合 $A = \{ x { \Biggr | } { \frac { x + 2 } { x - 2 } } \leq 0 \} , B = \{ x { \big | } \log _ { 2 } x \geq a { \big \} }$ ，若 $B \subseteq \left( \spadesuit A \right)$ ， 则 $a$ 的取值范围是

30．（2024·宁夏银川·三模）已知命题 $p$ ：关于 $x$ 的方程 $x ^ { 2 } - a x + 4 = 0$ 有实根；命题 $q$ ：关 于 $x$ 的函数 $y = \log _ { 3 } \left( 2 x ^ { 2 } + a x + 3 \right) _ { \textstyle \frac { \ldots } { \textstyle \bigoplus _ { \textstyle \pm } } } \left[ 3 , + \infty \right)$ 上单调递增，若“ $p$ 或 $q$ ”是真命题，“ $p$ 且 $q$ ”是 假命题，则实数 $a$ 的取值范围是 .

成套的课件成套的教案成套的试题成套的微专题尽在高中数学同步资源大全 QQ 群 552511468 也可联系微信 fjshuxue 加入百度网盘群 4000G 一线老师必备资料一键转存自动更新永不过期