---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 15
source_file: 1_7.1  数列的概念及表示讲解册.md
title: 1_7.1  数列的概念及表示讲解册
type: problem_type
---

# 考情 清单

<html><body><table><tr><td>考点</td><td>真题示例</td><td>考向</td><td>4年考频</td><td>核心素养</td></tr><tr><td rowspan="11">等差数列及其前n项和</td><td>2021新高考I,17(1)</td><td rowspan="2">等差数列的判定</td><td rowspan="8">10考 数学运算</td><td rowspan="8"></td></tr><tr><td>2023新课标Ⅰ,7</td></tr><tr><td>2023 新课标I,20(1)</td></tr><tr><td>2023 新课标Ⅱ,18（1)</td></tr><tr><td>等差数列的通项公式 2022 新高考Ⅰ,17（1)</td></tr><tr><td>2021新高考Ⅱ,17(1)</td></tr><tr><td>2023新课标I,20（2)</td></tr><tr><td>2021新高考Ⅱ,17（2) 等差数列的前n项和</td></tr><tr><td>2020 新高考Ⅰ,14</td><td rowspan="3">等比数列的通项公式</td><td rowspan="3"></td></tr><tr><td>2020新高考Ⅱ,15</td></tr><tr><td>2022 新高考Ⅱ,17(1)</td></tr><tr><td rowspan="5">等比数列及其前n项和</td><td>2020 新高考Ⅰ,18（1)</td><td rowspan="3">5考 错位相减法求和</td><td rowspan="5">数学运算</td></tr><tr><td>2020 新高考Ⅱ,18(1)</td></tr><tr><td>2020 新高考Ⅰ,18（2)</td></tr><tr><td>2023 新课标Ⅱ,8</td><td rowspan="2">等比数列的前n项和</td></tr><tr><td>2021新高考Ⅰ,16</td></tr><tr><td rowspan="3">数列求和</td><td>2021新高考I,17(2)</td><td rowspan="2">分组并项法求和</td><td rowspan="2">数学运算 3考 逻辑推理</td></tr><tr><td>2020新高考Ⅱ,18(2)</td></tr><tr><td>2022 新高考Ⅱ,17(2)</td><td>数列与函数综合</td><td rowspan="4">数学运算 5考 逻辑推理</td></tr><tr><td rowspan="4">数列综合</td><td>2023新课标Ⅱ,18(2)</td></tr><tr><td>数列与不等式综合 2022 新高考Ⅰ,17（2)</td></tr><tr><td>2021新高考Ⅰ,12</td></tr><tr><td>2022 新高考Ⅱ,3</td><td>数列创新 数列的实际应用</td></tr></table></body></html>

# 命题形式

本专题内容是高考必考内容之一,试题难度适中,考查形式多样,分值为5\~17分.小题重点考查等差、等比数列的概念、性质、通项公式和前 $n$ 项和公式.解答题常考查数列求通项公式及求和的方法,重点体现错位相减法、裂项相消法及分组并项求和法的应用.偶有出现以数学文化为背景的数列问题和数列与其他知识相结合的创新题型.复习时应注重基础,提升能力,保证计算的准确性,重视分类讨论、逻辑推理、转化与化归的应用.

# 7.1 数列的概念及表示

# 考点清单

# 考点 数列的概念及表示

1.数列的概念：一般地,我们把按照确定的顺序排列的一列数称为数列,数列中的每一个数叫做这个数列的项.

2.数列的通项公式：如果数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 的第 $n$ 项$a _ { n }$ 与序号 $n$ 之间的对应关系可以用一个式子$a _ { n } = f ( n )$ 来表示，那么这个式子叫做这个数列的通项公式.

3.数列的递推公式：如果一个数列的相邻两项或多项之间的关系可以用一个式子来表示，那么这个式子叫做数列 $\left\{ a _ { n } \right\}$ 的递推公式.

4.数列的前 $n$ 项和及其与通项的关系：我们把数列 $\left\{ a _ { n } \right\}$ 从第1项起到第 $n$ 项止的各项之和,称为数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和,记作 $S _ { n }$ ,即 $S _ { n } = a _ { 1 } +$ $a _ { 2 } + \cdots + a _ { n }$ $a _ { n }$ 与 $S _ { n }$ 的关系为 $a _ { n }$ $= \left\{ \begin{array} { l l } { S _ { 1 } \left( n = 1 \right) , } \\ { \quad } \\ { S _ { n } { - } S _ { n - 1 } \left( n { \geqslant } 2 \right) . } \end{array} \right.$

5.数列与函数的关系：数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 是从正整数集$\mathbf { N } ^ { * }$ （或它的有限子集 $\{ 1 , 2 , 3 , \cdots , n \}$ )到实数集 $\mathbf { R }$ 的函数,其自变量是序号 $n$ ,对应的函数值是数列的第 $n$ 项 $a _ { n }$ ,记为 $a _ { n } = f ( n )$ ,即当自变量从1开始，按照从小到大的顺序依次取值时,对应的一列函数值就是数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ .另一方面,对于函数 $\scriptstyle { y = f ( x ) }$ ，如果 $f ( n ) \left( n \in \mathbf { N } ^ { * } \right)$ 有意义，那么 $f ( 1 ) , f ( 2 ) , \cdots , f ( n )$ ,…构成了一个数列 $\{ f ( n ) \}$

6.数列的性质：由于数列可以看作一个关于 $n$ （ $\mathbf { \Phi } _ { n } \in \mathbf { N } ^ { \ast } \mathbf { \Phi } )$ 的函数,因此它具备函数的某些性质.(1)单调性：若 $a _ { n + 1 } > a _ { n }$ ,则 $\left\{ a _ { n } \right\}$ 为递增数列;若$a _ { n + 1 } < a _ { n }$ ,则 $\left\{ a _ { n } \right\}$ 为递减数列.(2)周期性：若 $a _ { n + k } = a _ { n } ( k \in \mathbf { N } ^ { * } )$ ，则 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 为周期数列， $k$ 为 $\left\{ a _ { n } \right\}$ 的一个周期.(3）最值：在数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 中,若 $a _ { n }$ 最大,则$\left\{ { a _ { n } \geqslant a _ { n - 1 } } , \right.$ 若 $a _ { n }$ 最小，则 $\left\{ \begin{array} { l l } { a _ { n } \leqslant a _ { n - 1 } , } \\ { \qquad } \\ { a _ { n } \leqslant a _ { n + 1 } . } \end{array} \right.$

# 即练即清

判断正误（对的打“ $\vee ^ { , , }$ ,错的打“×”)

(1)如果数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 的前 $n$ 项和为 $S _ { n }$ ,则对任意$n \in \mathbf { N } ^ { * }$ ,都有 $a _ { n + 1 } = S _ { n + 1 } - S _ { n }$ ）

(2)已知数列 $\left\{ \begin{array} { l } { a _ { n } \rule { 0 ex } { 5 ex } } \end{array} \right\}$ 满足 $a _ { 1 } + 2 a _ { 2 } + 3 a _ { 3 } + \cdots + n a _ { n } =$ 2”,则an $a _ { n } = { \frac { 2 ^ { n - 1 } } { n } } .$ （）

(3)在数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 中， $a _ { 1 } = 1 , a _ { n } = 1 + \frac { ( - 1 ) ^ { n } } { a _ { n - 1 } } ( n \geqslant$ 2),则 $a _ { 5 } = { \frac { 5 } { 3 } } .$

(4)若 $S _ { n }$ 为数列 $\left\{ \begin{array} { l } { a _ { n } \rule { 0 ex } { 5 ex } } \end{array} \right\}$ 的前 $n$ 项和,且 $S _ { n } = { \frac { n } { n + 1 } }$ 则 $\Big \vert \frac { 1 } { a _ { 5 } } { = } 2 0 .$ （ ）

(5)在数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 中， $a _ { \scriptscriptstyle n } = \left( n + 1 \right) \left( \frac { 7 } { 8 } \right) ^ { \scriptscriptstyle n }$ ,则数列$\left\{ a _ { n } \right\}$ 中的最大项是第6项. （）

即练即清答案： $( 1 ) { \mathsf V } \quad ( 2 ) \times \quad ( 3 ) \times \quad ( 4 ) \times \quad ( 5 ) \times \quad $

# 题型清单

# 题型<1 利用 $S _ { n }$ 和 $a _ { n }$ 的关系求通项公式已知 $S _ { n }$ 求 $a _ { n }$ 的步骤

(1)先利用 $a _ { 1 } = S _ { 1 }$ 求出 $a _ { 1 }$ (2)用 $n - 1$ 替换 $S _ { n }$ 中的 $n$ 得到一个新的关系，利用 $a _ { n } = S _ { n } - S _ { n - 1 } ( n \geqslant 2 )$ 便可求出当 $n \geqslant 2$ 时$a _ { n }$ 的表达式.

(3)对 $n = 1$ 时的结果进行检验,看是否符合 $n \geqslant$ 2时 $a _ { n }$ 的表达式,若符合，则数列的通项公式合写;若不符合，则应该分 $n = 1$ 与 $n \geqslant 2$ 两段来写.

例1（2018课标I理,14,5分）记 $S _ { n }$ 为数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和若 $S _ { n } = 2 a _ { n } + 1$ ，则 $S _ { 6 } = _ { \circ }$

V解析解法一 由 $S _ { n } = 2 a _ { n } + 1$ ，得 $a _ { 1 } = 2 a _ { 1 } + 1$ ，所以 $a _ { 1 } = - 1$ ，当 $n \geqslant 2$ 时， $a _ { n } = S _ { \scriptscriptstyle n } - S _ { \scriptscriptstyle n - 1 } = 2 a _ { n } + 1 -$ $\left( 2 a _ { n - 1 } + 1 \right)$ ，得 $a _ { n } = 2 a _ { n - 1 } , \therefore \left\{ \left. a _ { n } \right. \right\}$ 是首项为-1,公比为2的等比数列.  
$\therefore S _ { 6 } = \frac { a _ { 1 } ( 1 - q ^ { 6 } ) } { 1 - q } = \frac { - ( 1 - 2 ^ { 6 } ) } { 1 - 2 } = - 6 3 .$

解法二 由 $S _ { n } = 2 a _ { n } + 1$ ，得 $S _ { 1 } = 2 S _ { 1 } + 1$ ，所以 $S _ { 1 } =$ $^ { - 1 }$ ,当 $n \geqslant 2$ 时，由 $S _ { n } = 2 a _ { n } + 1$ 得 $S _ { n } = 2 ( S _ { n } - \frac { } { }$ $S _ { n - 1 } ) + 1$ ，即 $S _ { n } = 2 S _ { n - 1 } - 1 \ : , \therefore \ : S _ { n } - 1 = 2 ( \ : S _ { n - 1 } - 1 )$ ，又$S _ { 1 } - 1 = - 2 , \dot { \dots } \ \left\{ \ S _ { n } - 1 \ \right\}$ 是首项为-2,公比为2的等比数列，所以 $S _ { n } - 1 = - 2 \times 2 ^ { n - 1 } = - 2 ^ { n }$ ，所以 $S _ { n } =$ $1 - 2 ^ { n } , \therefore S _ { 6 } = 1 - 2 ^ { 6 } = - 6 3 .$

答案-63

技巧点拨 $S _ { n }$ 与 $a _ { n }$ 关系问题的求解思路根据所求结果的不同要求,将问题向不同的两个方向转化.

(1)利用 $a _ { n } = S _ { \scriptscriptstyle n } - S _ { \scriptscriptstyle n - 1 } \left( n \ge 2 \right)$ 转化为只含 $S _ { n } , S _ { n - 1 }$ 的关系式,再求解；

(2)利用 $S _ { n } - S _ { n - 1 } = a _ { n } ( n \geqslant 2 )$ 转化为只含 $a _ { n } , a _ { n - 1 }$ 的关系式,再求解.

# 即练即清

1.已知数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ ， $a _ { 1 } = 2 , S _ { n }$ 为数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 的前

$n$ 项和,且对任意 $n \geqslant 2$ ，都有 $\frac { 2 a _ { n } } { a _ { n } S _ { n } - S _ { n } ^ { 2 } } = 1$ 则$\left\{ a _ { n } \right\}$ 的通项公式为

答案 $a _ { _ n } = \left\{ \begin{array} { c } { 2 , n = 1 } \\ { } \\ { 2 } \\ { n ( n - 1 ) } \end{array} \right. , n \geqslant 2$

# 题型2 利用递推关系求通项公式

1.累加法：对于形如 $a _ { n + 1 } - a _ { n } = f ( n )$ 的数列的递推关系式，若 $f ( 1 ) + f ( 2 ) + \cdots + f ( n )$ 是可求的,可用多项式相加法求得 $a _ { n }$ ,称这种方法为累加法.

2.累乘法：对于形如 $\scriptstyle { \frac { a _ { n + 1 } } { a _ { n } } } = f ( n )$ 的数列的递推关系式,若 $f ( 1 ) \cdot f ( 2 ) \cdot \cdots \cdot f ( n )$ 是可求的,则可用多项式相乘法求得 $a _ { n }$ ,称这种方法为累乘法.

3.构造法：形如 $a _ { n + 1 } = p a _ { n } + q$ （其中 $p , q$ 均为常数， $p q ( p { - } 1 ) \neq 0 \rangle$ 的递推关系式,把原递推关系式转化为 $\scriptstyle a _ { n + 1 } - t = p \left( { \ a _ { n } - t } \right)$ ,其中 $t = { \frac { q } { 1 - p } }$ ，然后构造 1 ${ \frac { a _ { n + 1 } - t } { a _ { n } - t } } { = } p$ ,即 $\left\{ a _ { n } - t \right\}$ 是以 $a _ { 1 } - t$ 为首项， $p$ 为公比的等比数列.

4.辅助数列法：形如 $a _ { n + 1 } = p a _ { n } + q ^ { n }$ （其中 $p , q$ 均为常数, $p q ( p { - } 1 ) \neq 0 \rangle$ 的递推关系式，要先在递推关系式两边同除以 $q ^ { n + 1 }$ ，得 $\frac { a _ { n + 1 } } { q ^ { n + 1 } } = \frac { p } { q } \cdot \frac { a _ { n } } { q ^ { n } } +$ $\frac { 1 } { q }$ ，引入辅助数列 $\{ b _ { n } \} ($ 其中 $b _ { n } = { \frac { a _ { n } } { q ^ { n } } } \rfloor$ ，得 $b _ { n + 1 } =$ ${ \frac { p } { q } } \cdot b _ { n } + { \frac { 1 } { q } }$ ,再用构造法解决.

5.取倒数法：对an = $a _ { n } = \frac { m a _ { n - 1 } } { k ( a _ { n - 1 } + b ) }$ （其中 $n \geqslant 2$ ，$m k b \neq 0 ,$ 取倒数，得到 $\frac { 1 } { a _ { n } } = \frac { k } { m } \cdot \left( 1 + \frac { b } { a _ { n - 1 } } \right) \Longleftrightarrow \frac { 1 } { a _ { n } } =$ ${ \frac { k b } { m } } \cdot { \frac { 1 } { a _ { n - 1 } } } + { \frac { k } { m } } .$ 区 $b _ { n } = { \frac { 1 } { a _ { n } } }$ 则 $\left\{ \begin{array} { l }  b _ { n } \right\} \end{array}$ 可归为 $b _ { n + 1 } =$ $p b _ { n } + q ( p \neq 0 , 1 , q \neq 0 )$ 型.

6.取对数法：对 $a _ { n } = p a _ { n - 1 } ^ { r }$ （其中 $a _ { n } > 0 , p > 0 , n \geqslant$ 2)两边同取常用对数,得 $\mathrm { l g } \ a _ { n } = r \mathrm { l g } \ a _ { n - 1 } + \mathrm { l g } \ p$ ，令 $b _ { n } = \lg a _ { n }$ ，则 $\left\{ \begin{array} { l }  \displaystyle b _ { n } \right\} \end{array}$ 可归为 $b _ { n + 1 } = p b _ { n } + q ( p \neq 0 , 1$ （204 $q \not = 0 \not .$ 型.

例2(2023陕西师大附中期末,7)在数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 中,a= $a _ { 1 } = \frac { 1 } { 2 }$ 且紅 $\left( n + 2 \right) a _ { n + 1 } = n a _ { n }$ ,则它的前30项和$S _ { 3 0 } =$ （）

$\mathrm { A } . \frac { 3 0 } { 3 1 }$ c.28 D.19

$\blacktriangleright$ 解析 $\because \left( n + 2 \right) a _ { n + 1 } = n a _ { n } \mathrm { , \therefore } \frac { a _ { n + 1 } } { a _ { n } } = \frac { n } { n + 2 } \mathrm { , \therefore } a _ { n } =$ $\begin{array} { l } { { a _ { 1 } ~ \cdot ~ { \cfrac { a _ { 2 } } { a _ { 1 } } } ~ \cdot ~ { \cfrac { a _ { 3 } } { a _ { 2 } } } ~ \cdot ~ \dots ~ \cdot ~ { \cfrac { a _ { n } } { a _ { n - 1 } } } = { \cfrac { 1 } { 2 } } \times { \cfrac { 1 } { 3 } } \times { \cfrac { 2 } { 4 } } \times \dots \times { \cfrac { n - 1 } { n + 1 } } = } } \\ { { \cfrac { 1 } { n \left( n + 1 \right) } } = { \cfrac { 1 } { n } } { \cfrac { 1 } { n + 1 } } , n \geqslant 2 , \therefore { a _ { n } } = { \cfrac { 1 } { n } } - { \cfrac { 1 } { n + 1 } } , n \geqslant 2 , } \end{array}$ 当n=1时,a= $a _ { 1 } = \frac { 1 } { 2 }$ 适合上式，  
则 $a _ { n } = { \frac { 1 } { n } } - { \frac { 1 } { n + 1 } } , n \in \mathbf { N } ^ { * }$   
$\nearrow _ { 3 0 } = 1 - \frac { 1 } { 2 } \biggl . + \frac { 1 } { 2 } - \frac { 1 } { 3 } \biggl . + \dots + \frac { 1 } { 3 0 } - \frac { 1 } { 3 1 } = \frac { 3 0 } { 3 1 } .$ （204  
因此， 故选A.

$\blacktriangleright$ 答案A

例3(2024届江苏徐州铜北中学月考,7)已知数列 $\left\{ a _ { n } \right\}$ 满足 $a _ { 1 } = 2 , a _ { n + 1 } = a _ { n } ^ { 4 }$ ,则 $a _ { 6 }$ 的值为（ ）

A.220 B.224 C.21 024 D.24 096

解析由 $a _ { n + 1 } = a _ { n } ^ { 4 } , a _ { 1 } = 2$ ，易知 $a _ { n } > 0$ ，故两边取以e为底的对数得ln an+1=4lnan,故 $\{ \ln a _ { n } \}$ 是首项为 $\ln a _ { 1 } = \ln 2$ ,公比为4的等比数列，所以1 ${ \mathrm { ~ n ~ } } a _ { \scriptscriptstyle n } = 4 ^ { n - 1 } \cdot \ln 2$ ，则 $\ln a _ { 6 } = 4 ^ { 5 } \ln 2 = \ln 2 ^ { 4 ^ { 5 } } =$ $\ln 2 ^ { 2 ^ { 1 0 } } = \ln 2 ^ { 1 0 2 4 }$ ,故 $a _ { 6 } = 2 ^ { 1 0 2 4 }$ .故选C.

答案C

# 即练即清

2.(2024 届安徽黄山歙县新安中学期中,6)已知数列 $\left\{ a _ { n } \right\}$ 满足 $a _ { 1 } = 3 4$ ，且 $a _ { n + 1 } - a _ { n } = 2 n + 1$ 当 $\frac { a _ { n } } { n }$ 取$n$ 最小值时 $n$ 为 （ ）

A.4 B.5 C.6 D.7

# 答案

3.已知数列{an}满足a=1,an+1 $a _ { 1 } = 1 , a _ { n + 1 } = \frac { a _ { n } } { 2 a _ { n } + 1 }$ ，则数列 $\left\{ { { a } _ { n } } { { a } _ { n + 1 } } \right\}$ 的前 $n$ 项和 $T _ { n } =$

$$
\mathrm { A } . { \frac { n } { 2 n - 1 } } \quad \mathrm { B } . { \frac { n } { 2 n + 1 } } \quad \quad \mathrm { C } . { \frac { 2 n } { 2 n + 1 } } \quad \quad \mathrm { D } . { \frac { n } { 4 n + 2 } }
$$

答案】 B