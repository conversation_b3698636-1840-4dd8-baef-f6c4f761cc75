---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 困难
estimated_study_time: 10
source_file: 01_1.1  集合讲解册.md
title: 01_1.1  集合讲解册
type: problem_type
---

# 专题一 集合与常用逻辑用语

# 考情 清单

<html><body><table><tr><td>考点</td><td>真题示例</td><td>考向</td><td>4年考频</td><td>核心素养</td></tr><tr><td>集合及其关系</td><td>2023 新课标Ⅱ,2</td><td>已知集合之间关系求参</td><td>1考</td><td>逻辑推理</td></tr><tr><td rowspan="7">集合的基本运算</td><td>2023 新课标Ⅰ,1</td><td rowspan="7">集合的交集</td><td rowspan="7">8考</td><td rowspan="7">数学运算</td></tr><tr><td>2022 新高考Ⅰ,1</td></tr><tr><td>2022 新高考Ⅱ,1</td></tr><tr><td>2021新高考Ⅰ,1</td></tr><tr><td>2020新高考Ⅱ,1</td></tr><tr><td>2020新高考Ⅰ,1</td></tr><tr><td>2021新高考Ⅱ,2;2020新高考Ⅰ,5 集合的综合运算</td></tr><tr><td>充分条件与必要条件</td><td>2023 新课标Ⅰ,7</td><td>充分、必要条件的判断</td><td>1考 逻辑推理</td></tr></table></body></html>

# 命题形式

本专题中集合多与方程、不等式的求解结合考查,设问有交集、并集、补集、元素个数及元素与集合间的关系等;常用逻辑用语多与其他知识结合(不等式、函数的概念与性质、基本初等函数、三角函数、数列、平面向量、立体几何等)考查,需深刻理解知识内涵，设问通常是充分、必要条件的判断.本专题的考查以选择题的形式呈现.题目难度简单或中等.

# 1.1集合

# 考点清单

# 考点1 集合及其关系

# 1.集合的含义与表示

(1)集合中元素的特性：确定性、互异性、无序性.

(2)元素与集合的关系是属于（用符号“ $\in$ ”表示)或不属于(用符号“”表示）.

(3)常用数集及其符号表示：非负整数集（自然数集)N、正整数集 $\mathbf { N } ^ { * }$ （或 $\mathbf { N } _ { + }$ ）、整数集 $\mathbf { Z }$ 、有理数集Q、实数集R.

(4)集合的表示方法：列举法、描述法、图示法.

# 2.集合间的基本关系

<html><body><table><tr><td colspan="2">表示 关系</td><td>定义</td><td>记法</td></tr><tr><td rowspan="3">集合 间的 基本 关系</td><td>相等</td><td>集合A与集合B中的所有 元素都相同</td><td>A=B</td></tr><tr><td>子集</td><td>集合A中任意一个元素均 为集合B中的元素</td><td>ACB或B UA</td></tr><tr><td>真子集</td><td>集合A中任意一个元素均 为集合B中的元素,且B中 至少有一个元素A中没有</td><td>AB或B A</td></tr><tr><td colspan="2" rowspan="2">空集</td><td>空集是任何集合的子集</td><td>QnB</td></tr><tr><td>空集是任何非空集合的真 子集</td><td>QB(B≠ Q</td></tr></table></body></html>

# 5年高考3年模拟A版高考数学

# 知识拓展·

有限集的子集个数

设集合 $A$ 是有 $n$ 个元素的有限集,即card $( A ) =$ $n \big ( n \in \mathbf { N } ^ { * }$ ），则 $A$ 的子集个数是 $2 ^ { n }$ ;真子集个数是 $2 ^ { n }$ 一1;非空子集个数是 $2 ^ { n } - 1$ ;非空真子集个数是 $2 ^ { n } - 2$

# 知识拓展

1.德·摩根定律： $\complement _ { \upsilon } ( A \cup B ) = \ ( \complement _ { \upsilon } A ) \cap ( \complement _ { \upsilon } B ) ; \complement _ { \upsilon } ( A \cap$   
$B ) = ( \complement _ { \mathit { v } } A ) \cup ( \complement _ { \mathit { v } } B ) .$   
2.一般地,对任意两个有限集合 $A , B$ ,有 $\operatorname { c a r d } ( A \cup B ) =$ $\operatorname { c a r d } ( A ) + \operatorname { c a r d } ( B ) - \operatorname { c a r d } ( A \cap B ) .$

# 考点2 集合的基本运算

已知全集 $U$ ,集合 $A , B$

<html><body><table><tr><td></td><td>并集</td><td>交集</td><td>补集</td></tr><tr><td>符号 表示</td><td>AUB</td><td>ANB</td><td>A的补集为uA</td></tr><tr><td>图形 表示</td><td>A B AUB</td><td>A B</td><td>U A CUA</td></tr><tr><td>意义</td><td>{xlx∈A或 x∈B}</td><td>{xlx∈A,且 x∈B}</td><td>{xlxεU,且 xA}</td></tr><tr><td>性质</td><td>AUQ=A; AUA=A; AUB=BUA; AUB=A↔B 门A</td><td>AnΩ=α; AnA=A; AnB=BnA; AnB=AA CB</td><td>AU(C,A)= U; An(CyA)=@; CU(C,A)=A</td></tr></table></body></html>

# 即练即清

判断正误（对的打“ $\vee ^ { , , }$ ,错的打 $^ { 6 6 } \times \warrow ^ { \prime \prime } .$ ）

(1)若集合 $A = \{ x \mid x \geqslant 6 \}$ ，则 $\left\{ 8 \right\} \in { \cal A }$ （）  
(2)集合 $\{ y \vert y = 2 x , x \in \mathbf { R } \}$ 与集合 $\{ \ ( \ x , y ) \mid _ { y } =$ $2 x , x \in \mathbf { R } \}$ 表示同一集合. （ ）  
(3) $\emptyset$ 和 $\{ \emptyset \}$ 表示的意义相同. （）  
(4)已知全集 $U = \mathbf { N } ^ { * }$ ，集合 $A = \{ 2 , 3 , 4 , 5 \}$ ， $B =$ {4,5,6,7},则 $( \complement _ { U } A ) \cap B = \{ 6 , 7 \}$ （）

即练即清答案： $\mathbf { \left( 1 \right) \times \left( 2 \right) \times \left( 3 \right) \times \left( 4 \right) } $

# 题型清单

# 题型1

# 集合间基本关系的求解及应用

1.判断两集合关系一般有两种方法：一是结构分析法，即化简集合，从表达式结构出发，寻找集合间的关系；二是用列举法(或图形)表示各个集合，从元素(或图形)中寻找关系.

2.已知集合间的关系求参数的值或取值范围,关键是将集合间关系转化为元素间关系，再转化为参数满足的条件.常借助数轴、Venn 图分析.

例1(2021全国乙理,2,5分)已知集合 $S = \left\{ s \vert s = \right.$ $2 n + 1 , n \in \mathbf { Z } \}$ ， $T = \left\{ \ t \left| t = 4 n + 1 , n \in \mathbf { Z } \right. \right\}$ ，则 $S \cap T =$ （ ）

A.Q B.S C.T D.Z $\blacktriangleright$ 解析 解法一对 $n$ 进行分类,从表达式中寻

找集合间关系.

当 $n$ 是偶数时,设 $n = 2 k , k \in \mathbf { Z }$ ，则 $s = 2 n + 1 = 4 k +$ $1 , k \in \mathbf { Z }$ ;当 $n$ 是奇数时，设 $n = 2 k + 1 , k \in { \bf Z }$ ，则 $s =$ $2 n + 1 = 4 k + 3 , k \in \mathbf { Z }$ ，则 $T \subsetneq S$ ,则 $S \cap T { = } T$ ,故选C. 解法二（列举法）从元素中寻找集合间关系. 由已知得 $S = \{ \cdots , - 3 , - 1 , 1 , 3 , 5 , \cdots \}$ ， $T = \{ \cdots , - 3 , 1$ $5 , 9 , \cdots \}$ ,观察可知， $T \subsetneq S$ ，所以 $T \cap S = T$ ,故选C.

答案C

# 即练即清

1.(2023 陕西渭南一模,6)已知集合 $A = \left\{ x \mid x ^ { 2 } - x - \right.$ $1 2 \leqslant 0 \}$ ， $B = \left\{ x | 2 m - 1 < x < m + 1 \right\}$ ，且 $A \cap B = B$ ，则实数 $m$ 的取值范围为 （）

A.[-1,2) B.[-1,3]

$$
\begin{array} { r } { \mathrm { C . } \left[ - 2 , + \infty \right) \qquad \mathrm { D . } \left[ - 1 , + \infty \right) } \end{array}
$$

答案 D

# 题型2 集合运算问题的求解

# 1.集合的基本运算

（1)以“形”定“法”：看集合的表示方法，用列举法表示的集合，宜用Venn图求解;用描述法表示的数集，常借助数轴分析得结果.  
（2)先“简”后“算”：运算前先对集合进行化简，分清是数集还是点集，是函数定义域还是值域,是方程的解还是不等式的解集等.

# 2.已知集合的运算结果求参数值（或范围）

根据集合运算的结果，利用集合运算的定义和数轴建立关于参数的方程(组)或不等式(组)求解,注意对空集的讨论.

例2设集合 $A = \left\{ { x \mid x ^ { 2 } - 3 x - 4 \leqslant 0 } \right\}$ ， $B = \{ \boldsymbol { x } \mid$ $\log _ { 2 } x > 1 \nmid$ ， $U = \mathbf { R }$ ,则 $( \complement _ { U } A ) \cup B =$ （）

A. $\{ x \mid x > 4 \}$ B. $\{ x \mid x > 2$ 或 $x { < } { - } 1 \}$ C. $\{ x \vert x { > } 4$ 或 $x < - 1 \nmid \qquad \mathrm { D . } \left\{ x \mid x < - 1 \right\}$

解析∵ $A = \{ x \mid x ^ { 2 } - 3 x - 4 \leqslant 0 \} = \{ x \mid - 1 \leqslant x \leqslant$ 4， $B = \{ x | \log _ { 2 } x > 1 \} = \{ x | x > 2 \} , \therefore \ell _ { \scriptscriptstyle U } A = \{ x | x < - 1$ $\{  \underline { { \} } } _ { \mathrm { \bf ~ { \mathscr { X } } } } x { > } 4 \} , \therefore ( { \hat { \mathsf { \mathsf { L } } } } _ { \mathrm { \bf { \mathscr { U } } } } A ) \cup B = \{ x | x { > } 2$ 或 $x { < } { - } 1 \}$ ,故选B.

答案B

例3已知集合 $A = \left\{ x \mid ( x - 2 ) { \bf \cdot } ( x - 3 ) \geqslant 0 \right\}$ ，$B = \left\{ \left. x \right| x { > } a { - } 1 \right\}$ ,若 $A \cup B { = } \mathbf { R }$ ,则实数 $a$ 的取值范围是 （）

A.(3,+∞） $\begin{array} { l } { { \mathrm { B . } [ 2 , + \infty \ ) } } \\ { { \mathrm { D . } ( - \infty \ , 3 ] } } \end{array}$ C.（18,2]

解析易知集合 $A = \{ x \vert x \geqslant 3$ 或 $x \leqslant 2 \}$ ，由 $B =$ $\{ x \mid x > a - 1 \}$ $A \cup B { = } \mathbf { R }$ ,借助于数轴(注意端点值能否取到)，如图所示，得 $a - 1 \leqslant 2$ ，即 $a \leqslant 3$ ,所以实数 $a$ 的取值范围是 $\left( - \infty , 3 \right]$ .故选D.

-101a-1234

# 答案D

# 即练即清

2.（2017课标 $\mathbb { I }$ 理,2,5分)设集合 $A = \{ 1 , 2 , 4 \}$ ，$B = \left\{ x \vert x ^ { 2 } - 4 x + m = 0 \right\}$ .若 $A \cap B = \{ 1 \}$ ,则 $B =$ （ ）

A.{1,-3}B.{1,0}C.{1,3}D.{1,5}

答案