---
chapter: 第01章
complexity_score: 10.0
created_date: '2025-07-13'
difficulty: 中等
estimated_study_time: 55
source_file: 专题13 数列（原卷版）.md
title: 专题13 数列（原卷版）
type: concept
---

# 专题13数列

# 考情概览

<html><body><table><tr><td>命题解读</td><td>考向</td><td>考查统计</td></tr><tr><td rowspan="3">（1）数列自身内部问题的综合考杳 如数列的递推公式、等差、等比数列的 性质、通项公式及前n项和公式、数列 求和等； （2）构造新数列求通项、求和 如“归纳、累加、累乘，分组、错位相</td><td>等差、等比数列基本量的计算</td><td>2023·新高考I卷，20 2022·新高考Ⅱ卷，17</td></tr><tr><td>等比数列的证明、数列结合解析几何</td><td>2024·新高考Ⅱ卷，19</td></tr><tr><td>累乘法求通项公式、裂项相消法求和</td><td>2022·新高考I卷，17</td></tr><tr><td>减、倒序相加、裂项、并项求和&quot;等方 法的应用与创新； （3）综合性问题 如与不等式、函数等其他知识的交汇问 题，与数列有关的数学文化问题及与实 际生活相关的应用问题以及结构不良 问题。</td><td>含奇偶项的分组求和</td><td>2023·新高考Ⅱ卷，18</td></tr></table></body></html>

# 2024年真题研析

# 命题分析

2024年高考新高考I卷考查了数列的新定义问题，后续专题会介绍。Ⅱ卷考查了等差数列基本量的计算，体现在填空第一题中，难度较易。大题中考查了等比数列的证明，但是是结合双曲线考查的，难度较难。

数列问题特别突出对考生数学思维能力的考查，既通过归纳、类比、递推等方法的应用突出对考生数学探究、理性思维的培养，又通过通项公式、递推公式、前n项和公式等内容进行大量技能训练，培养考生逻辑恩维、运算求解能力。从近三年的高考题可以看出，数列部分主要以考查基础知识为主，同时锻炼考生的运算求解能力、逻辑思维能力等。重点考查考生对数列基础知识的掌握程度及灵活应用，同时也要重视对通性通法的培养，所以在备考中应把重点放在以下几个方面。

（1)对数列的概念及表示法的理解和应用；

（2）等差、等比数列的性质、通项公式、递推公式、前 $\mathbf { n }$ 项和公式中基本量的运算或者利用它们之间的关系式通过多角度观察所给条件的结构，深人剖析其特征，利用其规律进行恰当变形与转化求解数列的问题；

（3）会利用等差、等比数列的定义判断或证明数列问题；  
（4）通过转化与化归思想利用错位相减、裂项相消、分组求和等方法求数列的前n项和；  
（5）数列与不等式、解析几何、函数导数等知识的交汇问题；  
（6）关注数学课本中有关数列的阅读与思考探究与发现的学习材料，有意识地培养考生的阅读能力和符号使用能力，也包括网络资料中与数列有关的数学文化问题，与实际生活相关的数列的应用问题；  
（7）结构不良试题、举例问题等创新题型。  
预计2025年高考还是主要考查数列基本量的计算和数列与其他知识交汇的问题，例如数列和不等式等。

# 试题精讲

# 一、解答题

1．（2024新高考Ⅱ卷·19）已知双曲线 $C : x ^ { 2 } - y ^ { 2 } = m { \bigl ( } m > 0 { \bigr ) }$ ，点 $P _ { 1 } ( 5 , 4 )$ 在 $C$ 上， $k$ 为常数， $0 < k < 1$ ．按照如下方式依次构造点 $P _ { n } \left( n = 2 , 3 , \ldots \right)$ ，过 $P _ { n - 1 }$ 作斜率为 $k$ 的直线与 $C$ 的左支交于点 $Q _ { n - 1 }$ ，令 $P _ { n }$ 为 $Q _ { n - 1 }$ 关于 $y$ 轴的对称点，记 $P _ { n }$ 的坐标为 $\left( x _ { n } , y _ { n } \right)$ ：

(1)若 $k = \frac 1 2$ ， 求 $x _ { 2 } , y _ { 2 }$

(2)证明：数列 $\left\{ x _ { n } - y _ { n } \right\}$ 是公比为 $\frac { 1 + k } { 1 - k }$ 的等比数列；

【答案】 $( 1 ) x _ { 2 } = 3$ ， $y _ { 2 } = 0$ (2)证明见解析

【分析】（1）直接根据题目中的构造方式计算出 $P _ { 2 }$ 的坐标即可；

（2）根据等比数列的定义即可验证结论;

【详解】(1)

![](images/3cb96dabce2185098582bc9fa4cc4b9cad7bc33322da10fa4fa970592908067d.jpg)

由已知有 $m = 5 ^ { 2 } - 4 ^ { 2 } = 9$ ，故 $C$ 的方程为 $x ^ { 2 } - y ^ { 2 } = 9$   
当 $k = \frac 1 2$ 时，过 $P _ { 1 } ( 5 , 4 )$ 且斜率为 $\frac { 1 } { 2 }$ 的直线为 $y = \frac { x + 3 } { 2 }$ ， 与 $x ^ { 2 } - y ^ { 2 } = 9$ 联立得到 $x ^ { 2 } - ( \frac { x + 3 } { 2 } ) ^ { 2 } = 9$ ：解得 $x = - 3$ 或 $x = 5$ ，所以该直线与 $C$ 的不同于 $P _ { 1 }$ 的交点为 $\mathcal { Q } _ { 1 } \left( - 3 , 0 \right)$ ，该点显然在 $C$ 的左支上.故 $P _ { 2 } \left( 3 , 0 \right)$ ，从而 $x _ { 2 } = 3$ ， $y _ { 2 } = 0$ ：

(2）由于过 $P _ { n } \left( x _ { n } , y _ { n } \right)$ 且斜率为 $k$ 的直线为 $y = k \left( x - x _ { n } \right) + y _ { n }$ ，与 $x ^ { 2 } - y ^ { 2 } = 9$ 联立，得到方程$x ^ { 2 } - \left( k \left( x - x _ { n } \right) + y _ { n } \right) ^ { 2 } = 9 .$ 展开即得 $\left( 1 - k ^ { 2 } \right) x ^ { 2 } - 2 k \left( y _ { n } - k x _ { n } \right) x - \left( y _ { n } - k x _ { n } \right) ^ { 2 } - 9 = 0$ ，由于 $P _ { n } \left( x _ { n } , y _ { n } \right)$ 已经是直线 $y = k \left( x - x _ { n } \right) + y _ { n }$ 和$x ^ { 2 } - y ^ { 2 } = 9$ 的公共点，故方程必有一根 $x = x _ { n }$ ：从而根据韦达定理，另一根 $x = { \frac { 2 k { \bigl ( } y _ { n } - k x _ { n } { \bigr ) } } { 1 - k ^ { 2 } } } - x _ { n } = { \frac { 2 k y _ { n } - x _ { n } - k ^ { 2 } x _ { n } } { 1 - k ^ { 2 } } }$ 相应的$y = k { \bigl ( } x - x _ { n } { \bigr ) } + y _ { n } = { \frac { y _ { n } + k ^ { 2 } y _ { n } - 2 k x _ { n } } { 1 - k ^ { 2 } } } .$ 所以该直线与 $C$ 的不同于 $P _ { n }$ 的交点为 $Q _ { n } \Bigg ( \frac { 2 k y _ { n } - x _ { n } - k ^ { 2 } x _ { n } } { 1 - k ^ { 2 } } , \frac { y _ { n } + k ^ { 2 } y _ { n } - 2 k x _ { n } } { 1 - k ^ { 2 } } \Bigg )$ 而注意到 $Q _ { n }$ 的横坐标亦可通过韦达定理表示为 $\frac { - { \left( y _ { n } - k x _ { n } \right) } ^ { 2 } - 9 } { { \left( 1 - k ^ { 2 } \right) } x _ { n } }$ 故 $Q _ { n }$ 一定在 $C$ 的左支上.所以 $P _ { n + 1 } \Bigg ( \frac { x _ { n } + k ^ { 2 } x _ { n } - 2 k y _ { n } } { 1 - k ^ { 2 } } , \frac { y _ { n } + k ^ { 2 } y _ { n } - 2 k x _ { n } } { 1 - k ^ { 2 } } \Bigg )$ 这就得到 $x _ { n + 1 } = \frac { x _ { n } + k ^ { 2 } x _ { n } - 2 k y _ { n } } { 1 - k ^ { 2 } } , y _ { n + 1 } = \frac { y _ { n } + k ^ { 2 } y _ { n } - 2 k x _ { n } } { 1 - k ^ { 2 } } .$ 所以$= \frac { x _ { n } + k ^ { 2 } x _ { n } + 2 k x _ { n } } { 1 - k ^ { 2 } } - \frac { y _ { n } + k ^ { 2 } y _ { n } + 2 k y _ { n } } { 1 - k ^ { 2 } } = \frac { 1 + k ^ { 2 } + 2 k } { 1 - k ^ { 2 } } \big ( x _ { n } - y _ { n } \big ) = \frac { 1 + k } { 1 - k } \big ( x _ { n } - y _ { n } \big )$ 再由 $x _ { 1 } ^ { 2 } - y _ { 1 } ^ { 2 } = 9$ ，就知道 $x _ { 1 } - y _ { 1 } \neq 0$ ，所以数列 $\left\{ x _ { n } - y _ { n } \right\}$ 是公比为 $\frac { 1 + k } { 1 - k }$ 的等比数列.

# 近年真题精选

# 一、填空题

1．（2024新高考Ⅱ卷·12）记 $S _ { n }$ 为等差数列 $\{ a _ { n } \}$ 的前 $n$ 项和，若 $a _ { 3 } + a _ { 4 } = 7$ ， $3 a _ { 2 } + a _ { 5 } = 5$ ，则 $S _ { 1 0 } =$ （204

【答案】95

【分析】利用等差数列通项公式得到方程组，解出 $a _ { \scriptscriptstyle 1 } , d$ ，再利用等差数列的求和公式节即可得到答案.

【详解】因为数列 $a _ { n }$ 为等差数列，则由题意得 $\left\{ \begin{array} { l l } { a _ { 1 } + 2 d + a _ { 1 } + 3 d = 7 } \\ { 3 { \bigl ( } a _ { 1 } + d { \bigr ) } + a _ { 1 } + 4 d = 5 } \end{array} \right.$ ，解得 $\textstyle { \left\{ \begin{array} { l l } { a _ { 1 } = - 4 } \\ { d = 3 } \end{array} \right. } ,$ 则 $S _ { 1 0 } = 1 0 a _ { 1 } + { \frac { 1 0 \times 9 } { 2 } } d = 1 0 \times \left( - 4 \right) + 4 5 \times 3 = 9 5 .$   
故答案为：95.

# 二、解答题

1．（2022新高考I卷·17）记 $S _ { n }$ 为数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和，已知 $a _ { 1 } = 1 , \left\{ { \frac { S _ { n } } { a _ { n } } } \right\}$ 是公差为 $\frac 1 3$ 的等差数列.

(1)求 $\left\{ a _ { n } \right\}$ 的通项公式；

(2)证明： $\frac { 1 } { a _ { 1 } } + \frac { 1 } { a _ { 2 } } + \cdots + \frac { 1 } { a _ { n } } < 2$ 【答案】(1)an = $a _ { n } = { \frac { n ( n + 1 ) } { 2 } }$

(2)见解析

【分析】（1）利用等差数列的通项公式求得 ${ \frac { S _ { n } } { a _ { n } } } = 1 + { \frac { 1 } { 3 } } { \big ( } n - 1 { \big ) } = { \frac { n + 2 } { 3 } }$ 得到 $S _ { n } = { \frac { \left( n + 2 \right) a _ { n } } { 3 } }$ 利用和与项的关系得到当 $n \geq 2$ 时， $a _ { n } = S _ { n } - S _ { n - 1 } = { \frac { \left( n + 2 \right) a _ { n } } { 3 } } - { \frac { \left( n + 1 \right) a _ { n - 1 } } { 3 } }$ ，进而得： $\frac { a _ { n } } { a _ { n - 1 } } = \frac { n + 1 } { n - 1 }$ ，利用累乘法求得 $a _ { n } = { \frac { n ( n + 1 ) } { 2 } }$ 检验对于 $n = 1$ 也成立，得到 $\left\{ a _ { n } \right\}$ 的通项公式 $a _ { n } = { \frac { n ( n + 1 ) } { 2 } }$ ：

（2）由（1）的结论，利用裂项求和法得到 ${ \frac { 1 } { a _ { 1 } } } + { \frac { 1 } { a _ { 2 } } } + \cdots + { \frac { 1 } { a _ { n } } } = 2 { \Biggl ( } 1 - { \frac { 1 } { n + 1 } } { \Biggr ) }$ 进而证得.

【详解】(1） $a _ { \mathrm { 1 } } = 1$ ， $\therefore S _ { 1 } = a _ { 1 } = 1 \ u , \ u , \ast \frac { S _ { 1 } } { a _ { 1 } } = 1 ,$   
又: $\left\{ { \frac { S _ { n } } { a _ { n } } } \right\}$ 是公差为 $\frac 1 3$ 的等差数列，  
$\therefore { \frac { S _ { n } } { a _ { n } } } = 1 + { \frac { 1 } { 3 } } { \big ( } n - 1 { \big ) } = { \frac { n + 2 } { 3 } } , \therefore S _ { n } = { \frac { { \big ( } n + 2 { \big ) } a _ { n } } { 3 } } ,$ $n \geq 2$ 时 $S _ { n - 1 } = { \frac { \left( n + 1 \right) a _ { n - 1 } } { 3 } }$   
$\therefore a _ { n } = S _ { n } - S _ { n - 1 } = { \frac { \left( n + 2 \right) a _ { n } } { 3 } } - { \frac { \left( n + 1 \right) a _ { n - 1 } } { 3 } } ,$   
整理得： ${ \bigl ( } n - 1 { \bigr ) } a _ { n } = { \bigl ( } n + 1 { \bigr ) } a _ { n - 1 }$ ，  
即 ${ \frac { a _ { n } } { a _ { n - 1 } } } = { \frac { n + 1 } { n - 1 } } ,$   
$a _ { n } = a _ { 1 } \times \frac { a _ { 2 } } { a _ { 1 } } \times \frac { a _ { 3 } } { a _ { 2 } } \times . . . \times \frac { a _ { n - 1 } } { a _ { n - 2 } } \times \frac { a _ { n } } { a _ { n - 1 } }$   
$= 1 \times \frac { 3 } { 1 } \times \frac { 4 } { 2 } \times \ldots \times \frac { n } { n - 2 } \times \frac { n + 1 } { n - 1 } = \frac { n \left( n + 1 \right) } { 2 }$   
显然对于 $n = 1$ 也成立,

$\therefore \left\{ \boldsymbol { a } _ { n } \right\}$ 的通项公式 $a _ { n } = { \frac { n ( n + 1 ) } { 2 } }$ ${ \begin{array} { c } { { \displaystyle { \frac { 1 } { a _ { n } } } = { \frac { 2 } { n \left( n + 1 \right) } } = 2 { \Bigg ( } { \frac { 1 } { n } } - { \frac { 1 } { n + 1 } } { \Bigg ) } , } } \\ { { \displaystyle { \frac { 1 } { a _ { 1 } } } + { \frac { 1 } { a _ { 2 } } } + \dots + { \frac { 1 } { a _ { n } } } = 2 { \Bigg [ } { \Bigg ( } 1 - { \frac { 1 } { 2 } } { \Bigg ) } + { \Bigg ( } { \frac { 1 } { 2 } } - { \frac { 1 } { 3 } } { \Bigg ) } + \dots { \Bigg ( } { \frac { 1 } { n } } - { \frac { 1 } { n + 1 } } { \Bigg ) } { \Bigg ] } = 2 { \Bigg ( } 1 - { \frac { 1 } { n + 1 } } { \Bigg ) } < 2 } } \end{array} }$

2．2023新高考1卷·20）设等差数列 $\left\{ a _ { n } \right\}$ 的公差为 $d$ 且 $d > 1$ 令 $b _ { n } = { \frac { n ^ { 2 } + n } { a _ { n } } }$ ，记 $S _ { n } , T _ { n }$ 分别为数列 $\left\{ a _ { n } \right\} , \left\{ b _ { n } \right\}$ 的前 $n$ 项和.

(1)若 $3 a _ { 2 } = 3 a _ { 1 } + a _ { 3 } , S _ { 3 } + T _ { 3 } = 2 1$ ，求 $\left\{ a _ { n } \right\}$ 的通项公式；(2)若 $\left\{ b _ { n } \right\}$ 为等差数列，且 $S _ { 9 9 } - T _ { 9 9 } = 9 9$ ，求 $d$ ：

【答案】 $\ a _ { n } = 3 n$ $( 2 ) d = \frac { 5 1 } { 5 0 }$

【分析】（1）根据等差数列的通项公式建立方程求解即可；

(2）由 $\left\{ b _ { n } \right\}$ 为等差数列得出 $a _ { \scriptscriptstyle 1 } = d$ 或 $a _ { 1 } = 2 d$ ，再由等差数列的性质可得 $a _ { 5 0 } - b _ { 5 0 } = 1$ ，分类讨论即可得解.

【详解】（1）∵ $3 a _ { 2 } = 3 a _ { 1 } + a _ { 3 }$ ， $\cdot , 3 d = a _ { 1 } + 2 d$ ，解得 $a _ { \scriptscriptstyle 1 } = d$ ，  
$\therefore S _ { 3 } = 3 a _ { 2 } = 3 ( a _ { 1 } + d ) = 6 d$   
又 $T _ { 3 } = b _ { 1 } + b _ { 2 } + b _ { 3 } = \frac { 2 } { d } + \frac { 6 } { 2 d } + \frac { 1 2 } { 3 d } = \frac { 9 } { d }$   
$\therefore S _ { 3 } + T _ { 3 } = 6 d + { \frac { 9 } { d } } = 2 1 ,$   
即 $2 d ^ { 2 } - 7 d + 3 = 0$ ，解得 $d = 3$ 或 $d = \frac { 1 } { 2 }$ （舍去），  
$\therefore a _ { n } = a _ { 1 } + ( n - 1 ) \cdot d = 3 n$   
$\cdots \{ b _ { n } \}$ 为等差数列，  
$\dot { \cdot } 2 b _ { 2 } = b _ { 1 } + b _ { 3 }$ ， 即 $\frac { 1 2 } { a _ { 2 } } = \frac { 2 } { a _ { 1 } } + \frac { 1 2 } { a _ { 3 } } ;$   
$\therefore 6 ( \frac { 1 } { a _ { 2 } } - \frac { 1 } { a _ { 3 } } ) = \frac { 6 d } { a _ { 2 } a _ { 3 } } = \frac { 1 } { a _ { 1 } }$ 即 $a _ { 1 } ^ { 2 } - 3 a _ { 1 } d + 2 d ^ { 2 } = 0$ ，解得 $a _ { \scriptscriptstyle 1 } = d$ 或 $a _ { 1 } = 2 d$ ，  
$\because d > 1 , \therefore a _ { n } > 0 ,$   
又 $S _ { 9 9 } - T _ { 9 9 } = 9 9$ ，由等差数列性质知， $9 9 a _ { 5 0 } - 9 9 b _ { 5 0 } = 9 9$ ，即 $a _ { 5 0 } - b _ { 5 0 } = 1$ ，$\cdot \ a _ { 5 0 } - \frac { 2 5 5 0 } { \ a _ { 5 0 } } = 1$ ，即 $a _ { 5 0 } ^ { 2 } - a _ { 5 0 } - 2 5 5 0 = 0$ ，解得 $a _ { 5 0 } = 5 1$ 或 $a _ { 5 0 } = - 5 0$ (舍去)当 $a _ { 1 } = 2 d$ 时， $a _ { 5 0 } = a _ { 1 } + 4 9 d = 5 1 d = 5 1$ ，解得 $d = 1$ ，与 $d > 1$ 矛盾，无解；当 $a _ { \scriptscriptstyle 1 } = d$ 时， $a _ { 5 0 } = a _ { 1 } + 4 9 d = 5 0 d = 5 1$ ，解得 $d = \frac { 5 1 } { 5 0 }$   
综上， $d = \frac { 5 1 } { 5 0 }$

3．（2022新高考Ⅱ卷·17）已知 $\left\{ a _ { n } \right\}$ 为等差数列， $\left\{ b _ { n } \right\}$ 是公比为2的等比数列，且$a _ { 2 } - b _ { 2 } = a _ { 3 } - b _ { 3 } = b _ { 4 } - a _ { 4 } .$

(1)证明： $a _ { \scriptscriptstyle 1 } = b _ { \scriptscriptstyle 1 }$ ;

(2)求集合 $\left\{ k \big | b _ { k } = a _ { m } + a _ { 1 } , 1 \leq m \leq 5 0 0 \right\}$ 中元素个数.

【答案】(1)证明见解析;  
(2)9.

【分析】（1）设数列 $\left\{ a _ { n } \right\}$ 的公差为 $^ { d }$ ，根据题意列出方程组即可证出；

（2）根据题意化简可得 $m = 2 ^ { k - 2 }$ ，即可解出.

【详解】（1）设数列 $\left\{ a _ { n } \right\}$ 的公差为 $^ { d }$ ，所以， $\left\{ \begin{array} { l l } { a _ { 1 } + d - 2 b _ { 1 } = a _ { 1 } + 2 d - 4 b _ { 1 } } \\ { a _ { 1 } + d - 2 b _ { 1 } = 8 b _ { 1 } - \left( a _ { 1 } + 3 d \right) } \end{array} \right.$ 即可解得， （204 $b _ { 1 } = a _ { 1 } = \frac { d } { 2 }$ 所以原命题得证.

(2）由（1)知， $b _ { 1 } = a _ { 1 } = \frac { d } { 2 }$ 所以 $b _ { k } = a _ { m } + a _ { 1 } \Longleftrightarrow b _ { 1 } \times 2 ^ { k - 1 } = a _ { 1 } + \left( m - 1 \right) d + a _ { 1 }$ ，即 $2 ^ { k - 1 } = 2 m$ ，亦即$m = 2 ^ { k - 2 } \in [ 1 , 5 0 0 ]$ ，解得 $2 \leq k \leq 1 0$ ，所以满足等式的解 $k = 2 , 3 , 4 , \cdots , 1 0$ ，故集合 $\left\{ k \mid b _ { k } = a _ { m } + a _ { 1 } , 1 \leq m \leq 5 0 0 \right\}$ （24中的元素个数为 $1 0 - 2 + 1 = 9$ ·

4．（2023 新高考Ⅱ卷·18）已知 $\left\{ a _ { n } \right\}$ 为等差数列， [2ann为偶数，记S，T分别为数列{a}，{b}的前 $n$ 项和， $S _ { 4 } = 3 2$ ， $T _ { 3 } = 1 6$ ：

(1)求 $\left\{ a _ { n } \right\}$ 的通项公式；(2)证明：当 $n > 5$ 时， $T _ { n } > S _ { n }$ ：

【答案】 $\ a _ { n } = 2 n + 3$ (2)证明见解析.

【分析】（1）设等差数列 $\left\{ a _ { n } \right\}$ 的公差为 $d$ ，用 $a _ { 1 } , d$ 表示 $S _ { n }$ 及 $T _ { n }$ ，即可求解作答.

（2）方法1，利用（1）的结论求出 $S _ { n }$ ， $b _ { n }$ ，再分奇偶结合分组求和法求出 $T _ { n }$ ，并与 $S _ { n }$ 作差比较作答；方法2，利用（1）的结论求出 $S _ { n }$ ， $b _ { n }$ ，再分奇偶借助等差数列前 $\mathbf { n }$ 项和公式求出 $T _ { n }$ ，并与 $S _ { n }$ 作差比较作答.

【详解】（1）设等差数列 $\left\{ a _ { n } \right\}$ 的公差为 $d$ ，而 $b _ { n } = \left\{ { \begin{array} { l } { a _ { n } - 6 , n = 2 k - 1 } \\ { 2 a _ { n } , n = 2 k } \end{array} } , k \in \mathrm { N } ^ { * } \right.$ 则 $b _ { 1 } = a _ { 1 } - 6 , b _ { 2 } = 2 a _ { 2 } = 2 a _ { 1 } + 2 d , b _ { 3 } = a _ { 3 } - 6 = a _ { 1 } + 2 d - 6 ,$   
于是 $\left\{ \begin{array} { l l } { S _ { 4 } = 4 a _ { 1 } + 6 d = 3 2 } \\ { T _ { 3 } = 4 a _ { 1 } + 4 d - 1 2 = 1 6 } \end{array} \right.$ 解得 $a _ { 1 } = 5 , d = 2$ ， $a _ { n } = a _ { 1 } + ( n - 1 ) d = 2 n + 3$ ，所以数列 $\left\{ a _ { n } \right\}$ 的通项公式是 $a _ { n } = 2 n + 3$ ：  
(2）方法1：由（1）知， $S _ { n } = \frac { n ( 5 + 2 n + 3 ) } { 2 } = n ^ { 2 } + 4 n b _ { n } = \left\{ \begin{array} { l l } { 2 n - 3 , n = 2 k - 1 } \\ { 4 n + 6 , n = 2 k } \end{array} \right. , k \in \mathrm { N } ^ { * }$   
当 $n$ 为偶数时， $b _ { n - 1 } + b _ { n } = 2 ( n - 1 ) - 3 + 4 n + 6 = 6 n + 1$ ，  
$T _ { n } = { \frac { 1 3 + ( 6 n + 1 ) } { 2 } } \cdot { \frac { n } { 2 } } = { \frac { 3 } { 2 } } n ^ { 2 } + { \frac { 7 } { 2 } } n$   
当 $n > 5$ 时， $T _ { n } - S _ { n } = ( \frac { 3 } { 2 } n ^ { 2 } + \frac { 7 } { 2 } n ) - ( n ^ { 2 } + 4 n ) = \frac { 1 } { 2 } n ( n - 1 ) > 0$ ，因此 $T _ { n } > S _ { n }$ ，  
当陽 $n$ 为奇数时， $T _ { n } = T _ { n + 1 } - b _ { n + 1 } = { \frac { 3 } { 2 } } ( n + 1 ) ^ { 2 } + { \frac { 7 } { 2 } } ( n + 1 ) - \left[ 4 ( n + 1 ) + 6 \right] = { \frac { 3 } { 2 } } n ^ { 2 } + { \frac { 5 } { 2 } } n - 5 \ ,$   
当 $n > 5$ 时， $T _ { n } - S _ { n } = ( { \frac { 3 } { 2 } } n ^ { 2 } + { \frac { 5 } { 2 } } n - 5 ) - ( n ^ { 2 } + 4 n ) = { \frac { 1 } { 2 } } ( n + 2 ) ( n - 5 ) > 0$ ，因此 $T _ { n } > S _ { n }$ ，  
所以当 $n > 5$ 时， $T _ { n } > S _ { n }$ ：  
方法2：由（1）知， $S _ { n } = \frac { n ( 5 + 2 n + 3 ) } { 2 } = n ^ { 2 } + 4 n , b _ { n } = \left\{ \begin{array} { l l } { 2 n - 3 , n = 2 k - 1 } \\ { 4 n + 6 , n = 2 k } \end{array} \right. , k \in \mathrm { N } ^ { * } ,$   
当陽 $n$ 为偶数时， $T _ { n } = ( b _ { 1 } + b _ { 3 } + \cdots + b _ { n - 1 } ) + ( b _ { 2 } + b _ { 4 } + \cdots + b _ { n } ) = { \frac { - 1 + 2 ( n - 1 ) - 3 } { 2 } } \cdot { \frac { n } { 2 } } + { \frac { 1 4 + 4 n + 6 } { 2 } } \cdot { \frac { n } { 2 } } = { \frac { 3 } { 2 } } n ^ { 2 } + { \frac { 7 } { 2 } } n$   
当 $n > 5$ 时 $T _ { n } - S _ { n } = ( { \frac { 3 } { 2 } } n ^ { 2 } + { \frac { 7 } { 2 } } n ) - ( n ^ { 2 } + 4 n ) = { \frac { 1 } { 2 } } n ( n - 1 ) > 0$ ，因此 $T _ { n } > S _ { n }$ ，  
当 $n$ 为奇数时，若 $n \geq 3$ ，则 $T _ { n } = ( b _ { 1 } + b _ { 3 } + \cdots + b _ { n } ) + ( b _ { 2 } + b _ { 4 } + \cdots + b _ { n - 1 } ) = { \frac { - 1 + 2 n - 3 } { 2 } } \cdot { \frac { n + 1 } { 2 } } + { \frac { 1 4 + 4 ( n - 1 ) + 6 } { 2 } } \cdot { \frac { n - 1 } { 2 } }$   
$= \frac { 3 } { 2 } n ^ { 2 } + \frac { 5 } { 2 } n - 5$ n-5，显然=𝑏=−|满足上式，因此当为奇数时， $T _ { n } = { \frac { 3 } { 2 } } n ^ { 2 } + { \frac { 5 } { 2 } } n - 5$   
当 $n > 5$ 时， $T _ { n } - S _ { n } = ( { \frac { 3 } { 2 } } n ^ { 2 } + { \frac { 5 } { 2 } } n - 5 ) - ( n ^ { 2 } + 4 n ) = { \frac { 1 } { 2 } } ( n + 2 ) ( n - 5 ) > 0$ ，因此 $T _ { n } > S _ { n }$ ，  
所以当 $n > 5$ 时， $T _ { n } > S _ { n }$ ：

# 必备知识速记

# 等差数列的有关概念

# 1、等差数列的定义

一般地，如果一个数列从第2项起，每一项与它的前一项的差等于同一个常数，那么这个数列就叫做等差数列，这个常数叫做等差数列的公差，通常用字母 $d$ 表示，定义表达式为 $a _ { n } - a _ { n - 1 } = d$ （常数）$( n \in N ^ { * } , ~ n \geq 2 )$

# 2、等差中项

若三个数 $a$ ， $A$ ， $^ { b }$ 成等差数列，则 $A$ 叫做 $a$ 与 $^ { b }$ 的等差中项，且有 $\scriptstyle A = { \frac { a + b } { 2 } }$

# 等差数列的有关公式

# 1、等差数列的通项公式

如果等差数列 $\left\{ a _ { n } \right\}$ 的首项为 $a _ { 1 }$ ，公差为 $d$ ，那么它的通项公式是 $a _ { n } = a _ { 1 } + ( n - 1 ) d$ ：

# 2、等差数列的前 $n$ 项和公式

设等差数列 $\left\{ a _ { n } \right\}$ 的公差为 $d$ ，其前 $n$ 项和 $S _ { n } = n a _ { 1 } + \frac { n ( n - 1 ) } { 2 } d = \frac { n ( a _ { 1 } + a _ { n } ) } { 2 } .$

# 三、等差数列的常用性质

已知 $\left\{ a _ { n } \right\}$ 为等差数列， $d$ 为公差， $S _ { n }$ 为该数列的前 $n$ 项和.

1、通项公式的推广： $\begin{array} { r } { a _ { n } = a _ { m } + ( n - m ) d ( n , ~ m \in N ^ { * } ) } \end{array}$ ：

2、在等差数列 $\left\{ a _ { n } \right\}$ 中，当 $m + n = p + q$ 时， $a _ { { \scriptscriptstyle m } } + a _ { { \scriptscriptstyle n } } = a _ { { \scriptscriptstyle p } } + a _ { { \scriptscriptstyle q } } ( m , n , p , q \in N ^ { * } )$ 特别地，若 $m + n = 2 t$ ，则 $a _ { \scriptscriptstyle m } + a _ { \scriptscriptstyle n } = 2 a _ { \scriptscriptstyle t } ( m , n , t \in N ^ { \ast } )$ ：

3、 $a _ { k }$ ， $a _ { k + m }$ ， $a _ { k + 2 m }$ ，..仍是等差数列，公差为 $m d ( k , ~ m \in N ^ { * } )$ ：

4、Sn，S2n-S,，S3n—S2n，..也成等差数列，公差为n²d.

5、若 $\{ a _ { n } \} \ , \quad \{ b _ { n } \}$ 是等差数列，则 $\{ p a _ { n } + q b _ { n } \}$ 也是等差数列.

6、若 $\left\{ a _ { n } \right\}$ 是等差数列，则 $\{ { \frac { S _ { n } } { n } } \}$ 也成等差数列，其首项与 $\left\{ a _ { n } \right\}$ 首项相同，公差是 $\{ a _ { n } \}$ 公差的 $\frac { 1 } { 2 }$

7、若项数为偶数 $2 n$ ，则 $S _ { 2 n } = n ( a _ { 1 } + a _ { 2 n } ) = n ( a _ { n } + a _ { n + 1 } ) ~ ; ~ S _ { _ { \scriptscriptstyle  { 1 \vert \ast } } } - S _ { _ { \scriptscriptstyle  { 1 \vert \ast } } } = n d ~ ; ~ \frac { S _ { _ { \scriptscriptstyle  { 1 \vert \ast } } } } { S _ { _ { \scriptscriptstyle  { 1 \vert \ast } } } } = \frac { a _ { n } } { a _ { n + 1 } } ~ .$

8、若项数为奇数2n-1，则 S2n-1=(2n-1)an；S寄—S偶=an； $S _ { 2 n - 1 } = ( 2 n - 1 ) a _ { n } \ ; S _ { \scriptscriptstyle  {  { e f } } } - S _ { \scriptscriptstyle  {  { e f } } } = a _ { n } \ ; \frac { S _ { \scriptscriptstyle  { e f } } } { S _ { \scriptscriptstyle  {  { e f } } } } = \frac { n } { n - 1 } \ .$

9、在等差数列 $\left\{ a _ { n } \right\}$ 中，若 $a _ { \scriptscriptstyle 1 } > 0$ ， $d < 0$ ，则满足 $\begin{array} { l } { \displaystyle { \int _ { a _ { m } } \geq 0 } } \\ { \displaystyle { \left\{ a _ { m + 1 } \leq 0 \right. } }  \end{array}$ 的项数 $m$ 使得 $S _ { n }$ 取得最大值 $S _ { m }$ ；若 $a _ { \scriptscriptstyle 1 } < 0 , d > 0$ ， 则满足 $\begin{array} { l } { \displaystyle { \int _ { a _ { m } } \le 0 } } \\ { \displaystyle { \left\{ a _ { m + 1 } \ge 0 \right. } }  \end{array}$ 的项数 $m$ 使得 $S _ { n }$ 取得最小值 $S _ { m }$

# 四、等差数列的前 $\mathbf { n }$ 项和公式与函数的关系

$S _ { n } = \frac { d } { 2 } n ^ { 2 } + ( a _ { 1 } - \frac { d } { 2 } ) n$ ．数列 $\left\{ a _ { n } \right\}$ 是等差数列 $\Leftrightarrow S _ { n } = A n ^ { 2 } + B n$ （ $A , \ B$ 为常数）.

# 五、等差数列的前 $\cdot$ 项和的最值

公差 $d > 0 \Leftrightarrow \{ a _ { n } \}$ 为递增等差数列， $S _ { n }$ 有最小值;  
公差 $d < 0 \Leftrightarrow \{ a _ { n } \}$ 为递减等差数列， $S _ { n }$ 有最大值;  
公差 $d = 0 \Leftrightarrow \{ a _ { n } \}$ 为常数列.

# 特别地

若>0 ，则 $S _ { n }$ 有最大值（所有正项或非负项之和）；  
若<0 ，则 $S _ { n }$ 有最小值（所有负项或非正项之和）.

# 六、其他衍生等差数列.

1、若已知等差数列 $\left\{ a _ { n } \right\}$ ，公差为 $d$ ，前 $n$ 项和为 $S _ { n }$ ，则：$\textcircled{1}$ 等间距抽取 $a _ { p } , a _ { p + t } , a _ { p + 2 t } , \cdots a _ { p + ( n - 1 ) t } , \cdots$ 为等差数列，公差为td.$\textcircled{2}$ 等长度截取 $S _ { m } , S _ { 2 m } - S _ { m } , S _ { 3 m } - S _ { 2 m } , \cdots$ 为等差数列，公差为 $m ^ { 2 } d$ ：③算术平均值 $\frac { S _ { 1 } } { 1 } , \frac { S _ { 2 } } { 2 } , \frac { S _ { 3 } } { 3 } ,$ …为等差数列，公差为 $\frac { d } { 2 }$

# 七、等比数列的有关概念

1、定义：如果一个数列从第2项起，每一项与它的前一项的比等于同一常数（不为零），那么这个数列就叫做等比数列，这个常数叫做等比数列的公比，通常用字母 $q$ 表示，定义的表达式为 $\scriptstyle { \frac { a _ { n + 1 } } { a _ { n } } } = q$

2、等比中项：如果 $a$ ， $G$ ， $^ b$ 成等比数列，那么 $G$ 叫做 $a$ 与 $^ b$ 的等比中项.即 $G$ 是 $a$ 与 $^ b$ 的等比中项 $\Leftrightarrow _ { a }$ ， $G$ ， $^ b$ 成等比数列 $\Rightarrow G ^ { 2 } = a b$ ：

# 八、等比数列的有关公式

# 1、等比数列的通项公式

设等比数列 $\left\{ a _ { n } \right\}$ 的首项为 $a _ { \scriptscriptstyle 1 }$ ，公比为 $q ( q \neq 0 )$ ，则它的通项公式 $a _ { n } = a _ { 1 } q ^ { n - 1 } = c \cdot q ^ { n } ( c = { \frac { a _ { 1 } } { q } } ) ( a _ { 1 } , q \neq 0 )$ 推广形式： $a _ { n } = a _ { m } \cdot q ^ { n - m }$

# 2、等比数列的前 $\mathbf { n }$ 项和公式

等比数列{a)的公比为q(q≠0)，其前n项和为S={g(l-q"）−q(q≠1) $S _ { n } = \left\{ \begin{array} { l l } { n a _ { 1 } ( q = 1 ) } \\ { \displaystyle \frac { a _ { 1 } ( 1 - q ^ { n } ) } { 1 - q } = \frac { a _ { 1 } - a _ { n } q } { 1 - q } ( q \neq 1 ) } \end{array} \right.$

# 九、等比数列的性质

1、等比中项的推广若 $m + n = p + q$ 时，则 $a _ { m } a _ { n } = a _ { p } a _ { q }$ ，特别地，当 $m + n = 2 p$ 时， $a _ { { } _ { m } } a _ { { } _ { n } } = a _ { { } _ { p } } ^ { 2 }$ ：

2、 $\textcircled{1}$ 设 $\left\{ a _ { n } \right\}$ 为等比数列，则 $\{ \lambda a _ { n } \}$ （为非零常数）， $\{ \left| a _ { n } \right| \}$ ， $\{ a _ { n } ^ { m } \}$ 仍为等比数列.  
$\textcircled{2}$ 设 $\left\{ a _ { n } \right\}$ 与 $\{ { \sf b } _ { n } \}$ 为等比数列，则 $\{ a _ { n } \ b _ { n } \}$ 也为等比数列.3、等比数列 $\left\{ a _ { n } \right\}$ 的单调性（等比数列的单调性由首项 $a _ { \scriptscriptstyle 1 }$ 与公比 $q$ 决定）.  
当 $\begin{array} { l } { \left\{ a _ { 1 } > 0 \right. } \\ { \left. \begin{array} { r l } \end{array} \right. } \end{array}$ 或祎 （204 $\begin{array} { l } { \int a _ { 1 } < 0 } \\ { \left\lfloor 0 < q < 1 \right\rfloor } \end{array}$ 时， $\left\{ a _ { n } \right\}$ 为递增数列;  
当 $\begin{array} { l } { { \displaystyle { q _ { _ { 1 } } > 0 } } } \\ { { \displaystyle { ) < q < 1 } ^ { \oplus } } } \end{array} \right. \left\{ { a _ { _ { 1 } } < 0 } \atop { q > 1 } $ 时， $\left\{ a _ { n } \right\}$ 为递减数列.4、其他衍生等比数列.  
若已知等比数列 $\left\{ a _ { n } \right\}$ ，公比为 $q$ ，前 $n$ 项和为 $S _ { n }$ ，则：  
$\textcircled{1}$ 等间距抽取  
$a _ { p } , a _ { p + t } , a _ { p + 2 t } , \cdot \cdot a _ { p + ( n - 1 ) t }$ 为等比数列，公比为 $\boldsymbol { q } ^ { \mathrm { ~ \prime ~ } }$ ：  
$\textcircled{2}$ 等长度截取  
$S _ { m } , S _ { 2 m } - S _ { m } , S _ { 3 m } - S _ { 2 m } , \nonumber$ .为等比数列，公比为 $q ^ { m }$ （当 $q = - 1$ 时， $m$ 不为偶数）.

# 十、求数列的通项公式

1、观察法：  
已知数列前若干项，求该数列的通项时，一般对所给的项观察分析，寻找规律，从而根据规律写出此数列的一个通项.

# 2、公式法：

若已知数列的前 $n$ 项和 $S _ { n }$ 与 $\boldsymbol { a } _ { \ n }$ 的关系，求数列 $\left\{ a _ { n } \right\}$ 的通项 $\boldsymbol { a } _ { \ n }$ 可用公式  
$a _ { n } = \left\{ { { S _ { 1 } } , \left( { n = 1 } \right) } \atop { { S _ { n } - S _ { n - 1 } } , \left( { n \geq 2 } \right) } \right.$ 构造两式作差求解.  
用此公式时要注意结论有两种可能，一种是“一分为二”，即分段式；另一种是"合二为一”，即 $a _ { \scriptscriptstyle 1 }$ 和 $\boldsymbol { a } _ { \scriptscriptstyle n }$ 合为一个表达，（要先分 $n { = } 1$ 和 $n { \geq } 2$ 两种情况分别进行运算，然后验证能否统一）．

# 3、累加法：

形如 $a _ { n + 1 } = a _ { n } + f ( n )$ 型的递推数列（其中 $f ( n )$ 是关于 $n$ 的函数）可构造： $\left\{ \begin{array} { l l } { a _ { n } - a _ { n - 1 } = f ( n - 1 ) } \\ { a _ { n - 1 } - a _ { n - 2 } = f ( n - 2 ) } \\ { \ldots } \\ { a _ { 2 } - a _ { 1 } = f ( 1 ) } \end{array} \right.$ 将上述 $m _ { 2 }$ 个式子两边分别相加，可得： $a _ { n } = f ( n - 1 ) + f ( n - 2 ) + . . . f ( 2 ) + f ( 1 ) + a _ { 1 } , ( n \geq 2 )$ $\textcircled{1}$ 若 $f ( n )$ 是关于 $n$ 的一次函数，累加后可转化为等差数列求和；  
$\textcircled{2}$ 若 $f ( n )$ 是关于 $n$ 的指数函数，累加后可转化为等比数列求和；  
$\textcircled{3}$ 若 $f ( n )$ 是关于 $n$ 的二次函数，累加后可分组求和；

$\textcircled{4}$ 若 $f ( n )$ 是关于 $n$ 的分式函数，累加后可裂项求和.

# 4、累乘法：

形如 $a _ { n + 1 } = a _ { n } \cdot f ( n ) \left( { \frac { a _ { n + 1 } } { a _ { n } } } = f ( n ) \right)$ 型的递推数列（其中 $f ( n )$ 是关于 $n$ 的函数）可构造： $\left\{ \begin{array} { l l } { \displaystyle \frac { a _ { n } } { a _ { n - 1 } } = f ( n - 1 ) } \\ { \quad } \\ { \displaystyle \frac { a _ { n - 1 } } { a _ { n - 2 } } = f ( n - 2 ) } \\ { \quad } \\ { \cdots } \\ { \displaystyle \frac { a _ { 2 } } { a _ { 1 } } = f ( 1 ) } \end{array} \right.$ 将上述 $m _ { 2 }$ 个式子两边分别相乘，可得： $a _ { n } = f ( n - 1 ) \cdot f ( n - 2 ) \cdot \ldots \cdot f ( 2 ) f ( 1 ) a _ { 1 } , ( n \geq 2 )$ 有时若不能直接用，可变形成这种形式，然后用这种方法求解.

# 5、构造数列法：

（一）形如 $a _ { n + 1 } = p a _ { n } + q$ （其中 $p , q$ 均为常数且 $p \neq 0$ ）型的递推式：

（1）若 $p { = } 1$ 时，数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 为等差数列；  
(2）若 $q = 0$ 时，数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 为等比数列；  
（3）若 $p { \neq } 1$ 且 $q \neq 0$ 时，数列 $\left\{ \begin{array} { l } { a _ { n } } \end{array} \right\}$ 为线性递推数列，其通项可通过待定系数法构造等比数列来求．方法有如下两种：

法一：设 $\displaystyle a _ { n + 1 } + \lambda = p ( a _ { n } + \lambda )$ ，展开移项整理得 $a _ { n + 1 } = p a _ { n } + ( p - 1 ) \lambda$ ，与题设 $a _ { n + 1 } = p a _ { n } + q$ 比较系数（待定系数法）得 $\lambda = \frac { q } { p - 1 } , ( p \neq 0 ) \Rightarrow a _ { n + 1 } + \frac { q } { p - 1 } = p ( a _ { n } + \frac { q } { p - 1 } ) \Rightarrow a _ { n } + \frac { q } { p - 1 } = p ( a _ { n - 1 } + \frac { q } { p - 1 } )$ ）， 即 $\left\{ a _ { n } + { \frac { q } { p - 1 } } \right\}$ 构成以 $a _ { 1 } + \frac { q } { p - 1 }$ 为首项，以 $p$ 为公比的等比数列，再利用等比数列的通项公式求出 $\left\{ a _ { n } + { \frac { q } { p - 1 } } \right\}$ 的通项整理可得an

法二：由 $a _ { n + 1 } = p a _ { n } + q$ 得 $a _ { n } = p a _ { n - 1 } + q ( n \geq 2 )$ 两式相减并整理得 ${ \frac { a _ { n + 1 } - a _ { n } } { a _ { n } - a _ { n - 1 } } } = p ;$ 即， $\left\{ a _ { n + 1 } - a _ { n } \right\}$ 构成以 $a _ { 2 } - a _ { 1 }$ 为首项，以 $p$ 为公比的等比数列．求出 $\left\{ a _ { n + 1 } - a _ { n } \right\}$ 的通项再转化为类型Ⅲ（累加法）便可求出 $a _ { n }$

（二）形如 $a _ { n + 1 } = p a _ { n } + f ( n ) ( p \neq 1 )$ 型的递推式：

（1）当 $f ( n )$ 为一次函数类型（即等差数列）时：  
设 $a _ { n } + A n + B = p \left[ a _ { n - 1 } + A ( n - 1 ) + B \right]$ ，通过待定系数法确定 $A \setminus B$ 的值，转化成以 $a _ { 1 } + A + B$ 为首项，以  
$A _ { n } ^ { m } = \frac { n ! } { \left( n - m \right) ! }$ 为公比的等比数列 $\left\{ a _ { n } + A n + B \right\}$ ，再利用等比数列的通项公式求出 $\left\{ a _ { n } + A n + B \right\}$ 的通项整理可  
得an

# 一、数列求和

# 1、公式法

（1）等差数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和 $S _ { n } = \frac { n ( a _ { 1 } + a _ { n } ) } { 2 } = n a _ { 1 } + \frac { n ( n + 1 ) } { 2 } d$ （2）等比数列{an}的前n项和S={a(l-q") $S _ { n } = \left\{ \begin{array} { l l } { n a _ { 1 } \mathrm { ~ } , \ q = 1 } \\ { \displaystyle \frac { a _ { 1 } ( 1 - q ^ { n } ) } { 1 - q } , \ q \neq 1 } \end{array} \right.$

（3）一些常见的数列的前 $n$ 项和：

$\textcircled { 1 } \sum _ { k = 1 } ^ { n } k = 1 + 2 + 3 + \cdots + n = { \frac { 1 } { 2 } } n ( n + 1 ) ; \quad \sum _ { k = 1 } ^ { n } 2 k = 2 + 4 + 6 + \cdots + 2 n = n ( n + 1 )$   
$\textcircled { 2 } \sum _ { k = 1 } ^ { n } ( 2 k - 1 ) = 1 + 3 + 5 + \dots + ( 2 n - 1 ) = n ^ { 2 } ;$   
$\textcircled { 3 } \sum _ { k = 1 } ^ { n } k ^ { 2 } = 1 ^ { 2 } + 2 ^ { 2 } + 3 ^ { 2 } + \dots + n ^ { 2 } = { \frac { 1 } { 6 } } n ( n + 1 ) ( 2 n + 1 ) ;$   
$\textcircled { 4 } \sum _ { k = 1 } ^ { n } k ^ { 3 } = 1 ^ { 3 } + 2 ^ { 3 } + 3 ^ { 3 } + \dots + n ^ { 3 } = [ \frac { n ( n + 1 ) } { 2 } ] ^ { 2 }$

# 2、几种数列求和的常用方法

（1）分组求和法：一个数列的通项公式是由若干个等差或等比或可求和的数列组成的，则求和时可用分组求和法，分别求和后相加减.  
（2）并项求和法：一个数列的前 $\mathbf { n }$ 项和中，可两两结合求解，则称之为并项求和.  
（3）裂项相消法：把数列的通项拆成两项之差，在求和时中间的一些项可以相互抵消，从而求得前 $n$ 项和.  
（4）错位相减法：如果一个数列的各项是由一个等差数列和一个等比数列的对应项之积构成的，那么求这个数列的前 $n$ 项和即可用错位相减法求解.  
（5）倒序相加法：如果一个数列 $\left\{ a _ { n } \right\}$ 与首末两端等"距离"的两项的和相等或等于同一个常数，那么求这个数列的前 $n$ 项和即可用倒序相加法求解.

# 【数列常用结论】

# 1、数列的递推公式

（1）若数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和为 $S _ { n }$ ，通项公式为 $a _ { n }$ ，则 $a _ { n } = \left\{ { { S _ { 1 } } \atop { { \cal S } _ { n } } } , n = 1 \right.$ m≥2，$n \in N ^ { * }$ 注意：根据 $S _ { n }$ 求 $a _ { n }$ 时，不要忽视对 $n = 1$ 的验证.（2）在数列 $\left\{ a _ { n } \right\}$ 中，若 $a _ { n }$ 最大，则 $\begin{array} { r } { \left\{ { a _ { n } \geq a _ { n - 1 } } \atop { a _ { n } \geq a _ { n + 1 } } \right. } \end{array}$ n≥an-1,若an最小，则 （204 $\begin{array} { r } { \left\{ { a _ { n } \leq a _ { n - 1 } } \atop { a _ { n } \leq a _ { n + 1 } } \right. } \end{array}$

# 2、等差数列

（1）等差数列 $\{ a _ { n } \}$ 中，若 $a _ { n } = m , a _ { m } = n ( m \neq n , m , n \in N ^ { * } )$ ，则 $a _ { { \scriptscriptstyle m } + n } = 0$ ：（2）等差数列 $\left\{ a _ { n } \right\}$ 中，若 $S _ { n } = m , S _ { m } = n ( m \neq n , m , n \in N ^ { * } )$ ，则 $S _ { m + n } = - ( m + n )$ ：

（3）等差数列 $\left\{ a _ { n } \right\}$ 中，若 $S _ { n } = S _ { m } ( m \neq n , m , n \in N ^ { * } )$ ，则 $S _ { m + n } = 0$

（4）若 $\left\{ a _ { n } \right\}$ 与 $\left\{ \mathbf { b } _ { n } \right\}$ 为等差数列，且前 $n$ 项和为 $S _ { n }$ 与 $T _ { n }$ ，则 $\frac { a _ { m } } { b _ { m } } = \frac { S _ { 2 m - 1 } } { T _ { 2 m - 1 } }$

# 3、等比数列

（1）若 $m + n = p + q = 2 k ( m , n , p , q , q , k \in N ^ { * } )$ ，则 $a _ { _ m } \cdot a _ { _ n } { = } a _ { _ p } \cdot a _ { _ q } { = } a _ { _ k } ^ { _ 2 }$

(2）若 $\{ a _ { n } \} \ , \quad \{ b _ { n } \}$ （项数相同）是等比数列，则 $\{ \lambda a _ { _ n } \} ( \lambda \ne 0 ) ~ , ~ \{ \frac { 1 } { a _ { _ n } } \} ~ , ~ \{ a _ { _ n } ^ { ~ 2 } \} ~ , ~ \{ a _ { _ n } \cdot b _ { _ n } \} ~ , ~ \{ \frac { a _ { _ n } } { b _ { _ n } } \}$ 仍是等比数 列.

（3）在等比数列 $\left\{ a _ { n } \right\}$ 中，等距离取出若干项也构成一个等比数列，即 $a _ { n }$ ， $a _ { n + k }$ ， $a _ { n + 2 k }$ ， $a _ { n + 3 k } \ldots$ 为等比数列，公比为 $q ^ { \ k }$ ：

（4）公比不为－1的等比数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和为 $S _ { n }$ ，则 $S _ { n } ~ , ~ S _ { 2 n } - S _ { n } ~ , ~ S _ { 3 n } - S _ { 2 n }$ 仍成等比数列，其公比为$q ^ { \ n }$ ：

（5 $\{ a _ { n } \}$ 为等比数列，若 $a _ { 1 } \cdot a _ { 2 } \dots a _ { n } { = } T _ { n }$ 则 $T _ { n }$ $\ , \ { \frac { T _ { 2 n } } { T _ { n } } } , \ { \frac { T _ { 3 n } } { T _ { 2 n } } }$ ，.等比数列，

(6）当 $q \neq 0$ ， $q \neq 1$ 时， $S _ { n } = k ^ { - } k { \cdot } q ^ { n } ( k \neq 0 )$ 是 $\left\{ a _ { n } \right\}$ 成等比数列的充要条件，此时 $k = \frac { a _ { 1 } } { 1 - q }$

（7）有穷等比数列中，与首末两项等距离的两项的积相等．特别地，若项数为奇数时，还等于中间项的平方.

（8）若 $\left\{ a _ { n } \right\}$ 为正项等比数列，则 $\{ \log _ { c } a _ { n } \} ( { \bf c } > 0 , { \tt c } \neq 1 )$ 为等差数列.

（9）若 $\left\{ a _ { n } \right\}$ 为等差数列，则 $\{ \mathbf { c } ^ { a _ { n } } \} ( \mathbf { c } > 0 , \mathbf { c } \neq 1 )$ 为等比数列.

（10）若 $\left\{ a _ { n } \right\}$ 既是等差数列又是等比数列 $\Leftrightarrow \{ a _ { n } )$ 是非零常数列.

# 4、数列求和

（1）裂项技巧$\textcircled{1}$ 等差型

(1） ${ \frac { 1 } { n ( n + 1 ) } } = { \frac { 1 } { n } } - { \frac { 1 } { n + 1 } }$   
(2） $\frac { 1 } { n ( n + k ) } = \frac { 1 } { k } ( \frac { 1 } { n } - \frac { 1 } { n + k } )$   
（3) $\frac { 1 } { 4 n ^ { 2 } - 1 } = \frac { 1 } { 2 } ( \frac { 1 } { 2 n - 1 } - \frac { 1 } { 2 n + 1 } )$   
$\textcircled{2}$ 根式型   
(1) $\begin{array} { l } { \displaystyle { \frac { 1 } { \sqrt { n + 1 } + \sqrt { n } } = \sqrt { n + 1 } - \sqrt { n } } } \\ { \displaystyle { \frac { 1 } { \sqrt { n + k } + \sqrt { n } } = \frac { 1 } { k } ( \sqrt { n + k } - \sqrt { n } ) } } \end{array}$   
(2)

(3） $\frac { 1 } { \sqrt { 2 n - 1 } + \sqrt { 2 n + 1 } } = \frac { 1 } { 2 } ( \sqrt { 2 n + 1 } - \sqrt { 2 n - 1 } )$ $\textcircled{3}$ 指数型

$$
{ \frac { 2 ^ { n } } { ( 2 ^ { n + 1 } - 1 ) ( 2 ^ { n } - 1 ) } } = { \frac { ( 2 ^ { n + 1 } - 1 ) - ( 2 ^ { n } - 1 ) } { ( 2 ^ { n + 1 } - 1 ) ( 2 ^ { n } - 1 ) } } = { \frac { 1 } { 2 ^ { n } - 1 } } - { \frac { 1 } { 2 ^ { n + 1 } - 1 } }
$$

# 名校模拟探源

# 一、单选题

1． （2024·江西九江·三模）已知等差数列{an}的公差为d(d≠0)，a是a4与𝛼g的等比中项，则 $\frac { a _ { 1 } } { d } = ~ ( ~ )$

A. $- \frac { 5 } { 2 }$ $\mathrm { ~ \bf ~ B . ~ } - \frac { 2 } { 5 } { \mathrm { ~  ~ \ c ~ } } . \frac { 5 } { 2 } { \mathrm { ~  ~ \omega ~ } } \mathrm { ~  ~ \makebox ~ { ~ \bf ~ D . ~ } ~ } \frac { 2 } { 5 } { \mathrm { ~  ~ \omega ~ } }$

2. （2024·天津滨海新·三模）已知数列 $\left\{ a _ { n } \right\}$ 为各项不为零的等差数列， $S _ { n }$ 为数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和,$4 S _ { n } = a _ { n } \cdot a _ { n + 1 }$ ，则 $a _ { 8 }$ 的值为（）

A.4 B.8 C. 12 D.16

3．（2024·天津北辰·三模）已知在等比数列 $\left\{ a _ { n } \right\}$ 中， $a _ { 4 } a _ { 8 } = 1 2 a _ { 6 }$ ，等差数列 $\left\{ b _ { n } \right\}$ 的前 $n$ 项和为 $S _ { n }$ ，且$2 b _ { 4 } = a _ { 6 }$ ，则 $S _ { 7 } =$ （）

A.60 B.54 C. 42 D.36

4. （2024·新疆喀什·三模）已知等差数列 $\left\{ a _ { n } \right\}$ 满足 $a _ { 2 } + a _ { 5 } + a _ { 8 } = 1 5$ ，记 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和为 $S _ { n }$ ，则 $S _ { 9 } = \mathrm { ~ ( ~ ) ~ }$

A.18 B.24 C. 27 D. 45

5．（2024·陕西西安·三模）已知 $S _ { n }$ 是等比数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和， $a _ { \scriptscriptstyle 1 } + a _ { \scriptscriptstyle 4 } + a _ { \scriptscriptstyle 7 } = 2$ ， $a _ { 2 } + a _ { 5 } + a _ { 8 } = 4$ ，则 $S _ { 9 } =$ （）

A.12 B.14 C.16 D.18

6．（2024·广东汕头·三模）已知等差数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和为 $S _ { n }$ ， $a _ { 2 } = 3$ ， $a _ { 2 n } = 2 a _ { n } + 1$ ，若 $S _ { n } + a _ { n + 1 } = 1 0 0$ ，则 $n =$ （）

A.8 B.9 C.10 D.11

7．（2024·浙江·三模）已知等差数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和为 $S _ { n }$ ，“ $a _ { 2 0 2 4 } = 0$ ”是“ $S _ { n } = S _ { 4 0 4 7 - n } \left( n < 4 0 4 7 , n \in \mathbf { N } ^ { * } \right)$ 的（）

A．充分不必要条件 B．必要不充分条件C．充要条件 D．既不充分也不必要条件

8．（2023·天津和平·三模）已知数列 $\left\{ a _ { n } \right\}$ 满足 $a _ { \scriptscriptstyle 1 } = 1$ ， $a _ { n + 1 } = 2 a _ { n } + 1 { \Big ( } n \in \mathbf { N } ^ { * } { \Big ) }$ ， $S _ { n }$ 是数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和，则 $S _ { 9 } = \mathrm { ~ ( ~ ) ~ }$

A. $2 ^ { 9 } - 1 0$ B. 29-11 C. $2 ^ { 1 0 } - 1 0$ D. $2 ^ { 1 0 } - 1 1$

9．（2024·陕西西安·三模）如图，用相同的球堆成若干堆"正三棱锥"形的装饰品，其中第1堆只有1层，且只有1个球；第2堆有2层4个球，其中第1层有1个球，第2层有3个球；...；第 $n$ 堆有 $n$ 层共 $S _ { n }$ 个球，第1层有1个球，第2层有3个球，第3层有6个球，．．。．已知 $S _ { 2 0 } = 1 5 4 0$ ，则 $\sum _ { n = 1 } ^ { 2 0 } n ^ { 2 } =$ （ ）

![](images/5f32d7933f6a91b7ad5d8937d703a9827af70608636e93349943111445cf4dbc.jpg)

A． 2290 B．2540 C. 2650 D. 2870

$a _ { 1 } = 1 , a _ { n + 1 } = { \left\{ \begin{array} { l l } { a _ { n } + 1 , n { \xrightarrow { } } \forall \qquad { \xrightarrow { } } \qquad } \\ { 2 a _ { n } , n { \xrightarrow { } } \forall \qquad { \overset { \mathrm { i f } } { | } } \sharp { \xrightarrow { } } \qquad } \end{array} \right. }$ 改10．（2024·河北张家口·三模）已知数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和为 $S _ { n }$ ，且满足 ，则 $S _ { 1 0 0 } =$ （）

A. $3 \times 2 ^ { 5 1 } - 1 5 6$ B. $3 \times 2 ^ { 5 1 } - 1 0 3$ C. $3 \times 2 ^ { 5 0 } - 1 5 6$ D. 3×250-103

11．(2024·浙江绍兴·三模）设 $0 \leq a _ { 1 } < a _ { 2 } < \cdots < a _ { 9 9 } < a _ { 1 0 0 } \leq 1$ ，已知 $a _ { n + 1 } \geq 3 a _ { n } \left( 1 \leq n \leq 9 9 \right)$ ，若 $\operatorname* { m a x } \left\{ a _ { n + 1 } - a _ { n } \right\} \geq m$ 恒成立，则 $m$ 的取值范围为（）

A. $m \leq \frac { 1 } { 9 }$ $\begin{array} { l } { { \mathrm { ~  ~ B ~ . ~ } } } { { \displaystyle m \leq \frac { 1 } { 3 } } } \\ { { \mathrm { ~  ~ \ D ~ . ~ } } } \end{array}$ c $m \leq \frac { 2 } { 3 }$

# 二、多选题

12．（2024·江西·三模）已知数列 $\left\{ a _ { n } \right\}$ 满足 $a _ { \scriptscriptstyle 1 } = 1 , a _ { \scriptscriptstyle n + 1 } = 2 a _ { \scriptscriptstyle n } + 1$ ，则（）

A．数列 $\left\{ a _ { n } \right\}$ 是等比数列 B．数列 $\left\{ \log _ { 2 } \left( a _ { n } + 1 \right) \right\}$ 是等差数列 C．数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和为 $2 ^ { n + 1 } - n - 2$ D. $a _ { 2 0 }$ 能被3整除

13．（2024·湖南益阳·三模）已知 $\left\{ a _ { n } \right\}$ 是等比数列， $S _ { n }$ 是其前 $n$ 项和，满足 $a _ { 3 } = 2 a _ { 1 } + a _ { 2 }$ ，则下列说法正确的有（）

A．若 $\left\{ a _ { n } \right\}$ 是正项数列，则 $\left\{ a _ { n } \right\}$ 是单调递增数列  
B. $S _ { n } , S _ { 2 n } - S _ { n } , S _ { 3 n } - S _ { 2 n }$ 一定是等比数列  
C．若存在 $M > 0$ ，使 $\left. a _ { n } \right. \leq M$ 对 $\forall n \in \mathbf { N } ^ { + }$ 都成立，则 $\left\{ \mid a _ { n } \mid \right\}$ 是等差数列  
D.若 $a _ { n } > 0$ ，且 $a _ { 1 } = { \frac { 1 } { 1 0 0 } } , ~ T _ { n } = a _ { 1 } \cdot a _ { 2 } \cdots a _ { n }$ ，则 $n = 7$ 时 $T _ { n }$ 取最小值

14. （2024·山东济宁·三模）已知数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和为 $S _ { n }$ ，且满足 $2 S _ { n } = 3 ^ { n + 1 } - 3$ ，数列 $\left\{ b _ { n } \right\}$ 的前 $n$ 项和为

$T _ { n }$ ，且满足 ${ \frac { T _ { n } } { n } } = { \frac { 1 } { 2 } } b _ { n } + 1$ ，则下列说法中正确的是（）

A. $a _ { 1 } = 3 b _ { 1 }$ B．数列 $\left\{ a _ { n } \right\}$ 是等比数列 C．数列 $\left\{ b _ { n } \right\}$ 是等差数列 D.若 $b _ { 2 } = 3$ ，则 $\sum _ { n = 1 } ^ { 1 0 } { \frac { 1 } { b _ { _ { n } } \log _ { 3 } a _ { n } } } = { \frac { 9 } { 1 0 } }$

15．（2024·山西吕梁·三模）已知等差数列 $\left\{ a _ { n } \right\}$ 的首项为 $a _ { 1 }$ ，公差为 $d$ ，前 $n$ 项和为 $S _ { n }$ ，若 $S _ { 1 0 } < S _ { 8 } < S _ { 9 }$ ，则下列说法正确的是（）

A．当 $n = 8 , S _ { n }$ 最大  
B．使得 $S _ { n } < 0$ 成立的最小自然数 $n = 1 8$   
C. $\left| a _ { 8 } + a _ { 5 } \right| > \left| a _ { 1 0 } + a _ { 1 1 } \right|$ $\left\{ { \frac { S _ { n } } { a _ { n } } } \right\}$ 中最小项为 $\frac { S _ { 1 0 } } { a _ { 1 0 } }$

# 三、填空题

16. （2024·湖北荆州·三模）若实数 $0 , x , y , 6$ 成等差数列， $- { \frac { 1 } { 2 } } , a , b , c , - { \frac { 1 } { 8 } }$ 成等比数列，则-x ${ \frac { y - x } { b } } = \underbrace { \phantom { - } } _ { \qquad b \qquad } .$

17．(2024·山东青岛·三模）已知等差数列 $\left\{ a _ { n } \right\}$ 的公差 $d \neq 0$ ，首项 $a _ { \scriptscriptstyle 1 } = \frac { 1 } { 2 }$ ， $a _ { 4 }$ 是 $a _ { 2 }$ 与 $a _ { 8 }$ 的等比中项，记 $S _ { n }$ 为数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和，则 $S _ { 2 0 } =$

18. (2024-湖南邵阳·三模）已知数列 $\left\{ a _ { n } \right\}$ 銀与 $\left\{ { \frac { a _ { n } ^ { 2 } } { n } } \right\}$ 均为等差数列 $\left( n \in \mathbf { N } ^ { * } \right)$ ，且 $a _ { 2 } = 1$ ，则 $a _ { 2 0 2 4 } =$ （204

19．（2024·宁夏银川·三模）设为 $S _ { n }$ 等差数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和，已知 $S _ { 1 }$ 、 $S _ { 2 }$ 、 $S _ { 4 }$ 成等比数列， $S _ { 2 } = 2 a _ { 1 } + 2$ ，当 $6 a _ { n } - S _ { n }$ 取得最大值时， $n = \_$

20．（2024·上海浦东新·三模）已知数列 $\left\{ a _ { n } \right\}$ 为等比数列， $a _ { 5 } = 8$ ， $a _ { 8 } = 1$ ，则 $\sum _ { i = 1 } ^ { 8 } a _ { i } = \underbrace { \phantom { - } } \qquad .$

21．（2024·上海闵行·三模）设 $S _ { n }$ 是等比数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和，若 $S _ { 3 } = 4$ ， $a _ { 4 } + a _ { 5 } + a _ { 6 } = 8$ ，则 $\frac { S _ { 1 2 } } { S _ { 6 } } =$

22．（2024-四川·三模）在数列 $\left\{ a _ { n } \right\}$ 中，已知 $a _ { \scriptscriptstyle 1 } = \frac { 1 } { 2 }$ ， ${ \big ( } n + 2 { \big ) } a _ { n + 1 } = n a _ { n }$ ，则数列 $\left\{ a _ { n } \right\}$ 的前2024项和$S _ { 2 0 2 4 } = \_ { \cdot } \qquad .$

23．2024·浙江绍兴·三模)记T,为正项数列{an}的前n项积,已知T" =- $T _ { n } = { \frac { a _ { n } } { a _ { n } - 1 } }$ ，则 $a _ { 1 } = .$ ； $T _ { 2 0 2 4 } =$

# 四、解答题

24．（2024·新疆喀什·三模）已知数列 $\left\{ a _ { n } \right\}$ 的首项 $a _ { \mathrm { 1 } } = 3$ ，且满足 $a _ { n + 1 } = 2 a _ { n } - 1$ （ $\ b { n } \in \mathbf { N } ^ { * }$ ）：

(1)求证：数列 $\left\{ a _ { n } - 1 \right\}$ 为等比数列；

(2)记 $b _ { n } = \log _ { 2 } \left( a _ { n } - 1 \right)$ ，求数列 $\left\{ \frac { 1 } { b _ { n } b _ { n + 1 } } \right\}$ 的前 $n$ 项和 $S _ { n }$ ，并证明 $\frac { 1 } { 2 } \leq S _ { n } < 1$ ：

25．（2024·四川自贡·三模）已知数列 $\left\{ a _ { n } \right\}$ 的前项和为 $S _ { n }$ ，且 $S _ { n } - n a _ { n } = { \frac { 1 } { 2 } } n ( n - 1 )$

(1)证明：数列 $\left\{ a _ { n } \right\}$ 为等差数列；   
(2)若 $a _ { 5 }$ ， $a _ { 9 }$ ， $a _ { 1 1 }$ 成等比数列，求 $S _ { n }$ 的最大值.

26．（2024·浙江绍兴·三模）已知数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和为 $S _ { n }$ ，且 $a _ { \mathrm { 1 } } = 2$ ， $S _ { n } = { \frac { n } { n + 2 } } a _ { n + 1 }$ ，设 $b _ { n } = { \frac { S _ { n } } { n } }$

(1)求证：数列 $\left\{ b _ { n } \right\}$ 为等比数列；(2)求数列 $\left\{ S _ { n } \right\}$ 的前 $n$ 项和 $T _ { n }$ ：

27．（2024·新疆·三模）若一个数列从第二项起，每一项和前一项的比值组成的新数列是一个等比数列，则称这个数列是一个“二阶等比数列”，如：1，3，27，729，.....已知数列 $\left\{ a _ { n } \right\}$ 是一个二阶等比数列， $a _ { 1 } = 1$ ，（204 $a _ { 2 } = 4 , a _ { 3 } = 6 4 .$

(1)求 $\left\{ a _ { n } \right\}$ 的通项公式；

（2）设b= $b _ { n } = { \frac { n + 2 } { \left( a _ { n } \right) ^ { \frac { 1 } { n } } \cdot \log _ { 2 } a _ { n + 1 } } }$ ，求数列 $\left\{ b _ { n } \right\}$ 的前 $n$ 项和 $S _ { n }$ ：

28．（2024·重庆九龙坡·三模）已知 $S _ { n }$ 是等差数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和， $S _ { 5 } = a _ { 1 1 } = 2 0$ ，数列 $\left\{ b _ { n } \right\}$ 是公比大于1的等比数列，且 $b _ { 3 } ^ { 2 } = b _ { 6 }$ ， $b _ { 4 } ^ { } - b _ { 2 } ^ { } = 1 2$ ：

(1)求数列 $\left\{ a _ { n } \right\}$ 和 $\left\{ b _ { n } \right\}$ 的通项公式；  
(2)设 $c _ { n } = { \frac { S _ { n } } { b _ { n } } }$ 求使 $c _ { n }$ 取得最大值时 $n$ 的值.

29. （2024·湖南长沙·三模）若各项均为正数的数列 $\left\{ c _ { n } \right\}$ 满足 $c _ { n } c _ { n + 2 } - c _ { n + 1 } ^ { 2 } = k c _ { n } c _ { n + 1 }$ （ $n \in \mathbf { N } ^ { * } , k$ 为常数），则称$\left\{ c _ { n } \right\}$ 为"比差等数列”已知 $\left\{ a _ { n } \right\}$ 为"比差等数列”，且 $a _ { 1 } = \frac { 5 } { 8 } , a _ { 2 } = \frac { 1 5 } { 1 6 } , 3 a _ { 4 } = 2 a _ { 5 }$

(1)求 $\left\{ a _ { n } \right\}$ 的通项公式；

(2)设 ba-+1,n为偶数，求数列{b,}的前n项和S

30．（2024·陕西·三模）数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项的最大值记为 $M _ { n }$ ，即 $M _ { n } = \operatorname* { m a x } \left\{ a _ { 1 } , a _ { 2 } , \cdots , a _ { n } \right\}$ ；前 $n$ 项的最小值记为 $m _ { n }$ ，即 $m _ { n } = \operatorname* { m i n } \left\{ a _ { 1 } , a _ { 2 } , \cdots , a _ { n } \right\}$ ，令 $p _ { n } = M _ { n } - m _ { n }$ ，并将数列 $\left\{ p _ { n } \right\}$ 称为 $\left\{ a _ { n } \right\}$ 的"生成数列”.

(1)设数列 $\left\{ p _ { n } \right\}$ 的"生成数列"为 $\left\{ q _ { n } \right\}$ ，求证： ${ \boldsymbol { p } } _ { n } = { \boldsymbol { q } } _ { n }$ ；   
(2)若 $a _ { n } = 2 ^ { n } - 3 n$ ，求其生成数列 $\left\{ p _ { n } \right\}$ 的前 $n$ 项和.

31．（2024·江苏宿迁·三模）在数列 $\left\{ a _ { n } \right\}$ 中， $a _ { 1 } = 2 , a _ { n } + a _ { n + 1 } = 3 \cdot 2 ^ { n } ( n \in \mathbf { N } ^ { * } )$ ：

(1)求数列 $\left\{ a _ { n } \right\}$ 的通项公式；  
(2)已知数列 $\left\{ b _ { n } \right\}$ 满足 $4 ^ { b _ { 1 } - 1 } 4 ^ { b _ { 2 } - 1 } \cdots 4 ^ { b _ { n } - 1 } = a _ { n } ^ { \ b _ { n } }$ ；  
$\textcircled{1}$ 求证：数列 $\left\{ b _ { n } \right\}$ 是等差数列；  
$\textcircled{2}$ 韓若 $b _ { 2 } = 3$ ，设数列 $c _ { n } = { \frac { b _ { n } b _ { n + 1 } } { a _ { n } } }$ 的前 $n$ 项和为 $T _ { n }$ ，求证： $T _ { n } < 1 4$ ：

32．（2024·天津滨海新·三模）已知等差数列 $\left\{ a _ { n } \right\}$ 的前 $n$ 项和为 $S _ { n }$ ， $a _ { 3 } = 5$ ， $S _ { 9 } = 6 3$ ，数列 $\left\{ b _ { n } \right\}$ 是公比大于1的等比数列，且 $b _ { 1 } + b _ { 2 } + b _ { 3 } = 1 4$ ， $b _ { 1 } b _ { 2 } b _ { 3 } = 6 4$ ：

(1)求 $\left\{ a _ { n } \right\}$ ， $\left\{ b _ { n } \right\}$ 的通项公式；

(2)数列 $\left\{ a _ { n } \right\}$ ， $\left\{ b _ { n } \right\}$ 的所有项按照"当 $n$ 为奇数时， $b _ { n }$ 放在 $a _ { n }$ 的前面；当 $n$ 为偶数时， $a _ { n }$ 放在 $b _ { n }$ 的前面"的要求进行"交叉排列”，得到一个新数列 $\left\{ c _ { n } \right\}$ ： $b _ { 1 }$ ， $a _ { 1 }$ ， $a _ { 2 }$ ， $b _ { 2 }$ ， $b _ { 3 }$ ， $a _ { 3 }$ ， $a _ { 4 }$ ，..，求数列 $\left\{ c _ { n } \right\}$ 的前7项和 $T _ { \tau }$ 及前 $4 n + 3$ 项和 $T _ { 4 n + 3 }$ ：

(3)是否存在数列 $\left\{ d _ { n } \right\}$ ，满足等式 $\sum _ { i = 1 } ^ { n } { \bigl ( } a _ { i } - 2 { \bigr ) } d _ { n + 1 - i } = 2 ^ { n + 1 } - n - 2$ 成立，若存在，求出数列 $\left\{ d _ { n } \right\}$ 的通项公式，若不存在，请说明理由.

33．（2024·黑龙江·三模）如果 $n$ 项有穷数列 $\left\{ a _ { n } \right\}$ 满足 $a _ { 1 } = a _ { n }$ ， $a _ { 2 } = a _ { n - 1 }$ ，...， $a _ { n } = a _ { 1 }$ ，即 （204 $a _ { i } = a _ { n - i + 1 } \left( i = 1 , 2 , \cdots , n \right)$ ，则称有穷数列 $\left\{ a _ { n } \right\}$ 为"对称数列”

(1)设数列 $\left\{ b _ { n } \right\}$ 是项数为7的"对称数列”，其中 $b _ { 1 } , b _ { 2 } , b _ { 3 } , b _ { 4 }$ 成等差数列，且 $b _ { 2 } = 3 , b _ { 5 } = 5$ ，依次写出数列 $\left\{ b _ { n } \right\}$ 的每一项;  
(2)设数列 $\left\{ c _ { n } \right\}$ 是项数为 $2 k - 1 \left( k \in \mathbf { N } ^ { * } \right.$ 且 $k \geq 2$ )的"对称数列”，且满足 $\left| c _ { n + 1 } - c _ { n } \right| = 2$ ，记 $S _ { n }$ 为数列 $\left\{ c _ { n } \right\}$ 的前 $n$ 项和.  
$\textcircled{1}$ 若 $c _ { 1 }$ ， $c _ { 2 }$ ，…， $c _ { k }$ 构成单调递增数列，且 $c _ { k } = 2 0 2 3$ .当 $k$ 为何值时， $S _ { 2 k - 1 }$ 取得最大值？  
$\textcircled{2}$ 若 $c _ { 1 } = 2 0 2 4$ ，且 $S _ { 2 k - 1 } = 2 0 2 4$ ，求 $k$ 的最小值.