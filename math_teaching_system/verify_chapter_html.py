#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
章节HTML文件验证脚本
验证生成的章节HTML文件是否正确
"""

import os
import json
from pathlib import Path

def verify_chapter_html_files():
    """验证章节HTML文件"""
    
    chapters_dir = Path("/media/dp/software/mathtech1.0/3.0/math_teaching_system/frontend/chapters")
    knowledge_base_dir = Path("/media/dp/software/mathtech1.0/3.0/math_teaching_system/knowledge_base_structured")
    
    print("🔍 开始验证章节HTML文件...")
    print(f"章节目录: {chapters_dir}")
    print(f"知识库目录: {knowledge_base_dir}")
    
    stats = {
        "textbooks": 0,
        "chapters": 0,
        "html_files": 0,
        "json_files": 0,
        "missing_html": 0,
        "missing_json": 0
    }
    
    errors = []
    
    # 验证HTML文件
    for textbook_dir in chapters_dir.iterdir():
        if not textbook_dir.is_dir():
            continue
            
        textbook_name = textbook_dir.name
        print(f"\n📚 验证教材: {textbook_name}")
        stats["textbooks"] += 1
        
        # 检查对应的知识库目录
        kb_textbook_dir = knowledge_base_dir / textbook_name
        if not kb_textbook_dir.exists():
            error_msg = f"知识库中缺少教材目录: {textbook_name}"
            errors.append(error_msg)
            print(f"  ❌ {error_msg}")
            continue
        
        # 验证章节HTML文件
        for html_file in textbook_dir.glob("*.html"):
            chapter_name = html_file.stem
            print(f"  📖 验证章节HTML: {chapter_name}")
            stats["html_files"] += 1
            
            # 检查对应的知识库章节目录
            kb_chapter_dir = kb_textbook_dir / chapter_name
            if not kb_chapter_dir.exists():
                error_msg = f"知识库中缺少章节目录: {textbook_name}/{chapter_name}"
                errors.append(error_msg)
                print(f"    ❌ {error_msg}")
                continue
            
            # 验证HTML文件内容
            try:
                with open(html_file, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # 检查关键内容
                if 'CHAPTER_CONFIG' in html_content:
                    print(f"    ✅ HTML文件包含章节配置")
                else:
                    error_msg = f"HTML文件缺少章节配置: {html_file}"
                    errors.append(error_msg)
                    print(f"    ❌ {error_msg}")
                
                if '逗逗专用高中智能数学教学系统' in html_content:
                    print(f"    ✅ HTML文件包含正确标题")
                else:
                    error_msg = f"HTML文件标题不正确: {html_file}"
                    errors.append(error_msg)
                    print(f"    ❌ {error_msg}")
                    
            except Exception as e:
                error_msg = f"读取HTML文件失败: {html_file} - {e}"
                errors.append(error_msg)
                print(f"    ❌ {error_msg}")
            
            # 验证对应的JSON文件
            json_files = list(kb_chapter_dir.glob("*.json"))
            if json_files:
                print(f"    ✅ 找到 {len(json_files)} 个JSON文件")
                stats["json_files"] += len(json_files)
            else:
                error_msg = f"章节目录中没有JSON文件: {kb_chapter_dir}"
                errors.append(error_msg)
                print(f"    ❌ {error_msg}")
                stats["missing_json"] += 1
            
            stats["chapters"] += 1
    
    # 检查知识库中是否有HTML文件缺失的章节
    for textbook_dir in knowledge_base_dir.iterdir():
        if not textbook_dir.is_dir():
            continue
            
        textbook_name = textbook_dir.name
        html_textbook_dir = chapters_dir / textbook_name
        
        for chapter_dir in textbook_dir.iterdir():
            if not chapter_dir.is_dir():
                continue
                
            chapter_name = chapter_dir.name
            html_file = html_textbook_dir / f"{chapter_name}.html"
            
            if not html_file.exists():
                error_msg = f"缺少HTML文件: {textbook_name}/{chapter_name}.html"
                errors.append(error_msg)
                stats["missing_html"] += 1
    
    # 输出验证结果
    print(f"\n🎉 章节HTML文件验证完成!")
    print(f"📊 验证统计:")
    print(f"  - 教材数量: {stats['textbooks']}")
    print(f"  - 章节数量: {stats['chapters']}")
    print(f"  - HTML文件: {stats['html_files']}")
    print(f"  - JSON文件: {stats['json_files']}")
    print(f"  - 缺失HTML: {stats['missing_html']}")
    print(f"  - 缺失JSON: {stats['missing_json']}")
    
    if errors:
        print(f"\n⚠️ 发现 {len(errors)} 个问题:")
        for i, error in enumerate(errors[:10], 1):  # 只显示前10个错误
            print(f"  {i}. {error}")
        if len(errors) > 10:
            print(f"  ... 还有 {len(errors) - 10} 个问题")
    else:
        print(f"\n✅ 所有验证通过，章节HTML文件完整！")
    
    return stats, errors

def generate_file_list():
    """生成文件列表"""
    
    chapters_dir = Path("/media/dp/software/mathtech1.0/3.0/math_teaching_system/frontend/chapters")
    
    print(f"\n📁 生成的章节HTML文件列表:")
    
    for textbook_dir in sorted(chapters_dir.iterdir()):
        if not textbook_dir.is_dir():
            continue
            
        print(f"\n📚 {textbook_dir.name}/")
        
        html_files = sorted(textbook_dir.glob("*.html"))
        for html_file in html_files:
            print(f"  ├── {html_file.name}")
    
    print(f"\n📋 访问方式:")
    print(f"主程序: http://localhost:8080/frontend/index.html")
    print(f"章节页面示例:")
    print(f"  - http://localhost:8080/frontend/chapters/必修第一册/第1章_集合与常用逻辑用语.html")
    print(f"  - http://localhost:8080/frontend/chapters/必修第一册/第3章_函数的概念与性质.html")
    print(f"  - http://localhost:8080/frontend/chapters/选择性必修第一册/第2章_直线和圆的方程.html")

if __name__ == "__main__":
    # 验证章节HTML文件
    stats, errors = verify_chapter_html_files()
    
    # 生成文件列表
    generate_file_list()
    
    # 输出总结
    print(f"\n📋 实现总结:")
    print(f"✅ 基于框架文件 ceshi1.html 生成了 {stats['html_files']} 个章节HTML文件")
    print(f"✅ 每个章节页面都包含完整的JavaScript配置和功能")
    print(f"✅ 主程序 index.html 负责调用各个章节页面")
    print(f"✅ 所有页面都调用对应的JSON数据文件")
    print(f"✅ 保持了原始框架的样式和交互逻辑")
    
    if stats['missing_html'] == 0 and stats['missing_json'] == 0:
        print(f"🎉 所有文件生成完成，系统架构实现成功！")
    else:
        print(f"⚠️ 发现 {stats['missing_html']} 个缺失HTML文件和 {stats['missing_json']} 个缺失JSON文件，需要进一步完善。")
