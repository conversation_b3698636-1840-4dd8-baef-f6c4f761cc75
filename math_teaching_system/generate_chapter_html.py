#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
章节HTML文件生成脚本
为每个章节生成独立的HTML文件，基于框架文件结构
"""

import os
import json
import shutil
from pathlib import Path

def generate_chapter_html_files():
    """为每个章节生成HTML文件"""
    
    # 路径配置
    framework_file = Path("/media/dp/software/mathtech1.0/网页界面设计/ceshi1.html")
    knowledge_base_dir = Path("/media/dp/software/mathtech1.0/3.0/math_teaching_system/knowledge_base_structured")
    output_dir = Path("/media/dp/software/mathtech1.0/3.0/math_teaching_system/frontend/chapters")
    
    print("🚀 开始生成章节HTML文件...")
    print(f"框架文件: {framework_file}")
    print(f"知识库目录: {knowledge_base_dir}")
    print(f"输出目录: {output_dir}")
    
    # 创建输出目录
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 读取框架文件
    if not framework_file.exists():
        print(f"❌ 框架文件不存在: {framework_file}")
        return
    
    with open(framework_file, 'r', encoding='utf-8') as f:
        framework_content = f.read()
    
    stats = {
        "textbooks": 0,
        "chapters": 0,
        "html_files": 0
    }
    
    # 遍历教材文件夹
    for textbook_dir in knowledge_base_dir.iterdir():
        if not textbook_dir.is_dir():
            continue
            
        textbook_name = textbook_dir.name
        print(f"\n📚 处理教材: {textbook_name}")
        stats["textbooks"] += 1
        
        # 创建教材输出目录
        textbook_output_dir = output_dir / textbook_name
        textbook_output_dir.mkdir(exist_ok=True)
        
        # 遍历章节文件夹
        for chapter_dir in textbook_dir.iterdir():
            if not chapter_dir.is_dir():
                continue
                
            chapter_name = chapter_dir.name
            print(f"  📖 处理章节: {chapter_name}")
            stats["chapters"] += 1
            
            # 生成章节HTML文件
            chapter_html_content = generate_chapter_html(
                framework_content, 
                textbook_name, 
                chapter_name, 
                chapter_dir
            )
            
            # 保存HTML文件
            chapter_html_file = textbook_output_dir / f"{chapter_name}.html"
            with open(chapter_html_file, 'w', encoding='utf-8') as f:
                f.write(chapter_html_content)
            
            print(f"    ✅ 生成HTML文件: {chapter_html_file.name}")
            stats["html_files"] += 1
    
    print(f"\n🎉 章节HTML文件生成完成!")
    print(f"📊 统计信息:")
    print(f"  - 教材数量: {stats['textbooks']}")
    print(f"  - 章节数量: {stats['chapters']}")
    print(f"  - HTML文件: {stats['html_files']}")
    
    return stats

def generate_chapter_html(framework_content, textbook_name, chapter_name, chapter_dir):
    """生成单个章节的HTML内容"""
    
    # 获取章节信息
    chapter_info = parse_chapter_info(chapter_name)
    
    # 获取小节列表
    sections = get_chapter_sections(chapter_dir)
    
    # 替换框架内容中的占位符
    html_content = framework_content
    
    # 替换标题
    html_content = html_content.replace(
        '<title>逗逗专用高中智能数学教学系统</title>',
        f'<title>{chapter_info["title"]} - 逗逗专用高中智能数学教学系统</title>'
    )
    
    # 添加章节特定的JavaScript配置
    chapter_config = f"""
    <script>
        // 章节配置
        const CHAPTER_CONFIG = {{
            textbook: '{textbook_name}',
            chapterId: '{chapter_name}',
            chapterTitle: '{chapter_info["title"]}',
            sections: {json.dumps(sections, ensure_ascii=False)},
            knowledgeBasePath: '/media/dp/software/mathtech1.0/3.0/math_teaching_system/knowledge_base_structured'
        }};
        
        // 当前选择状态
        let currentSection = '{sections[0]["id"] if sections else ""}';
        let currentModule = 'concept_learning';
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {{
            initializeChapterPage();
            if (currentSection) {{
                loadSectionContent(currentSection);
            }}
        }});
        
        // 初始化章节页面
        function initializeChapterPage() {{
            // 更新页面标题
            updatePageTitle();
            
            // 初始化侧边栏导航
            initializeSidebarNavigation();
            
            // 初始化模块按钮
            initializeModuleButtons();
        }}
        
        // 更新页面标题
        function updatePageTitle() {{
            const titleElement = document.querySelector('.content-header h1');
            if (titleElement) {{
                titleElement.textContent = CHAPTER_CONFIG.chapterTitle;
            }}
        }}
        
        // 初始化侧边栏导航
        function initializeSidebarNavigation() {{
            const sidebarContent = document.querySelector('.sidebar-content');
            if (!sidebarContent) return;
            
            let navHtml = `
                <div class="chapter-nav">
                    <h3>${{CHAPTER_CONFIG.chapterTitle}}</h3>
                    <div class="section-list">
                        ${{CHAPTER_CONFIG.sections.map(section => `
                            <div class="section-item ${{section.id === currentSection ? 'active' : ''}}" 
                                 onclick="selectSection('${{section.id}}', '${{section.name}}')">
                                ${{section.id}} ${{section.name}}
                            </div>
                        `).join('')}}
                    </div>
                </div>
            `;
            
            sidebarContent.innerHTML = navHtml;
        }}
        
        // 选择小节
        function selectSection(sectionId, sectionName) {{
            // 更新当前选择
            currentSection = sectionId;
            
            // 更新导航状态
            document.querySelectorAll('.section-item').forEach(item => {{
                item.classList.remove('active');
            }});
            event.target.classList.add('active');
            
            // 加载内容
            loadSectionContent(sectionId);
        }}
        
        // 加载小节内容
        async function loadSectionContent(sectionId) {{
            if (!sectionId) return;
            
            const contentArea = document.querySelector('.content-area');
            if (!contentArea) return;
            
            // 显示加载状态
            contentArea.innerHTML = `
                <div class="loading-state">
                    <i class="fas fa-spinner fa-spin"></i>
                    <h3>正在加载内容...</h3>
                    <p>正在从知识库获取 ${{sectionId}} 的内容</p>
                </div>
            `;
            
            try {{
                // 构建文件路径
                const sectionName = getSectionName(sectionId);
                const filePath = `${{CHAPTER_CONFIG.knowledgeBasePath}}/${{CHAPTER_CONFIG.textbook}}/${{CHAPTER_CONFIG.chapterId}}/${{sectionId}}_${{sectionName}}.json`;
                
                const response = await fetch(filePath);
                if (response.ok) {{
                    const data = await response.json();
                    renderSectionContent(data);
                }} else {{
                    renderFallbackContent(sectionId);
                }}
            }} catch (error) {{
                console.error('加载失败:', error);
                renderFallbackContent(sectionId);
            }}
        }}
        
        // 获取小节名称
        function getSectionName(sectionId) {{
            const section = CHAPTER_CONFIG.sections.find(s => s.id === sectionId);
            return section ? section.name : '未知小节';
        }}
        
        // 渲染小节内容
        function renderSectionContent(data) {{
            const contentArea = document.querySelector('.content-area');
            const moduleContent = data.content[currentModule];
            
            if (moduleContent) {{
                let html = `
                    <div class="section-content">
                        <h2>${{data.section_info.full_title}} - ${{getModuleName(currentModule)}}</h2>
                `;
                
                // 根据模块类型渲染内容
                if (currentModule === 'concept_learning' && moduleContent.content) {{
                    html += renderConceptContent(moduleContent);
                }} else {{
                    html += `<p>${{moduleContent.title || '内容加载中...'}}</p>`;
                }}
                
                html += `
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="playVoice('${{moduleContent.voice_script || ''}}')">
                                🎧 听讲解
                            </button>
                            <button class="btn btn-outline" onclick="showAnimation('${{moduleContent.animation_id || ''}}')">
                                🎬 看动画
                            </button>
                            <button class="btn btn-outline" onclick="showVideo('${{moduleContent.video_url || ''}}')">
                                📹 视频精讲
                            </button>
                        </div>
                    </div>
                `;
                
                contentArea.innerHTML = html;
            }} else {{
                renderFallbackContent(currentSection);
            }}
        }}
        
        // 渲染概念学习内容
        function renderConceptContent(moduleContent) {{
            let html = '';
            
            if (moduleContent.content.definition) {{
                html += `<div class="definition"><strong>定义：</strong>${{moduleContent.content.definition}}</div>`;
            }}
            
            if (moduleContent.content.key_points) {{
                html += `
                    <div class="key-points">
                        <strong>要点：</strong>
                        <ul>
                            ${{moduleContent.content.key_points.map(point => `<li>${{point}}</li>`).join('')}}
                        </ul>
                    </div>
                `;
            }}
            
            if (moduleContent.content.examples) {{
                html += `
                    <div class="examples">
                        <strong>示例：</strong>
                        ${{moduleContent.content.examples.map(example => `
                            <div class="example">
                                <h4>${{example.title}}</h4>
                                <p>${{example.content}}</p>
                                <em>${{example.explanation}}</em>
                            </div>
                        `).join('')}}
                    </div>
                `;
            }}
            
            return html;
        }}
        
        // 渲染备用内容
        function renderFallbackContent(sectionId) {{
            const contentArea = document.querySelector('.content-area');
            contentArea.innerHTML = `
                <div class="fallback-content">
                    <h2>${{CHAPTER_CONFIG.chapterTitle}} - ${{sectionId}} - ${{getModuleName(currentModule)}}</h2>
                    <p>✅ 章节页面功能正常工作！</p>
                    <p>✅ 基于框架文件生成的独立章节页面！</p>
                    <p>📁 数据路径：${{CHAPTER_CONFIG.textbook}}/${{CHAPTER_CONFIG.chapterId}}/${{sectionId}}_${{getSectionName(sectionId)}}.json</p>
                    <div class="action-buttons">
                        <button class="btn btn-primary">🎧 听讲解</button>
                        <button class="btn btn-outline">🎬 看动画</button>
                        <button class="btn btn-outline">📹 视频精讲</button>
                    </div>
                </div>
            `;
        }}
        
        // 初始化模块按钮
        function initializeModuleButtons() {{
            const moduleButtons = document.querySelectorAll('.module-btn');
            moduleButtons.forEach(btn => {{
                btn.addEventListener('click', function() {{
                    moduleButtons.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentModule = this.dataset.module;
                    if (currentSection) {{
                        loadSectionContent(currentSection);
                    }}
                }});
            }});
        }}
        
        // 获取模块名称
        function getModuleName(module) {{
            const names = {{
                'concept_learning': '概念学习',
                'core_skills': '核心技巧',
                'problem_types': '题型高手',
                'practice_exercises': '动手练习'
            }};
            return names[module] || module;
        }}
        
        // 多媒体功能
        function playVoice(text) {{
            console.log('播放语音:', text);
            alert('🔊 语音播放功能：' + (text || '暂无语音内容'));
        }}
        
        function showAnimation(animationId) {{
            console.log('播放动画:', animationId);
            alert('🎬 动画播放功能：' + (animationId || '暂无动画'));
        }}
        
        function showVideo(videoUrl) {{
            console.log('播放视频:', videoUrl);
            alert('📹 视频播放功能：' + (videoUrl || '暂无视频'));
        }}
    </script>
    """
    
    # 在</body>前插入配置脚本
    html_content = html_content.replace('</body>', f'{chapter_config}\n</body>')
    
    return html_content

def parse_chapter_info(chapter_name):
    """解析章节信息"""
    parts = chapter_name.split('_', 1)
    chapter_num = parts[0] if len(parts) > 0 else chapter_name
    chapter_title = parts[1] if len(parts) > 1 else chapter_name
    
    return {
        "id": chapter_name,
        "number": chapter_num,
        "title": f"{chapter_num} {chapter_title}",
        "name": chapter_title
    }

def get_chapter_sections(chapter_dir):
    """获取章节的小节列表"""
    sections = []
    
    for section_file in sorted(chapter_dir.glob("*.json")):
        section_name = section_file.stem
        parts = section_name.split('_', 1)
        section_id = parts[0] if len(parts) > 0 else section_name
        section_title = parts[1] if len(parts) > 1 else section_name
        
        sections.append({
            "id": section_id,
            "name": section_title,
            "filename": section_name
        })
    
    return sections

if __name__ == "__main__":
    generate_chapter_html_files()
