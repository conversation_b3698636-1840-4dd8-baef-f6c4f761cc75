#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库重构脚本
将章节JSON文件拆分为更细粒度的小节文件
"""

import os
import json
import shutil
from pathlib import Path

def restructure_knowledge_base():
    """重构知识库结构"""
    
    # 源目录和目标目录
    source_dir = Path("knowledge_base_structured")
    target_dir = Path("/media/dp/software/mathtech1.0/3.0/math_teaching_system/knowledge_base_structured")
    
    print("🚀 开始知识库重构...")
    print(f"源目录: {source_dir}")
    print(f"目标目录: {target_dir}")
    
    # 创建目标目录
    target_dir.mkdir(parents=True, exist_ok=True)
    
    # 统计信息
    stats = {
        "textbooks": 0,
        "chapters": 0,
        "sections": 0,
        "files_created": 0
    }
    
    # 遍历教材文件夹
    for textbook_dir in source_dir.iterdir():
        if not textbook_dir.is_dir():
            continue
            
        textbook_name = textbook_dir.name
        print(f"\n📚 处理教材: {textbook_name}")
        stats["textbooks"] += 1
        
        # 创建教材目录
        target_textbook_dir = target_dir / textbook_name
        target_textbook_dir.mkdir(exist_ok=True)
        
        # 处理章节JSON文件
        for chapter_file in textbook_dir.glob("*.json"):
            chapter_name = chapter_file.stem
            print(f"  📖 处理章节: {chapter_name}")
            stats["chapters"] += 1
            
            # 读取章节JSON文件
            try:
                with open(chapter_file, 'r', encoding='utf-8') as f:
                    chapter_data = json.load(f)
                
                # 创建章节文件夹
                target_chapter_dir = target_textbook_dir / chapter_name
                target_chapter_dir.mkdir(exist_ok=True)
                
                # 处理每个小节
                if "sections" in chapter_data:
                    for section_key, section_data in chapter_data["sections"].items():
                        # 生成小节文件名
                        section_filename = generate_section_filename(section_key)
                        section_file_path = target_chapter_dir / f"{section_filename}.json"
                        
                        # 创建小节JSON数据
                        section_json = create_section_json(
                            section_key, 
                            section_data, 
                            chapter_data["chapter_info"],
                            textbook_name
                        )
                        
                        # 写入小节文件
                        with open(section_file_path, 'w', encoding='utf-8') as f:
                            json.dump(section_json, f, ensure_ascii=False, indent=2)
                        
                        print(f"    ✅ 创建小节文件: {section_filename}.json")
                        stats["sections"] += 1
                        stats["files_created"] += 1
                        
            except Exception as e:
                print(f"    ❌ 处理章节 {chapter_name} 时出错: {e}")
                continue
    
    # 输出统计信息
    print(f"\n🎉 知识库重构完成!")
    print(f"📊 统计信息:")
    print(f"  - 教材数量: {stats['textbooks']}")
    print(f"  - 章节数量: {stats['chapters']}")
    print(f"  - 小节数量: {stats['sections']}")
    print(f"  - 创建文件: {stats['files_created']}")
    
    return stats

def generate_section_filename(section_key):
    """生成小节文件名"""
    # 提取节次号和名称
    # 例如: "1.1 集合的概念" -> "1.1_集合的概念"
    if " " in section_key:
        section_num, section_name = section_key.split(" ", 1)
        return f"{section_num}_{section_name}"
    else:
        return section_key.replace(" ", "_")

def create_section_json(section_key, section_data, chapter_info, textbook_name):
    """创建小节JSON数据结构"""
    
    # 提取节次信息
    section_parts = section_key.split(" ", 1)
    section_num = section_parts[0] if len(section_parts) > 0 else section_key
    section_title = section_parts[1] if len(section_parts) > 1 else section_key
    
    # 创建小节JSON结构
    section_json = {
        "section_info": {
            "id": section_key,
            "number": section_num,
            "title": section_title,
            "full_title": section_key,
            "chapter_id": chapter_info["id"],
            "chapter_title": chapter_info["title"],
            "textbook": textbook_name,
            "estimated_time": 30,  # 默认30分钟
            "difficulty": chapter_info.get("difficulty", "中等"),
            "importance": chapter_info.get("importance", "中等")
        },
        "content": {
            "concept_learning": section_data.get("concept_learning", create_default_module("概念学习", section_title)),
            "core_skills": section_data.get("core_skills", create_default_module("核心技巧", section_title)),
            "problem_types": section_data.get("problem_types", create_default_module("题型高手", section_title)),
            "practice_exercises": section_data.get("practice_exercises", create_default_module("动手练习", section_title))
        }
    }
    
    return section_json

def create_default_module(module_type, section_title):
    """创建默认模块内容"""
    return {
        "title": f"{section_title} - {module_type}",
        "content": f"这里是{section_title}的{module_type}内容，正在整理中...",
        "voice_script": f"欢迎学习{section_title}的{module_type}内容。",
        "estimated_time": 10
    }

if __name__ == "__main__":
    restructure_knowledge_base()
