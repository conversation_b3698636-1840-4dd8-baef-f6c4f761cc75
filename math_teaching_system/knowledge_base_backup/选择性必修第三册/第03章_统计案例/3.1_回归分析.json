{"section_info": {"id": "3.1 回归分析", "number": "3.1", "title": "回归分析", "full_title": "3.1 回归分析", "chapter_id": "第03章_统计案例", "chapter_title": "第三章 统计案例", "textbook": "选择性必修第三册", "estimated_time": 30, "difficulty": "中等", "importance": "中等"}, "content": {"concept_learning": {"title": "回归分析", "content": {"definition": "回归分析是研究变量间相关关系的统计方法，通过建立回归方程来描述变量间的数量关系。", "key_points": ["线性回归方程：ŷ = bx + a", "回归系数：b = Σ(xᵢ-x̄)(yᵢ-ȳ)/Σ(xᵢ-x̄)²", "截距：a = ȳ - bx̄", "相关系数：r = Σ(xᵢ-x̄)(yᵢ-ȳ)/√[Σ(xᵢ-x̄)²Σ(yᵢ-ȳ)²]", "决定系数：R² = 1 - Σ(yᵢ-ŷᵢ)²/Σ(yᵢ-ȳ)²"], "examples": [{"title": "线性回归", "content": "研究身高与体重的关系，建立回归方程ŷ = 0.8x - 80", "explanation": "通过最小二乘法建立线性回归方程"}, {"title": "相关性分析", "content": "计算相关系数r = 0.85，说明身高与体重有较强的正相关关系", "explanation": "相关系数的绝对值越接近1，相关性越强"}]}, "voice_script": "回归分析是统计学中重要的分析方法，用于研究变量间的相关关系。线性回归是最基本的回归分析方法，通过建立线性方程来描述两个变量间的关系。相关系数用来衡量变量间线性相关的强弱。", "video_url": "/videos/concept/回归分析.mp4", "animation_id": "regression_analysis_animation", "concept_images": ["/images/concepts/散点图.png", "/images/concepts/回归直线图.png", "/images/concepts/相关系数图.png"]}, "core_skills": {"title": "回归分析技巧", "skills": [{"name": "回归方程建立", "content": {"最小二乘法": {"description": "利用最小二乘法求回归方程", "voice_script": "最小二乘法是建立回归方程的标准方法，要熟练掌握计算步骤。"}, "相关性判断": {"description": "通过相关系数判断变量间的相关性", "voice_script": "相关系数的大小和符号反映了变量间相关关系的强弱和方向。"}}}]}, "problem_types": {"title": "回归分析的典型题型", "types": [{"name": "回归方程求解", "description": "根据数据建立回归方程并进行预测", "voice_script": "回归方程的建立要按照最小二乘法的步骤进行计算。", "examples": [{"problem": "根据5组数据建立y关于x的回归方程", "solution": "ŷ = 2x + 1", "explanation": "利用最小二乘法公式计算回归系数"}]}]}, "practice_exercises": {"title": "回归分析练习题", "exercises": [{"id": "exercise_3_1_1", "type": "应用题", "difficulty": "中等", "question": "某商店统计了5天的气温x(℃)和冷饮销量y(杯)的数据，建立回归方程并预测气温30℃时的销量。数据：(20,50), (22,60), (25,75), (28,85), (30,95)", "answer": "ŷ = 3x - 10，预测销量80杯", "explanation": "利用最小二乘法建立回归方程，然后代入x=30进行预测。", "voice_script": "这是一个典型的回归分析问题。首先计算各项统计量：x̄=25，ȳ=73，然后利用最小二乘法公式计算回归系数b和截距a，得到回归方程ŷ = 3x - 10。当x=30时，预测销量为3×30-10=80杯。", "estimated_time": 8}]}}}