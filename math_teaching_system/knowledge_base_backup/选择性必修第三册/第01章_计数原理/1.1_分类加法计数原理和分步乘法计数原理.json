{"section_info": {"id": "1.1 分类加法计数原理和分步乘法计数原理", "number": "1.1", "title": "分类加法计数原理和分步乘法计数原理", "full_title": "1.1 分类加法计数原理和分步乘法计数原理", "chapter_id": "第01章_计数原理", "chapter_title": "第一章 计数原理", "textbook": "选择性必修第三册", "estimated_time": 30, "difficulty": "中等", "importance": "高"}, "content": {"concept_learning": {"title": "分类加法计数原理和分步乘法计数原理", "content": {"definition": "计数原理是解决计数问题的基本方法，包括分类加法计数原理和分步乘法计数原理。", "key_points": ["分类加法计数原理：完成一件事有n类办法，各类办法的方法数分别为m₁, m₂, ..., mₙ，则总方法数为m₁ + m₂ + ... + mₙ", "分步乘法计数原理：完成一件事需要n个步骤，各步骤的方法数分别为m₁, m₂, ..., mₙ，则总方法数为m₁ × m₂ × ... × mₙ", "关键区别：分类是'或'的关系，分步是'且'的关系", "应用条件：各类方法相互独立，各步骤连续进行"], "examples": [{"title": "分类加法原理", "content": "从北京到上海，可以坐飞机（5种航班）或火车（3种车次），共有5+3=8种方法", "explanation": "不同的交通方式是分类关系，用加法"}, {"title": "分步乘法原理", "content": "从A地到C地要经过B地，A到B有2条路，B到C有3条路，共有2×3=6种路线", "explanation": "必须分步进行，用乘法"}]}, "voice_script": "计数原理是组合数学的基础。分类加法原理用于'或'的情况，分步乘法原理用于'且'的情况。正确区分这两种情况是解决计数问题的关键。", "video_url": "/videos/concept/计数原理.mp4", "animation_id": "counting_principle_animation", "concept_images": ["/images/concepts/分类加法原理图.png", "/images/concepts/分步乘法原理图.png", "/images/concepts/计数原理对比图.png"]}, "core_skills": {"title": "计数原理应用技巧", "skills": [{"name": "原理选择", "content": {"问题分析": {"description": "分析问题结构，选择合适的计数原理", "voice_script": "要仔细分析问题是分类还是分步的结构，这决定了用加法还是乘法。"}, "复杂问题": {"description": "对于复杂问题，可能需要两个原理结合使用", "voice_script": "复杂的计数问题往往需要分类和分步原理的综合运用。"}}}]}, "problem_types": {"title": "计数原理的典型题型", "types": [{"name": "基本计数问题", "description": "直接应用两个计数原理解决问题", "voice_script": "基本计数问题要准确识别是分类还是分步的结构。", "examples": [{"problem": "书架上有5本数学书，3本物理书，任选一本，有多少种选法？", "solution": "8种", "explanation": "这是分类问题，用加法原理：5+3=8"}]}]}, "practice_exercises": {"title": "计数原理练习题", "exercises": [{"id": "exercise_1_1_1", "type": "选择题", "difficulty": "基础", "question": "某人有3件上衣，2条裤子，要搭配一套衣服，有多少种搭配方法？", "options": ["A. 5", "B. 6", "C. 8", "D. 9"], "answer": "B", "explanation": "这是分步问题，先选上衣再选裤子，用乘法原理：3×2=6。", "voice_script": "正确答案是B。搭配衣服需要分两步：第一步选上衣有3种选择，第二步选裤子有2种选择。由于这两步必须都完成才能搭配成一套衣服，所以用分步乘法计数原理，总的搭配方法数为3×2=6种。", "estimated_time": 2}]}}}