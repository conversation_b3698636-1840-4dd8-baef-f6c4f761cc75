{"section_info": {"id": "2.3 离散型随机变量及其分布列", "number": "2.3", "title": "离散型随机变量及其分布列", "full_title": "2.3 离散型随机变量及其分布列", "chapter_id": "第02章_随机变量及其分布", "chapter_title": "第二章 随机变量及其分布", "textbook": "选择性必修第三册", "estimated_time": 30, "difficulty": "困难", "importance": "高"}, "content": {"concept_learning": {"title": "离散型随机变量及其分布列", "content": {"definition": "离散型随机变量是取值为有限个或可列无限个的随机变量，分布列描述了随机变量各个取值的概率。", "key_points": ["离散型随机变量：取值可以一一列举", "分布列：P(X = xᵢ) = pᵢ，i = 1,2,...", "分布列性质：pᵢ ≥ 0，Σpᵢ = 1", "数学期望：E(X) = Σxᵢpᵢ", "方差：D(X) = E(X²) - [E(X)]² = Σ(xᵢ - E(X))²pᵢ"], "examples": [{"title": "分布列", "content": "掷骰子的点数X的分布列：P(X=k) = 1/6，k=1,2,3,4,5,6", "explanation": "每个点数出现的概率都是1/6"}, {"title": "期望和方差", "content": "上述骰子点数的期望E(X) = 3.5，方差D(X) = 35/12", "explanation": "利用期望和方差的计算公式"}]}, "voice_script": "离散型随机变量是概率论中的基本概念。分布列完整描述了随机变量的概率分布，数学期望反映了随机变量的平均水平，方差反映了随机变量的离散程度。", "video_url": "/videos/concept/离散型随机变量.mp4", "animation_id": "discrete_random_variable_animation", "concept_images": ["/images/concepts/分布列图.png", "/images/concepts/数学期望图.png", "/images/concepts/方差图.png"]}, "core_skills": {"title": "随机变量分析技巧", "skills": [{"name": "分布列构造", "content": {"概率计算": {"description": "根据问题背景计算各取值的概率", "voice_script": "构造分布列要准确计算随机变量各个取值的概率。"}, "性质验证": {"description": "验证分布列是否满足基本性质", "voice_script": "分布列必须满足概率的基本性质：非负性和归一性。"}}}]}, "problem_types": {"title": "随机变量的典型题型", "types": [{"name": "期望方差计算", "description": "计算离散型随机变量的数学期望和方差", "voice_script": "期望方差的计算要熟练运用相关公式。", "examples": [{"problem": "随机变量X的分布列为P(X=0)=0.3, P(X=1)=0.7，求E(X)和D(X)", "solution": "E(X)=0.7, D(X)=0.21", "explanation": "利用期望和方差的定义公式计算"}]}]}, "practice_exercises": {"title": "随机变量练习题", "exercises": [{"id": "exercise_2_3_1", "type": "选择题", "difficulty": "中等", "question": "随机变量X的分布列为P(X=1)=0.2, P(X=2)=0.3, P(X=3)=0.5，则E(X)=（）", "options": ["A. 2.0", "B. 2.3", "C. 2.5", "D. 3.0"], "answer": "B", "explanation": "E(X) = 1×0.2 + 2×0.3 + 3×0.5 = 0.2 + 0.6 + 1.5 = 2.3。", "voice_script": "正确答案是B。数学期望的计算公式是E(X) = Σxᵢpᵢ。代入各个取值和对应的概率：E(X) = 1×0.2 + 2×0.3 + 3×0.5 = 0.2 + 0.6 + 1.5 = 2.3。", "estimated_time": 3}]}}}