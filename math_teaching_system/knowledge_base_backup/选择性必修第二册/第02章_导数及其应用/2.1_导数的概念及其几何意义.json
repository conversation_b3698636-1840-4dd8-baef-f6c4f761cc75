{"section_info": {"id": "2.1 导数的概念及其几何意义", "number": "2.1", "title": "导数的概念及其几何意义", "full_title": "2.1 导数的概念及其几何意义", "chapter_id": "第02章_导数及其应用", "chapter_title": "第二章 导数及其应用", "textbook": "选择性必修第二册", "estimated_time": 30, "difficulty": "困难", "importance": "高"}, "content": {"concept_learning": {"title": "导数的概念及其几何意义", "content": {"definition": "函数y=f(x)在点x₀处的导数是函数在该点处切线的斜率，反映了函数在该点的瞬时变化率。", "key_points": ["导数的定义：f'(x₀) = lim[h→0] [f(x₀+h)-f(x₀)]/h", "几何意义：函数图象在某点处的切线斜率", "物理意义：瞬时变化率", "导函数：f'(x) = lim[h→0] [f(x+h)-f(x)]/h", "可导与连续的关系：可导必连续，连续不一定可导"], "examples": [{"title": "导数的几何意义", "content": "函数f(x)=x²在点(1,1)处的导数f'(1)=2，表示切线斜率为2", "explanation": "导数的几何意义是切线斜率"}, {"title": "瞬时速度", "content": "位移函数s(t)=5t²，在t=2时的瞬时速度v=s'(2)=20m/s", "explanation": "导数的物理意义是瞬时变化率"}]}, "voice_script": "导数是微积分的核心概念之一。它描述了函数在某一点的瞬时变化率，几何上表示为函数图象在该点的切线斜率。导数概念的建立为研究函数的性质提供了强有力的工具。", "video_url": "/videos/concept/导数的概念.mp4", "animation_id": "derivative_concept_animation", "concept_images": ["/images/concepts/导数定义图.png", "/images/concepts/导数几何意义图.png", "/images/concepts/切线斜率图.png"]}, "core_skills": {"title": "导数概念技巧", "skills": [{"name": "导数定义应用", "content": {"极限计算": {"description": "利用导数定义计算导数值", "voice_script": "用导数定义计算导数需要熟练掌握极限的计算方法。"}, "几何应用": {"description": "利用导数的几何意义求切线方程", "voice_script": "导数的几何意义是求切线方程的关键。"}}}]}, "problem_types": {"title": "导数概念的典型题型", "types": [{"name": "切线方程", "description": "利用导数求函数图象的切线方程", "voice_script": "求切线方程需要先求出切点处的导数值，即切线斜率。", "examples": [{"problem": "求函数f(x)=x³在点(1,1)处的切线方程", "solution": "y = 3x - 2", "explanation": "f'(1) = 3，切线方程为y-1 = 3(x-1)"}]}]}, "practice_exercises": {"title": "导数概念练习题", "exercises": [{"id": "exercise_2_1_1", "type": "选择题", "difficulty": "中等", "question": "函数f(x)=2x²在x=1处的导数为（）", "options": ["A. 2", "B. 4", "C. 6", "D. 8"], "answer": "B", "explanation": "f'(x) = 4x，所以f'(1) = 4。", "voice_script": "正确答案是B。对于函数f(x)=2x²，利用幂函数的导数公式，f'(x) = 2×2x = 4x。因此在x=1处的导数f'(1) = 4×1 = 4。", "estimated_time": 3}]}}}