{"section_info": {"id": "1.3 集合的基本运算", "number": "1.3", "title": "集合的基本运算", "full_title": "1.3 集合的基本运算", "chapter_id": "第01章_集合与常用逻辑用语", "chapter_title": "第一章 集合与常用逻辑用语", "textbook": "必修第一册", "estimated_time": 30, "difficulty": "基础", "importance": "高"}, "content": {"concept_learning": {"title": "集合的基本运算", "content": {"definition": "集合的基本运算包括交集、并集、补集。这些运算是集合论的核心内容，广泛应用于数学各个分支。", "key_points": ["交集A∩B：既属于A又属于B的元素组成的集合", "并集A∪B：属于A或属于B的元素组成的集合", "补集∁UA：在全集U中但不属于A的元素组成的集合", "运算律：交换律、结合律、分配律、德摩根律"], "examples": [{"title": "交集运算", "content": "A={1,2,3}，B={2,3,4}，则A∩B={2,3}", "explanation": "交集包含两个集合的公共元素"}, {"title": "并集运算", "content": "A={1,2,3}，B={2,3,4}，则A∪B={1,2,3,4}", "explanation": "并集包含两个集合的所有元素，重复元素只写一次"}]}, "voice_script": "集合的基本运算包括交集、并集、补集。交集A∩B是既属于A又属于B的元素组成的集合。并集A∪B是属于A或属于B的元素组成的集合。补集∁UA是在全集U中但不属于A的元素组成的集合。这些运算满足交换律、结合律、分配律等重要性质。", "video_url": "/videos/concept/集合的基本运算.mp4", "animation_id": "set_operations_animation", "concept_images": ["/images/concepts/交集运算图.png", "/images/concepts/并集运算图.png", "/images/concepts/补集运算图.png"]}, "core_skills": {"title": "集合运算的核心技巧", "skills": [{"name": "韦恩图法", "content": {"画图技巧": {"description": "用圆圈表示集合，重叠部分表示交集，整体表示并集", "voice_script": "韦恩图是解决集合运算问题的重要工具。用圆圈表示集合，重叠部分表示交集，所有圆圈覆盖的区域表示并集。"}, "区域分析": {"description": "将韦恩图分成不同区域，分别计算各区域的元素个数", "voice_script": "使用韦恩图时，要将图形分成不同的区域，每个区域代表不同的集合关系，然后分别计算各区域的元素个数。"}}}, {"name": "运算律应用", "content": {"交换律": {"description": "A∪B=B∪A，A∩B=B∩A", "voice_script": "集合的并集和交集运算都满足交换律，即运算顺序不影响结果。"}, "结合律": {"description": "(A∪B)∪C=A∪(B∪C)，(A∩B)∩C=A∩(B∩C)", "voice_script": "集合运算满足结合律，多个集合运算时可以任意加括号。"}, "分配律": {"description": "A∪(B∩C)=(A∪B)∩(A∪C)", "voice_script": "并集对交集满足分配律，这在化简复杂集合表达式时很有用。"}}}]}, "problem_types": {"title": "集合运算的典型题型", "types": [{"name": "基本运算题", "description": "直接进行交集、并集、补集运算", "voice_script": "基本运算题是集合运算的基础，要熟练掌握交集、并集、补集的定义和计算方法。", "examples": [{"problem": "设A={1,2,3,4}，B={3,4,5,6}，求A∩B，A∪B", "solution": "A∩B={3,4}，A∪B={1,2,3,4,5,6}", "explanation": "交集取公共元素，并集取所有元素"}]}, {"name": "韦恩图应用题", "description": "利用韦恩图解决实际问题", "voice_script": "韦恩图应用题通常涉及调查统计问题，要根据题意画出韦恩图，然后计算各部分的数量。", "examples": [{"problem": "某班50人，30人喜欢数学，25人喜欢物理，10人都喜欢，求只喜欢数学的人数", "solution": "只喜欢数学的人数=30-10=20人", "explanation": "用韦恩图分析，只喜欢数学的人数等于喜欢数学的总人数减去两科都喜欢的人数"}]}]}, "practice_exercises": {"title": "集合运算练习题", "exercises": [{"id": "exercise_1_3_1", "type": "选择题", "difficulty": "基础", "question": "设A={1,2,3}，B={2,3,4}，则A∪B=（）", "options": ["<PERSON><PERSON> {2,3}", "B. {1,2,3,4}", "C. {1,4}", "D. {1,2,3,2,3,4}"], "answer": "B", "explanation": "并集包含两个集合的所有元素，重复元素只写一次，所以A∪B={1,2,3,4}。", "voice_script": "正确答案是B。并集A∪B包含属于A或属于B的所有元素。A中有1、2、3，B中有2、3、4。合并后得到1、2、3、4，注意重复的元素2、3只写一次，所以A∪B={1,2,3,4}。", "estimated_time": 2}, {"id": "exercise_1_3_2", "type": "选择题", "difficulty": "基础", "question": "设A={1,2,3}，B={2,3,4}，则A∩B=（）", "options": ["<PERSON><PERSON> {1,4}", "<PERSON>. {2,3}", "C. {1,2,3,4}", "D. ∅"], "answer": "B", "explanation": "交集包含两个集合的公共元素，A和B的公共元素是2和3。", "voice_script": "正确答案是B。交集A∩B包含既属于A又属于B的元素。A={1,2,3}，B={2,3,4}，它们的公共元素是2和3，所以A∩B={2,3}。", "estimated_time": 2}, {"id": "exercise_1_3_3", "type": "应用题", "difficulty": "中等", "question": "某班有40名学生，其中25人喜欢数学，20人喜欢物理，5人两科都不喜欢。求两科都喜欢的学生人数。", "answer": "10人", "explanation": "设两科都喜欢的人数为x，则只喜欢数学的人数为25-x，只喜欢物理的人数为20-x。总人数：(25-x)+(20-x)+x+5=40，解得x=10。", "voice_script": "这是一个典型的韦恩图应用题。设两科都喜欢的人数为x。根据题意，只喜欢数学的人数为25-x，只喜欢物理的人数为20-x，两科都不喜欢的人数为5。所有学生的总数为：(25-x)+(20-x)+x+5=50-x。因为总共40人，所以50-x=40，解得x=10。因此两科都喜欢的学生有10人。", "estimated_time": 5}]}}}