{"section_info": {"id": "3.1 函数的概念及其表示", "number": "3.1", "title": "函数的概念及其表示", "full_title": "3.1 函数的概念及其表示", "chapter_id": "第03章_函数概念与性质", "chapter_title": "第三章 函数概念与性质", "textbook": "必修第一册", "estimated_time": 30, "difficulty": "中等", "importance": "高"}, "content": {"concept_learning": {"title": "函数的概念及其表示", "content": {"definition": "函数是描述两个变量之间依赖关系的数学概念。设A、B是非空数集，如果按照某种确定的对应关系f，使对于集合A中的任意一个数x，在集合B中都有唯一确定的数f(x)和它对应，那么就称f：A→B为从集合A到集合B的一个函数。", "key_points": ["函数的三要素：定义域、值域、对应关系", "函数的表示方法：解析法、列表法、图象法", "函数的记号：y=f(x)，其中x是自变量，y是因变量", "定义域：自变量x的取值范围", "值域：因变量y的取值范围"], "examples": [{"title": "函数的定义域", "content": "函数f(x)=√(x-1)的定义域为[1,+∞)", "explanation": "根号下的表达式必须大于等于0，所以x-1≥0，即x≥1"}, {"title": "函数的对应关系", "content": "f(x)=x²表示每个实数x对应它的平方", "explanation": "这是一个明确的对应关系，每个x值都有唯一的y值与之对应"}]}, "voice_script": "函数是数学中最重要的概念之一，它描述了两个变量之间的依赖关系。函数有三个要素：定义域、值域和对应关系。定义域是自变量的取值范围，值域是因变量的取值范围，对应关系说明了自变量和因变量之间的具体联系。函数可以用解析法、列表法和图象法来表示。", "video_url": "/videos/concept/函数的概念.mp4", "animation_id": "function_concept_animation", "concept_images": ["/images/concepts/函数的概念图.png", "/images/concepts/函数的三要素图.png", "/images/concepts/函数的表示方法图.png"]}, "core_skills": {"title": "函数概念的核心技巧", "skills": [{"name": "定义域求法", "content": {"分式函数": {"description": "分母不能为零", "voice_script": "对于分式函数，分母不能为零。比如f(x)=1/(x-2)的定义域是x≠2，即(-∞,2)∪(2,+∞)。"}, "根式函数": {"description": "偶次根号下的表达式必须大于等于零", "voice_script": "对于偶次根式函数，根号下的表达式必须大于等于零。比如f(x)=√(x-1)的定义域是x≥1。"}, "对数函数": {"description": "真数必须大于零，底数大于零且不等于1", "voice_script": "对于对数函数，真数必须大于零，底数必须大于零且不等于1。"}}}, {"name": "函数值计算", "content": {"直接代入": {"description": "将自变量的值直接代入函数表达式", "voice_script": "计算函数值最直接的方法是将自变量的值代入函数表达式。比如f(x)=x²+1，则f(2)=2²+1=5。"}, "复合函数": {"description": "先计算内层函数，再计算外层函数", "voice_script": "对于复合函数，要先计算内层函数的值，再将结果代入外层函数。"}}}]}, "problem_types": {"title": "函数的概念及其表示 - 题型高手", "content": "这里是函数的概念及其表示的题型高手内容，正在整理中...", "voice_script": "欢迎学习函数的概念及其表示的题型高手内容。", "estimated_time": 10}, "practice_exercises": {"title": "函数概念练习题", "exercises": [{"id": "exercise_3_1_1", "type": "选择题", "difficulty": "基础", "question": "函数f(x)=√(x-2)的定义域为（）", "options": ["<PERSON><PERSON> (-∞,2)", "B. (2,+∞)", "C. [2,+∞)", "D. (-∞,2]"], "answer": "C", "explanation": "根号下的表达式x-2必须大于等于0，所以x≥2，定义域为[2,+∞)。", "voice_script": "正确答案是C。对于函数f(x)=√(x-2)，由于是偶次根式，所以根号下的表达式必须大于等于0。即x-2≥0，解得x≥2。因此定义域为[2,+∞)。注意这里包含端点2，所以用方括号。", "estimated_time": 2}, {"id": "exercise_3_1_2", "type": "填空题", "difficulty": "基础", "question": "若f(x)=2x+1，则f(3)=________", "answer": "7", "explanation": "将x=3代入函数表达式：f(3)=2×3+1=7。", "voice_script": "这是一个函数值计算题。将x=3代入函数f(x)=2x+1中，得到f(3)=2×3+1=6+1=7。", "estimated_time": 1}]}}}