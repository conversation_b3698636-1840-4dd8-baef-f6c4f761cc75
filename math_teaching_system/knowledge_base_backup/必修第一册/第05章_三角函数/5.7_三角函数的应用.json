{"section_info": {"id": "5.7 三角函数的应用", "number": "5.7", "title": "三角函数的应用", "full_title": "5.7 三角函数的应用", "chapter_id": "第05章_三角函数", "chapter_title": "第五章 三角函数", "textbook": "必修第一册", "estimated_time": 30, "difficulty": "中等", "importance": "高"}, "content": {"concept_learning": {"title": "三角函数的应用", "content": {"definition": "三角函数在实际生活中有广泛应用，特别是在描述周期性现象方面，如简谐运动、交流电、潮汐变化等。", "key_points": ["简谐运动：位移随时间的变化规律", "交流电：电压和电流的变化规律", "潮汐现象：海水高度的周期性变化", "气温变化：日温差和年温差的周期性", "建立数学模型的步骤"], "examples": [{"title": "简谐运动", "content": "弹簧振子的位移方程：x = Asin(ωt + φ)", "explanation": "A为振幅，ω为角频率，φ为初相位"}]}, "voice_script": "三角函数在描述周期性现象方面有重要应用。无论是物理中的简谐运动、电学中的交流电，还是生活中的潮汐变化、气温变化，都可以用三角函数来建立数学模型。掌握三角函数的应用有助于我们理解和解决实际问题。", "video_url": "/videos/concept/三角函数应用.mp4", "animation_id": "trigonometric_application_animation", "concept_images": ["/images/concepts/简谐运动图.png", "/images/concepts/交流电图.png", "/images/concepts/潮汐变化图.png"]}, "core_skills": {"title": "三角函数应用的核心技巧", "skills": [{"name": "建模技巧", "content": {"数据分析": {"description": "从实际数据中提取周期、振幅等参数", "voice_script": "分析实际数据时，要识别出周期性规律，确定最大值、最小值和周期。"}, "模型建立": {"description": "根据实际情况选择合适的三角函数模型", "voice_script": "根据问题的特点选择正弦函数或余弦函数，确定各参数的物理意义。"}}}]}, "problem_types": {"title": "三角函数应用的典型题型", "types": [{"name": "实际问题建模", "description": "将实际问题转化为三角函数模型", "voice_script": "实际问题建模需要理解问题背景，提取关键信息，建立合适的数学模型。", "examples": [{"problem": "某地一天中气温T(℃)与时间t(小时)的关系可近似表示为T = 20 + 10sin(πt/12 - π/2)，求最高气温和最低气温", "solution": "最高气温30℃，最低气温10℃", "explanation": "T的最大值为20+10=30，最小值为20-10=10"}]}]}, "practice_exercises": {"title": "三角函数应用练习题", "exercises": [{"id": "exercise_5_7_1", "type": "应用题", "difficulty": "中等", "question": "某港口的水深h(米)与时间t(小时)的关系为h = 5 + 3sin(πt/6)，求该港口水深的最大值和最小值。", "answer": "最大值8米，最小值2米", "explanation": "h的最大值为5+3=8米，最小值为5-3=2米。", "voice_script": "这是一个典型的三角函数应用题。函数h = 5 + 3sin(πt/6)中，5是平均水深，3是振幅。正弦函数的值域是[-1,1]，所以h的最大值是5+3×1=8米，最小值是5+3×(-1)=2米。", "estimated_time": 4}]}}}