{"section_info": {"id": "4.2 指数函数", "number": "4.2", "title": "指数函数", "full_title": "4.2 指数函数", "chapter_id": "第04章_指数函数与对数函数", "chapter_title": "第四章 指数函数与对数函数", "textbook": "必修第一册", "estimated_time": 30, "difficulty": "中等", "importance": "高"}, "content": {"concept_learning": {"title": "指数函数", "content": {"definition": "形如y = a^x (a>0且a≠1)的函数叫做指数函数，其中x是自变量，定义域为R。", "key_points": ["指数函数的定义：y = a^x (a>0且a≠1)", "定义域：R，值域：(0,+∞)", "当a>1时，函数单调递增；当0<a<1时，函数单调递减", "图象都过点(0,1)", "指数函数的应用：人口增长、放射性衰变等"], "examples": [{"title": "指数函数性质", "content": "y = 2^x 在R上单调递增，y = (1/2)^x 在R上单调递减", "explanation": "底数大于1时递增，底数在0和1之间时递减"}]}, "voice_script": "指数函数是形如y = a^x的函数，其中a大于0且不等于1。指数函数的图象都经过点(0,1)，当底数大于1时函数单调递增，当底数在0和1之间时函数单调递减。指数函数在实际生活中有广泛应用，如人口增长模型、复利计算等。", "video_url": "/videos/concept/指数函数.mp4", "animation_id": "exponential_function_animation", "concept_images": ["/images/concepts/指数函数图像.png", "/images/concepts/指数函数性质表.png"]}, "core_skills": {"title": "指数函数 - 核心技巧", "content": "这里是指数函数的核心技巧内容，正在整理中...", "voice_script": "欢迎学习指数函数的核心技巧内容。", "estimated_time": 10}, "problem_types": {"title": "指数函数 - 题型高手", "content": "这里是指数函数的题型高手内容，正在整理中...", "voice_script": "欢迎学习指数函数的题型高手内容。", "estimated_time": 10}, "practice_exercises": {"title": "指数函数练习", "exercises": [{"id": "exercise_4_2_1", "type": "选择题", "difficulty": "基础", "question": "函数y = 3^x的值域为（）", "options": ["<PERSON><PERSON> R", "B. (0,+∞)", "C. [0,+∞)", "D. (1,+∞)"], "answer": "B", "explanation": "指数函数的值域为(0,+∞)，因为a^x > 0对所有实数x成立。", "voice_script": "正确答案是B。指数函数y = a^x (a>0且a≠1)的值域都是(0,+∞)。这是因为无论x取什么实数值，a^x都大于0，但永远不能等于0。当x趋向负无穷时，a^x趋向0但不等于0；当x趋向正无穷时，a^x趋向正无穷。", "estimated_time": 2}]}}}