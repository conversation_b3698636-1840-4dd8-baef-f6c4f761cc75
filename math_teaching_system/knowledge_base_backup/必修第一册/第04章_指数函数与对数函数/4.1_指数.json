{"section_info": {"id": "4.1 指数", "number": "4.1", "title": "指数", "full_title": "4.1 指数", "chapter_id": "第04章_指数函数与对数函数", "chapter_title": "第四章 指数函数与对数函数", "textbook": "必修第一册", "estimated_time": 30, "difficulty": "中等", "importance": "高"}, "content": {"concept_learning": {"title": "指数", "content": {"definition": "指数是表示重复乘法的一种记号。对于正数a和整数n，a^n表示n个a相乘的结果。", "key_points": ["整数指数幂：a^n = a·a·...·a (n个a相乘)", "指数运算法则：a^m · a^n = a^(m+n), (a^m)^n = a^(mn), (ab)^n = a^n · b^n", "负指数：a^(-n) = 1/a^n (a≠0)", "零指数：a^0 = 1 (a≠0)", "分数指数：a^(m/n) = ⁿ√(a^m) (a>0, n>0)"], "examples": [{"title": "指数运算", "content": "2³ × 2² = 2^(3+2) = 2⁵ = 32", "explanation": "同底数幂相乘，底数不变，指数相加"}, {"title": "分数指数", "content": "8^(2/3) = (∛8)² = 2² = 4", "explanation": "分数指数可以转化为根式运算"}]}, "voice_script": "指数是数学中重要的概念，它表示重复乘法。指数运算有一套完整的法则，包括同底数幂的乘除法、幂的乘方、积的乘方等。特别要注意负指数和分数指数的含义，它们扩展了指数的概念，使指数运算更加完整。", "video_url": "/videos/concept/指数运算.mp4", "animation_id": "exponent_rules_animation", "concept_images": ["/images/concepts/指数运算法则图.png", "/images/concepts/分数指数图.png"]}, "core_skills": {"title": "指数 - 核心技巧", "content": "这里是指数的核心技巧内容，正在整理中...", "voice_script": "欢迎学习指数的核心技巧内容。", "estimated_time": 10}, "problem_types": {"title": "指数 - 题型高手", "content": "这里是指数的题型高手内容，正在整理中...", "voice_script": "欢迎学习指数的题型高手内容。", "estimated_time": 10}, "practice_exercises": {"title": "指数运算练习", "exercises": [{"id": "exercise_4_1_1", "type": "选择题", "difficulty": "基础", "question": "计算2³ × 2² =（）", "options": ["A. 2⁵", "B. 2⁶", "C. 4⁵", "D. 4⁶"], "answer": "A", "explanation": "同底数幂相乘，底数不变，指数相加：2³ × 2² = 2^(3+2) = 2⁵。", "voice_script": "正确答案是A。这是同底数幂相乘的运算。根据指数运算法则，同底数幂相乘时，底数不变，指数相加。所以2³ × 2² = 2^(3+2) = 2⁵。", "estimated_time": 2}]}}}