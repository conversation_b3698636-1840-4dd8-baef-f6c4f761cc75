{"section_info": {"id": "4.3 对数", "number": "4.3", "title": "对数", "full_title": "4.3 对数", "chapter_id": "第04章_指数函数与对数函数", "chapter_title": "第四章 指数函数与对数函数", "textbook": "必修第一册", "estimated_time": 30, "difficulty": "中等", "importance": "高"}, "content": {"concept_learning": {"title": "对数", "content": {"definition": "如果a^x = N (a>0, a≠1, N>0)，那么数x叫做以a为底N的对数，记作x = log_a N。", "key_points": ["对数的定义：如果a^x = N，则x = log_a N", "对数与指数互为逆运算", "对数运算法则：log_a(MN) = log_a M + log_a N", "换底公式：log_a N = log_c N / log_c a", "常用对数：lg N = log_10 N，自然对数：ln N = log_e N"], "examples": [{"title": "对数计算", "content": "log_2 8 = 3，因为2³ = 8", "explanation": "对数就是求指数，即求2的几次方等于8"}, {"title": "对数运算", "content": "log_2 4 + log_2 8 = log_2(4×8) = log_2 32 = 5", "explanation": "利用对数运算法则简化计算"}]}, "voice_script": "对数是指数的逆运算。如果a的x次方等于N，那么x就是以a为底N的对数。对数有一套完整的运算法则，包括积的对数等于对数的和、商的对数等于对数的差等。常用对数以10为底，自然对数以e为底。", "video_url": "/videos/concept/对数运算.mp4", "animation_id": "logarithm_rules_animation", "concept_images": ["/images/concepts/对数定义图.png", "/images/concepts/对数运算法则图.png"]}, "core_skills": {"title": "对数 - 核心技巧", "content": "这里是对数的核心技巧内容，正在整理中...", "voice_script": "欢迎学习对数的核心技巧内容。", "estimated_time": 10}, "problem_types": {"title": "对数 - 题型高手", "content": "这里是对数的题型高手内容，正在整理中...", "voice_script": "欢迎学习对数的题型高手内容。", "estimated_time": 10}, "practice_exercises": {"title": "对数运算练习", "exercises": [{"id": "exercise_4_3_1", "type": "选择题", "difficulty": "基础", "question": "log_2 8 =（）", "options": ["A. 2", "B. 3", "C. 4", "D. 8"], "answer": "B", "explanation": "因为2³ = 8，所以log_2 8 = 3。", "voice_script": "正确答案是B。对数log_2 8就是求2的几次方等于8。我们知道2³ = 8，所以log_2 8 = 3。这体现了对数和指数互为逆运算的关系。", "estimated_time": 2}]}}}