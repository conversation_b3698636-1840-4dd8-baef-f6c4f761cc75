{"section_info": {"id": "2.5 直线与圆、圆与圆的位置关系", "number": "2.5", "title": "直线与圆、圆与圆的位置关系", "full_title": "2.5 直线与圆、圆与圆的位置关系", "chapter_id": "第02章_直线和圆的方程", "chapter_title": "第二章 直线和圆的方程", "textbook": "选择性必修第一册", "estimated_time": 30, "difficulty": "中等", "importance": "高"}, "content": {"concept_learning": {"title": "直线与圆、圆与圆的位置关系", "content": {"definition": "直线与圆、圆与圆之间存在不同的位置关系，可以通过代数方法和几何方法来判断。", "key_points": ["直线与圆：相离、相切、相交", "判断方法：圆心到直线距离与半径比较", "圆与圆：外离、外切、相交、内切、内含", "判断方法：圆心距与半径和差的比较", "弦长公式、切线方程"], "examples": [{"title": "直线与圆位置关系", "content": "直线3x+4y-10=0与圆x²+y²=4的位置关系：d=2>r=2，相离", "explanation": "圆心到直线距离大于半径，相离"}, {"title": "圆与圆位置关系", "content": "圆(x-1)²+y²=1与圆(x+1)²+y²=1：圆心距=2，半径和=2，外切", "explanation": "圆心距等于半径和，两圆外切"}]}, "voice_script": "直线与圆、圆与圆的位置关系是解析几何的重要内容。判断位置关系主要通过距离比较：直线与圆看圆心到直线的距离与半径的关系，圆与圆看圆心距与半径和差的关系。掌握这些判断方法对解决相关问题很重要。", "video_url": "/videos/concept/直线与圆位置关系.mp4", "animation_id": "line_circle_relation_animation", "concept_images": ["/images/concepts/直线与圆位置关系图.png", "/images/concepts/圆与圆位置关系图.png", "/images/concepts/切线方程图.png"]}, "core_skills": {"title": "位置关系判断技巧", "skills": [{"name": "距离比较法", "content": {"直线与圆": {"description": "比较圆心到直线距离与半径", "voice_script": "判断直线与圆的位置关系，关键是计算圆心到直线的距离，然后与半径比较。"}, "圆与圆": {"description": "比较圆心距与半径和差", "voice_script": "判断两圆位置关系，要计算圆心距，然后与两圆半径的和与差比较。"}}}]}, "problem_types": {"title": "位置关系的典型题型", "types": [{"name": "位置关系判断", "description": "判断直线与圆或圆与圆的位置关系", "voice_script": "位置关系判断要熟练运用距离公式和比较方法。", "examples": [{"problem": "判断直线y=x+1与圆x²+y²=1的位置关系", "solution": "相切", "explanation": "圆心(0,0)到直线x-y+1=0的距离为√2/2<1，但需重新计算"}]}]}, "practice_exercises": {"title": "位置关系练习题", "exercises": [{"id": "exercise_2_5_1", "type": "选择题", "difficulty": "中等", "question": "直线x+y-2=0与圆x²+y²=2的位置关系是（）", "options": ["<PERSON><PERSON> 相离", "B. 相切", "C. 相交", "D. 无法确定"], "answer": "B", "explanation": "圆心(0,0)到直线的距离d = |0+0-2|/√2 = √2，等于半径√2，所以相切。", "voice_script": "正确答案是B。圆x²+y²=2的圆心为(0,0)，半径为√2。利用点到直线距离公式，圆心到直线x+y-2=0的距离为d = |0+0-2|/√(1²+1²) = 2/√2 = √2。因为d = r = √2，所以直线与圆相切。", "estimated_time": 4}]}}}