{"section_info": {"id": "10.2 事件的相互独立性", "number": "10.2", "title": "事件的相互独立性", "full_title": "10.2 事件的相互独立性", "chapter_id": "第10章_概率", "chapter_title": "第十章 概率", "textbook": "必修第二册", "estimated_time": 30, "difficulty": "中等", "importance": "高"}, "content": {"concept_learning": {"title": "事件的相互独立性", "content": {"definition": "两个事件相互独立是指一个事件的发生不影响另一个事件发生的概率。", "key_points": ["独立性定义：P(AB) = P(A)P(B)", "独立事件的性质", "多个事件的相互独立", "独立性的判断方法", "独立重复试验"], "examples": [{"title": "抛硬币两次", "content": "第一次和第二次的结果相互独立，P(两次都是正面) = 1/2 × 1/2 = 1/4", "explanation": "每次抛硬币的结果不受前面结果的影响"}, {"title": "独立重复试验", "content": "进行n次独立重复试验，事件A恰好发生k次的概率为C(n,k)p^k(1-p)^(n-k)", "explanation": "这是二项分布的概率公式"}]}, "voice_script": "事件的相互独立性是概率论的重要概念。两个事件相互独立意味着一个事件的发生不会影响另一个事件发生的概率。独立事件的概率计算遵循乘法法则。独立重复试验是独立性的重要应用。", "video_url": "/videos/concept/事件的相互独立性.mp4", "animation_id": "event_independence_animation", "concept_images": ["/images/concepts/事件独立性图.png", "/images/concepts/独立重复试验图.png", "/images/concepts/二项分布图.png"]}, "core_skills": {"title": "独立性判断技巧", "skills": [{"name": "独立性验证", "content": {"定义验证": {"description": "验证P(AB) = P(A)P(B)是否成立", "voice_script": "判断两个事件是否独立，可以验证它们同时发生的概率是否等于各自概率的乘积。"}, "实际意义": {"description": "从实际背景判断事件间是否有影响", "voice_script": "有时可以从事件的实际意义来判断它们是否相互独立。"}}}]}, "problem_types": {"title": "独立性的典型题型", "types": [{"name": "独立重复试验", "description": "计算独立重复试验中事件发生的概率", "voice_script": "独立重复试验问题通常用二项分布来解决。", "examples": [{"problem": "投篮命中率为0.6，投篮5次，恰好命中3次的概率", "solution": "C(5,3) × 0.6³ × 0.4²", "explanation": "这是二项分布B(5, 0.6)的概率计算"}]}]}, "practice_exercises": {"title": "事件独立性练习题", "exercises": [{"id": "exercise_10_2_1", "type": "选择题", "difficulty": "中等", "question": "甲、乙两人独立地解同一道题，甲解对的概率是0.8，乙解对的概率是0.6，则这道题被解对的概率是（）", "options": ["A. 0.48", "B. 0.88", "C. 0.92", "D. 1.4"], "answer": "C", "explanation": "题目被解对的概率 = 1 - 两人都解错的概率 = 1 - 0.2×0.4 = 1 - 0.08 = 0.92。", "voice_script": "正确答案是C。这道题被解对意味着至少有一人解对。我们可以用对立事件来计算：题目被解对的概率等于1减去两人都解错的概率。甲解错的概率是1-0.8=0.2，乙解错的概率是1-0.6=0.4。由于两人独立解题，两人都解错的概率是0.2×0.4=0.08。所以题目被解对的概率是1-0.08=0.92。", "estimated_time": 4}]}}}