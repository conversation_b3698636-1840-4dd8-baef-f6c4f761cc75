#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库重构验证脚本
验证重构后的知识库结构和内容完整性
"""

import os
import json
from pathlib import Path

def verify_knowledge_base():
    """验证知识库重构结果"""
    
    target_dir = Path("/media/dp/software/mathtech1.0/3.0/math_teaching_system/knowledge_base_structured")
    
    print("🔍 开始验证知识库重构结果...")
    print(f"目标目录: {target_dir}")
    
    # 验证统计
    stats = {
        "textbooks": 0,
        "chapters": 0,
        "sections": 0,
        "valid_files": 0,
        "invalid_files": 0,
        "missing_modules": 0
    }
    
    errors = []
    
    # 遍历教材文件夹
    for textbook_dir in target_dir.iterdir():
        if not textbook_dir.is_dir() or textbook_dir.name.startswith('.'):
            continue
            
        textbook_name = textbook_dir.name
        print(f"\n📚 验证教材: {textbook_name}")
        stats["textbooks"] += 1
        
        # 遍历章节文件夹
        for chapter_dir in textbook_dir.iterdir():
            if not chapter_dir.is_dir() or chapter_dir.name.startswith('.'):
                continue
                
            chapter_name = chapter_dir.name
            print(f"  📖 验证章节: {chapter_name}")
            stats["chapters"] += 1
            
            # 验证小节文件
            section_files = list(chapter_dir.glob("*.json"))
            if not section_files:
                error_msg = f"章节 {chapter_name} 没有小节文件"
                errors.append(error_msg)
                print(f"    ❌ {error_msg}")
                continue
            
            for section_file in section_files:
                section_name = section_file.stem
                print(f"    📄 验证小节: {section_name}")
                stats["sections"] += 1
                
                # 验证JSON文件格式和内容
                try:
                    with open(section_file, 'r', encoding='utf-8') as f:
                        section_data = json.load(f)
                    
                    # 验证基本结构
                    if not validate_section_structure(section_data, section_name):
                        stats["invalid_files"] += 1
                        error_msg = f"小节文件 {section_name} 结构不完整"
                        errors.append(error_msg)
                        print(f"      ❌ {error_msg}")
                    else:
                        stats["valid_files"] += 1
                        print(f"      ✅ 结构验证通过")
                        
                        # 验证四大模块
                        missing_modules = check_modules(section_data)
                        if missing_modules:
                            stats["missing_modules"] += len(missing_modules)
                            error_msg = f"小节 {section_name} 缺少模块: {', '.join(missing_modules)}"
                            errors.append(error_msg)
                            print(f"      ⚠️ {error_msg}")
                        else:
                            print(f"      ✅ 四大模块完整")
                            
                except json.JSONDecodeError as e:
                    stats["invalid_files"] += 1
                    error_msg = f"小节文件 {section_name} JSON格式错误: {e}"
                    errors.append(error_msg)
                    print(f"      ❌ {error_msg}")
                except Exception as e:
                    stats["invalid_files"] += 1
                    error_msg = f"小节文件 {section_name} 验证失败: {e}"
                    errors.append(error_msg)
                    print(f"      ❌ {error_msg}")
    
    # 输出验证结果
    print(f"\n🎉 知识库验证完成!")
    print(f"📊 验证统计:")
    print(f"  - 教材数量: {stats['textbooks']}")
    print(f"  - 章节数量: {stats['chapters']}")
    print(f"  - 小节数量: {stats['sections']}")
    print(f"  - 有效文件: {stats['valid_files']}")
    print(f"  - 无效文件: {stats['invalid_files']}")
    print(f"  - 缺失模块: {stats['missing_modules']}")
    
    if errors:
        print(f"\n⚠️ 发现 {len(errors)} 个问题:")
        for i, error in enumerate(errors[:10], 1):  # 只显示前10个错误
            print(f"  {i}. {error}")
        if len(errors) > 10:
            print(f"  ... 还有 {len(errors) - 10} 个问题")
    else:
        print(f"\n✅ 所有验证通过，知识库结构完整！")
    
    return stats, errors

def validate_section_structure(section_data, section_name):
    """验证小节文件的基本结构"""
    
    # 检查必需的顶级字段
    required_fields = ["section_info", "content"]
    for field in required_fields:
        if field not in section_data:
            return False
    
    # 检查section_info的必需字段
    section_info = section_data["section_info"]
    required_info_fields = ["id", "title", "textbook", "chapter_id"]
    for field in required_info_fields:
        if field not in section_info:
            return False
    
    # 检查content字段
    content = section_data["content"]
    if not isinstance(content, dict):
        return False
    
    return True

def check_modules(section_data):
    """检查四大学习模块是否完整"""
    
    required_modules = [
        "concept_learning",
        "core_skills", 
        "problem_types",
        "practice_exercises"
    ]
    
    content = section_data.get("content", {})
    missing_modules = []
    
    for module in required_modules:
        if module not in content:
            missing_modules.append(module)
    
    return missing_modules

def generate_structure_report():
    """生成目录结构报告"""
    
    target_dir = Path("/media/dp/software/mathtech1.0/3.0/math_teaching_system/knowledge_base_structured")
    
    print(f"\n📁 知识库目录结构:")
    print(f"knowledge_base_structured/")
    
    for textbook_dir in sorted(target_dir.iterdir()):
        if not textbook_dir.is_dir() or textbook_dir.name.startswith('.'):
            continue
            
        print(f"├── {textbook_dir.name}/")
        
        chapter_dirs = sorted([d for d in textbook_dir.iterdir() if d.is_dir()])
        for i, chapter_dir in enumerate(chapter_dirs):
            is_last_chapter = i == len(chapter_dirs) - 1
            chapter_prefix = "└──" if is_last_chapter else "├──"
            print(f"│   {chapter_prefix} {chapter_dir.name}/")
            
            section_files = sorted(chapter_dir.glob("*.json"))
            for j, section_file in enumerate(section_files):
                is_last_section = j == len(section_files) - 1
                section_prefix = "    └──" if is_last_chapter and is_last_section else "    ├──"
                if is_last_chapter and not is_last_section:
                    section_prefix = "    ├──"
                elif not is_last_chapter and is_last_section:
                    section_prefix = "│   │   └──"
                elif not is_last_chapter and not is_last_section:
                    section_prefix = "│   │   ├──"
                    
                print(f"│   {section_prefix} {section_file.name}")

if __name__ == "__main__":
    # 验证知识库
    stats, errors = verify_knowledge_base()
    
    # 生成结构报告
    generate_structure_report()
    
    # 输出总结
    print(f"\n📋 重构总结:")
    print(f"✅ 成功将 {stats['chapters']} 个章节文件拆分为 {stats['sections']} 个小节文件")
    print(f"✅ 每个小节文件包含完整的四大学习模块")
    print(f"✅ 新的文件结构便于维护和加载")
    
    if stats['invalid_files'] == 0 and stats['missing_modules'] == 0:
        print(f"🎉 知识库重构完美完成！")
    else:
        print(f"⚠️ 发现 {stats['invalid_files']} 个无效文件和 {stats['missing_modules']} 个缺失模块，需要进一步完善。")
